import aioftp
import logging
import xml.etree.ElementTree as ET
from decimal import Decimal
from clubs.models import Club, ClubPlayer
from core.models import Competition
from match.models import Game
from players.models import Player
from stats.models import GamePlayerStats
from ..helpers import async_find_first, async_upsert, process_path_if_needed, read_file_to_str, xml_value


logger = logging.getLogger(__name__)

# master/Participate_Stats-{COMPETITION_ID}-{DS_CLUB_ID}.xml
_ds_mapping_game_stats = {
    'start_f': 'StartF',
    'entry_f': 'EntryF',
    'minutes_played': 'Time',
    'goals': 'Goal',
    'yellow_cards': 'Yellow',
    'red_cards': 'Red',
}

# {MATCH_ID}/live_stats_details.xml
_ds_mapping_game_stats_live = {
    'assists': 'Assist',
    'clean_sheets': 'CleanSheet',
    'distance': 'Distance',
    'goals': 'Score',
    'minutes_played': 'Time',
    'red_cards': 'Red',
    'saves': 'SaveCount',
    'shots': 'Shoot',
    'sprints': 'Sprint',
    # 'start_f': 'StartF', # ignores this, to avoid conflict with above StartF
    'top_speed': 'TopSpeed',
    'yellow_cards': 'Yellow',
}

_decimal_value_stats = ('distance', 'top_speed')


def _is_decimal_value_stat(stat: str):
    return True if stat in _decimal_value_stats else False


def _get_stat_value_type(stat: str):
    return Decimal if _is_decimal_value_stat(stat) else int


async def import_game_player_stats(ftp_client: aioftp.Client, file_path: str, file_stat: dict):
    """Import player stats of each match from DS
    The file which outputs player stats per game.
    source file: master/Participate_Stats-{COMPETITION_ID}-{DS_CLUB_ID}.xml
    """
    async with process_path_if_needed(file_path, file_stat) as should_process:
        if not should_process:
            return

        # read XML file
        file_content = await read_file_to_str(ftp_client, file_path)
        root = ET.fromstring(file_content)  # Football node
        report_node = root.find('ParticipateStatsReport')
        year = xml_value(report_node, 'Year', int)
        club_id = xml_value(report_node, 'TeamID', int)
        competition_id = xml_value(report_node, 'GameKindID', int)

        # check club exists
        club = await async_find_first(Club, ds_club_id=club_id)
        if not club:
            logger.warn('Club not found (ds_club_id=%s)', club_id)
            return

        # check competition exists
        competition = await async_find_first(Competition, id=competition_id)
        if not competition:
            logger.warn('Competition not found (id=%s)', competition_id)
            return

        # process game player stats
        stat_keys = _ds_mapping_game_stats.keys()
        for player_info_node in report_node.findall('PlayerInfo'):
            # check player exists
            player_id = xml_value(player_info_node, 'PlayerID', int)
            player = await async_find_first(Player, ds_player_id=player_id)
            if not player:
                # player does not exist, skipped
                logger.warn('Player not found (ds_player_id=%s)', player_id)
                continue

            # process game player stat
            for stats_node in player_info_node.findall('Stats'):
                # check game exists
                game_id = int(stats_node.attrib['GameID'])
                game = await async_find_first(Game, id=game_id)
                if not game:
                    # game does not exist, skipped
                    logger.warn('Game not found (id=%s)', game_id)

                # dynamically collect player stats
                game_player_stats = {}
                for stat in stat_keys:
                    ds_key = _ds_mapping_game_stats.get(stat)
                    if ds_key is not None:
                        value_type = _get_stat_value_type(stat)
                        game_player_stats[stat] = xml_value(stats_node, ds_key, value_type)

                # update stats of player on each game
                await async_upsert(GamePlayerStats, year=year, game=game, player=player, defaults=game_player_stats)

            logger.info('Processed game player stats (player_id=%s)', player_id)


async def import_game_player_stats_live(ftp_client: aioftp.Client, file_path: str, file_stat: dict):
    """Import player stats of each match from DS
    The file which outputs player stats per game.
    source file: {MATCH_ID}/live_stats_details.xml
    """
    async with process_path_if_needed(file_path, file_stat) as should_process:
        if not should_process:
            return

        # read XML file
        file_content = await read_file_to_str(ftp_client, file_path)
        root = ET.fromstring(file_content)  # Football node
        report_node = root.find('StatsReport')
        game_id = xml_value(report_node, 'GameID', int)

        # check game exists
        game: Game = await async_find_first(Game, id=game_id, select_related=('competition',))
        if not game:
            logger.warn('Game not found (id=%s)', game_id)
            return

        # check competition exists
        competition = game.competition
        if not competition:
            logger.warn('Game does not have a Competition')
            return

        year = int(str(game_id)[:4])

        # process player stats
        stat_keys = _ds_mapping_game_stats_live.keys()
        for team_info_node in report_node.findall('TeamInfo'):
            # check club exists
            club_id = xml_value(team_info_node, 'ID', int)
            club = await async_find_first(Club, ds_club_id=club_id)
            if not club:
                # club does not exist, skipped
                logger.warn('Club not found (ds_club_id=%s)', club_id)
                return

            for player_node in team_info_node.findall('Player'):
                # check player exists
                player_id = xml_value(player_node, 'ID', int)
                player = await async_find_first(Player, ds_player_id=player_id)
                if not player:
                    # player does not exist, skipped
                    logger.warn('Player not found (ds_player_id=%s)', player_id)
                    continue

                # dynamically collect player stats
                game_player_stats = {}
                for stat in stat_keys:
                    ds_key = _ds_mapping_game_stats_live.get(stat)
                    if ds_key is not None:
                        value_type = _get_stat_value_type(stat)
                        game_player_stats[stat] = xml_value(player_node, ds_key, value_type)

                # update stats of player on each game
                await async_upsert(GamePlayerStats, year=year, game=game, player=player, defaults=game_player_stats)
                logger.info('Processed live game player stats (game_id=%s, player_id=%s)', game.id, player_id)


async def import_game_player_stat_distance(ftp_client: aioftp.Client, file_path: str, file_stat: dict):
    """Import game player stat:distance from DS
    The file which outputs the top 20 players that have the highest distance stat on each game of the season.
    source file: ranking/rank_tracking_distance-{COMPETITION_ID}.xml
    """
    async with process_path_if_needed(file_path, file_stat) as should_process:
        if not should_process:
            return

        # read XML file
        file_content = await read_file_to_str(ftp_client, file_path)
        root = ET.fromstring(file_content)  # Football node
        report_node = root.find('RankTrackingDistanceReport')
        year = xml_value(report_node, 'Year', int)
        competition_id = xml_value(report_node, 'GameKindID', int)

        # check competition exists
        competition = await async_find_first(Competition, id=competition_id)
        if not competition:
            logger.warn('Competition not found (id=%s).', competition_id)
            return

        for rank_info in report_node.findall('RankInfo'):
            player_id = xml_value(rank_info, 'PlayerID', int)
            club_id = xml_value(rank_info, 'TeamID', int)
            game_id = xml_value(rank_info, 'GameID', int)
            club = None
            player = None

            # check game exists
            game = await async_find_first(Game, id=game_id)
            if not game:
                logger.warn('Game not found (id=%s).', game_id)
                continue

            # check player and club exist
            club_player = await async_find_first(
                ClubPlayer,
                club__ds_club_id=club_id,
                player__ds_player_id=player_id,
                year=year,
                select_related=('club', 'player'),
            )
            if club_player:
                club = club_player.club
                player = club_player.player
            if not player or not club:
                logger.warn('Club or Player not found (year=%s, club_id=%s, player_id=%s).', year, club_id, player_id)
                continue

            distance = xml_value(rank_info, 'Distance', Decimal)

            # update game player stat:distance
            await async_upsert(GamePlayerStats, year=year, game=game, player=player, defaults={'distance': distance})
            logger.info(
                'Processed game player stat:distance (year=%s, game_id=%s, club_id=%s, player_id=%s)',
                year,
                game_id,
                club_id,
                player_id,
            )


async def import_game_player_stat_sprints(ftp_client: aioftp.Client, file_path: str, file_stat: dict):
    """Import game player stat:sprints from DS
    The file which outputs the top 20 players that have the highest sprints stat on each game of the season.
    source file: ranking/rank_tracking_sprint-{COMPETITION_ID}.xml
    """
    async with process_path_if_needed(file_path, file_stat) as should_process:
        if not should_process:
            return

        # read XML file
        file_content = await read_file_to_str(ftp_client, file_path)
        root = ET.fromstring(file_content)  # Football node
        report_node = root.find('RankTrackingSprintReport')
        year = xml_value(report_node, 'Year', int)
        competition_id = xml_value(report_node, 'GameKindID', int)

        # check competition exists
        competition = await async_find_first(Competition, id=competition_id)
        if not competition:
            logger.warn('Competition not found (id=%s).', competition_id)
            return

        for rank_info in report_node.findall('RankInfo'):
            player_id = xml_value(rank_info, 'PlayerID', int)
            club_id = xml_value(rank_info, 'TeamID', int)
            game_id = xml_value(rank_info, 'GameID', int)
            club = None
            player = None

            # check game exists
            game = await async_find_first(Game, id=game_id)
            if not game:
                logger.warn('Game not found (id=%s).', game_id)
                continue

            # check player and club exist
            club_player = await async_find_first(
                ClubPlayer,
                club__ds_club_id=club_id,
                player__ds_player_id=player_id,
                year=year,
                select_related=('club', 'player'),
            )
            if club_player:
                club = club_player.club
                player = club_player.player
            if not club or not player:
                logger.warn('Club or Player not found (year=%s, club_id=%s, player_id=%s).', year, club_id, player_id)
                continue

            sprints = xml_value(rank_info, 'SprintCnt', int)

            # upsert game player stat:sprints
            await async_upsert(GamePlayerStats, year=year, game=game, player=player, defaults={'sprints': sprints})
            logger.info(
                'Processed game player stat:sprints (year=%s, game_id=%s, club_id=%s, player_id=%s)',
                year,
                game_id,
                club_id,
                player_id,
            )
