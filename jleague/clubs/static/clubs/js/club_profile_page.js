const navContainer = (() => {
  const nav = document.querySelector('.club-profile-nav');
  const navChecker = document.querySelector('.club-profile-nav-sticky-checker');
  const navEls = [...nav.querySelectorAll('a')];

  let observers = [];

  function init() {
    if ('IntersectionObserver' in window) {
      for (let observer of observers) {
        observer.disconnect();
      }
      observers = [];

      const observer = new IntersectionObserver((entries, observer) => {
        entries.forEach((entry) => {
          if (!entry.isIntersecting && navChecker.getBoundingClientRect().top < 100) {
            nav.classList.add('sticky');
            navChecker.classList.add('active');
          } else {
            nav.classList.remove('sticky');
            navChecker.classList.remove('active');
          }
        });
      });
      observer.observe(navChecker);
      observers.push(observer);

      navEls.forEach((el) => {
        const section = el.getAttribute('data-section');
        const contentEl = document.querySelector('#' + section);
        if (!contentEl) return;

        const observer = new IntersectionObserver(
          (entries, observer) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                activeItem(el);
              }
            });
          },
          { threshold: [0.1] }
        );
        observer.observe(contentEl);
        observers.push(observer);
      });
    } else {
      handleScroll();
      window.addEventListener('scroll', handleScroll);
    }
  }

  function handleScroll() {
    if (!nav) return;

    if (navChecker.getBoundingClientRect().top < 100) {
      nav.classList.add('sticky');
    } else {
      nav.classList.remove('sticky');
    }
  }

  function activeItem(itemEl) {
    const section = itemEl.getAttribute('data-section');

    function handleActiveItem(el) {
      if (el.getAttribute('data-section') === section) {
        el.classList.add('active');
      } else {
        el.classList.remove('active');
      }
    }

    navEls.forEach((el) => handleActiveItem(el));
  }

  return { init };
})();

const clubSelector = (function () {
  const containerEl = document.querySelector('.club-selector');
  const selectorEl = containerEl && containerEl.querySelector('.club-selector__current');

  function init() {
    selectorEl.onclick = (event) => {
      event.stopPropagation();
      if (containerEl.classList.contains('club-selector--closed')) {
        containerEl.classList.remove('club-selector--closed');
      } else {
        containerEl.classList.add('club-selector--closed');
      }
    };

    window.addEventListener('click', (event) => {
      if (!event.target.matches('.dropdown-group__body')) {
        containerEl.classList.add('club-selector--closed');
      }
    });
  }

  return { init };
})();

const stadiums = (function () {
  function init() {
    let slider;
    const sliderElm = '.stadium-photo-slider';
    const sliderOption = {
      slidesPerView: 1,
      spaceBetween: 0,
      freeMode: true,
      grabCursor: true,
      navigation: {
        nextEl: '.swiper-button-next',
        prevEl: '.swiper-button-prev',
      },
      pagination: {
        el: '.swiper-pagination',
        type: 'bullets',
      },
      breakpoints: {
        // when window width is >= 1200px
        1200: {
          slidesPerView: 4,
        },
      },
    };

    slider = new Swiper(sliderElm, sliderOption);
    slider.on('tap', function (swiper, event) {
      // popup image if window.innerWidth >= 1200
      if (event.target.nodeName === 'IMG' && window.innerWidth >= 1200) {
        $.magnificPopup.open(
          {
            items: {
              src: event.target.src,
            },
            type: 'image',
          },
          0
        );
      }
    });
  }

  return { init };
})();

$(document).ready(function () {
  // goal highlight videos section
  const clubGoalHighlights = new GoalHighlights('.club-profile .goal-highlights__body');
  clubGoalHighlights.setClub(selectedClubId);
  clubGoalHighlights.loadVideos();

  // players section
  clubPlayersSection.init();

  // stats section
  clubStatsSection.init();

  clubSelector.init();
  stadiums.init();

  // sticky nav
  const observer = new MutationObserver(navContainer.init);
  observer.observe(document, { childList: true, subtree: true });

  // latest videos section
  initPopupVideo('.popup-video', { mainClass: 'club-profile-popup-video' });
});
