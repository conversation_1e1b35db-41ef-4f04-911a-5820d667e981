@import 'setup/variable';
@import 'setup/mixin';

.sidebar {
  display: flex;
  flex-direction: column;
  height: 100vh; // fallback
  height: 100dvh;
  max-height: -webkit-fill-available;
  width: 56px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9000;
  background-color: var(--color-earth-2);
  border-right: solid 1px transparent;
  transition: all 300ms linear, width 250ms linear;

  .sidebar__header {
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .sidebar__body {
    padding: 0 8px 32px;
    max-height: calc(100vh - 325px);
    overflow-y: auto;
    @include scrollbars();
    transition: all 300ms linear, width 250ms linear;
    visibility: hidden;
    opacity: 0;
    padding-top: 16px;
  }

  .sidebar__content {
    animation: xspring-reset 450ms ease forwards;
    will-change: transform;
  }

  .sidebar-control,
  .sidebar-tabs {
    display: flex;
    align-items: center;
    overflow: hidden;
  }

  .sidebar-control {
    padding: 8px;
    justify-content: space-between;
    border-bottom: 1px solid var(--color-earth-3);
  }

  .sidebar-tabs {
    margin: 16px 16px 0;
    gap: 16px;
    transition: all 300ms ease-in-out;
    @include scrollbars();
    overflow-x: auto;

    .display-none {
      display: none;
    }

    .sidebar-tabs__item {
      color: var(--color-black);
      font-family: var(--font-overpass);
      font-size: 16px;
      font-weight: 900;
      line-height: 20px;
      letter-spacing: 0px;
      text-align: center;
      transition: all 300ms ease-in-out;
      padding: 8px 0;
      position: relative;
      width: 0;
      white-space: nowrap;

      &::after {
        content: '';
        background-color: var(--color-red);
        height: 0;
        width: 100%;
        position: absolute;
        bottom: 0;
        left: 0;
        opacity: 0;
        transition: height 250ms ease-in-out, opacity 300ms linear;
      }

      &:hover {
        cursor: pointer;
        color: var(--color-red);

        &::after {
          height: 4px;
          opacity: 1;
        }
      }

      &.sidebar-tabs__item--active {
        color: var(--color-red);

        &::after {
          height: 4px;
          opacity: 1;
        }
      }
    }
  }

  .sidebar-toggle {
    background: transparent;
    border: none;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40px;
    width: 40px;
    position: relative;
    padding: 8px;
    border-radius: 5px;

    .icon {
      transition: all 500ms linear;
      height: 24px;
      width: 24px;
    }
  }

  .competition-dropdown {
    transition: all 300ms ease-in-out;

    .competition-dropdown__value {
      padding: 8px;
      border-radius: 5px;

      &:hover {
        background-color: var(--color-white);
      }
    }

    .competition-dropdown__items {
      right: auto;
      top: 40px;
      left: 0;

      .dropdown-list {
        position: fixed;
        box-shadow: 0 0 1px 0 var(--color-earth-4);
      }
    }
  }

  .sidebar-tabs,
  .competition-dropdown {
    width: 0;
    opacity: 0;
    pointer-events: none;
  }

  .sidebar__trigger {
    transition: none !important;

    .trigger-label {
      display: none;

      .trigger-label-show,
      .trigger-label-hide {
        text-align: center;
        font-feature-settings: 'clig' off, 'liga' off;
        font-family: var(--font-barlow-condensed);
        font-size: 18px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px;
        letter-spacing: 1.5px;
        text-transform: uppercase;
        display: none;
      }

      .trigger-label-show {
        color: var(--color-earth-4);
      }

      .trigger-label-hide {
        color: var(--color-white);
      }
    }

    .trigger-icon {
      display: none;

      .trigger-icon-show,
      .trigger-icon-hide {
        display: none;
      }
    }
  }

  &.sidebar--opened {
    width: 224px;

    .sidebar__body {
      visibility: visible;
      opacity: 1;
    }

    .competition-dropdown,
    .sidebar-tabs {
      width: auto;
      opacity: 1;
      pointer-events: auto;

      .sidebar-tabs__item {
        width: 100%;
      }
    }

    .sidebar-toggle {
      .icon {
        transform: rotateY(180deg);
      }
    }

    .sidebar__content {
      animation: xspring 900ms ease forwards;
      will-change: transform;
    }
  }

  &:not(.sidebar--opened) {
    .sidebar__trigger {
      position: absolute;
      top: 55px;
      left: 0;
      width: 100%;
      height: 100%;
      cursor: pointer;
    }
  }

  @media (hover: hover) {
    // avoid unexpected hover state on touch device
    &:hover:not(.sidebar--opened) {
      background-color: var(--color-white);
      border-right-color: var(--color-earth-3);
    }

    .sidebar-toggle {
      &:hover {
        cursor: pointer;
        background-color: var(--color-white);
      }
    }
  }
}

body {
  transition: all 300ms ease-in-out;

  &.body--sidebar {
    padding-left: 56px;
  }

  &.body--sidebar-opened {
    padding-left: 224px;
  }

  &.lang-th {
    .sidebar .sidebar-tabs .sidebar-tabs__item {
      font-weight: 700;
    }
  }

  &.lang-vi {
    .sidebar .sidebar-tabs .sidebar-tabs__item {
      font-weight: 600;
    }
  }

  @media screen and (max-width: 991px) {
    &.body--sidebar-opened {
      padding-left: 56px;
    }
  }
}

@media screen and (max-width: 576px) {
  .sidebar {
    height: 56px;
    width: 100%;
    bottom: 0;
    top: auto;

    .sidebar-toggle {
      display: none;
    }

    .sidebar-tabs {
      margin: 0 auto;
    }

    .sidebar__trigger {
      display: flex;
      justify-content: space-between;
      background-color: var(--color-earth-2);

      .trigger-icon {
        height: 24px;
        width: 24px;
        align-items: center;
        justify-content: center;
      }
    }

    .sidebar__content {
      animation: yspring-reset 450ms ease forwards;
      will-change: transform;
    }

    &.sidebar--opened,
    &:not(.sidebar--opened) {
      border-right: none;

      .sidebar__trigger {
        padding: 16px;
        top: auto;
        bottom: 0;
        position: fixed;
        height: 56px;
        width: 100%;
        cursor: pointer;
        transition: background 300ms linear;
        border-top: solid 1px transparent;

        .trigger-icon,
        .trigger-label {
          display: flex;
          transition: all 300ms linear;
        }
      }

      &:hover {
        background-color: var(--color-earth-2);
      }
    }

    &:not(.sidebar--opened) {
      .sidebar__trigger {
        border-top-color: var(--color-earth-3);

        .trigger-label {
          .trigger-label-show {
            display: block;
          }

          .trigger-label-hide {
            display: none;
          }
        }

        .trigger-icon {
          .trigger-icon-show {
            display: block;
          }

          .trigger-icon-hide {
            display: none;
          }
        }
      }
    }

    &.sidebar--opened {
      height: 100vh; // fallback
      height: 100dvh;
      width: 100%;

      .sidebar__trigger {
        background-color: var(--color-j1);
        border-top-color: var(--color-black);

        .trigger-label {
          .trigger-label-show {
            display: none;
          }

          .trigger-label-hide {
            display: block;
          }
        }

        .trigger-icon {
          .trigger-icon-show {
            display: none;
          }

          .trigger-icon-hide {
            display: block;
          }
        }
      }

      .sidebar__content {
        animation: yspring 1000ms ease forwards;
        will-change: transform;
        height: calc(100vh - 56px); // fallback
        height: calc(100dvh - 56px);
      }

      .sidebar__body {
        max-height: calc(100vh - 165px); // fallback
        max-height: calc(100dvh - 365px);
      }
    }
  }

  body.body--sidebar {
    padding-left: 0;
    height: 100%;

    &.body--sidebar-opened {
      overflow: hidden;
    }
  }
}

@keyframes xspring {
  0% {
    transform: scale3d(0.96, 1, 1);
  }

  20% {
    transform: scale3d(1.1, 1, 1);
  }

  40% {
    transform: scale3d(0.98, 1, 1);
  }

  60% {
    transform: scale3d(1.05, 1, 1);
  }

  80% {
    transform: scale3d(1.01, 1, 1);
  }

  100% {
    transform: scale3d(1, 1, 1);
  }
}

@keyframes xspring-reset {
  0% {
    transform: scale3d(1, 1, 1);
  }

  50% {
    transform: scale3d(1.1, 1, 1);
  }

  100% {
    transform: scale3d(0.96, 1, 1);
  }
}

@keyframes yspring {
  0% {
    transform: scale3d(1, 0.96, 1);
  }

  20% {
    transform: scale3d(1, 1.1, 1);
  }

  40% {
    transform: scale3d(1, 0.98, 1);
  }

  60% {
    transform: scale3d(1, 1.05, 1);
  }

  80% {
    transform: scale3d(1, 1.01, 1);
  }

  100% {
    transform: scale3d(1, 1, 1);
  }
}

@keyframes yspring-reset {
  0% {
    transform: scale3d(1, 1, 1);
  }

  50% {
    transform: scale3d(1, 1.1, 1);
  }

  100% {
    transform: scale3d(1, 0.96, 1);
  }
}

@import 'matches';
@import 'standings';
@import 'stats';
@import 'sidebar-cta';
