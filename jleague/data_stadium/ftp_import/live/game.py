import aioftp
import channels.layers
import logging
import xml.etree.ElementTree as ET

from asgiref.sync import async_to_sync
from decimal import Decimal

from match.models import Game, Referee
from ..helpers import (
    async_find_first,
    async_save_item,
    parse_date,
    parse_datetime,
    async_upsert,
    process_path_if_needed,
    read_file_to_str,
    xml_value,
)

logger = logging.getLogger(__name__)


def parse_team_results(node):
    if not node:
        return None

    first_half_state_node = None
    second_half_state_node = None
    for game_state_node in node.findall("GameState"):
        if game_state_node.attrib["ID"] == "1":
            first_half_state_node = game_state_node
        elif game_state_node.attrib["ID"] == "2":
            second_half_state_node = game_state_node

    return {
        "team_id": xml_value(node, "ID", int),
        "score": xml_value(node, "Score", int),
        "first_half_score": (
            xml_value(first_half_state_node, "Score", int)
            if first_half_state_node
            else 0
        ),
        "second_half_score": (
            xml_value(second_half_state_node, "Score", int)
            if second_half_state_node
            else 0
        ),
        "sh": xml_value(node, "Shoot", int) or 0,
        "ck": xml_value(node, "CK", int) or 0,
        "fk": (xml_value(node, "FKD", int) or 0) + (xml_value(node, "FKI", int) or 0),
        "pk": xml_value(node, "PK", int) or 0,
        "yellow_cards": xml_value(node, "Yellow", int) or 0,
        "red_cards": xml_value(node, "Red", int) or 0,
    }


# Remove "undefined" player name
def filter_player_name(value: str):
    return value if value and ("undefined" not in value.lower()) else ""


async def parse_warnings(data, home_id, away_id):
    result = []

    for item in data:
        state_id = xml_value(item, "StateID", int)
        time = xml_value(item, "Time")
        player_id = xml_value(item, "PlayerID", int)
        uniform_no = xml_value(item, "PlayerUni", int)
        player_name = xml_value(item, "PlayerName")
        team_id = xml_value(item, "TeamID", int)
        divide = xml_value(item, "Divide")
        is_second_yellow_card = xml_value(item, "SecondF", lambda x: x == "1")

        team = None
        if team_id == home_id:
            team = "home"
        elif team_id == away_id:
            team = "away"

        warning_type = None
        if divide == "1":
            warning_type = "yellow_card"
        elif divide == "2":
            warning_type = "red_card"

        result.append(
            {
                "state_id": state_id,
                "time": time,
                "player_id": player_id,  # value will be -1 if it's staff or manager
                "player_name": player_name,  # need this for staff or manager
                "uniform_no": uniform_no,
                "team_id": team_id,
                "team": team,
                "type": warning_type,
                "is_second_yellow_card": is_second_yellow_card,
            }
        )

    return result


async def parse_goals(data, home_id, away_id):
    result = []

    for item in data:
        state_id = xml_value(item, "StateID", int)
        time = xml_value(item, "Time")
        player_id = xml_value(item, "GPlayerID", int)
        uniform_no = xml_value(item, "GPlayerUni", int)
        team_id = xml_value(item, "TeamID", int)
        is_penalty_goal = xml_value(item, "PKGoalF", lambda x: x == "1")
        is_own_goal = xml_value(item, "OwnGoalF", lambda x: x == "1")
        score_team_id = xml_value(item, "ScoreTeamID", int)
        order = int(item.attrib["No"])

        team = None
        if team_id == home_id:
            team = "home"
        elif team_id == away_id:
            team = "away"

        result.append(
            {
                "state_id": state_id,
                "time": time,
                "player_id": player_id,
                "uniform_no": uniform_no,
                "team_id": team_id,
                "team": team,
                "is_penalty_goal": is_penalty_goal,
                "is_own_goal": is_own_goal,
                "score_team_id": score_team_id,
            }
        )

    return result


async def parse_penalties(data, home_id, away_id):
    result = []

    for item in data:
        if item.attrib["HV"] == "1":
            team = "home"
            team_id = home_id
        elif item.attrib["HV"] == "2":
            team = "away"
            team_id = away_id
        else:
            continue

        players = item.findall("Player")

        for player in players:
            no = int(player.attrib["No"] or 6)
            serial_no = int(player.attrib["SerialNo"] or 6)
            player_id = xml_value(player, "PlayerID", int)
            uniform_no = xml_value(player, "PlayerUni", int)
            success = xml_value(player, "SuccessF", lambda x: x != "0")

            result.append(
                {
                    "no": no,
                    "serial_no": serial_no,
                    "player_id": player_id,
                    "uniform_no": uniform_no,
                    "team_id": team_id,
                    "team": team,
                    "success": success,
                }
            )

    result = sorted(result, key=lambda x: x["serial_no"])
    return result


async def import_live_game(ftp_client: aioftp.Client, file_path: str, file_stat: dict):
    """Import live game info from DS
    source file: {MATCH_ID}/live_game.xml
    doc file: https://docs.google.com/spreadsheets/d/1sIpbQxRQbgzXNAdzASqqo_wx99wBnEyH/edit#gid=3266047
    """
    async with process_path_if_needed(file_path, file_stat) as should_process:
        if not should_process:
            return

        # read XML file
        file_content = await read_file_to_str(ftp_client, file_path)
        root = ET.fromstring(file_content)  # Football node
        report_node = root.find("GameReport")
        game_id = xml_value(report_node, "GameID", int)

        # check game exists
        game: Game = await async_find_first(Game, id=game_id)
        if not game:
            logger.warn("Game not found, game_id=%d", game_id)
            return

        # upsert referee into the db
        referee_id = xml_value(report_node, "RefereeID", int)
        referee = None
        if referee_id:
            referee = await async_find_first(Referee, id=referee_id)
            referee_name = xml_value(report_node, "Referee", str)
            if not referee and referee_name:
                referee, _ = await async_upsert(
                    Referee, id=referee_id, defaults={"name": referee_name}
                )
                logger.info("Processed Referee %d", referee_id)

        # patch incoming data of Game
        game.season_id = xml_value(report_node, "SeasonID", int)
        game.game_date_raw = xml_value(report_node, "GameDate")
        game.game_time_raw = xml_value(report_node, "StartTime")
        game.situation_id = xml_value(report_node, "SituationID", int)
        game.game_date = parse_date(game.game_date_raw)
        game.game_full_date = parse_datetime(game.game_date_raw, game.game_time_raw)

        if referee:
            game.referee = referee

        if not game.details:
            game.details = {}

        game.details["day_night"] = xml_value(report_node, "DayNight", int)
        game.details["state_id"] = xml_value(report_node, "StateID", int)
        game.details["time"] = xml_value(report_node, "Time")
        game.details["time_in_half"] = xml_value(report_node, "Half", int)
        game.details["half_end"] = xml_value(report_node, "HalfEndF", int)

        # patch game team stats count e.g. score, assist, offside, yellow, red
        home_team_id = None
        away_team_id = None
        for team_info_node in report_node.findall("TeamInfo"):
            if team_info_node.attrib["HV"] == "1":
                game.home_score = xml_value(team_info_node, "Score", int)
                game.details["home_team"] = parse_team_results(team_info_node)
                home_team_id = (game.details["home_team"] or {}).get("team_id", None)
            elif team_info_node.attrib["HV"] == "2":
                game.away_score = xml_value(team_info_node, "Score", int)
                game.details["away_team"] = parse_team_results(team_info_node)
                away_team_id = (game.details["away_team"] or {}).get("team_id", None)

        # patch game events
        game.details["warnings"] = await parse_warnings(
            report_node.findall("WarningInfo"), home_team_id, away_team_id
        )
        game.details["goals"] = await parse_goals(
            report_node.findall("GoalInfo"), home_team_id, away_team_id
        )
        game.details["penalties"] = await parse_penalties(
            report_node.findall("PKInfo"), home_team_id, away_team_id
        )

        # patch weather and spectators
        game_end_info_node = report_node.find("GameEndInfo")
        if game_end_info_node:
            game.humidity = xml_value(game_end_info_node, "Humidity", Decimal)
            game.spectators = xml_value(game_end_info_node, "Spectators", int)
            game.temperature = xml_value(game_end_info_node, "Temperature", Decimal)

        await async_save_item(game)

        # # Prepare data and build message
        # message = {
        #     "game_id": game_id,
        #     "home_score": game.home_score,
        #     "away_score": game.away_score,
        #     "details": game.details,
        # }

        # # broadcast message to clients
        # channel_layer = channels.layers.get_channel_layer()

        # await(channel_layer.group_send)(
        #     f"match_{game_id}",
        #     {"type": "score.update.message", "message": message},
        # )

        logger.info("Processed live game (id=%s)", game_id)
