@import 'partner-logo';
@import 'partner-text';

.partners-area {
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  max-width: 800px;
  padding: 66px 40px;

  .partners-area__row {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    // padding: 0 48px;
  }

  .partner-group {
    display: flex;
    flex-direction: column;

    .partner-group__header,
    .partner-group__body {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: center;
    }

    // .partner-group__header {
    // margin-bottom: 10px;
    // }

    .partner-group__body {
      .partner-logo {
        margin: 0 13px 20px;
      }

      // &.partner-group__body--grid {
      //   display: grid;
      //   grid-template-columns: repeat(8, 1fr);

      //   .partner-item {
      //     grid-column: span 2;

      //     // handle last row with 3 items
      //     &:nth-last-child(3):nth-child(4n + 1) {
      //       grid-column-end: -6;
      //     }

      //     // handle last row with 2 items
      //     &:nth-last-child(2):nth-child(4n + 1) {
      //       grid-column-end: -5;
      //     }

      //     // handle last row with 1 item
      //     &:last-child:nth-child(4n + 1) {
      //       grid-column-end: -4;
      //     }
      //   }
      // }
    }

    &.partner-group--broadcast {
      .partner-group__header {
        display: flex;
        flex-direction: row;
      }
    }
  }

  // .partners-area__row.partners-area__row--multi-groups {
  //   display: grid;
  //   grid-template-columns: repeat(8, 1fr);
  //   width: 600px;
  //   margin: 0 auto;

  //   .partner-group {
  //     grid-column: span 2;

  //     .partner-group__header {
  //       margin-bottom: 0px;
  //     }

  //     // handle last row with 3 items
  //     &:nth-last-child(3):nth-child(4n + 1) {
  //       grid-column-end: -6;
  //     }

  //     // handle last row with 2 items
  //     &:nth-last-child(2):nth-child(4n + 1) {
  //       grid-column-end: -5;
  //     }

  //     // handle last row with 1 item
  //     &:last-child:nth-child(4n + 1) {
  //       grid-column-end: -4;
  //     }
  //   }
  // }

  @media screen and (max-width: 768px) {
    .partners-area__row {
      &:not(.partners-area__row--multi-groups) {
        .partner-group {
          &.partner-group--broadcast {
            .partner-group__header {
              flex-direction: column;
            }
          }

          .partner-group__body {
            display: grid;
            grid-template-columns: repeat(6, 1fr);

            .partner-item {
              grid-column: span 2;

              // handle last row with 2 items
              &:last-child:nth-child(3n - 1) {
                grid-column-end: -2;
              }

              &:nth-last-child(2):nth-child(3n + 1) {
                grid-column-end: 4;
              }

              // handle last row with 1 item
              &:last-child:nth-child(3n - 2) {
                grid-column-end: 5;
              }
            }
          }
        }
      }

      &.partners-area__row--multi-groups {
        align-items: center;
        // max-width: 450px;
        margin: 0 auto;
      }
    }
  }

  @media screen and (max-width: 576px) {
    padding: 40px 20px;

    .partners-area__row {
      &:not(.partners-area__row--multi-groups) {
        .partner-group {
          .partner-group__body {
            display: flex;
            // &.partner-group__body--grid {
            //   display: flex;
            // }
          }
        }
      }

      .partners-area__row--multi-groups {
        display: flex;
        width: unset;
      }
    }
  }
}
