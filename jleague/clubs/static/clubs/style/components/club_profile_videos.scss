@import 'setup/variable';
@import 'setup/mixin';

.body-club-profile {
  .goal-highlights-container {
    margin-top: 80px;
    background: var(--color-black);

    .goal-highlights-content {
      // max-width: 1920px;
      overflow: hidden;
      margin: 0 auto;

      &::after {
        content: '';
        display: block;
        height: 1px;
        width: 100%;
        background-color: var(--color-earth-4);
      }
    }
  }

  .club-profile-videos {
    background: var(--color-black);
    padding: 80px 0;
    position: relative;

    .club-profile-videos__header {
      margin-bottom: 40px;

      .club-profile-videos__header__title {
        @include section-header-title-text();
      }
    }

    .popup-video__info {
      .popup-video__info__title,
      .popup-video__info__date {
        color: var(--color-white);
      }
    }
  }

  @media screen and (max-width: 1199px) {
    .goal-highlights-container {
      margin-top: 40px;

      .goal-highlights {
        .goal-highlights__header {
          .title {
            font-size: 24px;
            line-height: 28px;
          }
        }
      }
    }

    .club-profile-videos {
      // margin-top: 40px;
      padding: 50px 0;

      .club-profile-videos__header {
        .club-profile-videos__header__title {
          font-size: 24px;
          line-height: 28px;
        }
      }
    }
  }

  @media screen and (max-width: 767px) {
    .goal-highlights-container {
      .goal-highlights {
        .goal-highlights__header {
          .title {
            font-size: 20px;
            line-height: 24px;
          }
        }
      }
    }

    .club-profile-videos {
      .club-profile-videos__header {
        .club-profile-videos__header__title {
          font-size: 20px;
          line-height: 24px;
        }
      }
    }
  }

  @media screen and (max-width: 576px) {
    .goal-highlights-container {
      .goal-highlights {
        .goal-highlights__header {
          margin-bottom: 24px;

          .title {
            font-size: 16px;
            line-height: 20px;
          }
        }
      }
    }

    .club-profile-videos {
      .club-profile-videos__header {
        margin-bottom: 24px;

        .club-profile-videos__header__title {
          font-size: 16px;
          line-height: 20px;
        }
      }
    }
  }
}

body.lang-th {
  .club-profile-videos {
    .latest-videos .explore-button,
    .club-profile-videos__header .club-profile-videos__header__title {
      // @include font-thai();
      font-weight: 700;
    }
  }
}

body.lang-vi {
  .club-profile-videos {
    .latest-videos .explore-button,
    .club-profile-videos__header .club-profile-videos__header__title {
      // @include font-vietnamese();
      font-weight: 600;
    }
  }
}
