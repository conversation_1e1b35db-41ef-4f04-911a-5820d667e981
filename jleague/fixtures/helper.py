import json
from calendar import monthrange
from datetime import date, datetime
from clubs.models import ClubCompetition, ClubExtraInfo
from core.constants import (
    ALLOW_COMPETITIONS,
    CUP_COMPETITIONS,
    LEAGUE_COMPETITIONS,
    PLAY_OFF_COMPETITIONS,
)
from core.enums import (
    GameKindEnum,
    GameKindShortNameEnum,
    GameSituationEnum,
    SeasonEnum,
)
from core.helpers import get_local_time, get_translation_value
from dateutil.relativedelta import relativedelta
from django.db.models import Max, Q
from django.db.models.expressions import OuterRef, Subquery
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.utils.timezone import now
from match.models import Game, GameExtraInfo, GameTicketLink
from stadiums.models import StadiumExtraInfo
from utils.helpers import (
    get_display_data_year,
    get_competition_system,
    get_cup_stages,
    get_season_slug,
    get_stage_schedule_nums,
    humanize_stage_name,
    is_off_season,
    japan_tz,
    to_tz_info,
    start_of,
)


tournament_occasions = {}
playoff_stages = {}


def get_tournament_occasions(year=None):
    year = year or get_display_data_year()

    if tournament_occasions.get(year) is None:
        tournament_occasions[year] = {}
        for competition in LEAGUE_COMPETITIONS:
            occasion = Game.objects.filter(
                competition__slug=competition,
                year=year,
            ).aggregate(max=Max("occasion_no"))
            if occasion and occasion.get("max"):
                tournament_occasions[year][competition] = [
                    i for i in range(1, occasion["max"] + 1)
                ]
    return tournament_occasions[year]


def get_playoff_stages(year=None):
    year = year or get_display_data_year()

    if playoff_stages.get(year) is None:
        playoff_stages[year] = {}
        for competition in PLAY_OFF_COMPETITIONS:
            occasion = Game.objects.filter(
                competition__slug=competition,
                year=year,
            ).aggregate(max=Max("occasion_no"))
            if occasion and occasion.get("max"):
                playoff_stages[year][competition] = [
                    i for i in range(1, occasion["max"] + 1)
                ]
    return playoff_stages[year]


def get_fixture_stage_selector_choices(competition_slug, year):
    choices = []

    if competition_slug in LEAGUE_COMPETITIONS:
        stages = get_tournament_occasions(year)[competition_slug]
        for stage in stages:
            lines = [
                i
                for i in humanize_stage_name(competition_slug, None, stage, False, year)
                if i
            ]
            choices.append(
                {
                    "label": (
                        mark_safe("<br>".join(lines)) if len(lines) > 1 else lines[0]
                    ),
                    "value": stage,
                }
            )
    elif competition_slug in PLAY_OFF_COMPETITIONS:
        stages = get_playoff_stages(year)[competition_slug]
        for stage in stages:
            lines = [
                i
                for i in humanize_stage_name(competition_slug, None, stage, False, year)
                if i
            ]
            choices.append(
                {
                    "label": (
                        mark_safe("<br>".join(lines)) if len(lines) > 1 else lines[0]
                    ),
                    "value": stage,
                }
            )
    elif competition_slug in CUP_COMPETITIONS:
        stages = get_cup_stages(competition_slug, year)
        for stage in stages:
            lines = [
                i
                for i in humanize_stage_name(
                    competition_slug, stage[0], stage[1], False, year
                )
                if i
            ]
            choices.append(
                {
                    "label": (
                        mark_safe("<br>".join(lines)) if len(lines) > 1 else lines[0]
                    ),
                    "value": f"{stage[0]}|{stage[1]}",
                }
            )

    return choices


def _get_state_url(
    competition_slug, year, occasion_no=None, stage=None, stage_round=None
):
    view_name = None
    kwargs = {
        "competition_slug": competition_slug,
        "year": year,
    }
    if occasion_no:
        view_name = "fixtures_stage_tournament"
        kwargs["occasion_no"] = occasion_no
    elif stage is not None and stage_round is not None:
        view_name = "fixtures_stage_cup"
        kwargs["stage"] = stage
        kwargs["stage_round"] = stage_round
    return reverse(view_name, kwargs=kwargs)


def _get_nav_data_for_stage(game: Game, stages):
    if not stages:
        return {}

    prev_stage_name = ""
    next_stage_name = ""
    prev_stage_url = ""
    next_stage_url = ""
    current_stage_name = ""
    current_stage_fullname = ""
    gk_slug = game.competition.slug

    if gk_slug in LEAGUE_COMPETITIONS:
        current_stage = game.occasion_no
        short_name = get_translation_value("GLOBAL_MATCHWEEK_SHORT")
        full_name = get_translation_value("GLOBAL_MATCHWEEK")
        current_stage_name = f"{short_name} {current_stage}"
        current_stage_fullname = f"{full_name} {current_stage}"
        current_stage_index = stages.index(current_stage)

        # add link to previous stage if current stage isn't first
        if current_stage != stages[0]:
            prev_occasion_no = stages[current_stage_index - 1]
            prev_stage_name = f"{short_name} {prev_occasion_no}"
            prev_stage_url = _get_state_url(
                gk_slug, game.year, occasion_no=prev_occasion_no
            )

        # add link to next stage if current stage isn't last
        if current_stage != stages[-1]:
            next_occasion_no = stages[current_stage_index + 1]
            next_stage_name = f"{short_name} {next_occasion_no}"
            next_stage_url = _get_state_url(
                gk_slug, game.year, occasion_no=next_occasion_no
            )

    elif gk_slug in PLAY_OFF_COMPETITIONS:
        current_stage = game.occasion_no
        short_name = get_translation_value("MATCH_STAGE_ROUND_SHORT")
        full_name = get_translation_value("MATCH_STAGE_ROUND")
        current_stage_name = f"{short_name} {current_stage}"
        current_stage_fullname = f"{full_name} {current_stage}"
        current_stage_index = stages.index(current_stage)
        if current_stage == 3:
            current_stage_name = get_translation_value("MATCH_STAGE_FINAL")
            current_stage_fullname = get_translation_value("MATCH_STAGE_FINAL")

        # add link to previous stage if current stage isn't first
        if current_stage != stages[0]:
            prev_occasion_no = stages[current_stage_index - 1]
            prev_stage_name = f"{short_name} {prev_occasion_no}"
            prev_stage_url = _get_state_url(
                gk_slug, game.year, occasion_no=prev_occasion_no
            )

        # add link to next stage if current stage isn't last
        if current_stage != stages[-1]:
            next_occasion_no = stages[current_stage_index + 1]
            if next_occasion_no == 3:
                next_stage_name = get_translation_value("MATCH_STAGE_FINAL")
            else:
                next_stage_name = f"{short_name} {next_occasion_no}"
            next_stage_url = _get_state_url(
                gk_slug, game.year, occasion_no=next_occasion_no
            )

    elif gk_slug in CUP_COMPETITIONS:
        stage_num = game.round
        season_slug = get_season_slug(
            gk_slug, game.season_id, game.schedule_no, game.year
        )
        stage_key = (season_slug, game.round)
        if game.season_id == SeasonEnum.GROUP_STAGE:
            stage_num = game.occasion_no
            stage_key = (season_slug, game.occasion_no)

        lines = [
            i
            for i in humanize_stage_name(
                gk_slug, season_slug, stage_num, True, game.year
            )
            if i
        ]
        current_stage_name = "".join(lines) if len(lines) > 1 else lines[0]
        # if "mw" not in current_stage_name.lower() and game.competition.slug == "acl":
        #     current_stage_name = lines[0]

        # add link to previous stage if current stage isn't first
        if stage_key != stages[0] and stage_key in stages:
            prev_stage, prev_stage_round = stages[stages.index(stage_key) - 1]
            prev_stage_url = _get_state_url(
                gk_slug, game.year, stage=prev_stage, stage_round=prev_stage_round
            )

            lines = [
                i
                for i in humanize_stage_name(
                    gk_slug, prev_stage, prev_stage_round, True, game.year
                )
                if i
            ]
            # too long text for group stage
            prev_stage_name = "".join(lines) if len(lines) > 1 else lines[0]
            # if "mw" not in prev_stage_name.lower() and game.competition.slug == "acl":
            #     prev_stage_name = lines[0]

        # add link to next stage if current stage isn't last
        if stage_key != stages[-1] and stage_key in stages:
            next_stage, next_stage_round = stages[stages.index(stage_key) + 1]
            next_stage_url = _get_state_url(
                gk_slug, game.year, stage=next_stage, stage_round=next_stage_round
            )

            lines = [
                i
                for i in humanize_stage_name(
                    gk_slug, next_stage, next_stage_round, True, game.year
                )
                if i
            ]
            # too long text for group stage
            next_stage_name = "".join(lines) if len(lines) > 1 else lines[0]
            # if "mw" not in next_stage_name.lower() and game.competition.slug == "acl":
            #     next_stage_name = lines[0]

    return {
        "prev_stage_name": prev_stage_name,
        "prev_stage_url": prev_stage_url,
        "next_stage_name": next_stage_name,
        "next_stage_url": next_stage_url,
        "current_stage_name": current_stage_name,
        "current_stage_fullname": current_stage_fullname,
    }


def _create_fixture_stage(game, stages):
    return {
        "nav": _get_nav_data_for_stage(game, stages),
        "competition": game.competition,
        "games": [],
    }


def _group_game_by_date(games, **kwargs):
    user_tz = to_tz_info(kwargs.get("timezone"))
    league_stages = {}
    playoff_stages = {}
    cup_stages = {}

    # check if year is passed through or use current year
    year = kwargs.get("year") or get_display_data_year()
    if year:
        league_stages = get_tournament_occasions(year)
        playoff_stages = get_playoff_stages(year)
        # cup_stages = get_cup_stages(year=year)
    else:
        league_stages = get_tournament_occasions()
        playoff_stages = get_playoff_stages()
        # cup_stages = get_cup_stages()

    competitions_stages = {
        **league_stages,
        **playoff_stages,
        # **cup_stages,
    }

    fixtures = {}
    games_by_date = {}
    game_ids = set()
    club_ids = set()
    stadium_ids = set()

    curr_date = now().astimezone(user_tz)

    # grouped games by game_date
    for game in games:
        # get ids
        game_ids.add(game.id)
        club_ids.add(game.home_team.id)
        club_ids.add(game.away_team.id)
        stadium_ids.add(game.stadium_id)

        date_format = (
            kwargs["group_date_format"]
            if kwargs.get("group_date_format")
            else "FIXTURE_DATE_FORMAT"
        )

        game_local_date_str = get_local_time(game.game_full_date, date_format, **kwargs)
        if games_by_date.get(game_local_date_str) is None:
            match_date = game.game_full_date.astimezone(user_tz).date()
            games_by_date[game_local_date_str] = {
                "date": game.game_full_date,
                "formatted_date": game_local_date_str,
                "is_today": match_date == curr_date.date(),
                "games": [],
            }
        games_by_date[game_local_date_str]["games"].append(game)

    club_infos = ClubExtraInfo.objects.filter(club_id__in=club_ids)
    extra_infos = GameExtraInfo.objects.filter(game_id__in=game_ids).select_related(
        "live_video",
        "live_video_thai",
        "live_video_bahasa",
        "live_video_vietnamese",
    )
    ticket_links = GameTicketLink.objects.filter(
        game_extra_info__game_id__in=game_ids
    ).select_related(
        "game_extra_info",
        "game_extra_info__game",
    )
    stadium_infos = None
    if kwargs.get("incl_stadium"):
        stadium_infos = StadiumExtraInfo.objects.filter(stadium_id__in=stadium_ids)

    afs_matches = [2023072604]  # !AFS Temporary

    # grouped games by game kind and game round
    for group_key, data in games_by_date.items():
        if len(data["games"]) <= 0:
            continue

        competition_rounds = {}

        # prepare stages data
        for game in sorted(data["games"], key=lambda x: (x.competition.id, x.round)):
            round_key = f"{game.competition.slug}|{game.round}"

            if competition_rounds.get(round_key) is None:
                if game.competition.slug in CUP_COMPETITIONS:
                    cup_stages = get_cup_stages(year=game.year)
                    stages = cup_stages.get(f"{game.year}_{game.competition.slug}")
                else:
                    stages = competitions_stages.get(game.competition.slug)
                competition_rounds[round_key] = _create_fixture_stage(game, stages)

            extra_info = None
            # check extra info
            for ei in extra_infos:
                if ei.game_id == game.id:
                    extra_info = ei
                    break

            if extra_info:
                if extra_info.inactive_match is True:
                    continue

            # populate stages with games
            formatted_game = game.format()
            is_home_jp = formatted_game["home"].get("allow_view_profile") is True
            is_away_jp = formatted_game["away"].get("allow_view_profile") is True

            # filter only jleague club
            if is_home_jp or is_away_jp or game.id in afs_matches:
                # check match has live
                if extra_info:
                    formatted_game["is_friday_night_match"] = (
                        extra_info.is_friday_night_match
                    )
                    formatted_game["is_kokuritsu_match"] = extra_info.is_kokuritsu_match

                    formatted_game["has_live"] = extra_info.live_video_enabled
                    formatted_game["has_giveaway_jersey"] = (
                        extra_info.has_giveaway_jersey
                    )
                    formatted_game["live_video_url"] = (
                        extra_info.live_video.video_url
                        if extra_info.live_video
                        else None
                    )
                    formatted_game["live_video_thai_url"] = (
                        extra_info.live_video_thai.video_url
                        if extra_info.live_video_thai
                        else None
                    )
                    formatted_game["live_video_bahasa_url"] = (
                        extra_info.live_video_bahasa.video_url
                        if extra_info.live_video_bahasa
                        else None
                    )
                    formatted_game["live_video_vietnamese_url"] = (
                        extra_info.live_video_vietnamese.video_url
                        if extra_info.live_video_vietnamese
                        else None
                    )

                # check stadium extra info
                if kwargs.get("incl_stadium"):
                    for si in stadium_infos:
                        if si.stadium_id == game.stadium_id:
                            formatted_game["stadium_local_names"] = si.t_name
                            break

                # patch club info
                club_info_count = 0
                for ci in club_infos:
                    if ci.club_id == formatted_game["home"]["id"]:
                        formatted_game["home"]["t_name"] = ci.t_name
                        formatted_game["home"]["t_name_short"] = ci.t_name_short
                        formatted_game["home"][
                            "four_letters_name"
                        ] = ci.four_letters_name
                        club_info_count += 1
                    elif ci.club_id == formatted_game["away"]["id"]:
                        formatted_game["away"]["t_name"] = ci.t_name
                        formatted_game["away"]["t_name_short"] = ci.t_name_short
                        formatted_game["away"][
                            "four_letters_name"
                        ] = ci.four_letters_name
                        club_info_count += 1

                    if club_info_count == 2:
                        break

                # check ticket links
                links = []
                for tl in ticket_links:
                    if tl.game_extra_info.game_id == game.id:
                        links.append(tl)
                formatted_game["ticket_links"] = links

                # use for tickets modal
                tmp_links = {}
                for i, link in enumerate(links):
                    if link.label:
                        tmp_links[link.label] = link.link
                    else:
                        key = f'{get_translation_value("GLOBAL_TICKET_LINK")} {i+1}'
                        tmp_links[key] = link.link

                formatted_game["formatted_ticket_links"] = json.dumps(tmp_links)

                # !round_key = f"{game.competition.slug}|{game.round}"
                competition_rounds[round_key]["games"].append(formatted_game)

        # remove match week without games
        for key, items in competition_rounds.items():
            if items.get("games") and len(items["games"]):
                if fixtures.get(group_key) is None:
                    fixtures[group_key] = {}
                fixtures[group_key][key] = items
                fixtures[group_key]["is_today"] = data["is_today"]

                # !AFS Temporary
                for game in items["games"]:
                    if game["id"] in afs_matches:
                        fixtures[group_key]["competition_name"] = get_translation_value(
                            "GLOBAL_COMPETITION_AFS_POWERED_BY_DOCOMO_FULL"
                        )
                        break

    return fixtures


def get_fixtures(
    competition_slug="all",
    year=None,
    month=None,
    club_slug="all",
    occasion_no=None,
    stage=None,
    stage_round=None,
    **kwargs,
):
    user_tz = to_tz_info(kwargs.get("timezone"))
    year = year or get_display_data_year()
    game_filter_by = {}
    game_exclude_list = {}

    # only these ids
    if kwargs.get("ids"):
        game_filter_by["id__in"] = kwargs["ids"]
    # exclude these ids
    if kwargs.get("excl_ids"):
        game_exclude_list["id__in"] = kwargs["excl_ids"]

    # filter by competition
    if competition_slug is None or competition_slug == "all":
        game_filter_by["competition__slug__in"] = ALLOW_COMPETITIONS
    else:
        game_filter_by["competition__slug"] = competition_slug

    # filter by year
    if year is not None:
        if competition_slug in ["all", *LEAGUE_COMPETITIONS]:
            game_filter_by["game_full_date__year"] = year
        elif competition_slug in CUP_COMPETITIONS:
            if month not in ["all", "latest", None]:
                game_filter_by["game_full_date__year"] = year
            else:
                game_filter_by["year"] = year
        else:
            game_filter_by["year"] = year

    if kwargs.get("date") and isinstance(kwargs.get("date"), date):
        game_filter_by["game_date"] = kwargs["date"]
    else:
        # filter by month
        if month == "latest" or (month == "all" and club_slug == "all"):
            if is_off_season():
                # when off season, shows the last match day
                last_game = (
                    Game.objects.filter(**game_filter_by, game_date__lte=now())
                    .values_list("game_date", flat=True)
                    .order_by("-game_full_date", "-id")
                    .first()
                )
                game_filter_by["game_date"] = last_game
            elif occasion_no is None and stage is None and stage_round is None:
                start = None
                end = None
                if competition_slug in ["all", *LEAGUE_COMPETITIONS]:
                    # when on season, shows matches from -3 <= now <= +7 days
                    start = start_of("day") - relativedelta(days=3)
                    end = start_of("day") + relativedelta(days=8)
                elif competition_slug in CUP_COMPETITIONS:
                    # Cup competition, shows matches from -60 <= now <= +60 days
                    start = start_of("day") - relativedelta(days=60)
                    end = start_of("day") + relativedelta(days=60)

                if start and end:
                    game_filter_by["game_full_date__gte"] = start
                    game_filter_by["game_full_date__lt"] = end
        elif month and month != "all" and occasion_no is None and stage is None:
            start_date = datetime.strptime(
                f"{year}-{month}-01 00:00:00", "%Y-%m-%d %H:%M:%S"
            )

            if user_tz is not None:
                start_date = start_date.astimezone(user_tz)
            else:
                start_date = start_date.astimezone(japan_tz)

            num_days = monthrange(year, int(month))[1]
            end_date = start_date + relativedelta(days=num_days)

            game_filter_by["game_full_date__gte"] = start_date
            game_filter_by["game_full_date__lt"] = end_date

    # filter by matchweek (occasion_no) or stage (stage & stage_round)
    if occasion_no is not None:
        game_filter_by["occasion_no"] = occasion_no
    elif all([competition_slug, stage, stage_round]):
        cup_system = get_competition_system(competition_slug, year)
        season_id = cup_system[stage].get("season_id")
        game_filter_by["season_id"] = season_id
        if season_id == SeasonEnum.GROUP_STAGE:
            game_filter_by["occasion_no"] = stage_round
        elif season_id == SeasonEnum.KNOCKOUT_STAGE:
            schedule_nums = get_stage_schedule_nums(
                competition_slug, season_id, stage, year
            )
            game_filter_by["schedule_no__in"] = schedule_nums
            game_filter_by["round"] = stage_round
        else:
            game_filter_by["round"] = stage_round

    # convert filter dict to filter object
    game_filter_obj = Q(**game_filter_by)

    if kwargs.get("only_upcoming") is True:
        game_filter_obj &= Q(situation_id__isnull=True) | Q(
            situation_id=GameSituationEnum.BEFORE
        )

    # filter by team
    if club_slug is not None and club_slug != "all":
        game_filter_obj &= Q(home_team__slug=club_slug) | Q(away_team__slug=club_slug)

    # group game data
    ordering_in_group_sq = ClubCompetition.objects.filter(
        year=year, competition=OuterRef("competition"), club=OuterRef("home_team")
    ).values("ordering")[:1]

    # fetch game data
    games = (
        Game.objects.filter(game_filter_obj)
        .exclude(**game_exclude_list)
        .select_related("stadium", "home_team", "away_team", "competition")
        .annotate(ordering_in_group=Subquery(ordering_in_group_sq))
        .order_by("game_full_date", "schedule_no", "ordering_in_group")
    )

    if not len(games) and month == "latest":
        # get upcoming games
        start = start_of("day")
        upcoming = Game.objects.filter(game_full_date__gte=start).first()

        game_filter_by["game_full_date__gte"] = upcoming.game_full_date
        game_filter_by["game_full_date__lt"] = upcoming.game_full_date + relativedelta(
            days=8
        )
        game_filter_obj = Q(**game_filter_by)
        games = (
            Game.objects.filter(game_filter_obj)
            .exclude(**game_exclude_list)
            .select_related("stadium", "home_team", "away_team", "competition")
            .annotate(ordering_in_group=Subquery(ordering_in_group_sq))
            .order_by("game_full_date", "schedule_no", "ordering_in_group")
        )

    # pass year to match group helper
    kwargs["year"] = year
    fixtures = _group_game_by_date(games, **kwargs)

    return fixtures


def get_fixtures_v2(filter_by=None, **kwargs):
    # TODO: find the better name
    # group game data
    ordering_in_group_sq = ClubCompetition.objects.filter(
        competition=OuterRef("competition"),
        club=OuterRef("home_team"),
    ).values("ordering")[:1]

    exclude_list = {}
    if kwargs.get("exclude_list"):
        exclude_list = kwargs["exclude_list"]

    # fetch game data
    games = (
        Game.objects.filter(**filter_by)
        .select_related("stadium", "home_team", "away_team", "competition")
        .exclude(**exclude_list)
        .annotate(ordering_in_group=Subquery(ordering_in_group_sq))
        .order_by("game_full_date", "schedule_no", "ordering_in_group")
    )

    fixtures = _group_game_by_date(games, **kwargs)

    return fixtures


def get_fixture_competitions(year: int = None, month: str = None, is_ticket=False):
    competitions = []
    search_view = "fixtures_search"
    all_view = "fixtures"
    search_type = "fixture"
    search_term = ""

    if is_ticket:
        search_view = "tickets_search"
        all_view = "tickets"
        search_type = "ticket"

    competitions.append(
        {
            "slug": "all",
            "name": "ALL",
            "translation_key": "GLOBAL_ALL",
            "url": reverse(all_view),
        }
    )

    competitions_ordered = (
        (
            GameKindEnum.J1_LEAGUE.value,
            GameKindShortNameEnum.J1_LEAGUE.value,
            "GLOBAL_COMPETITION_J1_LEAGUE",
        ),
        (
            GameKindEnum.LEVAIN_CUP.value,
            GameKindShortNameEnum.LEVAIN_CUP.value,
            "GLOBAL_COMPETITION_LEVAIN_CUP",
        ),
        # (   # 2023
        #     GameKindEnum.AFC_CHAMPIONS_LEAGUE.value,
        #     GameKindShortNameEnum.AFC_CHAMPIONS_LEAGUE.value,
        #     "GLOBAL_COMPETITION_AFC_CHAMPIONS_LEAGUE",
        # ),
        (   # 2024 ACLE
            GameKindEnum.AFC_CHAMPIONS_LEAGUE_ELITE.value,
            GameKindShortNameEnum.AFC_CHAMPIONS_LEAGUE_ELITE.value,
            "GLOBAL_COMPETITION_AFC_CHAMPIONS_LEAGUE_ELITE",
        ),
        (   # 2024 ACL2
            GameKindEnum.AFC_CHAMPIONS_LEAGUE_TWO.value,
            GameKindShortNameEnum.AFC_CHAMPIONS_LEAGUE_TWO.value,
            "GLOBAL_COMPETITION_AFC_CHAMPIONS_LEAGUE_TWO",
        ),
        (
            # 2022
            # GameKindEnum.J1_ENTRY_PLAY_OFF.value,
            # GameKindShortNameEnum.J1_ENTRY_PLAY_OFF.value,
            # 2023
            GameKindEnum.J1_PROMOTION_PLAY_OFF.value,
            GameKindShortNameEnum.J1_PROMOTION_PLAY_OFF.value,
            "GLOBAL_COMPETITION_J1_ENTRY_PLAY_OFF",
        ),
        (
            GameKindEnum.J2_LEAGUE.value,
            GameKindShortNameEnum.J2_LEAGUE.value,
            "GLOBAL_COMPETITION_J2_LEAGUE",
        ),
        (
            GameKindEnum.J3_LEAGUE.value,
            GameKindShortNameEnum.J3_LEAGUE.value,
            "GLOBAL_COMPETITION_J3_LEAGUE",
        ),
        (
            GameKindEnum.J2_PROMOTION_PLAY_OFF.value,
            GameKindShortNameEnum.J2_PROMOTION_PLAY_OFF.value,
            "GLOBAL_COMPETITION_J2_PLAY_OFFS",
        ),
        (
            GameKindEnum.J3_JFL_Replace.value,
            GameKindShortNameEnum.J3_JFL_Replace.value,
            "GLOBAL_COMPETITION_J3_JFL_REPLACE",
        ),
        (
            GameKindEnum.FUJIFILM_SUPER_CUP.value,
            GameKindShortNameEnum.FUJIFILM_SUPER_CUP.value,
            "GLOBAL_COMPETITION_FUJIFILM_SUPER_CUP",
        ),
        (
            GameKindEnum.FIFA_CLUB_WORLD_CUP.value,
            GameKindShortNameEnum.FIFA_CLUB_WORLD_CUP.value,
            "GLOBAL_COMPETITION_FIFA_CLUB_WORLD_CUP",
        ),
        (
            GameKindEnum.ASIA_CHALLENGE.value,
            GameKindShortNameEnum.ASIA_CHALLENGE.value,
            "GLOBAL_COMPETITION_ASIA_CHALLENGE",
        ),
        (
            GameKindEnum.WORLD_CHALLENGE.value,
            GameKindShortNameEnum.WORLD_CHALLENGE.value,
            "GLOBAL_COMPETITION_WORLD_CHALLENGE",
        ),
        # !NOTE: Temporary use for short period
        (
            "afs",
            "Audi Football Summit powered by docomo",
            "GLOBAL_COMPETITION_AFS_POWERED_BY_DOCOMO",
        ),
        (
            GameKindEnum.FRIENDLY_MATCH.value,
            GameKindShortNameEnum.FRIENDLY_MATCH.value,
            "GLOBAL_COMPETITION_FRIENDLY_MATCH",
        ),
    )

    for slug, name, translation_key in competitions_ordered:
        logo_year = datetime.now().year

        search_view_kwargs = {"competition_slug": slug}
        if year:
            search_view_kwargs["year"] = year
        if month:
            search_view_kwargs["month"] = month

        if is_ticket:
            search_term = slug
        else:
            search_term = f"{slug}:{year}:{month}"

        competition = {
            "slug": slug,
            "name": name,
            "translation_key": translation_key,
            "url": reverse(search_view, kwargs=search_view_kwargs),
            "ga4_search_type": search_type,
            "ga4_search_term": search_term,
            "logo_year": logo_year,
        }
        competitions.append(competition)

    return competitions


def get_ticket_fixtures(competition="all", timezone=None, **kwargs):
    start_date = start_of("day")
    end_date = start_date + relativedelta(months=3)

    filter_by = {
        "game_date__gte": start_date,
        "game_date__lt": end_date,
    }
    exclude_list = {}

    if competition and competition != "all":
        filter_by["competition__slug"] = competition

    if kwargs.get("excl_ids"):
        exclude_list["id__in"] = kwargs["excl_ids"]

    if kwargs.get("ids"):
        filter_by["id__in"] = kwargs["ids"]
    else:
        game_ids = list(
            GameTicketLink.objects.select_related(
                "game_extra_info",
                "game_extra_info__game",
            )
            .filter(
                game_extra_info__game__game_date__gte=start_date,
                game_extra_info__game__game_date__lt=end_date,
            )
            .values_list("game_extra_info__game_id", flat=True)
            .order_by("game_extra_info__game_id")
        )

        if game_ids and len(game_ids):
            filter_by["id__in"] = game_ids

    # fetch game data
    fixtures = get_fixtures_v2(filter_by, timezone=timezone, exclude_list=exclude_list)

    return fixtures


def get_live_fixtures(competitions="all", **kwargs):
    start_date = start_of("day")
    end_date = start_date + relativedelta(days=7)

    filter_by = {
        "game_date__gte": start_date,
        "game_date__lt": end_date,
    }

    if competitions and competitions != "all":
        filter_by["competition__slug__in"] = competitions

    if kwargs.get("ids"):
        filter_by["id__in"] = kwargs["ids"]
    else:
        live_game_filter = Q(
            game__game_date__gte=start_date,
            game__game_date__lt=end_date,
            live_video_enabled=True,
        )
        game_ids = list(
            GameExtraInfo.objects.select_related("game")
            .filter(live_game_filter)
            # .exclude(
            #     live_video__isnull=True,
            #     live_video_thai__isnull=True,
            #     live_video_bahasa__isnull=True,
            #     live_video_vietnamese__isnull=True,
            # )
            .values_list("game_id", flat=True)
            .order_by("game_id")
        )

        if game_ids and len(game_ids):
            filter_by["id__in"] = game_ids
        else:
            return None

    # fetch game data
    fixtures = get_fixtures_v2(filter_by, **kwargs)

    return fixtures
