<?xml version="1.0" encoding="UTF-8"?>
<svg width="30" height="35" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 58 80">
<rect width="30" height="35" transform="translate(0.921875)" fill="url(#pattern0)"/>
  <defs>
    <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
</pattern>
    <style>
      .cls-1 {
        fill: url(#radial-gradient-7);
      }

      .cls-2 {
        fill: url(#radial-gradient-6);
      }

      .cls-3 {
        fill: url(#radial-gradient-5);
      }

      .cls-4 {
        fill: url(#radial-gradient-4);
      }

      .cls-5 {
        fill: url(#radial-gradient-9);
      }

      .cls-6 {
        fill: url(#radial-gradient-8);
      }

      .cls-7 {
        fill: url(#radial-gradient-3);
      }

      .cls-8 {
        fill: url(#radial-gradient-2);
      }

      .cls-9 {
        fill: url(#linear-gradient-8);
      }

      .cls-10 {
        fill: url(#linear-gradient-7);
      }

      .cls-11 {
        fill: url(#linear-gradient-5);
      }

      .cls-12 {
        fill: url(#linear-gradient-6);
      }

      .cls-13 {
        fill: url(#linear-gradient-9);
      }

      .cls-14 {
        fill: url(#linear-gradient-4);
      }

      .cls-15 {
        fill: url(#linear-gradient-3);
      }

      .cls-16 {
        fill: url(#linear-gradient-2);
      }

      .cls-17 {
        fill: url(#radial-gradient);
      }

      .cls-18 {
        fill: url(#linear-gradient);
      }

      .cls-19 {
        fill: #00a8e1;
      }

      .cls-20 {
        fill: #070c33;
      }

      .cls-21 {
        fill: url(#radial-gradient-12);
      }

      .cls-22 {
        fill: url(#radial-gradient-14);
      }

      .cls-23 {
        fill: url(#radial-gradient-13);
      }

      .cls-24 {
        fill: url(#radial-gradient-10);
      }

      .cls-25 {
        fill: url(#radial-gradient-11);
      }

      .cls-26 {
        fill: url(#linear-gradient-34);
      }

      .cls-26, .cls-27, .cls-28, .cls-29, .cls-30, .cls-31, .cls-32, .cls-33 {
        opacity: .9;
      }

      .cls-27 {
        fill: url(#linear-gradient-35);
      }

      .cls-28 {
        fill: url(#linear-gradient-33);
      }

      .cls-29 {
        fill: url(#linear-gradient-17);
      }

      .cls-30 {
        fill: url(#linear-gradient-32);
      }

      .cls-31 {
        fill: url(#linear-gradient-22);
      }

      .cls-32 {
        fill: url(#linear-gradient-40);
      }

      .cls-33 {
        fill: url(#linear-gradient-41);
      }

      .cls-34 {
        fill: url(#linear-gradient-21);
      }

      .cls-34, .cls-35 {
        opacity: .8;
      }

      .cls-35 {
        fill: url(#linear-gradient-23);
      }

      .cls-36 {
        fill: url(#linear-gradient-36);
      }

      .cls-36, .cls-37 {
        opacity: .4;
      }

      .cls-37 {
        fill: url(#linear-gradient-37);
      }

      .cls-38 {
        fill: url(#linear-gradient-42);
      }

      .cls-39 {
        fill: url(#linear-gradient-10);
      }

      .cls-40 {
        fill: url(#linear-gradient-11);
      }

      .cls-41 {
        fill: url(#linear-gradient-12);
      }

      .cls-42 {
        fill: url(#linear-gradient-13);
      }

      .cls-43 {
        fill: url(#linear-gradient-19);
      }

      .cls-44 {
        fill: url(#linear-gradient-14);
      }

      .cls-45 {
        fill: url(#linear-gradient-20);
      }

      .cls-46 {
        fill: url(#linear-gradient-28);
      }

      .cls-47 {
        fill: url(#linear-gradient-25);
      }

      .cls-48 {
        fill: url(#linear-gradient-31);
      }

      .cls-49 {
        fill: url(#linear-gradient-30);
      }

      .cls-50 {
        fill: url(#linear-gradient-15);
      }

      .cls-51 {
        fill: url(#linear-gradient-16);
      }

      .cls-52 {
        fill: url(#linear-gradient-18);
      }

      .cls-53 {
        fill: url(#linear-gradient-38);
      }

      .cls-54 {
        fill: url(#linear-gradient-27);
      }

      .cls-55 {
        fill: url(#linear-gradient-24);
      }

      .cls-56 {
        fill: url(#linear-gradient-26);
      }

      .cls-57 {
        fill: url(#linear-gradient-29);
      }

      .cls-58 {
        fill: url(#linear-gradient-39);
      }
    </style>
    <radialGradient id="radial-gradient" cx="-10795.25" cy="6.47" fx="-10795.25" fy="6.47" r="31.53" gradientTransform="translate(-10426.77) rotate(-180) scale(.97 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".32" stop-color="#cdd5ec"/>
      <stop offset=".33" stop-color="#cad3eb"/>
      <stop offset=".43" stop-color="#7facd8"/>
      <stop offset=".5" stop-color="#5093cd"/>
      <stop offset=".53" stop-color="#3f8ac9"/>
      <stop offset=".54" stop-color="#3e84c4"/>
      <stop offset=".64" stop-color="#395aa7"/>
      <stop offset=".84" stop-color="#05054c"/>
    </radialGradient>
    <radialGradient id="radial-gradient-2" cx="-10795.77" cy="5.77" fx="-10795.77" fy="5.77" r="35.3" gradientTransform="translate(-10426.77) rotate(-180) scale(.97 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".41" stop-color="#cdd5ec"/>
      <stop offset=".41" stop-color="#cad3eb"/>
      <stop offset=".47" stop-color="#7facd8"/>
      <stop offset=".51" stop-color="#5093cd"/>
      <stop offset=".53" stop-color="#3f8ac9"/>
      <stop offset=".54" stop-color="#3e84c4"/>
      <stop offset=".64" stop-color="#395aa7"/>
      <stop offset=".84" stop-color="#05054c"/>
    </radialGradient>
    <radialGradient id="radial-gradient-3" cx="-10810.31" cy=".09" fx="-10810.31" fy=".09" r="19.46" gradientTransform="translate(-10426.77) rotate(-180) scale(.97 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".32" stop-color="#cdd5ec"/>
      <stop offset=".33" stop-color="#cad3eb"/>
      <stop offset=".44" stop-color="#7facd8"/>
      <stop offset=".51" stop-color="#5093cd"/>
      <stop offset=".55" stop-color="#3f8ac9"/>
      <stop offset=".56" stop-color="#3e86c6"/>
      <stop offset=".68" stop-color="#365ca9"/>
      <stop offset=".84" stop-color="#06044c"/>
    </radialGradient>
    <radialGradient id="radial-gradient-4" cx="-10803.55" cy="1.3" fx="-10803.55" fy="1.3" r="26.81" gradientTransform="translate(-10426.77) rotate(-180) scale(.97 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".44" stop-color="#cdd5ec"/>
      <stop offset=".44" stop-color="#cad3eb"/>
      <stop offset=".5" stop-color="#7facd8"/>
      <stop offset=".54" stop-color="#5093cd"/>
      <stop offset=".56" stop-color="#3f8ac9"/>
      <stop offset=".57" stop-color="#3e84c4"/>
      <stop offset=".64" stop-color="#395aa7"/>
      <stop offset=".84" stop-color="#05054c"/>
    </radialGradient>
    <linearGradient id="linear-gradient" x1="-10655.25" y1=".64" x2="-10658.81" y2="4.15" gradientTransform="translate(-10605.14) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".17" stop-color="#cdd5ec"/>
      <stop offset=".18" stop-color="#cad3eb"/>
      <stop offset=".35" stop-color="#7facd8"/>
      <stop offset=".48" stop-color="#5093cd"/>
      <stop offset=".54" stop-color="#3f8ac9"/>
      <stop offset=".57" stop-color="#3e84c4"/>
      <stop offset=".81" stop-color="#395aa7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="-10652.66" y1="16.15" x2="-10652.47" y2="-6.5" gradientTransform="translate(-10605.14) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".21" stop-color="#cdd5ec"/>
      <stop offset=".22" stop-color="#cad3eb"/>
      <stop offset=".34" stop-color="#7facd8"/>
      <stop offset=".42" stop-color="#5093cd"/>
      <stop offset=".46" stop-color="#3f8ac9"/>
      <stop offset=".5" stop-color="#3e84c4"/>
      <stop offset=".81" stop-color="#395aa7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="-10652.39" y1="22.6" x2="-10644.16" y2="-9.46" gradientTransform="translate(-10605.14) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".26" stop-color="#cdd5ec"/>
      <stop offset=".26" stop-color="#cad3eb"/>
      <stop offset=".38" stop-color="#7facd8"/>
      <stop offset=".46" stop-color="#5093cd"/>
      <stop offset=".51" stop-color="#3f8ac9"/>
      <stop offset=".53" stop-color="#3e84c4"/>
      <stop offset=".74" stop-color="#395aa7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="-10651.12" y1="32.12" x2="-10643.37" y2="-11.14" gradientTransform="translate(-10605.14) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".24" stop-color="#cdd5ec"/>
      <stop offset=".25" stop-color="#cad3eb"/>
      <stop offset=".37" stop-color="#7facd8"/>
      <stop offset=".46" stop-color="#5093cd"/>
      <stop offset=".51" stop-color="#3f8ac9"/>
      <stop offset=".53" stop-color="#3e84c4"/>
      <stop offset=".7" stop-color="#395aa7"/>
      <stop offset=".84" stop-color="#05054c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="46.33" y1="49.19" x2="25.82" y2="49.19" gradientUnits="userSpaceOnUse">
      <stop offset=".12" stop-color="#395aa7"/>
      <stop offset=".13" stop-color="#3961ac"/>
      <stop offset=".18" stop-color="#3c77bc"/>
      <stop offset=".23" stop-color="#3e85c5"/>
      <stop offset=".27" stop-color="#3f8ac9"/>
      <stop offset=".39" stop-color="#3f8ac9"/>
      <stop offset=".6" stop-color="#3f8ac9"/>
      <stop offset=".74" stop-color="#3f8ac9"/>
      <stop offset=".76" stop-color="#3e84c4"/>
      <stop offset=".9" stop-color="#395aa7"/>
    </linearGradient>
    <radialGradient id="radial-gradient-5" cx="229.87" cy="6.47" fx="229.87" fy="6.47" r="31.53" gradientTransform="translate(-178.37) scale(.97 1)" xlink:href="#radial-gradient"/>
    <radialGradient id="radial-gradient-6" cx="229.34" cy="5.77" fx="229.34" fy="5.77" r="35.3" gradientTransform="translate(-178.37) scale(.97 1)" xlink:href="#radial-gradient-2"/>
    <radialGradient id="radial-gradient-7" cx="214.8" cy=".09" fx="214.8" fy=".09" r="19.46" gradientTransform="translate(-178.37) scale(.97 1)" gradientUnits="userSpaceOnUse">
      <stop offset=".4" stop-color="#cdd5ec"/>
      <stop offset=".4" stop-color="#cad3eb"/>
      <stop offset=".47" stop-color="#7facd8"/>
      <stop offset=".52" stop-color="#5093cd"/>
      <stop offset=".55" stop-color="#3f8ac9"/>
      <stop offset=".56" stop-color="#3e86c6"/>
      <stop offset=".67" stop-color="#355caa"/>
      <stop offset=".84" stop-color="#06044c"/>
    </radialGradient>
    <radialGradient id="radial-gradient-8" cx="221.57" cy="1.3" fx="221.57" fy="1.3" r="26.81" gradientTransform="translate(-178.37) scale(.97 1)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-9" cx="197.21" cy="3.46" fx="197.21" fy="3.46" r="39.28" gradientTransform="translate(-178.37) scale(.97 1)" gradientUnits="userSpaceOnUse">
      <stop offset=".16" stop-color="#d0d3d3"/>
      <stop offset=".16" stop-color="#cdd0d2"/>
      <stop offset=".26" stop-color="#7d91bb"/>
      <stop offset=".32" stop-color="#4c69ac"/>
      <stop offset=".36" stop-color="#395aa7"/>
      <stop offset=".36" stop-color="#395aa7"/>
      <stop offset=".4" stop-color="#3b6fb6"/>
      <stop offset=".45" stop-color="#3d7ec0"/>
      <stop offset=".5" stop-color="#3e87c7"/>
      <stop offset=".58" stop-color="#3f8ac9"/>
      <stop offset=".76" stop-color="#172f73"/>
      <stop offset=".86" stop-color="#05054c"/>
    </radialGradient>
    <linearGradient id="linear-gradient-6" x1="32.58" y1="34.28" x2="39.51" y2="34.28" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#1c2472"/>
      <stop offset=".17" stop-color="#395aa7"/>
      <stop offset=".19" stop-color="#395ca8"/>
      <stop offset=".37" stop-color="#3a6db4"/>
      <stop offset=".5" stop-color="#3b74b9"/>
      <stop offset=".81" stop-color="#395aa7"/>
      <stop offset="1" stop-color="#1c2472"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="15.16" y1="11.46" x2="36.06" y2="34.78" gradientUnits="userSpaceOnUse">
      <stop offset=".35" stop-color="#d0d3d3"/>
      <stop offset=".44" stop-color="#83accd"/>
      <stop offset=".51" stop-color="#5293ca"/>
      <stop offset=".54" stop-color="#3f8ac9"/>
      <stop offset=".74" stop-color="#172f73"/>
      <stop offset=".85" stop-color="#05054c"/>
    </linearGradient>
    <radialGradient id="radial-gradient-10" cx="207.5" cy="14.46" fx="207.5" fy="14.46" r="25.28" gradientTransform="translate(-178.37) scale(.97 1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#d0d3d3"/>
      <stop offset=".04" stop-color="#bbc2cd"/>
      <stop offset=".19" stop-color="#758ab8"/>
      <stop offset=".3" stop-color="#4967ab"/>
      <stop offset=".35" stop-color="#395aa7"/>
      <stop offset=".55" stop-color="#3f8ac9"/>
      <stop offset=".74" stop-color="#172f73"/>
      <stop offset=".84" stop-color="#05054c"/>
    </radialGradient>
    <linearGradient id="linear-gradient-8" x1="22.03" y1=".64" x2="18.48" y2="4.15" gradientUnits="userSpaceOnUse">
      <stop offset=".17" stop-color="#cdd5ec"/>
      <stop offset=".17" stop-color="#cad3eb"/>
      <stop offset=".33" stop-color="#7facd8"/>
      <stop offset=".44" stop-color="#5093cd"/>
      <stop offset=".5" stop-color="#3f8ac9"/>
      <stop offset=".53" stop-color="#3e84c4"/>
      <stop offset=".81" stop-color="#395aa7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-9" x1="16.51" y1="1.04" x2="24.81" y2="11.48" gradientUnits="userSpaceOnUse">
      <stop offset=".01" stop-color="#3767b0"/>
      <stop offset=".55" stop-color="#395aa7"/>
      <stop offset=".9" stop-color="#05054c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-10" x1="24.62" y1="16.15" x2="24.81" y2="-6.5" gradientTransform="matrix(1,0,0,1,0,0)" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-11" x1="24.89" y1="22.6" x2="33.13" y2="-9.46" gradientTransform="matrix(1,0,0,1,0,0)" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-12" x1="26.16" y1="32.12" x2="33.91" y2="-11.14" gradientTransform="matrix(1,0,0,1,0,0)" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-13" x1="36.03" y1="-1.33" x2="27.76" y2="40.43" gradientUnits="userSpaceOnUse">
      <stop offset=".09" stop-color="#cdd5ec"/>
      <stop offset=".11" stop-color="#c1cfe9"/>
      <stop offset=".22" stop-color="#7baad7"/>
      <stop offset=".3" stop-color="#4f92cd"/>
      <stop offset=".34" stop-color="#3f8ac9"/>
      <stop offset=".73" stop-color="#172f74"/>
      <stop offset=".93" stop-color="#05054c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-14" x1="33.48" y1="6.56" x2="33.48" y2="42.81" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#3f8ac9"/>
      <stop offset=".02" stop-color="#3e84c4"/>
      <stop offset=".15" stop-color="#395aa7"/>
      <stop offset=".53" stop-color="#1d2c76"/>
      <stop offset=".9" stop-color="#05054c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-15" x1="29.71" y1="21.26" x2="35.95" y2="37.33" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#d0d3d3"/>
      <stop offset=".14" stop-color="#9ab5ca"/>
      <stop offset=".35" stop-color="#548ebe"/>
      <stop offset=".5" stop-color="#2876b7"/>
      <stop offset=".57" stop-color="#186db5"/>
      <stop offset=".93" stop-color="#05054c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-16" x1="-10624.56" y1="-70.32" x2="-10606.44" y2="-70.32" gradientTransform="translate(-10404.66 109.65) rotate(-180) scale(.98 -1.02)" gradientUnits="userSpaceOnUse">
      <stop offset=".16" stop-color="#395aa7"/>
      <stop offset=".18" stop-color="#3961ac"/>
      <stop offset=".28" stop-color="#3c77bc"/>
      <stop offset=".37" stop-color="#3e85c5"/>
      <stop offset=".45" stop-color="#3f8ac9"/>
      <stop offset=".56" stop-color="#3f8ac9"/>
      <stop offset=".59" stop-color="#3e84c4"/>
      <stop offset=".86" stop-color="#395aa7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-17" x1="-10636.64" y1="33.93" x2="-10640.06" y2="37.73" gradientTransform="translate(-10605.05) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".21" stop-color="#cdd5ec"/>
      <stop offset=".27" stop-color="#cdd5ec" stop-opacity=".86"/>
      <stop offset=".39" stop-color="#cdd5ec" stop-opacity=".6"/>
      <stop offset=".5" stop-color="#cdd5ec" stop-opacity=".38"/>
      <stop offset=".61" stop-color="#cdd5ec" stop-opacity=".22"/>
      <stop offset=".7" stop-color="#cdd5ec" stop-opacity=".1"/>
      <stop offset=".79" stop-color="#cdd5ec" stop-opacity=".03"/>
      <stop offset=".85" stop-color="#cdd5ec" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-18" x1="44.25" y1="36.48" x2="27.72" y2="36.48" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#cdd5ec"/>
      <stop offset="0" stop-color="#c5cee8"/>
      <stop offset=".04" stop-color="#93a5d1"/>
      <stop offset=".08" stop-color="#6c84be"/>
      <stop offset=".11" stop-color="#506db1"/>
      <stop offset=".14" stop-color="#3f5fa9"/>
      <stop offset=".16" stop-color="#395aa7"/>
      <stop offset=".24" stop-color="#395aa7"/>
      <stop offset=".27" stop-color="#3961ac"/>
      <stop offset=".35" stop-color="#3c77bc"/>
      <stop offset=".43" stop-color="#3e85c5"/>
      <stop offset=".5" stop-color="#3f8ac9"/>
      <stop offset=".53" stop-color="#3e84c4"/>
      <stop offset=".76" stop-color="#395aa7"/>
      <stop offset=".86" stop-color="#395aa7"/>
      <stop offset="1" stop-color="#cdd5ec"/>
    </linearGradient>
    <linearGradient id="linear-gradient-19" x1="45.62" y1="43.84" x2="26.52" y2="43.75" gradientUnits="userSpaceOnUse">
      <stop offset=".16" stop-color="#395aa7"/>
      <stop offset=".19" stop-color="#3961ac"/>
      <stop offset=".3" stop-color="#3c77bc"/>
      <stop offset=".41" stop-color="#3e85c5"/>
      <stop offset=".5" stop-color="#3f8ac9"/>
      <stop offset=".56" stop-color="#3f8ac9"/>
      <stop offset=".59" stop-color="#3e84c4"/>
      <stop offset=".86" stop-color="#395aa7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-20" x1="45.61" y1="47.72" x2="26.53" y2="47.62" gradientUnits="userSpaceOnUse">
      <stop offset=".16" stop-color="#395aa7"/>
      <stop offset=".18" stop-color="#3961ac"/>
      <stop offset=".25" stop-color="#3c77bc"/>
      <stop offset=".33" stop-color="#3e85c5"/>
      <stop offset=".39" stop-color="#3f8ac9"/>
      <stop offset=".61" stop-color="#3f8ac9"/>
      <stop offset=".64" stop-color="#3e84c4"/>
      <stop offset=".86" stop-color="#395aa7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-21" x1="42.89" y1="47.67" x2="38.09" y2="47.67" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#395aa7"/>
      <stop offset="0" stop-color="#395aa7"/>
      <stop offset=".65" stop-color="#3d7cbf"/>
      <stop offset="1" stop-color="#3f8ac9"/>
    </linearGradient>
    <linearGradient id="linear-gradient-22" x1="26.62" y1="47.67" x2="30.47" y2="47.67" gradientUnits="userSpaceOnUse">
      <stop offset=".28" stop-color="#cdd5ec" stop-opacity=".8"/>
      <stop offset=".4" stop-color="#cdd5ec" stop-opacity=".6"/>
      <stop offset=".55" stop-color="#cdd5ec" stop-opacity=".38"/>
      <stop offset=".69" stop-color="#cdd5ec" stop-opacity=".22"/>
      <stop offset=".81" stop-color="#cdd5ec" stop-opacity=".1"/>
      <stop offset=".92" stop-color="#cdd5ec" stop-opacity=".03"/>
      <stop offset="1" stop-color="#cdd5ec" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-23" x1="-10634.44" y1="47.67" x2="-10639.24" y2="47.67" gradientTransform="translate(-10605.18) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#395aa7"/>
      <stop offset="0" stop-color="#395aa7"/>
      <stop offset=".64" stop-color="#3d7cbf"/>
      <stop offset=".99" stop-color="#3f8ac9"/>
    </linearGradient>
    <radialGradient id="radial-gradient-11" cx="-10827.95" cy="3.46" fx="-10827.95" fy="3.46" r="39.28" gradientTransform="translate(-10426.81) rotate(-180) scale(.97 -1)" xlink:href="#radial-gradient-9"/>
    <linearGradient id="linear-gradient-24" x1="-10662.17" y1="11.46" x2="-10641.26" y2="34.78" gradientTransform="translate(-10605.18) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-7"/>
    <radialGradient id="radial-gradient-12" cx="-10817.67" cy="14.46" fx="-10817.67" fy="14.46" r="25.28" gradientTransform="translate(-10426.81) rotate(-180) scale(.97 -1)" xlink:href="#radial-gradient-10"/>
    <linearGradient id="linear-gradient-25" x1="-10641.29" y1="-1.33" x2="-10649.56" y2="40.43" gradientTransform="translate(-10605.18) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".09" stop-color="#cdd5ec"/>
      <stop offset=".11" stop-color="#c1cfe9"/>
      <stop offset=".22" stop-color="#7baad7"/>
      <stop offset=".3" stop-color="#4f92cd"/>
      <stop offset=".34" stop-color="#3f8ac9"/>
      <stop offset=".73" stop-color="#182e73"/>
      <stop offset=".93" stop-color="#06044c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-26" x1="-10643.85" y1="6.56" x2="-10643.85" y2="42.81" gradientTransform="translate(-10605.18) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-14"/>
    <linearGradient id="linear-gradient-27" x1="-10647.61" y1="21.26" x2="-10641.37" y2="37.33" gradientTransform="translate(-10605.18) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#d0d3d3"/>
      <stop offset=".14" stop-color="#9ab5ca"/>
      <stop offset=".35" stop-color="#548ebe"/>
      <stop offset=".5" stop-color="#2876b7"/>
      <stop offset=".57" stop-color="#186db5"/>
      <stop offset=".93" stop-color="#000041"/>
    </linearGradient>
    <linearGradient id="linear-gradient-28" x1="36.07" y1="4.15" x2="36.07" y2="37.75" gradientUnits="userSpaceOnUse">
      <stop offset=".06" stop-color="#cdd5ec"/>
      <stop offset=".34" stop-color="#3f8ac9"/>
      <stop offset=".49" stop-color="#2960ac"/>
      <stop offset=".75" stop-color="#3f8ac9"/>
      <stop offset=".75" stop-color="#3d86c6"/>
      <stop offset=".84" stop-color="#1f4084"/>
      <stop offset=".9" stop-color="#0d145b"/>
      <stop offset=".93" stop-color="#06044c"/>
    </linearGradient>
    <radialGradient id="radial-gradient-13" cx="35.9" cy="4.93" fx="35.9" fy="4.93" r="33.14" gradientTransform="translate(37.89 -31) rotate(89.3) scale(1 .49)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#cdd5ec" stop-opacity="0"/>
      <stop offset=".11" stop-color="#b5c4e2" stop-opacity=".11"/>
      <stop offset=".33" stop-color="#7898ca" stop-opacity=".39"/>
      <stop offset=".61" stop-color="#1f59a7" stop-opacity=".8"/>
      <stop offset=".66" stop-color="#285faa" stop-opacity=".76"/>
      <stop offset=".74" stop-color="#4372b5" stop-opacity=".63"/>
      <stop offset=".84" stop-color="#6e91c6" stop-opacity=".43"/>
      <stop offset=".94" stop-color="#aabcde" stop-opacity=".16"/>
      <stop offset="1" stop-color="#cdd5ec" stop-opacity="0"/>
    </radialGradient>
    <radialGradient id="radial-gradient-14" cx="35.84" cy="23.77" fx="35.84" fy="23.77" r="13.07" gradientTransform="translate(54.72 -12.07) rotate(90) scale(1 .79)" gradientUnits="userSpaceOnUse">
      <stop offset=".16" stop-color="#cdd5ec"/>
      <stop offset=".31" stop-color="#7d93c7"/>
      <stop offset=".42" stop-color="#4c69af"/>
      <stop offset=".48" stop-color="#395aa7"/>
      <stop offset=".49" stop-color="#3962ad"/>
      <stop offset=".51" stop-color="#3b71b7"/>
      <stop offset=".54" stop-color="#3b7abe"/>
      <stop offset=".59" stop-color="#3c7dc0"/>
      <stop offset=".75" stop-color="#182f75"/>
      <stop offset=".85" stop-color="#05054c"/>
    </radialGradient>
    <linearGradient id="linear-gradient-29" x1="41.06" y1="35.21" x2="31.09" y2="35.21" gradientUnits="userSpaceOnUse">
      <stop offset=".07" stop-color="#a9b1d9"/>
      <stop offset=".16" stop-color="#395aa7"/>
      <stop offset=".18" stop-color="#3961ac"/>
      <stop offset=".3" stop-color="#3c77bc"/>
      <stop offset=".4" stop-color="#3e85c5"/>
      <stop offset=".5" stop-color="#3f8ac9"/>
      <stop offset=".54" stop-color="#3e84c4"/>
      <stop offset=".86" stop-color="#395aa7"/>
      <stop offset=".93" stop-color="#a7afd8"/>
    </linearGradient>
    <linearGradient id="linear-gradient-30" x1="29.76" y1="43.79" x2="34.97" y2="43.79" gradientUnits="userSpaceOnUse">
      <stop offset=".17" stop-color="#395aa7"/>
      <stop offset=".17" stop-color="#395aa7"/>
      <stop offset=".7" stop-color="#3d7cbf"/>
      <stop offset=".99" stop-color="#3f8ac9"/>
    </linearGradient>
    <linearGradient id="linear-gradient-31" x1="-10647.56" y1="43.79" x2="-10642.86" y2="43.79" gradientTransform="translate(10685.77 87.58) scale(1 -1)" xlink:href="#linear-gradient-30"/>
    <linearGradient id="linear-gradient-32" x1="-10636.12" y1="32.33" x2="-10641.3" y2="37.3" gradientTransform="translate(-10605.24) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".29" stop-color="#cdd5ec"/>
      <stop offset=".32" stop-color="#cdd5ec" stop-opacity=".86"/>
      <stop offset=".38" stop-color="#cdd5ec" stop-opacity=".6"/>
      <stop offset=".44" stop-color="#cdd5ec" stop-opacity=".38"/>
      <stop offset=".49" stop-color="#cdd5ec" stop-opacity=".22"/>
      <stop offset=".54" stop-color="#cdd5ec" stop-opacity=".1"/>
      <stop offset=".58" stop-color="#cdd5ec" stop-opacity=".03"/>
      <stop offset=".61" stop-color="#cdd5ec" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-33" x1="-2454.35" y1="33.93" x2="-2457.76" y2="37.73" gradientTransform="translate(2494.89)" xlink:href="#linear-gradient-17"/>
    <linearGradient id="linear-gradient-34" x1="-2453.82" y1="32.33" x2="-2459" y2="37.3" gradientTransform="translate(2495.08)" xlink:href="#linear-gradient-32"/>
    <linearGradient id="linear-gradient-35" x1="26.05" y1="49.19" x2="29.84" y2="49.19" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#cdd5ec" stop-opacity=".8"/>
      <stop offset=".17" stop-color="#cdd5ec" stop-opacity=".6"/>
      <stop offset=".37" stop-color="#cdd5ec" stop-opacity=".38"/>
      <stop offset=".56" stop-color="#cdd5ec" stop-opacity=".22"/>
      <stop offset=".74" stop-color="#cdd5ec" stop-opacity=".1"/>
      <stop offset=".89" stop-color="#cdd5ec" stop-opacity=".03"/>
      <stop offset="1" stop-color="#cdd5ec" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-36" x1="28.9" y1="49.19" x2="32.41" y2="49.19" xlink:href="#linear-gradient-21"/>
    <linearGradient id="linear-gradient-37" x1="-10648.42" y1="49.19" x2="-10644.91" y2="49.19" gradientTransform="translate(10689.44 98.38) scale(1 -1)" xlink:href="#linear-gradient-21"/>
    <linearGradient id="linear-gradient-38" x1="-10660.77" y1="1.04" x2="-10652.47" y2="11.48" gradientTransform="translate(-10605.14) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#3767b0"/>
      <stop offset=".55" stop-color="#395aa7"/>
      <stop offset=".9" stop-color="#05054c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-39" x1="26.62" y1="43.79" x2="32.98" y2="43.79" gradientUnits="userSpaceOnUse">
      <stop offset=".19" stop-color="#cdd5ec" stop-opacity=".8"/>
      <stop offset=".33" stop-color="#cdd5ec" stop-opacity=".6"/>
      <stop offset=".49" stop-color="#cdd5ec" stop-opacity=".38"/>
      <stop offset=".65" stop-color="#cdd5ec" stop-opacity=".22"/>
      <stop offset=".79" stop-color="#cdd5ec" stop-opacity=".1"/>
      <stop offset=".91" stop-color="#cdd5ec" stop-opacity=".03"/>
      <stop offset="1" stop-color="#cdd5ec" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-40" x1="-8155.67" y1="47.67" x2="-8151.83" y2="47.67" gradientTransform="translate(-8110.16) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-22"/>
    <linearGradient id="linear-gradient-41" x1="-8156.25" y1="49.19" x2="-8152.46" y2="49.19" gradientTransform="translate(-8110.16) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#cdd5ec" stop-opacity=".8"/>
      <stop offset=".17" stop-color="#cdd5ec" stop-opacity=".6"/>
      <stop offset=".37" stop-color="#cdd5ec" stop-opacity=".38"/>
      <stop offset=".56" stop-color="#cdd5ec" stop-opacity=".22"/>
      <stop offset=".74" stop-color="#cdd5ec" stop-opacity=".1"/>
      <stop offset=".89" stop-color="#cdd5ec" stop-opacity=".03"/>
      <stop offset="1" stop-color="#cdd5ec" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-42" x1="-8155.68" y1="43.79" x2="-8149.32" y2="43.79" gradientTransform="translate(-8110.16) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-39"/>
  </defs>
  <g>
    <g>
      <polygon class="cls-20" points="37.96 54.3 37.96 53.42 35.02 53.42 35.02 57.48 36.07 57.48 36.07 56.2 35.95 55.86 37.45 55.86 37.45 55.04 36.07 55.04 36.07 54.3 37.96 54.3"/>
      <path class="cls-20" d="M39.24,57.28c-.35-.17-.61-.41-.8-.72-.19-.31-.28-.68-.28-1.1,0-.42.09-.79.28-1.1.19-.31.45-.56.8-.72.35-.17.75-.25,1.2-.25.3,0,.57.03.8.09.24.06.45.15.65.27v.96c-.18-.13-.37-.23-.6-.29s-.48-.09-.76-.09c-.41,0-.73.1-.95.31-.22.2-.34.49-.34.85,0,.36.11.64.34.85.23.21.54.31.95.31.29,0,.55-.03.77-.1.23-.06.44-.17.64-.31v.95c-.38.25-.88.37-1.5.37-.46,0-.86-.08-1.2-.25"/>
      <path class="cls-20" d="M31.91,55.86l.52-1.36.52,1.36h-1.04ZM34.67,57.48l-1.71-4.06h-1.41l.14.5-1.5,3.56h1.11l.2-.5-.12-.33h1.83s.34.83.34.83h1.1Z"/>
    </g>
    <g>
      <path class="cls-20" d="M22.8,64.37h-1.55v-2.28h-1.79v2.28h-1.55v-5.58h1.55v2.07h1.79v-2.07h1.55v5.58Z"/>
      <path class="cls-20" d="M13.47,64.11c-.48-.23-.85-.56-1.11-1-.26-.43-.39-.94-.39-1.52,0-.58.13-1.09.39-1.52.26-.43.63-.77,1.1-1,.48-.23,1.03-.35,1.66-.35.41,0,.78.04,1.11.12.33.08.63.2.9.37v1.32c-.24-.18-.52-.32-.82-.4s-.66-.13-1.06-.13c-.57,0-1.01.14-1.32.42-.31.28-.46.67-.46,1.17,0,.5.16.89.47,1.17.31.28.75.42,1.31.42.4,0,.75-.04,1.07-.13.31-.09.61-.23.88-.43v1.32c-.52.34-1.22.51-2.08.51-.63,0-1.18-.12-1.66-.35"/>
      <path class="cls-20" d="M25.59,62.14l.72-1.88.71,1.88h-1.44ZM29.38,64.37l-2.35-5.58h-1.95l.2.69-2.06,4.9h1.53l.28-.69-.16-.46h2.52s.47,1.14.47,1.14h1.52Z"/>
      <path class="cls-20" d="M38.97,61.27c-.16.11-.38.16-.66.16h-.81v-1.46h.81c.29,0,.51.05.67.16.15.1.23.29.23.57s-.08.45-.23.56M40.53,61.77c.19-.29.27-.65.27-1.06,0-.62-.19-1.09-.56-1.42-.37-.33-.89-.5-1.56-.5h-2.54v5.58h1.47v-1.28l-.16-.46h1.26c.32,0,.72-.06,1.03-.2.39-.19.66-.49.78-.66"/>
      <polygon class="cls-20" points="43.07 58.79 41.2 58.79 41.39 59.48 41.39 63.69 41.2 64.37 43.06 64.37 42.87 63.69 42.87 59.48 43.07 58.79"/>
      <path class="cls-20" d="M43.46,61.55c0-1.56,1.36-2.83,3.07-2.83s3.08,1.27,3.08,2.83-1.35,2.83-3.08,2.83-3.07-1.26-3.07-2.83ZM48.16,61.55c0-.85-.71-1.54-1.64-1.54s-1.62.7-1.62,1.54.67,1.53,1.62,1.53,1.64-.7,1.64-1.53Z"/>
      <path class="cls-20" d="M29.38,58.79h1.38l1.64,2.09,1.69-2.09h1.32v5.58h-1.41v-3.54l-1.61,1.98-1.59-1.97v3.53h-1.42v-5.58Z"/>
      <polygon class="cls-20" points="55.13 64.33 55.12 58.79 53.65 58.79 53.65 62.09 51.5 58.79 50.05 58.79 50.05 64.33 51.52 64.33 51.52 61.03 53.68 64.33 55.13 64.33"/>
      <path class="cls-20" d="M56.03,63.19l-.54,1.14h2.44c.75,0,1.31-.14,1.69-.42.4-.3.62-.8.52-1.29-.24-1.14-1.63-1.35-2.56-1.89-.16-.09-.23-.21-.23-.36,0-.15.06-.26.22-.34.15-.08.37-.12.63-.12h1.34l.41-1.13h-2.01c-.42,0-.79.05-1.11.18-.32.13-.57.32-.75.56-.18.24-.27.51-.27.82,0,.37.12.68.35.94.25.27.63.43.96.58.44.19.94.34,1.33.62.09.06.17.17.17.28,0,.12-.03.21-.12.27-.27.21-.64.17-.97.17h-1.51Z"/>
    </g>
    <g>
      <polygon class="cls-20" points="22.45 70.41 22.45 65.99 20.95 65.99 20.95 71.68 21 71.68 22.45 71.68 25.08 71.68 25.08 70.41 22.45 70.41"/>
      <polygon class="cls-20" points="25.59 71.68 29.71 71.68 29.71 70.47 27.06 70.47 27.06 69.4 28.99 69.4 28.99 68.26 27.06 68.26 27.06 67.19 29.71 67.19 29.71 65.99 25.59 65.99 25.59 71.68"/>
      <path class="cls-20" d="M32.11,69.41l.74-1.92.73,1.92h-1.47ZM35.98,71.68l-2.39-5.7h-1.99l.2.7-2.1,5h1.56l.28-.7-.16-.47h2.57s.48,1.17.48,1.17h1.55Z"/>
      <path class="cls-20" d="M43.89,71.77c.71,0,1.52-.2,1.96-.66.44-.45.66-1.06.66-1.82v-3.3h-1.46v3.32c0,.38-.1.67-.31.86-.2.2-.49.29-.85.29s-.65-.1-.85-.29c-.2-.2-.31-.48-.31-.86v-3.32h-1.46v3.3c0,.76.22,1.37.66,1.82.44.46,1.25.66,1.96.66"/>
      <polygon class="cls-20" points="47.08 71.68 51.19 71.68 51.19 70.47 48.55 70.47 48.55 69.4 50.48 69.4 50.48 68.26 48.55 68.26 48.55 67.2 51.19 67.2 51.19 65.99 47.08 65.99 47.08 71.68"/>
      <path class="cls-20" d="M40.89,68.99h-1.45v1.18l.34.2s-.13.05-.35.08c-.17.02-.35.03-.54.03-.57,0-1.01-.14-1.33-.43-.32-.29-.47-.69-.47-1.19,0-.51.16-.9.47-1.19.31-.29.76-.43,1.33-.43.4,0,.76.04,1.07.13.31.09.59.22.83.41v-1.35c-.28-.17-.58-.3-.91-.38-.33-.08-.7-.12-1.12-.12-.64,0-1.2.12-1.68.36-.48.24-.85.58-1.12,1.02-.26.44-.39.96-.39,1.55,0,.59.13,1.11.39,1.55.26.44.63.78,1.12,1.01.48.23,1.04.35,1.68.35.87,0,1.6-.18,2.13-.53h0s0-.01,0-.01v-2.24Z"/>
    </g>
    <g>
      <g>
        <path class="cls-19" d="M39.65,76.18c0-1.64,1.39-2.96,3.13-2.96s3.14,1.33,3.14,2.96-1.38,2.96-3.14,2.96-3.13-1.32-3.13-2.96ZM44.46,76.18c0-.89-.72-1.61-1.67-1.61s-1.66.73-1.66,1.61.69,1.6,1.66,1.6,1.67-.73,1.67-1.6Z"/>
        <path class="cls-19" d="M31.1,73.32h1.6l.96,3.53.84-2.82-.24-.7h1.84l1.05,3.53.96-3.53h1.59l-1.7,5.72h-1.72l-.87-3.15-.86,3.15h-1.77l-1.68-5.72Z"/>
        <polygon class="cls-19" points="31.1 73.32 26.21 73.32 26.21 74.62 27.92 74.62 27.92 79.04 29.43 79.04 29.43 74.62 31.1 74.62 31.1 73.32"/>
      </g>
      <path class="cls-19" d="M46.97,74.26h-.16v-.94h.14l.31.42.29-.4h.16v.92h-.18v-.63l-.25.34h-.04l-.27-.34v.63ZM46.35,73.5v.76h-.18v-.76h-.25v-.18h.71v.18h-.27Z"/>
    </g>
  </g>
  <g>
    <path class="cls-17" d="M39.95,4.24s.02.04.03.07c0,0,0,0,0,0,1.15,2,3.68,7.29,3.68,14.69,0,.75-.09,1.77-.27,2.97.52-1.53,3.67-11.42.25-19.06-.07-.15-.12-.19-.32-.12-.13.04-3.17,1.13-3.29,1.17-.12.05-.14.16-.08.27Z"/>
    <path class="cls-8" d="M43.39,2.2c.05.15.11.35.18.6.03.03.05.06.08.12,2.57,5.73,1.43,12.73.49,16.52,1.02-2.52,5.36-13.73,3.44-17.89-.08-.17-.21-.2-.31-.19-.12.01-3.56.51-3.7.53-.17.03-.23.13-.17.32Z"/>
    <path class="cls-7" d="M48.21,10.44c1.39-1.95,3.51-4.63,6.15-7.09,0-.02.07-.42-.05-.85-.22-.81-1.06-1.49-2.45-1.5-.45,0-.81.01-.96.03-.11,0-.15.04-.17.15-.01.1-.08.65-.08.65,0,0-.02.06-.05.17.37-.01.65-.02.69-.02.15,0,.25.06.25.23,0,.14,0,.35,0,.65-.08,1.75-.78,3.59-3.34,7.6Z"/>
    <path class="cls-4" d="M46.31,13.32l.11-.18c.17-.3.79-1.31,1.78-2.7,2.55-4.01,3.25-5.84,3.34-7.6.01-.3.01-.51,0-.65,0-.17-.1-.23-.25-.23s-2.92.1-3.2.11c-.16,0-.25.03-.29.1.75,2.67-.3,7.28-1.5,11.14Z"/>
    <path class="cls-18" d="M50.61,1.99c.37-.01.65-.02.69-.02h0c.02-.17.03-.34.04-.51,0-.19.01-.34.01-.46-.21,0-.37.01-.45.02-.11,0-.15.04-.17.15-.01.1-.08.65-.08.65,0,0-.02.06-.05.17Z"/>
    <path class="cls-16" d="M46.31,13.32c1.35-3.21,2.66-8.33,2.18-11.25-.2,0-.34.01-.39.02-.16,0-.25.03-.29.1.75,2.67-.3,7.28-1.5,11.14Z"/>
    <path class="cls-15" d="M43.39,2.2c.05.15.11.35.18.6.03.03.05.06.08.12,2.57,5.73,1.43,12.73.49,16.52.12-.29.28-.7.47-1.2.97-4.17,1.81-10.8-.6-16.36,0-.02-.02-.04-.03-.06-.23.03-.39.06-.42.06-.17.03-.23.13-.17.32Z"/>
    <path class="cls-14" d="M39.95,4.24s.02.04.03.07c0,0,0,0,0,0,1.15,2,3.68,7.29,3.68,14.69,0,.75-.09,1.77-.27,2.97.24-.75.6-3.22.6-3.95,0-6.9-2.21-11.98-3.43-14.25-.29.11-.5.18-.53.19-.12.05-.14.16-.08.27Z"/>
    <path class="cls-11" d="M46.33,48.57v1.47h-20.51v-1.47c0-.17.04-.23.23-.23h20.05c.19,0,.23.06.23.23Z"/>
    <path class="cls-3" d="M32.19,4.24s-.02.04-.03.07c0,0,0,0,0,0-1.15,2-3.68,7.29-3.68,14.69,0,.75.09,1.77.27,2.97-.52-1.53-3.67-11.42-.25-19.06.07-.15.12-.19.32-.12.13.04,3.17,1.13,3.29,1.17.12.05.14.16.08.27Z"/>
    <path class="cls-2" d="M28.75,2.2c-.05.15-.11.35-.18.6-.03.03-.05.06-.08.12-2.57,5.73-1.43,12.73-.49,16.52-1.02-2.52-5.36-13.73-3.44-17.89.08-.17.21-.2.31-.19.12.01,3.56.51,3.7.53.17.03.23.13.17.32Z"/>
    <path class="cls-1" d="M23.93,10.44c-1.39-1.95-3.51-4.63-6.15-7.09,0-.02-.07-.42.05-.85.22-.81,1.06-1.49,2.45-1.5.45,0,.81.01.96.03.11,0,.15.04.17.15.01.1.08.65.08.65,0,0,.02.06.05.17-.37-.01-.65-.02-.69-.02-.15,0-.25.06-.25.23,0,.14,0,.35,0,.65.08,1.75.78,3.59,3.34,7.6Z"/>
    <path class="cls-6" d="M25.83,13.32l-.11-.18c-.17-.3-.79-1.31-1.78-2.7-2.55-4.01-3.25-5.84-3.34-7.6-.01-.3-.01-.51,0-.65,0-.17.1-.23.25-.23s2.92.1,3.2.11c.16,0,.25.03.29.1-.75,2.67.3,7.28,1.5,11.14Z"/>
    <path class="cls-5" d="M30.87,30.57c-.06-.04-4.08-2.75-7.65-7.43-1.51-1.93-2.97-4.26-4.03-6.97-1.05-2.59-1.73-5.47-1.76-8.6-.03-.97.02-1.71.07-2.16,0,0,0,0,0,0,.08-.99.28-2.05.29-2.05,4.51,4.21,7.51,9.07,7.93,9.79l.11.18c.05.16.1.31.15.46,0,0,0,0,0,0-.39-.67-2.52-2.8-3.91-4.14-1.39-1.33-2.69-2.36-2.69-2.36-.32,7.48,2.43,12.54,5.14,15.72,2.43,2.85,5.4,4.39,5.4,4.39,0,.02.96,3.18.96,3.18Z"/>
    <path class="cls-12" d="M39.52,33.8v.97h-6.9v-.97h6.9Z"/>
    <path class="cls-10" d="M30.87,30.57c-.06-.04-4.08-2.75-7.65-7.43-1.51-1.93-2.97-4.26-4.03-6.97-.58-1.44-1.05-2.97-1.36-4.59.68,2.42.99,3.5,1.72,5.07,1.2,2.65,2.77,4.9,4.38,6.74,2.44,2.88,5.04,4.94,6.59,6.05.19.61.34,1.13.34,1.13Z"/>
    <path class="cls-24" d="M29.9,27.39s-2.75-.93-6.1-4.8c-2.86-3.31-5.58-8.69-5.17-16.52,0,0,1.35,1.1,2.79,2.51,1.45,1.41,4.23,4.45,4.54,5.21,0,0,0,0,0,0-.39-.67-2.52-2.8-3.91-4.14-1.39-1.33-2.69-2.36-2.69-2.36-.32,7.48,2.43,12.54,5.14,15.72,2.43,2.85,5.4,4.39,5.4,4.39Z"/>
    <path class="cls-9" d="M21.53,1.99c-.37-.01-.65-.02-.69-.02h0c-.02-.17-.03-.34-.04-.51,0-.19-.01-.34-.01-.46.21,0,.37.01.45.02.11,0,.15.04.17.15.01.1.08.65.08.65,0,0,.02.06.05.17Z"/>
    <path class="cls-13" d="M23.46,9.79c-1.38-1.87-3.32-4.24-5.67-6.44,0-.02-.07-.42.05-.85.03-.12.08-.23.13-.34.79,1.38,4.24,5.51,5.49,7.63Z"/>
    <path class="cls-39" d="M25.83,13.32c-1.35-3.21-2.66-8.33-2.18-11.25.2,0,.34.01.39.02.16,0,.25.03.29.1-.75,2.67.3,7.28,1.5,11.14Z"/>
    <path class="cls-40" d="M28.75,2.2c-.05.15-.11.35-.18.6-.03.03-.05.06-.08.12-2.57,5.73-1.43,12.73-.49,16.52-.12-.29-.28-.7-.47-1.2-.97-4.17-1.81-10.8.6-16.36,0-.02.02-.04.03-.06.23.03.39.06.42.06.17.03.23.13.17.32Z"/>
    <path class="cls-41" d="M32.19,4.24s-.02.04-.03.07c0,0,0,0,0,0-1.15,2-3.68,7.29-3.68,14.69,0,.75.09,1.77.27,2.97-.24-.75-.6-3.22-.6-3.95,0-6.9,2.21-11.98,3.43-14.25.29.11.5.18.53.19.12.05.14.16.08.27Z"/>
    <path class="cls-42" d="M35.89,5.32h0s-.04-.06-.07-.08c-.12-.08-2.71-1.56-2.89-1.66-.17-.1-.28-.06-.38.09-.77,1.17-4.08,6.89-4.08,15.34,0,2.69,1.23,9.01,3.23,13.76,0,.02.03.05.06.09.01.01.02.03.04.04,0,0,.02.02.02.03.01.01.03.03.04.04.01.01.03.02.04.03.09.07.32.26.74.11.55-.19,1.22-.93.95-2.31-.08-.42-.67-2.72-1.06-4.76-1.13-6.03-.81-11.74,3.17-20.21.04-.08.09-.14.13-.21.06-.1.09-.22.04-.3Z"/>
    <path class="cls-44" d="M32.54,26.04c-1.13-6.03-.81-11.74,3.17-20.21-4.59,7.99-5.08,13.34-3.95,19.37.38,2.04,1.17,4.83,1.28,5.25.61,2.25-1.17,2.52-1.18,2.51.01.01.03.02.04.03.09.07.32.26.74.11.55-.19,1.22-.93.95-2.31-.08-.42-.67-2.72-1.06-4.76Z"/>
    <path class="cls-50" d="M33.05,30.45c-.11-.42-.9-3.21-1.28-5.25-.4-2.13-.6-4.18-.48-6.28-.11,1.64-.18,3.65.27,6.12.38,2.05,1.12,4.89,1.23,5.3.61,2.24-1.08,2.38-1.1,2.36,0,.02.02.04.02.06,0,.02.03.05.06.09.01.01.02.03.04.04,0,0,.02.02.02.03.01.01.03.03.04.04.01.01,1.79-.26,1.18-2.51Z"/>
    <path class="cls-51" d="M43.45,37.31c.6,0,1.09,1.72,1.52,1.72s-18.23,0-17.81,0,.92-1.72,1.52-1.72,14.17,0,14.77,0Z"/>
    <path class="cls-29" d="M28.69,37.31c-.6,0-1.09,1.72-1.52,1.72s18.23,0,17.81,0-.92-1.72-1.52-1.72-14.17,0-14.77,0Z"/>
    <path class="cls-52" d="M36.07,37.31h7.38c-1.54-.27-1-1.65-2.45-1.65h-4.93s0,0,0,0h-4.93c-1.45,0-.91,1.38-2.45,1.65h7.38Z"/>
    <path class="cls-43" d="M45.6,40.78v6.22h-19.07v-6.22c0-.15.03-.2.19-.2h18.69c.16,0,.19.04.19.2Z"/>
    <rect class="cls-45" x="26.54" y="47" width="19.07" height="1.35"/>
    <rect class="cls-34" x="38.74" y="47" width="4.17" height="1.35"/>
    <rect class="cls-31" x="26.54" y="47" width="2.69" height="1.35"/>
    <rect class="cls-35" x="29.23" y="47" width="4.17" height="1.35"/>
    <path class="cls-25" d="M41.27,30.57c.06-.04,4.08-2.75,7.65-7.43,1.51-1.93,2.97-4.26,4.03-6.97,1.05-2.59,1.73-5.47,1.76-8.6.03-.97-.02-1.71-.07-2.16,0,0,0,0,0,0-.08-.99-.28-2.05-.29-2.05-4.51,4.21-7.51,9.07-7.93,9.79l-.11.18c-.05.16-.1.31-.15.46,0,0,0,0,0,0,.39-.67,2.52-2.8,3.91-4.14,1.39-1.33,2.69-2.36,2.69-2.36.32,7.48-2.43,12.54-5.14,15.72-2.43,2.85-5.4,4.39-5.4,4.39,0,.02-.96,3.18-.96,3.18Z"/>
    <path class="cls-55" d="M41.27,30.57c.06-.04,4.08-2.75,7.65-7.43,1.51-1.93,2.97-4.26,4.03-6.97.58-1.44,1.05-2.97,1.36-4.59-.68,2.42-.99,3.5-1.72,5.07-1.2,2.65-2.77,4.9-4.38,6.74-2.44,2.88-5.04,4.94-6.59,6.05-.19.61-.34,1.13-.34,1.13Z"/>
    <path class="cls-21" d="M42.24,27.39s2.75-.93,6.1-4.8c2.86-3.31,5.58-8.69,5.17-16.52,0,0-1.35,1.1-2.79,2.51-1.45,1.41-4.23,4.45-4.54,5.21,0,0,0,0,0,0,.39-.67,2.52-2.8,3.91-4.14,1.39-1.33,2.69-2.36,2.69-2.36.32,7.48-2.43,12.54-5.14,15.72-2.43,2.85-5.4,4.39-5.4,4.39Z"/>
    <path class="cls-47" d="M36.3,5.62c.04.07.1.14.13.21,3.98,8.47,4.3,14.18,3.17,20.21-.38,2.04-.97,4.34-1.06,4.76-.27,1.38.39,2.11.95,2.31.42.15.65-.04.74-.11.01-.01.03-.02.04-.03.01-.01.03-.03.04-.04,0,0,.02-.02.02-.03.01-.02.03-.03.04-.04.03-.04.05-.07.06-.09,2.01-4.75,3.23-11.06,3.23-13.76,0-8.45-3.31-14.17-4.08-15.34-.1-.15-.21-.19-.38-.09-.17.1-2.76,1.59-2.89,1.66-.03.02-.06.05-.07.08h0c-.04.08-.02.2.04.3Z"/>
    <path class="cls-56" d="M38.55,30.8c-.27,1.38.39,2.11.95,2.31.42.15.65-.04.74-.11.01-.01.03-.02.04-.03-.01.01-1.79-.26-1.18-2.51.11-.42.9-3.21,1.28-5.25,1.13-6.03.65-11.38-3.95-19.37,3.98,8.47,4.3,14.18,3.17,20.21-.38,2.04-.97,4.34-1.06,4.76Z"/>
    <path class="cls-54" d="M40.28,32.97s.03-.03.04-.04c0,0,.02-.02.02-.03.01-.02.03-.03.04-.04.03-.04.05-.07.06-.09,0-.02.02-.04.02-.06-.02.02-1.7-.13-1.1-2.36.11-.42.85-3.26,1.23-5.3.46-2.47.39-4.48.27-6.12.11,2.11-.08,4.15-.48,6.28-.38,2.04-1.17,4.83-1.28,5.25-.61,2.25,1.17,2.52,1.18,2.51Z"/>
    <path class="cls-46" d="M39.88,33.16c.09.28-.17.64-.35.64h-.29c-.34,0-.89-.43-1.07-.79-.24-.47-.78-.94-2.1-.94v-.02.02c-1.32,0-1.86.46-2.1.94-.18.36-.72.79-1.07.79h-.29c-.18,0-.45-.36-.35-.64.1.01.23,0,.38-.05.55-.19,1.22-.93.95-2.31-.08-.42-.67-2.72-1.06-4.76-1.13-6.03-.81-11.74,3.17-20.21.04-.08.09-.14.13-.21.06-.1.09-.22.04-.3h0s0,0,0,0c.05.02.12.04.18.04h0c.06,0,.13-.02.18-.04,0,0,0,0,0,0h0c-.04.08-.02.2.04.3.04.07.1.14.13.21,3.98,8.47,4.3,14.18,3.17,20.21-.38,2.04-.97,4.34-1.06,4.76-.27,1.38.39,2.11.95,2.31.15.05.28.06.38.05Z"/>
    <path class="cls-23" d="M39.88,33.16c.09.28-.17.64-.35.64h-.29c-.34,0-.89-.43-1.07-.79-.24-.47-.78-.94-2.1-.94v-.02.02c-1.32,0-1.86.46-2.1.94-.18.36-.72.79-1.07.79h-.29c-.18,0-.45-.36-.35-.64.1.01.23,0,.38-.05.55-.19,1.22-.93.95-2.31-.08-.42-.67-2.72-1.06-4.76-1.13-6.03-.81-11.74,3.17-20.21.04-.08.09-.14.13-.21.06-.1.09-.22.04-.3h0s0,0,0,0c.05.02.12.04.18.04h0c.06,0,.13-.02.18-.04,0,0,0,0,0,0h0c-.04.08-.02.2.04.3.04.07.1.14.13.21,3.98,8.47,4.3,14.18,3.17,20.21-.38,2.04-.97,4.34-1.06,4.76-.27,1.38.39,2.11.95,2.31.15.05.28.06.38.05Z"/>
    <path class="cls-22" d="M39.23,33.8h-6.32c.34,0,.89-.43,1.07-.79.24-.47.78-.94,2.09-.94s1.86.46,2.1.94c.18.36.72.79,1.07.79Z"/>
    <path class="cls-57" d="M40.88,35.66h-9.61c.36,0,.41-.88,1.03-.88h7.56c.62,0,.66.88,1.03.88Z"/>
    <rect class="cls-49" x="29.76" y="40.58" width="4.17" height="6.41"/>
    <rect class="cls-48" x="38.21" y="40.58" width="4.17" height="6.41" transform="translate(80.58 87.58) rotate(-180)"/>
    <path class="cls-30" d="M36.07,37.31h-7.38c1.54-.27,1-1.65,2.45-1.65h9.87c1.45,0,.91,1.38,2.45,1.65h-7.38s0,0,0,0Z"/>
    <path class="cls-28" d="M43.45,37.31c.6,0,1.09,1.72,1.52,1.72s-18.23,0-17.81,0,.92-1.72,1.52-1.72,14.17,0,14.77,0Z"/>
    <path class="cls-26" d="M36.08,37.31h7.38c-1.54-.27-1-1.65-2.45-1.65h-4.93s0,0,0,0h-4.93c-1.45,0-.91,1.38-2.45,1.65h7.38Z"/>
    <path class="cls-27" d="M28.68,50.04h-2.87v-1.47c0-.17.04-.23.23-.23h2.64v1.69Z"/>
    <rect class="cls-36" x="28.68" y="48.34" width="2.66" height="1.69"/>
    <rect class="cls-37" x="40.8" y="48.34" width="2.66" height="1.69" transform="translate(84.25 98.38) rotate(-180)"/>
    <path class="cls-53" d="M48.68,9.79c1.38-1.87,3.32-4.24,5.67-6.44,0-.02.07-.42-.05-.85-.03-.12-.08-.23-.13-.34-.79,1.38-4.24,5.51-5.49,7.63Z"/>
    <path class="cls-58" d="M31.68,47h-5.14v-6.22c0-.15.03-.2.19-.2h4.95v6.41Z"/>
    <rect class="cls-32" x="42.91" y="47" width="2.69" height="1.35"/>
    <path class="cls-33" d="M43.46,50.04h2.87s0-1.3,0-1.47c0-.17-.04-.23-.23-.23h-2.64s0,1.69,0,1.69Z"/>
    <path class="cls-38" d="M40.47,47h5.14s0-6.06,0-6.22c0-.15-.03-.2-.19-.2h-4.95s0,6.41,0,6.41Z"/>
  </g>
</svg>
