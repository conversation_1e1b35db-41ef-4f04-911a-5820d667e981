.template-about-page {
  .content-container {
    // margin-bottom: 80px;

    .content-container__header {
      // margin-bottom: 80px;
    }
  }

  .about-navigation {
    margin-top: 80px;
  }

  .block-sectionheader,
  .block-richtext,
  .block-table {
    color: var(--color-black);
    font-family: var(--font-overpass);
    font-style: normal;
    font-weight: 400;
    font-size: 20px;
    line-height: 1.6;
    margin-bottom: 36px;
  }

  .block-image {
    overflow: hidden;

    img {
      height: auto;
      width: 100%;
    }
  }

  .block-richtext {

    h2,
    h3,
    h4 {
      font-weight: 700;
      line-height: 2;
    }

    h2 {
      font-size: 32px;
    }

    h3 {
      font-size: 26px;
    }

    h4 {
      font-size: 22px;
    }

    b {
      font-weight: 600;
    }

    i {
      font-style: italic;
    }

    a,
    p {
      &:empty {
        margin-bottom: 32px;
      }
    }

    a {
      color: var(--color-blue);

      &:hover {
        opacity: 1;
        text-decoration: underline;
      }
    }

    blockquote {
      background: var(--color-earth-1);
      border-left: 8px solid var(--color-earth-3);
      padding: 10px;
      quotes: '\201C' '\201D' '\2018' '\2019';

      &::before {
        color: var(--color-earth-4);
        content: open-quote;
        font-size: 3em;
        line-height: 1;
        margin-right: 10px;
        vertical-align: -24px;
      }
    }

    ol,
    ul {
      padding-left: 40px;

      li {
        &::marker {
          color: var(--color-red);
        }
      }
    }

    ol {
      list-style: decimal;
    }

    ul {
      list-style: square;
    }

    img.richtext-image {
      height: auto;

      &.full-width {
        width: 100%;
      }

      &.right {
        text-align: right;
      }

      &.left {
        text-align: left;
      }
    }
  }

  .block-table {
    table {
      width: 100%;
      border: solid 1px var(--color-black);

      th,
      td {
        border: solid 1px var(--color-black);
        text-align: left;
        vertical-align: top;
        padding: 10px 20px;
        height: auto;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      th {
        background-color: var(--color-earth-2);
        font-weight: 700;
        max-width: 25%;
      }
    }

    .htCenter {
      text-align: center;
    }

    .htJustify {
      text-align: justify;
    }

    .htRight {
      text-align: right;
    }

    .htLeft {
      text-align: left;
    }

    .htTop {
      vertical-align: top;
    }

    .htMiddle {
      vertical-align: middle;
    }

    .htBottom {
      vertical-align: bottom;
    }
  }
}
