{% load static core_tags %}

<div class="fixture-item">
  <div class="fixture-item__body">
    {% if game.is_friday_night_match or game.is_kokuritsu_match %}
      <div class="match-logos">
        {% if game.is_friday_night_match %}
          <div class="match-logo match-logo--friday-night">
            <img src="{% static 'images/logo/jl-friday-night-a.png' %}" alt="J.League Friday Night Logo A">
          </div>
        {% endif %}

        {% if game.is_kokuritsu_match %}
        <a
        href="{% url 'landingpages:kokuritsu' %}"
        rel="noopener"
        onclick="sendSelectContentEvent('kokuritsu', 'landingpage');" >
          <div class="match-logo match-logo--kokuritsu">
            <img src="{% static 'images/logo/kokuritsu-a-wide.png' %}" alt="J.League Kokuritsu Logo A wide">
          </div>
        </a>
        {% endif %}
      </div>
    {% endif %}

    <div class="match-info">
      <div class="match-situation match-situation--{{ game.situation_cls }}">
        {% if game.has_live %}
          <img class="ytlive-icon" src="{% static 'images/logo/youtube.svg' %}" loading="lazy" alt="" />
        {% endif %}

        {% if game.situation_cls != 'postponed' %}
          {% include 'components/match-situation.html' with situation_cls=game.situation_cls current_time=game.current_time half_time='HT' full_time='FT' postponed='PP' after_extra_time='AET' %}
        {% endif %}
      </div>

      <div class="match-details">
        <div class="club-info club-info--home">
          {% include 'fixtures/components/fixture_item_club.html' with club=game.home %}
        </div>

        <div class="score-info score-info--{{ game.situation_cls }}{% if game.is_upcoming %} score-info--upcoming{% endif %}">
          <a class="score-info__body"
            href="{% url 'match_overview' competition_stage.competition.slug game.id %}"
            onclick="sendSelectContentEvent('match', '{{ competition_stage.competition.slug }}_{{ game.id }}')"
          >
            {% if game.situation_cls == 'postponed' %}
              {% translation 'MATCH_STATE_POSTPONED' %}
            {% elif game.is_upcoming %}
              {% if game.date|time != '00:00' %}
                {% local_time game.date %}
              {% else %}
                TBA
              {% endif %}
            {% else %}
              {{ game.home.score }}&nbsp;&nbsp;-&nbsp;&nbsp;{{ game.away.score }}
            {% endif %}
          </a>
        </div>

        <div class="club-info club-info--away">
          {% include 'fixtures/components/fixture_item_club.html' with club=game.away %}
        </div>
      </div>

      <a class="match-link"
        href="{% url 'match_overview' competition_stage.competition.slug game.id %}"
        onclick="sendSelectContentEvent('match', '{{ competition_stage.competition.slug }}_{{ game.id }}')"
      >
        <i class="mi-chevron-right match-link__icon"></i>
      </a>
    </div>
  </div>

  <div class="fixture-item__footer">
    {% if not game.is_finished and game.ticket_links and game.ticket_links|length %}
      {% comment %} {% for ticket in game.ticket_links %}
        <a
          class="jl-button"
          href="{{ ticket.link }}"
          target="_blank"
          rel="noopener"
          onclick="sendSelectContentEvent('ticket_link', '{{ competition_stage.competition.slug }}_{{ game.id }}')"
        >
          <span class="jl-button__label">
            {% if ticket.label %}
              {{ ticket.label }}
            {% else %}
              {% translation 'GLOBAL_BUY_TICKETS' %}
            {% endif %}
          </span>
        </a>
      {% endfor %} {% endcomment %}

      {% if game.ticket_links|length == 1 %}
        {% with game.ticket_links|first as ticket %}
          <a
            class="jl-button"
            href="{{ ticket.link }}"
            target="_blank"
            rel="noopener"
          >
            <span class="jl-button__label">
              {% if ticket.label %}
                {{ ticket.label }}
              {% else %}
                {% translation 'GLOBAL_BUY_TICKETS' %}
              {% endif %}
            </span>
          </a>
        {% endwith %}
      {% else%}
        <button
          class="jl-button tickets-modal-trigger"
          type="button"
          data-links="{{ game.formatted_ticket_links }}"
        >
          <span class="jl-button__label">
            {% translation 'GLOBAL_BUY_TICKETS' %}
          </span>
        </button>
      {% endif %}
    {% endif %}
  </div>
</div>
