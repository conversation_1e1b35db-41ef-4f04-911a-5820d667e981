from rest_framework import viewsets
from django_filters import rest_framework as filters
from core.api.serializers import CompetitionSerializer, FormationSerializer
from core.models import Competition, Formation


class CompetitionFilter(filters.FilterSet):
    class Meta:
        model = Competition
        fields = {
            "id": ("exact", "in"),
            "name": ("exact",),
            "slug": ("exact", "in"),
        }


class FormationFilter(filters.FilterSet):
    class Meta:
        model = Formation
        fields = {
            "id": ("exact", "in"),
            "name": ("exact",),
        }


class CompetitionViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows competitions to be viewed.
    """

    queryset = Competition.objects.all()
    serializer_class = CompetitionSerializer
    filterset_class = CompetitionFilter
    # filterset_fields = "__all__"


class FormationViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows formations to be viewed.
    """

    queryset = Formation.objects.all()
    serializer_class = FormationSerializer
    filterset_class = FormationFilter
    # filterset_fields = "__all__"
