"""
Django settings for jleague project.

Generated by 'django-admin startproject'

For more information on this file, see
https://docs.djangoproject.com/en/dev/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/dev/ref/settings/
"""

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
import datetime
import environ
from pathlib import Path

BASE_DIR = Path(__file__).resolve(strict=True).parent.parent.parent
APPS_DIR = BASE_DIR / "jleague"

env = environ.Env()

READ_DOT_ENV_FILE = env.bool("DJANGO_READ_DOT_ENV_FILE", default=False)
if READ_DOT_ENV_FILE:
    # OS environment variables take precedence over variables from .env
    env.read_env(str(BASE_DIR / ".env"))

# read dotenv for local development
CURR_ENV = env("DJANGO_SETTINGS_MODULE")
if CURR_ENV.endswith(".local"):
    env.read_env(str(BASE_DIR.parent / ".env"))

# ------------------------------------------------------------------------------
# GENERAL
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#debug
DEBUG = env.bool("DJANGO_DEBUG", default=False)
# Local time zone. Choices are
# http://en.wikipedia.org/wiki/List_of_tz_zones_by_name
# though not all of them may be available with every OS.
# In Windows, this must be set to your system time zone.
TIME_ZONE = "Asia/Tokyo"
# https://docs.djangoproject.com/en/dev/ref/settings/#language-code
LANGUAGE_CODE = "en-us"
# https://docs.djangoproject.com/en/dev/ref/settings/#languages
# https://en.wikipedia.org/wiki/List_of_ISO_639_language_codes
LANGUAGES = [
    ("en", "English"),
    ("id", "Bahasa"),
    ("ja", "Japanese"),
    ("th", "Thai"),
    ("vi", "Vietnamese"),
]
# https://docs.djangoproject.com/en/dev/ref/settings/#site-id
SITE_ID = 1
# https://docs.djangoproject.com/en/dev/ref/settings/#use-i18n
USE_I18N = True
# https://docs.djangoproject.com/en/dev/ref/settings/#use-tz
USE_TZ = True
# https://docs.djangoproject.com/en/dev/ref/settings/#format-module-path
FORMAT_MODULE_PATH = ["jleague.formats"]
# https://docs.djangoproject.com/en/dev/ref/settings/#language-cookie-name
LANGUAGE_COOKIE_NAME = "jl_language"
# https://docs.djangoproject.com/en/dev/ref/settings/#file-upload-max-memory-size
FILE_UPLOAD_MAX_MEMORY_SIZE = 10485760  # 10MB

# ------------------------------------------------------------------------------
# WAGTAIL
# ------------------------------------------------------------------------------
WAGTAIL_SITE_NAME = "jleague"
# https://docs.wagtail.org/en/stable/advanced_topics/i18n.html#
WAGTAIL_I18N_ENABLED = True
WAGTAIL_CONTENT_LANGUAGES = LANGUAGES
WAGTAILADMIN_PERMITTED_LANGUAGES = LANGUAGES
WAGTAILADMIN_RICH_TEXT_EDITORS = {
    "default": {
        "WIDGET": "wagtail.admin.rich_text.DraftailRichTextArea",
        "OPTIONS": {
            "features": [
                "h2",
                "h3",
                "bold",
                "italic",
                "ol",
                "ul",
                "hr",
                "link",
                "image",
                "embed",
                "blockquote",
            ]
        },
    },
    "secondary": {
        "WIDGET": "some.external.RichTextEditor",
    },
}
WAGTAILEMBEDS_RESPONSIVE_HTML = True

# ------------------------------------------------------------------------------
# DATABASES
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/stable/ref/settings/#std:setting-DEFAULT_AUTO_FIELD
DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

# ------------------------------------------------------------------------------
# CACHES
# ------------------------------------------------------------------------------
CACHES = {
    "default": {
        "BACKEND": "django_redis.cache.RedisCache",
        "LOCATION": env("REDIS_URL"),
        "OPTIONS": {
            "CLIENT_CLASS": "django_redis.client.DefaultClient",
            # Mimicing memcache behavior.
            # https://github.com/jazzband/django-redis#memcached-exceptions-behavior
            "IGNORE_EXCEPTIONS": True,
        },
    }
}

# ------------------------------------------------------------------------------
# URLS
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#root-urlconf
ROOT_URLCONF = "jleague.urls"
# https://docs.djangoproject.com/en/dev/ref/settings/#wsgi-application
WSGI_APPLICATION = "jleague.wsgi.application"

# ------------------------------------------------------------------------------
# APPS
# ------------------------------------------------------------------------------
DJANGO_WAGTAIL_APPS = [
    "wagtail_localize",
    "wagtail_localize.locales",
    "wagtail.contrib.forms",
    "wagtail.contrib.modeladmin",
    "wagtail.contrib.redirects",
    "wagtail.contrib.table_block",
    "wagtail.embeds",
    "wagtail.sites",
    "wagtail.users",
    "wagtail.snippets",
    "wagtail.documents",
    "wagtail.images",
    "wagtail.search",
    "wagtail.admin",
    "wagtail",
    "modelcluster",
    "taggit",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.sitemaps",
    "django.contrib.sites",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django.contrib.humanize",
    "django.contrib.admin",
]
THIRD_PARTY_APPS = [
    "django_jsonform",
    "pipeline",
    "storages",
    "wagtailautocomplete",
    "rest_framework",
    "rest_framework.authtoken",
    "corsheaders",
    "django_filters",
]
LOCAL_APPS = [
    "core",
    "utils",
    "data_stadium",
    "media",
    "home",
    "about",
    "clubs",
    "fixtures",
    "mascots",
    "match",
    "news",
    "players",
    "standings",
    "stats",
    "stadiums",
    "search",
    "widgets",
    "landingpages",
    "newsletter",
]
# https://docs.djangoproject.com/en/dev/ref/settings/#installed-apps
INSTALLED_APPS = DJANGO_WAGTAIL_APPS + THIRD_PARTY_APPS + LOCAL_APPS

# ------------------------------------------------------------------------------
# PASSWORDS
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#password-hashers
PASSWORD_HASHERS = [
    # https://docs.djangoproject.com/en/dev/topics/auth/passwords/#using-argon2-with-django
    "django.contrib.auth.hashers.Argon2PasswordHasher",
    "django.contrib.auth.hashers.PBKDF2PasswordHasher",
    "django.contrib.auth.hashers.PBKDF2SHA1PasswordHasher",
    "django.contrib.auth.hashers.BCryptSHA256PasswordHasher",
]
# https://docs.djangoproject.com/en/dev/ref/settings/#auth-password-validators
AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator"
    },
    {"NAME": "django.contrib.auth.password_validation.MinimumLengthValidator"},
    {"NAME": "django.contrib.auth.password_validation.CommonPasswordValidator"},
    {"NAME": "django.contrib.auth.password_validation.NumericPasswordValidator"},
]

# ------------------------------------------------------------------------------
# MIDDLEWARE
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#middleware
MIDDLEWARE = [
    "django.middleware.security.SecurityMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.middleware.cache.UpdateCacheMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.middleware.locale.LocaleMiddleware",
    "core.middleware.UserTimeZoneMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "django.middleware.gzip.GZipMiddleware",
    "django.middleware.cache.FetchFromCacheMiddleware",
    "pipeline.middleware.MinifyHTMLMiddleware",
    "wagtail.contrib.redirects.middleware.RedirectMiddleware",
]

# ------------------------------------------------------------------------------
# STATIC
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#static-root
STATIC_ROOT = str(BASE_DIR / "staticfiles")
# https://docs.djangoproject.com/en/dev/ref/settings/#static-url
STATIC_URL = "/static/"
# https://docs.djangoproject.com/en/dev/ref/contrib/staticfiles/#std:setting-STATICFILES_DIRS
STATICFILES_DIRS = [str(APPS_DIR / "static")]
# https://docs.djangoproject.com/en/dev/ref/contrib/staticfiles/#staticfiles-finders
STATICFILES_FINDERS = [
    "django.contrib.staticfiles.finders.FileSystemFinder",
    "django.contrib.staticfiles.finders.AppDirectoriesFinder",
    "pipeline.finders.PipelineFinder",
]

# TODO: Use this setting when Django > 4.2
# STORAGES = {
#     "default": {
#         "BACKEND": env(
#             "STORAGE_DEFAULT_FILE_STORAGE",
#             default="django.core.files.storage.FileSystemStorage",
#         ),
#     },
#     "staticfiles": {
#         "BACKEND": "jleague.storage.WhiteNoisePipelineManifestStorage",
#     },
# }

# TODO: Use this setting when Django < 4.2
DEFAULT_FILE_STORAGE = env(
    "STORAGE_DEFAULT_FILE_STORAGE",
    default="django.core.files.storage.FileSystemStorage",
)
STATICFILES_STORAGE = "jleague.storage.WhiteNoisePipelineManifestStorage"


# ------------------------------------------------------------------------------
# MEDIA
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#media-root
# MEDIA_ROOT = str(APPS_DIR / "media")
# https://docs.djangoproject.com/en/dev/ref/settings/#media-url
# MEDIA_URL = "/media/"

# ------------------------------------------------------------------------------
# TEMPLATES
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#templates
TEMPLATES = [
    {
        # https://docs.djangoproject.com/en/dev/ref/settings/#std:setting-TEMPLATES-BACKEND
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        # https://docs.djangoproject.com/en/dev/ref/settings/#dirs
        "DIRS": [str(APPS_DIR / "templates")],
        # https://docs.djangoproject.com/en/dev/ref/settings/#app-dirs
        "APP_DIRS": True,
        "OPTIONS": {
            # https://docs.djangoproject.com/en/dev/ref/settings/#template-context-processors
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.template.context_processors.i18n",
                "django.template.context_processors.media",
                "django.template.context_processors.static",
                "django.template.context_processors.tz",
                "django.contrib.messages.context_processors.messages",
                "jleague.context_processors.settings",
                "jleague.context_processors.seo_attrs",
            ],
        },
    }
]

# ------------------------------------------------------------------------------
# SECURITY
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#session-cookie-httponly
SESSION_COOKIE_HTTPONLY = True
# https://docs.djangoproject.com/en/dev/ref/settings/#csrf-cookie-httponly
CSRF_COOKIE_HTTPONLY = True
# https://docs.djangoproject.com/en/dev/ref/settings/#x-frame-options
X_FRAME_OPTIONS = "DENY"

# ------------------------------------------------------------------------------
# EMAIL
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#email-backend
EMAIL_BACKEND = env(
    "DJANGO_EMAIL_BACKEND", default="django.core.mail.backends.console.EmailBackend"
)
# https://docs.djangoproject.com/en/dev/ref/settings/#email-timeout
EMAIL_TIMEOUT = env.int("DJANGO_EMAIL_TIMEOUT", default=5)
# https://docs.djangoproject.com/en/dev/ref/settings/#email-host
EMAIL_HOST = env("DJANGO_EMAIL_HOST", default=None)
# https://docs.djangoproject.com/en/dev/ref/settings/#email-port
EMAIL_PORT = env("DJANGO_EMAIL_PORT", default=None)
# https://docs.djangoproject.com/en/dev/ref/settings/#email-host-user
EMAIL_HOST_USER = env("DJANGO_EMAIL_HOST_USER", default=None)
# https://docs.djangoproject.com/en/dev/ref/settings/#email-host-password
EMAIL_HOST_PASSWORD = env("DJANGO_EMAIL_HOST_PASSWORD", default=None)
# https://docs.djangoproject.com/en/dev/ref/settings/#email-use-tls
EMAIL_USE_TLS = env.bool("DJANGO_EMAIL_USE_TLS", default=False)
# https://docs.djangoproject.com/en/dev/ref/settings/#default-from-email
DEFAULT_FROM_EMAIL = env("DJANGO_DEFAULT_FROM_EMAIL", default=None)
# https://docs.djangoproject.com/en/dev/ref/settings/#server-email
SERVER_EMAIL = env("DJANGO_SERVER_EMAIL", default=None)
# https://docs.djangoproject.com/en/dev/ref/settings/#email-subject-prefix
EMAIL_SUBJECT_PREFIX = env("DJANGO_EMAIL_SUBJECT_PREFIX", default=None)

# ------------------------------------------------------------------------------
# LOGGING
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#logging
# See https://docs.djangoproject.com/en/dev/topics/logging for
# more details on how to customize your logging configuration.
LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "simple": {
            "format": "[%(asctime)s] %(levelname)s [%(name)s:%(lineno)s] %(message)s",
        },
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "formatter": "simple",
        },
        "jleague_file": {
            "class": "logging.handlers.RotatingFileHandler",
            "filename": "logs/jleague.log",
            "maxBytes": 1024 * 1024 * 5,  # 5 MB
            "backupCount": 10,
            "formatter": "simple",
        },
        "django_file": {
            "class": "logging.handlers.RotatingFileHandler",
            "filename": "logs/django.log",
            "maxBytes": 1024 * 1024 * 5,  # 5 MB
            "backupCount": 10,
            "formatter": "simple",
        },
    },
    "root": {
        "handlers": ["console", "jleague_file"],
        "level": "INFO",
    },
    "loggers": {
        "django": {
            "handlers": ["django_file"],
            "level": "WARNING",
            "propagate": False,
        },
    },
}

# ------------------------------------------------------------------------------
# django-pipeline
# ------------------------------------------------------------------------------
# https://django-pipeline.readthedocs.io/en/latest/configuration.html#configuration
PIPELINE = {
    # 'PIPELINE_ENABLED': True,
    # 'PIPELINE_COLLECTOR_ENABLED': False,
    # 'CSS_COMPRESSOR': 'pipeline.compressors.yuglify.YuglifyCompressor',
    # 'JS_COMPRESSOR': 'pipeline.compressors.yuglify.YuglifyCompressor',
    # 'YUGLIFY_BINARY': 'yuglify',
    # 'YUGLIFY_CSS_ARGUMENTS': '--terminal',
    # 'YUGLIFY_JS_ARGUMENTS': '--terminal',
    "COMPILERS": ("pipeline.compilers.sass.SASSCompiler",),
    "SASS_BINARY": "pysassc",
    "SASS_ARGUMENTS": f'--include-path {str(APPS_DIR / "static/style")}',
    "DISABLE_WRAPPER": True,
    "JAVASCRIPT": {
        "vendor": {
            "source_filenames": (
                "vendor/jquery/jquery.min.js",
                "vendor/popper/popper.min.js",
                "vendor/swiperjs/swiper-bundle.min.js",
                "vendor/videojs/videojs.min.js",
            ),
            "output_filename": "js/vendor.js",
            "extra_context": {
                "defer": True,
            },
        },
        "map": {
            "source_filenames": (
                "vendor/jquery/jquery.vmap.min.js",
                "vendor/jquery/jquery.vmap.world.js",
            ),
            "output_filename": "js/map.js",
            "extra_context": {
                "defer": True,
            },
        },
        "magnific": {
            "source_filenames": (
                "vendor/jquery/jquery.magnific-popup.min.js",
                "js/magnific-popup.js",
            ),
            "output_filename": "js/magnific-popup.js",
            "extra_context": {
                "defer": True,
            },
        },
        "animatejs": {
            "source_filenames": ("vendor/animatejs/anime.min.js",),
            "output_filename": "js/anime.js",
            "extra_context": {
                "defer": True,
            },
        },
        "gaspjs": {
            "source_filenames": (
                "vendor/gsap/gsap.min.js",
                "vendor/gsap/ScrollTrigger.min.js",
            ),
            "output_filename": "js/gsap.js",
            "extra_context": {
                "defer": True,
            },
        },
        "lottie": {
            "source_filenames": ("vendor/bodymovin/lottie.min.js",),
            "output_filename": "js/lottie.js",
            "extra_context": {
                "defer": True,
            },
        },
        "atropos": {
            "source_filenames": ("vendor/atropos/atropos.min.js",),
            "output_filename": "js/atropos.js",
            "extra_context": {
                "defer": True,
            },
        },
        "momentjs": {
            "source_filenames": ("vendor/moment.min.js",),
            "output_filename": "js/moment.js",
            "extra_context": {
                "defer": True,
            },
        },
        "videojs": {
            "source_filenames": ("vendor/videojs/videojs.min.js",),
            "output_filename": "js/videojs.js",
            "extra_context": {
                "defer": True,
            },
        },
        "daterangepicker": {
            "source_filenames": ("vendor/daterangepicker/daterangepicker.js",),
            "output_filename": "js/daterangepicker.js",
            "extra_context": {
                "defer": True,
            },
        },
        "main": {
            "source_filenames": (
                "js/helper.js",
                "js/jleague.js",
                "js/lazy-loader.js",
                "js/cookie-settings.js",
                "js/menu-modals.js",
                "js/teams-list.js",
                "js/competition-dropdown.js",
                "js/goal-highlights.js",
                "js/banner-slider.js",
                "search/js/search-modal.js",
            ),
            "output_filename": "js/main.js",
            "extra_context": {
                "defer": True,
            },
        },
        "sidebar": {
            "source_filenames": ("js/sidebar.js",),
            "output_filename": "js/sidebar.js",
            "extra_context": {
                "defer": True,
            },
        },
        "homepage": {
            "source_filenames": (
                "js/tickets-modal.js",
                "home/js/home-page.js",
            ),
            "output_filename": "js/homepage.js",
            "extra_context": {
                "defer": True,
            },
        },
        "news-page": {
            "source_filenames": ("news/js/news_index_page.js",),
            "output_filename": "js/news-page.js",
            "extra_context": {
                "defer": True,
            },
        },
        "broadcast-page": {
            "source_filenames": ("about/js/broadcast.js",),
            "output_filename": "js/broadcast-page.js",
            "extra_context": {
                "defer": True,
            },
        },
        "asia-challenge-2022": {
            "source_filenames": (
                "js/lazy-loader.js",
                "landingpages/asia-challenge-2022/js/script.js",
            ),
            "output_filename": "js/asia-challenge-2022.js",
            "extra_context": {
                "defer": True,
            },
        },
        "asia-challenge-2023": {
            "source_filenames": (
                "landingpages/asia-challenge-2023/js/match-overview.js",
                "landingpages/asia-challenge-2023/js/match-lineups.js",
                "landingpages/asia-challenge-2023/js/match-details.js",
                "landingpages/asia-challenge-2023/js/script.js",
            ),
            "output_filename": "js/asia-challenge-2023.js",
            "extra_context": {
                "defer": True,
            },
        },
        "fujifilm-super-cup-2023": {
            "source_filenames": ("landingpages/fujifilm-super-cup-2023/js/script.js",),
            "extra_context": {
                "defer": True,
            },
            "output_filename": "js/fujifilm-super-cup-2023.js",
        },
        "fujifilm-super-cup-2024": {
            "source_filenames": (
                "landingpages/fujifilm-super-cup-2024/js/script.js",
                "js/tickets-modal.js",
            ),
            "extra_context": {
                "defer": True,
            },
            "output_filename": "js/fujifilm-super-cup-2024.js",
        },
        "fujifilm-super-cup-2025": {
            "source_filenames": (
                "landingpages/fujifilm-super-cup-2025/js/script.js",
                "js/tickets-modal.js",
            ),
            "extra_context": {
                "defer": True,
            },
            "output_filename": "js/fujifilm-super-cup-2025.js",
        },
        "fujifilm-super-cup-2025-sub-child": {
            "source_filenames": (
                "landingpages/fujifilm-super-cup-2025/sub-child/js/script.js",
                "js/tickets-modal.js",
            ),
            "extra_context": {
                "defer": True,
            },
            "output_filename": "js/fujifilm-super-cup-2025-sub-child.js",
        },
        "special-250525": {
            "source_filenames": (
                "landingpages/special/js/script.js",
            ),
            "extra_context": {
                "defer": True,
            },
            "output_filename": "js/special.js",
        },
        "kokuritsu": {
            "source_filenames": (
                "landingpages/kokuritsu/js/script.js",
                "js/tickets-modal.js",
            ),
            "extra_context": {
                "defer": True,
            },
            "output_filename": "js/kokuritsu.js",
        },
        "jwc": {
            "source_filenames": (
                "landingpages/jwc/js/script.js",
                "js/tickets-modal.js",
            ),
            "extra_context": {
                "defer": True,
            },
            "output_filename": "js/jwc.js",
        },
        "thirty-years": {
            "source_filenames": ("landingpages/thirty-years/js/script.js",),
            "extra_context": {
                "defer": True,
            },
            "output_filename": "js/thirty-years.js",
        },
        "clubs-page": {
            "source_filenames": ("clubs/js/club_list_page.js",),
            "output_filename": "js/clubs-page.js",
            "extra_context": {
                "defer": True,
            },
        },
        "club-profile-page": {
            "source_filenames": (
                "clubs/js/club_profile_players.js",
                "clubs/js/club_profile_stats.js",
                "clubs/js/club_profile_page.js",
            ),
            "output_filename": "js/club-profile-page.js",
            "extra_context": {
                "defer": True,
            },
        },
        "fixtures-page": {
            "source_filenames": (
                "js/swiper-competitions.js",
                "js/tickets-modal.js",
                "fixtures/js/fixtures_page.js",
            ),
            "output_filename": "js/fixtures-page.js",
            "extra_context": {
                "defer": True,
            },
        },
        "tickets-page": {
            "source_filenames": (
                "js/swiper-competitions.js",
                "js/tickets-modal.js",
                "fixtures/js/tickets_page.js",
            ),
            "output_filename": "js/tickets-page.js",
            "extra_context": {
                "defer": True,
            },
        },
        "match-details-page": {
            "source_filenames": (
                "js/tickets-modal.js",
                "match/js/match_overview.js",
                "match/js/match_lineups.js",
                "match/js/match_stats.js",
                "match/js/match_standings.js",
                "match/js/match_detail.js",
            ),
            "output_filename": "js/match-details-page.js",
            "extra_context": {
                "defer": True,
            },
        },
        "player-profile-page": {
            "source_filenames": (
                "players/js/player_stats.js",
                "players/js/player_game_log.js",
                "players/js/player_profile_page.js",
            ),
            "output_filename": "js/player-profile-page.js",
            "extra_context": {
                "defer": True,
            },
        },
        "club-stats-page": {
            "source_filenames": ("stats/js/stats_clubs.js",),
            "output_filename": "js/club-stats-page.js",
            "extra_context": {
                "defer": True,
            },
        },
        "player-stats-page": {
            "source_filenames": ("stats/js/stats_players.js",),
            "output_filename": "js/player-stats-page.js",
            "extra_context": {
                "defer": True,
            },
        },
        "dashboard-stats-page": {
            "source_filenames": ("stats/js/stats_dashboard.js",),
            "output_filename": "js/dashboard-stats-page.js",
            "extra_context": {
                "defer": True,
            },
        },
        "standings-page": {
            "source_filenames": (
                "js/swiper-competitions.js",
                "standings/js/standings_page.js",
            ),
            "output_filename": "js/standings-page.js",
            "extra_context": {
                "defer": True,
            },
        },
        "jersey-giveaway": {
            "source_filenames": ("landingpages/jersey-giveaway/js/script.js",),
            "output_filename": "js/jersey-giveaway.js",
            "extra_context": {
                "defer": True,
            },
        },
        "tminternal": {
            "source_filenames": (
                "vendor/html2canvas.min.js",
                "js/lazy-loader.js",
                "tminternal/js/main.js",
            ),
            "output_filename": "js/tminternal.js",
            "extra_context": {
                "defer": True,
            },
        },
        "watch-party": {
            "source_filenames": ("landingpages/watch-party/js/script.js",),
            "extra_context": {
                "defer": True,
            },
            "output_filename": "js/watch-party.js",
        },
        # "ext_cerezo": {
        #     "source_filenames": (
        #         "vendor/html2canvas.min.js",
        #         # "js/lazy-loader.js",
        #         "ext_cerezo/js/main.js",
        #     ),
        #     "output_filename": "js/ext_cerezo.js",
        #     "extra_context": {
        #         "defer": True,
        #     },
        # },
        "next-gen-page": {
            "source_filenames": ("landingpages/next-gen/js/script.js",),
            "output_filename": "js/next-gen.js",
            "extra_context": {
                "defer": True,
            },
        },
        "clubtickets-lp": {
            "source_filenames": (
                "landingpages/clubtickets-lp/js/script.js",
            ),
            "output_filename": "js/clubtickets-lp.js",
            "extra_context": {
                "defer": True,
            },
        },
        "fujifilm-super-cup-2025-sub-child": {
            "source_filenames": (
                "landingpages/fujifilm-super-cup-2025/sub-child/js/script.js",
            ),
            "output_filename": "static/js/fujifilm-super-cup-2025-sub-child.js",
            "extra_context": {
                "defer": True,
            },
        },
    },
    "STYLESHEETS": {
        "vendor": {
            "source_filenames": (
                "vendor/swiperjs/swiper-bundle.min.css",
                "vendor/videojs/videojs.min.css",
            ),
            "output_filename": "css/vendor.css",
        },
        "daterangepicker": {
            "source_filenames": ("vendor/daterangepicker/daterangepicker.css",),
            "output_filename": "css/daterangepicker.css",
        },
        "atropos": {
            "source_filenames": ("vendor/atropos/atropos.min.css",),
            "output_filename": "css/astropos.css",
        },
        "videojs": {
            "source_filenames": ("vendor/videojs/videojs.min.css",),
            "output_filename": "css/videojs.css",
        },
        "main": {
            "source_filenames": (
                "vendor/mono-icons/icons.css",
                "style/index.scss",
                "search/style/search-modal.scss",
                "widgets/style/teams-list.scss",
            ),
            "output_filename": "css/main.css",
        },
        "sidebar": {
            "source_filenames": (
                "style/components/competition-dropdown.scss",
                "style/components/goal-highlights/goal-highlights.scss",
                "style/components/match-event-icon.scss",
                "style/components/score-board.scss",
                "style/components/sidebar/sidebar.scss",
            ),
            "output_filename": "css/sidebar.css",
        },
        "homepage": {
            "source_filenames": (
                "home/style/home-page.scss",
                "widgets/style/home-live-game.scss",
                "widgets/style/home-news.scss",
                "widgets/style/home-photos.scss",
                "widgets/style/home-today-games.scss",
                "widgets/style/home-videos.scss",
                "style/components/news-articles-list.scss",
            ),
            "output_filename": "css/homepage.css",
        },
        "about-page": {
            "source_filenames": (
                "about/style/about.scss",
                "about/style/about_navigation.scss",
                "about/style/about_breadcrumb.scss",
            ),
            "output_filename": "css/about-page.css",
        },
        "broadcast-page": {
            "source_filenames": (
                "about/style/about_breadcrumb.scss",
                "about/style/broadcast.scss",
                "style/components/news-articles-list.scss",
            ),
            "output_filename": "css/broadcast-page.css",
        },
        "asia-challenge-2022": {
            "source_filenames": (
                "style/index.scss",
                "home/style/components/home_page_latest_news.scss",
                "news/style/components/related_news.scss",
                "fixtures/style/components/fixtures_results.scss",
                "landingpages/asia-challenge-2022/style/asia-challenge.scss",
            ),
            "output_filename": "css/asia-challenge-2022.css",
        },
        "asia-challenge-2023": {
            "source_filenames": (
                "style/components/football-field-half.scss",
                "style/components/football-field-player.scss",
                "style/components/match-event-icon.scss",
                "style/components/score-board.scss",
                "home/style/components/home_page_latest_news.scss",
                "landingpages/asia-challenge-2023/styles/style.scss",
            ),
            "output_filename": "css/asia-challenge-2023.css",
        },
        "fujifilm-super-cup-2023": {
            "source_filenames": (
                "landingpages/fujifilm-super-cup-2023/style/fujifilm-super-cup.scss",
                "fixtures/style/components/fixtures_results.scss",
            ),
            "output_filename": "css/fujifilm-super-cup-2023.css",
        },
        "fujifilm-super-cup-2024": {
            "source_filenames": (
                "fixtures/style/components/fixtures_results.scss",
                "landingpages/fujifilm-super-cup-2024/style/fujifilm-super-cup.scss",
            ),
            "output_filename": "css/fujifilm-super-cup-2024.css",
        },
        "fujifilm-super-cup-2025": {
            "source_filenames": (
                "fixtures/style/components/fixtures_results.scss",
                "landingpages/fujifilm-super-cup-2025/style/fujifilm-super-cup.scss",
            ),
            "output_filename": "css/fujifilm-super-cup-2025.css",
        },
        "fujifilm-super-cup-2025-sub-child": {
            "source_filenames": (
                "fixtures/style/components/fixtures_results.scss",
                "landingpages/fujifilm-super-cup-2025/sub-child/style/sub-child.scss",
            ),
            "output_filename": "css/fujifilm-super-cup-2025-sub-child.css",
        },
        "special-250525": {
            "source_filenames": (
                "fixtures/style/components/fixtures_results.scss",
                "landingpages/special/style/special250525.scss",
            ),
            "output_filename": "css/special250525.css",
        },
        "kokuritsu": {
            "source_filenames": (
                "fixtures/style/components/fixtures_results.scss",
                "landingpages/kokuritsu/style/kokuritsu.scss",
            ),
            "output_filename": "css/kokuritsu.css",
        },
        "jwc": {
            "source_filenames": (
                "fixtures/style/components/fixtures_results.scss",
                "landingpages/jwc/style/jwc.scss",
            ),
            "output_filename": "css/jwc.css",
        },
        "aboutj": {
            "source_filenames": (
                "landingpages/aboutj/style/aboutj.scss",
            ),
            "output_filename": "css/aboutj.css",
        },
        "thirty-years": {
            "source_filenames": ("landingpages/thirty-years/style/style.scss",),
            "output_filename": "css/thirty-years.css",
        },
        "clubs-page": {
            "source_filenames": ("clubs/style/club_list_page.scss",),
            "output_filename": "css/clubs-page.css",
        },
        "club-profile-page": {
            "source_filenames": (
                "style/components/player-rank-board.scss",
                "clubs/style/club_profile_page.scss",
            ),
            "output_filename": "css/club-profile-page.css",
        },
        "fixtures-page": {
            "source_filenames": ("fixtures/style/fixtures.scss",),
            "output_filename": "css/fixtures-page.css",
        },
        "match-details-page": {
            "source_filenames": (
                "style/components/player-rank-board.scss",
                "style/components/football-field-half.scss",
                "style/components/football-field-player.scss",
                "match/style/match_detail.scss",
            ),
            "output_filename": "css/match-details-page.css",
        },
        "news-page": {
            "source_filenames": (
                "news/style/news_index_page.scss",
                "style/components/news-articles-list.scss",
            ),
            "output_filename": "css/news-page.css",
        },
        "news-article-page": {
            "source_filenames": (
                "news/style/news_article_page.scss",
                "news/style/components/related_news.scss",
            ),
            "output_filename": "css/news-article-page.css",
        },
        "photos-page": {
            "source_filenames": ("media/style/photos.scss",),
            "output_filename": "css/photos-page.css",
        },
        "videos-page": {
            "source_filenames": ("media/style/videos.scss",),
            "output_filename": "css/videos-page.css",
        },
        "player-profile-page": {
            "source_filenames": ("players/style/player_profile/player_profile.scss",),
            "output_filename": "css/player-profile-page.css",
        },
        "standings-page": {
            "source_filenames": ("standings/style/standings_page.scss",),
            "output_filename": "css/standings-page.css",
        },
        "club-stats-page": {
            "source_filenames": (
                "stats/style/stats.scss",
                "stats/style/components/stats_clubs.scss",
            ),
            "output_filename": "css/club-stats-page.css",
        },
        "player-stats-page": {
            "source_filenames": (
                "stats/style/stats.scss",
                "stats/style/components/stats_players.scss",
            ),
            "output_filename": "css/player-stats-page.css",
        },
        "dashboard-stats-page": {
            "source_filenames": (
                "stats/style/stats.scss",
                "stats/style/components/stats_dashboard.scss",
                "style/components/player-rank-board.scss",
                "style/components/club-rank-board.scss",
            ),
            "output_filename": "css/dashboard-stats-page.css",
        },
        "jersey-giveaway": {
            "source_filenames": ("landingpages/jersey-giveaway/style/style.scss",),
            "output_filename": "css/jersey-giveaway.css",
        },
        "tminternal": {
            "source_filenames": (
                "style/components/football-field-half.scss",
                "style/components/football-field-player.scss",
                "style/components/section-header-v2.scss",
                "style/components/team/index.scss",
                "tminternal/style/index.scss",
            ),
            "output_filename": "css/tminternal.css",
        },
        "newsletter-page": {
            "source_filenames": ("newsletter/style/newsletter_page.scss",),
            "output_filename": "css/newsletter_page.css",
        },
        "watch-party": {
            "source_filenames": ("landingpages/watch-party/style/style.scss",),
            "output_filename": "css/watch-party-page.css",
        },
        # "ext_cerezo": {
        #     "source_filenames": ("ext_cerezo/style/index.scss",),
        #     "output_filename": "css/ext_cerezo.css",
        # },
        "next-gen-page": {
            "source_filenames": ("landingpages/next-gen/style/style.scss",),
            "output_filename": "css/next-gen.css",
        },
    },
}

# ------------------------------------------------------------------------------
# STORAGE
# ------------------------------------------------------------------------------
# https://django-storages.readthedocs.io/en/latest/backends/amazon-S3.html#settings
ENDPOINT_BASE_URL = env("STORAGE_ENDPOINT_BASE_URL", default=None)
AWS_S3_FILE_OVERWRITE = env.bool("STORAGE_AWS_S3_FILE_OVERWRITE", default=False)
AWS_DEFAULT_ACL = env("STORAGE_AWS_DEFAULT_ACL", default=None)
AWS_ACCESS_KEY_ID = env("STORAGE_AWS_ACCESS_KEY_ID", default=None)
AWS_SECRET_ACCESS_KEY = env("STORAGE_AWS_SECRET_ACCESS_KEY", default=None)
AWS_STORAGE_BUCKET_NAME = env("STORAGE_AWS_STORAGE_BUCKET_NAME", default=None)
AWS_S3_REGION_NAME = env("STORAGE_AWS_S3_REGION_NAME", default=None)
AWS_S3_ENDPOINT_URL = (
    f"https://{AWS_STORAGE_BUCKET_NAME}.s3.{AWS_S3_REGION_NAME}.{ENDPOINT_BASE_URL}"
)

# Disable querystring authentication for public access
AWS_QUERYSTRING_AUTH = False

# ------------------------------------------------------------------------------
# Channels
# ------------------------------------------------------------------------------
# https://github.com/django/channels_redis/?tab=readme-ov-file#usage
CHANNEL_LAYERS = {
    "default": {
        "BACKEND": "channels_redis.core.RedisChannelLayer",
        "CONFIG": {
            "hosts": [("redis", 6379)],
        },
    },
}

# ------------------------------------------------------------------------------
# DATA STADIUM
# ------------------------------------------------------------------------------
DS_INTEGRATION_FTP_HOST = env("DATASTADIUM_FTP_HOST", default=None)
DS_INTEGRATION_FTP_USER = env("DATASTADIUM_FTP_USER", default=None)
DS_INTEGRATION_FTP_PASSWORD = env("DATASTADIUM_FTP_PASSWORD", default=None)
DS_INTEGRATION_HEALTHCHECK_URL = env("DATASTADIUM_HEALTHCHECK_URL", default=None)
DS_INTEGRATION_SOCKS_PROXY_HOST = env("DATASTADIUM_SOCKS_PROXY_HOST", default=None)
DS_INTEGRATION_SOCKS_PROXY_PORT = env.int("DATASTADIUM_SOCKS_PROXY_PORT", default=None)
DS_INTEGRATION_SOCKS_PROXY_VERSION = env.int(
    "DATASTADIUM_SOCKS_PROXY_VERSION", default=None
)
DS_INTEGRATION_USE_SOCKS_PROXY = env.bool("DATASTADIUM_USE_SOCKS_PROXY", default=False)

# ------------------------------------------------------------------------------
# IPREGISTRY
# ------------------------------------------------------------------------------
IPREGISTRY_API_ENDPOINT = env("IPREGISTRY_API_ENDPOINT", default=None)
IPREGISTRY_API_KEY = env("IPREGISTRY_API_KEY", default=None)

# ------------------------------------------------------------------------------
# SLACK
# ------------------------------------------------------------------------------
SLACK_BOT_TOKEN = env("SLACK_BOT_TOKEN", default=None)
SLACK_CHANNEL_ID_TEST_SLACK_API = env("SLACK_CHANNEL_ID_TEST_SLACK_API", default=None)
SLACK_CHANNEL_ID_JLEGUECO_DEV = env("SLACK_CHANNEL_ID_JLEGUECO_DEV", default=None)

# ------------------------------------------------------------------------------
# WHITENOISE
# ------------------------------------------------------------------------------
WHITENOISE_MANIFEST_STRICT = False

# ------------------------------------------------------------------------------
# MAILER LITE
# ------------------------------------------------------------------------------
MAILER_LITE_ENABLED = False

# ------------------------------------------------------------------------------
# SEO
# ------------------------------------------------------------------------------
SEO_TRACKERS_ENABLED = False

# ------------------------------------------------------------------------------
# WSC
# ------------------------------------------------------------------------------
WSC_VIDEO_ALERT_ENABLED = False
WSC_VIDEO_RELEASED_SINCE = datetime.date(2022, 8, 1)
# WSC videos path
WSC_VIDEO_ENDPOINT_URL = (
    f"https://{AWS_STORAGE_BUCKET_NAME}.s3.{AWS_S3_REGION_NAME}.{ENDPOINT_BASE_URL}"
)

# ------------------------------------------------------------------------------
# django-rest-framework
# ------------------------------------------------------------------------------
# https://www.django-rest-framework.org/api-guide/settings/#settings
REST_FRAMEWORK = {
    # https://www.django-rest-framework.org/api-guide/settings/#default_authentication_classes
    "DEFAULT_AUTHENTICATION_CLASSES": (
        # https://www.django-rest-framework.org/api-guide/authentication/#sessionauthentication
        "rest_framework.authentication.SessionAuthentication",
        # https://www.django-rest-framework.org/api-guide/authentication/#tokenauthentication
        "jleague.authentication.BearerTokenAuthentication",
    ),
    # https://www.django-rest-framework.org/api-guide/settings/#default_permission_classes
    "DEFAULT_PERMISSION_CLASSES": ("rest_framework.permissions.IsAuthenticated",),
    # https://www.django-rest-framework.org/api-guide/settings/#default_pagination_class
    "DEFAULT_PAGINATION_CLASS": "rest_framework.pagination.LimitOffsetPagination",
    # https://www.django-rest-framework.org/api-guide/settings/#default_filter_backends
    "DEFAULT_FILTER_BACKENDS": (
        # https://www.django-rest-framework.org/api-guide/filtering/#orderingfilter
        "rest_framework.filters.OrderingFilter",
        # https://django-filter.readthedocs.io/en/stable/guide/rest_framework.html
        "django_filters.rest_framework.DjangoFilterBackend",
    ),
    # https://www.django-rest-framework.org/api-guide/settings/#page_size
    "PAGE_SIZE": 5,
    # https://www.django-rest-framework.org/api-guide/settings/#default_renderer_classes
    "DEFAULT_RENDERER_CLASSES": ("rest_framework.renderers.JSONRenderer",),
}

# ------------------------------------------------------------------------------
# django-cors-headers
# ------------------------------------------------------------------------------
# https://github.com/adamchainz/django-cors-headers#setup
CORS_ALLOWED_ORIGINS = env.list("DJANGO_CORS_ALLOWED_ORIGINS", default=[])
CORS_ALLOWED_ORIGIN_REGEXES = env.list("DJANGO_CORS_ALLOWED_ORIGIN_REGEXES", default=[])
CORS_URLS_REGEX = r"^/api/.*$"

# ------------------------------------------------------------------------------
# google captcha
# ------------------------------------------------------------------------------
GOOGLE_CAPTCHA_SECRET_KEY = env("GOOGLE_CAPTCHA_SECRET_KEY", default=None)
GOOGLE_CAPTCHA_PUBLIC_KEY = env("GOOGLE_CAPTCHA_PUBLIC_KEY", default=None)
