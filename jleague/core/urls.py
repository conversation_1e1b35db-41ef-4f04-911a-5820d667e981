from django.urls import path
from . import views

urlpatterns = [
    path("offline/", views.OfflineView.as_view(), name="offline"),
    path("sw.js", views.ServiceWorkerView.as_view(), name="service-worker"),
    path("robots.txt", views.RobotsTxtView.as_view(), name="robots-txt"),
    path("health/", views.health),
    path("t/", views.generate_token),
    path("tools/cache/", views.tool_caches),
    path("tools/cache/clear/", views.tool_cache_clear),
    path("tools/cache/delete/", views.tool_cache_delete),
    path("i/<slug:sns>/<slug:name>/", views.influencer_count),
    # path('notify_me/', views.notify_me),
    # path('add_subscription/', views.add_subscription),
    # path('remove_subscription/', views.remove_subscription),
]
