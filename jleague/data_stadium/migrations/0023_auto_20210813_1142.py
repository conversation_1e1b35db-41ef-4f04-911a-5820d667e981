# Generated by Django 3.1.13 on 2021-08-13 15:42

from django.db import migrations, models
import django.db.models.deletion
import modelcluster.fields
import wagtail.fields


class Migration(migrations.Migration):
    dependencies = [
        ('wagtailimages', '0023_add_choose_permissions'),
        ('data_stadium', '0022_auto_20210811_0514'),
    ]

    operations = [
        migrations.CreateModel(
            name='ImageItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, null=True)),
                ('description', wagtail.fields.RichTextField(null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='VideoItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('description', wagtail.fields.RichTextField(null=True)),
                ('video_url', models.URLField(verbose_name='URL')),
                ('thumbnail_url', models.URLField()),
                ('embed_url', models.URLField()),
                ('html', models.TextField()),
                ('author_name', models.CharField(max_length=255, null=True)),
                ('provider_name', models.CharField(max_length=255)),
                ('date', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.AddField(
            model_name='stadium',
            name='address',
            field=models.TextField(null=True),
        ),
        migrations.AddField(
            model_name='stadium',
            name='city',
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='stadium',
            name='introduction',
            field=wagtail.fields.RichTextField(null=True),
        ),
        migrations.AddField(
            model_name='team',
            name='address',
            field=models.TextField(null=True),
        ),
        migrations.AddField(
            model_name='team',
            name='introduction',
            field=wagtail.fields.RichTextField(null=True),
        ),
        migrations.AddField(
            model_name='team',
            name='practice_ground',
            field=models.TextField(null=True),
        ),
        migrations.AddIndex(
            model_name='referee',
            index=models.Index(fields=['id', 'name'], name='data_stadiu_id_620a08_idx'),
        ),
        migrations.AddConstraint(
            model_name='referee',
            constraint=models.UniqueConstraint(fields=('id',), name='referee_primary_keys'),
        ),
        migrations.AddField(
            model_name='imageitem',
            name='photo',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='wagtailimages.image'),
        ),
        migrations.AddField(
            model_name='game',
            name='videos',
            field=modelcluster.fields.ParentalManyToManyField(
                blank=True, related_name='videos', to='data_stadium.VideoItem'
            ),
        ),
        migrations.AddField(
            model_name='stadium',
            name='featured_photo',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='featured_photo',
                to='data_stadium.imageitem',
            ),
        ),
        migrations.AddField(
            model_name='stadium',
            name='photos',
            field=modelcluster.fields.ParentalManyToManyField(
                blank=True, related_name='photos', to='data_stadium.ImageItem'
            ),
        ),
        migrations.AddField(
            model_name='stadium',
            name='skeleton',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='skeleton',
                to='data_stadium.imageitem',
            ),
        ),
        migrations.AddField(
            model_name='team',
            name='mascotes',
            field=modelcluster.fields.ParentalManyToManyField(
                blank=True, related_name='mascotes', to='data_stadium.ImageItem'
            ),
        ),
    ]
