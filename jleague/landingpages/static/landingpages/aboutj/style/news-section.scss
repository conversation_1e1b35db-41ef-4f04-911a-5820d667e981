@mixin flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin font-settings($family, $size, $weight: normal, $style: normal, $line-height: normal, $letter-spacing: normal, $transform: none) {
  font-family: $family;
  font-size: $size;
  font-weight: $weight;
  font-style: $style;
  line-height: $line-height;
  letter-spacing: $letter-spacing;
  text-transform: $transform;
}

@mixin text_ellipsis($lines: 1) {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
}

#news.section.section--news {
  padding: 80px 40px 120px 40px;

  .section__body {
    max-width: none;
  }

  .related-news__body {
    display: flex;
    justify-content: space-between;

    .related-news__body__item {
      width: 276px;
      margin-top: 40px;

      &>img:first-child {
        width: 100%;
        height: auto;
        aspect-ratio: 3 / 2;
        object-fit: cover;
        border-radius: 3px;
      }

      &>div:nth-child(2) {
        &>div:first-child {
          margin-top: 16px;
          font-family: var(--font-barlow-condensed);
          font-style: normal;
          font-weight: bold;
          font-size: 14px;
          line-height: 16px;
          letter-spacing: 1.66667px;
          text-transform: uppercase;
          color: var(--color-j1);
        }

        &>h3:nth-child(2) {
          margin-top: 8px;
          font-family: var(--font-overpass);
          font-style: normal;
          font-weight: 900;
          font-size: 20px;
          line-height: 24px;
          color: var(--color-black);
          @include text_ellipsis(3);
        }

        &>div:nth-child(3) {
          margin-top: 8px;
          font-family: var(--font-barlow-condensed);
          font-style: normal;
          font-weight: bold;
          font-size: 14px;
          line-height: 16px;
          letter-spacing: 0.5px;
          text-transform: uppercase;
          color: var(--color-drawn);
        }
      }
    }
  }

  .related-news__more-button {
    margin: 40px auto 80px auto;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 150px;
    height: 40px;
    border: 1px solid var(--color-black);
    border-radius: 20px;
    font-family: var(--font-barlow-condensed);
    font-style: normal;
    font-weight: bold;
    font-size: 16px;
    line-height: 19px;
    text-align: center;
    letter-spacing: 1.92px;
    text-transform: uppercase;
    color: var(--color-black);
  }

  .news-articles-list {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    gap: 20px;
  }

  .text-body-news {
    text-align: center;
    @include font-settings(var(--font-overpass), 2rem);
  }

  .home__latest-news-container {
    .related-news__more-button {
      margin: 40px auto 80px;
      @include flex-center;
      width: 150px;
      height: 40px;
      border: 1px solid var(--color-black);
      border-radius: 20px;
      @include font-settings(var(--font-barlow-condensed), 16px, bold, normal, 19px, 1.92px, uppercase);
      color: var(--color-black);
    }

    .home__latest-news-content {
      background-color: var(--color-blue);
      gap: 32px;

      .home__latest-news-content__header {
        border-radius: 3px;
        filter: blur(0px);
        flex: auto;
        width: 100%;
      }

      .home__latest-news-content__body {
        padding: 0;

        .home__latest-news-article {

          .home__latest-news-article__category,
          .home__latest-news-article__title,
          .home__latest-news-article__content {
            color: var(--color-white);
          }

          .home__latest-news-article__category {
            text-transform: uppercase;
          }
        }
      }
    }

    &:first-child {
      .home__latest-news-content__body {
        padding-top: 32px;
      }
    }

    &:not(:first-child) {
      .home__latest-news-content {
        .home__latest-news-content__header {
          max-width: 420px;
          max-height: 280px;
        }

        .home__latest-news-article__read-more-button {
          display: none;
        }
      }
    }
  }


  @media (max-width: 1199px) {
    padding: 0 48px;

    .related-news__body {
      display: block;
    }
  }

  @media (max-width: 1199px) and (min-width: 768px) {
    .related-news__body {
      font-size: 0px;

      &>.related-news__body__item:nth-of-type(even) {
        margin-left: 32px;
      }

      .related-news__body__item {
        width: calc(50% - 16px);
        display: inline-flex;
        flex-wrap: wrap;
      }
    }
  }

  @media (max-width: 1023px) {
    padding: 0 32px;
  }

  @media (max-width: 767px) {
    padding: 0 24px;

    .related-news__body {
      .related-news__body__item {
        display: flex;
        width: 100%;
        margin-top: 24px;

        &>img:first-child {
          width: 120px;
          height: 120px;
          object-fit: cover;
        }

        &>div:nth-child(2) {
          margin-left: 16px;

          &>div:first-child {
            margin-top: 0px;
          }
        }
      }
    }
  }

  @media (max-width: 425px) {
    .news-articles-list {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      gap: 20px;
    }
  }
}
