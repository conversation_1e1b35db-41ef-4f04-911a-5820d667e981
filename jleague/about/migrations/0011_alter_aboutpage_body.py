# Generated by Django 4.1.4 on 2022-12-28 10:42

from django.db import migrations
import wagtail.blocks
import wagtail.contrib.table_block.blocks
import wagtail.embeds.blocks
import wagtail.fields
import wagtail.images.blocks


class Migration(migrations.Migration):
    dependencies = [
        ("about", "0010_remove_navigationlink_link_page_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="aboutpage",
            name="body",
            field=wagtail.fields.StreamField(
                [
                    ("richtext", wagtail.blocks.RichTextBlock()),
                    ("embed", wagtail.embeds.blocks.EmbedBlock()),
                    ("image", wagtail.images.blocks.ImageChooserBlock()),
                    ("table", wagtail.contrib.table_block.blocks.TableBlock()),
                    (
                        "sectionheader",
                        wagtail.blocks.StructBlock([("title", wagtail.blocks.CharBlock())]),
                    ),
                    ("rawhtml", wagtail.blocks.RawHTMLBlock()),
                ],
                use_json_field=True,
            ),
        ),
    ]
