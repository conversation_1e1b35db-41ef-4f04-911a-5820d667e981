<?xml version="1.0" encoding="UTF-8"?>
<svg width="260" height="80" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 260 80">
  <defs>
    <style>
      .cls-1 {
        fill: url(#radial-gradient-6);
        opacity: .05;
      }

      .cls-2 {
        fill: url(#radial-gradient-2);
      }

      .cls-2, .cls-3 {
        opacity: .2;
      }

      .cls-3 {
        fill: url(#radial-gradient-3);
      }

      .cls-4 {
        fill: #150335;
      }

      .cls-5 {
        fill: url(#radial-gradient-5);
      }

      .cls-6 {
        fill: url(#radial-gradient-4);
      }

      .cls-7 {
        fill: url(#linear-gradient-8);
      }

      .cls-8 {
        fill: url(#linear-gradient-7);
      }

      .cls-9 {
        fill: url(#linear-gradient-5);
      }

      .cls-10 {
        fill: url(#linear-gradient-6);
      }

      .cls-11 {
        fill: url(#linear-gradient-9);
      }

      .cls-12 {
        fill: url(#linear-gradient-4);
      }

      .cls-13 {
        fill: url(#linear-gradient-3);
      }

      .cls-14 {
        fill: url(#linear-gradient-2);
      }

      .cls-15 {
        fill: url(#linear-gradient);
      }

      .cls-16 {
        fill: url(#radial-gradient);
      }

      .cls-16, .cls-17 {
        opacity: .6;
      }

      .cls-18 {
        fill: url(#linear-gradient-39);
        opacity: .8;
      }

      .cls-17 {
        fill: url(#linear-gradient-11);
      }

      .cls-19 {
        fill: url(#linear-gradient-38);
        opacity: .4;
      }

      .cls-20 {
        fill: url(#linear-gradient-43);
      }

      .cls-21 {
        fill: url(#linear-gradient-40);
      }

      .cls-22 {
        fill: url(#linear-gradient-41);
      }

      .cls-23 {
        fill: url(#linear-gradient-42);
      }

      .cls-24 {
        fill: url(#linear-gradient-45);
      }

      .cls-25 {
        fill: url(#linear-gradient-44);
      }

      .cls-26 {
        fill: url(#linear-gradient-46);
      }

      .cls-27 {
        fill: url(#linear-gradient-10);
      }

      .cls-28 {
        fill: url(#linear-gradient-12);
      }

      .cls-29 {
        fill: url(#linear-gradient-13);
      }

      .cls-30 {
        fill: url(#linear-gradient-19);
      }

      .cls-31 {
        fill: url(#linear-gradient-14);
      }

      .cls-32 {
        fill: url(#linear-gradient-21);
      }

      .cls-33 {
        fill: url(#linear-gradient-20);
      }

      .cls-34 {
        fill: url(#linear-gradient-22);
      }

      .cls-35 {
        fill: url(#linear-gradient-23);
      }

      .cls-36 {
        fill: url(#linear-gradient-28);
      }

      .cls-37 {
        fill: url(#linear-gradient-25);
      }

      .cls-38 {
        fill: url(#linear-gradient-32);
      }

      .cls-39 {
        fill: url(#linear-gradient-31);
      }

      .cls-40 {
        fill: url(#linear-gradient-33);
      }

      .cls-41 {
        fill: url(#linear-gradient-30);
      }

      .cls-42 {
        fill: url(#linear-gradient-15);
      }

      .cls-43 {
        fill: url(#linear-gradient-16);
      }

      .cls-44 {
        fill: url(#linear-gradient-17);
      }

      .cls-45 {
        fill: url(#linear-gradient-18);
      }

      .cls-46 {
        fill: url(#linear-gradient-35);
      }

      .cls-47 {
        fill: url(#linear-gradient-34);
      }

      .cls-48 {
        fill: url(#linear-gradient-36);
      }

      .cls-49 {
        fill: url(#linear-gradient-27);
      }

      .cls-50 {
        fill: url(#linear-gradient-24);
      }

      .cls-51 {
        fill: url(#linear-gradient-26);
      }

      .cls-52 {
        fill: url(#linear-gradient-29);
      }

      .cls-53 {
        fill: url(#linear-gradient-37);
      }

      .cls-54 {
        fill: #af9870;
      }
    </style>
    <linearGradient id="linear-gradient" x1="24.68" y1="1.01" x2="24.68" y2="76.17" gradientUnits="userSpaceOnUse">
      <stop offset=".28" stop-color="#5e4c8f"/>
      <stop offset=".29" stop-color="#645395"/>
      <stop offset=".33" stop-color="#7869a9"/>
      <stop offset=".37" stop-color="#8477b5"/>
      <stop offset=".4" stop-color="#897cba"/>
      <stop offset=".48" stop-color="#9b92c6"/>
      <stop offset=".57" stop-color="#897cba"/>
      <stop offset=".59" stop-color="#8376b4"/>
      <stop offset=".67" stop-color="#5e4c8f"/>
      <stop offset=".89" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="6381.09" y1="10.62" x2="6391.19" y2="28.05" gradientTransform="translate(6427.16) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".06" stop-color="#cfd2d2"/>
      <stop offset=".28" stop-color="#897cba"/>
      <stop offset="1" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="6383.86" y1="7.36" x2="6393.67" y2="36.37" gradientTransform="translate(6427.16) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".08" stop-color="#cfd2d2"/>
      <stop offset=".28" stop-color="#5e4c8f"/>
      <stop offset=".38" stop-color="#5e4c8f"/>
      <stop offset=".55" stop-color="#897cba"/>
      <stop offset=".61" stop-color="#6e5d9f"/>
      <stop offset=".65" stop-color="#5e4c8f"/>
      <stop offset="1" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="6389.36" y1="4.94" x2="6393.06" y2="39.51" gradientTransform="translate(6427.16) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".05" stop-color="#cfd2d2"/>
      <stop offset=".28" stop-color="#5e4c8f"/>
      <stop offset=".38" stop-color="#5e4c8f"/>
      <stop offset=".55" stop-color="#897cba"/>
      <stop offset=".61" stop-color="#6e5d9f"/>
      <stop offset=".65" stop-color="#5e4c8f"/>
      <stop offset="1" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="3.29" y1="10.62" x2="13.39" y2="28.05" gradientTransform="matrix(1,0,0,1,0,0)" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-6" x1="6.06" y1="7.36" x2="15.87" y2="36.37" gradientTransform="matrix(1,0,0,1,0,0)" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-7" x1="11.56" y1="4.94" x2="15.26" y2="39.51" gradientTransform="matrix(1,0,0,1,0,0)" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-8" x1="24.68" y1="2.06" x2="24.68" y2="55.28" gradientUnits="userSpaceOnUse">
      <stop offset=".1" stop-color="#cfd2d2"/>
      <stop offset=".22" stop-color="#5e4c8f"/>
      <stop offset=".32" stop-color="#5e4c8f"/>
      <stop offset=".42" stop-color="#897cb9"/>
      <stop offset=".46" stop-color="#9789c0"/>
      <stop offset=".51" stop-color="#a99aca"/>
      <stop offset=".54" stop-color="#9a8cc2"/>
      <stop offset=".59" stop-color="#897cb9"/>
      <stop offset=".78" stop-color="#100133"/>
    </linearGradient>
    <radialGradient id="radial-gradient" cx="-317.09" cy="-127.59" fx="-317.09" fy="-127.59" r="65.36" gradientTransform="translate(-144.58 342.28) rotate(90) scale(.93 1.32)" gradientUnits="userSpaceOnUse">
      <stop offset=".46" stop-color="#ae9770" stop-opacity="0"/>
      <stop offset=".49" stop-color="#c1ab83" stop-opacity=".24"/>
      <stop offset=".57" stop-color="#fdedc0"/>
      <stop offset=".85" stop-color="#fdedc0"/>
      <stop offset=".91" stop-color="#dcc99f" stop-opacity=".59"/>
      <stop offset=".97" stop-color="#bba57d" stop-opacity=".17"/>
      <stop offset="1" stop-color="#ae9770" stop-opacity="0"/>
    </radialGradient>
    <radialGradient id="radial-gradient-2" cx="24.82" cy="48.95" fx="24.82" fy="48.95" r="19.84" gradientUnits="userSpaceOnUse">
      <stop offset=".32" stop-color="#fff" stop-opacity="0"/>
      <stop offset=".35" stop-color="#fff" stop-opacity=".25"/>
      <stop offset=".47" stop-color="#fff"/>
      <stop offset=".51" stop-color="#fff" stop-opacity=".58"/>
      <stop offset=".56" stop-color="#fff" stop-opacity=".17"/>
      <stop offset=".58" stop-color="#fff" stop-opacity="0"/>
    </radialGradient>
    <linearGradient id="linear-gradient-9" x1="24.68" y1="48.09" x2="24.68" y2="82.22" gradientUnits="userSpaceOnUse">
      <stop offset=".08" stop-color="#a99aca" stop-opacity=".5"/>
      <stop offset=".34" stop-color="#5e4c8f" stop-opacity="0"/>
    </linearGradient>
    <radialGradient id="radial-gradient-3" cx="24.81" cy="44.02" fx="24.81" fy="44.02" r="26.09" gradientUnits="userSpaceOnUse">
      <stop offset=".37" stop-color="#fff" stop-opacity="0"/>
      <stop offset=".4" stop-color="#fff" stop-opacity=".25"/>
      <stop offset=".48" stop-color="#fff"/>
      <stop offset=".53" stop-color="#fef7e4" stop-opacity=".58"/>
      <stop offset=".57" stop-color="#fdf0ca" stop-opacity=".17"/>
      <stop offset=".59" stop-color="#fdedc0" stop-opacity="0"/>
    </radialGradient>
    <linearGradient id="linear-gradient-10" x1=".65" y1="11.04" x2="48.71" y2="11.04" gradientUnits="userSpaceOnUse">
      <stop offset=".05" stop-color="#a99aca"/>
      <stop offset=".07" stop-color="#a495c5"/>
      <stop offset=".09" stop-color="#9687b8"/>
      <stop offset=".11" stop-color="#8070a3"/>
      <stop offset=".13" stop-color="#615084"/>
      <stop offset=".15" stop-color="#39285e"/>
      <stop offset=".16" stop-color="#301e55"/>
      <stop offset=".28" stop-color="#5f4f8a"/>
      <stop offset=".4" stop-color="#897cb9"/>
      <stop offset=".41" stop-color="#8c81bc"/>
      <stop offset=".5" stop-color="#a9b0d6"/>
      <stop offset=".51" stop-color="#a5aad2"/>
      <stop offset=".6" stop-color="#897cb9"/>
      <stop offset=".72" stop-color="#594a83"/>
      <stop offset=".84" stop-color="#301e55"/>
      <stop offset=".85" stop-color="#39285e"/>
      <stop offset=".87" stop-color="#615084"/>
      <stop offset=".89" stop-color="#8070a3"/>
      <stop offset=".91" stop-color="#9687b8"/>
      <stop offset=".93" stop-color="#a495c5"/>
      <stop offset=".95" stop-color="#a99aca"/>
    </linearGradient>
    <linearGradient id="linear-gradient-11" x1=".65" y1="11.04" x2="48.71" y2="11.04" gradientUnits="userSpaceOnUse">
      <stop offset=".18" stop-color="#ae9770" stop-opacity="0"/>
      <stop offset=".43" stop-color="#d5ba82"/>
      <stop offset=".57" stop-color="#d5b981"/>
      <stop offset=".82" stop-color="#ae9770" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-12" x1=".75" y1="12.19" x2="48.61" y2="12.19" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#34215b"/>
      <stop offset=".17" stop-color="#5e4c8f"/>
      <stop offset=".5" stop-color="#7769ae"/>
      <stop offset=".83" stop-color="#5e4c8f"/>
      <stop offset="1" stop-color="#34215b"/>
    </linearGradient>
    <radialGradient id="radial-gradient-4" cx="1388.6" cy="-175.76" fx="1388.6" fy="-175.76" r="99.91" gradientTransform="translate(-146.01 -1300.1) rotate(75.74) scale(.93 -1.03)" gradientUnits="userSpaceOnUse">
      <stop offset=".12" stop-color="#cfd2d2"/>
      <stop offset=".16" stop-color="#c0c0cc"/>
      <stop offset=".29" stop-color="#988fbe"/>
      <stop offset=".35" stop-color="#897cb9"/>
      <stop offset=".41" stop-color="#71639e"/>
      <stop offset=".51" stop-color="#504078"/>
      <stop offset=".61" stop-color="#36255b"/>
      <stop offset=".71" stop-color="#231245"/>
      <stop offset=".81" stop-color="#180639"/>
      <stop offset=".9" stop-color="#150335"/>
    </radialGradient>
    <radialGradient id="radial-gradient-5" cx="4551.52" cy="-175.76" fx="4551.52" fy="-175.76" r="99.91" gradientTransform="translate(922.82 -4162.75) rotate(104.26) scale(.93 1.03)" xlink:href="#radial-gradient-4"/>
    <linearGradient id="linear-gradient-13" x1="12.06" y1="31.18" x2="17.4" y2="22.02" gradientUnits="userSpaceOnUse">
      <stop offset=".6" stop-color="#cfd2d2" stop-opacity="0"/>
      <stop offset=".68" stop-color="#948caf" stop-opacity=".52"/>
      <stop offset=".75" stop-color="#6d5d97" stop-opacity=".87"/>
      <stop offset=".78" stop-color="#5e4c8f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-14" x1="11.53" y1="69.38" x2="37.79" y2="69.79" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#100133"/>
      <stop offset=".36" stop-color="#6c5d90"/>
      <stop offset=".48" stop-color="#a091c5"/>
      <stop offset=".52" stop-color="#a091c5"/>
      <stop offset=".73" stop-color="#433467"/>
      <stop offset=".85" stop-color="#100133"/>
    </linearGradient>
    <linearGradient id="linear-gradient-15" x1="10.93" y1="65.99" x2="23.65" y2="72.31" gradientUnits="userSpaceOnUse">
      <stop offset=".1" stop-color="#fdedc0"/>
      <stop offset=".29" stop-color="#d5b981"/>
      <stop offset=".59" stop-color="#fdedc0" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-16" x1="-2942.67" y1="65.99" x2="-2929.95" y2="72.31" gradientTransform="translate(-2904.24) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-15"/>
    <linearGradient id="linear-gradient-17" x1="18.56" y1="62.26" x2="30.8" y2="62.26" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#5e4c8f"/>
      <stop offset=".18" stop-color="#645395"/>
      <stop offset=".29" stop-color="#7869a9"/>
      <stop offset=".4" stop-color="#8477b4"/>
      <stop offset=".5" stop-color="#897cb9"/>
      <stop offset=".54" stop-color="#8376b3"/>
      <stop offset=".85" stop-color="#5e4c8f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-18" x1="24.68" y1="-4.6" x2="24.68" y2="53.29" gradientUnits="userSpaceOnUse">
      <stop offset=".17" stop-color="#5e4c8f"/>
      <stop offset="1" stop-color="#261749"/>
    </linearGradient>
    <linearGradient id="linear-gradient-19" x1="24.68" y1="19.88" x2="24.68" y2="58.07" gradientUnits="userSpaceOnUse">
      <stop offset=".44" stop-color="#4c3679"/>
      <stop offset=".99" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-20" x1="5.51" y1="10.53" x2="5.51" y2="41.72" gradientUnits="userSpaceOnUse">
      <stop offset=".34" stop-color="#d5b981"/>
      <stop offset=".62" stop-color="#fdedc0"/>
      <stop offset=".8" stop-color="#d5b981"/>
      <stop offset=".94" stop-color="#ae9770"/>
    </linearGradient>
    <linearGradient id="linear-gradient-21" x1=".78" y1="38.52" x2="17.98" y2="38.52" gradientUnits="userSpaceOnUse">
      <stop offset=".02" stop-color="#fdedc0"/>
      <stop offset=".11" stop-color="#e6e0c8"/>
      <stop offset=".22" stop-color="#d5d5cf"/>
      <stop offset=".29" stop-color="#cfd2d2"/>
      <stop offset=".44" stop-color="#d5b981"/>
      <stop offset=".55" stop-color="#bfa677"/>
      <stop offset=".66" stop-color="#ae9770"/>
      <stop offset="1" stop-color="#fdedc0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-22" x1="6378.57" y1="38.52" x2="6395.78" y2="38.52" gradientTransform="translate(6427.16) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-21"/>
    <linearGradient id="linear-gradient-23" x1="8.64" y1="11.07" x2="8.64" y2="37.12" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#d5b981"/>
      <stop offset=".05" stop-color="#dfc690"/>
      <stop offset=".12" stop-color="#ecd7a5"/>
      <stop offset=".2" stop-color="#f5e3b4"/>
      <stop offset=".29" stop-color="#fbeabd"/>
      <stop offset=".44" stop-color="#fdedc0"/>
      <stop offset=".48" stop-color="#e6d6be"/>
      <stop offset=".55" stop-color="#beafbc"/>
      <stop offset=".62" stop-color="#a093bb"/>
      <stop offset=".68" stop-color="#8f82ba"/>
      <stop offset=".72" stop-color="#897cba"/>
    </linearGradient>
    <linearGradient id="linear-gradient-24" x1="9.72" y1="9.46" x2="9.72" y2="42.81" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#897cba"/>
      <stop offset=".03" stop-color="#9585b0"/>
      <stop offset=".08" stop-color="#b59f98"/>
      <stop offset=".12" stop-color="#d5b981"/>
      <stop offset=".13" stop-color="#dbc18b"/>
      <stop offset=".17" stop-color="#edd9a8"/>
      <stop offset=".2" stop-color="#f9e7b9"/>
      <stop offset=".22" stop-color="#fdedc0"/>
      <stop offset=".53" stop-color="#897cba"/>
      <stop offset=".56" stop-color="#7264a0"/>
      <stop offset=".62" stop-color="#56467f"/>
      <stop offset=".67" stop-color="#3e2e64"/>
      <stop offset=".73" stop-color="#2b1a4f"/>
      <stop offset=".8" stop-color="#1e0d40"/>
      <stop offset=".88" stop-color="#170537"/>
      <stop offset="1" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-25" x1="12.15" y1="8.03" x2="12.15" y2="58.8" xlink:href="#linear-gradient-24"/>
    <linearGradient id="linear-gradient-26" x1="15.9" y1="8.12" x2="15.9" y2="75.63" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#897cba"/>
      <stop offset=".03" stop-color="#9585b0"/>
      <stop offset=".08" stop-color="#b59f98"/>
      <stop offset=".12" stop-color="#d5b981"/>
      <stop offset=".13" stop-color="#dbc18b"/>
      <stop offset=".17" stop-color="#edd9a8"/>
      <stop offset=".2" stop-color="#f9e7b9"/>
      <stop offset=".22" stop-color="#fdedc0"/>
      <stop offset=".43" stop-color="#897cba"/>
      <stop offset=".45" stop-color="#7264a0"/>
      <stop offset=".48" stop-color="#56467f"/>
      <stop offset=".52" stop-color="#3e2e64"/>
      <stop offset=".55" stop-color="#2b1a4f"/>
      <stop offset=".59" stop-color="#1e0d40"/>
      <stop offset=".64" stop-color="#170537"/>
      <stop offset=".71" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-27" x1="6386.44" y1="11.07" x2="6386.44" y2="37.12" gradientTransform="translate(6427.16) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-23"/>
    <linearGradient id="linear-gradient-28" x1="6387.52" y1="9.46" x2="6387.52" y2="42.81" gradientTransform="translate(6427.16) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-24"/>
    <linearGradient id="linear-gradient-29" x1="6389.95" y1="8.03" x2="6389.95" y2="58.8" gradientTransform="translate(6427.16) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-24"/>
    <linearGradient id="linear-gradient-30" x1="6393.7" y1="8.12" x2="6393.7" y2="75.63" gradientTransform="translate(6427.16) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#897cba"/>
      <stop offset=".03" stop-color="#9585b0"/>
      <stop offset=".08" stop-color="#b59f98"/>
      <stop offset=".12" stop-color="#d5b981"/>
      <stop offset=".13" stop-color="#dbc18b"/>
      <stop offset=".17" stop-color="#edd9a8"/>
      <stop offset=".2" stop-color="#f9e7b9"/>
      <stop offset=".22" stop-color="#fdedc0"/>
      <stop offset=".43" stop-color="#897cba"/>
      <stop offset=".45" stop-color="#7264a0"/>
      <stop offset=".48" stop-color="#56467f"/>
      <stop offset=".52" stop-color="#3e2e64"/>
      <stop offset=".55" stop-color="#2b1a4f"/>
      <stop offset=".59" stop-color="#1e0d40"/>
      <stop offset=".64" stop-color="#170537"/>
      <stop offset=".71" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-31" x1="19.69" y1="25.46" x2="19.69" y2="48.97" gradientUnits="userSpaceOnUse">
      <stop offset=".1" stop-color="#fdedc0"/>
      <stop offset=".19" stop-color="#ecdbaf"/>
      <stop offset=".44" stop-color="#bfaa81"/>
      <stop offset=".57" stop-color="#ae9770"/>
      <stop offset=".58" stop-color="#a89172"/>
      <stop offset=".62" stop-color="#8d787c"/>
      <stop offset=".67" stop-color="#786484"/>
      <stop offset=".73" stop-color="#69568a"/>
      <stop offset=".79" stop-color="#604e8d"/>
      <stop offset=".9" stop-color="#5e4c8f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-32" x1="20.54" y1="29.75" x2="20.54" y2="63.42" gradientUnits="userSpaceOnUse">
      <stop offset=".1" stop-color="#fdedc0"/>
      <stop offset=".19" stop-color="#ecdbaf"/>
      <stop offset=".45" stop-color="#bfaa81"/>
      <stop offset=".59" stop-color="#ae9770"/>
      <stop offset=".69" stop-color="#7e695d"/>
      <stop offset=".9" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-33" x1="29.67" y1="25.46" x2="29.67" y2="48.97" xlink:href="#linear-gradient-31"/>
    <linearGradient id="linear-gradient-34" x1="28.82" y1="29.75" x2="28.82" y2="63.42" xlink:href="#linear-gradient-32"/>
    <linearGradient id="linear-gradient-35" x1="32.54" y1="62.26" x2="21.91" y2="62.26" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#897cb9"/>
      <stop offset=".66" stop-color="#3a295f"/>
      <stop offset="1" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-36" x1="6389.86" y1="31.18" x2="6395.2" y2="22.02" gradientTransform="translate(6427.16) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-13"/>
    <linearGradient id="linear-gradient-37" x1="6383.3" y1="10.53" x2="6383.3" y2="41.72" gradientTransform="translate(6427.16) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".48" stop-color="#d5b981"/>
      <stop offset=".62" stop-color="#fdedc0"/>
      <stop offset=".8" stop-color="#d5b981"/>
      <stop offset=".94" stop-color="#ae9770"/>
    </linearGradient>
    <linearGradient id="linear-gradient-38" x1="7510.02" y1="72.68" x2="7536.33" y2="72.68" gradientTransform="translate(7547.86) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#150335"/>
      <stop offset=".04" stop-color="#251448"/>
      <stop offset=".14" stop-color="#493970"/>
      <stop offset=".24" stop-color="#655690"/>
      <stop offset=".33" stop-color="#786ba6"/>
      <stop offset=".42" stop-color="#8477b4"/>
      <stop offset=".5" stop-color="#897cb9"/>
      <stop offset=".64" stop-color="#8679b6"/>
      <stop offset=".72" stop-color="#7f72ae"/>
      <stop offset=".79" stop-color="#7365a0"/>
      <stop offset=".85" stop-color="#61528b"/>
      <stop offset=".91" stop-color="#4a3a71"/>
      <stop offset=".96" stop-color="#2e1d52"/>
      <stop offset="1" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-39" x1="19.11" y1="61.1" x2="29.77" y2="67.59" gradientUnits="userSpaceOnUse">
      <stop offset=".08" stop-color="#e1ca93"/>
      <stop offset=".43" stop-color="#9688b3"/>
      <stop offset=".5" stop-color="#897cba"/>
      <stop offset=".73" stop-color="#3a295f"/>
      <stop offset=".86" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-40" x1="24.68" y1="35.96" x2="24.68" y2="61.48" xlink:href="#linear-gradient-19"/>
    <linearGradient id="linear-gradient-41" x1="21.44" y1="37.03" x2="21.44" y2="61.47" gradientUnits="userSpaceOnUse">
      <stop offset=".1" stop-color="#fdedc0"/>
      <stop offset=".4" stop-color="#e1c894"/>
      <stop offset=".57" stop-color="#d5b981"/>
      <stop offset=".59" stop-color="#c9ae82"/>
      <stop offset=".63" stop-color="#a89086"/>
      <stop offset=".69" stop-color="#8d7789"/>
      <stop offset=".75" stop-color="#78648b"/>
      <stop offset=".81" stop-color="#69568d"/>
      <stop offset=".88" stop-color="#604e8e"/>
      <stop offset="1" stop-color="#5e4c8f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-42" x1="5746.45" y1="37.03" x2="5746.45" y2="61.47" gradientTransform="translate(5774.36) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-41"/>
    <linearGradient id="linear-gradient-43" x1="24.68" y1="47.27" x2="24.68" y2="63.31" gradientUnits="userSpaceOnUse">
      <stop offset=".02" stop-color="#150335"/>
      <stop offset=".08" stop-color="#301e55"/>
      <stop offset=".22" stop-color="#3c2a64"/>
      <stop offset=".63" stop-color="#5e4c8f"/>
      <stop offset=".68" stop-color="#513f7f"/>
      <stop offset=".83" stop-color="#301e55"/>
      <stop offset=".98" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-44" x1="22.67" y1="43.51" x2="22.67" y2="58.8" xlink:href="#linear-gradient-41"/>
    <linearGradient id="linear-gradient-45" x1="-566.86" y1="43.51" x2="-566.86" y2="58.8" gradientTransform="translate(-540.18) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-41"/>
    <linearGradient id="linear-gradient-46" x1="24.68" y1="1.2" x2="24.68" y2="57.39" gradientUnits="userSpaceOnUse">
      <stop offset=".53" stop-color="#9b92c6" stop-opacity="0"/>
      <stop offset=".59" stop-color="#7f74a8" stop-opacity=".2"/>
      <stop offset=".85" stop-color="#150335"/>
    </linearGradient>
    <radialGradient id="radial-gradient-6" cx="24.71" cy="37.26" fx="24.71" fy="37.26" r="38.64" gradientTransform="translate(55.81 12.23) rotate(89.41) scale(1 .84)" gradientUnits="userSpaceOnUse">
      <stop offset=".42" stop-color="#fff" stop-opacity="0"/>
      <stop offset=".43" stop-color="#fff" stop-opacity=".25"/>
      <stop offset=".46" stop-color="#fff"/>
      <stop offset=".48" stop-color="#fff" stop-opacity=".58"/>
      <stop offset=".5" stop-color="#fff" stop-opacity=".17"/>
      <stop offset=".51" stop-color="#fff" stop-opacity="0"/>
    </radialGradient>
  </defs>
  <g>
    <g>
      <polygon class="cls-4" points="78.7 19.63 78.7 17.45 71.4 17.45 71.4 27.54 74.01 27.54 74.01 24.34 73.72 23.51 77.44 23.51 77.44 21.46 74.01 21.46 74.01 19.63 78.7 19.63"/>
      <path class="cls-4" d="M81.9,27.05c-.86-.42-1.53-1.01-1.99-1.79-.47-.78-.7-1.7-.7-2.75,0-1.05.23-1.97.7-2.75.47-.78,1.13-1.38,1.99-1.8.86-.42,1.86-.63,2.99-.63.75,0,1.41.07,2,.21.59.14,1.13.36,1.63.67v2.38c-.44-.33-.93-.57-1.48-.72-.55-.15-1.19-.23-1.9-.23-1.02,0-1.82.25-2.37.76-.56.51-.83,1.21-.83,2.11,0,.9.28,1.6.84,2.11.56.51,1.35.77,2.37.77.72,0,1.36-.08,1.92-.24.57-.16,1.09-.42,1.58-.77v2.37c-.95.62-2.19.92-3.74.92-1.13,0-2.13-.21-2.99-.62"/>
      <path class="cls-4" d="M63.65,23.51l1.3-3.4,1.29,3.4h-2.6ZM70.52,27.54l-4.24-10.1h-3.52l.35,1.24-3.72,8.86h2.77l.51-1.24-.29-.83h4.55s.85,2.07.85,2.07h2.75Z"/>
    </g>
    <g>
      <path class="cls-4" d="M85.44,44.7h-3.86v-5.66h-4.46v5.66h-3.86v-13.9h3.86v5.16h4.46v-5.16h3.86v13.9Z"/>
      <path class="cls-4" d="M62.24,44.03c-1.19-.57-2.11-1.4-2.75-2.48-.65-1.08-.97-2.34-.97-3.79,0-1.45.32-2.72.97-3.79.65-1.08,1.56-1.91,2.75-2.49,1.19-.58,2.57-.87,4.13-.87,1.03,0,1.95.1,2.76.29.81.2,1.56.5,2.25.92v3.29c-.6-.46-1.29-.79-2.05-1-.76-.21-1.64-.31-2.63-.31-1.42,0-2.51.35-3.28,1.05-.77.7-1.15,1.67-1.15,2.91,0,1.24.39,2.22,1.17,2.92.78.71,1.87,1.06,3.27,1.06.99,0,1.88-.11,2.66-.33.78-.22,1.51-.58,2.18-1.06v3.27c-1.31.85-3.03,1.28-5.17,1.28-1.57,0-2.94-.29-4.13-.86"/>
      <path class="cls-4" d="M92.38,39.15l1.8-4.67,1.78,4.67h-3.57ZM101.83,44.7l-5.84-13.89h-4.85l.49,1.71-5.12,12.19h3.8l.69-1.71-.4-1.14h6.26s1.17,2.85,1.17,2.85h3.78Z"/>
      <path class="cls-4" d="M125.69,36.99c-.39.27-.94.41-1.65.41h-2.03v-3.62h2.03c.72,0,1.28.13,1.66.39.38.26.57.73.57,1.42s-.19,1.13-.58,1.4M129.58,38.23c.48-.72.68-1.61.68-2.65,0-1.54-.47-2.72-1.4-3.54-.93-.82-2.22-1.23-3.87-1.23h-6.32v13.89h3.67v-3.19l-.39-1.15h3.14c.81,0,1.8-.14,2.56-.5.98-.47,1.65-1.22,1.93-1.63"/>
      <polygon class="cls-4" points="135.9 30.81 131.25 30.81 131.73 32.51 131.74 42.99 131.26 44.7 135.88 44.7 135.41 42.99 135.42 32.51 135.9 30.81"/>
      <path class="cls-4" d="M136.87,37.68c0-3.89,3.38-7.05,7.64-7.05s7.66,3.16,7.66,7.05-3.36,7.05-7.66,7.05-7.64-3.14-7.64-7.05ZM148.58,37.68c0-2.11-1.76-3.84-4.07-3.84s-4.03,1.73-4.03,3.84,1.68,3.82,4.03,3.82,4.07-1.73,4.07-3.82Z"/>
      <path class="cls-4" d="M101.83,30.81h3.44l4.08,5.2,4.21-5.2h3.29v13.9h-3.52v-8.81l-4,4.93-3.96-4.91v8.8h-3.54v-13.9Z"/>
      <polygon class="cls-4" points="165.92 44.6 165.91 30.81 162.24 30.81 162.24 39.02 156.9 30.81 153.27 30.81 153.28 44.6 156.95 44.6 156.95 36.37 162.31 44.6 165.92 44.6"/>
      <path class="cls-4" d="M168.17,41.76l-1.34,2.84h6.07c1.86,0,3.26-.35,4.2-1.04,1-.74,1.55-1.99,1.3-3.2-.59-2.84-4.06-3.35-6.37-4.71-.39-.23-.58-.52-.58-.89,0-.37.16-.65.54-.85.38-.2.92-.3,1.56-.3h3.33l1.03-2.81h-5.01c-1.04,0-1.96.11-2.75.44-.8.33-1.42.79-1.86,1.39-.45.6-.67,1.27-.67,2.03,0,.92.29,1.7.88,2.33.62.67,1.57,1.07,2.4,1.43,1.09.47,2.34.84,3.31,1.53.22.16.43.42.43.69,0,.31-.08.53-.29.68-.67.52-1.58.42-2.4.42h-3.76Z"/>
    </g>
    <g>
      <polygon class="cls-4" points="188.45 41.43 188.45 30.42 184.71 30.42 184.71 44.6 184.85 44.6 188.45 44.6 195 44.6 195 41.43 188.45 41.43"/>
      <polygon class="cls-4" points="196.26 44.6 206.51 44.6 206.51 41.57 199.92 41.57 199.92 38.91 204.73 38.91 204.73 36.09 199.92 36.09 199.92 33.43 206.51 33.43 206.51 30.42 196.26 30.42 196.26 44.6"/>
      <path class="cls-4" d="M212.5,38.93l1.83-4.77,1.82,4.77h-3.65ZM222.14,44.6l-5.96-14.18h-4.95l.5,1.74-5.23,12.44h3.88l.71-1.74-.4-1.16h6.39s1.2,2.91,1.2,2.91h3.86Z"/>
      <path class="cls-4" d="M241.82,44.82c1.77,0,3.78-.5,4.88-1.64,1.09-1.13,1.63-2.65,1.63-4.54v-8.22h-3.64v8.26c0,.95-.25,1.66-.76,2.15-.51.49-1.21.73-2.12.73s-1.61-.24-2.12-.73c-.51-.49-.76-1.2-.76-2.15v-8.26h-3.64v8.22c0,1.89.54,3.41,1.63,4.54,1.1,1.14,3.1,1.64,4.88,1.64"/>
      <polygon class="cls-4" points="249.75 44.6 260 44.6 260 41.57 253.41 41.57 253.41 38.91 258.22 38.91 258.22 36.09 253.41 36.09 253.41 33.43 260 33.43 260 30.42 249.75 30.42 249.75 44.6"/>
      <path class="cls-4" d="M234.36,37.89h-3.61v2.94l.83.49s-.33.13-.86.21c-.42.05-.87.08-1.33.08-1.42,0-2.52-.36-3.3-1.07-.78-.72-1.18-1.71-1.18-2.97,0-1.26.39-2.25,1.16-2.96.78-.71,1.88-1.07,3.31-1.07,1,0,1.88.11,2.66.32.77.21,1.46.55,2.07,1.02v-3.35c-.69-.42-1.45-.74-2.27-.94-.82-.2-1.75-.3-2.79-.3-1.58,0-2.97.3-4.18.89-1.2.59-2.13,1.44-2.78,2.53-.65,1.1-.98,2.38-.98,3.86,0,1.48.33,2.76.98,3.86.65,1.1,1.58,1.94,2.78,2.52,1.2.58,2.59.88,4.18.88,2.17,0,3.99-.45,5.3-1.31v-.03s0,0,0,0v-5.58Z"/>
    </g>
    <g>
      <g>
        <polygon class="cls-54" points="86.81 48.74 82.05 48.74 82.54 50.48 82.54 61.16 82.06 62.9 86.8 62.9 86.31 61.16 86.32 50.48 86.81 48.74"/>
        <polygon class="cls-54" points="74.58 59.75 74.58 48.8 70.87 48.8 70.87 62.9 71.01 62.9 74.58 62.9 81.09 62.9 81.09 59.75 74.58 59.75"/>
        <polygon class="cls-54" points="59.38 62.9 69.58 62.9 69.58 59.89 63.02 59.89 63.02 57.24 67.81 57.24 67.81 54.43 63.02 54.43 63.02 51.78 69.58 51.78 69.58 48.79 59.38 48.79 59.38 62.9"/>
        <polygon class="cls-54" points="100.47 48.74 87.87 48.74 87.87 52.02 92.26 52.02 92.26 62.9 96.15 62.9 96.15 52.02 100.47 52.02 100.47 48.74"/>
        <polygon class="cls-54" points="101.75 62.9 111.99 62.9 111.99 59.88 105.4 59.88 105.4 57.22 110.21 57.22 110.21 54.4 105.4 54.4 105.4 51.75 111.99 51.75 111.99 48.74 101.75 48.74 101.75 62.9"/>
      </g>
      <path class="cls-54" d="M116.35,51.08h-.41v-2.34h.36l.77,1.04.72-.99h.41v2.3h-.45v-1.58l-.63.86h-.09l-.68-.86v1.58ZM114.82,49.19v1.89h-.45v-1.89h-.63v-.45h1.76v.45h-.68Z"/>
    </g>
  </g>
  <g>
    <path class="cls-15" d="M32.18,53.82c.13-4.78-1.39-9.34-2.8-14.82-1.68-6.51-3.4-11.71-4.24-13.59,0,0,0,0,0,0-.25-.49-.41-.75-.46-.75s-.21.26-.46.75h0s0,0,0,0c-.84,1.88-2.56,7.08-4.24,13.59-1.41,5.47-2.93,10.04-2.8,14.82h0c.12.63.28,1.35.51,2.06.78,2.44,1.76,4.25,3.08,4.81.9.38,3.48.58,3.91.58s3-.2,3.91-.58c1.33-.55,2.3-2.37,3.08-4.81.23-.71.39-1.42.51-2.06h0ZM25.12,61.21s-.03,0-.04,0c-.13,0-.26.01-.4.01s-.27,0-.4-.01c-.01,0-.03,0-.04,0-3.55-.21-5.68-3.06-5.68-7.16,0-3.05,1.01-5.95,2.14-9.42,1.7-5.21,3.81-9.08,3.98-9.08s2.28,3.87,3.98,9.08c1.13,3.47,2.14,6.37,2.14,9.42,0,4.1-2.12,6.95-5.68,7.16ZM18.81,54.74c0-2.98,1.2-6.02,2.27-9.42,1.64-5.18,3.33-9.23,3.6-9.23s1.96,4.05,3.6,9.23c1.07,3.4,2.27,6.44,2.27,9.42,0,3.3-2.16,6.48-5.87,6.48-3.7,0-5.87-3.18-5.87-6.48ZM21.95,55.7c0-4.96,2.58-8.53,2.73-8.53s2.73,3.58,2.73,8.53c0,2.37-.65,5.53-2.73,5.53-2.08,0-2.73-3.16-2.73-5.53ZM24.68,13.02c.22,0,4.62,7.38,6.75,17.93,1.37,6.79,1.09,5.59,1.07,18.12h0c-.24-4.08-1.5-8-2.78-12.69-2.13-7.8-4.82-13.59-5.03-13.59s-2.91,5.79-5.03,13.59c-1.28,4.69-2.54,8.61-2.78,12.69h0c-.02-12.53-.3-11.34,1.07-18.12,2.13-10.55,6.53-17.93,6.75-17.93Z"/>
    <path class="cls-14" d="M36.24,27.83c.54-1.56,1.14-3.1,1.81-4.56,2.45-5.42,5.64-6.66,7.16-7.07-.03-.44-.06-.87-.09-1.29-.08-.39-.34-.76-.78-1.12-1.2.4-4.24,1.85-6.07,6.63-.8,2.1-.9,3.76-2.03,7.41Z"/>
    <path class="cls-13" d="M34.95,31.96c.37-1.35.8-2.74,1.27-4.11h0c1.13-3.67,1.23-5.33,2.03-7.43,1.83-4.78,4.87-6.23,6.07-6.63-.76-.62-2.03-1.19-3.72-1.68-.56.34-2.82,1.92-4.07,5.76-1.63,4.96-.39,8.85-1.58,14.1Z"/>
    <path class="cls-12" d="M33.81,36.85c.29-1.52.68-3.17,1.15-4.88h0c1.2-5.26-.04-9.15,1.59-14.11,1.26-3.84,3.51-5.41,4.07-5.76-1.65-.48-3.71-.88-6.06-1.18-.16.24-.93,1.46-1.25,3.09-.71,3.61.07,6.6.79,11.88.33,2.43.21,6.39-.29,10.97Z"/>
    <path class="cls-9" d="M13.13,27.83c-.54-1.56-1.14-3.1-1.81-4.56-2.45-5.42-5.64-6.66-7.16-7.07.03-.44.06-.87.09-1.29.08-.39.34-.76.78-1.12,1.2.4,4.24,1.85,6.07,6.63.8,2.1.9,3.76,2.03,7.41Z"/>
    <path class="cls-10" d="M14.41,31.96c-.37-1.35-.8-2.74-1.27-4.11h0c-1.13-3.67-1.23-5.33-2.03-7.43-1.83-4.78-4.87-6.23-6.07-6.63.76-.62,2.03-1.19,3.72-1.68.56.34,2.82,1.92,4.07,5.76,1.63,4.96.39,8.85,1.58,14.1Z"/>
    <path class="cls-8" d="M15.56,36.85c-.29-1.52-.68-3.17-1.15-4.88h0c-1.2-5.26.04-9.15-1.59-14.11-1.26-3.84-3.51-5.41-4.07-5.76,1.65-.48,3.71-.88,6.06-1.18.16.24.93,1.46,1.25,3.09.71,3.61-.07,6.6-.79,11.88-.33,2.43-.21,6.39.29,10.97Z"/>
    <path class="cls-7" d="M34.1,25.88c.13.92.19,2.05.19,3.35,0,2.16-.17,4.77-.48,7.63-.27,1.42-.46,2.71-.57,3.81-.15,1.55-.29,3.44-.41,5.21-.12,1.77-.23,3.43-.29,4.52,0-.45-.01-.89-.04-1.33.02-12.53.3-11.34-1.07-18.12-2.13-10.55-6.53-17.93-6.75-17.93s-4.62,7.38-6.75,17.93c-.95,4.7-1.1,5.57-1.1,9.83,0,1.89.03,4.44.04,8.29-.03.44-.04.89-.04,1.34-.13-2.19-.41-6.63-.7-9.73-.1-1.1-.29-2.39-.57-3.81h0c-.5-4.58-.62-8.55-.29-10.98.72-5.28,1.5-8.27.79-11.88-.32-1.63-1.1-2.85-1.25-3.09,2.9-.37,6.25-.58,9.88-.58s6.98.21,9.88.58c-.16.24-.93,1.46-1.25,3.09-.71,3.61.07,6.6.79,11.88Z"/>
    <path class="cls-16" d="M33.8,36.86c-.27,1.42-.46,2.71-.57,3.81-.15,1.55-.29,3.44-.41,5.21-.12,1.77-.23,3.43-.29,4.52-.01.1-.02.2-.03.28,0,.11,0,.21,0,.31-.03.46-.04.73-.04.73,0,0-.12,2.09-.78,4.13-.78,2.44-1.76,4.25-3.08,4.8-.9.38-3.48.58-3.91.58s-3-.2-3.91-.58c-1.33-.55-2.3-2.37-3.08-4.8-.66-2.05-.78-4.13-.78-4.13,0,0,0-.07-.01-.19,0-.13-.02-.31-.03-.54,0-.12,0-.24,0-.36-.01-.07-.02-.15-.03-.23-.13-2.19-.41-6.63-.7-9.73-.1-1.1-.3-2.4-.57-3.81h0s0,0,0,0c-.29-1.52-.68-3.17-1.15-4.88h0s0-.01,0-.02c-.37-1.35-.8-2.73-1.27-4.1h0s0-.02,0-.03c-.54-1.56-1.14-3.1-1.8-4.56-2.45-5.42-5.64-6.66-7.16-7.07.03-.44.06-.87.09-1.29.08-.39.34-.76.78-1.12.76-.62,2.03-1.19,3.72-1.68,1.65-.48,3.71-.88,6.06-1.18,2.9-.37,6.25-.58,9.88-.58s6.98.21,9.88.58c2.35.3,4.4.7,6.06,1.18,1.69.49,2.96,1.06,3.72,1.68.44.36.7.73.78,1.12.03.42.06.85.09,1.29-1.51.41-4.71,1.65-7.16,7.07-.66,1.46-1.26,3-1.8,4.56,0,0,0,.01,0,.02h0c-.47,1.38-.9,2.76-1.27,4.11,0,0,0,0,0,.01h0c-.47,1.71-.85,3.37-1.15,4.89h0Z"/>
    <path class="cls-2" d="M21.95,55.7c0-4.96,2.58-8.53,2.73-8.53s2.73,3.58,2.73,8.53c0,2.37-.65,5.53-2.73,5.53-2.08,0-2.73-3.16-2.73-5.53Z"/>
    <path class="cls-11" d="M21.95,55.7c0-4.96,2.58-8.53,2.73-8.53s2.73,3.58,2.73,8.53c0,2.37-.65,5.53-2.73,5.53-2.08,0-2.73-3.16-2.73-5.53Z"/>
    <path class="cls-3" d="M28.28,45.32c-1.64-5.18-3.33-9.23-3.6-9.23s-1.96,4.05-3.6,9.23c-1.07,3.4-2.27,6.44-2.27,9.42,0,3.3,2.16,6.48,5.87,6.48s5.87-3.18,5.87-6.48c0-2.98-1.2-6.02-2.27-9.42ZM27.87,58.51c-.51,1.46-1.46,2.72-3.19,2.72s-2.66-1.24-3.18-2.68c-.62-.94-.87-2.48-.84-4.23.07-4.01,3.84-7.34,4.01-7.45h0s.03.03.07.06c0,0,0,0,0,0,.55.47,3.87,3.64,3.94,7.39.03,1.72-.22,3.25-.81,4.19Z"/>
    <path class="cls-27" d="M24.68,9.47c13.18,0,23.93,2.43,23.93,5.44l.1-2.29c0-3.01-10.79-5.46-24.03-5.46C11.45,7.16.65,9.61.65,12.62l.1,2.29c0-3,10.75-5.44,23.93-5.44Z"/>
    <path class="cls-17" d="M24.68,9.47c13.18,0,23.93,2.43,23.93,5.44l.1-2.29c0-3.01-10.79-5.46-24.03-5.46C11.45,7.16.65,9.61.65,12.62l.1,2.29c0-3,10.75-5.44,23.93-5.44Z"/>
    <path class="cls-28" d="M24.68,10.34c11.28,0,19.93,2.02,20.43,4.56h3.5c0-3-10.75-5.44-23.93-5.44C11.5,9.47.75,11.91.75,14.91h3.5c.5-2.54,9.16-4.56,20.43-4.56Z"/>
    <path class="cls-6" d="M17.98,56.72c-.5-.53-2.31-2.48-5.1-5.75-3.07-3.6-6.52-9.39-9.06-15.91C1.71,29.61.39,22.24.75,14.91h3.5c-.36,5.45-.92,12.03,1.24,19.22,2.16,7.19,9.62,14.95,11.38,16.7,0,.06,0,.12,0,.18.03.46.04.73.04.73,0,0,.12,2.09.78,4.13.09.29.19.57.29.85Z"/>
    <path class="cls-5" d="M31.38,56.72c.5-.53,2.31-2.48,5.1-5.75,3.07-3.6,6.52-9.39,9.06-15.91,2.12-5.44,3.44-12.81,3.08-20.14h-3.5c.36,5.45.92,12.03-1.24,19.22-2.16,7.19-9.62,14.95-11.38,16.7,0,.06,0,.12,0,.18-.03.46-.04.73-.04.73,0,0-.12,2.09-.78,4.13-.09.29-.19.57-.29.85Z"/>
    <path class="cls-29" d="M17.98,56.72c-.5-.53-2.31-2.48-5.1-5.75-3.07-3.6-6.52-9.39-9.06-15.91C1.71,29.61.39,22.24.75,14.91h3.5c-.36,5.45-.92,12.03,1.24,19.22,2.16,7.19,9.62,14.95,11.38,16.7,0,.06,0,.12,0,.18.03.46.04.73.04.73,0,0,.12,2.09.78,4.13.09.29.19.57.29.85Z"/>
    <path class="cls-31" d="M24.68,73.06h13.15s0-.63,0-.63c0,0,0-.12-.15-.12h-.33s-2.94-5.85-2.94-5.85c0,0-.19-.41-.42-.41s-9.32,0-9.32,0h-9.32c-.23,0-.42.41-.42.41l-2.94,5.85h-.33c-.14,0-.15.12-.15.12v.63h13.16Z"/>
    <path class="cls-42" d="M24.68,66.05h-9.32c-.23,0-.42.41-.42.41l-2.94,5.85h-.33c-.14,0-.15.12-.15.12v.63h13.16v-7.01Z"/>
    <path class="cls-43" d="M24.68,66.05h0s9.09,0,9.32,0,.42.41.42.41l2.94,5.85h.33c.14,0,.15.12.15.12v.63h-13.15s0,0,0,0v-7.01Z"/>
    <path class="cls-44" d="M30.8,64.01h-12.24l1.88-3.5c.11.06.22.12.33.17.9.38,3.48.58,3.91.58s3-.2,3.91-.58c.11-.05.22-.1.33-.17l1.88,3.5Z"/>
    <path class="cls-45" d="M24.68,13.02c.22,0,4.62,7.38,6.75,17.93.71,3.51.97,4.89,1.06,7.13h0c.22-1.71.01-5.4-.49-7.75-2.31-10.89-7.09-18.51-7.33-18.51s-5.02,7.62-7.33,18.51c-.5,2.36-.7,6.04-.49,7.75h0c.09-2.24.36-3.62,1.06-7.13,2.13-10.55,6.53-17.93,6.75-17.93"/>
    <path class="cls-30" d="M24.68,22.8c.22,0,2.91,5.79,5.03,13.59,1.42,5.2,2.82,9.45,2.82,14.02-.01.22-.03.42-.04.59h0c0-4.35-1.43-8.47-2.85-13.42-2.13-7.42-4.75-12.93-4.97-12.93s-2.84,5.51-4.97,12.93c-1.42,4.95-2.85,9.07-2.85,13.42h0c-.01-.17-.02-.37-.04-.59,0-4.57,1.4-8.82,2.82-14.02,2.13-7.8,4.82-13.59,5.03-13.59Z"/>
    <path class="cls-33" d="M5.49,34.13c.5,1.67,1.29,3.37,2.23,5.02-1.09-1.58-2.07-3.48-2.76-5.79-2.04-6.81-1.75-13.1-1.4-18.45h.7c-.36,5.45-.92,12.03,1.24,19.22Z"/>
    <path class="cls-32" d="M17.98,56.72c-.5-.53-2.31-2.48-5.1-5.75-3.07-3.6-6.52-9.39-9.06-15.91-1.59-4.1-2.74-9.29-3.05-14.73.47,5.15,1.63,10.01,3.18,13.91,2.69,6.78,6.34,12.8,9.56,16.53,1.7,1.96,3.05,3.47,3.99,4.49.06.2.12.41.18.62.09.29.19.57.29.85Z"/>
    <path class="cls-34" d="M31.38,56.72c.5-.53,2.31-2.48,5.1-5.75,3.07-3.6,6.52-9.39,9.06-15.91,1.59-4.1,2.74-9.29,3.05-14.73-.47,5.15-1.63,10.01-3.18,13.91-2.69,6.78-6.34,12.8-9.56,16.53-1.7,1.96-3.05,3.47-3.99,4.49-.06.2-.12.41-.18.62-.09.29-.19.57-.29.85Z"/>
    <path class="cls-35" d="M13.13,27.83c-.54-1.56-1.14-3.1-1.81-4.56-2.45-5.42-5.64-6.66-7.16-7.07.01-.18.02-.36.04-.54,1.63.52,4.98,1.96,7.27,7.51.53,1.29,1.02,2.63,1.46,3.99,0,0,0,.01,0,.02.06.21.13.42.19.64Z"/>
    <path class="cls-50" d="M14.41,31.96c-.37-1.35-.8-2.74-1.27-4.11h0c-1.13-3.67-1.23-5.33-2.03-7.43-1.83-4.78-4.87-6.23-6.07-6.63.13-.11.28-.22.45-.33,1.6.67,4.32,2.28,5.9,6.42.84,2.2.94,3.94,2.13,7.77h0c.15.44.29.87.43,1.29.09.96.23,1.96.47,3.02Z"/>
    <path class="cls-37" d="M15.56,36.85c-.29-1.52-.68-3.17-1.15-4.88h0c-1.2-5.26.04-9.15-1.59-14.11-1.26-3.84-3.51-5.41-4.07-5.76.45-.13.93-.26,1.43-.37.94.96,2.1,2.53,2.9,4.94,1.73,5.27.73,9.66,1.68,14.99h0c.18.64.34,1.27.49,1.89.08,1.05.18,2.16.3,3.3Z"/>
    <path class="cls-51" d="M15.8,25.76c-.37,2.43-.29,6.39.14,10.98h0c.25,1.42.43,2.72.52,3.82.25,3.11.27,7.66.37,9.85-.13-2.19-.41-6.63-.7-9.73-.1-1.1-.3-2.39-.57-3.81h0c-.5-4.58-.62-8.55-.29-10.98.72-5.28,1.5-8.27.79-11.88-.32-1.63-1.1-2.85-1.25-3.09.25-.03.5-.06.76-.09.2.31.9,1.51,1.19,3.07.66,3.62-.16,6.59-.95,11.86Z"/>
    <path class="cls-49" d="M36.24,27.83c.54-1.56,1.14-3.1,1.81-4.56,2.45-5.42,5.64-6.66,7.16-7.07-.01-.18-.02-.36-.04-.54-1.63.52-4.98,1.96-7.27,7.51-.53,1.29-1.02,2.63-1.46,3.99,0,0,0,.01,0,.02-.06.21-.13.42-.19.64Z"/>
    <path class="cls-36" d="M34.95,31.96c.37-1.35.8-2.74,1.27-4.11h0c1.13-3.67,1.23-5.33,2.03-7.43,1.83-4.78,4.87-6.23,6.07-6.63-.13-.11-.28-.22-.45-.33-1.6.67-4.32,2.28-5.9,6.42-.84,2.2-.94,3.94-2.13,7.77h0c-.15.44-.29.87-.43,1.29-.09.96-.23,1.96-.47,3.02Z"/>
    <path class="cls-52" d="M33.81,36.85c.29-1.52.68-3.17,1.15-4.88h0c1.2-5.26-.04-9.15,1.59-14.11,1.26-3.84,3.51-5.41,4.07-5.76-.45-.13-.93-.26-1.43-.37-.94.96-2.1,2.53-2.9,4.94-1.73,5.27-.73,9.66-1.68,14.99h0c-.18.64-.34,1.27-.49,1.89-.08,1.05-.18,2.16-.3,3.3Z"/>
    <path class="cls-41" d="M33.56,25.76c.37,2.43.29,6.39-.14,10.98h0c-.25,1.42-.43,2.72-.52,3.82-.25,3.11-.27,7.66-.37,9.85.13-2.19.41-6.63.7-9.73.1-1.1.3-2.39.57-3.81h0c.5-4.58.62-8.55.29-10.98-.72-5.28-1.5-8.27-.79-11.88.32-1.63,1.1-2.85,1.25-3.09-.25-.03-.5-.06-.76-.09-.2.31-.9,1.51-1.19,3.07-.66,3.62.16,6.59.95,11.86Z"/>
    <path class="cls-39" d="M22.55,16.87c-1.22,2.87-3.06,8.07-4.48,15.14-.71,3.55-1.09,5.8-1.23,7.78.03-3.43.23-4.52,1.1-8.84,1.2-5.96,3.13-10.9,4.62-14.08Z"/>
    <path class="cls-38" d="M19.98,39c-1.41,5.47-2.93,10.04-2.8,14.82h0c-.22-1.19-.27-2.08-.27-2.08,0,0-.01-.27-.04-.73,0-4.35,1.43-8.47,2.85-13.42,1.66-5.8,3.63-10.43,4.51-12.18-.84,1.88-2.56,7.08-4.24,13.59Z"/>
    <path class="cls-40" d="M26.81,16.87c1.22,2.87,3.06,8.07,4.48,15.14.71,3.55,1.09,5.8,1.23,7.78-.03-3.43-.23-4.52-1.1-8.84-1.2-5.96-3.13-10.9-4.62-14.08Z"/>
    <path class="cls-47" d="M29.38,39c1.41,5.47,2.93,10.04,2.8,14.82h0c.22-1.19.27-2.08.27-2.08,0,0,.01-.27.04-.73,0-4.35-1.43-8.47-2.85-13.42-1.66-5.8-3.63-10.43-4.51-12.18.84,1.88,2.56,7.08,4.24,13.59Z"/>
    <path class="cls-46" d="M24.68,61.26c.42,0,3-.2,3.91-.58.11-.05.22-.1.33-.17l1.88,3.5h-6.12v-2.75Z"/>
    <path class="cls-48" d="M31.38,56.72c.5-.53,2.31-2.48,5.1-5.75,3.07-3.6,6.52-9.39,9.06-15.91,2.12-5.44,3.44-12.81,3.08-20.14h-3.5c.36,5.45.92,12.03-1.24,19.22-2.16,7.19-9.62,14.95-11.38,16.7,0,.06,0,.12,0,.18-.03.46-.04.73-.04.73,0,0-.12,2.09-.78,4.13-.09.29-.19.57-.29.85Z"/>
    <path class="cls-53" d="M43.88,34.13c-.5,1.67-1.29,3.37-2.23,5.02,1.09-1.58,2.07-3.48,2.76-5.79,2.04-6.81,1.75-13.1,1.4-18.45h-.7c.36,5.45.92,12.03-1.24,19.22Z"/>
    <path class="cls-19" d="M12,72.31h-.33c-.14,0-.15.12-.15.12v.63h26.31s0-.63,0-.63c0,0,0-.12-.15-.12h-.33s-25.36,0-25.36,0Z"/>
    <path class="cls-18" d="M24.68,61.26c-.42,0-3-.2-3.91-.58-.11-.05-.22-.1-.33-.17l-1.88,3.5h6.12v-2.75Z"/>
    <path class="cls-21" d="M25.08,61.21c3.46-.22,5.47-3.29,5.47-6.47,0-2.98-1.2-6.02-2.27-9.42-1.64-5.18-3.33-9.23-3.6-9.23s-1.96,4.05-3.6,9.23c-1.07,3.4-2.27,6.44-2.27,9.42,0,3.18,2.01,6.25,5.47,6.47-3.58-.2-5.72-3.05-5.72-7.16,0-3.05,1.01-5.95,2.14-9.42,1.7-5.21,3.81-9.08,3.98-9.08s2.28,3.87,3.98,9.08c1.13,3.47,2.14,6.37,2.14,9.42,0,4.12-2.14,6.97-5.72,7.16Z"/>
    <path class="cls-22" d="M24.33,36.63c-.64,1.26-1.96,4.63-3.24,8.7-1.07,3.4-2.27,6.44-2.27,9.42,0,3.18,2.01,6.25,5.47,6.47-3.58-.2-5.72-3.05-5.72-7.16,0-3.05,1.01-5.95,2.14-9.42.95-2.89,3.63-8,3.63-8Z"/>
    <path class="cls-23" d="M25.03,36.63c.64,1.26,1.96,4.63,3.24,8.7,1.07,3.4,2.27,6.44,2.27,9.42,0,3.18-2.01,6.25-5.47,6.47,3.58-.2,5.72-3.05,5.72-7.16,0-3.05-1.01-5.95-2.14-9.42-.95-2.89-3.63-8-3.63-8Z"/>
    <path class="cls-20" d="M28.41,55.07c0,1.89-.55,6.15-3.73,6.15,2.08,0,2.73-3.16,2.73-5.53,0-4.96-2.58-8.53-2.73-8.53s-2.73,3.58-2.73,8.53c0,2.37.65,5.53,2.73,5.53-3.18,0-3.73-4.27-3.73-6.15,0-4.3,3.68-8.21,3.73-8.21s3.73,3.9,3.73,8.21Z"/>
    <path class="cls-25" d="M20.95,55.07c0,.87.12,2.25.56,3.48-.62-.94-.87-2.48-.84-4.23.07-4.01,3.84-7.34,4.01-7.45-.17.13-3.73,3.97-3.73,8.2Z"/>
    <path class="cls-24" d="M28.4,55.07c0,.87-.12,2.25-.56,3.48.62-.94.87-2.48.84-4.23-.07-4.01-3.84-7.34-4.01-7.45.17.13,3.73,3.97,3.73,8.2Z"/>
    <path class="cls-26" d="M24.68,13.02c.22,0,4.62,7.38,6.75,17.93,1.37,6.79,1.09,5.59,1.07,18.12h0c-.24-4.08-1.5-8-2.78-12.69-2.13-7.8-4.82-13.59-5.03-13.59s-2.91,5.79-5.03,13.59c-1.28,4.69-2.54,8.61-2.78,12.69h0c-.02-12.53-.3-11.34,1.07-18.12,2.13-10.55,6.53-17.93,6.75-17.93Z"/>
    <path class="cls-1" d="M32.18,53.82c.13-4.78-1.39-9.34-2.8-14.82-1.68-6.51-3.4-11.71-4.24-13.59,0,0,0,0,0,0-.25-.49-.41-.75-.46-.75s-.21.26-.46.75h0s0,0,0,0c-.84,1.88-2.56,7.08-4.24,13.59-1.41,5.47-2.93,10.04-2.8,14.82h0c.12.63.28,1.35.51,2.06.78,2.44,1.76,4.25,3.08,4.81.9.38,3.48.58,3.91.58s3-.2,3.91-.58c1.33-.55,2.3-2.37,3.08-4.81.23-.71.39-1.42.51-2.06h0ZM25.12,61.21s-.03,0-.04,0c-.13,0-.26.01-.4.01s-.27,0-.4-.01c-.01,0-.03,0-.04,0-3.55-.21-5.68-3.06-5.68-7.16,0-3.05,1.01-5.95,2.14-9.42,1.7-5.21,3.81-9.08,3.98-9.08s2.28,3.87,3.98,9.08c1.13,3.47,2.14,6.37,2.14,9.42,0,4.1-2.12,6.95-5.68,7.16Z"/>
  </g>
</svg>
