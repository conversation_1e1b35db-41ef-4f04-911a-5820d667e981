@import 'setup/variable';
@import 'setup/mixin';

.score-board {
  position: relative;
  border-bottom: 1px solid var(--color-earth-2);
  border-top: 1px solid var(--color-earth-2);
  display: flex;
  align-items: center;
  flex: auto;

  .score-board__team {
    flex: 0 0 auto;
    width: 50%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 96px;

    .team-info {
      display: flex;
      align-items: center;

      .team-name {
        color: var(--color-black);
        font-family: var(--font-jleague-kick);
        font-style: normal;
        font-weight: 700;
        font-size: 30px;
        line-height: 36px;
        @include text_ellipsis();
      }

      .club-emblem {
        flex: 0 0 auto;
        width: 64px;
        height: 64px;
      }

      &:hover {
        opacity: 1;

        &[href] {
          .team-name {
            text-decoration: underline;
          }
        }
      }
    }

    &.score-board__team--home {
      padding-right: 50px;

      .team-info {
        .team-name {
          text-align: right;
        }

        .club-emblem {
          margin-left: 8px;
        }
      }
    }

    &.score-board__team--away {
      flex-direction: row-reverse;
      padding-left: 50px;

      .team-info {
        flex-direction: row-reverse;

        .club-emblem {
          margin-right: 8px;
        }
      }
    }
  }

  .score-board__score {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: var(--color-black);
    border-radius: 48px;
    display: inline-flex;
    align-items: center;
    width: 84px;
    height: 40px;

    .score-board__score__away,
    .score-board__score__home,
    .score-board__score__divider {
      color: var(--color-white);
      font-family: var(--font-jleague-kick);
      font-style: normal;
      font-weight: 700;
      font-size: 30px;
      line-height: 30px;
    }

    .score-board__score__away,
    .score-board__score__home {
      flex: 1 1;
    }

    .score-board__score__home {
      text-align: right;
    }

    .score-board__score__away {
      text-align: left;
    }

    .score-board__score__divider {
      margin: 0 8px;
    }
  }

  &.score-board--no-border {
    border: none;
  }

  &.score-board--white-text {
    .score-board__team {
      .team-info {
        .team-name {
          color: var(--color-white);
        }
      }
    }

    .score-board__score {
      .score-board__score__away,
      .score-board__score__home,
      .score-board__score__divider {
        color: var(--color-white);
      }
    }
  }

  &.score-board--video-item {
    .score-board__team {
      height: 32px;

      .team-info {
        .team-name {
          font-size: 16px;
          line-height: 16px;
        }

        .club-emblem {
          height: 32px;
          width: 32px;
        }
      }

      &.score-board__team--home {
        padding-right: 30px;

        .team-info {
          .club-emblem {
            margin-left: 4px;
          }
        }
      }

      &.score-board__team--away {
        padding-left: 30px;

        .team-info {
          .club-emblem {
            margin-right: 4px;
          }
        }
      }
    }

    .score-board__score {
      height: 32px;
      width: 54px;

      .score-board__score__away,
      .score-board__score__home {
        font-size: 24px;
        line-height: 24px;
      }

      .score-board__score__divider {
        margin: 0 3px;
      }
    }
  }

  &.score-board--video-modal {
    .score-board__team {
      height: 32px;

      .team-name {
        font-size: 20px;
        line-height: 24px;
      }

      .club-emblem {
        height: 40px;
        width: 40px;
      }

      &.score-board__team--home {
        padding-right: 30px;

        .club-emblem {
          margin-left: 4px;
        }
      }

      &.score-board__team--away {
        padding-left: 30px;

        .club-emblem {
          margin-right: 4px;
        }
      }
    }

    .score-board__score {
      height: 32px;
      width: 54px;

      .score-board__score__away,
      .score-board__score__home {
        font-size: 24px;
        line-height: 24px;
      }

      .score-board__score__divider {
        margin: 0 3px;
      }
    }
  }
}

@media screen and (max-width: 1199px) {
  .score-board {
    .score-board__team {
      height: 72px;

      .team-info {
        .team-name {
          font-size: 24px;
          line-height: 24px;
        }

        .club-emblem {
          width: 40px;
          height: 40px;
        }
      }

      &.score-board__team--home {
        padding-right: 32px;
      }

      &.score-board__team--away {
        padding-left: 32px;
      }
    }

    .score-board__score {
      width: 54px;
      height: 32px;

      .score-board__score__away,
      .score-board__score__home {
        font-size: 24px;
        line-height: 24px;
      }

      .score-board__score__divider {
        margin: 0 3px;
      }
    }
  }
}

@media screen and (max-width: 767px) {
  .score-board {
    .score-board__team {
      .team-info {
        .team-name {
          font-size: 20px;
        }
      }
    }
  }
}

@media screen and (max-width: 576px) {
  .score-board {
    .score-board__score {
      width: 48px;
      margin: 0;

      .score-board__score__away,
      .score-board__score__home {
        font-size: 20px;
      }
    }
  }
}

// body.lang-th .score-board {
//   .team-info .team-name {
//     @include font-thai();
//   }
// }

:root {
  body.lang-th {
    .score-board .score-board__score {
      --font-jleague-kick: #{$font-jleague-kick};
    }
  }
}
