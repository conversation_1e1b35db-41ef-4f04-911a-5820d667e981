# Generated by Django 3.1.7 on 2021-04-27 00:20

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ('data_stadium', '0006_auto_20210424_2025'),
    ]

    operations = [
        migrations.CreateModel(
            name='PlayerGameParticipation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField()),
                ('goals_count', models.IntegerField(default=0)),
                ('shots_count', models.IntegerField(default=0)),
                ('saves_count', models.IntegerField(default=0)),
                ('assists_count', models.IntegerField(default=0)),
                ('game_time', models.IntegerField(default=0)),
                ('start_f', models.IntegerField(default=0)),
                ('entry_f', models.IntegerField(default=0)),
                ('yellow_cards', models.IntegerField(default=0)),
                ('red_cards', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.AddIndex(
            model_name='game',
            index=models.Index(fields=['home_team_id', 'game_full_date'], name='data_stadiu_home_te_4197d7_idx'),
        ),
        migrations.AddIndex(
            model_name='game',
            index=models.Index(fields=['away_team_id', 'game_full_date'], name='data_stadiu_away_te_20bfa0_idx'),
        ),
        migrations.AddField(
            model_name='playergameparticipation',
            name='game',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='player_game_participations',
                to='data_stadium.game',
            ),
        ),
        migrations.AddField(
            model_name='playergameparticipation',
            name='player',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_stadium.player'),
        ),
        migrations.AddConstraint(
            model_name='playergameparticipation',
            constraint=models.UniqueConstraint(
                fields=('year', 'game_id', 'player_id'), name='player_game_participation_uniq'
            ),
        ),
    ]
