@import 'setup/variable';
@import 'setup/mixin';

.body-club-profile {
  .club-profile-mascots {
    background-attachment: fixed;
    position: relative;

    // .club-profile-mascots__content {
    // }

    .mascot-item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .mascot-image {
        object-fit: cover;
        border-radius: 2px;
        width: 494px;
        height: 640px;
      }

      .info-container {
        background: $color-white;
        border-radius: 2px;
        font-family: $font-overpass;
        font-style: normal;
        font-weight: 900;

        .info-title {
          margin-bottom: 24px;
          color: $color-black;
          font-size: 28px;
          line-height: 32px;
        }

        .mascot-name {
          color: $color-black;
          text-transform: uppercase;
        }

        .mascot-description {
          color: $color-black;
          font-weight: normal;
          font-size: 20px;
          line-height: 32px;
          margin-top: 24px;
        }
      }
    }

    @media screen and (min-width: 1200px) {
      .mascot-item {
        .info-container {
          .mascot-name {
            font-size: 48px;
            line-height: 56px;
          }

          .mascot-description {
            line-height: 28px;
          }
        }
      }
    }

    @media screen and (max-width: 1199px) {
      .mascot-item {
        .info-container {
          .info-title {
            display: none;
          }
        }
      }
    }

    @media screen and (max-width: 1199px) and (min-width: 768px) {
      .mascot-item {
        .mascot-image {
          width: 435px;
          height: 563px;
        }

        .info-container {
          .mascot-name {
            font-size: 38px;
            line-height: 38px;
          }
        }
      }
    }

    @media screen and (min-width: 768px) {
      padding: 80px 0;

      .mascot-item {
        .info-container {
          margin-top: 40px;
          width: 580px;
          padding: 48px;
        }
      }
    }

    @media screen and (max-width: 767px) {
      padding: 40px 24px;

      .mascot-item {
        .mascot-image {
          width: 347px;
          height: 450px;
        }

        .info-container {
          margin-top: 32px;
          padding: 48px 44px;

          .mascot-name {
            font-size: 30px;
            line-height: 31px;
          }

          .mascot-description {
            margin-top: 14px;
          }
        }
      }
    }

    @media screen and (max-width: 450px) {
      .mascot-item {
        .mascot-image {
          width: 100%;
          height: auto;
        }
      }
    }
  }
}

body.lang-th {
  .club-profile-mascots .mascot-item .info-container {
    .info-title,
    .mascot-name,
    .mascot-description {
      @include font-thai();
    }
  }
}

body.lang-vi {
  .club-profile-mascots .mascot-item .info-container {
    .info-title,
    .mascot-name,
    .mascot-description {
      @include font-vietnamese();
    }

    .info-title,
    .mascot-name {
      font-weight: 600;
    }
  }
}
