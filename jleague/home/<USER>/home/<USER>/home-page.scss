@import 'setup/variable';
@import 'setup/mixin';

.template-homepage {
  header {
    overflow: hidden;
    @media screen and (max-width: 425px) {
      padding: 0;
    }
  }

  .content-container {
    margin-top: 48px;

    .content-container__body {
      display: grid;
      width: 100%;
    }
  }

  // .home__content-menu {
  //   display: flex;
  //   flex-direction: row;
  //   padding: 0;
  //   margin: 0;

  //   .home__content-menu__button {
  //     font-family: $font-overpass;
  //     font-size: 20px;
  //     font-style: normal;
  //     font-weight: 700;
  //     line-height: 32px;
  //     letter-spacing: 0px;
  //     text-align: center;
  //     padding: 8px 24px;
  //     background: $color-white;
  //     border: none;
  //     border-bottom: solid 8px $color-earth-2;

  //     &:hover:not(.active) {
  //       border-color: $color-red;
  //       color: $color-red;
  //       cursor: pointer;
  //     }

  //     &.active {
  //       border-color: $color-red;
  //       color: $color-red;
  //     }
  //   }
  // }

  // .home__content-latest {
  //   margin-right: 64px;
  // }

  // .home__content-matches {
  // }

  .goal-highlights-container {
    background: var(--color-black);

    .goal-highlights-content {
      // max-width: 1920px;
      overflow: hidden;
      margin: 0 auto;
    }
  }

  // @media screen and (min-width: 992px) {
  //   .content-container {
  //     .content-container__header {
  //       .home__content-menu {
  //         display: none;
  //       }
  //     }

  //     // .content-container__body {
  //     //   grid-template-columns: auto 400px;
  //     // }
  //   }
  // }

  // @media screen and (max-width: 991px) {
  //   .content-container {
  //     .content-container__header {
  //       .home__content-menu {
  //         display: flex;
  //         justify-content: center;
  //         margin-top: 32px;
  //       }
  //     }

  //     // .content-container__body {
  //     //   grid-template-columns: 100%;
  //     // }
  //   }

  //   .home__content-latest {
  //     margin-right: 0;
  //   }

  //   .home__content-matches {
  //     display: none;
  //   }
  // }

  @media screen and (max-width: 767px) {
    .content-container {
      margin-top: 40px;
    }

    .header-banner {
      margin-bottom: 40px;
    }
  }

  @media screen and (max-width: 425px) {
    .content-container {
      margin-top: 0px;
      margin-bottom: 24px;
    }
  }
}

// body.lang-th {
//   .home__content-menu .home__content-menu__button {
//     @include font-thai();
//   }
// }

// @import 'components/home_page_popup';
@import 'components/home_page_latest_news';
@import 'components/hero_section';
@import 'components/home_cta';
