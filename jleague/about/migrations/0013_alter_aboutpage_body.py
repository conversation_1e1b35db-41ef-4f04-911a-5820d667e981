# Generated by Django 4.1.4 on 2023-01-11 08:51

from django.db import migrations
import wagtail.blocks
import wagtail.contrib.table_block.blocks
import wagtail.embeds.blocks
import wagtail.fields
import wagtail.images.blocks


class Migration(migrations.Migration):
    dependencies = [
        ("about", "0012_alter_aboutpage_body"),
    ]

    operations = [
        migrations.AlterField(
            model_name="aboutpage",
            name="body",
            field=wagtail.fields.StreamField(
                [
                    ("richtext", wagtail.blocks.RichTextBlock()),
                    ("embed", wagtail.embeds.blocks.EmbedBlock()),
                    ("image", wagtail.images.blocks.ImageChooserBlock()),
                    (
                        "table",
                        wagtail.contrib.table_block.blocks.TableBlock(
                            table_options={
                                "contextMenu": [
                                    "row_above",
                                    "row_below",
                                    "---------",
                                    "col_left",
                                    "col_right",
                                    "---------",
                                    "remove_row",
                                    "remove_col",
                                    "---------",
                                    "undo",
                                    "redo",
                                    "---------",
                                    "copy",
                                    "cut",
                                    "---------",
                                    "alignment",
                                ]
                            }
                        ),
                    ),
                    (
                        "sectionheader",
                        wagtail.blocks.StructBlock([("title", wagtail.blocks.CharBlock())]),
                    ),
                    ("html", wagtail.blocks.RawHTMLBlock()),
                ],
                use_json_field=True,
            ),
        ),
    ]
