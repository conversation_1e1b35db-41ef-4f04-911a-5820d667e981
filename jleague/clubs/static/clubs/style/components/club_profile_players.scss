@import 'setup/variable';
@import 'setup/mixin';

.body-club-profile {
  .club-profile-players {
    .section-header {
      margin-top: 40px;
    }

    .club-profile-players__filters {
      display: flex;
      justify-content: center;
      margin-top: 40px;
    }

    .club-profile-players__content {
      margin-top: 40px;

      &.loading {
        min-height: 1800px;
      }

      @media screen and (max-width: 1199px) {
        margin-top: 32px;
      }

      @media screen and (max-width: 767px) {
        margin-top: 24px;
      }
    }

    .players-item {
      padding-top: 24px;
      border-top: 1px solid $color-earth-4;

      .label {
        color: $color-black;
        font-family: $font-overpass;
        font-style: normal;
        font-weight: 900;
        font-size: 28px;
        line-height: 32px;

        @media screen and (max-width: 1199px) {
          font-size: 24px;
          line-height: 28px;
        }

        @media screen and (max-width: 767px) {
          font-size: 20px;
          line-height: 24px;
        }
      }

      .player-list {
        display: flex;

        @media screen and (min-width: 1200px) {
          margin-top: 24px;
          flex-wrap: wrap;
          justify-content: space-between;
        }

        @media screen and (max-width: 1199px) {
          margin-top: 32px;
          flex-direction: column;
          align-items: center;

          .player-item:last-child {
            border-bottom: none;
          }
        }

        @media screen and (max-width: 767px) {
          margin-top: 8px;
        }
      }

      .player-item {
        flex: 0 0 auto;
        height: 88px;
        border-bottom: 1px solid $color-earth-2;
        display: flex;
        align-items: center;
        width: calc((100% - 40px) / 2);

        @media screen and (max-width: 1199px) {
          width: 450px;
        }

        @media screen and (max-width: 767px) {
          width: 100%;
          max-width: 450px;
        }

        .uniform-no {
          flex: 0 0 auto;
          width: 24px;
          text-align: center;
          color: $color-black;
          font-family: $font-jleague-kick;
          font-style: normal;
          font-weight: 700;
          font-size: 24px;
          line-height: 24px;
        }

        .position {
          flex: 0 0 auto;
          margin-left: 16px;
          width: 24px;
          color: $color-earth-4;
          font-family: $font-barlow-condensed;
          font-style: normal;
          font-weight: 700;
          font-size: 18px;
          line-height: 24px;
        }

        .player-photo {
          flex: 0 0 auto;
          margin-left: 16px;
          width: 64px;
          height: 64px;
          border-radius: 32px;

          @media screen and (max-width: 1199px) {
            width: 48px;
            height: 48px;
            border-radius: 24px;
          }
        }

        .info {
          flex: auto;
          margin-left: 16px;

          @media screen and (max-width: 1199px) {
            margin-left: 25px;
          }

          .player-name {
            color: $color-black;
            font-family: $font-overpass;
            font-style: normal;
            font-weight: 900;
            font-size: 20px;
            line-height: 20px;
            @include text_ellipsis();

            @media screen and (max-width: 1199px) {
              font-size: 16px;
              line-height: 16px;
            }
          }

          .nationality {
            margin-top: 12px;
            display: flex;
            align-items: center;

            @media screen and (max-width: 1199px) {
              margin-top: 8px;
            }

            .flag {
              width: 24px;
              height: 24px;
              overflow: hidden;
              display: inline-block;
              margin-right: 8px;

              img {
                width: 100%;
                height: auto;
              }
            }

            .name {
              color: $color-earth-4;
              font-family: $font-barlow-condensed;
              font-style: normal;
              font-weight: 500;
              font-size: 16px;
              line-height: 16px;
              display: inline-block;

              @media screen and (max-width: 1199px) {
                font-size: 14px;
                line-height: 14px;
              }
            }
          }
        }
      }

      .player-item:hover {
        opacity: 1;
      }

      .player-item[href]:hover .info > .player-name {
        text-decoration: underline;
      }
    }

    .players-item + .players-item {
      margin-top: 40px;
    }
  }
}

body.lang-th {
  .club-profile-players .players-item {
    .label,
    .player-item .info .player-name,
    .player-item .info .nationality .name {
      @include font-thai();
    }

    .label,
    .player-item .info .player-name {
      font-weight: 700;
    }
  }
}

body.lang-vi {
  .club-profile-players .players-item {
    .label,
    .player-item .info .player-name,
    .player-item .info .nationality .name {
      @include font-vietnamese();
      font-weight: 600;
    }
  }
}
