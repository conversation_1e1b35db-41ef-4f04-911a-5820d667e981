from django.db import models
from django.utils.decorators import method_decorator
from django.views.decorators.cache import never_cache
from wagtail.admin.panels import FieldPanel, MultiFieldPanel
from wagtail.models import Page
from wagtail.images.models import Image
from match.models import GameExtraInfo
from utils.helpers import get_competitions_list
from utils.helpers.etc import (
    get_partners,
    get_footer_navigations,
    get_main_navigations,
    get_display_banners,
)
from core.helpers import get_sidebar_banner
from news.helper import get_latest_news
from newsletter.forms import SubscribeForm


@method_decorator(never_cache, name="serve")
class HomePage(Page):
    # Database fields
    is_popup_image_enabled = models.BooleanField("Enable", default=False)
    popup_image = models.ForeignKey(
        Image, null=True, blank=True, on_delete=models.SET_NULL, related_name="+"
    )
    popup_image_text = models.CharField(max_length=254, null=True, blank=True)
    is_live_game_enabled = models.BooleanField("Enable", default=False)
    live_game = models.ForeignKey(
        GameExtraInfo,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="+",
    )

    # Editor panels configuration
    content_panels = Page.content_panels + [
        MultiFieldPanel(
            [
                FieldPanel("popup_image_text"),
                FieldPanel("popup_image"),
                FieldPanel("is_popup_image_enabled"),
            ],
            heading="Popup Image",
        ),
        MultiFieldPanel(
            [
                FieldPanel("is_live_game_enabled"),
                FieldPanel("live_game"),
            ],
            heading="Live Game",
        ),
    ]

    # Parent page / subpage type rules
    parent_page_types = []
    # subpage_types = []

    template = "home/home-page.html"

    def get_context(self, request):
        context = super().get_context(request)

        context["header"] = {
            "template": "home/components/home-page-header.html",
            "navigations": get_main_navigations(),
        }

        # get header bg and news page link
        news_article = get_latest_news()
        if news_article and len(news_article):
            news = news_article[0]
            context["latest_news"] = news
            context["news_page"] = news.get_parent()
            if news.image:
                context["header"]["background"] = news.image

        # fixtures & results widget at the header
        context["competitions"] = get_competitions_list()

        lang_code = "en" if request.LANGUAGE_CODE == "en-us" else request.LANGUAGE_CODE
        context["banners"] = get_display_banners(lang_code=lang_code)

        context["footer"] = {
            "partners": get_partners(),
            "navigations": get_footer_navigations(),
        }

        context["sidebar"] = {"banner": get_sidebar_banner()}

        context["subscribe_form"] = SubscribeForm(initial={"source": "jleague-footer"})

        if self.is_popup_image_enabled and self.popup_image:
            context["popup_image"] = self.popup_image
            context["popup_image_text"] = self.popup_image_text

        if self.is_live_game_enabled and self.live_game:
            context["has_live_game"] = True

        return context

    def get_sitemap_urls(self, request):
        sitemap = super().get_sitemap_urls(request)
        sitemap[0]["priority"] = 1
        sitemap[0]["changefreq"] = "weekly"
        return sitemap
