<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 28.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 84 80" style="enable-background:new 0 0 84 80;" xml:space="preserve">
<style type="text/css">
	.st0{display:none;}
	.st1{display:inline;}
	.st2{fill:#FFFFFF;}
	.st3{clip-path:url(#SVGID_00000039103112739000394620000001770459218391099800_);}
	.st4{fill:none;stroke:#BAA700;stroke-width:0.1416;stroke-miterlimit:10;}
	.st5{fill:none;stroke:#BAA701;stroke-width:0.1416;stroke-miterlimit:10;}
	.st6{fill:none;stroke:#BAA802;stroke-width:0.1416;stroke-miterlimit:10;}
	.st7{fill:none;stroke:#BBA803;stroke-width:0.1416;stroke-miterlimit:10;}
	.st8{fill:none;stroke:#BBA804;stroke-width:0.1416;stroke-miterlimit:10;}
	.st9{fill:none;stroke:#BBA805;stroke-width:0.1416;stroke-miterlimit:10;}
	.st10{fill:none;stroke:#BBA906;stroke-width:0.1416;stroke-miterlimit:10;}
	.st11{fill:none;stroke:#BCA907;stroke-width:0.1416;stroke-miterlimit:10;}
	.st12{fill:none;stroke:#BCA908;stroke-width:0.1416;stroke-miterlimit:10;}
	.st13{fill:none;stroke:#BCAA09;stroke-width:0.1416;stroke-miterlimit:10;}
	.st14{fill:none;stroke:#BCAA0A;stroke-width:0.1416;stroke-miterlimit:10;}
	.st15{fill:none;stroke:#BDAA0B;stroke-width:0.1416;stroke-miterlimit:10;}
	.st16{fill:none;stroke:#BDAB0C;stroke-width:0.1416;stroke-miterlimit:10;}
	.st17{fill:none;stroke:#BDAB0D;stroke-width:0.1416;stroke-miterlimit:10;}
	.st18{fill:none;stroke:#BDAB0E;stroke-width:0.1416;stroke-miterlimit:10;}
	.st19{fill:none;stroke:#BEAB0F;stroke-width:0.1416;stroke-miterlimit:10;}
	.st20{fill:none;stroke:#BEAC10;stroke-width:0.1416;stroke-miterlimit:10;}
	.st21{fill:none;stroke:#BEAC11;stroke-width:0.1416;stroke-miterlimit:10;}
	.st22{fill:none;stroke:#BEAC12;stroke-width:0.1416;stroke-miterlimit:10;}
	.st23{fill:none;stroke:#BFAD13;stroke-width:0.1416;stroke-miterlimit:10;}
	.st24{fill:none;stroke:#BFAD14;stroke-width:0.1416;stroke-miterlimit:10;}
	.st25{fill:none;stroke:#BFAD15;stroke-width:0.1416;stroke-miterlimit:10;}
	.st26{fill:none;stroke:#BFAE16;stroke-width:0.1416;stroke-miterlimit:10;}
	.st27{fill:none;stroke:#C0AE17;stroke-width:0.1416;stroke-miterlimit:10;}
	.st28{fill:none;stroke:#C0AE18;stroke-width:0.1416;stroke-miterlimit:10;}
	.st29{fill:none;stroke:#C0AE19;stroke-width:0.1416;stroke-miterlimit:10;}
	.st30{fill:none;stroke:#C0AF1A;stroke-width:0.1416;stroke-miterlimit:10;}
	.st31{fill:none;stroke:#C1AF1B;stroke-width:0.1416;stroke-miterlimit:10;}
	.st32{fill:none;stroke:#C1AF1C;stroke-width:0.1416;stroke-miterlimit:10;}
	.st33{fill:none;stroke:#C1B01D;stroke-width:0.1416;stroke-miterlimit:10;}
	.st34{fill:none;stroke:#C1B01E;stroke-width:0.1416;stroke-miterlimit:10;}
	.st35{fill:none;stroke:#C2B01F;stroke-width:0.1416;stroke-miterlimit:10;}
	.st36{fill:none;stroke:#C2B120;stroke-width:0.1416;stroke-miterlimit:10;}
	.st37{fill:none;stroke:#C2B121;stroke-width:0.1416;stroke-miterlimit:10;}
	.st38{fill:none;stroke:#C2B122;stroke-width:0.1416;stroke-miterlimit:10;}
	.st39{fill:none;stroke:#C3B123;stroke-width:0.1416;stroke-miterlimit:10;}
	.st40{fill:none;stroke:#C3B224;stroke-width:0.1416;stroke-miterlimit:10;}
	.st41{fill:none;stroke:#C3B225;stroke-width:0.1416;stroke-miterlimit:10;}
	.st42{fill:none;stroke:#C3B226;stroke-width:0.1416;stroke-miterlimit:10;}
	.st43{fill:none;stroke:#C4B327;stroke-width:0.1416;stroke-miterlimit:10;}
	.st44{fill:none;stroke:#C4B328;stroke-width:0.1416;stroke-miterlimit:10;}
	.st45{fill:none;stroke:#C4B329;stroke-width:0.1416;stroke-miterlimit:10;}
	.st46{fill:none;stroke:#C4B32A;stroke-width:0.1416;stroke-miterlimit:10;}
	.st47{fill:none;stroke:#C5B42B;stroke-width:0.1416;stroke-miterlimit:10;}
	.st48{fill:none;stroke:#C5B42C;stroke-width:0.1416;stroke-miterlimit:10;}
	.st49{fill:none;stroke:#C5B42D;stroke-width:0.1416;stroke-miterlimit:10;}
	.st50{fill:none;stroke:#C5B52E;stroke-width:0.1416;stroke-miterlimit:10;}
	.st51{fill:none;stroke:#C6B52F;stroke-width:0.1416;stroke-miterlimit:10;}
	.st52{fill:none;stroke:#C6B530;stroke-width:0.1416;stroke-miterlimit:10;}
	.st53{fill:none;stroke:#C6B631;stroke-width:0.1416;stroke-miterlimit:10;}
	.st54{fill:none;stroke:#C6B632;stroke-width:0.1416;stroke-miterlimit:10;}
	.st55{fill:none;stroke:#C7B633;stroke-width:0.1416;stroke-miterlimit:10;}
	.st56{fill:none;stroke:#C7B634;stroke-width:0.1416;stroke-miterlimit:10;}
	.st57{fill:none;stroke:#C7B735;stroke-width:0.1416;stroke-miterlimit:10;}
	.st58{fill:none;stroke:#C7B736;stroke-width:0.1416;stroke-miterlimit:10;}
	.st59{fill:none;stroke:#C8B737;stroke-width:0.1416;stroke-miterlimit:10;}
	.st60{fill:none;stroke:#C8B838;stroke-width:0.1416;stroke-miterlimit:10;}
	.st61{fill:none;stroke:#C8B839;stroke-width:0.1416;stroke-miterlimit:10;}
	.st62{fill:none;stroke:#C8B83A;stroke-width:0.1416;stroke-miterlimit:10;}
	.st63{fill:none;stroke:#C9B93B;stroke-width:0.1416;stroke-miterlimit:10;}
	.st64{fill:none;stroke:#C9B93C;stroke-width:0.1416;stroke-miterlimit:10;}
	.st65{fill:none;stroke:#C9B93D;stroke-width:0.1416;stroke-miterlimit:10;}
	.st66{fill:none;stroke:#C9B93E;stroke-width:0.1416;stroke-miterlimit:10;}
	.st67{fill:none;stroke:#CABA3F;stroke-width:0.1416;stroke-miterlimit:10;}
	.st68{fill:none;stroke:#CABA40;stroke-width:0.1416;stroke-miterlimit:10;}
	.st69{fill:none;stroke:#CABA41;stroke-width:0.1416;stroke-miterlimit:10;}
	.st70{fill:none;stroke:#CABB42;stroke-width:0.1416;stroke-miterlimit:10;}
	.st71{fill:none;stroke:#CBBB43;stroke-width:0.1416;stroke-miterlimit:10;}
	.st72{fill:none;stroke:#CBBB44;stroke-width:0.1416;stroke-miterlimit:10;}
	.st73{fill:none;stroke:#CBBC45;stroke-width:0.1416;stroke-miterlimit:10;}
	.st74{fill:none;stroke:#CBBC46;stroke-width:0.1416;stroke-miterlimit:10;}
	.st75{fill:none;stroke:#CBBC47;stroke-width:0.1416;stroke-miterlimit:10;}
	.st76{fill:none;stroke:#CCBC48;stroke-width:0.1416;stroke-miterlimit:10;}
	.st77{fill:none;stroke:#CCBD49;stroke-width:0.1416;stroke-miterlimit:10;}
	.st78{fill:none;stroke:#CCBD4A;stroke-width:0.1416;stroke-miterlimit:10;}
	.st79{fill:none;stroke:#CCBD4B;stroke-width:0.1416;stroke-miterlimit:10;}
	.st80{fill:none;stroke:#CDBE4C;stroke-width:0.1416;stroke-miterlimit:10;}
	.st81{fill:none;stroke:#CDBE4D;stroke-width:0.1416;stroke-miterlimit:10;}
	.st82{fill:none;stroke:#CDBE4E;stroke-width:0.1416;stroke-miterlimit:10;}
	.st83{fill:none;stroke:#CDBE4F;stroke-width:0.1416;stroke-miterlimit:10;}
	.st84{fill:none;stroke:#CEBF50;stroke-width:0.1416;stroke-miterlimit:10;}
	.st85{fill:none;stroke:#CEBF51;stroke-width:0.1416;stroke-miterlimit:10;}
	.st86{fill:none;stroke:#CEBF52;stroke-width:0.1416;stroke-miterlimit:10;}
	.st87{fill:none;stroke:#CEC053;stroke-width:0.1416;stroke-miterlimit:10;}
	.st88{fill:none;stroke:#CFC054;stroke-width:0.1416;stroke-miterlimit:10;}
	.st89{fill:none;stroke:#CFC055;stroke-width:0.1416;stroke-miterlimit:10;}
	.st90{fill:none;stroke:#CFC156;stroke-width:0.1416;stroke-miterlimit:10;}
	.st91{fill:none;stroke:#CFC157;stroke-width:0.1416;stroke-miterlimit:10;}
	.st92{fill:none;stroke:#D0C158;stroke-width:0.1416;stroke-miterlimit:10;}
	.st93{fill:none;stroke:#D0C159;stroke-width:0.1416;stroke-miterlimit:10;}
	.st94{fill:none;stroke:#D0C25A;stroke-width:0.1416;stroke-miterlimit:10;}
	.st95{fill:none;stroke:#D0C25B;stroke-width:0.1416;stroke-miterlimit:10;}
	.st96{fill:none;stroke:#D1C25C;stroke-width:0.1416;stroke-miterlimit:10;}
	.st97{fill:none;stroke:#D1C35D;stroke-width:0.1416;stroke-miterlimit:10;}
	.st98{fill:none;stroke:#D1C35E;stroke-width:0.1416;stroke-miterlimit:10;}
	.st99{fill:none;stroke:#D1C35F;stroke-width:0.1416;stroke-miterlimit:10;}
	.st100{fill:none;stroke:#D2C460;stroke-width:0.1416;stroke-miterlimit:10;}
	.st101{fill:none;stroke:#D2C461;stroke-width:0.1416;stroke-miterlimit:10;}
	.st102{fill:none;stroke:#D2C462;stroke-width:0.1416;stroke-miterlimit:10;}
	.st103{fill:none;stroke:#D2C463;stroke-width:0.1416;stroke-miterlimit:10;}
	.st104{fill:none;stroke:#D3C564;stroke-width:0.1416;stroke-miterlimit:10;}
	.st105{fill:none;stroke:#D3C565;stroke-width:0.1416;stroke-miterlimit:10;}
	.st106{fill:none;stroke:#D3C566;stroke-width:0.1416;stroke-miterlimit:10;}
	.st107{fill:none;stroke:#D3C667;stroke-width:0.1416;stroke-miterlimit:10;}
	.st108{fill:none;stroke:#D4C668;stroke-width:0.1416;stroke-miterlimit:10;}
	.st109{fill:none;stroke:#D4C669;stroke-width:0.1416;stroke-miterlimit:10;}
	.st110{fill:none;stroke:#D4C66A;stroke-width:0.1416;stroke-miterlimit:10;}
	.st111{fill:none;stroke:#D4C76B;stroke-width:0.1416;stroke-miterlimit:10;}
	.st112{fill:none;stroke:#D5C76C;stroke-width:0.1416;stroke-miterlimit:10;}
	.st113{fill:none;stroke:#D5C76D;stroke-width:0.1416;stroke-miterlimit:10;}
	.st114{fill:none;stroke:#D5C86E;stroke-width:0.1416;stroke-miterlimit:10;}
	.st115{fill:none;stroke:#D5C86F;stroke-width:0.1416;stroke-miterlimit:10;}
	.st116{fill:none;stroke:#D6C870;stroke-width:0.1416;stroke-miterlimit:10;}
	.st117{fill:none;stroke:#D6C971;stroke-width:0.1416;stroke-miterlimit:10;}
	.st118{fill:none;stroke:#D6C972;stroke-width:0.1416;stroke-miterlimit:10;}
	.st119{fill:none;stroke:#D6C973;stroke-width:0.1416;stroke-miterlimit:10;}
	.st120{fill:none;stroke:#D7C974;stroke-width:0.1416;stroke-miterlimit:10;}
	.st121{fill:none;stroke:#D7CA75;stroke-width:0.1416;stroke-miterlimit:10;}
	.st122{fill:none;stroke:#D7CA76;stroke-width:0.1416;stroke-miterlimit:10;}
	.st123{fill:none;stroke:#D7CA77;stroke-width:0.1416;stroke-miterlimit:10;}
	.st124{fill:none;stroke:#D8CB78;stroke-width:0.1416;stroke-miterlimit:10;}
	.st125{fill:none;stroke:#D8CB79;stroke-width:0.1416;stroke-miterlimit:10;}
	.st126{fill:none;stroke:#D8CB7A;stroke-width:0.1416;stroke-miterlimit:10;}
	.st127{fill:none;stroke:#D8CC7B;stroke-width:0.1416;stroke-miterlimit:10;}
	.st128{fill:none;stroke:#D9CC7C;stroke-width:0.1416;stroke-miterlimit:10;}
	.st129{fill:none;stroke:#D9CC7D;stroke-width:0.1416;stroke-miterlimit:10;}
	.st130{fill:none;stroke:#D9CC7E;stroke-width:0.1416;stroke-miterlimit:10;}
	.st131{fill:none;stroke:#D9CD7F;stroke-width:0.1416;stroke-miterlimit:10;}
	.st132{fill:none;stroke:#DACD80;stroke-width:0.1416;stroke-miterlimit:10;}
	.st133{fill:none;stroke:#DACD81;stroke-width:0.1416;stroke-miterlimit:10;}
	.st134{fill:none;stroke:#DACE82;stroke-width:0.1416;stroke-miterlimit:10;}
	.st135{fill:none;stroke:#DACE83;stroke-width:0.1416;stroke-miterlimit:10;}
	.st136{fill:none;stroke:#DBCE84;stroke-width:0.1416;stroke-miterlimit:10;}
	.st137{fill:none;stroke:#DBCF85;stroke-width:0.1416;stroke-miterlimit:10;}
	.st138{fill:none;stroke:#DBCF86;stroke-width:0.1416;stroke-miterlimit:10;}
	.st139{fill:none;stroke:#DBCF87;stroke-width:0.1416;stroke-miterlimit:10;}
	.st140{fill:none;stroke:#DCCF88;stroke-width:0.1416;stroke-miterlimit:10;}
	.st141{fill:none;stroke:#DCD089;stroke-width:0.1416;stroke-miterlimit:10;}
	.st142{fill:none;stroke:#DCD08A;stroke-width:0.1416;stroke-miterlimit:10;}
	.st143{fill:none;stroke:#CBBB45;stroke-width:0.1416;stroke-miterlimit:10;}
	.st144{clip-path:url(#SVGID_00000133502191498402038070000002989907894013859498_);}
	.st145{fill:#504100;}
	
		.st146{clip-path:url(#SVGID_00000176028356408810878950000007216722485086767757_);fill:url(#SVGID_00000041990200790441475000000008421811619605823419_);}
	
		.st147{clip-path:url(#SVGID_00000167357381890562699360000001759061533602322851_);fill:url(#SVGID_00000037675060184720895070000015838341300181873067_);}
	
		.st148{clip-path:url(#SVGID_00000027565426760155298230000001243868498601741702_);fill:url(#SVGID_00000151519820124792269320000013990905970331909762_);}
	
		.st149{clip-path:url(#SVGID_00000143579200118278916730000013988145581061912972_);fill:url(#SVGID_00000171709445638498981350000010778298553178032810_);}
	
		.st150{clip-path:url(#SVGID_00000112624806262712987350000012409876699852159126_);fill:url(#SVGID_00000103250889627157966130000002686694848746544807_);}
	
		.st151{clip-path:url(#SVGID_00000159457154267091918900000004373362509976516028_);fill:url(#SVGID_00000098921916897900689310000007683235921247764142_);}
	
		.st152{clip-path:url(#SVGID_00000078748683655567474320000002986599983851539840_);fill:url(#SVGID_00000041979380610612535270000002253972005016100742_);}
	
		.st153{clip-path:url(#SVGID_00000010274751108174986990000003215851846653590423_);fill:url(#SVGID_00000099658571100940472060000007206110770121542555_);}
	
		.st154{clip-path:url(#SVGID_00000029044683431803200600000013809665435420296854_);fill:url(#SVGID_00000011015423420101284270000012637523756867752101_);}
	
		.st155{clip-path:url(#SVGID_00000065794007825005980850000010966163531625190274_);fill:url(#SVGID_00000096754524178068583990000001249174304584703621_);}
	
		.st156{clip-path:url(#SVGID_00000016036505775251092890000003736181323388400790_);fill:url(#SVGID_00000088126347856844451080000009560531995060121994_);}
	
		.st157{clip-path:url(#SVGID_00000031906546760795233410000002528070998585445285_);fill:url(#SVGID_00000018238095889113744000000017393163939208848000_);}
	
		.st158{clip-path:url(#SVGID_00000119812162799609716850000007024845068819945897_);fill:url(#SVGID_00000150086452920305626990000003698037422606158467_);}
	
		.st159{clip-path:url(#SVGID_00000140703667492650076390000017170609973270726803_);fill:url(#SVGID_00000057113868043862836280000002101859181496661408_);}
	
		.st160{clip-path:url(#SVGID_00000063614969984107858160000009728836435544229259_);fill:url(#SVGID_00000060746861708672723600000000844956626333703343_);}
	
		.st161{clip-path:url(#SVGID_00000170271960189044454320000017191682874249298363_);fill:url(#SVGID_00000070810077653745411750000014477660596461438637_);}
	
		.st162{clip-path:url(#SVGID_00000103964228843211293980000015938106070229288863_);fill:url(#SVGID_00000044898947752859391760000010326376960606739603_);}
	
		.st163{clip-path:url(#SVGID_00000008831996846946884220000013076131638407137172_);fill:url(#SVGID_00000013173533121263391100000002338181267010082456_);}
	
		.st164{clip-path:url(#SVGID_00000109001005802028510410000017474795172258192286_);fill:url(#SVGID_00000063632775415847980040000006071659847627670942_);}
	
		.st165{clip-path:url(#SVGID_00000036960479925300269740000006843064421623142834_);fill:url(#SVGID_00000022559544796920544590000015315704350659493256_);}
	
		.st166{clip-path:url(#SVGID_00000049916042884712593860000014748818931726130567_);fill:url(#SVGID_00000132048517482797167670000007504189392931747503_);}
	
		.st167{clip-path:url(#SVGID_00000172422587927652438580000015395023490877801611_);fill:url(#SVGID_00000094612556025027515380000005485414554553298340_);}
	
		.st168{clip-path:url(#SVGID_00000035497062491482190760000001969741903228862115_);fill:url(#SVGID_00000183249462699378337100000009462686268037909675_);}
	
		.st169{clip-path:url(#SVGID_00000093889878012386513820000017715106344935790513_);fill:url(#SVGID_00000095311052733503651260000003908253435126861456_);}
	
		.st170{clip-path:url(#SVGID_00000110445855785697898320000014820198774013735595_);fill:url(#SVGID_00000173840578869866459780000007977645112775359646_);}
	
		.st171{clip-path:url(#SVGID_00000119826449990337855340000016914579744758181040_);fill:url(#SVGID_00000163788473995050471080000014792444476568534413_);}
	
		.st172{clip-path:url(#SVGID_00000119079226838723896900000002338189615258770853_);fill:url(#SVGID_00000036959802915682050200000003954925993818804362_);}
	
		.st173{clip-path:url(#SVGID_00000065767667132884665110000002533948066513733281_);fill:url(#SVGID_00000171716175167282131060000013278040961008903071_);}
	
		.st174{clip-path:url(#SVGID_00000039102370295822457880000000344768425107648700_);fill:url(#SVGID_00000047769516604312478870000004650978261314930571_);}
	
		.st175{clip-path:url(#SVGID_00000078046824509076735200000000249471177640752017_);fill:url(#SVGID_00000117669055706521102200000009375275093844775328_);}
	
		.st176{clip-path:url(#SVGID_00000129172460197481879320000010869727169674660768_);fill:url(#SVGID_00000052090071527075420800000002691937141588640186_);}
	
		.st177{clip-path:url(#SVGID_00000112607076339365208980000009227661208173680789_);fill:url(#SVGID_00000048483425466836579290000011288500975050556837_);}
	
		.st178{clip-path:url(#SVGID_00000083066810066228004340000000844286663116575383_);fill:url(#SVGID_00000038401996191562588030000002441455458653415563_);}
	
		.st179{clip-path:url(#SVGID_00000160872386011653303170000000561321606735052189_);fill:url(#SVGID_00000153695864812354555370000015128838235524716955_);}
	
		.st180{clip-path:url(#SVGID_00000136389125746820923120000006223869395965735574_);fill:url(#SVGID_00000039134080125998148850000013511692482306972340_);}
	
		.st181{clip-path:url(#SVGID_00000165219714775954061830000001444742561729468565_);fill:url(#SVGID_00000000201884799094776490000012252729337396839088_);}
	
		.st182{clip-path:url(#SVGID_00000127761417538011046720000007033854151435499926_);fill:url(#SVGID_00000013188055437991908800000008927512345767234731_);}
	
		.st183{clip-path:url(#SVGID_00000119803821947837983940000018219301018525581995_);fill:url(#SVGID_00000111888421923729946610000010528617433184742823_);}
	
		.st184{clip-path:url(#SVGID_00000077317606080589669990000011515981083082769044_);fill:url(#SVGID_00000111153716261853432780000016574172791640248470_);}
	
		.st185{clip-path:url(#SVGID_00000031180160033379684410000015276874592710771859_);fill:url(#SVGID_00000081636778305579005990000005613058438818977428_);}
	
		.st186{clip-path:url(#SVGID_00000112634344050729558460000004280934395324858793_);fill:url(#SVGID_00000129897077337645939220000006125304493850329260_);}
	
		.st187{clip-path:url(#SVGID_00000151538750892246275130000014128893886486092434_);fill:url(#SVGID_00000065760175549009127170000014542040554851552650_);}
	
		.st188{clip-path:url(#SVGID_00000093865691248846499770000001026399014927162758_);fill:url(#SVGID_00000170257838304627011090000016678344707757534397_);}
	
		.st189{clip-path:url(#SVGID_00000055669236587148610100000009908752411812284076_);fill:url(#SVGID_00000096029159194019510680000015673137678881345708_);}
	
		.st190{clip-path:url(#SVGID_00000012456406596723608260000007739236024806534311_);fill:url(#SVGID_00000132071652876809854520000009414565338825976216_);}
	
		.st191{clip-path:url(#SVGID_00000084505101748393663580000002515134345658846395_);fill:url(#SVGID_00000035522967407396739090000009148980292585947317_);}
	
		.st192{clip-path:url(#SVGID_00000137133604766758984500000014404525197179001262_);fill:url(#SVGID_00000114760050405722245020000008753634396537720991_);}
	
		.st193{clip-path:url(#SVGID_00000103963973509475869390000001470779280944874126_);fill:url(#SVGID_00000118360088298073867370000015974658617654359737_);}
	
		.st194{clip-path:url(#SVGID_00000008136999444677899550000006439575122753616039_);fill:url(#SVGID_00000114753978511664857910000017475208879790730666_);}
	
		.st195{clip-path:url(#SVGID_00000168106949405115847430000002263581993711115926_);fill:url(#SVGID_00000075871530415581192880000012698921029519457463_);}
	
		.st196{clip-path:url(#SVGID_00000093877765384759213130000015900011569099007932_);fill:url(#SVGID_00000179633072235334140860000017772849844534692502_);}
	
		.st197{clip-path:url(#SVGID_00000015336723792143091780000017941703799139400070_);fill:url(#SVGID_00000120539265162617568430000005363621869706594690_);}
	
		.st198{clip-path:url(#SVGID_00000011029578135874641920000012077457275944627077_);fill:url(#SVGID_00000022544970532586692180000001732329737745497238_);}
	
		.st199{clip-path:url(#SVGID_00000069388072925572647360000015024993855202029744_);fill:url(#SVGID_00000021824114400460283840000014102037928490491324_);}
	.st200{opacity:0.8;}
	.st201{clip-path:url(#SVGID_00000138556311421667349590000013191053311509688452_);}
	
		.st202{clip-path:url(#SVGID_00000138536224047427474850000013967439593540802953_);fill:url(#SVGID_00000140724128534987857450000008770682560635916722_);}
	.st203{opacity:0.3;}
	.st204{clip-path:url(#SVGID_00000064341422792243919000000002594059295170386877_);}
	
		.st205{clip-path:url(#SVGID_00000080899448348327754310000007792094728233643393_);fill:url(#SVGID_00000152966743790963156100000008241519613844074941_);}
	.st206{fill:#AF9C60;}
	.st207{fill:#ADB2B4;}
	.st208{fill:#00378F;}
</style>
<g class="st0">
	<g class="st1">
		<g>
			<polygon class="st2" points="21.2,61.5 26.6,61.5 26.6,62.7 23.2,62.7 23.2,63.8 26.3,63.8 26.3,65 23.2,65 23.2,67 21.2,67 			
				"/>
			<polygon class="st2" points="40.4,61.5 45.8,61.5 45.8,62.7 42.4,62.7 42.4,63.8 45.5,63.8 45.5,65 42.4,65 42.4,67 40.4,67 			
				"/>
			<path class="st2" d="M27.2,61.5h1.9v3.1c0,0.3,0,0.6,0.2,0.8c0.2,0.4,0.7,0.5,1.1,0.5c0.5,0,0.8-0.2,0.9-0.3
				c0.3-0.3,0.3-0.8,0.3-1.1v-3.1h1.9v2.9c0,0.6,0,1.3-0.3,1.8c-0.6,0.9-2,1-2.9,1c-1.2,0-1.9-0.2-2.3-0.4c-0.8-0.4-0.9-1.3-0.9-1.7
				L27.2,61.5L27.2,61.5z"/>
			<path class="st2" d="M35.1,61.5h2v3.8c0,0.4,0,0.9-0.4,1.3s-1,0.5-1.5,0.5H34v-1.3h0.4c0.4,0,0.7,0,0.7-0.6
				C35.1,65.2,35.1,61.5,35.1,61.5z"/>
			<rect x="37.7" y="61.5" class="st2" width="2" height="5.5"/>
			<rect x="46.4" y="61.5" class="st2" width="2" height="5.5"/>
			<g>
				<g>
					<path class="st2" d="M49.1,61.5h2v4.2h3V67h-4.9L49.1,61.5L49.1,61.5z"/>
					<path class="st2" d="M57.6,67l-1.3-3.8l0,0V67h-2v-5.5h3.1l0.8,2.6l0.8-2.6h3.1V67h-2v-3.8l0,0L58.8,67H57.6z"/>
				</g>
			</g>
		</g>
		<g>
			<path class="st2" d="M3,70.1c0,0.1,0,0.1,0.1,0.2c0.1,0.2,0.7,0.3,1,0.4c0.4,0.1,0.7,0.2,1.1,0.2c0.2,0,0.5,0.1,0.7,0.2
				c0.7,0.3,1,0.8,1,1.3c0,0.4-0.1,0.7-0.3,0.9c-0.5,0.7-1.4,1-2.8,1c-0.5,0-1.6,0-2.3-0.6C1,73.3,1,72.8,0.9,72.5l1.9-0.1
				C2.9,72.8,3.2,73,4,73c0.2,0,0.5,0,0.7-0.1C4.8,72.8,5,72.7,5,72.5c0-0.3-0.2-0.4-0.4-0.4c-0.2-0.1-1.5-0.3-1.7-0.4
				c-0.2,0-0.4-0.1-0.6-0.2s-1.1-0.4-1.1-1.4c0-0.4,0.2-0.8,0.4-1.1c0.6-0.6,1.7-0.7,2.3-0.7c0.7,0,1.3,0.1,1.7,0.2
				c1,0.3,1.1,1.1,1.2,1.4L4.9,70c0-0.2-0.1-0.5-1-0.5C3.6,69.6,3,69.7,3,70.1"/>
			<path class="st2" d="M7.3,68.7h1.9v3.1c0,0.3,0,0.6,0.2,0.8c0.2,0.4,0.7,0.5,1.1,0.5c0.5,0,0.8-0.2,0.9-0.3
				c0.3-0.3,0.3-0.8,0.3-1.1v-3.1h1.9v2.9c0,0.6,0,1.3-0.3,1.8c-0.6,0.9-2,1-2.9,1c-1.2,0-1.9-0.2-2.3-0.4c-0.8-0.4-0.9-1.3-0.9-1.7
				v-3.5H7.3z"/>
			<path class="st2" d="M16.4,69.9h1.4c0.2,0,0.3,0,0.5,0.1c0.1,0.1,0.2,0.2,0.2,0.4c0,0.5-0.5,0.5-0.8,0.5h-1.3
				C16.4,70.9,16.4,69.9,16.4,69.9z M14.5,74.2h1.9v-1.9h1.5c0.9,0,1.7,0,2.2-0.7c0.3-0.3,0.4-0.8,0.4-1.1c0-0.8-0.3-1.2-0.5-1.3
				c-0.4-0.4-1.1-0.4-1.8-0.4h-3.7C14.5,68.8,14.5,74.2,14.5,74.2z"/>
			<polygon class="st2" points="26.7,68.7 26.7,69.9 22.9,69.9 22.9,70.8 26.4,70.8 26.4,72 22.9,72 22.9,72.9 26.8,72.9 26.8,74.2 
				21,74.2 21,68.7 			"/>
			<path class="st2" d="M29.4,69.9H31c0.1,0,0.3,0,0.5,0.2c0.1,0.1,0.1,0.2,0.1,0.4c0,0.5-0.5,0.5-0.8,0.5h-1.5L29.4,69.9L29.4,69.9
				z M27.4,74.2h2v-2h1l1.1,2h2.2L32.3,72c0.2-0.1,0.6-0.1,0.9-0.5c0.2-0.3,0.3-0.7,0.3-1c0-0.6-0.2-1.1-0.6-1.4
				c-0.5-0.3-1-0.3-1.5-0.4h-4C27.4,68.7,27.4,74.2,27.4,74.2z"/>
			<path class="st2" d="M37.8,71.4c0,0.8,0.5,1.7,1.6,1.7c0.4,0,0.8-0.1,1.1-0.4c0.3-0.2,0.3-0.4,0.4-0.6l1.9,0.2
				c-0.1,0.2-0.1,0.4-0.3,0.7c-0.6,1-1.9,1.3-3.1,1.3c-0.5,0-1.1,0-1.6-0.2c-1.1-0.4-2-1.2-2-2.7c0-1.1,0.6-3,3.7-3
				c2.6,0,3.1,1.2,3.2,1.7l-1.8,0.3c-0.1-0.2-0.2-0.4-0.5-0.6s-0.7-0.3-0.9-0.3C38.6,69.7,37.8,70.2,37.8,71.4"/>
			<path class="st2" d="M43.1,68.7H45v3.1c0,0.3,0,0.6,0.2,0.8c0.2,0.4,0.7,0.5,1.1,0.5c0.5,0,0.8-0.2,0.9-0.3
				c0.3-0.3,0.3-0.8,0.3-1.1v-3.1h1.9v2.9c0,0.6,0,1.3-0.3,1.8c-0.6,0.9-2,1-2.9,1c-1.2,0-1.9-0.2-2.3-0.4c-0.8-0.4-0.9-1.3-0.9-1.7
				L43.1,68.7L43.1,68.7z"/>
			<path class="st2" d="M52.3,69.9h1.4c0.2,0,0.3,0,0.5,0.1c0.1,0.1,0.2,0.2,0.2,0.4c0,0.5-0.5,0.5-0.8,0.5h-1.3
				C52.3,70.9,52.3,69.9,52.3,69.9z M50.3,74.2h1.9v-1.9h1.5c0.9,0,1.7,0,2.2-0.7c0.3-0.3,0.4-0.8,0.4-1.1c0-0.8-0.3-1.2-0.5-1.3
				c-0.4-0.4-1.1-0.4-1.8-0.4h-3.7C50.3,68.8,50.3,74.2,50.3,74.2z"/>
		</g>
		<path class="st2" d="M63.6,74.2h-5.5V73c0.2-0.4,0.4-0.8,1.4-1.4c0.2-0.1,0.3-0.2,1.3-0.7c0.5-0.3,0.8-0.4,0.8-0.7
			c0-0.5-0.6-0.5-0.8-0.5s-0.4,0-0.6,0.1C60,70.1,60,70.4,60,70.6h-1.6c0-0.4,0.1-0.8,0.3-1.2c0.3-0.5,0.8-0.8,2.4-0.8
			c1.2,0,1.8,0.2,2.1,0.6c0.2,0.3,0.3,0.6,0.3,1c0,0.8-0.6,1.2-1.2,1.5c-0.3,0.2-0.6,0.3-0.9,0.4c-0.2,0.1-0.8,0.4-1.2,0.7
			c-0.1,0-0.1,0.1-0.1,0.1c0.1,0,0.2,0,0.2,0c0.1,0,0.2,0,0.2,0h3.1V74.2z"/>
		<path class="st2" d="M67.2,69.7c0.5,0,0.8,0.2,1,0.5s0.3,0.8,0.3,1.2c0,0.3-0.1,0.9-0.3,1.2c-0.3,0.4-0.7,0.5-0.9,0.5
			c-1,0-1.1-0.9-1.2-1.3c0-0.1,0-0.3,0-0.4c0-0.5,0.1-0.9,0.3-1.2C66.5,69.8,66.9,69.7,67.2,69.7 M67.3,68.5c-1.4,0-2.7,0.4-3.1,2
			c-0.1,0.3-0.1,0.6-0.1,1c0,0.7,0.1,1.7,1.1,2.4c0.7,0.5,1.5,0.5,2,0.5c1.8,0,3.1-0.8,3.1-2.9c0-0.7-0.1-1.6-0.8-2.2
			C69,68.7,68.2,68.5,67.3,68.5"/>
		<path class="st2" d="M76.3,74.2h-5.5V73c0.2-0.4,0.4-0.8,1.4-1.4c0.2-0.1,0.3-0.2,1.3-0.7c0.5-0.3,0.8-0.4,0.8-0.7
			c0-0.5-0.6-0.5-0.8-0.5c-0.2,0-0.4,0-0.6,0.1c-0.3,0.2-0.3,0.5-0.3,0.7H71c0-0.4,0.1-0.8,0.3-1.2c0.3-0.5,0.8-0.8,2.4-0.8
			c1.2,0,1.8,0.2,2.1,0.6c0.2,0.3,0.3,0.6,0.3,1c0,0.8-0.6,1.2-1.2,1.5c-0.3,0.2-0.6,0.3-0.9,0.4c-0.2,0.1-0.8,0.4-1.2,0.7
			c-0.1,0-0.1,0.1-0.1,0.1c0.1,0,0.2,0,0.2,0c0.1,0,0.2,0,0.2,0h3.1L76.3,74.2L76.3,74.2z"/>
		<path class="st2" d="M79.1,70.8h0.4c0.5,0,1,0,1.2-0.1s0.3-0.2,0.3-0.5c0-0.4-0.4-0.5-0.9-0.5c-0.9,0-1.1,0.3-1.1,0.5l-1.8-0.1
			c0.1-0.5,0.3-1.6,2.9-1.6c0.3,0,0.6,0,0.8,0c0.4,0,1,0.1,1.4,0.4s0.6,0.8,0.6,1.1c0,0.1,0,0.3-0.1,0.5s-0.4,0.5-0.9,0.6
			c-0.1,0-0.1,0-0.2,0c0.1,0,0.1,0,0.2,0c0.7,0.1,1.1,0.5,1.1,1.2c0,1.7-1.9,1.8-3.1,1.8c-0.9,0-1.8,0-2.4-0.5
			c-0.6-0.4-0.7-1-0.7-1.3l1.7-0.1c0,0.1,0.1,0.3,0.3,0.4c0.3,0.2,0.6,0.2,1,0.2c0.8,0,1.1-0.2,1.1-0.6c0-0.5-0.6-0.6-0.9-0.6h-1.1
			v-0.8H79.1z"/>
	</g>
	<g class="st1">
		<g>
			<g>
				<g>
					<defs>
						<path id="SVGID_1_" d="M54.5,55.2c0.1-0.2,0.1-0.3,0.2-0.5c0-0.1,0.1-0.2,0.1-0.2l0.1-0.1l0.1-0.1c0.1-0.2,0.2-0.3,0.3-0.4
							c0.1-0.1,0.1-0.1,0.2-0.2c0.1-0.1,0.1-0.1,0.2-0.2c0.2-0.1,0.4-0.3,0.6-0.4c0.2-0.1,0.4-0.2,0.6-0.2c-0.1,0.2-0.1,0.5-0.2,0.8
							c0,0.3-0.1,0.7-0.2,1c0,0.1,0,0.2-0.1,0.2c0,0.1-0.1,0.2-0.1,0.2c-0.1,0.2-0.2,0.3-0.3,0.4c-0.2,0.3-0.5,0.4-0.9,0.5
							c-0.1,0-0.2,0-0.3,0s-0.2,0-0.2,0c-0.1,0-0.2-0.1-0.3-0.2c-0.1-0.1-0.1-0.2-0.1-0.3C54.4,55.5,54.4,55.3,54.5,55.2z M55.2,57
							c0,0.1,0,0.2,0,0.3c0.1,0.1,0.2,0.2,0.3,0.2s0.3,0.1,0.5,0.1s0.4,0,0.6,0c0.1,0,0.2,0,0.3-0.1c0.1,0,0.1,0,0.2,0
							c0.1,0,0.1,0,0.2,0c0.2-0.1,0.4-0.1,0.6-0.2c0.1,0,0.2-0.1,0.3-0.1s0.2-0.1,0.2-0.2c0.2-0.2,0.4-0.3,0.6-0.5
							c0.2-0.2,0.3-0.4,0.4-0.7c-0.3,0.1-0.6,0.1-0.9,0.1c-0.3,0-0.7-0.1-1-0.1c-0.1,0-0.2,0-0.3,0s-0.2,0-0.3,0
							c-0.2,0-0.3,0.1-0.5,0.1c-0.3,0.1-0.6,0.3-0.9,0.7c-0.1,0.1-0.1,0.2-0.2,0.3C55.3,56.8,55.3,56.9,55.2,57z M59.3,51.4
							c0-0.3,0-0.6,0.1-0.8c-0.2,0.1-0.4,0.2-0.6,0.3c-0.1,0.1-0.2,0.1-0.3,0.2l-0.1,0.1l-0.1,0.1c-0.1,0.1-0.2,0.2-0.3,0.4
							c0,0.1-0.1,0.2-0.1,0.2c0,0.1-0.1,0.2-0.1,0.2c-0.1,0.2-0.2,0.3-0.2,0.5c0,0.1-0.1,0.2-0.1,0.3s0,0.2-0.1,0.2
							c-0.1,0.6,0,1.1,0.9,0.9c0.1,0,0.1,0,0.1-0.1c0,0,0.1,0,0.1-0.1c0.1,0,0.2-0.1,0.2-0.2c0.1-0.1,0.3-0.2,0.3-0.4
							c0-0.1,0.1-0.1,0.1-0.2c0-0.1,0.1-0.2,0.1-0.2c0-0.2,0.1-0.3,0.1-0.5c0-0.1,0-0.2,0-0.3s0-0.2,0-0.3
							C59.3,51.7,59.3,51.5,59.3,51.4z M59.1,55.3c0.1,0,0.2,0,0.3,0s0.2,0,0.3-0.1c0.2-0.1,0.4-0.1,0.6-0.2c0.1,0,0.2-0.1,0.3-0.1
							c0.1-0.1,0.2-0.1,0.3-0.2c0.2-0.1,0.3-0.2,0.5-0.4l0.1-0.1c0-0.1,0.1-0.1,0.1-0.2c0.1-0.1,0.2-0.2,0.2-0.3
							c0.1-0.2,0.3-0.5,0.3-0.7c-0.2,0.1-0.5,0.2-0.9,0.2c-0.2,0-0.3,0-0.5,0c-0.1,0-0.2,0-0.3,0s-0.2,0-0.3,0c-0.2,0-0.3,0-0.5,0.1
							c-0.1,0-0.2,0-0.2,0.1c-0.1,0-0.2,0.1-0.2,0.1c-0.2,0.1-0.3,0.2-0.4,0.3c-0.1,0.1-0.1,0.1-0.2,0.2l-0.1,0.1l-0.1,0.1
							C58,55.1,58.4,55.4,59.1,55.3z M61.4,48.7c-0.1-0.3-0.1-0.6-0.1-0.8c-0.2,0.1-0.4,0.2-0.5,0.4c-0.1,0.1-0.2,0.2-0.2,0.2
							c-0.1,0.1-0.1,0.2-0.2,0.3c0,0.1-0.1,0.1-0.1,0.2c0,0,0,0.1-0.1,0.1c0,0,0,0.1-0.1,0.1c-0.1,0.2-0.1,0.3-0.2,0.5
							c0,0.1-0.1,0.2-0.1,0.3s0,0.2-0.1,0.3c0,0.2-0.1,0.4-0.1,0.5c0,0.2,0,0.3,0,0.4c0,0.1,0.1,0.2,0.1,0.3
							c0.1,0.1,0.2,0.1,0.3,0.1s0.3,0,0.5-0.1s0.3-0.2,0.5-0.3c0.1-0.1,0.2-0.3,0.3-0.4c0.1-0.2,0.1-0.3,0.1-0.5s0-0.3,0-0.5
							s0-0.3,0-0.5C61.5,49,61.5,48.9,61.4,48.7z M61.8,52.6c0.2,0,0.3-0.1,0.5-0.2c0.1,0,0.2-0.1,0.3-0.1c0.1-0.1,0.2-0.1,0.3-0.2
							c0.2-0.1,0.3-0.2,0.5-0.4l0.1-0.1l0.1-0.1c0.1-0.1,0.1-0.1,0.2-0.2c0.1-0.1,0.2-0.2,0.2-0.3c0.1-0.1,0.1-0.2,0.2-0.4
							s0.2-0.5,0.2-0.7c-0.2,0.1-0.5,0.2-0.8,0.3c-0.2,0-0.3,0.1-0.5,0.1s-0.3,0.1-0.5,0.1s-0.3,0.1-0.5,0.2
							c-0.2,0.1-0.3,0.1-0.4,0.2c-0.1,0.1-0.3,0.2-0.4,0.4c-0.1,0.1-0.2,0.3-0.3,0.5s-0.1,0.4-0.1,0.5s0.1,0.2,0.1,0.3
							c0.1,0.1,0.2,0.1,0.3,0.1C61.5,52.7,61.6,52.7,61.8,52.6z M63.5,47.7c0.1-0.3,0.1-0.7,0-1c0-0.1,0-0.2-0.1-0.3
							c0-0.1,0-0.2-0.1-0.2c0-0.2-0.1-0.3-0.1-0.5s-0.1-0.3-0.1-0.4c0-0.1-0.1-0.3-0.1-0.4c-0.1,0.1-0.2,0.1-0.3,0.2
							c-0.1,0.1-0.2,0.2-0.2,0.2c-0.1,0.2-0.3,0.4-0.4,0.6C62,46,62,46.2,61.9,46.4c0,0.1-0.1,0.2-0.1,0.3v0.1v0.1
							c0,0.2-0.1,0.4-0.1,0.6c0,0.1,0,0.2,0,0.3v0.1V48c0,0.6,0.3,1.1,1.1,0.6C63.2,48.4,63.4,48,63.5,47.7z M64.4,49.5
							c0.1,0,0.2-0.1,0.3-0.2c0.2-0.1,0.3-0.2,0.5-0.4l0.1-0.1l0.1-0.1c0.1-0.1,0.1-0.2,0.2-0.2c0.1-0.2,0.2-0.3,0.3-0.5
							c0.1-0.2,0.2-0.5,0.3-0.7c0-0.1,0.1-0.3,0.1-0.4c0-0.1,0-0.3,0-0.4c-0.1,0.1-0.2,0.2-0.4,0.2c-0.1,0.1-0.3,0.1-0.4,0.2
							C65.3,47,65.2,47,65,47.1c-0.1,0-0.2,0.1-0.2,0.1c-0.1,0-0.2,0.1-0.2,0.1c-0.3,0.1-0.6,0.3-0.9,0.5c-0.2,0.2-0.4,0.5-0.5,1
							c-0.2,0.9,0.3,1,0.9,0.8c0,0,0.1,0,0.1-0.1C64.3,49.5,64.3,49.5,64.4,49.5z M65.1,44.5c0-0.2,0-0.3,0-0.5S65,43.7,65,43.5
							c-0.1-0.2-0.1-0.3-0.2-0.5s-0.1-0.3-0.2-0.4c-0.1-0.3-0.3-0.6-0.3-0.8c-0.2,0.2-0.3,0.3-0.4,0.5l-0.1,0.1l-0.1,0.1
							c-0.1,0.1-0.1,0.2-0.1,0.3s0,0.1-0.1,0.2c0,0.1,0,0.2-0.1,0.3c0,0.2-0.1,0.4-0.1,0.5s0,0.2,0,0.3v0.1v0.1c0,0.2,0,0.4,0.1,0.5
							c0.1,0.3,0.2,0.6,0.4,0.7c0.2,0.1,0.4,0,0.8-0.3c0.1-0.1,0.1-0.1,0.2-0.2c0.1-0.1,0.1-0.2,0.2-0.2
							C65,44.8,65.1,44.7,65.1,44.5z M66,46.3c0.1-0.1,0.3-0.2,0.5-0.3l0.1-0.1l0.1-0.1c0.1-0.1,0.1-0.2,0.2-0.2
							c0.1-0.2,0.3-0.3,0.4-0.5c0.1-0.1,0.1-0.2,0.1-0.3s0.1-0.2,0.1-0.3s0.1-0.3,0.1-0.4c0-0.1,0-0.1,0-0.2c0-0.1,0-0.1,0-0.2
							c0-0.3,0-0.5,0-0.8c-0.2,0.2-0.4,0.4-0.7,0.5c-0.1,0.1-0.3,0.2-0.5,0.2c-0.2,0.1-0.3,0.2-0.5,0.3c-0.1,0.1-0.3,0.2-0.4,0.3
							s-0.3,0.2-0.4,0.4c-0.1,0.1-0.2,0.3-0.2,0.5c0,0.1-0.1,0.2-0.1,0.3s0,0.2,0,0.3c0,0.4,0.1,0.7,0.3,0.8
							C65.4,46.5,65.7,46.4,66,46.3z M65.9,42.1c0.3-0.3,0.4-0.7,0.4-1s-0.1-0.7-0.3-1c0-0.1-0.1-0.2-0.1-0.2c0-0.1-0.1-0.1-0.1-0.2
							c-0.1-0.1-0.2-0.3-0.3-0.4c-0.1-0.1-0.2-0.3-0.2-0.4v-0.1l0,0l0,0l0,0l0,0l0,0l0,0l0,0l0,0c0-0.1-0.1-0.1-0.1-0.2
							c0,0-0.1,0.1-0.1,0.2l0,0l0,0l0,0l0,0l0,0l0,0l0,0l0,0l0,0c0,0,0,0,0,0.1C65,39,65,39.1,64.9,39.2c0,0.1-0.1,0.2-0.1,0.3
							s0,0.1,0,0.2c0,0.1,0,0.1,0,0.2c0,0.1-0.1,0.3-0.1,0.5s0,0.4,0,0.5c0,0.2,0,0.4,0.1,0.6c0,0.2,0.1,0.4,0.1,0.5
							C64.9,42.4,65.3,42.8,65.9,42.1z M67.8,42.3c0.1-0.2,0.3-0.3,0.4-0.5s0.2-0.4,0.3-0.6c0.1-0.2,0.1-0.4,0.2-0.5
							c0-0.1,0-0.1,0-0.2c0-0.1,0-0.1,0-0.2c0-0.1,0-0.3,0-0.4c0-0.1,0-0.3,0-0.4v-0.1l0,0l0,0l0,0c0,0,0-0.1,0,0l0,0l0,0l0,0l0,0
							l0,0c0-0.1,0-0.1,0-0.2c0,0.1-0.1,0.1-0.1,0.2l0,0l0,0l0,0l0,0c0,0,0,0.1,0,0l0,0l0,0l0,0l-0.1,0.1c-0.1,0.1-0.2,0.2-0.4,0.3
							c-0.1,0.1-0.3,0.2-0.4,0.3s-0.1,0.1-0.2,0.2c-0.1,0.1-0.1,0.1-0.2,0.2c-0.3,0.2-0.5,0.5-0.7,0.8s-0.3,0.6-0.2,1.1
							c0.1,0.9,0.6,0.9,1.1,0.5C67.5,42.6,67.7,42.5,67.8,42.3z M66.4,38.9L66.4,38.9L66.4,38.9L66.4,38.9L66.4,38.9L66.4,38.9
							C66.5,38.9,66.5,38.8,66.4,38.9c0.2-0.2,0.2-0.2,0.3-0.3c0.1-0.2,0.2-0.4,0.2-0.5V38v-0.1c0-0.1,0-0.2,0-0.3
							c0-0.2-0.1-0.3-0.1-0.5c0-0.1-0.1-0.2-0.1-0.2c0-0.1-0.1-0.1-0.1-0.2c-0.2-0.3-0.4-0.5-0.6-0.8c-0.1-0.1-0.2-0.2-0.3-0.3
							c-0.1-0.1-0.2-0.2-0.2-0.3c-0.1,0.1-0.1,0.2-0.1,0.3s-0.1,0.2-0.1,0.3C65.1,36,65,36.2,65,36.5c0,0.1,0,0.2,0,0.2
							c0,0.1,0,0.2,0,0.2c0,0.2,0.1,0.3,0.1,0.5v0.1c0,0,0,0,0,0.1v0.1c0,0.1,0,0.2,0.1,0.3c0.1,0.2,0.1,0.4,0.2,0.5
							c0,0.1,0.1,0.1,0.1,0.2c0,0,0,0.1,0.1,0.1l0,0l0,0l0,0l0,0l0,0l0,0l0,0l0,0C65.8,39,65.9,39,66,39
							C66.1,39.1,66.2,39,66.4,38.9C66.4,39,66.4,39,66.4,38.9L66.4,38.9L66.4,38.9z M68.6,38.5c0.1-0.1,0.1-0.2,0.2-0.2v-0.1
							c0,0,0,0,0-0.1c0,0,0-0.1,0.1-0.1c0.1-0.2,0.2-0.4,0.2-0.6c0-0.1,0.1-0.2,0.1-0.3s0-0.2,0-0.3c0-0.3,0-0.5,0-0.8
							c0-0.1,0-0.3-0.1-0.4c0-0.1-0.1-0.2-0.1-0.4c-0.1,0.1-0.1,0.2-0.2,0.4c-0.1,0.1-0.2,0.2-0.3,0.4c-0.2,0.2-0.5,0.5-0.7,0.7
							c-0.1,0.1-0.1,0.1-0.2,0.2c-0.1,0.1-0.1,0.1-0.2,0.2c-0.1,0.1-0.2,0.3-0.2,0.4c0,0.1-0.1,0.2-0.1,0.2v0.1v0.1
							c0,0.2,0,0.4,0,0.6c0,0.1,0.1,0.2,0.1,0.3c0,0,0,0.1,0.1,0.1l0,0l0,0l0,0l0,0l0,0l0,0l0,0l0,0c0.1,0.1,0.2,0.2,0.3,0.2
							c0.1,0,0.2,0,0.3-0.1l0,0l0,0l0,0l0,0l0,0l0,0l0,0l0,0c0,0,0.1,0,0.1-0.1c0.1,0,0.1-0.1,0.2-0.2
							C68.4,38.8,68.5,38.7,68.6,38.5z M67,33.7c0-0.1-0.1-0.1-0.1-0.2c-0.1-0.1-0.2-0.3-0.3-0.4c-0.1-0.1-0.2-0.2-0.4-0.4
							c-0.1-0.1-0.3-0.2-0.4-0.3c-0.2-0.2-0.5-0.4-0.6-0.6c-0.1,0.2-0.1,0.4-0.2,0.7c0,0.1,0,0.2,0,0.3s0,0.2,0,0.3
							c0,0.2,0,0.3,0.1,0.5v0.1v0.1c0,0.1,0.1,0.2,0.1,0.3c0.1,0.2,0.1,0.3,0.2,0.5c0,0.1,0.1,0.2,0.1,0.2c0,0.1,0.1,0.1,0.1,0.2
							c0.4,0.5,0.8,0.7,1.2-0.1c0-0.1,0.1-0.2,0.1-0.3s0-0.2,0-0.3c0-0.2,0-0.3-0.1-0.5C67.1,33.8,67,33.8,67,33.7z M69.3,33.6
							c0-0.1,0-0.1,0-0.2c0-0.2,0-0.4,0-0.6c0-0.1,0-0.3,0-0.4c0-0.1-0.1-0.3-0.1-0.4c-0.1-0.3-0.2-0.5-0.3-0.7
							c-0.1,0.3-0.3,0.5-0.5,0.8c-0.1,0.1-0.2,0.3-0.3,0.4c-0.1,0.1-0.2,0.3-0.3,0.4c-0.1,0.1-0.2,0.3-0.3,0.4
							c0,0.1-0.1,0.2-0.1,0.2c0,0.1-0.1,0.2-0.1,0.2c0,0.2-0.1,0.3,0,0.5c0,0.1,0,0.2,0,0.3s0,0.2,0.1,0.3c0.3,0.8,0.8,0.7,1.2,0.1
							c0.1-0.1,0.1-0.1,0.2-0.2c0-0.1,0.1-0.2,0.1-0.3c0.1-0.2,0.2-0.4,0.2-0.6c0-0.1,0.1-0.2,0.1-0.3
							C69.2,33.7,69.3,33.7,69.3,33.6z M66.1,29.5c-0.3-0.2-0.6-0.4-0.8-0.6c-0.1-0.1-0.3-0.2-0.4-0.2c-0.1-0.1-0.2-0.2-0.3-0.3
							c0,0.1,0,0.2-0.1,0.3c0,0.1,0,0.2,0,0.3c0,0.2,0,0.5,0.1,0.7c0,0.1,0.1,0.3,0.1,0.5c0.1,0.2,0.1,0.3,0.2,0.5
							c0,0.1,0.1,0.2,0.1,0.2c0,0.1,0.1,0.2,0.2,0.2c0.1,0.1,0.2,0.3,0.3,0.4c0.1,0.1,0.2,0.2,0.3,0.3c0.1,0.1,0.2,0.1,0.3,0.1
							c0.1,0,0.2-0.1,0.3-0.2c0,0,0.1-0.1,0.1-0.2s0.1-0.2,0.1-0.3c0.1-0.2,0.1-0.4,0.1-0.6c0-0.1,0-0.2,0-0.3
							c0-0.1-0.1-0.2-0.1-0.2c-0.1-0.2-0.2-0.3-0.3-0.4C66.4,29.8,66.3,29.7,66.1,29.5z M68.8,30.2c0-0.2,0-0.4,0-0.6s0-0.4,0-0.6
							c-0.1-0.3-0.1-0.5-0.2-0.8c-0.1-0.1-0.1-0.2-0.2-0.3c-0.1-0.1-0.1-0.2-0.2-0.3c0,0.1-0.1,0.3-0.1,0.4
							c-0.1,0.1-0.1,0.3-0.2,0.4c-0.2,0.3-0.4,0.6-0.5,0.9c-0.1,0.2-0.1,0.3-0.2,0.5s-0.1,0.3-0.1,0.5c0,0.1,0,0.2,0,0.3
							c0,0.1,0,0.2,0,0.3c0,0.2,0.1,0.4,0.2,0.6c0.1,0.1,0.1,0.2,0.2,0.3c0.1,0.1,0.1,0.1,0.2,0.2c0.1,0.1,0.2,0.1,0.3,0.1
							c0.1,0,0.2-0.1,0.3-0.2c0.1-0.1,0.2-0.2,0.3-0.4c0.1-0.2,0.1-0.3,0.2-0.5c0-0.1,0.1-0.2,0.1-0.3
							C68.8,30.4,68.8,30.3,68.8,30.2z M65.2,26.1C65.1,26.1,65,26,65,26c-0.1,0-0.1-0.1-0.2-0.1c-0.2-0.1-0.3-0.1-0.5-0.2
							c-0.1-0.1-0.3-0.1-0.4-0.2s-0.2-0.1-0.3-0.2c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0.1,0.3v0.1v0.1
							c0,0.1,0,0.1,0.1,0.2c0.1,0.3,0.3,0.6,0.5,0.9c0.1,0.1,0.2,0.3,0.4,0.4c0.1,0.1,0.1,0.1,0.2,0.2s0.1,0.1,0.2,0.2
							c0.5,0.4,1,0.5,1.1-0.4c0-0.1,0-0.2,0-0.3c0-0.1,0-0.2,0-0.3c0-0.2-0.1-0.3-0.2-0.5c0-0.1-0.1-0.1-0.1-0.2
							c-0.1-0.1-0.1-0.1-0.2-0.2C65.5,26.3,65.3,26.2,65.2,26.1z M67.9,26.4c0-0.4-0.1-0.9-0.2-1.2c0-0.1,0-0.1-0.1-0.2v-0.1v-0.1
							c-0.1-0.1-0.1-0.2-0.2-0.3c-0.1-0.1-0.1-0.2-0.2-0.3c-0.1-0.1-0.2-0.2-0.3-0.3c0,0.1,0,0.3-0.1,0.4c0,0.1-0.1,0.3-0.1,0.5
							c-0.1,0.2-0.1,0.3-0.2,0.5c0,0.1-0.1,0.2-0.1,0.2c0,0.1-0.1,0.2-0.1,0.3c-0.1,0.2-0.1,0.3-0.1,0.5c0,0.1,0,0.2,0,0.3
							c0,0.1,0,0.2,0,0.3c0,0.2,0,0.3,0.1,0.5c0,0.1,0.1,0.2,0.1,0.3c0,0.1,0.1,0.2,0.2,0.3c0.5,0.7,1,0.5,1.2-0.2
							c0-0.1,0.1-0.2,0.1-0.3c0-0.1,0-0.2,0.1-0.3C67.9,26.8,67.9,26.6,67.9,26.4z M65,24.5c0-0.2,0-0.4-0.1-0.6
							c0-0.1-0.1-0.2-0.1-0.2c0-0.1-0.1-0.1-0.2-0.2c-0.2-0.3-0.5-0.4-0.8-0.6c-0.3-0.1-0.6-0.2-0.9-0.3c-0.3-0.1-0.6-0.2-0.8-0.3
							c0,0.2,0.1,0.5,0.1,0.7c0.1,0.2,0.1,0.4,0.3,0.6c0,0.1,0.1,0.1,0.1,0.2s0.1,0.1,0.1,0.2c0.1,0.1,0.2,0.3,0.4,0.4
							c0.1,0.1,0.1,0.1,0.2,0.2c0.1,0.1,0.1,0.1,0.2,0.2c0.1,0.1,0.3,0.2,0.4,0.3c0.1,0,0.1,0.1,0.2,0.1s0.1,0.1,0.2,0.1
							s0.2,0,0.3,0C64.8,25.1,65,24.9,65,24.5z M66.6,23.4c0-0.1,0-0.2,0-0.3c0-0.1,0-0.2,0-0.3c0-0.2-0.1-0.4-0.1-0.6
							c0-0.1-0.1-0.2-0.1-0.3c0-0.1-0.1-0.2-0.1-0.3c-0.1-0.2-0.3-0.5-0.4-0.7c-0.2-0.2-0.4-0.4-0.6-0.5c0,0.3,0,0.6-0.1,0.9
							S65,22,65,22.3c-0.1,0.3-0.1,0.7,0,1c0,0.1,0,0.2,0.1,0.2c0,0.1,0.1,0.2,0.1,0.2c0.1,0.2,0.2,0.3,0.3,0.5
							c0.3,0.3,0.6,0.4,0.8,0.3c0.1,0,0.2-0.1,0.2-0.2s0.1-0.1,0.1-0.2s0-0.2,0.1-0.2C66.6,23.8,66.6,23.6,66.6,23.4z M63.2,20.7
							c-0.1-0.2-0.2-0.3-0.3-0.4c-0.3-0.2-0.6-0.4-0.9-0.4c-0.2,0-0.3-0.1-0.5-0.1s-0.3,0-0.5-0.1c-0.2,0-0.3,0-0.4-0.1
							c-0.1,0-0.3-0.1-0.4-0.1c0,0.1,0.1,0.2,0.1,0.3c0,0.1,0.1,0.2,0.1,0.3c0,0.1,0.1,0.2,0.2,0.3c0.1,0.1,0.1,0.2,0.2,0.3
							c0.1,0.1,0.2,0.2,0.3,0.4c0.1,0.1,0.3,0.2,0.4,0.4c0.1,0.1,0.3,0.2,0.5,0.3c0.1,0,0.2,0.1,0.2,0.1c0.1,0,0.2,0.1,0.2,0.1
							c0.6,0.2,1.1,0.2,1-0.7c0-0.1,0-0.2-0.1-0.3C63.2,20.9,63.2,20.8,63.2,20.7z M64.8,20.5c0-0.1,0-0.2,0-0.3c0-0.1,0-0.2,0-0.3
							c0-0.2-0.1-0.4-0.1-0.6c-0.1-0.2-0.1-0.4-0.2-0.6s-0.2-0.4-0.3-0.5c-0.1-0.1-0.2-0.2-0.3-0.3c-0.1-0.1-0.2-0.2-0.3-0.3
							c-0.1-0.1-0.2-0.2-0.3-0.2c-0.1-0.1-0.2-0.1-0.3-0.2c0,0.1,0.1,0.3,0.1,0.4c0,0.2,0,0.3,0,0.5s0,0.3,0,0.5s0,0.3,0,0.5
							c0,0.4,0,0.7,0.1,1c0.1,0.2,0.1,0.3,0.2,0.5c0.1,0.1,0.1,0.1,0.2,0.2c0.1,0.1,0.1,0.1,0.2,0.2C64.4,21.6,64.7,21.2,64.8,20.5z
							 M60.6,17.4c-0.1-0.1-0.3-0.2-0.4-0.2c-0.2,0-0.3-0.1-0.5-0.1s-0.3,0-0.5,0c-0.1,0-0.2,0-0.2,0c-0.1,0-0.2,0-0.2,0
							c-0.3,0-0.6,0-0.8-0.1c0.1,0.4,0.4,0.8,0.7,1.1c0.1,0.1,0.2,0.2,0.4,0.3c0.1,0.1,0.3,0.2,0.5,0.3c0.1,0,0.2,0.1,0.3,0.1
							c0,0,0.1,0,0.1,0.1c0,0,0.1,0,0.1,0.1c0.2,0.1,0.3,0.1,0.5,0.2c0.3,0.1,0.6,0.1,0.8-0.1c0.2-0.1,0.2-0.4,0.1-0.8
							c0-0.1-0.1-0.2-0.1-0.3c0-0.1-0.1-0.2-0.1-0.2C60.9,17.6,60.8,17.5,60.6,17.4z M62.4,18.1c0.2-0.1,0.2-0.4,0.2-0.8
							c0-0.2,0-0.4-0.1-0.6v-0.1v-0.1c0-0.1-0.1-0.2-0.1-0.3c-0.1-0.2-0.2-0.4-0.3-0.6c-0.1-0.2-0.2-0.3-0.4-0.5
							c-0.4-0.4-0.8-0.7-1.3-0.9c0.1,0.2,0.1,0.6,0.2,0.9c0,0.1,0,0.2,0,0.3s0,0.2,0,0.3c0,0.2,0,0.4,0,0.5c0,0.2,0,0.3,0.1,0.5
							c0,0.2,0.1,0.3,0.2,0.5c0.1,0.2,0.2,0.3,0.3,0.4s0.1,0.1,0.2,0.2c0.1,0.1,0.2,0.1,0.3,0.2C62,18.2,62.3,18.3,62.4,18.1z
							 M57.1,16.4c0.3,0.1,0.7,0.2,1,0.3c0.3,0,0.6,0,0.7-0.2c0.1-0.1,0.1-0.2,0.1-0.3c0-0.1,0-0.1,0-0.2c0-0.1-0.1-0.2-0.1-0.3
							c-0.1-0.2-0.2-0.4-0.3-0.5s-0.3-0.2-0.4-0.3c-0.3-0.1-0.6-0.2-1-0.2c-0.3,0-0.7,0.1-1,0.1s-0.6,0.1-0.8,0
							c0.1,0.1,0.1,0.2,0.2,0.3l0.1,0.1l0.1,0.1c0.1,0.2,0.3,0.3,0.5,0.5c0.1,0,0.1,0.1,0.2,0.1c0.1,0,0.1,0.1,0.2,0.1
							C56.7,16.3,56.9,16.3,57.1,16.4z M59.9,15.3c0.1-0.2,0.2-0.4,0.1-0.8c-0.1-0.3-0.2-0.7-0.5-1.1c-0.1-0.2-0.2-0.4-0.4-0.5
							c-0.1-0.1-0.1-0.2-0.2-0.2c-0.1-0.1-0.1-0.1-0.2-0.2c-0.2-0.2-0.4-0.3-0.7-0.4c-0.1,0-0.1-0.1-0.2-0.1c-0.1,0-0.1,0-0.2-0.1
							c-0.1,0-0.2-0.1-0.4-0.1c0.1,0.2,0.2,0.5,0.3,0.9c0.1,0.3,0.1,0.7,0.2,1c0.1,0.3,0.2,0.7,0.4,1c0.1,0.1,0.2,0.3,0.4,0.4
							c0.1,0.1,0.3,0.2,0.5,0.3c0.1,0,0.2,0.1,0.3,0.1s0.2,0,0.2,0C59.8,15.5,59.9,15.4,59.9,15.3z M52.4,13.1
							c0.1,0.2,0.3,0.4,0.4,0.5c0.1,0.1,0.2,0.1,0.3,0.2c0.1,0.1,0.2,0.1,0.3,0.2c0.1,0.1,0.3,0.1,0.5,0.2c0.1,0,0.2,0.1,0.3,0.1
							s0.2,0,0.3,0.1c0.2,0,0.4,0.1,0.5,0.1c0.2,0,0.4,0,0.5,0c0.6,0,1.1-0.3,0.6-1.1c-0.1-0.1-0.1-0.2-0.2-0.2
							c-0.1-0.1-0.1-0.1-0.2-0.2c-0.1-0.1-0.3-0.2-0.5-0.2c-0.1,0-0.2,0-0.2-0.1c-0.1,0-0.2,0-0.2,0c-0.2,0-0.3,0-0.5,0
							s-0.3,0.1-0.5,0.1s-0.3,0.1-0.5,0.1C52.9,13.1,52.6,13.1,52.4,13.1z M57.1,12.1c-0.1-0.2-0.1-0.3-0.2-0.5
							c-0.1-0.2-0.2-0.3-0.4-0.5c-0.1-0.1-0.1-0.2-0.2-0.2c-0.1-0.1-0.1-0.2-0.2-0.2c-0.2-0.1-0.3-0.3-0.5-0.3
							c-0.1-0.1-0.2-0.1-0.4-0.2C55.1,10.1,55,10,54.8,10c-0.3-0.1-0.5-0.1-0.8-0.1c0.2,0.2,0.3,0.5,0.4,0.8
							c0.1,0.2,0.1,0.3,0.2,0.5c0.1,0.2,0.1,0.3,0.2,0.5c0.1,0.2,0.1,0.3,0.2,0.5c0,0.1,0.1,0.1,0.1,0.2c0,0.1,0.1,0.1,0.2,0.2
							c0.1,0.1,0.2,0.2,0.4,0.3c0.1,0,0.2,0.1,0.3,0.1s0.2,0.1,0.3,0.1C57.1,13.3,57.3,12.8,57.1,12.1z"/>
					</defs>
					<clipPath id="SVGID_00000004515229253152443700000001223469619566005436_">
						<use xlink:href="#SVGID_1_"  style="overflow:visible;"/>
					</clipPath>
					<g style="clip-path:url(#SVGID_00000004515229253152443700000001223469619566005436_);">
						<path class="st4" d="M53.8,8.4c2.7,1.2,16.3,10.4,16.3,24.4c0,13.9-5,20.1-13.6,26.2"/>
						<path class="st5" d="M53.8,8.4c2.7,1.2,16.3,10.4,16.3,24.4c0,13.9-5,20.1-13.6,26.2"/>
						<path class="st6" d="M53.8,8.4c2.7,1.2,16.3,10.4,16.3,24.4c0,13.8-5,20.1-13.6,26.2"/>
						<path class="st7" d="M53.8,8.4c2.7,1.2,16.3,10.4,16.3,24.4c0,13.8-5,20.1-13.6,26.2"/>
						<path class="st8" d="M53.7,8.4C56.4,9.7,70,18.8,70,32.8c0,13.8-5,20.1-13.5,26.2"/>
						<path class="st9" d="M53.7,8.5C56.4,9.7,70,18.8,70,32.8c0,13.8-4.9,20-13.5,26.1"/>
						<path class="st10" d="M53.7,8.5C56.4,9.7,70,18.8,70,32.8c0,13.8-4.9,20-13.5,26.1"/>
						<path class="st11" d="M53.7,8.5C56.4,9.7,70,18.8,70,32.8c0,13.8-4.9,20-13.5,26.1"/>
						<path class="st12" d="M53.7,8.5C56.4,9.7,70,18.9,70,32.8c0,13.8-4.9,20-13.5,26.1"/>
						<path class="st13" d="M53.7,8.5c2.7,1.2,16.2,10.3,16.2,24.2c0,13.8-4.9,20-13.5,26.1"/>
						<path class="st14" d="M53.7,8.6c2.7,1.2,16.2,10.3,16.2,24.2c0,13.8-4.9,20-13.5,26.1"/>
						<path class="st15" d="M53.7,8.6c2.7,1.2,16.2,10.3,16.2,24.2c0,13.7-4.9,19.9-13.5,26"/>
						<path class="st16" d="M53.7,8.6c2.7,1.2,16.2,10.3,16.2,24.2c0,13.7-4.9,19.9-13.5,26"/>
						<path class="st17" d="M53.7,8.6c2.7,1.2,16.2,10.3,16.2,24.2c0,13.7-4.9,19.9-13.5,26"/>
						<path class="st18" d="M53.7,8.6c2.6,1.2,16.2,10.3,16.2,24.2c0,13.7-4.9,19.9-13.5,26"/>
						<path class="st19" d="M53.7,8.7C56.3,9.9,69.9,19,69.9,32.8c0,13.7-4.9,19.9-13.4,26"/>
						<path class="st20" d="M53.6,8.7C56.3,9.9,69.8,19,69.8,32.8c0,13.7-4.9,19.9-13.4,26"/>
						<path class="st21" d="M53.6,8.7C56.3,9.9,69.8,19,69.8,32.8c0,13.7-4.9,19.9-13.4,25.9"/>
						<path class="st22" d="M53.6,8.7C56.3,9.9,69.8,19,69.8,32.8c0,13.7-4.9,19.8-13.4,25.9"/>
						<path class="st23" d="M53.6,8.7C56.3,9.9,69.7,19,69.7,32.8c0,13.7-4.9,19.8-13.4,25.9"/>
						<path class="st24" d="M53.6,8.8c2.6,1.2,16.1,10.2,16.1,24c0,13.7-4.8,19.8-13.4,25.9"/>
						<path class="st25" d="M53.6,8.8c2.6,1.2,16.1,10.2,16.1,24c0,13.6-4.8,19.8-13.4,25.9"/>
						<path class="st26" d="M53.6,8.8c2.6,1.2,16.1,10.2,16.1,24c0,13.6-4.8,19.8-13.4,25.9"/>
						<path class="st27" d="M53.6,8.8c2.6,1.2,16.1,10.2,16.1,24c0,13.6-4.8,19.8-13.4,25.8"/>
						<path class="st28" d="M53.6,8.8c2.6,1.2,16.1,10.2,16.1,24c0,13.6-4.8,19.7-13.4,25.8"/>
						<path class="st29" d="M53.6,8.9c2.6,1.2,16,10.2,16,23.9c0,13.6-4.8,19.7-13.4,25.8"/>
						<path class="st30" d="M53.6,8.9c2.6,1.2,16,10.2,16,23.9c0,13.6-4.8,19.7-13.3,25.8"/>
						<path class="st31" d="M53.5,8.9c2.6,1.2,16,10.2,16,23.9c0,13.6-4.8,19.7-13.3,25.8"/>
						<path class="st32" d="M53.5,8.9c2.6,1.2,16,10.2,16,23.9c0,13.6-4.8,19.7-13.3,25.7"/>
						<path class="st33" d="M53.5,8.9c2.6,1.2,16,10.2,16,23.9c0,13.6-4.8,19.7-13.3,25.7"/>
						<path class="st34" d="M53.5,8.9c2.6,1.2,16,10.2,16,23.8c0,13.5-4.8,19.6-13.3,25.7"/>
						<path class="st35" d="M53.5,9c2.6,1.2,16,10.2,16,23.8c0,13.5-4.8,19.6-13.3,25.7"/>
						<path class="st36" d="M53.5,9c2.6,1.2,16,10.2,16,23.8c0,13.5-4.8,19.6-13.3,25.7"/>
						<path class="st37" d="M53.5,9c2.6,1.2,15.9,10.2,15.9,23.8c0,13.5-4.8,19.6-13.3,25.7"/>
						<path class="st38" d="M53.5,9c2.6,1.2,15.9,10.1,15.9,23.8c0,13.5-4.8,19.6-13.3,25.6"/>
						<path class="st39" d="M53.5,9c2.6,1.2,15.9,10.1,15.9,23.7c0,13.5-4.7,19.6-13.3,25.6"/>
						<path class="st40" d="M53.5,9.1c2.6,1.2,15.9,10.1,15.9,23.7c0,13.5-4.7,19.6-13.3,25.6"/>
						<path class="st41" d="M53.5,9.1c2.6,1.2,15.9,10.1,15.9,23.7c0,13.5-4.7,19.5-13.2,25.6"/>
						<path class="st42" d="M53.5,9.1c2.6,1.2,15.9,10.1,15.9,23.7c0,13.5-4.7,19.5-13.2,25.6"/>
						<path class="st43" d="M53.4,9.1c2.6,1.2,15.9,10.1,15.9,23.7c0,13.4-4.7,19.5-13.2,25.6"/>
						<path class="st44" d="M53.4,9.1c2.6,1.2,15.9,10.1,15.9,23.6c0,13.4-4.7,19.5-13.2,25.5"/>
						<path class="st45" d="M53.4,9.2c2.6,1.2,15.8,10.1,15.8,23.6c0,13.4-4.7,19.5-13.2,25.5"/>
						<path class="st46" d="M53.4,9.2c2.6,1.2,15.8,10.1,15.8,23.6c0,13.4-4.7,19.5-13.2,25.5"/>
						<path class="st47" d="M53.4,9.2c2.6,1.2,15.8,10.1,15.8,23.6c0,13.4-4.7,19.4-13.2,25.5"/>
						<path class="st48" d="M53.4,9.2c2.6,1.2,15.8,10.1,15.8,23.6c0,13.4-4.7,19.4-13.2,25.5"/>
						<path class="st49" d="M53.4,9.2c2.6,1.2,15.8,10.1,15.8,23.6c0,13.4-4.7,19.4-13.2,25.5"/>
						<path class="st50" d="M53.4,9.3c2.6,1.2,15.8,10.1,15.8,23.5S64.5,52.2,56,58.2"/>
						<path class="st51" d="M53.4,9.3c2.6,1.2,15.8,10.1,15.8,23.5S64.5,52.2,56,58.2"/>
						<path class="st52" d="M53.4,9.3c2.6,1.2,15.8,10,15.8,23.5c0,13.3-4.7,19.4-13.1,25.4"/>
						<path class="st53" d="M53.4,9.3c2.5,1.2,15.7,10,15.7,23.5c0,13.3-4.7,19.4-13.1,25.4"/>
						<path class="st54" d="M53.4,9.3c2.5,1.2,15.7,10,15.7,23.5c0,13.3-4.6,19.3-13.1,25.4"/>
						<path class="st55" d="M53.3,9.4c2.5,1.2,15.7,10,15.7,23.4c0,13.3-4.6,19.3-13.1,25.3"/>
						<path class="st56" d="M53.3,9.4c2.5,1.2,15.7,10,15.7,23.4c0,13.3-4.6,19.3-13.1,25.3"/>
						<path class="st57" d="M53.3,9.4c2.5,1.2,15.7,10,15.7,23.4c0,13.3-4.6,19.3-13.1,25.3"/>
						<path class="st58" d="M53.3,9.4c2.5,1.2,15.7,10,15.7,23.4c0,13.3-4.6,19.3-13.1,25.3"/>
						<path class="st59" d="M53.3,9.4c2.5,1.2,15.7,10,15.7,23.4c0,13.3-4.6,19.3-13.1,25.3"/>
						<path class="st60" d="M53.3,9.5c2.5,1.2,15.7,10,15.7,23.3S64.4,52,55.9,58.1"/>
						<path class="st61" d="M53.3,9.5c2.5,1.1,15.6,10,15.6,23.3c0,13.2-4.6,19.2-13,25.2"/>
						<path class="st62" d="M53.3,9.5c2.5,1.1,15.6,10,15.6,23.3c0,13.2-4.6,19.2-13,25.2"/>
						<path class="st63" d="M53.3,9.5c2.5,1.1,15.6,10,15.6,23.3C68.9,46,64.3,52,55.8,58"/>
						<path class="st64" d="M53.3,9.5c2.5,1.1,15.6,10,15.6,23.3c0,13.2-4.6,19.2-13,25.2"/>
						<path class="st65" d="M53.3,9.6c2.5,1.1,15.6,9.9,15.6,23.2c0,13.2-4.6,19.2-13,25.2"/>
						<path class="st66" d="M53.2,9.6c2.5,1.1,15.6,9.9,15.6,23.2c0,13.2-4.6,19.1-13,25.2"/>
						<path class="st67" d="M53.2,9.6c2.5,1.1,15.6,9.9,15.6,23.2c0,13.2-4.6,19.1-13,25.1"/>
						<path class="st68" d="M53.2,9.6c2.5,1.1,15.6,9.9,15.6,23.2c0,13.2-4.6,19.1-13,25.1"/>
						<path class="st69" d="M53.2,9.6c2.5,1.1,15.5,9.9,15.5,23.2c0,13.2-4.5,19.1-13,25.1"/>
						<path class="st70" d="M53.2,9.6c2.5,1.1,15.5,9.9,15.5,23.1s-4.5,19.1-13,25.1"/>
						<path class="st71" d="M53.2,9.7c2.5,1.1,15.5,9.9,15.5,23.1c0,13.1-4.5,19.1-13,25.1"/>
						<path class="st72" d="M53.2,9.7c2.5,1.1,15.5,9.9,15.5,23.1c0,13.1-4.5,19.1-13,25.1"/>
						<path class="st73" d="M53.2,9.7c2.5,1.1,15.5,9.9,15.5,23.1c0,13.1-4.5,19-13,25"/>
						<path class="st74" d="M53.2,9.7c2.5,1.1,15.5,9.9,15.5,23.1c0,13.1-4.5,19-13,25"/>
						<path class="st75" d="M53.2,9.7c2.5,1.1,15.5,9.9,15.5,23s-4.5,19-12.9,25"/>
						<path class="st76" d="M53.2,9.8c2.5,1.1,15.5,9.9,15.5,23s-4.5,19-12.9,25"/>
						<path class="st77" d="M53.2,9.8c2.5,1.1,15.4,9.9,15.4,23s-4.5,19-12.9,25"/>
						<path class="st78" d="M53.1,9.8c2.5,1.1,15.4,9.9,15.4,23s-4.5,19-12.9,24.9"/>
						<path class="st79" d="M53.1,9.8c2.5,1.1,15.4,9.8,15.4,23c0,13.1-4.5,18.9-12.9,24.9"/>
						<path class="st80" d="M53.1,9.8c2.5,1.1,15.4,9.8,15.4,23c0,13-4.5,18.9-12.9,24.9"/>
						<path class="st81" d="M53.1,9.9c2.5,1.1,15.4,9.8,15.4,22.9c0,13-4.5,18.9-12.9,24.9"/>
						<path class="st82" d="M53.1,9.9c2.5,1.1,15.4,9.8,15.4,22.9c0,13-4.5,18.9-12.9,24.9"/>
						<path class="st83" d="M53.1,9.9c2.5,1.1,15.4,9.8,15.4,22.9c0,13-4.5,18.9-12.9,24.9"/>
						<path class="st84" d="M53.1,9.9c2.5,1.1,15.4,9.8,15.4,22.9c0,13-4.4,18.9-12.9,24.8"/>
						<path class="st85" d="M53.1,9.9c2.5,1.1,15.3,9.8,15.3,22.9c0,13-4.4,18.8-12.9,24.8"/>
						<path class="st86" d="M53.1,10c2.5,1.1,15.3,9.8,15.3,22.8S64,51.6,55.6,57.6"/>
						<path class="st87" d="M53.1,10c2.5,1.1,15.3,9.8,15.3,22.8S64,51.6,55.6,57.6"/>
						<path class="st88" d="M53.1,10c2.4,1.1,15.3,9.8,15.3,22.8S64,51.6,55.6,57.6"/>
						<path class="st89" d="M53,10c2.4,1.1,15.3,9.8,15.3,22.8c0,12.9-4.4,18.8-12.8,24.8"/>
						<path class="st90" d="M53,10c2.4,1.1,15.3,9.8,15.3,22.8c0,12.9-4.4,18.8-12.8,24.7"/>
						<path class="st91" d="M53,10.1c2.4,1.1,15.3,9.8,15.3,22.7s-4.4,18.8-12.8,24.7"/>
						<path class="st92" d="M53,10.1c2.4,1.1,15.3,9.8,15.3,22.7s-4.4,18.7-12.8,24.7"/>
						<path class="st93" d="M53,10.1c2.4,1.1,15.2,9.7,15.2,22.7c0,12.9-4.4,18.7-12.8,24.7"/>
						<path class="st94" d="M53,10.1c2.4,1.1,15.2,9.7,15.2,22.7c0,12.9-4.4,18.7-12.8,24.7"/>
						<path class="st95" d="M53,10.1c2.4,1.1,15.2,9.7,15.2,22.7c0,12.9-4.4,18.7-12.8,24.7"/>
						<path class="st96" d="M53,10.2c2.4,1.1,15.2,9.7,15.2,22.6s-4.4,18.7-12.8,24.6"/>
						<path class="st97" d="M53,10.2c2.4,1.1,15.2,9.7,15.2,22.6s-4.4,18.7-12.7,24.6"/>
						<path class="st98" d="M53,10.2c2.4,1.1,15.2,9.7,15.2,22.6c0,12.8-4.4,18.6-12.7,24.6"/>
						<path class="st99" d="M53,10.2c2.4,1.1,15.2,9.7,15.2,22.6c0,12.8-4.4,18.6-12.7,24.6"/>
						<path class="st100" d="M53,10.2c2.4,1.1,15.2,9.7,15.2,22.6c0,12.8-4.3,18.6-12.7,24.6"/>
						<path class="st101" d="M52.9,10.2C55.3,11.3,68,19.9,68,32.7s-4.3,18.6-12.7,24.5"/>
						<path class="st102" d="M52.9,10.3C55.3,11.4,68,20,68,32.8s-4.3,18.6-12.7,24.5"/>
						<path class="st103" d="M52.9,10.3C55.3,11.4,68,20,68,32.8s-4.3,18.6-12.7,24.5"/>
						<path class="st104" d="M52.9,10.3C55.3,11.4,68,20,68,32.8s-4.3,18.5-12.7,24.5"/>
						<path class="st105" d="M52.9,10.3C55.3,11.4,68,20,68,32.8s-4.3,18.5-12.7,24.5"/>
						<path class="st106" d="M52.9,10.3C55.3,11.4,68,20,68,32.8s-4.3,18.5-12.7,24.5"/>
						<path class="st107" d="M52.9,10.4C55.3,11.5,68,20,68,32.8c0,12.7-4.3,18.5-12.7,24.4"/>
						<path class="st108" d="M52.9,10.4C55.3,11.5,68,20,68,32.8c0,12.7-4.3,18.5-12.6,24.4"/>
						<path class="st109" d="M52.9,10.4c2.4,1.1,15,9.6,15,22.4c0,12.7-4.3,18.5-12.6,24.4"/>
						<path class="st110" d="M52.9,10.4c2.4,1.1,15,9.6,15,22.4c0,12.7-4.3,18.5-12.6,24.4"/>
						<path class="st111" d="M52.9,10.4c2.4,1.1,15,9.6,15,22.4c0,12.7-4.3,18.4-12.6,24.4"/>
						<path class="st112" d="M52.8,10.5c2.4,1.1,15,9.6,15,22.3s-4.3,18.4-12.6,24.4"/>
						<path class="st113" d="M52.8,10.5c2.4,1.1,15,9.6,15,22.3s-4.3,18.4-12.6,24.3"/>
						<path class="st114" d="M52.8,10.5c2.4,1.1,15,9.6,15,22.3s-4.3,18.4-12.6,24.3"/>
						<path class="st115" d="M52.8,10.5c2.4,1.1,15,9.6,15,22.3s-4.2,18.4-12.6,24.3"/>
						<path class="st116" d="M52.8,10.5c2.4,1.1,15,9.6,15,22.3c0,12.6-4.2,18.4-12.6,24.3"/>
						<path class="st117" d="M52.8,10.6c2.4,1.1,14.9,9.6,14.9,22.2s-4.2,18.3-12.6,24.3"/>
						<path class="st118" d="M52.8,10.6c2.4,1.1,14.9,9.6,14.9,22.2s-4.2,18.3-12.6,24.3"/>
						<path class="st119" d="M52.8,10.6c2.4,1.1,14.9,9.6,14.9,22.2S63.5,51.1,55.1,57"/>
						<path class="st120" d="M52.8,10.6c2.4,1.1,14.9,9.5,14.9,22.2c0,12.6-4.2,18.3-12.5,24.2"/>
						<path class="st121" d="M52.8,10.6c2.4,1.1,14.9,9.5,14.9,22.2c0,12.6-4.2,18.3-12.5,24.2"/>
						<path class="st122" d="M52.8,10.7c2.3,1.1,14.9,9.5,14.9,22.1S63.5,51.1,55.2,57"/>
						<path class="st123" d="M52.8,10.7c2.3,1.1,14.9,9.5,14.9,22.1S63.5,51.1,55.2,57"/>
						<path class="st124" d="M52.7,10.7c2.3,1.1,14.9,9.5,14.9,22.1S63.4,51,55.1,57"/>
						<path class="st125" d="M52.7,10.7c2.3,1.1,14.8,9.5,14.8,22.1S63.3,51,55,56.9"/>
						<path class="st126" d="M52.7,10.7c2.3,1.1,14.8,9.5,14.8,22.1c0,12.5-4.2,18.2-12.5,24.1"/>
						<path class="st127" d="M52.7,10.8c2.3,1.1,14.8,9.5,14.8,22S63.3,51,55,56.9"/>
						<path class="st128" d="M52.7,10.8c2.3,1.1,14.8,9.5,14.8,22S63.3,51,55,56.9"/>
						<path class="st129" d="M52.7,10.8c2.3,1.1,14.8,9.5,14.8,22S63.3,51,55,56.9"/>
						<path class="st130" d="M52.7,10.8c2.3,1.1,14.8,9.5,14.8,22s-4.1,18.1-12.5,24"/>
						<path class="st131" d="M52.7,10.8c2.3,1.1,14.8,9.5,14.8,22s-4.1,18.1-12.4,24"/>
						<path class="st132" d="M52.7,10.8c2.3,1.1,14.8,9.5,14.8,21.9c0,12.5-4.1,18.1-12.4,24"/>
						<path class="st133" d="M52.7,10.9C55,12,67.4,20.4,67.4,32.8c0,12.5-4.1,18.1-12.4,24"/>
						<path class="st134" d="M52.7,10.9C55,12,67.4,20.3,67.4,32.8s-4.1,18.1-12.4,24"/>
						<path class="st135" d="M52.6,10.9C55,12,67.4,20.3,67.4,32.8c0,12.4-4.1,18.1-12.4,24"/>
						<path class="st136" d="M52.6,10.9c2.3,1.1,14.7,9.4,14.7,21.9c0,12.4-4.1,18-12.4,23.9"/>
						<path class="st137" d="M52.6,10.9c2.3,1,14.7,9.4,14.7,21.8s-4.1,18-12.4,23.9"/>
						<path class="st138" d="M52.6,11c2.3,1,14.7,9.4,14.7,21.8s-4.1,18-12.4,23.9"/>
						<path class="st139" d="M52.6,11c2.3,1,14.7,9.4,14.7,21.8s-4.1,18-12.4,23.9"/>
						<path class="st140" d="M52.6,11c2.3,1,14.7,9.4,14.7,21.8s-4.1,18-12.4,23.9"/>
						<path class="st141" d="M52.6,11c2.3,1,14.6,9.4,14.6,21.8s-4.1,18-12.4,23.9"/>
						<path class="st142" d="M52.6,11c2.3,1,14.6,9.4,14.6,21.8s-4.1,18-12.3,23.8"/>
						<path class="st141" d="M52.6,11.1c2.3,1,14.6,9.4,14.6,21.7c0,12.4-4.1,17.9-12.3,23.8"/>
						<path class="st140" d="M52.6,11.1c2.3,1,14.6,9.4,14.6,21.7c0,12.4-4.1,17.9-12.3,23.8"/>
						<path class="st139" d="M52.6,11.1c2.3,1,14.6,9.4,14.6,21.7s-4.1,17.9-12.3,23.8"/>
						<path class="st138" d="M52.6,11.1c2.3,1,14.6,9.4,14.6,21.7s-4.1,17.9-12.3,23.8"/>
						<path class="st137" d="M52.5,11.1c2.3,1,14.6,9.3,14.6,21.7c0,12.3-4.1,17.9-12.3,23.8"/>
						<path class="st136" d="M52.5,11.2c2.3,1,14.6,9.3,14.6,21.6S63,50.7,54.8,56.5"/>
						<path class="st135" d="M52.5,11.2c2.3,1,14.6,9.3,14.6,21.6s-4,17.9-12.3,23.7"/>
						<path class="st134" d="M52.5,11.2c2.3,1,14.5,9.3,14.5,21.6s-4,17.9-12.3,23.7"/>
						<path class="st133" d="M52.5,11.2c2.3,1,14.5,9.3,14.5,21.6s-4,17.8-12.3,23.7"/>
						<path class="st132" d="M52.5,11.2c2.2,1,14.5,9.3,14.5,21.6s-4,17.8-12.3,23.7"/>
						<path class="st131" d="M52.5,11.3c2.2,1,14.5,9.3,14.5,21.5c0,12.3-4,17.8-12.2,23.6"/>
						<path class="st130" d="M52.5,11.3c2.2,1,14.5,9.3,14.5,21.5c0,12.3-4,17.8-12.2,23.6"/>
						<path class="st129" d="M52.5,11.3c2.2,1,14.5,9.3,14.5,21.5c0,12.3-4,17.8-12.2,23.6"/>
						<path class="st128" d="M52.5,11.3c2.2,1,14.5,9.3,14.5,21.5c0,12.3-4,17.8-12.2,23.6"/>
						<path class="st127" d="M52.5,11.3c2.2,1,14.5,9.3,14.5,21.5c0,12.3-4,17.8-12.2,23.6"/>
						<path class="st126" d="M52.4,11.4c2.2,1,14.4,9.3,14.4,21.4c0,12.3-4,17.7-12.2,23.6"/>
						<path class="st125" d="M52.4,11.4c2.2,1,14.4,9.3,14.4,21.4c0,12.3-4,17.7-12.2,23.5"/>
						<path class="st124" d="M52.4,11.4c2.2,1,14.4,9.2,14.4,21.4s-4,17.7-12.2,23.5"/>
						<path class="st123" d="M52.4,11.4c2.2,1,14.4,9.2,14.4,21.4s-4,17.7-12.2,23.5"/>
						<path class="st122" d="M52.4,11.4c2.2,1,14.4,9.2,14.4,21.4s-4,17.7-12.2,23.5"/>
						<path class="st121" d="M52.4,11.4c2.2,1,14.4,9.2,14.4,21.3c0,12.2-4,17.7-12.2,23.5"/>
						<path class="st120" d="M52.4,11.5c2.2,1,14.4,9.2,14.4,21.3c0,12.2-4,17.7-12.1,23.5"/>
						<path class="st119" d="M52.4,11.5c2.2,1,14.4,9.2,14.4,21.3c0,12.2-4,17.7-12.1,23.4"/>
						<path class="st118" d="M52.4,11.5c2.2,1,14.3,9.2,14.3,21.3c0,12.2-4,17.6-12.1,23.4"/>
						<path class="st117" d="M52.4,11.5c2.2,1,14.3,9.2,14.3,21.3c0,12.2-4,17.6-12.1,23.4"/>
						<path class="st116" d="M52.4,11.5c2.2,1,14.3,9.2,14.3,21.2c0,12.2-4,17.6-12.1,23.4"/>
						<path class="st115" d="M52.4,11.6c2.2,1,14.3,9.2,14.3,21.2c0,12.2-4,17.6-12.1,23.4"/>
						<path class="st114" d="M52.3,11.6c2.2,1,14.3,9.2,14.3,21.2c0,12.2-4,17.6-12.1,23.4"/>
						<path class="st113" d="M52.3,11.6c2.2,1,14.3,9.2,14.3,21.2c0,12.2-4,17.6-12.1,23.3"/>
						<path class="st112" d="M52.3,11.6c2.2,1,14.3,9.2,14.3,21.2c0,12.2-4,17.6-12.1,23.3"/>
						<path class="st111" d="M52.3,11.6c2.2,1,14.3,9.2,14.3,21.2c0,12.2-4,17.6-12.1,23.3"/>
						<path class="st110" d="M52.3,11.7c2.2,1,14.2,9.1,14.2,21.1c0,12.2-4,17.5-12.1,23.3"/>
						<path class="st109" d="M52.3,11.7c2.1,1,14.2,9.1,14.2,21.1c0,12.1-4,17.5-12.1,23.3"/>
						<path class="st108" d="M52.3,11.7c2.1,1,14.2,9.1,14.2,21.1c0,12.1-4,17.5-12,23.2"/>
						<path class="st107" d="M52.3,11.7c2.1,1,14.2,9.1,14.2,21.1c0,12.1-4,17.5-12,23.2"/>
						<path class="st106" d="M52.3,11.7c2.1,1,14.2,9.1,14.2,21.1c0,12.1-4,17.5-12,23.2"/>
						<path class="st105" d="M52.3,11.8c2.1,1,14.2,9.1,14.2,21c0,12.1-4,17.5-12,23.2"/>
						<path class="st104" d="M52.3,11.8c2.1,1,14.2,9.1,14.2,21c0,12.1-4,17.5-12,23.2"/>
						<path class="st103" d="M52.2,11.8c2.1,1,14.2,9.1,14.2,21c0,12.1-4,17.5-12,23.2"/>
						<path class="st102" d="M52.2,11.8c2.1,1,14.1,9.1,14.1,21c0,12.1-4,17.4-12,23.1"/>
						<path class="st101" d="M52.2,11.8c2.1,1,14.1,9.1,14.1,21c0,12.1-4,17.4-12,23.1"/>
						<path class="st100" d="M52.2,11.9c2.1,1,14.1,9.1,14.1,20.9c0,12.1-4,17.4-12,23.1"/>
						<path class="st99" d="M52.2,11.9c2.1,1,14.1,9.1,14.1,20.9c0,12.1-4,17.4-12,23.1"/>
						<path class="st98" d="M52.2,11.9c2.1,1,14.1,9.1,14.1,20.9c0,12.1-4,17.4-12,23.1"/>
						<path class="st97" d="M52.2,11.9c2.1,1,14.1,9,14.1,20.9c0,12.1-4,17.4-11.9,23.1"/>
						<path class="st96" d="M52.2,11.9c2.1,1,14.1,9,14.1,20.9c0,12.1-4,17.4-11.9,23"/>
						<path class="st95" d="M52.2,12c2.1,1,14.1,9,14.1,20.8c0,12.1-4,17.4-11.9,23"/>
						<path class="st94" d="M52.2,12c2.1,0.9,14,9,14,20.8c0,12-4,17.3-11.9,23"/>
						<path class="st93" d="M52.2,12c2.1,0.9,14,9,14,20.8c0,12-4,17.3-11.9,23"/>
						<path class="st92" d="M52.2,12c2.1,0.9,14,9,14,20.8c0,12-3.9,17.3-11.9,23"/>
						<path class="st91" d="M52.1,12c2.1,0.9,14,9,14,20.8c0,12-3.9,17.3-11.9,23"/>
						<path class="st90" d="M52.1,12c2.1,0.9,14,9,14,20.7c0,12-3.9,17.3-11.9,22.9"/>
						<path class="st89" d="M52.1,12.1c2.1,0.9,14,9,14,20.7c0,12-3.9,17.3-11.9,22.9"/>
						<path class="st88" d="M52.1,12.1c2.1,0.9,14,9,14,20.7c0,12-3.9,17.3-11.9,22.9"/>
						<path class="st87" d="M52.1,12.1c2.1,0.9,14,9,14,20.7c0,12-3.9,17.3-11.9,22.9"/>
						<path class="st86" d="M52.1,12.1c2,0.9,13.9,9,13.9,20.7c0,12-3.9,17.2-11.8,22.9"/>
						<path class="st85" d="M52.1,12.1c2,0.9,13.9,9,13.9,20.6c0,12-3.9,17.2-11.8,22.8"/>
						<path class="st84" d="M52.1,12.2c2,0.9,13.9,8.9,13.9,20.6c0,12-3.9,17.2-11.8,22.8"/>
						<path class="st83" d="M52.1,12.2c2,0.9,13.9,8.9,13.9,20.6c0,12-3.9,17.2-11.8,22.8"/>
						<path class="st82" d="M52.1,12.2c2,0.9,13.9,8.9,13.9,20.6c0,12-3.9,17.2-11.8,22.8"/>
						<path class="st81" d="M52.1,12.2c2,0.9,13.9,8.9,13.9,20.6c0,12-3.9,17.2-11.8,22.8"/>
						<path class="st80" d="M52,12.2c2,0.9,13.9,8.9,13.9,20.6c0,12-3.9,17.2-11.8,22.8"/>
						<path class="st79" d="M52,12.3c2,0.9,13.9,8.9,13.9,20.5c0,11.9-3.9,17.2-11.8,22.7"/>
						<path class="st78" d="M52,12.3c2,0.9,13.8,8.9,13.8,20.5c0,11.9-3.9,17.1-11.8,22.7"/>
						<path class="st77" d="M52,12.3c2,0.9,13.8,8.9,13.8,20.5c0,11.9-3.9,17.1-11.8,22.7"/>
						<path class="st76" d="M52,12.3c2,0.9,13.8,8.9,13.8,20.5c0,11.9-3.9,17.1-11.8,22.7"/>
						<path class="st75" d="M52,12.3c2,0.9,13.8,8.9,13.8,20.5c0,11.9-3.9,17.1-11.7,22.7"/>
						<path class="st74" d="M52,12.4c2,0.9,13.8,8.9,13.8,20.4c0,11.9-3.9,17.1-11.7,22.7"/>
						<path class="st143" d="M52,12.4c2,0.9,13.8,8.9,13.8,20.4c0,11.9-3.9,17.1-11.7,22.6"/>
						<path class="st72" d="M52,12.4c2,0.9,13.8,8.9,13.8,20.4c0,11.9-3.9,17.1-11.7,22.6"/>
						<path class="st71" d="M52,12.4c2,0.9,13.8,8.8,13.8,20.4c0,11.9-3.9,17.1-11.7,22.6"/>
						<path class="st70" d="M52,12.4c2,0.9,13.7,8.8,13.7,20.4c0,11.9-3.9,17-11.7,22.6"/>
						<path class="st69" d="M52,12.5c2,0.9,13.7,8.8,13.7,20.3c0,11.9-3.9,17-11.7,22.6"/>
						<path class="st68" d="M51.9,12.5c2,0.9,13.7,8.8,13.7,20.3c0,11.9-3.9,17-11.7,22.6"/>
						<path class="st67" d="M51.9,12.5c2,0.9,13.7,8.8,13.7,20.3c0,11.9-3.9,17-11.7,22.5"/>
						<path class="st66" d="M51.9,12.5c2,0.9,13.7,8.8,13.7,20.3c0,11.9-3.9,17-11.7,22.5"/>
						<path class="st65" d="M51.9,12.5c2,0.9,13.7,8.8,13.7,20.3c0,11.9-3.9,17-11.7,22.5"/>
						<path class="st64" d="M51.9,12.6c2,0.9,13.7,8.8,13.7,20.2c0,11.8-3.9,17-11.6,22.5"/>
						<path class="st63" d="M51.9,12.6c1.9,0.9,13.7,8.8,13.7,20.2c0,11.8-3.9,17-11.6,22.5"/>
						<path class="st62" d="M51.9,12.6c1.9,0.9,13.6,8.8,13.6,20.2c0,11.8-3.9,16.9-11.6,22.4"/>
						<path class="st61" d="M51.9,12.6c1.9,0.9,13.6,8.8,13.6,20.2c0,11.8-3.9,16.9-11.6,22.4"/>
						<path class="st60" d="M51.9,12.6c1.9,0.9,13.6,8.8,13.6,20.2c0,11.8-3.9,16.9-11.6,22.4"/>
						<path class="st59" d="M51.9,12.6c1.9,0.9,13.6,8.8,13.6,20.1c0,11.8-3.9,16.9-11.6,22.4"/>
						<path class="st58" d="M51.9,12.7c1.9,0.9,13.6,8.8,13.6,20.1c0,11.8-3.9,16.9-11.6,22.4"/>
						<path class="st57" d="M51.8,12.7c1.9,0.9,13.6,8.7,13.6,20.1c0,11.8-3.9,16.9-11.6,22.4"/>
						<path class="st56" d="M51.8,12.7c1.9,0.9,13.6,8.7,13.6,20.1c0,11.8-3.9,16.9-11.6,22.3"/>
						<path class="st55" d="M51.8,12.7c1.9,0.9,13.6,8.7,13.6,20.1c0,11.8-3.9,16.8-11.6,22.3"/>
						<path class="st54" d="M51.8,12.7c1.9,0.9,13.5,8.7,13.5,20c0,11.8-3.9,16.8-11.6,22.3"/>
						<path class="st53" d="M51.8,12.8c1.9,0.9,13.5,8.7,13.5,20c0,11.8-3.9,16.8-11.6,22.3"/>
						<path class="st52" d="M51.8,12.8c1.9,0.9,13.5,8.7,13.5,20c0,11.8-3.9,16.8-11.5,22.3"/>
						<path class="st51" d="M51.8,12.8c1.9,0.9,13.5,8.7,13.5,20c0,11.8-3.9,16.8-11.5,22.3"/>
						<path class="st50" d="M51.8,12.8c1.9,0.9,13.5,8.7,13.5,20c0,11.8-3.9,16.8-11.5,22.2"/>
						<path class="st49" d="M51.8,12.8c1.9,0.9,13.5,8.7,13.5,20c0,11.7-3.8,16.8-11.5,22.2"/>
						<path class="st48" d="M51.8,12.9c1.9,0.9,13.5,8.7,13.5,19.9c0,11.7-3.8,16.8-11.5,22.2"/>
						<path class="st47" d="M51.8,12.9c1.9,0.9,13.5,8.7,13.5,19.9c0,11.7-3.8,16.7-11.5,22.2"/>
						<path class="st46" d="M51.8,12.9c1.9,0.9,13.4,8.7,13.4,19.9c0,11.7-3.8,16.7-11.5,22.2"/>
						<path class="st45" d="M51.7,12.9c1.9,0.9,13.4,8.7,13.4,19.9c0,11.7-3.8,16.7-11.5,22.2"/>
						<path class="st44" d="M51.7,12.9c1.9,0.8,13.4,8.6,13.4,19.9c0,11.7-3.8,16.7-11.5,22.1"/>
						<path class="st43" d="M51.7,13c1.9,0.8,13.4,8.6,13.4,19.8c0,11.7-3.8,16.7-11.5,22.1"/>
						<path class="st42" d="M51.7,13c1.9,0.8,13.4,8.6,13.4,19.8c0,11.7-3.8,16.7-11.5,22.1"/>
						<path class="st41" d="M51.7,13c1.9,0.8,13.4,8.6,13.4,19.8c0,11.7-3.8,16.7-11.4,22.1"/>
						<path class="st40" d="M51.7,13c1.9,0.8,13.4,8.6,13.4,19.8c0,11.7-3.8,16.7-11.4,22.1"/>
						<path class="st39" d="M51.7,13c1.8,0.8,13.4,8.6,13.4,19.8c0,11.7-3.8,16.6-11.4,22"/>
						<path class="st38" d="M51.7,13.1C53.5,13.9,65,21.7,65,32.8c0,11.7-3.8,16.6-11.4,22"/>
						<path class="st37" d="M51.7,13.1C53.5,13.9,65,21.7,65,32.8c0,11.7-3.8,16.6-11.4,22"/>
						<path class="st36" d="M51.7,13.1C53.5,13.9,65,21.7,65,32.8c0,11.7-3.8,16.6-11.4,22"/>
						<path class="st35" d="M51.7,13.1C53.5,13.9,65,21.7,65,32.8c0,11.7-3.8,16.6-11.4,22"/>
						<path class="st34" d="M51.6,13.1c1.8,0.8,13.3,8.6,13.3,19.7c0,11.6-3.8,16.6-11.4,22"/>
						<path class="st33" d="M51.6,13.2c1.8,0.8,13.3,8.6,13.3,19.6c0,11.6-3.8,16.6-11.4,21.9"/>
						<path class="st32" d="M51.6,13.2c1.8,0.8,13.3,8.6,13.3,19.6c0,11.6-3.8,16.6-11.4,21.9"/>
						<path class="st31" d="M51.6,13.2c1.8,0.8,13.3,8.5,13.3,19.6c0,11.6-3.8,16.5-11.4,21.9"/>
						<path class="st30" d="M51.6,13.2c1.8,0.8,13.2,8.5,13.2,19.6c0,11.6-3.8,16.5-11.3,21.9"/>
						<path class="st29" d="M51.6,13.2c1.8,0.8,13.2,8.5,13.2,19.6c0,11.6-3.8,16.5-11.3,21.9"/>
						<path class="st28" d="M51.6,13.2c1.8,0.8,13.2,8.5,13.2,19.5c0,11.6-3.8,16.5-11.3,21.9"/>
						<path class="st27" d="M51.6,13.3c1.8,0.8,13.2,8.5,13.2,19.5c0,11.6-3.8,16.5-11.3,21.8"/>
						<path class="st26" d="M51.6,13.3c1.8,0.8,13.2,8.5,13.2,19.5c0,11.6-3.8,16.5-11.3,21.8"/>
						<path class="st25" d="M51.6,13.3c1.8,0.8,13.2,8.5,13.2,19.5c0,11.6-3.8,16.5-11.3,21.8"/>
						<path class="st24" d="M51.6,13.3c1.8,0.8,13.2,8.5,13.2,19.5c0,11.6-3.8,16.5-11.3,21.8"/>
						<path class="st23" d="M51.6,13.3c1.8,0.8,13.2,8.5,13.2,19.4c0,11.6-3.8,16.4-11.3,21.8"/>
						<path class="st22" d="M51.5,13.4c1.8,0.8,13.1,8.5,13.1,19.4c0,11.6-3.8,16.4-11.3,21.8"/>
						<path class="st21" d="M51.5,13.4c1.8,0.8,13.1,8.5,13.1,19.4c0,11.6-3.8,16.4-11.3,21.7"/>
						<path class="st20" d="M51.5,13.4c1.8,0.8,13.1,8.5,13.1,19.4c0,11.6-3.8,16.4-11.3,21.7"/>
						<path class="st19" d="M51.5,13.4c1.8,0.8,13.1,8.5,13.1,19.4c0,11.5-3.8,16.4-11.2,21.7"/>
						<path class="st18" d="M51.5,13.4c1.8,0.8,13.1,8.4,13.1,19.4c0,11.5-3.8,16.4-11.2,21.7"/>
						<path class="st17" d="M51.5,13.5c1.8,0.8,13.1,8.4,13.1,19.3c0,11.5-3.8,16.4-11.2,21.7"/>
						<path class="st16" d="M51.5,13.5c1.7,0.8,13.1,8.4,13.1,19.3c0,11.5-3.8,16.4-11.2,21.7"/>
						<path class="st15" d="M51.5,13.5c1.7,0.8,13.1,8.4,13.1,19.3c0,11.5-3.8,16.3-11.2,21.6"/>
						<path class="st14" d="M51.5,13.5c1.7,0.8,13,8.4,13,19.3c0,11.5-3.8,16.3-11.2,21.6"/>
						<path class="st13" d="M51.5,13.5c1.7,0.8,13,8.4,13,19.3c0,11.5-3.8,16.3-11.2,21.6"/>
						<path class="st12" d="M51.5,13.6c1.7,0.8,13,8.4,13,19.2c0,11.5-3.8,16.3-11.2,21.6"/>
						<path class="st11" d="M51.4,13.6c1.7,0.8,13,8.4,13,19.2c0,11.5-3.8,16.3-11.2,21.6"/>
						<path class="st10" d="M51.4,13.6c1.7,0.8,13,8.4,13,19.2c0,11.5-3.8,16.3-11.2,21.5"/>
						<path class="st9" d="M51.4,13.6c1.7,0.8,13,8.4,13,19.2c0,11.5-3.8,16.3-11.2,21.5"/>
						<path class="st8" d="M51.4,13.6c1.7,0.8,13,8.4,13,19.2c0,11.5-3.8,16.3-11.1,21.5"/>
						<path class="st7" d="M51.4,13.7c1.7,0.8,13,8.4,13,19.1c0,11.5-3.7,16.2-11.1,21.5"/>
						<path class="st6" d="M51.4,13.7c1.7,0.8,12.9,8.4,12.9,19.1c0,11.5-3.7,16.2-11.1,21.5"/>
						<path class="st5" d="M51.4,13.7c1.7,0.8,12.9,8.4,12.9,19.1c0,11.5-3.7,16.2-11.1,21.5"/>
						<path class="st4" d="M53.2,54.2c7.4-5.2,11.1-10,11.1-21.4c0-10.7-11.2-18.3-12.9-19.1"/>
					</g>
				</g>
			</g>
			<g>
				<g>
					<defs>
						<path id="SVGID_00000082333856054881852740000016509613869802213524_" d="M28.6,55.6c0,0.1,0,0.2-0.1,0.3s-0.1,0.2-0.3,0.2
							c-0.1,0-0.1,0-0.2,0s-0.2,0-0.3,0c-0.4-0.1-0.7-0.3-0.9-0.5c-0.1-0.1-0.2-0.3-0.3-0.4c0-0.1-0.1-0.2-0.1-0.2
							c0-0.1,0-0.2-0.1-0.2c-0.1-0.3-0.1-0.7-0.2-1c0-0.3-0.1-0.6-0.2-0.8c0.2,0,0.4,0.1,0.6,0.2c0.2,0.1,0.4,0.2,0.6,0.4
							c0.1,0,0.1,0.1,0.2,0.2c0.1,0.1,0.1,0.1,0.2,0.2c0.1,0.1,0.2,0.3,0.3,0.4l0.1,0.1l0.1,0.1c0.1,0.1,0.1,0.2,0.1,0.2
							c0.1,0.2,0.2,0.3,0.2,0.5C28.5,55.3,28.6,55.5,28.6,55.6z M27.7,56.8c0-0.1-0.1-0.2-0.2-0.3c-0.2-0.4-0.5-0.6-0.9-0.7
							c-0.2-0.1-0.3-0.1-0.5-0.1c-0.1,0-0.2,0-0.3,0c-0.1,0-0.2,0-0.3,0c-0.3,0-0.7,0-1,0.1c-0.3,0-0.6,0-0.9-0.1
							c0.1,0.2,0.3,0.4,0.4,0.7c0.2,0.2,0.4,0.4,0.6,0.5c0.1,0.1,0.1,0.1,0.2,0.2c0.1,0,0.2,0.1,0.3,0.1c0.2,0.1,0.4,0.2,0.6,0.2
							c0.1,0,0.1,0,0.2,0s0.1,0,0.2,0s0.2,0,0.3,0.1c0.2,0,0.4,0,0.6,0s0.3,0,0.5-0.1c0.1,0,0.2-0.1,0.3-0.2c0.1-0.1,0.1-0.2,0-0.3
							C27.7,56.9,27.7,56.8,27.7,56.8z M23.7,51.9c0,0.1,0,0.2,0,0.3s0,0.2,0,0.3c0,0.2,0,0.3,0.1,0.5c0,0.1,0.1,0.2,0.1,0.2
							c0,0.1,0.1,0.2,0.1,0.2c0.1,0.1,0.2,0.3,0.3,0.4c0.1,0.1,0.1,0.1,0.2,0.2c0,0,0.1,0,0.1,0.1c0,0,0.1,0,0.1,0.1
							c0.8,0.3,1-0.2,0.9-0.9c0-0.1,0-0.2-0.1-0.2c0-0.1-0.1-0.2-0.1-0.3c-0.1-0.2-0.1-0.3-0.2-0.5c0-0.1-0.1-0.2-0.1-0.2
							c0-0.1-0.1-0.2-0.1-0.2c-0.1-0.1-0.2-0.3-0.3-0.4l-0.1-0.1l-0.1-0.1c-0.1-0.1-0.2-0.1-0.3-0.2c-0.2-0.1-0.4-0.2-0.6-0.3
							c0.1,0.2,0.1,0.5,0.1,0.8C23.7,51.5,23.7,51.7,23.7,51.9z M24.6,54.3l-0.1-0.1l-0.1-0.1c-0.1-0.1-0.1-0.2-0.2-0.2
							c-0.1-0.1-0.3-0.2-0.4-0.3c-0.1,0-0.2-0.1-0.2-0.1c-0.1,0-0.2-0.1-0.2-0.1c-0.2,0-0.3-0.1-0.5-0.1c-0.1,0-0.2,0-0.3,0
							c-0.1,0-0.2,0-0.3,0c-0.2,0-0.3,0-0.5,0c-0.3,0-0.6-0.1-0.9-0.2c0.1,0.2,0.2,0.5,0.3,0.7c0.1,0.1,0.1,0.2,0.2,0.3
							c0,0.1,0.1,0.1,0.1,0.2l0.1,0.1c0.1,0.1,0.3,0.2,0.5,0.4c0.1,0.1,0.2,0.1,0.3,0.2c0.1,0.1,0.2,0.1,0.3,0.1
							c0.2,0.1,0.4,0.2,0.6,0.2c0.1,0,0.2,0.1,0.3,0.1c0.1,0,0.2,0,0.3,0C24.6,55.4,25,55.1,24.6,54.3z M21.5,49.2
							c0,0.2,0,0.3,0,0.5s0,0.3,0,0.5s0.1,0.3,0.1,0.5c0.1,0.2,0.2,0.3,0.3,0.4c0.1,0.1,0.3,0.2,0.5,0.3s0.4,0.1,0.5,0.1
							s0.2,0,0.3-0.1c0.1-0.1,0.1-0.2,0.1-0.3s0-0.3,0-0.4c0-0.2,0-0.3-0.1-0.5c0-0.1,0-0.2-0.1-0.3c0-0.1-0.1-0.2-0.1-0.3
							c-0.1-0.2-0.1-0.3-0.2-0.5c0,0,0-0.1-0.1-0.1c0,0,0-0.1-0.1-0.1c0-0.1-0.1-0.1-0.1-0.2c-0.1-0.1-0.1-0.2-0.2-0.3
							s-0.2-0.2-0.2-0.2c-0.2-0.2-0.3-0.3-0.5-0.4c0,0.2,0,0.5-0.1,0.8C21.5,48.9,21.5,49,21.5,49.2z M21.6,52.7
							c0.1,0,0.2,0,0.3-0.1c0.1-0.1,0.1-0.2,0.1-0.3s0-0.3-0.1-0.5s-0.2-0.4-0.3-0.5c-0.1-0.1-0.2-0.3-0.4-0.4
							c-0.1-0.1-0.3-0.2-0.4-0.2c-0.2-0.1-0.3-0.1-0.5-0.2c-0.2,0-0.3-0.1-0.5-0.1s-0.3-0.1-0.5-0.1c-0.3-0.1-0.6-0.2-0.8-0.3
							c0,0.3,0.1,0.5,0.2,0.7c0.1,0.1,0.1,0.2,0.2,0.4c0.1,0.1,0.1,0.2,0.2,0.3s0.1,0.1,0.2,0.2l0.1,0.1l0.1,0.1
							c0.2,0.1,0.3,0.3,0.5,0.4c0.1,0.1,0.2,0.1,0.3,0.2c0.1,0.1,0.2,0.1,0.3,0.1c0.2,0.1,0.4,0.1,0.5,0.2
							C21.3,52.7,21.5,52.7,21.6,52.7z M20.1,48.6c0.7,0.5,1,0.1,1.1-0.6v-0.1v-0.1c0-0.1,0-0.2,0-0.3c0-0.2,0-0.4-0.1-0.6v-0.1
							v-0.1c0-0.1,0-0.2-0.1-0.3c-0.1-0.2-0.1-0.3-0.2-0.5s-0.2-0.4-0.4-0.6c-0.1-0.1-0.2-0.2-0.2-0.2c-0.1-0.1-0.2-0.2-0.3-0.2
							c0,0.1,0,0.2-0.1,0.4c0,0.1-0.1,0.3-0.1,0.4c0,0.2-0.1,0.3-0.1,0.5c0,0.1,0,0.2-0.1,0.2c0,0.1,0,0.2-0.1,0.3
							c-0.1,0.3-0.1,0.7,0,1C19.6,48,19.8,48.4,20.1,48.6z M18.7,49.6c0,0,0.1,0,0.1,0.1c0.7,0.3,1.1,0.1,0.9-0.8
							c-0.1-0.4-0.3-0.7-0.5-1c-0.2-0.2-0.5-0.4-0.9-0.5c-0.1,0-0.2-0.1-0.2-0.1c-0.1,0-0.2-0.1-0.2-0.1c-0.2-0.1-0.3-0.1-0.5-0.2
							c-0.2-0.1-0.3-0.1-0.4-0.2c-0.1-0.1-0.3-0.1-0.4-0.2c0,0.1,0,0.3,0,0.4c0,0.1,0.1,0.3,0.1,0.4c0.1,0.3,0.2,0.5,0.3,0.7
							c0.1,0.2,0.2,0.3,0.3,0.5c0.1,0.1,0.1,0.2,0.2,0.2l0.1,0.1l0.1,0.1c0.2,0.1,0.3,0.3,0.5,0.4c0.1,0.1,0.2,0.1,0.3,0.2
							C18.6,49.5,18.7,49.5,18.7,49.6z M18,45c0,0.1,0.1,0.2,0.2,0.2c0.1,0.1,0.1,0.2,0.2,0.2c0.3,0.3,0.6,0.4,0.8,0.3
							c0.2-0.1,0.3-0.4,0.4-0.7c0-0.2,0.1-0.3,0.1-0.5v-0.1v-0.1c0-0.1,0-0.2,0-0.3c0-0.2,0-0.4-0.1-0.5c0-0.1,0-0.2-0.1-0.3
							c0-0.1,0-0.2-0.1-0.2c0-0.1-0.1-0.2-0.1-0.3s-0.1-0.1-0.1-0.1l-0.1-0.1c-0.1-0.2-0.3-0.4-0.4-0.5c0,0.2-0.2,0.5-0.3,0.8
							c-0.1,0.1-0.1,0.3-0.2,0.4c-0.1,0.2-0.1,0.3-0.2,0.5s-0.1,0.3-0.1,0.5s0,0.3,0,0.5C17.9,44.7,18,44.8,18,45z M17.7,46.4
							c0.2-0.1,0.3-0.3,0.3-0.8c0-0.1,0-0.2,0-0.3s0-0.2-0.1-0.3c-0.1-0.2-0.1-0.3-0.2-0.5c-0.1-0.1-0.2-0.3-0.4-0.4
							c-0.1,0-0.3-0.1-0.4-0.1c-0.1-0.1-0.3-0.2-0.5-0.3s-0.3-0.2-0.5-0.2c-0.3-0.2-0.5-0.3-0.7-0.5c0,0.3,0,0.5,0,0.8
							c0,0.1,0,0.1,0,0.2c0,0.1,0,0.1,0,0.2c0,0.1,0.1,0.3,0.1,0.4c0,0.1,0.1,0.2,0.1,0.3s0.1,0.2,0.1,0.3c0.1,0.2,0.2,0.3,0.4,0.5
							c0.1,0.1,0.1,0.2,0.2,0.2l0.1,0.1l0.1,0.1c0.2,0.1,0.3,0.2,0.5,0.3C17.3,46.4,17.5,46.5,17.7,46.4z M18.3,41.8
							c0.1-0.2,0.1-0.3,0.1-0.5s0.1-0.4,0.1-0.6c0-0.2,0-0.4,0-0.5c0-0.2,0-0.3-0.1-0.5c0-0.1,0-0.1,0-0.2c0-0.1,0-0.1,0-0.2
							c0-0.1-0.1-0.2-0.1-0.3s-0.1-0.2-0.2-0.3c0,0,0,0,0-0.1l0,0l0,0l0,0c0,0,0,0.1,0,0l0,0l0,0l0,0l0,0l0,0c0-0.1-0.1-0.1-0.1-0.2
							c0,0.1,0,0.1-0.1,0.2l0,0l0,0l0,0l0,0c0,0,0-0.1,0,0l0,0l0,0l0,0v0.1c-0.1,0.1-0.1,0.3-0.2,0.4c-0.1,0.1-0.2,0.3-0.3,0.4
							c0,0.1-0.1,0.1-0.1,0.2c0,0.1-0.1,0.1-0.1,0.2c-0.1,0.3-0.3,0.6-0.3,1c0,0.3,0.1,0.7,0.4,1C17.7,42.8,18.1,42.4,18.3,41.8z
							 M15.6,42.7c0.6,0.4,1.1,0.4,1.1-0.5c0-0.4,0-0.8-0.2-1.1s-0.4-0.5-0.7-0.8c-0.1-0.1-0.1-0.1-0.2-0.2
							c-0.1-0.1-0.1-0.1-0.2-0.2c-0.1-0.1-0.3-0.2-0.4-0.3c-0.1-0.1-0.3-0.2-0.4-0.3l-0.1-0.1l0,0l0,0l0,0l0,0l0,0l0,0l0,0l0,0
							c0-0.1-0.1-0.1-0.1-0.2c0,0.1,0,0.1,0,0.2l0,0l0,0l0,0l0,0l0,0l0,0l0,0l0,0l0,0v0.1c0,0.1,0,0.3,0,0.4c0,0.1,0,0.3,0,0.4
							c0,0.1,0,0.1,0,0.2c0,0.1,0,0.1,0,0.2c0,0.2,0.1,0.4,0.2,0.5c0.1,0.2,0.2,0.4,0.3,0.6c0.1,0.2,0.2,0.3,0.4,0.5
							C15.3,42.5,15.4,42.6,15.6,42.7z M16.6,38.9L16.6,38.9C16.6,39,16.6,39,16.6,38.9C16.8,39,16.9,39,17,39s0.2-0.1,0.3-0.2l0,0
							l0,0l0,0l0,0l0,0l0,0l0,0l0,0c0,0,0-0.1,0.1-0.1c0-0.1,0.1-0.1,0.1-0.2c0.1-0.1,0.1-0.3,0.2-0.5c0-0.1,0.1-0.2,0.1-0.3v-0.1
							c0,0,0,0,0-0.1v-0.1c0-0.2,0.1-0.4,0.1-0.5s0-0.2,0-0.2c0-0.1,0-0.2,0-0.2c0-0.2-0.1-0.5-0.1-0.7c0-0.1-0.1-0.2-0.1-0.3
							s-0.1-0.2-0.1-0.3c-0.1,0.1-0.1,0.2-0.2,0.3s-0.2,0.2-0.3,0.3c-0.2,0.2-0.4,0.5-0.6,0.8c0,0.1-0.1,0.1-0.1,0.2
							c0,0.1-0.1,0.1-0.1,0.2c-0.1,0.2-0.1,0.3-0.1,0.5c0,0.1,0,0.2,0,0.3v0.1V38c0,0.2,0.1,0.4,0.2,0.5s0.1,0.2,0.2,0.2
							C16.5,38.9,16.5,38.9,16.6,38.9L16.6,38.9L16.6,38.9L16.6,38.9L16.6,38.9L16.6,38.9L16.6,38.9z M14.7,39
							c0.1,0.1,0.1,0.1,0.2,0.2c0,0,0.1,0,0.1,0.1l0,0l0,0l0,0l0,0l0,0l0,0l0,0l0,0c0.1,0.1,0.2,0.1,0.3,0.1s0.2-0.1,0.3-0.2l0,0
							l0,0l0,0l0,0l0,0l0,0l0,0l0,0c0,0,0-0.1,0.1-0.1c0-0.1,0.1-0.2,0.1-0.3c0-0.2,0.1-0.4,0-0.6v-0.1V38c0-0.1,0-0.2-0.1-0.2
							c-0.1-0.2-0.1-0.3-0.2-0.4c-0.1-0.1-0.1-0.1-0.2-0.2c-0.1-0.1-0.1-0.1-0.2-0.2c-0.2-0.3-0.5-0.5-0.7-0.7
							c-0.1-0.1-0.2-0.2-0.3-0.4c-0.1-0.1-0.2-0.2-0.2-0.4c0,0.1-0.1,0.2-0.1,0.4c0,0.1-0.1,0.3-0.1,0.4c0,0.3,0,0.5,0,0.8
							c0,0.1,0,0.2,0,0.3s0,0.2,0.1,0.3c0.1,0.2,0.1,0.4,0.2,0.6c0,0,0,0.1,0.1,0.1c0,0,0,0,0,0.1v0.1c0.1,0.1,0.1,0.2,0.2,0.2
							C14.5,38.7,14.6,38.8,14.7,39z M15.9,33.9c0,0.2-0.1,0.3-0.1,0.5c0,0.1,0,0.2,0,0.3s0.1,0.2,0.1,0.3c0.4,0.8,0.8,0.6,1.2,0.1
							c0-0.1,0.1-0.1,0.1-0.2c0-0.1,0.1-0.2,0.1-0.2c0.1-0.2,0.2-0.3,0.2-0.5c0-0.1,0.1-0.2,0.1-0.3v-0.1v-0.1
							c0-0.2,0.1-0.3,0.1-0.5c0-0.1,0-0.2,0-0.3s0-0.2,0-0.3c0-0.2-0.1-0.4-0.2-0.7c-0.1,0.2-0.3,0.4-0.6,0.6
							c-0.1,0.1-0.2,0.2-0.4,0.3c-0.1,0.1-0.2,0.2-0.4,0.4c-0.1,0.1-0.2,0.3-0.3,0.4c0,0.1-0.1,0.1-0.1,0.2
							C16,33.8,15.9,33.8,15.9,33.9z M13.8,33.8c0,0.1,0.1,0.2,0.1,0.3c0.1,0.2,0.1,0.4,0.2,0.6c0,0.1,0.1,0.2,0.1,0.3
							s0.1,0.2,0.2,0.2c0.4,0.6,0.9,0.7,1.2-0.1c0-0.1,0.1-0.2,0.1-0.3s0-0.2,0-0.3c0-0.2,0-0.4,0-0.5s0-0.2-0.1-0.2
							c0-0.1-0.1-0.2-0.1-0.2c-0.1-0.2-0.2-0.3-0.3-0.4c-0.1-0.1-0.2-0.3-0.3-0.4c-0.1-0.1-0.2-0.3-0.3-0.4
							c-0.2-0.3-0.4-0.5-0.5-0.8c-0.1,0.2-0.2,0.5-0.3,0.7c0,0.1-0.1,0.3-0.1,0.4c0,0.1,0,0.3,0,0.4c0,0.2,0,0.4,0,0.6
							c0,0.1,0,0.1,0,0.2C13.7,33.7,13.7,33.7,13.8,33.8z M16.5,29.9c-0.1,0.1-0.2,0.3-0.3,0.4c0,0.1-0.1,0.2-0.1,0.2
							c0,0.1,0,0.2,0,0.3c0,0.2,0,0.4,0.1,0.6c0,0.1,0.1,0.2,0.1,0.3c0,0.1,0.1,0.1,0.1,0.2c0.1,0.1,0.2,0.1,0.3,0.2
							c0.1,0,0.2,0,0.3-0.1c0.1-0.1,0.2-0.2,0.3-0.3c0.1-0.1,0.2-0.3,0.3-0.4c0.1-0.1,0.1-0.2,0.2-0.2c0-0.1,0.1-0.2,0.1-0.2
							c0.1-0.2,0.2-0.3,0.2-0.5c0.1-0.2,0.1-0.3,0.1-0.5c0.1-0.2,0.1-0.5,0.1-0.7c0-0.1,0-0.2,0-0.3c0-0.1,0-0.2-0.1-0.3
							c-0.1,0.1-0.2,0.2-0.3,0.3c-0.1,0.1-0.2,0.2-0.4,0.2c-0.3,0.2-0.6,0.4-0.8,0.6C16.7,29.7,16.6,29.8,16.5,29.9z M14.2,30.5
							c0,0.1,0.1,0.2,0.1,0.3c0.1,0.2,0.1,0.4,0.2,0.5c0.1,0.2,0.2,0.3,0.3,0.4s0.2,0.2,0.3,0.2s0.2,0,0.3-0.1
							c0.1,0,0.1-0.1,0.2-0.2c0.1-0.1,0.1-0.2,0.2-0.3c0.1-0.2,0.2-0.4,0.2-0.6c0-0.1,0-0.2,0-0.3c0-0.1,0-0.2,0-0.3
							c0-0.2-0.1-0.3-0.1-0.5c-0.1-0.2-0.1-0.3-0.2-0.5c-0.2-0.3-0.3-0.6-0.5-0.9c-0.1-0.1-0.2-0.3-0.2-0.4
							c-0.1-0.1-0.1-0.3-0.1-0.4c-0.1,0.1-0.2,0.2-0.2,0.3c-0.1,0.1-0.1,0.2-0.2,0.3c-0.1,0.2-0.2,0.5-0.2,0.8c0,0.2,0,0.4,0,0.6
							s0,0.4,0,0.6C14.2,30.3,14.2,30.4,14.2,30.5z M17.4,26.4c-0.1,0.1-0.1,0.1-0.2,0.2s-0.1,0.1-0.1,0.2c-0.1,0.1-0.2,0.3-0.2,0.5
							c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0,0.3c0.1,0.9,0.6,0.8,1.1,0.4c0.1,0,0.1-0.1,0.2-0.2s0.1-0.1,0.2-0.2s0.3-0.3,0.4-0.4
							c0.2-0.3,0.4-0.6,0.5-0.9c0-0.1,0-0.1,0.1-0.2v-0.1v-0.1c0-0.1,0.1-0.2,0.1-0.3c0-0.1,0-0.2,0-0.3c0-0.1,0-0.2,0-0.3
							c-0.1,0.1-0.2,0.1-0.3,0.2c-0.1,0.1-0.3,0.1-0.4,0.2s-0.3,0.1-0.5,0.2c-0.1,0-0.2,0.1-0.2,0.1c-0.1,0-0.1,0.1-0.2,0.1
							C17.7,26.2,17.5,26.3,17.4,26.4z M15.1,27c0,0.1,0,0.2,0.1,0.3c0,0.1,0.1,0.2,0.1,0.3c0.2,0.7,0.7,0.9,1.2,0.2
							c0.1-0.1,0.1-0.2,0.2-0.3c0-0.1,0.1-0.2,0.1-0.3c0.1-0.2,0.1-0.3,0.1-0.5c0-0.1,0-0.2,0-0.3c0-0.1,0-0.2,0-0.3
							c0-0.2-0.1-0.3-0.1-0.5c0-0.1-0.1-0.2-0.1-0.3c0-0.1-0.1-0.2-0.1-0.2c-0.1-0.2-0.1-0.3-0.2-0.5s-0.1-0.3-0.1-0.5
							c0-0.1-0.1-0.3-0.1-0.4c-0.1,0.1-0.2,0.2-0.3,0.3c-0.1,0.1-0.2,0.2-0.2,0.3c-0.1,0.1-0.1,0.2-0.2,0.3v0.1v0.1
							c0,0.1,0,0.1-0.1,0.2c-0.1,0.3-0.2,0.8-0.2,1.2C15,26.6,15.1,26.8,15.1,27z M18.3,25.2c0.1,0,0.2,0,0.3,0c0.1,0,0.1,0,0.2-0.1
							c0.1,0,0.1-0.1,0.2-0.1c0.1-0.1,0.3-0.2,0.4-0.3c0.1-0.1,0.1-0.1,0.2-0.2s0.1-0.1,0.2-0.2s0.3-0.3,0.4-0.4s0.1-0.1,0.1-0.2
							s0.1-0.1,0.1-0.2c0.1-0.2,0.2-0.4,0.3-0.6c0.1-0.2,0.1-0.4,0.1-0.7c-0.2,0.1-0.5,0.2-0.8,0.3c-0.3,0.1-0.6,0.2-0.9,0.3
							c-0.3,0.1-0.6,0.3-0.8,0.6c-0.1,0.1-0.1,0.1-0.2,0.2c0,0.1-0.1,0.2-0.1,0.2c-0.1,0.2-0.1,0.4-0.1,0.6
							C18,24.9,18.1,25.1,18.3,25.2z M16.5,23.9c0,0.1,0,0.2,0.1,0.2c0,0.1,0.1,0.1,0.1,0.2c0.1,0.1,0.1,0.2,0.2,0.2
							c0.2,0.1,0.4,0,0.8-0.3c0.2-0.2,0.3-0.3,0.3-0.5c0-0.1,0.1-0.2,0.1-0.2c0-0.1,0.1-0.2,0.1-0.2c0.1-0.3,0-0.7,0-1
							c-0.1-0.3-0.2-0.7-0.2-1c-0.1-0.3-0.1-0.6-0.1-0.9c-0.2,0.1-0.4,0.3-0.6,0.5s-0.3,0.4-0.4,0.7c0,0.1-0.1,0.2-0.1,0.3
							c0,0.1-0.1,0.2-0.1,0.3c-0.1,0.2-0.1,0.4-0.1,0.6c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0,0.3C16.4,23.6,16.4,23.8,16.5,23.9z
							 M19.7,21c0,0.1-0.1,0.2-0.1,0.3c-0.1,0.9,0.4,1,1,0.7c0.1,0,0.1-0.1,0.2-0.1s0.2-0.1,0.2-0.1c0.2-0.1,0.3-0.2,0.5-0.3
							c0.1-0.1,0.3-0.2,0.4-0.4c0.1-0.1,0.2-0.2,0.3-0.4c0.1-0.1,0.1-0.2,0.2-0.3c0.1-0.1,0.1-0.2,0.2-0.3c0-0.1,0.1-0.2,0.1-0.3
							c0-0.1,0.1-0.2,0.1-0.3c-0.1,0-0.2,0.1-0.4,0.1c-0.1,0-0.3,0.1-0.4,0.1c-0.2,0-0.3,0-0.5,0.1c-0.2,0-0.3,0.1-0.5,0.1
							c-0.3,0.1-0.6,0.2-0.9,0.4c-0.1,0.1-0.2,0.2-0.3,0.4C19.8,20.8,19.8,20.9,19.7,21z M19.3,21c0.1-0.1,0.2-0.1,0.2-0.2
							c0.1-0.1,0.1-0.1,0.2-0.2s0.2-0.3,0.2-0.5c0.1-0.3,0.1-0.7,0.1-1c0-0.2,0-0.4,0-0.5c0-0.2,0-0.3,0-0.5s0-0.3,0-0.5
							s0-0.3,0.1-0.4c-0.1,0.1-0.2,0.1-0.3,0.2c-0.1,0.1-0.2,0.2-0.3,0.2c-0.1,0.1-0.2,0.2-0.3,0.3c-0.1,0.1-0.2,0.2-0.3,0.3
							c-0.1,0.1-0.2,0.3-0.3,0.5c-0.1,0.2-0.2,0.4-0.2,0.6c-0.1,0.2-0.1,0.4-0.1,0.6c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0,0.3
							C18.2,21.2,18.6,21.6,19.3,21z M22,17.8c-0.1,0.1-0.1,0.2-0.1,0.2c0,0.1-0.1,0.2-0.1,0.3c-0.1,0.4-0.1,0.7,0.1,0.8
							c0.2,0.1,0.4,0.1,0.8,0.1c0.2,0,0.3-0.1,0.5-0.2c0,0,0.1,0,0.1-0.1c0,0,0.1,0,0.1-0.1c0.1,0,0.2-0.1,0.3-0.1
							c0.2-0.1,0.3-0.2,0.5-0.3c0.1-0.1,0.3-0.2,0.4-0.3c0.3-0.3,0.6-0.7,0.7-1.1c-0.2,0.1-0.5,0.1-0.8,0.1c-0.1,0-0.2,0-0.2,0
							c-0.1,0-0.2,0-0.2,0c-0.2,0-0.3,0-0.5,0s-0.3,0.1-0.5,0.1c-0.2,0.1-0.3,0.1-0.4,0.2C22.2,17.5,22.1,17.6,22,17.8z M21.4,18
							c0.1-0.1,0.2-0.1,0.3-0.2c0.1-0.1,0.1-0.1,0.2-0.2s0.2-0.3,0.3-0.4c0.1-0.2,0.1-0.3,0.2-0.5c0-0.2,0.1-0.3,0.1-0.5
							c0-0.2,0-0.4,0-0.5s0-0.2,0-0.3s0-0.2,0-0.3c0-0.3,0.1-0.6,0.2-0.9c-0.5,0.2-0.9,0.5-1.3,0.9c-0.1,0.1-0.2,0.3-0.4,0.5
							c-0.1,0.2-0.2,0.4-0.3,0.6c0,0.1-0.1,0.2-0.1,0.3v0.1c0,0.1,0,0.1,0,0.1c-0.1,0.2-0.1,0.4-0.1,0.6c0,0.3,0,0.6,0.2,0.8
							C20.7,18.3,21,18.2,21.4,18z M26.4,16.2c0.1,0,0.2-0.1,0.2-0.1c0.1,0,0.1-0.1,0.2-0.1c0.2-0.1,0.3-0.3,0.5-0.5l0.1-0.1
							l0.1-0.1c0.1-0.1,0.1-0.2,0.2-0.3c-0.2,0-0.5,0-0.8,0s-0.7-0.1-1-0.1s-0.7,0-1,0.2c-0.2,0.1-0.3,0.2-0.4,0.3s-0.2,0.3-0.3,0.5
							c0,0.1-0.1,0.2-0.1,0.3s0,0.2,0,0.2c0,0.1,0,0.2,0.1,0.3c0.2,0.1,0.4,0.2,0.7,0.2c0.3,0,0.7-0.1,1-0.3
							C26.1,16.3,26.3,16.3,26.4,16.2z M23.4,15.5c0.1,0,0.1,0,0.2,0s0.2,0,0.3-0.1c0.2-0.1,0.4-0.2,0.5-0.3s0.3-0.2,0.4-0.4
							c0.2-0.3,0.3-0.6,0.4-1c0.1-0.3,0.1-0.7,0.2-1s0.1-0.6,0.3-0.9c-0.1,0-0.2,0.1-0.4,0.1c-0.1,0-0.1,0-0.2,0.1
							c-0.1,0-0.1,0.1-0.2,0.1c-0.2,0.1-0.5,0.3-0.7,0.4c-0.1,0.1-0.1,0.1-0.2,0.2c-0.1,0.1-0.1,0.1-0.2,0.2
							c-0.1,0.2-0.3,0.3-0.4,0.5c-0.2,0.4-0.4,0.8-0.5,1.1s0,0.6,0.1,0.8C23.1,15.4,23.2,15.5,23.4,15.5z M29.8,13
							c-0.2,0-0.3-0.1-0.5-0.1s-0.3-0.1-0.5-0.1s-0.3,0-0.5,0c-0.1,0-0.2,0-0.2,0c-0.1,0-0.2,0-0.2,0.1c-0.2,0.1-0.3,0.1-0.5,0.2
							c-0.1,0.1-0.1,0.1-0.2,0.2c-0.1,0.1-0.1,0.2-0.2,0.2c-0.5,0.7,0,1,0.6,1.1c0.2,0,0.3,0,0.5,0s0.4,0,0.5-0.1
							c0.1,0,0.2,0,0.3-0.1c0.1,0,0.2-0.1,0.3-0.1c0.2-0.1,0.3-0.1,0.5-0.2c0.1-0.1,0.2-0.1,0.3-0.2c0.1-0.1,0.2-0.1,0.3-0.2
							c0.2-0.1,0.3-0.3,0.4-0.5C30.3,13.1,30.1,13.1,29.8,13z M26.7,13.1c0.1,0,0.2-0.1,0.3-0.1c0.1,0,0.2-0.1,0.3-0.1
							c0.2-0.1,0.3-0.2,0.4-0.3s0.1-0.1,0.2-0.2c0-0.1,0.1-0.1,0.1-0.2c0.1-0.1,0.2-0.3,0.2-0.5c0.1-0.2,0.1-0.3,0.2-0.5
							s0.1-0.3,0.2-0.5c0.1-0.3,0.2-0.6,0.4-0.8c-0.3,0-0.5,0.1-0.8,0.1c-0.1,0-0.2,0.1-0.4,0.1c-0.1,0.1-0.2,0.1-0.4,0.2
							c-0.2,0.1-0.3,0.2-0.5,0.3c-0.1,0.1-0.2,0.1-0.2,0.2c-0.1,0.1-0.1,0.2-0.2,0.2c-0.1,0.2-0.3,0.3-0.4,0.5s-0.2,0.4-0.2,0.5
							C25.7,12.8,25.8,13.3,26.7,13.1z"/>
					</defs>
					<clipPath id="SVGID_00000159460265089473278430000015321025939754289795_">
						<use xlink:href="#SVGID_00000082333856054881852740000016509613869802213524_"  style="overflow:visible;"/>
					</clipPath>
					<g style="clip-path:url(#SVGID_00000159460265089473278430000015321025939754289795_);">
						<path class="st4" d="M29.2,8.4c-2.7,1.2-16.3,10.4-16.3,24.4c0,13.9,5,20.1,13.6,26.2"/>
						<path class="st5" d="M29.2,8.4c-2.7,1.2-16.3,10.4-16.3,24.4c0,13.9,5,20.1,13.6,26.2"/>
						<path class="st6" d="M29.2,8.4c-2.7,1.2-16.3,10.4-16.3,24.4c0,13.8,5,20.1,13.6,26.2"/>
						<path class="st7" d="M29.2,8.4c-2.7,1.2-16.3,10.4-16.3,24.4c0,13.8,5,20.1,13.6,26.2"/>
						<path class="st8" d="M29.2,8.4c-2.7,1.2-16.3,10.4-16.3,24.3c0,13.8,5,20.1,13.5,26.2"/>
						<path class="st9" d="M29.2,8.5C26.6,9.7,13,18.8,13,32.8c0,13.8,4.9,20,13.5,26.1"/>
						<path class="st10" d="M29.3,8.5C26.6,9.7,13,18.8,13,32.8c0,13.8,4.9,20,13.5,26.1"/>
						<path class="st11" d="M29.3,8.5C26.6,9.7,13,18.8,13,32.8c0,13.8,4.9,20,13.5,26.1"/>
						<path class="st12" d="M29.3,8.5C26.6,9.7,13,18.9,13,32.8c0,13.8,4.9,20,13.5,26.1"/>
						<path class="st13" d="M29.3,8.5C26.6,9.8,13,18.9,13,32.8c0,13.8,4.9,20,13.5,26.1"/>
						<path class="st14" d="M29.3,8.6c-2.7,1.2-16.2,10.3-16.2,24.2c0,13.8,4.9,20,13.5,26.1"/>
						<path class="st15" d="M29.3,8.6c-2.7,1.2-16.2,10.3-16.2,24.2c0,13.7,4.9,19.9,13.5,26"/>
						<path class="st16" d="M29.3,8.6c-2.7,1.2-16.2,10.3-16.2,24.2c0,13.7,4.9,19.9,13.5,26"/>
						<path class="st17" d="M29.3,8.6c-2.7,1.2-16.2,10.3-16.2,24.2c0,13.7,4.9,19.9,13.5,26"/>
						<path class="st18" d="M29.3,8.6c-2.6,1.2-16.2,10.3-16.2,24.2c0,13.7,4.9,19.9,13.5,26"/>
						<path class="st19" d="M29.3,8.7C26.7,9.9,13.1,19,13.1,32.8c0,13.7,4.9,19.9,13.4,26"/>
						<path class="st20" d="M29.3,8.7C26.7,9.9,13.2,19,13.2,32.8c0,13.7,4.9,19.9,13.4,26"/>
						<path class="st21" d="M29.3,8.7C26.7,9.9,13.2,19,13.2,32.8c0,13.7,4.9,19.9,13.4,25.9"/>
						<path class="st22" d="M29.4,8.7C26.7,9.9,13.2,19,13.2,32.8c0,13.7,4.9,19.8,13.4,25.9"/>
						<path class="st23" d="M29.4,8.7C26.7,9.9,13.3,19,13.3,32.8c0,13.7,4.9,19.8,13.4,25.9"/>
						<path class="st24" d="M29.4,8.8C26.7,10,13.3,19,13.3,32.8c0,13.7,4.8,19.8,13.4,25.9"/>
						<path class="st25" d="M29.4,8.8C26.8,10,13.3,19,13.3,32.8c0,13.6,4.8,19.8,13.4,25.9"/>
						<path class="st26" d="M29.4,8.8C26.8,10,13.3,19,13.3,32.8c0,13.6,4.8,19.8,13.4,25.9"/>
						<path class="st27" d="M29.4,8.8C26.8,10,13.3,19,13.3,32.8c0,13.6,4.8,19.8,13.4,25.8"/>
						<path class="st28" d="M29.4,8.8C26.8,10,13.3,19,13.3,32.8c0,13.6,4.8,19.7,13.4,25.8"/>
						<path class="st29" d="M29.4,8.9c-2.6,1.2-16,10.2-16,23.9c0,13.6,4.8,19.7,13.4,25.8"/>
						<path class="st30" d="M29.4,8.9c-2.6,1.2-16,10.2-16,23.9c0,13.6,4.8,19.7,13.3,25.8"/>
						<path class="st31" d="M29.4,8.9c-2.6,1.2-16,10.2-16,23.9c0,13.6,4.8,19.7,13.3,25.8"/>
						<path class="st32" d="M29.4,8.9c-2.6,1.2-16,10.2-16,23.9c0,13.6,4.8,19.7,13.3,25.7"/>
						<path class="st33" d="M29.5,8.9c-2.6,1.2-16,10.2-16,23.9c0,13.6,4.8,19.7,13.3,25.7"/>
						<path class="st34" d="M29.5,8.9c-2.6,1.2-16,10.2-16,23.8c0,13.5,4.8,19.6,13.3,25.7"/>
						<path class="st35" d="M29.5,9c-2.6,1.2-16,10.2-16,23.8c0,13.5,4.8,19.6,13.3,25.7"/>
						<path class="st36" d="M29.5,9c-2.6,1.2-16,10.2-16,23.8c0,13.5,4.8,19.6,13.3,25.7"/>
						<path class="st37" d="M29.5,9c-2.6,1.2-15.9,10.2-15.9,23.8c0,13.5,4.8,19.6,13.3,25.7"/>
						<path class="st38" d="M29.5,9c-2.6,1.2-15.9,10.1-15.9,23.8c0,13.5,4.8,19.6,13.3,25.6"/>
						<path class="st39" d="M29.5,9c-2.6,1.2-15.9,10.1-15.9,23.7c0,13.5,4.7,19.6,13.3,25.6"/>
						<path class="st40" d="M29.5,9.1c-2.6,1.2-15.9,10.1-15.9,23.7c0,13.5,4.7,19.6,13.3,25.6"/>
						<path class="st41" d="M29.5,9.1c-2.6,1.2-15.9,10.1-15.9,23.7c0,13.5,4.7,19.5,13.2,25.6"/>
						<path class="st42" d="M29.5,9.1c-2.6,1.2-15.9,10.1-15.9,23.7c0,13.5,4.7,19.5,13.2,25.6"/>
						<path class="st43" d="M29.5,9.1C27,10.3,13.7,19.2,13.7,32.8c0,13.4,4.7,19.5,13.2,25.6"/>
						<path class="st44" d="M29.5,9.1C27,10.3,13.7,19.2,13.7,32.8c0,13.4,4.7,19.5,13.2,25.5"/>
						<path class="st45" d="M29.6,9.2C27,10.3,13.7,19.3,13.7,32.8c0,13.4,4.7,19.5,13.2,25.5"/>
						<path class="st46" d="M29.6,9.2C27,10.4,13.7,19.3,13.7,32.8c0,13.4,4.7,19.5,13.2,25.5"/>
						<path class="st47" d="M29.6,9.2C27,10.4,13.8,19.3,13.8,32.8c0,13.4,4.7,19.4,13.2,25.5"/>
						<path class="st48" d="M29.6,9.2C27,10.4,13.8,19.3,13.8,32.8c0,13.4,4.7,19.4,13.2,25.5"/>
						<path class="st49" d="M29.6,9.2C27,10.4,13.8,19.3,13.8,32.8c0,13.4,4.7,19.4,13.2,25.5"/>
						<path class="st50" d="M29.6,9.3c-2.6,1.1-15.8,10-15.8,23.5c0,13.4,4.7,19.4,13.2,25.4"/>
						<path class="st51" d="M29.6,9.3C27,10.5,13.8,19.4,13.8,32.8S18.5,52.2,27,58.2"/>
						<path class="st52" d="M29.6,9.3c-2.6,1.2-15.8,10-15.8,23.5c0,13.3,4.7,19.4,13.1,25.4"/>
						<path class="st53" d="M29.6,9.3c-2.5,1.2-15.7,10-15.7,23.5c0,13.3,4.7,19.4,13.1,25.4"/>
						<path class="st54" d="M29.6,9.3c-2.5,1.2-15.7,10-15.7,23.5c0,13.3,4.6,19.3,13.1,25.4"/>
						<path class="st55" d="M29.6,9.4c-2.5,1.2-15.7,10-15.7,23.4c0,13.3,4.6,19.3,13.1,25.3"/>
						<path class="st56" d="M29.7,9.4C27.2,10.6,14,19.4,14,32.8c0,13.3,4.6,19.3,13.1,25.3"/>
						<path class="st57" d="M29.7,9.4C27.1,10.5,14,19.4,14,32.8c0,13.3,4.6,19.3,13.1,25.3"/>
						<path class="st58" d="M29.7,9.4C27.1,10.6,14,19.4,14,32.8c0,13.3,4.6,19.3,13.1,25.3"/>
						<path class="st59" d="M29.7,9.4C27.1,10.6,14,19.4,14,32.8c0,13.3,4.6,19.3,13.1,25.3"/>
						<path class="st60" d="M29.7,9.5C27.2,10.6,14,19.4,14,32.8c0,13.3,4.6,19.2,13.1,25.3"/>
						<path class="st61" d="M29.7,9.5c-2.5,1.1-15.6,10-15.6,23.3c0,13.2,4.6,19.2,13,25.2"/>
						<path class="st62" d="M29.7,9.5c-2.5,1.1-15.6,10-15.6,23.3c0,13.2,4.6,19.2,13,25.2"/>
						<path class="st63" d="M29.7,9.5c-2.5,1.1-15.6,10-15.6,23.3c0,13.2,4.6,19.2,13,25.2"/>
						<path class="st64" d="M29.7,9.5c-2.5,1.1-15.6,10-15.6,23.3c0,13.2,4.6,19.2,13,25.2"/>
						<path class="st65" d="M29.7,9.6c-2.5,1.1-15.6,9.9-15.6,23.2c0,13.2,4.6,19.2,13,25.2"/>
						<path class="st66" d="M29.7,9.6c-2.5,1.1-15.6,9.9-15.6,23.2c0,13.2,4.6,19.1,13,25.2"/>
						<path class="st67" d="M29.7,9.6c-2.5,1.1-15.6,9.9-15.6,23.2c0,13.2,4.6,19.1,13,25.1"/>
						<path class="st68" d="M29.8,9.6c-2.5,1.1-15.6,9.9-15.6,23.2c0,13.2,4.6,19.1,13,25.1"/>
						<path class="st69" d="M29.8,9.6c-2.5,1.1-15.5,9.9-15.5,23.2c0,13.2,4.5,19.1,13,25.1"/>
						<path class="st70" d="M29.8,9.6c-2.5,1.1-15.5,9.9-15.5,23.1s4.5,19.1,13,25.1"/>
						<path class="st71" d="M29.8,9.7c-2.5,1.1-15.5,9.9-15.5,23.1c0,13.1,4.5,19.1,13,25.1"/>
						<path class="st72" d="M29.8,9.7c-2.5,1.1-15.5,9.9-15.5,23.1c0,13.1,4.5,19.1,13,25.1"/>
						<path class="st73" d="M29.8,9.7c-2.5,1.1-15.5,9.9-15.5,23.1c0,13.1,4.5,19,13,25"/>
						<path class="st74" d="M29.8,9.7c-2.5,1.1-15.5,9.9-15.5,23.1c0,13.1,4.5,19,13,25"/>
						<path class="st75" d="M29.8,9.7c-2.5,1.1-15.5,9.9-15.5,23s4.5,19,12.9,25"/>
						<path class="st76" d="M29.8,9.8c-2.5,1.1-15.5,9.9-15.5,23s4.5,19,12.9,25"/>
						<path class="st77" d="M29.8,9.8c-2.5,1.1-15.4,9.9-15.4,23s4.5,19,12.9,25"/>
						<path class="st78" d="M29.8,9.8c-2.5,1.1-15.4,9.9-15.4,23s4.5,19,12.9,24.9"/>
						<path class="st79" d="M29.9,9.8c-2.5,1.1-15.4,9.8-15.4,23c0,13.1,4.5,18.9,12.9,24.9"/>
						<path class="st80" d="M29.9,9.8c-2.5,1.1-15.4,9.8-15.4,23c0,13,4.5,18.9,12.9,24.9"/>
						<path class="st81" d="M29.9,9.9c-2.5,1.1-15.4,9.8-15.4,22.9c0,13,4.5,18.9,12.9,24.9"/>
						<path class="st82" d="M29.9,9.9c-2.5,1.1-15.4,9.8-15.4,22.9c0,13,4.5,18.9,12.9,24.9"/>
						<path class="st83" d="M29.9,9.9c-2.5,1.1-15.4,9.8-15.4,22.9c0,13,4.5,18.9,12.9,24.9"/>
						<path class="st84" d="M29.9,9.9c-2.5,1.1-15.4,9.8-15.4,22.9c0,13,4.4,18.9,12.9,24.8"/>
						<path class="st85" d="M29.9,9.9c-2.5,1.1-15.3,9.8-15.3,22.9c0,13,4.4,18.8,12.9,24.8"/>
						<path class="st86" d="M29.9,10c-2.5,1.1-15.3,9.8-15.3,22.8S19,51.6,27.4,57.6"/>
						<path class="st87" d="M29.9,10c-2.5,1.1-15.3,9.8-15.3,22.8S19,51.6,27.4,57.6"/>
						<path class="st88" d="M29.9,10c-2.4,1.1-15.3,9.8-15.3,22.8S19,51.6,27.4,57.6"/>
						<path class="st89" d="M29.9,10c-2.4,1.1-15.3,9.8-15.3,22.8c0,12.9,4.4,18.8,12.8,24.8"/>
						<path class="st90" d="M29.9,10c-2.4,1.1-15.3,9.8-15.3,22.8c0,12.9,4.4,18.8,12.8,24.7"/>
						<path class="st91" d="M30,10.1c-2.4,1.1-15.3,9.8-15.3,22.7s4.4,18.8,12.8,24.7"/>
						<path class="st92" d="M30,10.1c-2.4,1.1-15.3,9.8-15.3,22.7s4.4,18.7,12.8,24.7"/>
						<path class="st93" d="M30,10.1c-2.4,1.1-15.2,9.7-15.2,22.7c0,12.9,4.4,18.7,12.8,24.7"/>
						<path class="st94" d="M30,10.1c-2.4,1.1-15.2,9.7-15.2,22.7c0,12.9,4.4,18.7,12.8,24.7"/>
						<path class="st95" d="M30,10.1c-2.4,1.1-15.2,9.7-15.2,22.7c0,12.9,4.4,18.7,12.8,24.7"/>
						<path class="st96" d="M30,10.2c-2.4,1.1-15.2,9.7-15.2,22.6s4.4,18.7,12.8,24.6"/>
						<path class="st97" d="M30,10.2c-2.4,1.1-15.2,9.7-15.2,22.6s4.4,18.7,12.7,24.6"/>
						<path class="st98" d="M30,10.2c-2.4,1.1-15.2,9.7-15.2,22.6c0,12.8,4.4,18.6,12.7,24.6"/>
						<path class="st99" d="M30,10.2c-2.4,1.1-15.2,9.7-15.2,22.6c0,12.8,4.4,18.6,12.7,24.6"/>
						<path class="st100" d="M30,10.2c-2.4,1.1-15.2,9.7-15.2,22.6c0,12.8,4.3,18.6,12.7,24.6"/>
						<path class="st101" d="M30,10.2c-2.4,1.1-15.1,9.7-15.1,22.5s4.3,18.6,12.7,24.5"/>
						<path class="st102" d="M30.1,10.3C27.7,11.4,15,20,15,32.8s4.3,18.6,12.7,24.5"/>
						<path class="st103" d="M30.1,10.3C27.7,11.4,15,20,15,32.8s4.3,18.6,12.7,24.5"/>
						<path class="st104" d="M30.1,10.3C27.7,11.4,15,20,15,32.8s4.3,18.5,12.7,24.5"/>
						<path class="st105" d="M30.1,10.3C27.7,11.4,15,20,15,32.8s4.3,18.5,12.7,24.5"/>
						<path class="st106" d="M30.1,10.3C27.7,11.4,15,20,15,32.8s4.3,18.5,12.7,24.5"/>
						<path class="st107" d="M30.1,10.4C27.7,11.5,15,20,15,32.8c0,12.7,4.3,18.5,12.7,24.4"/>
						<path class="st108" d="M30.1,10.4C27.7,11.5,15,20,15,32.8c0,12.7,4.3,18.5,12.6,24.4"/>
						<path class="st109" d="M30.1,10.4c-2.4,1.1-15,9.6-15,22.4c0,12.7,4.3,18.5,12.6,24.4"/>
						<path class="st110" d="M30.1,10.4c-2.4,1.1-15,9.6-15,22.4c0,12.7,4.3,18.5,12.6,24.4"/>
						<path class="st111" d="M30.1,10.4c-2.4,1.1-15,9.6-15,22.4c0,12.7,4.3,18.4,12.6,24.4"/>
						<path class="st112" d="M30.1,10.5c-2.4,1.1-15,9.6-15,22.3s4.3,18.4,12.6,24.4"/>
						<path class="st113" d="M30.1,10.5c-2.4,1.1-15,9.6-15,22.3s4.3,18.4,12.6,24.3"/>
						<path class="st114" d="M30.2,10.5c-2.4,1.1-15,9.6-15,22.3s4.3,18.4,12.6,24.3"/>
						<path class="st115" d="M30.2,10.5c-2.4,1.1-15,9.6-15,22.3s4.2,18.4,12.6,24.3"/>
						<path class="st116" d="M30.2,10.5c-2.4,1.1-15,9.6-15,22.3c0,12.6,4.2,18.4,12.6,24.3"/>
						<path class="st117" d="M30.2,10.6c-2.4,1.1-14.9,9.6-14.9,22.2s4.2,18.3,12.6,24.3"/>
						<path class="st118" d="M30.2,10.6c-2.4,1.1-14.9,9.6-14.9,22.2s4.2,18.3,12.6,24.3"/>
						<path class="st119" d="M30.2,10.6c-2.4,1.1-14.9,9.6-14.9,22.2S19.5,51.1,27.9,57"/>
						<path class="st120" d="M30.2,10.6c-2.4,1.1-14.9,9.5-14.9,22.2c0,12.6,4.2,18.3,12.5,24.2"/>
						<path class="st121" d="M30.2,10.6c-2.4,1.1-14.9,9.5-14.9,22.2c0,12.6,4.2,18.3,12.5,24.2"/>
						<path class="st122" d="M30.2,10.7c-2.3,1.1-14.9,9.5-14.9,22.1S19.5,51.1,27.8,57"/>
						<path class="st123" d="M30.2,10.7c-2.3,1.1-14.9,9.5-14.9,22.1S19.5,51.1,27.8,57"/>
						<path class="st124" d="M30.2,10.7c-2.3,1.1-14.9,9.5-14.9,22.1S19.5,51,27.8,57"/>
						<path class="st125" d="M30.3,10.7c-2.3,1.1-14.8,9.5-14.8,22.1S19.7,51,28,56.9"/>
						<path class="st126" d="M30.3,10.7c-2.3,1.1-14.8,9.5-14.8,22.1c0,12.5,4.2,18.2,12.5,24.1"/>
						<path class="st127" d="M30.3,10.8c-2.3,1.1-14.8,9.5-14.8,22S19.7,51,28,56.9"/>
						<path class="st128" d="M30.3,10.8c-2.3,1.1-14.8,9.5-14.8,22S19.7,51,28,56.9"/>
						<path class="st129" d="M30.3,10.8c-2.3,1.1-14.8,9.5-14.8,22S19.7,51,28,56.9"/>
						<path class="st130" d="M30.3,10.8c-2.3,1.1-14.8,9.5-14.8,22s4.1,18.1,12.5,24"/>
						<path class="st131" d="M30.3,10.8c-2.3,1.1-14.8,9.5-14.8,22s4.1,18.1,12.4,24"/>
						<path class="st132" d="M30.3,10.8c-2.3,1.1-14.8,9.5-14.8,21.9c0,12.5,4.1,18.1,12.4,24"/>
						<path class="st133" d="M30.3,10.9C28,12,15.6,20.4,15.6,32.8c0,12.5,4.1,18.1,12.4,24"/>
						<path class="st134" d="M30.3,10.9C28,12,15.6,20.3,15.6,32.8s4.1,18.1,12.4,24"/>
						<path class="st135" d="M30.3,10.9C28,12,15.6,20.3,15.6,32.8c0,12.4,4.1,18.1,12.4,24"/>
						<path class="st136" d="M30.3,10.9C28,12,15.6,20.4,15.6,32.8s4.1,18,12.4,23.9"/>
						<path class="st137" d="M30.4,10.9c-2.3,1-14.7,9.4-14.7,21.8s4.1,18,12.4,23.9"/>
						<path class="st138" d="M30.4,11c-2.3,1-14.7,9.4-14.7,21.8s4.1,18,12.4,23.9"/>
						<path class="st139" d="M30.4,11c-2.3,1-14.7,9.4-14.7,21.8s4.1,18,12.4,23.9"/>
						<path class="st140" d="M30.4,11c-2.3,1-14.7,9.4-14.7,21.8s4.1,18,12.4,23.9"/>
						<path class="st141" d="M30.4,11c-2.3,1-14.6,9.4-14.6,21.8s4.1,18,12.4,23.9"/>
						<path class="st142" d="M30.4,11c-2.3,1-14.6,9.4-14.6,21.8s4.1,18,12.3,23.8"/>
						<path class="st141" d="M30.4,11.1c-2.3,1-14.6,9.4-14.6,21.7c0,12.4,4.1,17.9,12.3,23.8"/>
						<path class="st140" d="M30.4,11.1c-2.3,1-14.6,9.4-14.6,21.7c0,12.4,4.1,17.9,12.3,23.8"/>
						<path class="st139" d="M30.4,11.1c-2.3,1-14.6,9.4-14.6,21.7s4.1,17.9,12.3,23.8"/>
						<path class="st138" d="M30.4,11.1c-2.3,1-14.6,9.4-14.6,21.7s4.1,17.9,12.3,23.8"/>
						<path class="st137" d="M30.4,11.1c-2.3,1-14.6,9.3-14.6,21.7c0,12.3,4.1,17.9,12.3,23.8"/>
						<path class="st136" d="M30.4,11.2c-2.3,1-14.6,9.3-14.6,21.6s4.1,17.9,12.3,23.7"/>
						<path class="st135" d="M30.5,11.2c-2.3,1-14.6,9.3-14.6,21.6s4,17.9,12.3,23.7"/>
						<path class="st134" d="M30.5,11.2c-2.3,1-14.5,9.3-14.5,21.6s4,17.9,12.3,23.7"/>
						<path class="st133" d="M30.5,11.2c-2.3,1-14.5,9.3-14.5,21.6s4,17.8,12.3,23.7"/>
						<path class="st132" d="M30.5,11.2c-2.2,1-14.5,9.3-14.5,21.6s4,17.8,12.3,23.7"/>
						<path class="st131" d="M30.5,11.3c-2.2,1-14.5,9.3-14.5,21.5c0,12.3,4,17.8,12.2,23.6"/>
						<path class="st130" d="M30.5,11.3c-2.2,1-14.5,9.3-14.5,21.5c0,12.3,4,17.8,12.2,23.6"/>
						<path class="st129" d="M30.5,11.3c-2.2,1-14.5,9.3-14.5,21.5c0,12.3,4,17.8,12.2,23.6"/>
						<path class="st128" d="M30.5,11.3c-2.2,1-14.5,9.3-14.5,21.5c0,12.3,4,17.8,12.2,23.6"/>
						<path class="st127" d="M30.5,11.3c-2.2,1-14.5,9.3-14.5,21.5c0,12.3,4,17.8,12.2,23.6"/>
						<path class="st126" d="M30.5,11.4c-2.2,1-14.4,9.3-14.4,21.4c0,12.3,4,17.7,12.2,23.6"/>
						<path class="st125" d="M30.5,11.4c-2.2,1-14.4,9.3-14.4,21.4c0,12.3,4,17.7,12.2,23.5"/>
						<path class="st124" d="M30.6,11.4c-2.2,1-14.4,9.2-14.4,21.4s4,17.7,12.2,23.5"/>
						<path class="st123" d="M30.6,11.4c-2.2,1-14.4,9.2-14.4,21.4s4,17.7,12.2,23.5"/>
						<path class="st122" d="M30.6,11.4c-2.2,1-14.4,9.2-14.4,21.4s4,17.7,12.2,23.5"/>
						<path class="st121" d="M30.6,11.4c-2.2,1-14.4,9.2-14.4,21.3c0,12.2,4,17.7,12.2,23.5"/>
						<path class="st120" d="M30.6,11.5c-2.2,1-14.4,9.2-14.4,21.3c0,12.2,4,17.7,12.1,23.5"/>
						<path class="st119" d="M30.6,11.5c-2.2,1-14.4,9.2-14.4,21.3c0,12.2,4,17.7,12.1,23.4"/>
						<path class="st118" d="M30.6,11.5c-2.2,1-14.3,9.2-14.3,21.3c0,12.2,4,17.6,12.1,23.4"/>
						<path class="st117" d="M30.6,11.5c-2.2,1-14.3,9.2-14.3,21.3c0,12.2,4,17.6,12.1,23.4"/>
						<path class="st116" d="M30.6,11.5c-2.2,1-14.3,9.2-14.3,21.2c0,12.2,4,17.6,12.1,23.4"/>
						<path class="st115" d="M30.6,11.6c-2.2,1-14.3,9.2-14.3,21.2c0,12.2,4,17.6,12.1,23.4"/>
						<path class="st114" d="M30.6,11.6c-2.2,1-14.3,9.2-14.3,21.2c0,12.2,4,17.6,12.1,23.4"/>
						<path class="st113" d="M30.6,11.6c-2.2,1-14.3,9.2-14.3,21.2c0,12.2,4,17.6,12.1,23.3"/>
						<path class="st112" d="M30.7,11.6c-2.2,1-14.3,9.2-14.3,21.2c0,12.2,4,17.6,12.1,23.3"/>
						<path class="st111" d="M30.7,11.6c-2.2,1-14.3,9.2-14.3,21.2c0,12.2,4,17.6,12.1,23.3"/>
						<path class="st110" d="M30.7,11.7c-2.2,1-14.2,9.1-14.2,21.1c0,12.2,4,17.5,12.1,23.3"/>
						<path class="st109" d="M30.7,11.7c-2.1,1-14.2,9.1-14.2,21.1c0,12.1,4,17.5,12.1,23.3"/>
						<path class="st108" d="M30.7,11.7c-2.1,1-14.2,9.1-14.2,21.1c0,12.1,4,17.5,12,23.2"/>
						<path class="st107" d="M30.7,11.7c-2.1,1-14.2,9.1-14.2,21.1c0,12.1,4,17.5,12,23.2"/>
						<path class="st106" d="M30.7,11.7c-2.1,1-14.2,9.1-14.2,21.1c0,12.1,4,17.5,12,23.2"/>
						<path class="st105" d="M30.7,11.8c-2.1,1-14.2,9.1-14.2,21c0,12.1,4,17.5,12,23.2"/>
						<path class="st104" d="M30.7,11.8c-2.1,1-14.2,9.1-14.2,21c0,12.1,4,17.5,12,23.2"/>
						<path class="st103" d="M30.7,11.8c-2.1,1-14.2,9.1-14.2,21c0,12.1,4,17.5,12,23.2"/>
						<path class="st102" d="M30.7,11.8c-2.1,1-14.1,9.1-14.1,21c0,12.1,4,17.4,12,23.1"/>
						<path class="st101" d="M30.8,11.8c-2.1,1-14.1,9.1-14.1,21c0,12.1,4,17.4,12,23.1"/>
						<path class="st100" d="M30.8,11.9c-2.1,1-14.1,9.1-14.1,20.9c0,12.1,4,17.4,12,23.1"/>
						<path class="st99" d="M30.8,11.9c-2.1,1-14.1,9.1-14.1,20.9c0,12.1,4,17.4,12,23.1"/>
						<path class="st98" d="M30.8,11.9c-2.1,1-14.1,9.1-14.1,20.9c0,12.1,4,17.4,12,23.1"/>
						<path class="st97" d="M30.8,11.9c-2.1,1-14.1,9-14.1,20.9c0,12.1,4,17.4,11.9,23.1"/>
						<path class="st96" d="M30.8,11.9c-2.1,1-14.1,9-14.1,20.9c0,12.1,4,17.4,11.9,23"/>
						<path class="st95" d="M30.8,12c-2.1,1-14.1,9-14.1,20.8c0,12.1,4,17.4,11.9,23"/>
						<path class="st94" d="M30.8,12c-2.1,0.9-14,9-14,20.8c0,12,4,17.3,11.9,23"/>
						<path class="st93" d="M30.8,12c-2.1,0.9-14,9-14,20.8c0,12,4,17.3,11.9,23"/>
						<path class="st92" d="M30.8,12c-2.1,0.9-14,9-14,20.8c0,12,3.9,17.3,11.9,23"/>
						<path class="st91" d="M30.8,12c-2.1,0.9-14,9-14,20.8c0,12,3.9,17.3,11.9,23"/>
						<path class="st90" d="M30.8,12c-2.1,0.9-14,9-14,20.7c0,12,3.9,17.3,11.9,22.9"/>
						<path class="st89" d="M30.9,12.1c-2.1,0.9-14,9-14,20.7c0,12,3.9,17.3,11.9,22.9"/>
						<path class="st88" d="M30.9,12.1c-2.1,0.9-14,9-14,20.7c0,12,3.9,17.3,11.9,22.9"/>
						<path class="st87" d="M30.9,12.1c-2.1,0.9-14,9-14,20.7c0,12,3.9,17.3,11.9,22.9"/>
						<path class="st86" d="M30.9,12.1c-2,0.9-13.9,9-13.9,20.7c0,12,3.9,17.2,11.8,22.9"/>
						<path class="st85" d="M30.9,12.1c-2,0.9-13.9,9-13.9,20.6c0,12,3.9,17.2,11.8,22.8"/>
						<path class="st84" d="M30.9,12.2c-2,0.9-13.9,8.9-13.9,20.6c0,12,3.9,17.2,11.8,22.8"/>
						<path class="st83" d="M30.9,12.2c-2,0.9-13.9,8.9-13.9,20.6c0,12,3.9,17.2,11.8,22.8"/>
						<path class="st82" d="M30.9,12.2c-2,0.9-13.9,8.9-13.9,20.6c0,12,3.9,17.2,11.8,22.8"/>
						<path class="st81" d="M30.9,12.2c-2,0.9-13.9,8.9-13.9,20.6c0,12,3.9,17.2,11.8,22.8"/>
						<path class="st80" d="M30.9,12.2c-2,0.9-13.9,8.9-13.9,20.6c0,12,3.9,17.2,11.8,22.8"/>
						<path class="st79" d="M30.9,12.3c-2,0.9-13.9,8.9-13.9,20.5c0,11.9,3.9,17.2,11.8,22.7"/>
						<path class="st78" d="M31,12.3c-2,0.9-13.8,8.9-13.8,20.5c0,11.9,3.9,17.1,11.8,22.7"/>
						<path class="st77" d="M31,12.3c-2,0.9-13.8,8.9-13.8,20.5c0,11.9,3.9,17.1,11.8,22.7"/>
						<path class="st76" d="M31,12.3c-2,0.9-13.8,8.9-13.8,20.5c0,11.9,3.9,17.1,11.8,22.7"/>
						<path class="st75" d="M31,12.3c-2,0.9-13.8,8.9-13.8,20.5c0,11.9,3.9,17.1,11.7,22.7"/>
						<path class="st74" d="M31,12.4c-2,0.9-13.8,8.9-13.8,20.4c0,11.9,3.9,17.1,11.7,22.7"/>
						<path class="st143" d="M31,12.4c-2,0.9-13.8,8.9-13.8,20.4c0,11.9,3.9,17.1,11.7,22.6"/>
						<path class="st72" d="M31,12.4c-2,0.9-13.8,8.9-13.8,20.4c0,11.9,3.9,17.1,11.7,22.6"/>
						<path class="st71" d="M31,12.4c-2,0.9-13.8,8.8-13.8,20.4c0,11.9,3.9,17.1,11.7,22.6"/>
						<path class="st70" d="M31,12.4c-2,0.9-13.7,8.8-13.7,20.4c0,11.9,3.9,17,11.7,22.6"/>
						<path class="st69" d="M31,12.5c-2,0.9-13.7,8.8-13.7,20.3c0,11.9,3.9,17,11.7,22.6"/>
						<path class="st68" d="M31,12.5c-2,0.9-13.7,8.8-13.7,20.3c0,11.9,3.9,17,11.7,22.6"/>
						<path class="st67" d="M31,12.5c-2,0.9-13.7,8.8-13.7,20.3c0,11.9,3.9,17,11.7,22.5"/>
						<path class="st66" d="M31.1,12.5c-2,0.9-13.7,8.8-13.7,20.3c0,11.9,3.9,17,11.7,22.5"/>
						<path class="st65" d="M31.1,12.5c-2,0.9-13.7,8.8-13.7,20.3c0,11.9,3.9,17,11.7,22.5"/>
						<path class="st64" d="M31.1,12.6c-2,0.9-13.7,8.8-13.7,20.2c0,11.8,3.9,17,11.6,22.5"/>
						<path class="st63" d="M31.1,12.6c-1.9,0.9-13.7,8.8-13.7,20.2c0,11.8,3.9,17,11.6,22.5"/>
						<path class="st62" d="M31.1,12.6c-1.9,0.9-13.6,8.8-13.6,20.2c0,11.8,3.9,16.9,11.6,22.4"/>
						<path class="st61" d="M31.1,12.6c-1.9,0.9-13.6,8.8-13.6,20.2c0,11.8,3.9,16.9,11.6,22.4"/>
						<path class="st60" d="M31.1,12.6c-1.9,0.9-13.6,8.8-13.6,20.2c0,11.8,3.9,16.9,11.6,22.4"/>
						<path class="st59" d="M31.1,12.6c-1.9,0.9-13.6,8.8-13.6,20.1c0,11.8,3.9,16.9,11.6,22.4"/>
						<path class="st58" d="M31.1,12.7c-1.9,0.9-13.6,8.8-13.6,20.1c0,11.8,3.9,16.9,11.6,22.4"/>
						<path class="st57" d="M31.1,12.7c-1.9,0.9-13.6,8.7-13.6,20.1c0,11.8,3.9,16.9,11.6,22.4"/>
						<path class="st56" d="M31.1,12.7c-1.9,0.9-13.6,8.7-13.6,20.1c0,11.8,3.9,16.9,11.6,22.3"/>
						<path class="st55" d="M31.2,12.7c-1.9,0.9-13.6,8.7-13.6,20.1c0,11.8,3.9,16.8,11.6,22.3"/>
						<path class="st54" d="M31.2,12.7c-1.9,0.9-13.5,8.7-13.5,20c0,11.8,3.9,16.8,11.6,22.3"/>
						<path class="st53" d="M31.2,12.8c-1.9,0.9-13.5,8.7-13.5,20c0,11.8,3.9,16.8,11.6,22.3"/>
						<path class="st52" d="M31.2,12.8c-1.9,0.9-13.5,8.7-13.5,20c0,11.8,3.9,16.8,11.5,22.3"/>
						<path class="st51" d="M31.2,12.8c-1.9,0.9-13.5,8.7-13.5,20c0,11.8,3.9,16.8,11.5,22.3"/>
						<path class="st50" d="M31.2,12.8c-1.9,0.9-13.5,8.7-13.5,20c0,11.8,3.9,16.8,11.5,22.2"/>
						<path class="st49" d="M31.2,12.8c-1.9,0.9-13.5,8.7-13.5,20c0,11.7,3.8,16.8,11.5,22.2"/>
						<path class="st48" d="M31.2,12.9c-1.9,0.9-13.5,8.7-13.5,19.9c0,11.7,3.8,16.8,11.5,22.2"/>
						<path class="st47" d="M31.2,12.9c-1.9,0.9-13.5,8.7-13.5,19.9c0,11.7,3.8,16.7,11.5,22.2"/>
						<path class="st46" d="M31.2,12.9c-1.9,0.9-13.4,8.7-13.4,19.9c0,11.7,3.8,16.7,11.5,22.2"/>
						<path class="st45" d="M31.2,12.9c-1.9,0.9-13.4,8.7-13.4,19.9c0,11.7,3.8,16.7,11.5,22.2"/>
						<path class="st44" d="M31.2,12.9c-1.9,0.8-13.4,8.6-13.4,19.9c0,11.7,3.8,16.7,11.5,22.1"/>
						<path class="st43" d="M31.3,13c-1.9,0.8-13.4,8.6-13.4,19.8c0,11.7,3.8,16.7,11.5,22.1"/>
						<path class="st42" d="M31.3,13c-1.9,0.8-13.4,8.6-13.4,19.8c0,11.7,3.8,16.7,11.5,22.1"/>
						<path class="st41" d="M31.3,13c-1.9,0.8-13.4,8.6-13.4,19.8c0,11.7,3.8,16.7,11.4,22.1"/>
						<path class="st40" d="M31.3,13c-1.9,0.8-13.4,8.6-13.4,19.8c0,11.7,3.8,16.7,11.4,22.1"/>
						<path class="st39" d="M31.3,13c-1.8,0.8-13.4,8.6-13.4,19.8c0,11.7,3.8,16.6,11.4,22"/>
						<path class="st38" d="M31.3,13.1C29.5,13.9,18,21.7,18,32.8c0,11.7,3.8,16.6,11.4,22"/>
						<path class="st37" d="M31.3,13.1C29.5,13.9,18,21.7,18,32.8c0,11.7,3.8,16.6,11.4,22"/>
						<path class="st36" d="M31.3,13.1C29.5,13.9,18,21.7,18,32.8c0,11.7,3.8,16.6,11.4,22"/>
						<path class="st35" d="M31.3,13.1C29.5,13.9,18,21.7,18,32.8c0,11.7,3.8,16.6,11.4,22"/>
						<path class="st34" d="M31.3,13.1C29.5,14,18,21.7,18,32.8c0,11.6,3.8,16.6,11.4,22"/>
						<path class="st33" d="M31.3,13.2C29.5,14,18,21.8,18,32.8c0,11.6,3.8,16.6,11.4,21.9"/>
						<path class="st32" d="M31.4,13.2c-1.8,0.8-13.3,8.6-13.3,19.6c0,11.6,3.8,16.6,11.4,21.9"/>
						<path class="st31" d="M31.4,13.2c-1.8,0.8-13.3,8.5-13.3,19.6c0,11.6,3.8,16.5,11.4,21.9"/>
						<path class="st30" d="M31.4,13.2c-1.8,0.8-13.2,8.5-13.2,19.6c0,11.6,3.8,16.5,11.3,21.9"/>
						<path class="st29" d="M31.4,13.2c-1.8,0.8-13.2,8.5-13.2,19.6c0,11.6,3.8,16.5,11.3,21.9"/>
						<path class="st28" d="M31.4,13.2c-1.8,0.8-13.2,8.5-13.2,19.5c0,11.6,3.8,16.5,11.3,21.9"/>
						<path class="st27" d="M31.4,13.3c-1.8,0.8-13.2,8.5-13.2,19.5c0,11.6,3.8,16.5,11.3,21.8"/>
						<path class="st26" d="M31.4,13.3c-1.8,0.8-13.2,8.5-13.2,19.5c0,11.6,3.8,16.5,11.3,21.8"/>
						<path class="st25" d="M31.4,13.3c-1.8,0.8-13.2,8.5-13.2,19.5c0,11.6,3.8,16.5,11.3,21.8"/>
						<path class="st24" d="M31.4,13.3c-1.8,0.8-13.2,8.5-13.2,19.5c0,11.6,3.8,16.5,11.3,21.8"/>
						<path class="st23" d="M31.4,13.3c-1.8,0.8-13.2,8.5-13.2,19.4c0,11.6,3.8,16.4,11.3,21.8"/>
						<path class="st22" d="M31.4,13.4c-1.8,0.8-13.1,8.5-13.1,19.4c0,11.6,3.8,16.4,11.3,21.8"/>
						<path class="st21" d="M31.4,13.4c-1.8,0.8-13.1,8.5-13.1,19.4c0,11.6,3.8,16.4,11.3,21.7"/>
						<path class="st20" d="M31.5,13.4c-1.8,0.8-13.1,8.5-13.1,19.4c0,11.6,3.8,16.4,11.3,21.7"/>
						<path class="st19" d="M31.5,13.4c-1.8,0.8-13.1,8.5-13.1,19.4c0,11.5,3.8,16.4,11.2,21.7"/>
						<path class="st18" d="M31.5,13.4c-1.8,0.8-13.1,8.4-13.1,19.4c0,11.5,3.8,16.4,11.2,21.7"/>
						<path class="st17" d="M31.5,13.5c-1.8,0.8-13.1,8.4-13.1,19.3c0,11.5,3.8,16.4,11.2,21.7"/>
						<path class="st16" d="M31.5,13.5c-1.7,0.8-13.1,8.4-13.1,19.3c0,11.5,3.8,16.4,11.2,21.7"/>
						<path class="st15" d="M31.5,13.5c-1.7,0.8-13.1,8.4-13.1,19.3c0,11.5,3.8,16.3,11.2,21.6"/>
						<path class="st14" d="M31.5,13.5c-1.7,0.8-13,8.4-13,19.3c0,11.5,3.8,16.3,11.2,21.6"/>
						<path class="st13" d="M31.5,13.5c-1.7,0.8-13,8.4-13,19.3c0,11.5,3.8,16.3,11.2,21.6"/>
						<path class="st12" d="M31.5,13.6c-1.7,0.8-13,8.4-13,19.2c0,11.5,3.8,16.3,11.2,21.6"/>
						<path class="st11" d="M31.5,13.6c-1.7,0.8-13,8.4-13,19.2c0,11.5,3.8,16.3,11.2,21.6"/>
						<path class="st10" d="M31.5,13.6c-1.7,0.8-13,8.4-13,19.2c0,11.5,3.8,16.3,11.2,21.5"/>
						<path class="st9" d="M31.6,13.6c-1.7,0.8-13,8.4-13,19.2c0,11.5,3.8,16.3,11.2,21.5"/>
						<path class="st8" d="M31.6,13.6c-1.7,0.8-13,8.4-13,19.2c0,11.5,3.8,16.3,11.1,21.5"/>
						<path class="st7" d="M31.6,13.7c-1.7,0.8-13,8.4-13,19.1c0,11.5,3.7,16.2,11.1,21.5"/>
						<path class="st6" d="M31.6,13.7c-1.7,0.8-12.9,8.4-12.9,19.1c0,11.5,3.7,16.2,11.1,21.5"/>
						<path class="st5" d="M31.6,13.7c-1.7,0.8-12.9,8.4-12.9,19.1c0,11.5,3.7,16.2,11.1,21.5"/>
						<path class="st4" d="M29.8,54.2c-7.4-5.2-11.1-10-11.1-21.4c0-10.7,11.2-18.3,12.9-19.1"/>
					</g>
				</g>
			</g>
		</g>
		<g>
			<g>
				<g>
					<g>
						<g>
							<path class="st2" d="M51.1,19.4c-0.1,0-0.2,0-0.3,0c-0.3,0-0.5-0.1-0.6-0.3C50.1,19,50,18.8,50,18.4V17h-0.4v-0.7H50v-0.8
								h0.8v0.8h0.5V17h-0.5v1.2c0,0.2,0,0.3,0.1,0.3c0,0.1,0.1,0.1,0.2,0.1c0.1,0,0.1,0,0.2,0v0.7C51.3,19.4,51.2,19.4,51.1,19.4z"
								/>
							<path class="st2" d="M52.6,19.4h-0.9v-4.5h0.9v1.8l0,0c0-0.1,0.1-0.3,0.2-0.4c0.1-0.1,0.3-0.2,0.4-0.2c0.2,0,0.3,0,0.4,0.1
								c0.1,0.1,0.2,0.2,0.2,0.3C54,16.7,54,16.8,54,17s0,0.3,0,0.5v1.9h-0.9v-1.9c0-0.1,0-0.2-0.1-0.3c0-0.1-0.1-0.1-0.2-0.1
								c-0.1,0-0.2,0.1-0.3,0.2s-0.1,0.2-0.1,0.4v1.7H52.6z"/>
						</g>
					</g>
				</g>
			</g>
			<path class="st2" d="M45.4,8.5c0.7,0,1.1,0.5,1.3,1.2c0.3,0.8,0.4,1.9,0.4,2.7c0,0.7-0.1,2.1-0.4,2.9c-0.4,1-0.9,1.1-1.2,1.1
				c-1.3,0-1.5-2.2-1.6-3c0-0.3,0-0.7,0-1c0-1.1,0.1-2.1,0.4-2.8C44.5,8.7,45,8.5,45.4,8.5 M45.6,5.6c-1.9,0-3.7,1-4.2,4.7
				c-0.1,0.7-0.1,1.5-0.1,2.3c0,1.7,0.2,4.1,1.5,5.6c1,1.1,2.1,1.2,2.7,1.2c2.4,0,4.2-2,4.2-6.9c0-1.6-0.1-3.7-1.1-5.3
				C47.8,6.2,46.8,5.7,45.6,5.6 M35.4,11.2H36c0.7,0,1.4,0,1.6-0.2c0.2-0.2,0.4-0.5,0.4-1.1c0-1-0.6-1.2-1.2-1.2
				c-1.2,0-1.5,0.8-1.6,1.2l-2.4-0.2c0.2-1.3,0.5-3.8,3.9-3.8c0.4,0,0.8,0,1.2,0.1c0.6,0.1,1.4,0.2,2,1c0.6,0.7,0.7,1.9,0.7,2.6
				c0,0.3,0,0.8-0.2,1.2c-0.2,0.5-0.5,1.1-1.2,1.5c-0.1,0-0.2,0.1-0.3,0.1c0.1,0,0.2,0.1,0.3,0.1c0.9,0.3,1.5,1.2,1.5,2.9
				c0,3.9-2.6,4.2-4.2,4.2c-1.2,0-2.4-0.1-3.2-1.2c-0.8-1-0.9-2.3-1-3l2.3-0.2c0,0.2,0.2,0.7,0.4,1c0.4,0.4,0.8,0.5,1.4,0.5
				c1.1,0,1.5-0.6,1.5-1.5c0-1.2-0.8-1.3-1.2-1.3h-1.6v-2.7H35.4z"/>
		</g>
	</g>
	<g class="st1">
		<path class="st145" d="M51.3,30.5c-5.1,0.8-3.9,6-7.1,6.7c-5.1,1.2-5.4-7.7-10.6-11c-7.4-4.6-7.8,4.6-11.8,1.2
			C24.9,37.2,32.9,28,37,39.1c-3.1,0-8.6,0.2-8.9,4.8c-0.4,5.4,4.3,6.3,2.3,8.5c8.9,0,1.9-7.4,8.3-7.7c4.4-0.2,4.4,4.1,0.1,4.6
			c2,0.8,9.4,1.1,10.9-3.6c1.2-3.8-2.3-7.3,2.6-9.1c3.2-1.2,5.3,1.6,5.8,2.8C60.5,35.5,56.1,29.8,51.3,30.5"/>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000043457331361994833950000003789439154115535015_" d="M26.9,26c-1.8,1.4-2.9,3.4-5.1,1.5
						c3.1,9.7,11.1,0.4,15.3,11.6c-3.1-0.1-8.6,0.2-9,4.8c-0.4,5.4,4.3,6.3,2.3,8.5c8.6,0,2.2-7,7.7-7.7c0.2,0,0.3,0,0.5,0
						c4.4-0.2,4.4,4.1,0.1,4.6c2,0.8,9.4,1.1,10.9-3.6c1.2-3.8-2.3-7.3,2.6-9.1c3.2-1.2,5.3,1.7,5.8,2.8c2.4-4-1.9-9.6-6.8-8.9
						c-4.8,0.7-4,5.5-6.7,6.6c-0.1,0.1-0.3,0.1-0.4,0.1c-5.2,1.2-5.3-7.7-10.6-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.4,26.9,26
						"/>
				</defs>
				<clipPath id="SVGID_00000091705419015261803260000009631924827035284920_">
					<use xlink:href="#SVGID_00000043457331361994833950000003789439154115535015_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000039100960997929368800000007650492191166291133_" cx="-807.0341" cy="464.7837" r="0.4217" gradientTransform="matrix(63.2979 0 0 63.2979 51130.1602 -29377.6758)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#564700"/>
					<stop  offset="0.1648" style="stop-color:#514200"/>
					<stop  offset="0.4121" style="stop-color:#524200"/>
					<stop  offset="0.489" style="stop-color:#564700"/>
					<stop  offset="0.6703" style="stop-color:#564700"/>
					<stop  offset="0.7454" style="stop-color:#544500"/>
					<stop  offset="0.7527" style="stop-color:#534400"/>
					<stop  offset="0.9121" style="stop-color:#544500"/>
					<stop  offset="1" style="stop-color:#564700"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000091705419015261803260000009631924827035284920_);fill:url(#SVGID_00000039100960997929368800000007650492191166291133_);" width="38.7" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000078036114948544377300000002501408819564877701_" d="M26.9,26c-1.8,1.4-2.9,3.4-5.1,1.5
						c3.1,9.7,11.1,0.4,15.3,11.6c-3.1-0.1-8.7,0.1-9,4.7c-0.4,5.4,4.4,6.3,2.3,8.5c8.6-0.1,2.1-7.1,7.7-7.7c0.2,0,0.3,0,0.5,0
						c4.4-0.2,4.4,4.1,0.1,4.6c2,0.8,9.3,1.1,10.9-3.6c1.2-3.8-2.3-7.3,2.6-9.1c3.2-1.2,5.3,1.7,5.9,2.9c2.4-4-2-9.6-6.8-8.9
						s-4,5.5-6.7,6.6c-0.1,0.1-0.3,0.1-0.4,0.1c-5.3,1.2-5.3-7.7-10.6-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.4,26.9,26"/>
				</defs>
				<clipPath id="SVGID_00000145756685453565604100000013136979400774313374_">
					<use xlink:href="#SVGID_00000078036114948544377300000002501408819564877701_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000093888686477101854750000011778500219422757029_" cx="-807.0373" cy="464.7872" r="0.4213" gradientTransform="matrix(63.2892 0 0 63.2892 51123.3203 -29373.8633)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#5C4D00"/>
					<stop  offset="0.1648" style="stop-color:#534300"/>
					<stop  offset="0.4121" style="stop-color:#544400"/>
					<stop  offset="0.489" style="stop-color:#5C4D00"/>
					<stop  offset="0.6703" style="stop-color:#5C4C00"/>
					<stop  offset="0.735" style="stop-color:#5A4A00"/>
					<stop  offset="0.7527" style="stop-color:#564600"/>
					<stop  offset="0.9121" style="stop-color:#594900"/>
					<stop  offset="1" style="stop-color:#5C4C00"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000145756685453565604100000013136979400774313374_);fill:url(#SVGID_00000093888686477101854750000011778500219422757029_);" width="38.6" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000070820346148169328820000004158720781604921786_" d="M26.9,26c-1.8,1.4-2.9,3.4-5.1,1.5
						c3.1,9.7,11.2,0.4,15.3,11.7c-3.1-0.2-8.7,0.1-9,4.7c-0.4,5.4,4.4,6.3,2.3,8.5c8.6-0.1,2.1-7.1,7.7-7.7c0.2,0,0.3,0,0.5,0
						c4.5-0.2,4.4,4.1,0.1,4.7c2,0.8,9.3,1.1,10.9-3.6c1.3-3.8-2.3-7.3,2.6-9.1c3.2-1.2,5.3,1.7,5.9,2.9c2.4-4-2-9.6-6.8-8.9
						c-4.9,0.7-4,5.5-6.7,6.6c-0.1,0.1-0.3,0.1-0.4,0.1c-5.3,1.2-5.3-7.7-10.6-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.4,26.9,26
						"/>
				</defs>
				<clipPath id="SVGID_00000103236345810431787900000012910302387095980421_">
					<use xlink:href="#SVGID_00000070820346148169328820000004158720781604921786_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000180342051505624330580000009337215290385883559_" cx="-807.0367" cy="464.7915" r="0.4217" gradientTransform="matrix(63.281 0 0 63.281 51116.6719 -29370.3184)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#625300"/>
					<stop  offset="0.1648" style="stop-color:#544500"/>
					<stop  offset="0.4121" style="stop-color:#564500"/>
					<stop  offset="0.476" style="stop-color:#5F5005"/>
					<stop  offset="0.489" style="stop-color:#625306"/>
					<stop  offset="0.6703" style="stop-color:#625306"/>
					<stop  offset="0.7282" style="stop-color:#605105"/>
					<stop  offset="0.7507" style="stop-color:#5B4A01"/>
					<stop  offset="0.7527" style="stop-color:#5A4900"/>
					<stop  offset="0.9121" style="stop-color:#5D4D00"/>
					<stop  offset="1" style="stop-color:#625200"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000103236345810431787900000012910302387095980421_);fill:url(#SVGID_00000180342051505624330580000009337215290385883559_);" width="38.6" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000158021698114138835740000010300892452792234421_" d="M26.9,26c-1.8,1.4-2.9,3.4-5.1,1.5
						c3.1,9.7,11.2,0.3,15.3,11.7c-3.1-0.2-8.7,0.1-9,4.7c-0.4,5.4,4.4,6.4,2.3,8.5c8.6-0.1,2-7.1,7.7-7.8c0.2,0,0.3,0,0.5,0
						c4.5-0.2,4.4,4.2,0.1,4.7c2,0.8,9.3,1.1,10.9-3.6c1.3-3.8-2.3-7.4,2.6-9.2c3.3-1.2,5.3,1.7,5.9,2.9c2.4-4-2-9.6-6.8-8.8
						c-4.9,0.7-4,5.5-6.7,6.6c-0.1,0.1-0.3,0.1-0.4,0.1c-5.4,1.2-5.3-7.7-10.6-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26
						"/>
				</defs>
				<clipPath id="SVGID_00000163032980522360368780000007159152528181621376_">
					<use xlink:href="#SVGID_00000158021698114138835740000010300892452792234421_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000055704885346155961100000003796670881303098775_" cx="-807.0319" cy="464.7936" r="0.4214" gradientTransform="matrix(63.2728 0 0 63.2728 51109.7305 -29366.6484)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#695900"/>
					<stop  offset="0.1648" style="stop-color:#564600"/>
					<stop  offset="0.4121" style="stop-color:#584700"/>
					<stop  offset="0.4646" style="stop-color:#625209"/>
					<stop  offset="0.489" style="stop-color:#69590F"/>
					<stop  offset="0.6703" style="stop-color:#68590F"/>
					<stop  offset="0.7234" style="stop-color:#67570D"/>
					<stop  offset="0.744" style="stop-color:#615106"/>
					<stop  offset="0.7527" style="stop-color:#5D4C00"/>
					<stop  offset="0.9121" style="stop-color:#615100"/>
					<stop  offset="1" style="stop-color:#695904"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000163032980522360368780000007159152528181621376_);fill:url(#SVGID_00000055704885346155961100000003796670881303098775_);" width="38.6" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000150794282998996913770000002217029574682966418_" d="M26.9,26c-1.8,1.4-2.9,3.5-5.1,1.5
						c3.1,9.7,11.2,0.3,15.4,11.7c-3.1-0.2-8.7,0-9.1,4.6c-0.4,5.4,4.4,6.4,2.3,8.5c8.7-0.2,1.9-7.2,7.7-7.8c0.2,0,0.3,0,0.5,0
						c4.5-0.1,4.4,4.2,0.1,4.7c2,0.8,9.3,1.1,10.9-3.6c1.3-3.8-2.3-7.4,2.6-9.2c3.3-1.2,5.3,1.8,5.9,2.9c2.3-4-2.1-9.5-6.8-8.8
						c-4.9,0.7-4,5.5-6.7,6.6c-0.1,0.1-0.3,0.1-0.4,0.1c-5.4,1.2-5.3-7.7-10.6-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26
						"/>
				</defs>
				<clipPath id="SVGID_00000064327376810543365800000005569179669787482283_">
					<use xlink:href="#SVGID_00000150794282998996913770000002217029574682966418_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000114763121105937947740000013325977427337784967_" cx="-807.0243" cy="464.7859" r="0.4218" gradientTransform="matrix(63.2647 0 0 63.2647 51102.7148 -29362.3926)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#6F6006"/>
					<stop  offset="0.1648" style="stop-color:#584700"/>
					<stop  offset="0.4121" style="stop-color:#5A4800"/>
					<stop  offset="0.454" style="stop-color:#64530B"/>
					<stop  offset="0.489" style="stop-color:#6F6017"/>
					<stop  offset="0.6703" style="stop-color:#6E5F17"/>
					<stop  offset="0.7187" style="stop-color:#6D5E15"/>
					<stop  offset="0.7375" style="stop-color:#69590E"/>
					<stop  offset="0.7511" style="stop-color:#615002"/>
					<stop  offset="0.7527" style="stop-color:#604F00"/>
					<stop  offset="0.9121" style="stop-color:#665500"/>
					<stop  offset="1" style="stop-color:#6F5E0C"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000064327376810543365800000005569179669787482283_);fill:url(#SVGID_00000114763121105937947740000013325977427337784967_);" width="38.6" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000175285454182513120650000012662185709978681785_" d="M26.9,26c-1.8,1.4-2.9,3.5-5.1,1.5
						c3.1,9.7,11.3,0.2,15.4,11.8c-3.1-0.3-8.8,0-9.1,4.6c-0.4,5.4,4.5,6.4,2.3,8.5c8.7-0.2,1.9-7.2,7.7-7.8c0.2,0,0.3,0,0.5,0
						c4.5-0.1,4.4,4.2,0.1,4.7c2,0.8,9.3,1,10.9-3.6c1.3-3.8-2.3-7.4,2.6-9.2c3.3-1.2,5.3,1.8,5.9,2.9c2.3-4-2.1-9.5-6.8-8.8
						c-4.9,0.7-4,5.5-6.8,6.5c-0.1,0-0.3,0.1-0.4,0.1c-5.5,1.2-5.3-7.7-10.5-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26"
						/>
				</defs>
				<clipPath id="SVGID_00000065037938943481411930000004545559637421031339_">
					<use xlink:href="#SVGID_00000175285454182513120650000012662185709978681785_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000031892953500391242580000017185236207522255769_" cx="-807.0324" cy="464.7982" r="0.4214" gradientTransform="matrix(63.2566 0 0 63.2566 51096.6836 -29359.3828)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#76660D"/>
					<stop  offset="0.1586" style="stop-color:#5A4A01"/>
					<stop  offset="0.1648" style="stop-color:#594900"/>
					<stop  offset="0.4121" style="stop-color:#5C4900"/>
					<stop  offset="0.4465" style="stop-color:#65530B"/>
					<stop  offset="0.489" style="stop-color:#76661F"/>
					<stop  offset="0.6703" style="stop-color:#75651F"/>
					<stop  offset="0.7157" style="stop-color:#74641D"/>
					<stop  offset="0.7333" style="stop-color:#705F16"/>
					<stop  offset="0.746" style="stop-color:#69570A"/>
					<stop  offset="0.7527" style="stop-color:#635100"/>
					<stop  offset="0.9121" style="stop-color:#6A5900"/>
					<stop  offset="1" style="stop-color:#766513"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000065037938943481411930000004545559637421031339_);fill:url(#SVGID_00000031892953500391242580000017185236207522255769_);" width="38.6" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000083795507787405646590000000796362874888832932_" d="M26.9,26c-1.8,1.4-2.9,3.5-5.1,1.5
						c3.1,9.7,11.3,0.2,15.4,11.8c-3.1-0.3-8.8-0.1-9.1,4.6c-0.4,5.4,4.5,6.4,2.3,8.5c8.7-0.2,1.8-7.2,7.7-7.8c0.2,0,0.3,0,0.5,0
						c4.5-0.1,4.4,4.2,0.1,4.7c2,0.8,9.2,1,10.8-3.6c1.3-3.8-2.3-7.4,2.6-9.2c3.3-1.2,5.3,1.8,5.9,3c2.3-4-2.1-9.5-6.8-8.8
						c-4.9,0.8-4,5.5-6.8,6.5c-0.1,0-0.3,0.1-0.4,0.1c-5.5,1.2-5.3-7.7-10.5-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26"
						/>
				</defs>
				<clipPath id="SVGID_00000052072866244173401860000011305287532544388993_">
					<use xlink:href="#SVGID_00000083795507787405646590000000796362874888832932_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000052081811339553863600000004982587574746140598_" cx="-807.0202" cy="464.791" r="0.4215" gradientTransform="matrix(63.2483 0 0 63.2483 51089.1992 -29355.1211)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#7C6D13"/>
					<stop  offset="0.1337" style="stop-color:#615104"/>
					<stop  offset="0.1648" style="stop-color:#5A4A00"/>
					<stop  offset="0.4121" style="stop-color:#5E4B00"/>
					<stop  offset="0.4414" style="stop-color:#67550B"/>
					<stop  offset="0.489" style="stop-color:#7C6D26"/>
					<stop  offset="0.6703" style="stop-color:#7B6C26"/>
					<stop  offset="0.7137" style="stop-color:#7A6B24"/>
					<stop  offset="0.7306" style="stop-color:#76661D"/>
					<stop  offset="0.7428" style="stop-color:#705F11"/>
					<stop  offset="0.7527" style="stop-color:#675400"/>
					<stop  offset="0.9121" style="stop-color:#6F5D00"/>
					<stop  offset="1" style="stop-color:#7C6B19"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000052072866244173401860000011305287532544388993_);fill:url(#SVGID_00000052081811339553863600000004982587574746140598_);" width="38.6" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000076585517623572859110000007022372235842178701_" d="M26.9,26c-1.8,1.4-2.9,3.5-5.1,1.5
						c3.1,9.7,11.3,0.2,15.4,11.8c-3.1-0.3-8.8-0.1-9.1,4.5c-0.4,5.4,4.5,6.4,2.3,8.5c8.7-0.3,1.8-7.3,7.7-7.8c0.2,0,0.3,0,0.5,0
						c4.5-0.1,4.4,4.3,0.1,4.8c2,0.8,9.2,1,10.8-3.6c1.3-3.8-2.4-7.4,2.6-9.2c3.3-1.2,5.3,1.8,5.9,3c2.3-4-2.1-9.5-6.8-8.8
						c-4.9,0.8-4,5.5-6.8,6.5c-0.1,0-0.3,0.1-0.4,0.1c-5.6,1.2-5.3-7.7-10.5-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26"
						/>
				</defs>
				<clipPath id="SVGID_00000118357825290768668410000009423971317577485710_">
					<use xlink:href="#SVGID_00000076585517623572859110000007022372235842178701_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000018220056270252064300000001307017790952432827_" cx="-807.0267" cy="464.8022" r="0.4215" gradientTransform="matrix(63.2404 0 0 63.2404 51083.2305 -29352.1191)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#817318"/>
					<stop  offset="0.1185" style="stop-color:#675707"/>
					<stop  offset="0.1648" style="stop-color:#5C4B00"/>
					<stop  offset="0.4121" style="stop-color:#604C00"/>
					<stop  offset="0.4373" style="stop-color:#68550B"/>
					<stop  offset="0.4817" style="stop-color:#7D6E28"/>
					<stop  offset="0.489" style="stop-color:#81732E"/>
					<stop  offset="0.6703" style="stop-color:#80722D"/>
					<stop  offset="0.7122" style="stop-color:#7F712B"/>
					<stop  offset="0.7284" style="stop-color:#7C6C24"/>
					<stop  offset="0.7402" style="stop-color:#766518"/>
					<stop  offset="0.7498" style="stop-color:#6D5A07"/>
					<stop  offset="0.7527" style="stop-color:#6A5600"/>
					<stop  offset="0.9121" style="stop-color:#736000"/>
					<stop  offset="1" style="stop-color:#81711F"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000118357825290768668410000009423971317577485710_);fill:url(#SVGID_00000018220056270252064300000001307017790952432827_);" width="38.5" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000046334808269702025060000003496022864893316000_" d="M26.9,26c-1.8,1.4-2.8,3.5-5.1,1.5
						c3.1,9.7,11.4,0.1,15.5,11.9c-3.2-0.4-8.8-0.1-9.2,4.5c-0.4,5.4,4.5,6.4,2.3,8.5c8.7-0.3,1.7-7.3,7.7-7.9c0.2,0,0.3,0,0.5,0
						c4.5-0.1,4.4,4.3,0.1,4.8c2,0.8,9.2,1,10.8-3.6c1.3-3.8-2.4-7.4,2.6-9.3c3.4-1.2,5.3,1.8,5.9,3c2.3-4-2.2-9.5-6.8-8.8
						c-4.9,0.8-4,5.5-6.8,6.5c-0.1,0-0.3,0.1-0.4,0.1c-5.6,1.2-5.2-7.7-10.5-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26"
						/>
				</defs>
				<clipPath id="SVGID_00000017498756129236268500000003867785463498138261_">
					<use xlink:href="#SVGID_00000046334808269702025060000003496022864893316000_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000137126989496162994260000005422614453173409420_" cx="-807.0208" cy="464.8013" r="0.4216" gradientTransform="matrix(63.2324 0 0 63.2324 51076.4062 -29348.3535)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#887A1D"/>
					<stop  offset="0.1043" style="stop-color:#6E5E0B"/>
					<stop  offset="0.1648" style="stop-color:#5D4C00"/>
					<stop  offset="0.4121" style="stop-color:#614D00"/>
					<stop  offset="0.4347" style="stop-color:#69560B"/>
					<stop  offset="0.4744" style="stop-color:#7F6F28"/>
					<stop  offset="0.489" style="stop-color:#887A35"/>
					<stop  offset="0.6703" style="stop-color:#867835"/>
					<stop  offset="0.7107" style="stop-color:#857733"/>
					<stop  offset="0.7264" style="stop-color:#82732C"/>
					<stop  offset="0.7378" style="stop-color:#7C6C20"/>
					<stop  offset="0.7471" style="stop-color:#74620F"/>
					<stop  offset="0.7527" style="stop-color:#6D5900"/>
					<stop  offset="0.9121" style="stop-color:#776400"/>
					<stop  offset="1" style="stop-color:#887825"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000017498756129236268500000003867785463498138261_);fill:url(#SVGID_00000137126989496162994260000005422614453173409420_);" width="38.5" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000127032100348531804290000005680475195772927399_" d="M26.9,26c-1.8,1.4-2.8,3.5-5.1,1.5
						c3.1,9.7,11.4,0.1,15.5,11.9c-3.2-0.4-8.9-0.2-9.2,4.4c-0.4,5.4,4.5,6.4,2.3,8.5c8.7-0.4,1.6-7.4,7.7-7.9c0.2,0,0.3,0,0.5,0
						c4.5-0.1,4.4,4.3,0.1,4.8c2,0.8,9.2,1,10.8-3.6c1.3-3.8-2.4-7.5,2.6-9.3c3.4-1.2,5.3,1.9,5.9,3c2.3-4-2.2-9.5-6.8-8.8
						c-4.9,0.8-4,5.6-6.8,6.5c-0.1,0-0.3,0.1-0.4,0.1c-5.7,1.2-5.2-7.8-10.5-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26"
						/>
				</defs>
				<clipPath id="SVGID_00000124864458077967166990000008264715024301180850_">
					<use xlink:href="#SVGID_00000127032100348531804290000005680475195772927399_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000058565234092140898230000007710371996099563412_" cx="-807.0227" cy="464.8076" r="0.4216" gradientTransform="matrix(63.2245 0 0 63.2245 51070.1289 -29345.0508)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#8F8023"/>
					<stop  offset="9.499209e-02" style="stop-color:#756410"/>
					<stop  offset="0.1648" style="stop-color:#5F4D00"/>
					<stop  offset="0.4121" style="stop-color:#634E00"/>
					<stop  offset="0.4326" style="stop-color:#6B570B"/>
					<stop  offset="0.4687" style="stop-color:#807028"/>
					<stop  offset="0.489" style="stop-color:#8E803C"/>
					<stop  offset="0.6703" style="stop-color:#8D7F3C"/>
					<stop  offset="0.7097" style="stop-color:#8C7E3A"/>
					<stop  offset="0.7249" style="stop-color:#897A33"/>
					<stop  offset="0.736" style="stop-color:#837227"/>
					<stop  offset="0.745" style="stop-color:#7B6816"/>
					<stop  offset="0.7527" style="stop-color:#705B00"/>
					<stop  offset="0.893" style="stop-color:#7A6600"/>
					<stop  offset="0.9121" style="stop-color:#7C6800"/>
					<stop  offset="1" style="stop-color:#8E7E2B"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000124864458077967166990000008264715024301180850_);fill:url(#SVGID_00000058565234092140898230000007710371996099563412_);" width="38.5" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000049213455821454875620000015288418927173465018_" d="M26.9,26c-1.8,1.4-2.8,3.5-5.1,1.5
						c3.1,9.7,11.4,0.1,15.5,12c-3.2-0.4-8.9-0.2-9.2,4.4c-0.4,5.4,4.6,6.4,2.3,8.5c8.7-0.4,1.6-7.4,7.7-7.9c0.2,0,0.4,0,0.5,0
						c4.6-0.1,4.4,4.3,0.1,4.8c2,0.8,9.2,1,10.8-3.6c1.3-3.8-2.4-7.5,2.6-9.3c3.4-1.2,5.3,1.9,5.9,3c2.3-4-2.2-9.4-6.8-8.7
						c-4.9,0.8-4,5.6-6.8,6.5c-0.1,0-0.3,0.1-0.4,0.1c-5.7,1.2-5.2-7.8-10.5-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26"
						/>
				</defs>
				<clipPath id="SVGID_00000101087662166271404160000005504351949609917875_">
					<use xlink:href="#SVGID_00000049213455821454875620000015288418927173465018_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000135665854283328327780000005249479306864080024_" cx="-807.0248" cy="464.811" r="0.4215" gradientTransform="matrix(63.2167 0 0 63.2167 51063.9883 -29341.668)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#968628"/>
					<stop  offset="8.726253e-02" style="stop-color:#7B6A14"/>
					<stop  offset="0.1648" style="stop-color:#604E00"/>
					<stop  offset="0.4121" style="stop-color:#655000"/>
					<stop  offset="0.4307" style="stop-color:#6D590B"/>
					<stop  offset="0.4634" style="stop-color:#827128"/>
					<stop  offset="0.489" style="stop-color:#958744"/>
					<stop  offset="0.6703" style="stop-color:#948544"/>
					<stop  offset="0.7086" style="stop-color:#938442"/>
					<stop  offset="0.7235" style="stop-color:#90803B"/>
					<stop  offset="0.7343" style="stop-color:#8A792F"/>
					<stop  offset="0.7431" style="stop-color:#816F1E"/>
					<stop  offset="0.7507" style="stop-color:#776208"/>
					<stop  offset="0.7527" style="stop-color:#735E00"/>
					<stop  offset="0.893" style="stop-color:#7E6900"/>
					<stop  offset="0.9121" style="stop-color:#806B00"/>
					<stop  offset="1" style="stop-color:#968431"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000101087662166271404160000005504351949609917875_);fill:url(#SVGID_00000135665854283328327780000005249479306864080024_);" width="38.5" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000085222256620823034590000017526816889204840122_" d="M26.9,26c-1.8,1.4-2.8,3.6-5.1,1.5
						c3.1,9.7,11.4,0,15.6,12c-3.2-0.5-8.9-0.2-9.3,4.4c-0.4,5.4,4.6,6.4,2.3,8.5c8.7-0.4,1.5-7.4,7.7-7.9c0.2,0,0.4,0,0.5,0
						c4.6,0,4.4,4.4,0.1,4.8c2,0.8,9.2,1,10.8-3.6c1.3-3.7-2.4-7.5,2.6-9.3c3.4-1.3,5.3,1.9,5.9,3.1c2.2-4-2.2-9.4-6.8-8.7
						c-4.9,0.8-4,5.6-6.9,6.5c-0.1,0-0.3,0.1-0.4,0.1c-5.8,1.2-5.2-7.8-10.4-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26"
						/>
				</defs>
				<clipPath id="SVGID_00000064336749692120311710000015389194169743751862_">
					<use xlink:href="#SVGID_00000085222256620823034590000017526816889204840122_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000096775470387979065280000000078689372752985494_" cx="-807.014" cy="464.8028" r="0.4215" gradientTransform="matrix(63.2088 0 0 63.2088 51056.9336 -29337.4824)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#9D8D2D"/>
					<stop  offset="7.986256e-02" style="stop-color:#827119"/>
					<stop  offset="0.1648" style="stop-color:#614F00"/>
					<stop  offset="0.4121" style="stop-color:#675100"/>
					<stop  offset="0.4292" style="stop-color:#6F5A0B"/>
					<stop  offset="0.4591" style="stop-color:#837128"/>
					<stop  offset="0.489" style="stop-color:#9C8E4C"/>
					<stop  offset="0.6703" style="stop-color:#9A8C4C"/>
					<stop  offset="0.7077" style="stop-color:#998B4A"/>
					<stop  offset="0.7222" style="stop-color:#968743"/>
					<stop  offset="0.7328" style="stop-color:#908037"/>
					<stop  offset="0.7414" style="stop-color:#887626"/>
					<stop  offset="0.7488" style="stop-color:#7D6910"/>
					<stop  offset="0.7527" style="stop-color:#766000"/>
					<stop  offset="0.8781" style="stop-color:#806B00"/>
					<stop  offset="0.9121" style="stop-color:#846F00"/>
					<stop  offset="1" style="stop-color:#9C8A36"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000064336749692120311710000015389194169743751862_);fill:url(#SVGID_00000096775470387979065280000000078689372752985494_);" width="38.5" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000026843298111263602040000017659026334301757584_" d="M26.9,26.1c-1.8,1.4-2.8,3.6-5.1,1.5
						c3.1,9.7,11.5,0,15.6,12c-3.2-0.5-9-0.3-9.3,4.3c-0.4,5.4,4.6,6.4,2.3,8.5c8.7-0.5,1.4-7.5,7.7-7.9c0.2,0,0.4,0,0.5,0
						c4.6,0,4.4,4.4,0.1,4.8c2,0.8,9.1,1,10.8-3.6c1.3-3.7-2.4-7.5,2.6-9.3c3.5-1.3,5.4,1.9,5.9,3.1c2.2-4-2.3-9.4-6.8-8.7
						c-4.9,0.8-4,5.6-6.9,6.5c-0.1,0-0.3,0.1-0.4,0.1c-5.8,1.2-5.2-7.8-10.4-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26.1
						"/>
				</defs>
				<clipPath id="SVGID_00000101791967972012784800000004390855051352092083_">
					<use xlink:href="#SVGID_00000026843298111263602040000017659026334301757584_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000042702027804045502860000008486357858280193946_" cx="-807.0124" cy="464.8055" r="0.4216" gradientTransform="matrix(63.2011 0 0 63.2011 51050.6094 -29334.0742)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#A49433"/>
					<stop  offset="7.443804e-02" style="stop-color:#89781E"/>
					<stop  offset="0.1648" style="stop-color:#635100"/>
					<stop  offset="0.4121" style="stop-color:#695200"/>
					<stop  offset="0.4279" style="stop-color:#715B0B"/>
					<stop  offset="0.4557" style="stop-color:#857229"/>
					<stop  offset="0.489" style="stop-color:#A39554"/>
					<stop  offset="0.6703" style="stop-color:#A19253"/>
					<stop  offset="0.7071" style="stop-color:#A09151"/>
					<stop  offset="0.7214" style="stop-color:#9D8D4A"/>
					<stop  offset="0.7318" style="stop-color:#97863E"/>
					<stop  offset="0.7402" style="stop-color:#8E7C2D"/>
					<stop  offset="0.7475" style="stop-color:#846F17"/>
					<stop  offset="0.7527" style="stop-color:#796201"/>
					<stop  offset="0.8665" style="stop-color:#836D00"/>
					<stop  offset="0.9121" style="stop-color:#887300"/>
					<stop  offset="1" style="stop-color:#A4913C"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000101791967972012784800000004390855051352092083_);fill:url(#SVGID_00000042702027804045502860000008486357858280193946_);" width="38.5" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000086655893792305307460000009347329468941425836_" d="M26.9,26.1c-1.8,1.4-2.8,3.6-5.1,1.5
						c3.1,9.7,11.5-0.1,15.6,12.1c-3.2-0.5-9-0.3-9.3,4.3c-0.4,5.4,4.6,6.4,2.3,8.5c8.7-0.5,1.4-7.5,7.7-8c0.2,0,0.4,0,0.5,0
						c4.6,0,4.4,4.4,0.1,4.9c2,0.8,9.1,1,10.8-3.6c1.4-3.7-2.4-7.5,2.6-9.4c3.5-1.3,5.4,1.9,5.9,3.1c2.2-4-2.3-9.4-6.8-8.7
						c-4.9,0.8-4,5.6-6.9,6.5c-0.1,0-0.3,0.1-0.4,0.1c-5.9,1.2-5.2-7.8-10.4-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26.1
						"/>
				</defs>
				<clipPath id="SVGID_00000125578870618188552150000013178930226347062697_">
					<use xlink:href="#SVGID_00000086655893792305307460000009347329468941425836_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000098914028773413180780000017357711391593426364_" cx="-807.0164" cy="464.8165" r="0.4213" gradientTransform="matrix(63.1932 0 0 63.1932 51044.4531 -29331.0957)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#AB9B38"/>
					<stop  offset="6.888436e-02" style="stop-color:#907F23"/>
					<stop  offset="0.1648" style="stop-color:#645201"/>
					<stop  offset="0.4121" style="stop-color:#6B5300"/>
					<stop  offset="0.4267" style="stop-color:#725C0B"/>
					<stop  offset="0.4524" style="stop-color:#877329"/>
					<stop  offset="0.486" style="stop-color:#A79958"/>
					<stop  offset="0.489" style="stop-color:#AA9D5D"/>
					<stop  offset="0.6703" style="stop-color:#A79A5C"/>
					<stop  offset="0.7065" style="stop-color:#A6995A"/>
					<stop  offset="0.7205" style="stop-color:#A39553"/>
					<stop  offset="0.7307" style="stop-color:#9D8D47"/>
					<stop  offset="0.739" style="stop-color:#958336"/>
					<stop  offset="0.7461" style="stop-color:#8A7520"/>
					<stop  offset="0.7524" style="stop-color:#7D6505"/>
					<stop  offset="0.7527" style="stop-color:#7C6403"/>
					<stop  offset="0.857" style="stop-color:#856F01"/>
					<stop  offset="0.9121" style="stop-color:#8C7700"/>
					<stop  offset="1" style="stop-color:#AA9842"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000125578870618188552150000013178930226347062697_);fill:url(#SVGID_00000098914028773413180780000017357711391593426364_);" width="38.5" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000147185783188794248700000004611997799188510080_" d="M26.9,26.1c-1.8,1.4-2.8,3.6-5.1,1.5
						c3.1,9.7,11.5-0.1,15.6,12.1c-3.2-0.6-9-0.3-9.3,4.3c-0.4,5.4,4.7,6.4,2.3,8.5c8.8-0.5,1.3-7.5,7.7-8c0.2,0,0.4,0,0.5,0
						c4.6,0,4.4,4.4,0.1,4.9c2,0.8,9.1,1,10.8-3.6c1.4-3.7-2.4-7.5,2.7-9.4c3.5-1.3,5.4,2,5.9,3.1c2.2-4-2.3-9.4-6.8-8.7
						c-5,0.8-3.9,5.6-6.9,6.5c-0.1,0-0.3,0.1-0.4,0.1c-5.9,1.2-5.2-7.8-10.4-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26.1
						"/>
				</defs>
				<clipPath id="SVGID_00000165223575746136460890000002623212912869810087_">
					<use xlink:href="#SVGID_00000147185783188794248700000004611997799188510080_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000096753903972111068610000001593451729903691444_" cx="-807.0137" cy="464.8171" r="0.4213" gradientTransform="matrix(63.1854 0 0 63.1854 51037.9805 -29327.5098)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#B2A23D"/>
					<stop  offset="6.413458e-02" style="stop-color:#978629"/>
					<stop  offset="0.1648" style="stop-color:#655303"/>
					<stop  offset="0.4121" style="stop-color:#6D5400"/>
					<stop  offset="0.4258" style="stop-color:#745D0B"/>
					<stop  offset="0.4499" style="stop-color:#887429"/>
					<stop  offset="0.4814" style="stop-color:#A89A58"/>
					<stop  offset="0.489" style="stop-color:#B1A465"/>
					<stop  offset="0.6703" style="stop-color:#AEA165"/>
					<stop  offset="0.7058" style="stop-color:#ADA063"/>
					<stop  offset="0.7196" style="stop-color:#AA9C5C"/>
					<stop  offset="0.7296" style="stop-color:#A49450"/>
					<stop  offset="0.7378" style="stop-color:#9B8A3F"/>
					<stop  offset="0.7448" style="stop-color:#917D28"/>
					<stop  offset="0.751" style="stop-color:#836C0D"/>
					<stop  offset="0.7527" style="stop-color:#7F6704"/>
					<stop  offset="0.8529" style="stop-color:#897202"/>
					<stop  offset="0.9121" style="stop-color:#917B00"/>
					<stop  offset="1" style="stop-color:#B29F48"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000165223575746136460890000002623212912869810087_);fill:url(#SVGID_00000096753903972111068610000001593451729903691444_);" width="38.4" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000001662786969423327010000003311314902393207438_" d="M26.9,26.1c-1.8,1.4-2.8,3.6-5.1,1.5
						c3.1,9.7,11.6-0.1,15.7,12.1c-3.2-0.6-9-0.4-9.4,4.2c-0.4,5.4,4.7,6.4,2.3,8.5c8.8-0.6,1.2-7.6,7.8-8c0.2,0,0.4,0,0.5,0
						c4.6,0,4.4,4.5,0.1,4.9c2,0.8,9.1,0.9,10.8-3.6c1.4-3.7-2.4-7.6,2.7-9.4c3.5-1.3,5.4,2,5.9,3.1c2.2-4-2.3-9.4-6.8-8.7
						c-5,0.8-3.9,5.6-6.9,6.4c-0.1,0-0.3,0.1-0.4,0.1c-6,1.2-5.2-7.8-10.4-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.6,25.5,26.9,26.1"
						/>
				</defs>
				<clipPath id="SVGID_00000119839810527695610230000013635558257135915435_">
					<use xlink:href="#SVGID_00000001662786969423327010000003311314902393207438_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000072256378108666081010000002479303176812451202_" cx="-807.0073" cy="464.813" r="0.4212" gradientTransform="matrix(63.1777 0 0 63.1777 51031.3789 -29323.666)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#B9AA43"/>
					<stop  offset="5.938442e-02" style="stop-color:#9E8E2F"/>
					<stop  offset="0.1648" style="stop-color:#675404"/>
					<stop  offset="0.4121" style="stop-color:#6F5600"/>
					<stop  offset="0.4249" style="stop-color:#765F0B"/>
					<stop  offset="0.4474" style="stop-color:#8A7629"/>
					<stop  offset="0.4769" style="stop-color:#AA9B58"/>
					<stop  offset="0.489" style="stop-color:#B8AC6E"/>
					<stop  offset="0.6703" style="stop-color:#B5A86D"/>
					<stop  offset="0.7053" style="stop-color:#B4A76B"/>
					<stop  offset="0.7189" style="stop-color:#B0A364"/>
					<stop  offset="0.7288" style="stop-color:#AA9B58"/>
					<stop  offset="0.7369" style="stop-color:#A29147"/>
					<stop  offset="0.7439" style="stop-color:#968330"/>
					<stop  offset="0.75" style="stop-color:#897215"/>
					<stop  offset="0.7527" style="stop-color:#816906"/>
					<stop  offset="0.8491" style="stop-color:#8C7403"/>
					<stop  offset="0.9121" style="stop-color:#957E00"/>
					<stop  offset="1" style="stop-color:#B9A64F"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000119839810527695610230000013635558257135915435_);fill:url(#SVGID_00000072256378108666081010000002479303176812451202_);" width="38.4" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000067230201898045094060000002095583652354107053_" d="M26.9,26.1c-1.8,1.4-2.8,3.6-5.1,1.5
						c3.1,9.7,11.6-0.2,15.7,12.2c-3.2-0.7-9.1-0.4-9.4,4.2c-0.4,5.4,4.7,6.4,2.3,8.5c8.8-0.6,1.2-7.6,7.8-8c0.2,0,0.4,0,0.5,0
						c4.6,0,4.4,4.5,0.1,4.9c2,0.8,9.1,0.9,10.7-3.6c1.4-3.7-2.5-7.6,2.7-9.4c3.6-1.3,5.4,2,5.9,3.2c2.2-4-2.4-9.3-6.8-8.6
						c-5,0.8-3.9,5.6-6.9,6.4c-0.1,0-0.3,0.1-0.4,0.1c-6,1.2-5.1-7.8-10.4-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.6,25.5,26.9,26.1"
						/>
				</defs>
				<clipPath id="SVGID_00000034793786046923698990000012967720651671520427_">
					<use xlink:href="#SVGID_00000067230201898045094060000002095583652354107053_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000009568176808715831870000015104545461680031158_" cx="-807.0087" cy="464.8165" r="0.4217" gradientTransform="matrix(63.1701 0 0 63.1701 51025.332 -29320.3652)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#C0B047"/>
					<stop  offset="5.642876e-02" style="stop-color:#A59433"/>
					<stop  offset="0.1648" style="stop-color:#685505"/>
					<stop  offset="0.4121" style="stop-color:#715700"/>
					<stop  offset="0.4241" style="stop-color:#78600B"/>
					<stop  offset="0.4453" style="stop-color:#8C7629"/>
					<stop  offset="0.473" style="stop-color:#AB9B58"/>
					<stop  offset="0.489" style="stop-color:#BFB377"/>
					<stop  offset="0.6703" style="stop-color:#BBAF76"/>
					<stop  offset="0.7048" style="stop-color:#BAAE74"/>
					<stop  offset="0.7182" style="stop-color:#B7AA6D"/>
					<stop  offset="0.7279" style="stop-color:#B1A261"/>
					<stop  offset="0.7358" style="stop-color:#A89850"/>
					<stop  offset="0.7427" style="stop-color:#9D8A39"/>
					<stop  offset="0.7487" style="stop-color:#8F791E"/>
					<stop  offset="0.7527" style="stop-color:#846B07"/>
					<stop  offset="0.8457" style="stop-color:#8F7603"/>
					<stop  offset="0.9121" style="stop-color:#998100"/>
					<stop  offset="1" style="stop-color:#BFAD55"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000034793786046923698990000012967720651671520427_);fill:url(#SVGID_00000009568176808715831870000015104545461680031158_);" width="38.4" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000180340207861754704660000013533414737178494113_" d="M26.9,26.1c-1.8,1.4-2.7,3.6-5.1,1.4
						c3.1,9.7,11.6-0.2,15.7,12.2c-3.3-0.7-9.1-0.5-9.4,4.2c-0.4,5.4,4.7,6.4,2.3,8.5c8.8-0.6,1.1-7.7,7.8-8c0.2,0,0.4,0,0.5,0
						c4.6,0.1,4.4,4.5,0.1,4.9c2,0.8,9,0.9,10.7-3.6c1.4-3.7-2.5-7.6,2.7-9.4c3.6-1.3,5.4,2,5.9,3.2c2.1-4-2.4-9.3-6.8-8.6
						c-5,0.8-3.9,5.6-6.9,6.4c-0.1,0-0.3,0.1-0.4,0.1c-6.1,1.2-5.1-7.8-10.3-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.6,25.5,26.9,26.1
						"/>
				</defs>
				<clipPath id="SVGID_00000124841541697029764420000013202927669819555006_">
					<use xlink:href="#SVGID_00000180340207861754704660000013533414737178494113_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000052080347639520902110000010165779991609877949_" cx="-807.0129" cy="464.8248" r="0.4219" gradientTransform="matrix(63.1628 0 0 63.1628 51019.6875 -29317.4902)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#C7B84D"/>
					<stop  offset="5.229685e-02" style="stop-color:#AC9C39"/>
					<stop  offset="0.1648" style="stop-color:#695506"/>
					<stop  offset="0.4121" style="stop-color:#725700"/>
					<stop  offset="0.4235" style="stop-color:#79600B"/>
					<stop  offset="0.4435" style="stop-color:#8D7729"/>
					<stop  offset="0.4697" style="stop-color:#AC9C58"/>
					<stop  offset="0.489" style="stop-color:#C6BB80"/>
					<stop  offset="0.6703" style="stop-color:#C3B77F"/>
					<stop  offset="0.7043" style="stop-color:#C2B67D"/>
					<stop  offset="0.7175" style="stop-color:#BEB176"/>
					<stop  offset="0.7271" style="stop-color:#B8AA6A"/>
					<stop  offset="0.735" style="stop-color:#B09F59"/>
					<stop  offset="0.7417" style="stop-color:#A49142"/>
					<stop  offset="0.7477" style="stop-color:#968027"/>
					<stop  offset="0.7527" style="stop-color:#876D09"/>
					<stop  offset="0.8425" style="stop-color:#927805"/>
					<stop  offset="0.9121" style="stop-color:#9E8400"/>
					<stop  offset="1" style="stop-color:#C6B45C"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000124841541697029764420000013202927669819555006_);fill:url(#SVGID_00000052080347639520902110000010165779991609877949_);" width="38.4" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000088106091504122860110000003174664686860977299_" d="M26.9,26.1c-1.8,1.4-2.7,3.6-5.1,1.4
						c3.1,9.7,11.7-0.3,15.7,12.2c-3.3-0.7-9.1-0.5-9.4,4.1c-0.4,5.4,4.8,6.4,2.3,8.5c8.8-0.7,1-7.7,7.8-8.1c0.2,0,0.4,0,0.5,0
						c4.7,0.1,4.4,4.5,0.1,5c2,0.8,9,0.9,10.7-3.6c1.4-3.7-2.5-7.6,2.7-9.5c3.6-1.3,5.4,2,5.9,3.2c2.1-4-2.4-9.3-6.8-8.6
						c-5,0.8-3.9,5.6-7,6.4c-0.1,0-0.3,0.1-0.4,0.1c-6.1,1.1-5.1-7.8-10.3-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.6,25.5,26.9,26.1"
						/>
				</defs>
				<clipPath id="SVGID_00000036217459803059881360000013768790032850369681_">
					<use xlink:href="#SVGID_00000088106091504122860110000003174664686860977299_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000056423586555239439350000014001353911102014638_" cx="-807.0039" cy="464.8196" r="0.4214" gradientTransform="matrix(63.1551 0 0 63.1551 51012.8945 -29313.5859)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#CEC053"/>
					<stop  offset="4.916528e-02" style="stop-color:#B4A43F"/>
					<stop  offset="0.1548" style="stop-color:#715D0D"/>
					<stop  offset="0.1648" style="stop-color:#6A5608"/>
					<stop  offset="0.4121" style="stop-color:#745801"/>
					<stop  offset="0.4229" style="stop-color:#7B610C"/>
					<stop  offset="0.442" style="stop-color:#8F782A"/>
					<stop  offset="0.467" style="stop-color:#AE9D59"/>
					<stop  offset="0.489" style="stop-color:#CDC389"/>
					<stop  offset="0.6703" style="stop-color:#C9BE88"/>
					<stop  offset="0.7038" style="stop-color:#C8BD86"/>
					<stop  offset="0.7169" style="stop-color:#C5B87F"/>
					<stop  offset="0.7263" style="stop-color:#BFB173"/>
					<stop  offset="0.7341" style="stop-color:#B6A662"/>
					<stop  offset="0.7407" style="stop-color:#AB984B"/>
					<stop  offset="0.7467" style="stop-color:#9D8730"/>
					<stop  offset="0.752" style="stop-color:#8D730F"/>
					<stop  offset="0.7527" style="stop-color:#8A700A"/>
					<stop  offset="0.8396" style="stop-color:#957B05"/>
					<stop  offset="0.9121" style="stop-color:#A28800"/>
					<stop  offset="1" style="stop-color:#CDBB63"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000036217459803059881360000013768790032850369681_);fill:url(#SVGID_00000056423586555239439350000014001353911102014638_);" width="38.4" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000120517461764072170090000008142326651259062196_" d="M26.9,26.1c-1.8,1.4-2.7,3.7-5.1,1.4
						c3.1,9.7,11.7-0.3,15.8,12.3c-3.3-0.8-9.1-0.5-9.5,4.1c-0.4,5.4,4.8,6.4,2.3,8.5c8.8-0.7,1-7.8,7.8-8.1c0.2,0,0.4,0,0.5,0
						c4.7,0.1,4.4,4.6,0,5c2,0.8,9,0.9,10.7-3.6c1.4-3.7-2.5-7.6,2.7-9.5c3.6-1.3,5.4,2.1,5.9,3.2c2.1-4-2.4-9.3-6.8-8.6
						c-5,0.8-3.9,5.6-7,6.4c-0.1,0-0.3,0.1-0.4,0.1c-6.2,1.1-5.1-7.8-10.3-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.6,25.5,26.9,26.1"
						/>
				</defs>
				<clipPath id="SVGID_00000044179477664587320190000012145134919550728893_">
					<use xlink:href="#SVGID_00000120517461764072170090000008142326651259062196_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000164471509003411369470000016206436918369008029_" cx="-807.0104" cy="464.8334" r="0.4214" gradientTransform="matrix(63.1476 0 0 63.1476 51007.25 -29310.9414)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#D6C859"/>
					<stop  offset="4.640178e-02" style="stop-color:#BCAC45"/>
					<stop  offset="0.1461" style="stop-color:#786513"/>
					<stop  offset="0.1648" style="stop-color:#6B5709"/>
					<stop  offset="0.4121" style="stop-color:#765902"/>
					<stop  offset="0.4224" style="stop-color:#7D620D"/>
					<stop  offset="0.4406" style="stop-color:#91792B"/>
					<stop  offset="0.4645" style="stop-color:#B09F5B"/>
					<stop  offset="0.489" style="stop-color:#D5CB93"/>
					<stop  offset="0.6703" style="stop-color:#D0C691"/>
					<stop  offset="0.7035" style="stop-color:#CFC58F"/>
					<stop  offset="0.7163" style="stop-color:#CCC088"/>
					<stop  offset="0.7257" style="stop-color:#C5B97C"/>
					<stop  offset="0.7333" style="stop-color:#BDAE6B"/>
					<stop  offset="0.7399" style="stop-color:#B19F54"/>
					<stop  offset="0.7458" style="stop-color:#A38D39"/>
					<stop  offset="0.751" style="stop-color:#937918"/>
					<stop  offset="0.7527" style="stop-color:#8D710C"/>
					<stop  offset="0.8343" style="stop-color:#987C07"/>
					<stop  offset="0.9121" style="stop-color:#A68B00"/>
					<stop  offset="1" style="stop-color:#D5C36A"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000044179477664587320190000012145134919550728893_);fill:url(#SVGID_00000164471509003411369470000016206436918369008029_);" width="38.4" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000042005340226058086420000003206111103541958797_" d="M26.8,26.1c-1.8,1.4-2.7,3.7-5.1,1.4
						c3.1,9.7,11.7-0.3,15.8,12.3c-3.3-0.8-9.2-0.6-9.5,4c-0.4,5.4,4.8,6.4,2.3,8.5c8.8-0.7,0.9-7.8,7.8-8.1c0.2,0,0.4,0,0.5,0
						c4.7,0.1,4.4,4.6,0,5c2,0.8,9,0.9,10.7-3.6c1.4-3.7-2.5-7.6,2.7-9.5c3.7-1.3,5.4,2.1,5.9,3.2c2.1-4-2.5-9.3-6.8-8.6
						c-5,0.8-3.9,5.6-7,6.4c-0.1,0-0.3,0.1-0.4,0.1c-6.2,1.1-5.1-7.8-10.3-11c-1.5-1-2.8-1.3-3.8-1.3C28.5,25,27.6,25.5,26.8,26.1"
						/>
				</defs>
				<clipPath id="SVGID_00000030465895572393584620000008738417626293457829_">
					<use xlink:href="#SVGID_00000042005340226058086420000003206111103541958797_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000001640290702460083760000011521475664099558802_" cx="-806.9972" cy="464.8225" r="0.4213" gradientTransform="matrix(63.1401 0 0 63.1401 51000.3828 -29306.8008)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#DECF5F"/>
					<stop  offset="4.427637e-02" style="stop-color:#C3B34B"/>
					<stop  offset="0.1394" style="stop-color:#7F6C19"/>
					<stop  offset="0.1648" style="stop-color:#6C580B"/>
					<stop  offset="0.3965" style="stop-color:#775A04"/>
					<stop  offset="0.4121" style="stop-color:#785A03"/>
					<stop  offset="0.4219" style="stop-color:#7F630E"/>
					<stop  offset="0.4393" style="stop-color:#927A2C"/>
					<stop  offset="0.4621" style="stop-color:#B2A05C"/>
					<stop  offset="0.489" style="stop-color:#DCD39D"/>
					<stop  offset="0.6703" style="stop-color:#D7CE9B"/>
					<stop  offset="0.703" style="stop-color:#D6CD99"/>
					<stop  offset="0.7157" style="stop-color:#D3C892"/>
					<stop  offset="0.7249" style="stop-color:#CCC186"/>
					<stop  offset="0.7324" style="stop-color:#C4B675"/>
					<stop  offset="0.7389" style="stop-color:#B9A85E"/>
					<stop  offset="0.7447" style="stop-color:#AB9643"/>
					<stop  offset="0.7499" style="stop-color:#9B8222"/>
					<stop  offset="0.7527" style="stop-color:#90740D"/>
					<stop  offset="0.832" style="stop-color:#9B7F08"/>
					<stop  offset="0.9121" style="stop-color:#AA8F00"/>
					<stop  offset="1" style="stop-color:#DCCA71"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000030465895572393584620000008738417626293457829_);fill:url(#SVGID_00000001640290702460083760000011521475664099558802_);" width="38.3" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000060735008374650031470000015284103533115009947_" d="M26.8,26.1c-1.8,1.4-2.7,3.7-5.1,1.4
						c3.1,9.7,11.7-0.4,15.8,12.4c-3.3-0.8-9.2-0.6-9.5,4c-0.4,5.4,4.8,6.4,2.3,8.5c8.8-0.8,0.8-7.8,7.8-8.1c0.2,0,0.4,0,0.5,0
						c4.7,0.1,4.4,4.6,0,5c2,0.8,9,0.9,10.7-3.6c1.4-3.7-2.5-7.6,2.7-9.5c3.7-1.3,5.4,2.1,6,3.3c2.1-4-2.5-9.3-6.8-8.6
						c-5,0.8-3.9,5.6-7,6.4c-0.1,0-0.3,0.1-0.4,0.1c-6.3,1.1-5.1-7.8-10.3-11c-1.5-1-2.8-1.3-3.8-1.3C28.5,25,27.6,25.5,26.8,26.1"
						/>
				</defs>
				<clipPath id="SVGID_00000144319575901160827910000016540681158176653189_">
					<use xlink:href="#SVGID_00000060735008374650031470000015284103533115009947_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000065754246503412052270000004826004021233409967_" cx="-807.0021" cy="464.8312" r="0.4214" gradientTransform="matrix(63.1328 0 0 63.1328 50994.793 -29303.9297)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#E6D765"/>
					<stop  offset="4.174364e-02" style="stop-color:#CBBB51"/>
					<stop  offset="0.1314" style="stop-color:#887420"/>
					<stop  offset="0.1648" style="stop-color:#6D580C"/>
					<stop  offset="0.3824" style="stop-color:#785B05"/>
					<stop  offset="0.4121" style="stop-color:#7A5B04"/>
					<stop  offset="0.4215" style="stop-color:#81640F"/>
					<stop  offset="0.4381" style="stop-color:#947B2D"/>
					<stop  offset="0.4599" style="stop-color:#B4A15D"/>
					<stop  offset="0.4859" style="stop-color:#DFD59F"/>
					<stop  offset="0.489" style="stop-color:#E4DCA7"/>
					<stop  offset="0.6703" style="stop-color:#DFD5A5"/>
					<stop  offset="0.7026" style="stop-color:#DED4A3"/>
					<stop  offset="0.7151" style="stop-color:#DACF9C"/>
					<stop  offset="0.7243" style="stop-color:#D4C890"/>
					<stop  offset="0.7317" style="stop-color:#CBBD7F"/>
					<stop  offset="0.7381" style="stop-color:#C0AE68"/>
					<stop  offset="0.7439" style="stop-color:#B29C4D"/>
					<stop  offset="0.749" style="stop-color:#A1882C"/>
					<stop  offset="0.7527" style="stop-color:#92750F"/>
					<stop  offset="0.8278" style="stop-color:#9D8009"/>
					<stop  offset="0.9121" style="stop-color:#AE9200"/>
					<stop  offset="1" style="stop-color:#E4D278"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000144319575901160827910000016540681158176653189_);fill:url(#SVGID_00000065754246503412052270000004826004021233409967_);" width="38.3" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000163764092156713048550000008863886732063175822_" d="M26.8,26.1c-1.8,1.4-2.7,3.7-5.1,1.4
						c3.1,9.7,11.8-0.4,15.8,12.4c-3.3-0.9-9.2-0.6-9.5,4c-0.4,5.4,4.9,6.4,2.3,8.5c8.8-0.8,0.8-7.9,7.8-8.1c0.2,0,0.4,0,0.5,0
						c4.7,0.1,4.4,4.6,0,5c2,0.8,8.9,0.9,10.7-3.6c1.4-3.7-2.5-7.7,2.7-9.5c3.7-1.3,5.4,2.1,6,3.3c2.1-4-2.5-9.2-6.8-8.6
						c-5,0.8-3.9,5.6-7,6.4c-0.1,0-0.3,0.1-0.4,0.1c-6.3,1.1-5.1-7.8-10.3-11c-1.5-1-2.8-1.3-3.8-1.3C28.5,25,27.6,25.5,26.8,26.1"
						/>
				</defs>
				<clipPath id="SVGID_00000045607494850558830860000016704961472999739327_">
					<use xlink:href="#SVGID_00000163764092156713048550000008863886732063175822_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000055696491521854640970000001151124749211734951_" cx="-807.0042" cy="464.8357" r="0.4216" gradientTransform="matrix(63.1255 0 0 63.1255 50989.0273 -29300.8711)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#EDE06C"/>
					<stop  offset="3.951054e-02" style="stop-color:#D3C459"/>
					<stop  offset="0.1243" style="stop-color:#907D27"/>
					<stop  offset="0.1648" style="stop-color:#6E590E"/>
					<stop  offset="0.3824" style="stop-color:#795C06"/>
					<stop  offset="0.4121" style="stop-color:#7B5C05"/>
					<stop  offset="0.4211" style="stop-color:#826510"/>
					<stop  offset="0.437" style="stop-color:#967C2E"/>
					<stop  offset="0.458" style="stop-color:#B5A25E"/>
					<stop  offset="0.4829" style="stop-color:#E1D6A0"/>
					<stop  offset="0.489" style="stop-color:#ECE4B1"/>
					<stop  offset="0.6703" style="stop-color:#E6DEAF"/>
					<stop  offset="0.7022" style="stop-color:#E5DDAD"/>
					<stop  offset="0.7146" style="stop-color:#E1D8A6"/>
					<stop  offset="0.7236" style="stop-color:#DBD19A"/>
					<stop  offset="0.7309" style="stop-color:#D3C589"/>
					<stop  offset="0.7373" style="stop-color:#C7B772"/>
					<stop  offset="0.7429" style="stop-color:#B9A557"/>
					<stop  offset="0.748" style="stop-color:#A99136"/>
					<stop  offset="0.7526" style="stop-color:#967912"/>
					<stop  offset="0.7527" style="stop-color:#957810"/>
					<stop  offset="0.8258" style="stop-color:#A0830A"/>
					<stop  offset="0.9121" style="stop-color:#B29600"/>
					<stop  offset="1" style="stop-color:#EBD980"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000045607494850558830860000016704961472999739327_);fill:url(#SVGID_00000055696491521854640970000001151124749211734951_);" width="38.3" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000075865790906622433810000008864431621239101347_" d="M26.8,26.1c-1.8,1.4-2.7,3.7-5,1.4
						c3.1,9.7,11.8-0.5,15.9,12.4c-3.3-0.9-9.2-0.7-9.6,3.9c-0.4,5.4,4.9,6.4,2.3,8.5c8.9-0.9,0.7-7.9,7.8-8.2c0.2,0,0.4,0,0.5,0
						c4.7,0.1,4.4,4.6,0,5c2,0.8,8.9,0.9,10.7-3.6c1.4-3.7-2.5-7.7,2.7-9.6c3.7-1.3,5.4,2.1,6,3.3c2.1-4-2.6-9.2-6.8-8.5
						c-5,0.8-3.9,5.6-7,6.4c-0.1,0-0.3,0.1-0.4,0.1c-6.4,1.1-5.1-7.8-10.2-11c-1.5-1-2.8-1.3-3.8-1.3C28.5,25,27.6,25.5,26.8,26.1"
						/>
				</defs>
				<clipPath id="SVGID_00000164484756442143184390000000473135833510326945_">
					<use xlink:href="#SVGID_00000075865790906622433810000008864431621239101347_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000099621603000148901190000002187472058798276487_" cx="-806.9939" cy="464.8295" r="0.4214" gradientTransform="matrix(63.1182 0 0 63.1182 50982.4727 -29297.0586)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#F5E872"/>
					<stop  offset="3.773879e-02" style="stop-color:#DBCC5F"/>
					<stop  offset="0.1187" style="stop-color:#98852D"/>
					<stop  offset="0.1648" style="stop-color:#6F5A0F"/>
					<stop  offset="0.3702" style="stop-color:#7A5C08"/>
					<stop  offset="0.4121" style="stop-color:#7D5D06"/>
					<stop  offset="0.4207" style="stop-color:#846611"/>
					<stop  offset="0.4359" style="stop-color:#977D2F"/>
					<stop  offset="0.4558" style="stop-color:#B7A35F"/>
					<stop  offset="0.4795" style="stop-color:#E2D7A1"/>
					<stop  offset="0.489" style="stop-color:#F4EDBD"/>
					<stop  offset="0.6703" style="stop-color:#EDE6BA"/>
					<stop  offset="0.7018" style="stop-color:#ECE5B8"/>
					<stop  offset="0.714" style="stop-color:#E8E0B1"/>
					<stop  offset="0.7229" style="stop-color:#E2D8A5"/>
					<stop  offset="0.7302" style="stop-color:#DACD94"/>
					<stop  offset="0.7364" style="stop-color:#CEBF7D"/>
					<stop  offset="0.742" style="stop-color:#C0AD62"/>
					<stop  offset="0.7471" style="stop-color:#AF9841"/>
					<stop  offset="0.7516" style="stop-color:#9C801B"/>
					<stop  offset="0.7527" style="stop-color:#977911"/>
					<stop  offset="0.8206" style="stop-color:#A1840B"/>
					<stop  offset="0.9121" style="stop-color:#B69A00"/>
					<stop  offset="1" style="stop-color:#F3E287"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000164484756442143184390000000473135833510326945_);fill:url(#SVGID_00000099621603000148901190000002187472058798276487_);" width="38.3" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000051385056783987559940000002073356570696930176_" d="M26.8,26.1c-1.8,1.4-2.7,3.7-5,1.4
						C24.9,37.2,33.6,27,37.7,40c-3.3-0.9-9.3-0.7-9.6,3.9c-0.4,5.4,4.9,6.5,2.3,8.5c8.9-0.9,0.6-8,7.8-8.2c0.2,0,0.4,0,0.5,0
						c4.7,0.2,4.4,4.7,0,5.1c2,0.8,8.9,0.9,10.7-3.6c1.4-3.7-2.5-7.7,2.7-9.6c3.8-1.4,5.4,2.2,6,3.3c2-4-2.6-9.2-6.8-8.5
						c-5,0.8-3.9,5.6-7.1,6.4c-0.2,0-0.3,0.1-0.4,0.1c-6.5,1.1-5-7.8-10.2-11c-1.5-1-2.8-1.3-3.8-1.3C28.5,25,27.6,25.5,26.8,26.1"
						/>
				</defs>
				<clipPath id="SVGID_00000008830852427547349660000013256982703592180387_">
					<use xlink:href="#SVGID_00000051385056783987559940000002073356570696930176_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000076598378189899173970000010730955330394606518_" cx="-807.0055" cy="464.8435" r="0.421" gradientTransform="matrix(63.1111 0 0 63.1111 50977.4688 -29294.6445)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FDF079"/>
					<stop  offset="3.613938e-02" style="stop-color:#E3D465"/>
					<stop  offset="0.1137" style="stop-color:#9F8D33"/>
					<stop  offset="0.1648" style="stop-color:#705B10"/>
					<stop  offset="0.3594" style="stop-color:#7B5D09"/>
					<stop  offset="0.4121" style="stop-color:#7F5E07"/>
					<stop  offset="0.4204" style="stop-color:#866712"/>
					<stop  offset="0.435" style="stop-color:#997E30"/>
					<stop  offset="0.4542" style="stop-color:#B8A460"/>
					<stop  offset="0.4771" style="stop-color:#E3D8A2"/>
					<stop  offset="0.489" style="stop-color:#FBF5C7"/>
					<stop  offset="0.6703" style="stop-color:#F4EEC4"/>
					<stop  offset="0.7014" style="stop-color:#F3EDC2"/>
					<stop  offset="0.7135" style="stop-color:#EFE8BB"/>
					<stop  offset="0.7223" style="stop-color:#E9E0AF"/>
					<stop  offset="0.7295" style="stop-color:#E1D59E"/>
					<stop  offset="0.7357" style="stop-color:#D5C787"/>
					<stop  offset="0.7412" style="stop-color:#C7B56C"/>
					<stop  offset="0.7462" style="stop-color:#B7A04B"/>
					<stop  offset="0.7507" style="stop-color:#A48825"/>
					<stop  offset="0.7527" style="stop-color:#9A7B12"/>
					<stop  offset="0.819" style="stop-color:#A4860C"/>
					<stop  offset="0.9121" style="stop-color:#BA9D00"/>
					<stop  offset="1" style="stop-color:#FBE98E"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000008830852427547349660000013256982703592180387_);fill:url(#SVGID_00000076598378189899173970000010730955330394606518_);" width="38.3" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000115479569657485760120000000373167489984327336_" d="M26.8,26.1c-1.8,1.4-2.7,3.7-5,1.4
						C24.9,37.2,33.7,27,37.7,40c-3.3-1-9.3-0.7-9.6,3.9c-0.4,5.4,4.9,6.5,2.3,8.5c8.9-0.9,0.6-8,7.8-8.2c5.3-0.2,5,4.7,0.6,5.1
						c2,0.8,8.9,0.8,10.7-3.6C51,42,47,38,52.2,36.1c3.8-1.4,5.4,2.2,6,3.4c2-4-2.6-9.2-6.8-8.5c-5.1,0.8-3.9,5.6-7.1,6.3
						c-7,1.6-5.4-7.7-10.6-11c-1.5-1-2.8-1.3-3.8-1.3C28.5,25,27.6,25.5,26.8,26.1"/>
				</defs>
				<clipPath id="SVGID_00000156582625073136662300000002023165933825578894_">
					<use xlink:href="#SVGID_00000115479569657485760120000000373167489984327336_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000065063627319847247590000015028479877500513707_" cx="-806.993" cy="464.8366" r="0.4216" gradientTransform="matrix(63.1037 0 0 63.1037 50970.6914 -29290.7402)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF57F"/>
					<stop  offset="3.508200e-02" style="stop-color:#E5D96B"/>
					<stop  offset="0.1103" style="stop-color:#A49239"/>
					<stop  offset="0.1648" style="stop-color:#715B12"/>
					<stop  offset="0.3594" style="stop-color:#7C5E0B"/>
					<stop  offset="0.4121" style="stop-color:#805F08"/>
					<stop  offset="0.4201" style="stop-color:#876813"/>
					<stop  offset="0.4343" style="stop-color:#9A7F31"/>
					<stop  offset="0.4529" style="stop-color:#B8A461"/>
					<stop  offset="0.475" style="stop-color:#E2D8A3"/>
					<stop  offset="0.489" style="stop-color:#FFFBD0"/>
					<stop  offset="0.6703" style="stop-color:#FAF5CE"/>
					<stop  offset="0.7011" style="stop-color:#F9F4CC"/>
					<stop  offset="0.7131" style="stop-color:#F6EFC5"/>
					<stop  offset="0.7218" style="stop-color:#F0E8B9"/>
					<stop  offset="0.7289" style="stop-color:#E7DCA8"/>
					<stop  offset="0.735" style="stop-color:#DCCE91"/>
					<stop  offset="0.7405" style="stop-color:#CEBC76"/>
					<stop  offset="0.7454" style="stop-color:#BEA755"/>
					<stop  offset="0.7499" style="stop-color:#AB8F2F"/>
					<stop  offset="0.7527" style="stop-color:#9D7D13"/>
					<stop  offset="0.8175" style="stop-color:#A7880D"/>
					<stop  offset="0.9121" style="stop-color:#BEA000"/>
					<stop  offset="1" style="stop-color:#FFF095"/>
				</radialGradient>
				
					<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000156582625073136662300000002023165933825578894_);fill:url(#SVGID_00000065063627319847247590000015028479877500513707_);" width="38.3" height="27.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000126318339778877784150000016348742010228258946_" d="M29.6,25.1c-1.1,0-2,0.5-2.7,1
						c-1.8,1.3-2.7,3.7-5,1.5c3,9.5,11.8-0.6,15.9,12.5c-3.3-0.9-9.3-0.7-9.6,3.8c0,0.1,0,0.2,0,0.3c-0.2,5.1,4.8,6.2,2.3,8.2
						c8.6-0.9,0.5-8,7.7-8.2c0.2,0,0.4,0,0.6,0c4.7,0.2,4.3,4.7,0.1,5.1c2,0.7,8.7,0.8,10.4-3.6c1.4-3.5-2.2-7.3,2.1-9.3
						c0.2-0.1,0.4-0.2,0.6-0.2c3.7-1.3,5.4,2.1,6,3.3c1.9-3.9-2.6-9-6.8-8.4c-5,0.8-3.9,5.6-7,6.3c-0.6,0.1-1.1,0.2-1.6,0.2
						c-5.2-0.1-4.3-8-9-11.1C32,25.5,30.8,25.1,29.6,25.1C29.7,25.1,29.6,25.1,29.6,25.1"/>
				</defs>
				<clipPath id="SVGID_00000050657835516339029920000009832891573582132875_">
					<use xlink:href="#SVGID_00000126318339778877784150000016348742010228258946_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000074437184568146406420000014203161765374239122_" cx="-807.227" cy="464.44" r="0.4214" gradientTransform="matrix(64.4433 0 0 64.4433 52066.8711 -29887.5625)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF57F"/>
					<stop  offset="3.515533e-02" style="stop-color:#E5D96A"/>
					<stop  offset="0.1107" style="stop-color:#A49234"/>
					<stop  offset="0.1319" style="stop-color:#917D24"/>
					<stop  offset="0.1648" style="stop-color:#766013"/>
					<stop  offset="0.3022" style="stop-color:#7C600E"/>
					<stop  offset="0.3892" style="stop-color:#7E610C"/>
					<stop  offset="0.4121" style="stop-color:#826209"/>
					<stop  offset="0.4206" style="stop-color:#896B14"/>
					<stop  offset="0.4356" style="stop-color:#9D8232"/>
					<stop  offset="0.4554" style="stop-color:#BCA862"/>
					<stop  offset="0.478" style="stop-color:#E6DAA1"/>
					<stop  offset="0.4784" style="stop-color:#E8DCA4"/>
					<stop  offset="0.4821" style="stop-color:#F5ECBA"/>
					<stop  offset="0.4857" style="stop-color:#FCF6C7"/>
					<stop  offset="0.489" style="stop-color:#FFF9CB"/>
					<stop  offset="0.6703" style="stop-color:#FAF5CC"/>
					<stop  offset="0.6979" style="stop-color:#F9F4CA"/>
					<stop  offset="0.7101" style="stop-color:#F5EFC3"/>
					<stop  offset="0.7194" style="stop-color:#EFE7B6"/>
					<stop  offset="0.7271" style="stop-color:#E6DCA4"/>
					<stop  offset="0.7338" style="stop-color:#DBCD8D"/>
					<stop  offset="0.7399" style="stop-color:#CCBA70"/>
					<stop  offset="0.7455" style="stop-color:#BBA44D"/>
					<stop  offset="0.7506" style="stop-color:#A88C27"/>
					<stop  offset="0.7527" style="stop-color:#9F8014"/>
					<stop  offset="0.8297" style="stop-color:#AA8B0D"/>
					<stop  offset="0.8679" style="stop-color:#B19309"/>
					<stop  offset="0.9121" style="stop-color:#BFA200"/>
					<stop  offset="0.9835" style="stop-color:#F5E17B"/>
					<stop  offset="0.99" style="stop-color:#F7E480"/>
					<stop  offset="0.9979" style="stop-color:#FDED8F"/>
					<stop  offset="1" style="stop-color:#FFF094"/>
				</radialGradient>
				
					<rect x="21.9" y="25.1" style="clip-path:url(#SVGID_00000050657835516339029920000009832891573582132875_);fill:url(#SVGID_00000074437184568146406420000014203161765374239122_);" width="38" height="27.3"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000023998167855931416690000010406801254490514110_" d="M29.7,25.1c-1.1,0-2,0.5-2.7,1
						c-1.8,1.3-2.7,3.7-5,1.5C25,36.9,33.7,27,37.8,40c-3.3-0.9-9.3-0.7-9.6,3.8c0,0.1,0,0.2,0,0.3c-0.2,5.1,4.7,6.1,2.3,8.2
						c8.4-1,0.5-8,7.7-8.2c0.2,0,0.4,0,0.6,0c4.6,0.2,4.3,4.7,0.3,5.1c2,0.7,8.5,0.7,10.2-3.7c1.4-3.5-2.2-7.3,2.1-9.3
						c0.2-0.1,0.4-0.2,0.6-0.2c3.7-1.3,5.4,2,6,3.2c1.8-3.8-2.6-8.9-6.7-8.2c-4.9,0.8-3.8,5.6-7,6.3c-0.6,0.1-1.1,0.2-1.6,0.2
						c-5.2,0-4.3-7.9-9-11C32,25.5,30.8,25.1,29.7,25.1L29.7,25.1"/>
				</defs>
				<clipPath id="SVGID_00000090280555943886317930000003752295010068515225_">
					<use xlink:href="#SVGID_00000023998167855931416690000010406801254490514110_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000038407161661948512190000017588092429759043238_" cx="-807.4313" cy="464.0851" r="0.4214" gradientTransform="matrix(65.6842 0 0 65.6842 53082.043 -30440.2754)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF580"/>
					<stop  offset="3.596561e-02" style="stop-color:#E6D96B"/>
					<stop  offset="0.1132" style="stop-color:#A59236"/>
					<stop  offset="0.1319" style="stop-color:#958028"/>
					<stop  offset="0.1648" style="stop-color:#7B6516"/>
					<stop  offset="0.3022" style="stop-color:#7E6210"/>
					<stop  offset="0.3892" style="stop-color:#80630F"/>
					<stop  offset="0.4121" style="stop-color:#84650C"/>
					<stop  offset="0.421" style="stop-color:#8B6E17"/>
					<stop  offset="0.4368" style="stop-color:#9F8535"/>
					<stop  offset="0.4574" style="stop-color:#BFAC64"/>
					<stop  offset="0.478" style="stop-color:#E4D79B"/>
					<stop  offset="0.4786" style="stop-color:#E6DA9F"/>
					<stop  offset="0.4822" style="stop-color:#F3EAB5"/>
					<stop  offset="0.4858" style="stop-color:#FAF4C2"/>
					<stop  offset="0.489" style="stop-color:#FDF7C6"/>
					<stop  offset="0.6703" style="stop-color:#FAF5CA"/>
					<stop  offset="0.6951" style="stop-color:#F9F4C8"/>
					<stop  offset="0.7075" style="stop-color:#F5EFC0"/>
					<stop  offset="0.7173" style="stop-color:#EFE6B3"/>
					<stop  offset="0.7256" style="stop-color:#E5DAA0"/>
					<stop  offset="0.733" style="stop-color:#D9CA88"/>
					<stop  offset="0.7398" style="stop-color:#CAB769"/>
					<stop  offset="0.7461" style="stop-color:#B9A046"/>
					<stop  offset="0.7518" style="stop-color:#A5861D"/>
					<stop  offset="0.7527" style="stop-color:#A18116"/>
					<stop  offset="0.8297" style="stop-color:#AB8C0D"/>
					<stop  offset="0.8679" style="stop-color:#B29409"/>
					<stop  offset="0.9121" style="stop-color:#C0A300"/>
					<stop  offset="0.9835" style="stop-color:#F5E27A"/>
					<stop  offset="0.9902" style="stop-color:#F7E57F"/>
					<stop  offset="0.9983" style="stop-color:#FDEE8E"/>
					<stop  offset="1" style="stop-color:#FFF092"/>
				</radialGradient>
				
					<rect x="21.9" y="25.1" style="clip-path:url(#SVGID_00000090280555943886317930000003752295010068515225_);fill:url(#SVGID_00000038407161661948512190000017588092429759043238_);" width="37.8" height="27.2"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000013178763756910547330000011603630918902633145_" d="M27,26.2c-1.8,1.3-2.7,3.7-5,1.6
						c2.9,9.1,11.7-0.7,15.8,12.4c-3.3-0.9-9.2-0.7-9.6,3.7c0,0.1,0,0.2,0,0.3c-0.2,5.1,4.6,6.1,2.3,8.1c8.2-1,0.4-8,7.6-8.1
						c0.2,0,0.4,0,0.6,0c4.6,0.2,4.3,4.7,0.4,5.1c2,0.7,8.4,0.6,10-3.7c1.4-3.5-2.2-7.2,2.1-9.3c0.2-0.1,0.4-0.2,0.6-0.3
						c3.6-1.3,5.4,1.9,6.1,3.1c1.7-3.7-2.6-8.7-6.7-8c-4.9,0.8-3.8,5.5-6.9,6.3c-0.6,0.1-1.1,0.2-1.6,0.2c-5.2,0-4.4-7.8-9-10.9
						c-1.5-1-2.8-1.4-3.9-1.4c0,0,0,0-0.1,0C28.6,25.2,27.8,25.6,27,26.2"/>
				</defs>
				<clipPath id="SVGID_00000003812322098881570710000008851266254070851745_">
					<use xlink:href="#SVGID_00000013178763756910547330000011603630918902633145_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000098193748902399779150000007834289724490255002_" cx="-807.6104" cy="463.7667" r="0.421" gradientTransform="matrix(66.83 0 0 66.83 54019.2266 -30950.457)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF580"/>
					<stop  offset="3.712143e-02" style="stop-color:#E6D96B"/>
					<stop  offset="0.1169" style="stop-color:#A69236"/>
					<stop  offset="0.1319" style="stop-color:#99842B"/>
					<stop  offset="0.1648" style="stop-color:#7F6919"/>
					<stop  offset="0.3022" style="stop-color:#806512"/>
					<stop  offset="0.3865" style="stop-color:#826611"/>
					<stop  offset="0.4121" style="stop-color:#87680E"/>
					<stop  offset="0.4213" style="stop-color:#8E7119"/>
					<stop  offset="0.4376" style="stop-color:#A28837"/>
					<stop  offset="0.4589" style="stop-color:#C2AE66"/>
					<stop  offset="0.478" style="stop-color:#E2D597"/>
					<stop  offset="0.4782" style="stop-color:#E3D698"/>
					<stop  offset="0.482" style="stop-color:#F0E6AE"/>
					<stop  offset="0.4856" style="stop-color:#F7F0BB"/>
					<stop  offset="0.489" style="stop-color:#FAF3BF"/>
					<stop  offset="0.6703" style="stop-color:#FBF5C8"/>
					<stop  offset="0.6926" style="stop-color:#FAF3C6"/>
					<stop  offset="0.7052" style="stop-color:#F6EEBD"/>
					<stop  offset="0.7154" style="stop-color:#EFE5B0"/>
					<stop  offset="0.7243" style="stop-color:#E5D99C"/>
					<stop  offset="0.7324" style="stop-color:#D8C882"/>
					<stop  offset="0.7399" style="stop-color:#C9B463"/>
					<stop  offset="0.7467" style="stop-color:#B69D3E"/>
					<stop  offset="0.7527" style="stop-color:#A38417"/>
					<stop  offset="0.8297" style="stop-color:#AC8E0D"/>
					<stop  offset="0.8679" style="stop-color:#B39609"/>
					<stop  offset="0.9121" style="stop-color:#C1A501"/>
					<stop  offset="0.9835" style="stop-color:#F6E37B"/>
					<stop  offset="0.9906" style="stop-color:#F8E680"/>
					<stop  offset="0.9991" style="stop-color:#FEEF8F"/>
					<stop  offset="1" style="stop-color:#FFF091"/>
				</radialGradient>
				
					<rect x="22" y="25.2" style="clip-path:url(#SVGID_00000003812322098881570710000008851266254070851745_);fill:url(#SVGID_00000098193748902399779150000007834289724490255002_);" width="37.6" height="27.1"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000118358245657937443230000001133087457167927960_" d="M27.1,26.2c-1.8,1.3-2.7,3.7-5,1.6
						c2.9,8.9,11.6-0.7,15.7,12.3c-3.2-0.8-9.2-0.7-9.6,3.6c0,0.1,0,0.2,0,0.3c-0.2,5,4.5,6.1,2.4,8.1c7.9-1,0.4-8,7.5-8.1
						c0.2,0,0.4,0,0.6,0c4.6,0.2,4.3,4.6,0.5,5.1c1.9,0.6,8.2,0.5,9.8-3.7c1.4-3.5-2.2-7.2,2.1-9.3c0.2-0.1,0.4-0.2,0.6-0.3
						c3.5-1.3,5.4,1.8,6.1,3c1.5-3.7-2.7-8.5-6.7-7.9c-4.8,0.8-3.7,5.5-6.9,6.4c-0.6,0.1-1.1,0.2-1.6,0.2c-5.2,0-4.4-7.7-9-10.9
						c-1.5-1-2.8-1.5-3.9-1.5h-0.1C28.6,25.3,27.8,25.7,27.1,26.2"/>
				</defs>
				<clipPath id="SVGID_00000029763858315135969500000014211888032050327687_">
					<use xlink:href="#SVGID_00000118358245657937443230000001133087457167927960_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000054247255289566307750000006967192233576519598_" cx="-807.7722" cy="463.4843" r="0.4215" gradientTransform="matrix(67.8836 0 0 67.8836 54881.1484 -31419.5645)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF580"/>
					<stop  offset="3.866435e-02" style="stop-color:#E6D96B"/>
					<stop  offset="0.1218" style="stop-color:#A59235"/>
					<stop  offset="0.1319" style="stop-color:#9D892E"/>
					<stop  offset="0.1648" style="stop-color:#836E1C"/>
					<stop  offset="0.3022" style="stop-color:#826814"/>
					<stop  offset="0.3865" style="stop-color:#846913"/>
					<stop  offset="0.4121" style="stop-color:#896A10"/>
					<stop  offset="0.4217" style="stop-color:#90731B"/>
					<stop  offset="0.4385" style="stop-color:#A48B39"/>
					<stop  offset="0.4605" style="stop-color:#C4B168"/>
					<stop  offset="0.478" style="stop-color:#E1D393"/>
					<stop  offset="0.4818" style="stop-color:#EDE3A8"/>
					<stop  offset="0.4855" style="stop-color:#F5EDB5"/>
					<stop  offset="0.489" style="stop-color:#F8F0B9"/>
					<stop  offset="0.6699" style="stop-color:#FBF5C5"/>
					<stop  offset="0.6703" style="stop-color:#FBF5C5"/>
					<stop  offset="0.6905" style="stop-color:#FAF3C2"/>
					<stop  offset="0.7033" style="stop-color:#F5EEBA"/>
					<stop  offset="0.714" style="stop-color:#EEE4AB"/>
					<stop  offset="0.7235" style="stop-color:#E4D796"/>
					<stop  offset="0.7323" style="stop-color:#D7C67B"/>
					<stop  offset="0.7405" style="stop-color:#C6B05A"/>
					<stop  offset="0.7481" style="stop-color:#B39734"/>
					<stop  offset="0.7527" style="stop-color:#A68619"/>
					<stop  offset="0.8297" style="stop-color:#AD8F0F"/>
					<stop  offset="0.8668" style="stop-color:#B4970C"/>
					<stop  offset="0.9121" style="stop-color:#C3A705"/>
					<stop  offset="0.9835" style="stop-color:#F6E47A"/>
					<stop  offset="0.9906" style="stop-color:#F8E77F"/>
					<stop  offset="0.9991" style="stop-color:#FEF08E"/>
					<stop  offset="1" style="stop-color:#FFF190"/>
				</radialGradient>
				
					<rect x="22.1" y="25.3" style="clip-path:url(#SVGID_00000029763858315135969500000014211888032050327687_);fill:url(#SVGID_00000054247255289566307750000006967192233576519598_);" width="37.3" height="27"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000063631259083140951000000008508175263574961318_" d="M27.2,26.2c-1.8,1.3-2.7,3.7-5,1.7
						c2.8,8.6,11.6-0.8,15.7,12.3c-3.2-0.8-9.2-0.7-9.6,3.6c0,0.1,0,0.2,0,0.3c-0.2,5,4.4,6.1,2.4,8.1c7.7-1,0.3-8,7.5-8.1
						c0.2,0,0.4,0,0.6,0c4.5,0.2,4.4,4.6,0.7,5.2c1.9,0.6,8,0.4,9.6-3.7c1.3-3.5-2.2-7.2,2.1-9.3c0.2-0.1,0.4-0.2,0.6-0.3
						c3.4-1.3,5.4,1.6,6.2,2.9c1.4-3.6-2.7-8.4-6.7-7.7c-4.8,0.8-3.7,5.5-6.9,6.4c-0.6,0.1-1.1,0.2-1.6,0.2c-5.1,0.1-4.5-7.6-9-10.8
						c-1.5-1-2.7-1.5-3.8-1.5c-0.1,0-0.1,0-0.2,0C28.6,25.3,27.9,25.7,27.2,26.2"/>
				</defs>
				<clipPath id="SVGID_00000126302667940665283880000005349730720716418449_">
					<use xlink:href="#SVGID_00000063631259083140951000000008508175263574961318_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000110461343067182503060000013558281460347776156_" cx="-807.9281" cy="463.2428" r="0.4216" gradientTransform="matrix(68.8492 0 0 68.8492 55671.9141 -31850.1719)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF580"/>
					<stop  offset="4.001195e-02" style="stop-color:#E6D96B"/>
					<stop  offset="0.126" style="stop-color:#A79235"/>
					<stop  offset="0.1319" style="stop-color:#A28D31"/>
					<stop  offset="0.1648" style="stop-color:#87721F"/>
					<stop  offset="0.3022" style="stop-color:#856B15"/>
					<stop  offset="0.3865" style="stop-color:#876C14"/>
					<stop  offset="0.4121" style="stop-color:#8C6D12"/>
					<stop  offset="0.4221" style="stop-color:#93761D"/>
					<stop  offset="0.4397" style="stop-color:#A78D3B"/>
					<stop  offset="0.4627" style="stop-color:#C6B46A"/>
					<stop  offset="0.478" style="stop-color:#DED08E"/>
					<stop  offset="0.4817" style="stop-color:#EADFA2"/>
					<stop  offset="0.4855" style="stop-color:#F2E9AF"/>
					<stop  offset="0.489" style="stop-color:#F5ECB3"/>
					<stop  offset="0.6328" style="stop-color:#F9F3BF"/>
					<stop  offset="0.6703" style="stop-color:#FBF5C3"/>
					<stop  offset="0.6887" style="stop-color:#FAF3C0"/>
					<stop  offset="0.7015" style="stop-color:#F5EDB7"/>
					<stop  offset="0.7126" style="stop-color:#EEE3A8"/>
					<stop  offset="0.7227" style="stop-color:#E3D592"/>
					<stop  offset="0.7323" style="stop-color:#D5C375"/>
					<stop  offset="0.7413" style="stop-color:#C4AD53"/>
					<stop  offset="0.7498" style="stop-color:#B0932A"/>
					<stop  offset="0.7527" style="stop-color:#A8891A"/>
					<stop  offset="0.8297" style="stop-color:#AE9010"/>
					<stop  offset="0.8679" style="stop-color:#B5980D"/>
					<stop  offset="0.9121" style="stop-color:#C3A707"/>
					<stop  offset="0.9835" style="stop-color:#F8E579"/>
					<stop  offset="0.9906" style="stop-color:#FAE87E"/>
					<stop  offset="0.9991" style="stop-color:#FEEF8D"/>
					<stop  offset="1" style="stop-color:#FFF08F"/>
				</radialGradient>
				
					<rect x="22.2" y="25.3" style="clip-path:url(#SVGID_00000126302667940665283880000005349730720716418449_);fill:url(#SVGID_00000110461343067182503060000013558281460347776156_);" width="37.1" height="26.8"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000008847324417115338190000008349526692721651638_" d="M27.2,26.2c-1.8,1.2-2.8,3.6-5,1.7
						c2.8,8.4,11.5-0.8,15.7,12.3c-3.2-0.8-9.2-0.7-9.6,3.5c0,0.1,0,0.2,0,0.3c-0.2,5,4.4,6,2.4,8.1c7.5-1.1,0.3-8,7.4-8.1
						c0.2,0,0.4,0,0.6,0c4.5,0.2,4.4,4.6,0.8,5.2c1.9,0.6,7.8,0.3,9.4-3.7c1.3-3.5-2.2-7.1,2.1-9.3c0.2-0.1,0.4-0.2,0.6-0.3
						c3.4-1.3,5.4,1.5,6.2,2.8c1.3-3.5-2.7-8.2-6.7-7.6c-4.7,0.7-3.6,5.5-6.8,6.4c-0.5,0.2-1.1,0.2-1.6,0.2c-5.1,0.1-4.6-7.5-9-10.7
						c-1.4-1-2.7-1.5-3.8-1.6c-0.1,0-0.2,0-0.3,0C28.7,25.4,27.9,25.8,27.2,26.2"/>
				</defs>
				<clipPath id="SVGID_00000177480514825912335730000015165721601934282113_">
					<use xlink:href="#SVGID_00000008847324417115338190000008349526692721651638_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000167367623248715182390000004628914897256268713_" cx="-808.0532" cy="463.0136" r="0.4213" gradientTransform="matrix(69.7304 0 0 69.7304 56392.6445 -32242.1035)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF581"/>
					<stop  offset="4.183748e-02" style="stop-color:#E6D96C"/>
					<stop  offset="0.1319" style="stop-color:#A59235"/>
					<stop  offset="0.1648" style="stop-color:#8C7823"/>
					<stop  offset="0.3022" style="stop-color:#876D18"/>
					<stop  offset="0.3892" style="stop-color:#896E17"/>
					<stop  offset="0.4121" style="stop-color:#8D6F15"/>
					<stop  offset="0.4225" style="stop-color:#957820"/>
					<stop  offset="0.441" style="stop-color:#A9903E"/>
					<stop  offset="0.465" style="stop-color:#C9B76D"/>
					<stop  offset="0.478" style="stop-color:#DDCF8A"/>
					<stop  offset="0.4813" style="stop-color:#E7DC9B"/>
					<stop  offset="0.4853" style="stop-color:#EFE6A8"/>
					<stop  offset="0.489" style="stop-color:#F2E9AC"/>
					<stop  offset="0.6048" style="stop-color:#F8F0B8"/>
					<stop  offset="0.6703" style="stop-color:#FCF6C1"/>
					<stop  offset="0.6871" style="stop-color:#FAF4BE"/>
					<stop  offset="0.7" style="stop-color:#F6EEB4"/>
					<stop  offset="0.7116" style="stop-color:#EDE3A4"/>
					<stop  offset="0.7224" style="stop-color:#E2D48D"/>
					<stop  offset="0.7327" style="stop-color:#D3C16F"/>
					<stop  offset="0.7427" style="stop-color:#C1A94A"/>
					<stop  offset="0.7521" style="stop-color:#AC8E1F"/>
					<stop  offset="0.7527" style="stop-color:#AA8C1C"/>
					<stop  offset="0.8204" style="stop-color:#AE9111"/>
					<stop  offset="0.8297" style="stop-color:#AF920F"/>
					<stop  offset="0.8679" style="stop-color:#B69A0E"/>
					<stop  offset="0.9121" style="stop-color:#C5A90C"/>
					<stop  offset="0.9835" style="stop-color:#F8E679"/>
					<stop  offset="0.991" style="stop-color:#FAE97E"/>
					<stop  offset="1" style="stop-color:#FFF18D"/>
					<stop  offset="1" style="stop-color:#FFF18D"/>
				</radialGradient>
				
					<rect x="22.2" y="25.4" style="clip-path:url(#SVGID_00000177480514825912335730000015165721601934282113_);fill:url(#SVGID_00000167367623248715182390000004628914897256268713_);" width="36.8" height="26.7"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000050621825150664806390000003381448596421703071_" d="M27.3,26.2c-1.8,1.2-2.8,3.6-5,1.8
						c2.7,8.2,11.4-0.8,15.6,12.2c-3.2-0.7-9.2-0.7-9.6,3.5c0,0.1,0,0.2,0,0.3c-0.2,4.9,4.3,6,2.4,8c7.2-1.1,0.3-8,7.4-8.1
						c0.2,0,0.4,0,0.6,0c4.5,0.2,4.4,4.6,1,5.2c1.9,0.5,7.7,0.2,9.2-3.8c1.3-3.5-2.2-7.1,2.1-9.3c0.2-0.1,0.4-0.2,0.6-0.3
						c3.3-1.3,5.4,1.4,6.2,2.7c1.2-3.4-2.7-8-6.7-7.4c-4.7,0.7-3.6,5.4-6.8,6.4c-0.5,0.2-1,0.2-1.5,0.2c-5.1,0.2-4.6-7.4-9-10.6
						c-1.4-1-2.6-1.5-3.7-1.6c-0.1,0-0.2,0-0.4,0C28.7,25.4,28,25.8,27.3,26.2"/>
				</defs>
				<clipPath id="SVGID_00000154423732006425462590000014573484866694058159_">
					<use xlink:href="#SVGID_00000050621825150664806390000003381448596421703071_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000011713548129778231580000000302105403679207053_" cx="-808.1666" cy="462.8141" r="0.4216" gradientTransform="matrix(70.5297 0 0 70.5297 57046.582 -32597.7969)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF581"/>
					<stop  offset="4.341850e-02" style="stop-color:#E6D96C"/>
					<stop  offset="0.1319" style="stop-color:#AA9638"/>
					<stop  offset="0.1648" style="stop-color:#917C26"/>
					<stop  offset="0.2935" style="stop-color:#8B711B"/>
					<stop  offset="0.3022" style="stop-color:#8A701A"/>
					<stop  offset="0.3892" style="stop-color:#8C7119"/>
					<stop  offset="0.4121" style="stop-color:#907216"/>
					<stop  offset="0.423" style="stop-color:#977B21"/>
					<stop  offset="0.4422" style="stop-color:#AB933F"/>
					<stop  offset="0.4672" style="stop-color:#CCBA6E"/>
					<stop  offset="0.478" style="stop-color:#DBCC85"/>
					<stop  offset="0.4812" style="stop-color:#E5D995"/>
					<stop  offset="0.4853" style="stop-color:#EDE3A2"/>
					<stop  offset="0.489" style="stop-color:#F0E7A6"/>
					<stop  offset="0.5931" style="stop-color:#F6EEB2"/>
					<stop  offset="0.6703" style="stop-color:#FCF6BE"/>
					<stop  offset="0.6858" style="stop-color:#FAF4BB"/>
					<stop  offset="0.6988" style="stop-color:#F5EDB0"/>
					<stop  offset="0.711" style="stop-color:#ECE29F"/>
					<stop  offset="0.7226" style="stop-color:#E0D286"/>
					<stop  offset="0.7338" style="stop-color:#D0BE66"/>
					<stop  offset="0.7447" style="stop-color:#BDA540"/>
					<stop  offset="0.7527" style="stop-color:#AC8F1E"/>
					<stop  offset="0.8204" style="stop-color:#AF9213"/>
					<stop  offset="0.8297" style="stop-color:#AF9311"/>
					<stop  offset="0.8668" style="stop-color:#B69B11"/>
					<stop  offset="0.9121" style="stop-color:#C6AB11"/>
					<stop  offset="0.9835" style="stop-color:#F9E779"/>
					<stop  offset="0.9914" style="stop-color:#FBEA7E"/>
					<stop  offset="1" style="stop-color:#FFF18B"/>
				</radialGradient>
				
					<rect x="22.3" y="25.4" style="clip-path:url(#SVGID_00000154423732006425462590000014573484866694058159_);fill:url(#SVGID_00000011713548129778231580000000302105403679207053_);" width="36.6" height="26.6"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000152977528640250729750000000005281279983596213_" d="M27.4,26.3c-1.8,1.2-2.8,3.6-5,1.8
						c2.7,8,11.4-0.9,15.6,12.2c-3.1-0.7-9.1-0.7-9.6,3.4c0,0.1,0,0.2,0,0.3c-0.2,4.9,4.2,6,2.4,8c7-1.1,0.2-8,7.3-8
						c0.2,0,0.4,0,0.6,0c4.5,0.2,4.4,4.6,1.1,5.2c1.9,0.5,7.5,0.2,9-3.8c1.3-3.5-2.2-7,2.1-9.2c0.2-0.1,0.4-0.2,0.6-0.3
						c3.2-1.2,5.4,1.3,6.3,2.6c1.1-3.4-2.7-7.9-6.7-7.3c-4.6,0.7-3.5,5.4-6.7,6.4c-0.5,0.2-1,0.2-1.5,0.3c-5.1,0.2-4.7-7.3-9-10.6
						c-1.4-1-2.6-1.5-3.7-1.7c-0.1,0-0.3,0-0.4,0C28.7,25.5,28,25.8,27.4,26.3"/>
				</defs>
				<clipPath id="SVGID_00000130633292927899630220000011444739591690396330_">
					<use xlink:href="#SVGID_00000152977528640250729750000000005281279983596213_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000120518035913336324330000003431473201251381426_" cx="-808.2687" cy="462.6411" r="0.4218" gradientTransform="matrix(71.2517 0 0 71.2517 57637.4297 -32919.2891)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF582"/>
					<stop  offset="4.514301e-02" style="stop-color:#E6D96C"/>
					<stop  offset="0.1319" style="stop-color:#AE9A3B"/>
					<stop  offset="0.1648" style="stop-color:#958029"/>
					<stop  offset="0.2789" style="stop-color:#8E761E"/>
					<stop  offset="0.3022" style="stop-color:#8C731B"/>
					<stop  offset="0.3892" style="stop-color:#8E741A"/>
					<stop  offset="0.4121" style="stop-color:#927519"/>
					<stop  offset="0.4236" style="stop-color:#9A7E24"/>
					<stop  offset="0.444" style="stop-color:#AE9742"/>
					<stop  offset="0.4706" style="stop-color:#CFBE71"/>
					<stop  offset="0.478" style="stop-color:#D9CA80"/>
					<stop  offset="0.4812" style="stop-color:#E2D690"/>
					<stop  offset="0.4853" style="stop-color:#EAE09D"/>
					<stop  offset="0.489" style="stop-color:#EDE3A1"/>
					<stop  offset="0.5838" style="stop-color:#F4EBAD"/>
					<stop  offset="0.6703" style="stop-color:#FDF6BC"/>
					<stop  offset="0.6846" style="stop-color:#FBF4B8"/>
					<stop  offset="0.6978" style="stop-color:#F5ECAD"/>
					<stop  offset="0.7105" style="stop-color:#ECE09A"/>
					<stop  offset="0.723" style="stop-color:#DFD080"/>
					<stop  offset="0.7353" style="stop-color:#CDBA5E"/>
					<stop  offset="0.7473" style="stop-color:#B9A035"/>
					<stop  offset="0.7527" style="stop-color:#AE9220"/>
					<stop  offset="0.8166" style="stop-color:#B09415"/>
					<stop  offset="0.8297" style="stop-color:#B19412"/>
					<stop  offset="0.8668" style="stop-color:#B89C13"/>
					<stop  offset="0.9121" style="stop-color:#C6AC15"/>
					<stop  offset="0.9835" style="stop-color:#F9E778"/>
					<stop  offset="0.9914" style="stop-color:#FBEA7D"/>
					<stop  offset="1" style="stop-color:#FFF18A"/>
				</radialGradient>
				
					<rect x="22.4" y="25.5" style="clip-path:url(#SVGID_00000130633292927899630220000011444739591690396330_);fill:url(#SVGID_00000120518035913336324330000003431473201251381426_);" width="36.4" height="26.5"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000041978551507819230940000000975152246586607291_" d="M27.4,26.3c-1.8,1.2-2.8,3.6-5,1.8
						c2.7,7.8,11.3-0.9,15.5,12.2c-3.1-0.6-9.1-0.7-9.6,3.4c0,0.1,0,0.2,0,0.3c-0.2,4.9,4.1,6,2.4,8c6.7-1.1,0.2-8,7.2-8
						c0.2,0,0.4,0,0.6,0c4.4,0.2,4.4,4.6,1.2,5.2c1.9,0.4,7.3,0.1,8.8-3.8c1.3-3.5-2.2-7,2.1-9.2c0.2-0.1,0.4-0.2,0.6-0.3
						c3.2-1.2,5.4,1.2,6.3,2.5c1-3.3-2.7-7.7-6.6-7.1c-4.6,0.7-3.5,5.4-6.7,6.4c-0.5,0.2-1,0.2-1.5,0.3c-5.1,0.2-4.7-7.2-9-10.5
						c-1.3-1-2.5-1.6-3.7-1.7c-0.2,0-0.3,0-0.5,0C28.8,25.6,28.1,25.9,27.4,26.3"/>
				</defs>
				<clipPath id="SVGID_00000134936125286973866440000010120371162827363756_">
					<use xlink:href="#SVGID_00000041978551507819230940000000975152246586607291_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000172442783166372772830000010281000888911537066_" cx="-808.3555" cy="462.4825" r="0.4212" gradientTransform="matrix(71.902 0 0 71.902 58169.3008 -33208.4609)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF582"/>
					<stop  offset="4.750754e-02" style="stop-color:#E6D96C"/>
					<stop  offset="0.1319" style="stop-color:#B39F3F"/>
					<stop  offset="0.1648" style="stop-color:#9A852D"/>
					<stop  offset="0.2676" style="stop-color:#927B22"/>
					<stop  offset="0.3022" style="stop-color:#8F761D"/>
					<stop  offset="0.3892" style="stop-color:#91761C"/>
					<stop  offset="0.4121" style="stop-color:#95771B"/>
					<stop  offset="0.4242" style="stop-color:#9D8026"/>
					<stop  offset="0.4455" style="stop-color:#B19944"/>
					<stop  offset="0.4734" style="stop-color:#D1C173"/>
					<stop  offset="0.478" style="stop-color:#D7C87C"/>
					<stop  offset="0.4809" style="stop-color:#E0D28A"/>
					<stop  offset="0.4851" style="stop-color:#E8DD97"/>
					<stop  offset="0.489" style="stop-color:#EBE09B"/>
					<stop  offset="0.5763" style="stop-color:#F2E8A7"/>
					<stop  offset="0.6703" style="stop-color:#FDF5B9"/>
					<stop  offset="0.6837" style="stop-color:#FBF2B5"/>
					<stop  offset="0.6972" style="stop-color:#F5EBA9"/>
					<stop  offset="0.7108" style="stop-color:#EBDE95"/>
					<stop  offset="0.7243" style="stop-color:#DCCB78"/>
					<stop  offset="0.7379" style="stop-color:#CAB454"/>
					<stop  offset="0.7513" style="stop-color:#B49728"/>
					<stop  offset="0.7527" style="stop-color:#B19423"/>
					<stop  offset="0.8052" style="stop-color:#B19518"/>
					<stop  offset="0.8297" style="stop-color:#B19511"/>
					<stop  offset="0.8657" style="stop-color:#B89D13"/>
					<stop  offset="0.9121" style="stop-color:#C8AE17"/>
					<stop  offset="0.9835" style="stop-color:#FAE878"/>
					<stop  offset="0.992" style="stop-color:#FCEB7D"/>
					<stop  offset="1" style="stop-color:#FFF188"/>
				</radialGradient>
				
					<rect x="22.5" y="25.6" style="clip-path:url(#SVGID_00000134936125286973866440000010120371162827363756_);fill:url(#SVGID_00000172442783166372772830000010281000888911537066_);" width="36.1" height="26.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000078730703670970285260000007843286881669765555_" d="M27.5,26.3c-1.8,1.2-2.8,3.6-4.9,1.9
						c2.6,7.6,11.3-1,15.5,12.1c-3.1-0.6-9.1-0.7-9.5,3.3c0,0.1,0,0.2,0,0.3c-0.3,4.9,4,5.9,2.5,7.9c6.5-1.1,0.1-8,7.2-8
						c0.2,0,0.4,0,0.6,0c4.4,0.2,4.5,4.6,1.4,5.2c1.9,0.4,7.2,0,8.6-3.8c1.3-3.5-2.2-6.9,2-9.2c0.2-0.1,0.4-0.2,0.6-0.3
						c3.1-1.2,5.4,1.1,6.3,2.4c0.8-3.2-2.7-7.5-6.6-6.9c-4.5,0.7-3.4,5.4-6.6,6.4c-0.5,0.2-1,0.2-1.5,0.3c-5.1,0.3-4.8-7.1-9-10.4
						c-1.3-1-2.5-1.6-3.6-1.8c-0.2,0-0.4,0-0.6,0C28.8,25.6,28.1,25.9,27.5,26.3"/>
				</defs>
				<clipPath id="SVGID_00000148651242760424440020000013179131612724477316_">
					<use xlink:href="#SVGID_00000078730703670970285260000007843286881669765555_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000159460405570371153510000003731679263666891441_" cx="-808.436" cy="462.3469" r="0.4218" gradientTransform="matrix(72.4834 0 0 72.4834 58645.1562 -33467.2461)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF583"/>
					<stop  offset="5.015760e-02" style="stop-color:#E6D96D"/>
					<stop  offset="0.1319" style="stop-color:#B7A443"/>
					<stop  offset="0.1648" style="stop-color:#9F8A31"/>
					<stop  offset="0.2586" style="stop-color:#977F27"/>
					<stop  offset="0.3022" style="stop-color:#927820"/>
					<stop  offset="0.3924" style="stop-color:#94791F"/>
					<stop  offset="0.4121" style="stop-color:#977A1D"/>
					<stop  offset="0.4249" style="stop-color:#9F8328"/>
					<stop  offset="0.4475" style="stop-color:#B39C46"/>
					<stop  offset="0.4771" style="stop-color:#D4C475"/>
					<stop  offset="0.478" style="stop-color:#D5C577"/>
					<stop  offset="0.4806" style="stop-color:#DDCE83"/>
					<stop  offset="0.485" style="stop-color:#E5D890"/>
					<stop  offset="0.489" style="stop-color:#E8DC94"/>
					<stop  offset="0.5662" style="stop-color:#F0E5A0"/>
					<stop  offset="0.6703" style="stop-color:#FFF6B7"/>
					<stop  offset="0.683" style="stop-color:#FDF3B3"/>
					<stop  offset="0.6968" style="stop-color:#F6EBA5"/>
					<stop  offset="0.7111" style="stop-color:#EADD90"/>
					<stop  offset="0.7259" style="stop-color:#DBC971"/>
					<stop  offset="0.7407" style="stop-color:#C6B04A"/>
					<stop  offset="0.7527" style="stop-color:#B39825"/>
					<stop  offset="0.8052" style="stop-color:#B2971A"/>
					<stop  offset="0.8297" style="stop-color:#B29713"/>
					<stop  offset="0.8657" style="stop-color:#B99F15"/>
					<stop  offset="0.9121" style="stop-color:#C9B01A"/>
					<stop  offset="0.9835" style="stop-color:#FBE977"/>
					<stop  offset="0.992" style="stop-color:#FCEC7C"/>
					<stop  offset="1" style="stop-color:#FFF287"/>
				</radialGradient>
				
					<rect x="22.5" y="25.6" style="clip-path:url(#SVGID_00000148651242760424440020000013179131612724477316_);fill:url(#SVGID_00000159460405570371153510000003731679263666891441_);" width="35.9" height="26.3"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000023249028238061372740000013680053146339013046_" d="M27.6,26.3c-1.8,1.1-2.8,3.6-4.9,1.9
						c2.6,7.3,11.2-1,15.4,12.1c-3-0.6-9.1-0.7-9.5,3.3c0,0.1,0,0.2,0,0.3c-0.3,4.8,3.9,5.9,2.5,7.9c6.3-1.2,0.1-7.9,7.1-8
						c0.2,0,0.4,0,0.6,0c4.4,0.2,4.5,4.6,1.5,5.2c1.8,0.4,7-0.1,8.4-3.8c1.3-3.5-2.2-6.9,2-9.2c0.2-0.1,0.4-0.2,0.6-0.3
						c3-1.2,5.4,1,6.4,2.3c0.7-3.1-2.8-7.4-6.6-6.8c-4.5,0.7-3.4,5.3-6.6,6.4c-0.5,0.2-1,0.3-1.5,0.3c-5.1,0.3-4.9-7-9-10.4
						c-1.3-1-2.4-1.6-3.6-1.8c-0.2,0-0.4-0.1-0.6-0.1C28.8,25.7,28.2,25.9,27.6,26.3"/>
				</defs>
				<clipPath id="SVGID_00000178890482347986012550000000884176605255336354_">
					<use xlink:href="#SVGID_00000023249028238061372740000013680053146339013046_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000079446322711586095450000015204353418114122115_" cx="-808.4974" cy="462.2174" r="0.4216" gradientTransform="matrix(73.0014 0 0 73.0014 59068.4648 -33696.9844)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF583"/>
					<stop  offset="5.313212e-02" style="stop-color:#E6D96D"/>
					<stop  offset="0.1319" style="stop-color:#BBA947"/>
					<stop  offset="0.1648" style="stop-color:#A48F34"/>
					<stop  offset="0.2512" style="stop-color:#9B842A"/>
					<stop  offset="0.3022" style="stop-color:#947B21"/>
					<stop  offset="0.3924" style="stop-color:#967C20"/>
					<stop  offset="0.4121" style="stop-color:#997D1F"/>
					<stop  offset="0.4256" style="stop-color:#A1862A"/>
					<stop  offset="0.4495" style="stop-color:#B59F48"/>
					<stop  offset="0.478" style="stop-color:#D3C373"/>
					<stop  offset="0.4803" style="stop-color:#D9CB7D"/>
					<stop  offset="0.4848" style="stop-color:#E2D58A"/>
					<stop  offset="0.489" style="stop-color:#E5D98E"/>
					<stop  offset="0.5613" style="stop-color:#EDE29A"/>
					<stop  offset="0.6703" style="stop-color:#FFF6B4"/>
					<stop  offset="0.6824" style="stop-color:#FCF3AF"/>
					<stop  offset="0.6967" style="stop-color:#F5E9A1"/>
					<stop  offset="0.7121" style="stop-color:#E8DA89"/>
					<stop  offset="0.7284" style="stop-color:#D7C467"/>
					<stop  offset="0.7451" style="stop-color:#C1A83D"/>
					<stop  offset="0.7527" style="stop-color:#B59A27"/>
					<stop  offset="0.8031" style="stop-color:#B4991C"/>
					<stop  offset="0.8297" style="stop-color:#B39814"/>
					<stop  offset="0.8657" style="stop-color:#BAA017"/>
					<stop  offset="0.9121" style="stop-color:#CAB11D"/>
					<stop  offset="0.9835" style="stop-color:#FCEA76"/>
					<stop  offset="0.9923" style="stop-color:#FDED7B"/>
					<stop  offset="1" style="stop-color:#FFF285"/>
				</radialGradient>
				
					<rect x="22.6" y="25.7" style="clip-path:url(#SVGID_00000178890482347986012550000000884176605255336354_);fill:url(#SVGID_00000079446322711586095450000015204353418114122115_);" width="35.6" height="26.2"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000008859270662009149810000014190282587959933070_" d="M27.6,26.3c-1.8,1.1-2.9,3.5-4.9,2
						c2.5,7.1,11.1-1.1,15.4,12c-3-0.5-9.1-0.7-9.5,3.2c0,0.1,0,0.2,0,0.3c-0.3,4.8,3.8,5.9,2.5,7.9c6-1.2,0-7.9,7.1-8
						c0.2,0,0.4,0,0.6,0c4.3,0.2,4.5,4.5,1.6,5.3c1.8,0.3,6.8-0.2,8.2-3.9c1.3-3.5-2.2-6.8,2-9.2c0.2-0.1,0.4-0.2,0.6-0.3
						c3-1.2,5.4,0.9,6.4,2.2c0.6-3.1-2.8-7.2-6.6-6.6c-4.4,0.6-3.3,5.3-6.6,6.4c-0.5,0.2-1,0.3-1.5,0.3c-5.1,0.3-4.9-6.9-9-10.3
						c-1.2-1-2.4-1.6-3.5-1.9c-0.2,0-0.5-0.1-0.7-0.1C28.9,25.7,28.2,26,27.6,26.3"/>
				</defs>
				<clipPath id="SVGID_00000116918178757455109280000012617196098202031284_">
					<use xlink:href="#SVGID_00000008859270662009149810000014190282587959933070_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000158733985030243092950000000292030342797846973_" cx="-808.5604" cy="462.1129" r="0.4213" gradientTransform="matrix(73.461 0 0 73.461 59444.7344 -33901.4883)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF584"/>
					<stop  offset="5.650536e-02" style="stop-color:#E6D96E"/>
					<stop  offset="0.1319" style="stop-color:#C0AE4B"/>
					<stop  offset="0.1648" style="stop-color:#A99538"/>
					<stop  offset="0.2422" style="stop-color:#A08A2E"/>
					<stop  offset="0.3022" style="stop-color:#967E23"/>
					<stop  offset="0.3892" style="stop-color:#987F22"/>
					<stop  offset="0.4121" style="stop-color:#9C8021"/>
					<stop  offset="0.4264" style="stop-color:#A4892C"/>
					<stop  offset="0.4517" style="stop-color:#B8A24A"/>
					<stop  offset="0.478" style="stop-color:#D1C26F"/>
					<stop  offset="0.4799" style="stop-color:#D6C877"/>
					<stop  offset="0.4847" style="stop-color:#DFD284"/>
					<stop  offset="0.489" style="stop-color:#E2D688"/>
					<stop  offset="0.5571" style="stop-color:#EADF94"/>
					<stop  offset="0.6703" style="stop-color:#FFF6B1"/>
					<stop  offset="0.682" style="stop-color:#FCF2AB"/>
					<stop  offset="0.697" style="stop-color:#F4E89B"/>
					<stop  offset="0.7138" style="stop-color:#E6D781"/>
					<stop  offset="0.732" style="stop-color:#D2BF5C"/>
					<stop  offset="0.7509" style="stop-color:#BAA02E"/>
					<stop  offset="0.7527" style="stop-color:#B79D29"/>
					<stop  offset="0.7993" style="stop-color:#B59B1E"/>
					<stop  offset="0.8297" style="stop-color:#B49914"/>
					<stop  offset="0.8647" style="stop-color:#BBA118"/>
					<stop  offset="0.9121" style="stop-color:#CBB320"/>
					<stop  offset="0.9835" style="stop-color:#FCEB77"/>
					<stop  offset="0.9927" style="stop-color:#FDEE7C"/>
					<stop  offset="1" style="stop-color:#FFF285"/>
				</radialGradient>
				
					<rect x="22.7" y="25.7" style="clip-path:url(#SVGID_00000116918178757455109280000012617196098202031284_);fill:url(#SVGID_00000158733985030243092950000000292030342797846973_);" width="35.4" height="26.1"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000057125316682184540770000015742601007223184553_" d="M27.7,26.4c-1.8,1.1-2.9,3.5-4.9,2
						c2.5,6.9,11.1-1.1,15.3,12c-3-0.5-9-0.7-9.5,3.2c0,0.1,0,0.2,0,0.3c-0.3,4.8,3.7,5.9,2.5,7.9c5.8-1.2,0-7.9,7-8
						c0.2,0,0.4,0,0.6,0c4.3,0.2,4.5,4.5,1.8,5.3c1.8,0.3,6.6-0.3,8-3.9c1.3-3.4-2.2-6.8,2-9.2c0.2-0.1,0.4-0.2,0.6-0.3
						c2.9-1.2,5.4,0.8,6.5,2.1c0.5-3-2.8-7-6.6-6.5c-4.4,0.6-3.3,5.3-6.5,6.4C44,37.9,43.5,38,43,38c-5.1,0.4-5-6.8-9-10.2
						c-1.2-1-2.3-1.7-3.5-1.9c-0.3-0.1-0.5-0.1-0.7-0.1C28.9,25.8,28.3,26,27.7,26.4"/>
				</defs>
				<clipPath id="SVGID_00000001647774556512606640000005251385003414039189_">
					<use xlink:href="#SVGID_00000057125316682184540770000015742601007223184553_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000051341155177995587710000005112393495541834373_" cx="-808.615" cy="462.021" r="0.4213" gradientTransform="matrix(73.8671 0 0 73.8671 59777.1797 -34082.0586)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF584"/>
					<stop  offset="5.955044e-02" style="stop-color:#E6D96E"/>
					<stop  offset="0.1319" style="stop-color:#C4B24F"/>
					<stop  offset="0.1648" style="stop-color:#AE9A3C"/>
					<stop  offset="0.2352" style="stop-color:#A58F32"/>
					<stop  offset="0.3022" style="stop-color:#998025"/>
					<stop  offset="0.3966" style="stop-color:#9B8024"/>
					<stop  offset="0.4121" style="stop-color:#9D8123"/>
					<stop  offset="0.4275" style="stop-color:#A58B2E"/>
					<stop  offset="0.4546" style="stop-color:#BAA44B"/>
					<stop  offset="0.478" style="stop-color:#CFBF6A"/>
					<stop  offset="0.4797" style="stop-color:#D4C471"/>
					<stop  offset="0.4846" style="stop-color:#DDCF7E"/>
					<stop  offset="0.489" style="stop-color:#E0D382"/>
					<stop  offset="0.5522" style="stop-color:#E8DC8E"/>
					<stop  offset="0.6662" style="stop-color:#FEF5AE"/>
					<stop  offset="0.6703" style="stop-color:#FFF6AF"/>
					<stop  offset="0.6818" style="stop-color:#FCF2A9"/>
					<stop  offset="0.6977" style="stop-color:#F2E697"/>
					<stop  offset="0.7162" style="stop-color:#E3D379"/>
					<stop  offset="0.7365" style="stop-color:#CEB951"/>
					<stop  offset="0.7527" style="stop-color:#BAA02B"/>
					<stop  offset="0.7976" style="stop-color:#B79D20"/>
					<stop  offset="0.8297" style="stop-color:#B59A15"/>
					<stop  offset="0.8638" style="stop-color:#BCA219"/>
					<stop  offset="0.9121" style="stop-color:#CDB522"/>
					<stop  offset="0.9835" style="stop-color:#FCEB76"/>
					<stop  offset="0.9931" style="stop-color:#FDEE7B"/>
					<stop  offset="1" style="stop-color:#FFF283"/>
				</radialGradient>
				
					<rect x="22.8" y="25.8" style="clip-path:url(#SVGID_00000001647774556512606640000005251385003414039189_);fill:url(#SVGID_00000051341155177995587710000005112393495541834373_);" width="35.2" height="25.9"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000116209128996608182980000014910867262522115225_" d="M27.8,26.4c-1.8,1.1-2.9,3.5-4.9,2.1
						c2.4,6.7,11-1.2,15.3,12c-3-0.5-9-0.7-9.5,3.1c0,0.1,0,0.2,0,0.3c-0.3,4.7,3.6,5.8,2.5,7.8c5.6-1.2-0.1-7.9,6.9-7.9
						c0.2,0,0.4,0,0.6,0c4.3,0.2,4.5,4.5,1.9,5.3c1.8,0.3,6.5-0.3,7.8-3.9c1.3-3.4-2.2-6.7,2-9.2c0.2-0.1,0.4-0.2,0.6-0.3
						c2.8-1.1,5.4,0.7,6.5,2c0.4-2.9-2.8-6.9-6.6-6.3c-4.3,0.6-3.2,5.2-6.5,6.4c-0.5,0.2-1,0.3-1.5,0.3c-5.1,0.4-5-6.7-9-10.1
						c-1.2-1-2.3-1.7-3.4-2c-0.3-0.1-0.5-0.1-0.8-0.1C28.9,25.8,28.3,26.1,27.8,26.4"/>
				</defs>
				<clipPath id="SVGID_00000096754292201489637110000003179467036549603258_">
					<use xlink:href="#SVGID_00000116209128996608182980000014910867262522115225_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000029031171525377647640000011301678964541340585_" cx="-808.6583" cy="461.9366" r="0.4221" gradientTransform="matrix(74.2273 0 0 74.2273 60071.7188 -34241.9219)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF586"/>
					<stop  offset="6.389005e-02" style="stop-color:#E7D96F"/>
					<stop  offset="0.1319" style="stop-color:#C9B753"/>
					<stop  offset="0.1648" style="stop-color:#B39F40"/>
					<stop  offset="0.2313" style="stop-color:#AA9436"/>
					<stop  offset="0.3022" style="stop-color:#9C8327"/>
					<stop  offset="0.3966" style="stop-color:#9E8326"/>
					<stop  offset="0.4121" style="stop-color:#A08425"/>
					<stop  offset="0.4286" style="stop-color:#A88E30"/>
					<stop  offset="0.4576" style="stop-color:#BDA84D"/>
					<stop  offset="0.478" style="stop-color:#CEBD66"/>
					<stop  offset="0.4795" style="stop-color:#D2C26C"/>
					<stop  offset="0.4845" style="stop-color:#DBCC79"/>
					<stop  offset="0.489" style="stop-color:#DED07D"/>
					<stop  offset="0.5501" style="stop-color:#E6DA89"/>
					<stop  offset="0.6602" style="stop-color:#FDF3A9"/>
					<stop  offset="0.6703" style="stop-color:#FFF6AC"/>
					<stop  offset="0.6819" style="stop-color:#FBF1A5"/>
					<stop  offset="0.6992" style="stop-color:#F0E490"/>
					<stop  offset="0.7201" style="stop-color:#DFCE6F"/>
					<stop  offset="0.7435" style="stop-color:#C7B041"/>
					<stop  offset="0.7527" style="stop-color:#BCA32D"/>
					<stop  offset="0.7947" style="stop-color:#B99F22"/>
					<stop  offset="0.8297" style="stop-color:#B69B15"/>
					<stop  offset="0.8647" style="stop-color:#BDA319"/>
					<stop  offset="0.9121" style="stop-color:#CDB524"/>
					<stop  offset="0.9835" style="stop-color:#FDEC75"/>
					<stop  offset="0.9935" style="stop-color:#FEEF7A"/>
					<stop  offset="1" style="stop-color:#FFF381"/>
				</radialGradient>
				
					<rect x="22.9" y="25.8" style="clip-path:url(#SVGID_00000096754292201489637110000003179467036549603258_);fill:url(#SVGID_00000029031171525377647640000011301678964541340585_);" width="34.9" height="25.8"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000005972798348035766950000013908147105865246876_" d="M27.8,26.4c-1.8,1.1-2.9,3.5-4.9,2.1
						c2.4,6.5,11-1.2,15.3,11.9c-2.9-0.4-9-0.7-9.5,3.1c0,0.1,0,0.2,0,0.3c-0.3,4.7,3.5,5.8,2.5,7.8c5.3-1.2-0.1-7.9,6.9-7.9
						c0.2,0,0.4,0,0.5,0c4.3,0.2,4.5,4.5,2.1,5.3c1.8,0.2,6.3-0.4,7.6-3.9c1.3-3.4-2.2-6.7,2-9.1c0.2-0.1,0.4-0.2,0.6-0.3
						c2.8-1.1,5.4,0.6,6.5,1.9c0.2-2.8-2.8-6.7-6.6-6.2c-4.3,0.6-3.2,5.2-6.4,6.4c-0.5,0.2-1,0.3-1.5,0.3c-5,0.4-5.1-6.5-9-10.1
						c-1.1-1-2.3-1.7-3.4-2c-0.3-0.1-0.6-0.1-0.9-0.1C29,25.9,28.4,26.1,27.8,26.4"/>
				</defs>
				<clipPath id="SVGID_00000064321148760356673430000009539132756443242155_">
					<use xlink:href="#SVGID_00000005972798348035766950000013908147105865246876_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000106846526376115483140000011468138398815058869_" cx="-808.7044" cy="461.8665" r="0.4212" gradientTransform="matrix(74.5453 0 0 74.5453 60332.3984 -34383.3203)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF586"/>
					<stop  offset="6.868859e-02" style="stop-color:#E7D96F"/>
					<stop  offset="0.1319" style="stop-color:#CEBC57"/>
					<stop  offset="0.1648" style="stop-color:#B8A544"/>
					<stop  offset="0.2247" style="stop-color:#AF9A3B"/>
					<stop  offset="0.3022" style="stop-color:#9F8529"/>
					<stop  offset="0.4022" style="stop-color:#A18628"/>
					<stop  offset="0.4121" style="stop-color:#A28727"/>
					<stop  offset="0.4302" style="stop-color:#AA9132"/>
					<stop  offset="0.4619" style="stop-color:#BFAB4F"/>
					<stop  offset="0.478" style="stop-color:#CCBB61"/>
					<stop  offset="0.4793" style="stop-color:#CFBF66"/>
					<stop  offset="0.4844" style="stop-color:#D8C973"/>
					<stop  offset="0.489" style="stop-color:#DBCD77"/>
					<stop  offset="0.5462" style="stop-color:#E3D783"/>
					<stop  offset="0.6494" style="stop-color:#FAF0A3"/>
					<stop  offset="0.6703" style="stop-color:#FFF6AA"/>
					<stop  offset="0.6822" style="stop-color:#FAF0A1"/>
					<stop  offset="0.7014" style="stop-color:#EEE18A"/>
					<stop  offset="0.7255" style="stop-color:#DAC863"/>
					<stop  offset="0.7527" style="stop-color:#BFA62F"/>
					<stop  offset="0.7934" style="stop-color:#BBA224"/>
					<stop  offset="0.8297" style="stop-color:#B69D16"/>
					<stop  offset="0.8647" style="stop-color:#BDA51B"/>
					<stop  offset="0.9121" style="stop-color:#CEB727"/>
					<stop  offset="0.9835" style="stop-color:#FDED75"/>
					<stop  offset="0.9941" style="stop-color:#FEF07A"/>
					<stop  offset="1" style="stop-color:#FFF380"/>
				</radialGradient>
				
					<rect x="22.9" y="25.9" style="clip-path:url(#SVGID_00000064321148760356673430000009539132756443242155_);fill:url(#SVGID_00000106846526376115483140000011468138398815058869_);" width="34.7" height="25.7"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000179629951531869362570000009673363342515209886_" d="M27.9,26.4c-1.8,1-2.9,3.5-4.9,2.2
						c2.3,6.3,10.9-1.2,15.2,11.9c-2.9-0.4-9-0.7-9.5,3c0,0.1,0,0.2,0,0.3c-0.3,4.7,3.4,5.8,2.6,7.8c5.1-1.3-0.2-7.9,6.8-7.9
						c0.2,0,0.4,0,0.5,0c4.2,0.3,4.6,4.5,2.2,5.3c1.8,0.2,6.1-0.5,7.4-4c1.2-3.4-2.2-6.7,2-9.1c0.2-0.1,0.4-0.2,0.6-0.3
						c2.7-1.1,5.4,0.5,6.6,1.8c0.1-2.8-2.8-6.5-6.6-6c-4.2,0.6-3.1,5.2-6.4,6.4c-0.5,0.2-1,0.3-1.5,0.3c-5,0.5-5.2-6.4-9-10
						c-1.1-1-2.2-1.7-3.4-2C30.2,26,29.9,26,29.6,26C29,25.9,28.4,26.1,27.9,26.4"/>
				</defs>
				<clipPath id="SVGID_00000013908305792818868360000001018272004340239515_">
					<use xlink:href="#SVGID_00000179629951531869362570000009673363342515209886_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000145018280928362404130000001300543101216745391_" cx="-808.7374" cy="461.7989" r="0.4216" gradientTransform="matrix(74.8307 0 0 74.8307 60565.7148 -34509.8008)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF587"/>
					<stop  offset="7.599368e-02" style="stop-color:#E7D970"/>
					<stop  offset="0.1319" style="stop-color:#D3C25C"/>
					<stop  offset="0.1648" style="stop-color:#BEAB49"/>
					<stop  offset="0.2207" style="stop-color:#B5A03F"/>
					<stop  offset="0.3022" style="stop-color:#A2882A"/>
					<stop  offset="0.4022" style="stop-color:#A48929"/>
					<stop  offset="0.4121" style="stop-color:#A58928"/>
					<stop  offset="0.4318" style="stop-color:#AD9333"/>
					<stop  offset="0.4663" style="stop-color:#C2AE50"/>
					<stop  offset="0.478" style="stop-color:#CAB95C"/>
					<stop  offset="0.4791" style="stop-color:#CCBC60"/>
					<stop  offset="0.4842" style="stop-color:#D5C66D"/>
					<stop  offset="0.489" style="stop-color:#D8CA71"/>
					<stop  offset="0.5437" style="stop-color:#E1D47D"/>
					<stop  offset="0.6423" style="stop-color:#F8EE9D"/>
					<stop  offset="0.6703" style="stop-color:#FFF7A7"/>
					<stop  offset="0.6831" style="stop-color:#F9F09C"/>
					<stop  offset="0.7052" style="stop-color:#EBDD80"/>
					<stop  offset="0.7339" style="stop-color:#D3BF53"/>
					<stop  offset="0.7527" style="stop-color:#C1A931"/>
					<stop  offset="0.7934" style="stop-color:#BDA426"/>
					<stop  offset="0.8297" style="stop-color:#B79E18"/>
					<stop  offset="0.8638" style="stop-color:#BEA61D"/>
					<stop  offset="0.9121" style="stop-color:#CFB92A"/>
					<stop  offset="0.9835" style="stop-color:#FFEE74"/>
					<stop  offset="0.9941" style="stop-color:#FFF079"/>
					<stop  offset="1" style="stop-color:#FFF37F"/>
				</radialGradient>
				
					<rect x="23" y="25.9" style="clip-path:url(#SVGID_00000013908305792818868360000001018272004340239515_);fill:url(#SVGID_00000145018280928362404130000001300543101216745391_);" width="34.4" height="25.6"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000010308141092477317970000004512833449780323513_" d="M28,26.5c-1.8,1-2.9,3.5-4.9,2.2
						c2.3,6,10.8-1.3,15.2,11.9c-2.9-0.4-9-0.7-9.5,2.9c0,0.1,0,0.2,0,0.3c-0.3,4.7,3.3,5.8,2.6,7.7c4.9-1.3-0.2-7.9,6.8-7.9
						c0.2,0,0.4,0,0.5,0c4.2,0.3,4.6,4.5,2.3,5.3c1.8,0.2,5.9-0.6,7.1-4c1.2-3.4-2.3-6.6,2-9.1c0.2-0.1,0.4-0.2,0.6-0.3
						c2.6-1.1,5.4,0.3,6.6,1.7c0-2.7-2.8-6.4-6.5-5.9c-4.1,0.6-3.1,5.2-6.3,6.4C44,37.9,43.5,38,43,38c-5,0.5-5.2-6.3-9-9.9
						c-1.1-1-2.2-1.7-3.3-2.1c-0.3-0.1-0.7-0.2-1-0.2C29,26,28.5,26.2,28,26.5"/>
				</defs>
				<clipPath id="SVGID_00000008126279956506421240000003678000992906398862_">
					<use xlink:href="#SVGID_00000010308141092477317970000004512833449780323513_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000034779500406629895930000015209419570291774617_" cx="-808.7747" cy="461.7423" r="0.4216" gradientTransform="matrix(75.0897 0 0 75.0897 60778.0273 -34624.8516)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF587"/>
					<stop  offset="8.346549e-02" style="stop-color:#E7D970"/>
					<stop  offset="0.1319" style="stop-color:#D7C761"/>
					<stop  offset="0.1648" style="stop-color:#C3B14D"/>
					<stop  offset="0.2172" style="stop-color:#BAA644"/>
					<stop  offset="0.3022" style="stop-color:#A48B2D"/>
					<stop  offset="0.4022" style="stop-color:#A68C2C"/>
					<stop  offset="0.4121" style="stop-color:#A78C2B"/>
					<stop  offset="0.4341" style="stop-color:#AF9736"/>
					<stop  offset="0.4728" style="stop-color:#C6B353"/>
					<stop  offset="0.478" style="stop-color:#C9B758"/>
					<stop  offset="0.4785" style="stop-color:#CAB85A"/>
					<stop  offset="0.484" style="stop-color:#D2C367"/>
					<stop  offset="0.489" style="stop-color:#D5C66B"/>
					<stop  offset="0.5414" style="stop-color:#DED077"/>
					<stop  offset="0.6358" style="stop-color:#F5EB97"/>
					<stop  offset="0.6703" style="stop-color:#FFF6A4"/>
					<stop  offset="0.6848" style="stop-color:#F8ED97"/>
					<stop  offset="0.7117" style="stop-color:#E5D674"/>
					<stop  offset="0.7479" style="stop-color:#C7B03B"/>
					<stop  offset="0.7527" style="stop-color:#C3AB33"/>
					<stop  offset="0.791" style="stop-color:#BEA628"/>
					<stop  offset="0.8297" style="stop-color:#B89F18"/>
					<stop  offset="0.8638" style="stop-color:#BFA71D"/>
					<stop  offset="0.9121" style="stop-color:#D0BA2B"/>
					<stop  offset="0.9835" style="stop-color:#FFEF73"/>
					<stop  offset="0.9941" style="stop-color:#FFF178"/>
					<stop  offset="1" style="stop-color:#FFF37E"/>
				</radialGradient>
				
					<rect x="23.1" y="26" style="clip-path:url(#SVGID_00000008126279956506421240000003678000992906398862_);fill:url(#SVGID_00000034779500406629895930000015209419570291774617_);" width="34.2" height="25.5"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000090276047387558445160000000250266816271465612_" d="M28,26.5c-1.8,1-2.9,3.4-4.9,2.3
						c2.2,5.8,10.8-1.3,15.1,11.8c-2.9-0.3-8.9-0.7-9.5,2.9c0,0.1,0,0.2,0,0.3c-0.3,4.6,3.3,5.7,2.6,7.7c4.6-1.3-0.2-7.9,6.7-7.9
						c0.2,0,0.4,0,0.5,0c4.2,0.3,4.6,4.5,2.5,5.3c1.7,0.1,5.8-0.7,6.9-4c1.2-3.4-2.3-6.6,2-9.1c0.2-0.1,0.4-0.2,0.6-0.3
						c2.6-1.1,5.4,0.2,6.7,1.6c-0.1-2.6-2.8-6.2-6.5-5.7c-4.1,0.6-3.1,5.1-6.3,6.4c-0.5,0.2-0.9,0.3-1.5,0.3c-5,0.5-5.3-6.2-9-9.8
						c-1.1-1-2.1-1.8-3.3-2.1c-0.4-0.1-0.7-0.2-1-0.2C29.1,26,28.5,26.2,28,26.5"/>
				</defs>
				<clipPath id="SVGID_00000093887445562420024100000000293828591374963846_">
					<use xlink:href="#SVGID_00000090276047387558445160000000250266816271465612_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000021817833301427065810000017421032968774186161_" cx="-808.8051" cy="461.6879" r="0.4216" gradientTransform="matrix(75.327 0 0 75.327 60972.2891 -34730.0664)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF588"/>
					<stop  offset="9.265658e-02" style="stop-color:#E7D970"/>
					<stop  offset="0.1319" style="stop-color:#DCCC65"/>
					<stop  offset="0.1648" style="stop-color:#C8B652"/>
					<stop  offset="0.2151" style="stop-color:#BFAB48"/>
					<stop  offset="0.3022" style="stop-color:#A78E2F"/>
					<stop  offset="0.4107" style="stop-color:#A98F2D"/>
					<stop  offset="0.4121" style="stop-color:#A98F2D"/>
					<stop  offset="0.4372" style="stop-color:#B19A38"/>
					<stop  offset="0.478" style="stop-color:#C6B553"/>
					<stop  offset="0.4782" style="stop-color:#C6B554"/>
					<stop  offset="0.4838" style="stop-color:#D0C061"/>
					<stop  offset="0.489" style="stop-color:#D3C465"/>
					<stop  offset="0.5386" style="stop-color:#DCCE71"/>
					<stop  offset="0.6281" style="stop-color:#F3E891"/>
					<stop  offset="0.6703" style="stop-color:#FFF6A2"/>
					<stop  offset="0.6881" style="stop-color:#F5EA90"/>
					<stop  offset="0.7235" style="stop-color:#DDCB62"/>
					<stop  offset="0.7527" style="stop-color:#C6AF37"/>
					<stop  offset="0.788" style="stop-color:#C1A92C"/>
					<stop  offset="0.8297" style="stop-color:#B9A019"/>
					<stop  offset="0.863" style="stop-color:#C0A81E"/>
					<stop  offset="0.9121" style="stop-color:#D2BC2D"/>
					<stop  offset="0.9835" style="stop-color:#FFF073"/>
					<stop  offset="0.9954" style="stop-color:#FFF278"/>
					<stop  offset="1" style="stop-color:#FFF37C"/>
				</radialGradient>
				
					<rect x="23.2" y="26" style="clip-path:url(#SVGID_00000093887445562420024100000000293828591374963846_);fill:url(#SVGID_00000021817833301427065810000017421032968774186161_);" width="34.1" height="25.4"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000067228849070575023960000011174456047994650022_" d="M28.1,26.5c-1.8,1-3,3.4-4.9,2.3
						c2.2,5.6,10.7-1.4,15.1,11.8c-2.8-0.3-8.9-0.7-9.5,2.8c0,0.1,0,0.2,0,0.3c-0.3,4.6,3.2,5.7,2.6,7.7c4.4-1.3-0.3-7.9,6.7-7.8
						c0.2,0,0.4,0,0.5,0c4.1,0.3,4.6,4.5,2.6,5.4c1.7,0.1,5.6-0.8,6.7-4c1.2-3.4-2.3-6.5,2-9.1c0.2-0.1,0.4-0.2,0.6-0.3
						c2.5-1.1,5.4,0.1,6.7,1.5c-0.2-2.5-2.9-6-6.5-5.5c-4,0.5-3,5.1-6.3,6.4c-0.4,0-0.9,0.2-1.4,0.2c-5,0.6-5.4-6.1-9-9.8
						c-1-1-2.1-1.8-3.2-2.2c-0.4-0.1-0.7-0.2-1.1-0.2C29.1,26.1,28.6,26.2,28.1,26.5"/>
				</defs>
				<clipPath id="SVGID_00000026865166029492113400000013232450418779986841_">
					<use xlink:href="#SVGID_00000067228849070575023960000011174456047994650022_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000043423952344214688060000006285424643003000992_" cx="-808.8334" cy="461.6378" r="0.4215" gradientTransform="matrix(75.5409 0 0 75.5409 61147.5508 -34824.7969)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF588"/>
					<stop  offset="0.107" style="stop-color:#E6D970"/>
					<stop  offset="0.1319" style="stop-color:#E0D26A"/>
					<stop  offset="0.1648" style="stop-color:#CEBC57"/>
					<stop  offset="0.2124" style="stop-color:#C4B14D"/>
					<stop  offset="0.2959" style="stop-color:#AB9332"/>
					<stop  offset="0.3022" style="stop-color:#A99130"/>
					<stop  offset="0.4022" style="stop-color:#AB922F"/>
					<stop  offset="0.4121" style="stop-color:#AC922F"/>
					<stop  offset="0.4401" style="stop-color:#B49D3A"/>
					<stop  offset="0.478" style="stop-color:#C5B34F"/>
					<stop  offset="0.4837" style="stop-color:#CDBD5C"/>
					<stop  offset="0.489" style="stop-color:#D0C060"/>
					<stop  offset="0.5374" style="stop-color:#D9CA6C"/>
					<stop  offset="0.6245" style="stop-color:#F1E58C"/>
					<stop  offset="0.6703" style="stop-color:#FFF69F"/>
					<stop  offset="0.6952" style="stop-color:#F0E484"/>
					<stop  offset="0.7486" style="stop-color:#CBB63F"/>
					<stop  offset="0.7527" style="stop-color:#C8B239"/>
					<stop  offset="0.7871" style="stop-color:#C3AC2E"/>
					<stop  offset="0.8297" style="stop-color:#BAA21A"/>
					<stop  offset="0.863" style="stop-color:#C1AA20"/>
					<stop  offset="0.9121" style="stop-color:#D3BE30"/>
					<stop  offset="0.9835" style="stop-color:#FFF072"/>
					<stop  offset="0.9954" style="stop-color:#FFF277"/>
					<stop  offset="1" style="stop-color:#FFF37B"/>
				</radialGradient>
				
					<rect x="23.2" y="26.1" style="clip-path:url(#SVGID_00000026865166029492113400000013232450418779986841_);fill:url(#SVGID_00000043423952344214688060000006285424643003000992_);" width="33.9" height="25.3"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000163788869113433441590000016131659651502726554_" d="M28.2,26.5c-1.8,1-3,3.4-4.9,2.4
						c2.1,5.4,10.7-1.4,15,11.7c-2.8-0.3-8.9-0.7-9.5,2.8c0,0.1,0,0.2,0,0.3c-0.4,4.6,3.1,5.7,2.6,7.6c4.2-1.3-0.3-7.9,6.6-7.8
						c0.2,0,0.4,0,0.5,0c4.1,0.3,4.6,4.4,2.8,5.4c1.7,0.1,5.4-0.9,6.5-4c1.2-3.4-2.3-6.5,2-9.1c0.2-0.1,0.4-0.2,0.5-0.3
						c2.4-1,5.4,0,6.7,1.4c-0.3-2.4-2.9-5.9-6.5-5.4c-4,0.5-3,5.1-6.2,6.4c-0.4,0.2-0.9,0.3-1.4,0.4c-5,0.6-5.4-6-9-9.7
						c-1-1-2-1.8-3.2-2.2c-0.4-0.1-0.8-0.2-1.1-0.2C29.1,26.1,28.6,26.3,28.2,26.5"/>
				</defs>
				<clipPath id="SVGID_00000107557727240507913900000017453446115673273741_">
					<use xlink:href="#SVGID_00000163788869113433441590000016131659651502726554_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000133487192082655112350000014911801121331458715_" cx="-808.8517" cy="461.5877" r="0.4219" gradientTransform="matrix(75.7349 0 0 75.7349 61305.9258 -34910.2891)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF589"/>
					<stop  offset="0.123" style="stop-color:#E8D970"/>
					<stop  offset="0.1319" style="stop-color:#E6D76E"/>
					<stop  offset="0.1648" style="stop-color:#D3C25C"/>
					<stop  offset="0.2099" style="stop-color:#CAB752"/>
					<stop  offset="0.2892" style="stop-color:#B29937"/>
					<stop  offset="0.3022" style="stop-color:#AD9432"/>
					<stop  offset="0.4121" style="stop-color:#AE9531"/>
					<stop  offset="0.444" style="stop-color:#B6A03B"/>
					<stop  offset="0.478" style="stop-color:#C3B14B"/>
					<stop  offset="0.4833" style="stop-color:#CBBA56"/>
					<stop  offset="0.489" style="stop-color:#CEBE5A"/>
					<stop  offset="0.5356" style="stop-color:#D7C866"/>
					<stop  offset="0.6196" style="stop-color:#EEE386"/>
					<stop  offset="0.6703" style="stop-color:#FFF69C"/>
					<stop  offset="0.7181" style="stop-color:#E2D165"/>
					<stop  offset="0.7527" style="stop-color:#CBB53B"/>
					<stop  offset="0.7855" style="stop-color:#C5AF30"/>
					<stop  offset="0.8297" style="stop-color:#BAA31A"/>
					<stop  offset="0.863" style="stop-color:#C1AB21"/>
					<stop  offset="0.9121" style="stop-color:#D4BF32"/>
					<stop  offset="0.9835" style="stop-color:#FFF171"/>
					<stop  offset="0.9962" style="stop-color:#FFF376"/>
					<stop  offset="1" style="stop-color:#FFF479"/>
				</radialGradient>
				
					<rect x="23.3" y="26.1" style="clip-path:url(#SVGID_00000107557727240507913900000017453446115673273741_);fill:url(#SVGID_00000133487192082655112350000014911801121331458715_);" width="33.8" height="25.2"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000166661790539952335370000001689622332195872153_" d="M28.2,26.5c-1.8,1-3,3.4-4.8,2.4
						c2.1,5.2,10.6-1.5,15,11.7c-2.8-0.2-8.9-0.7-9.5,2.7c0,0.1,0,0.2,0,0.3c-0.4,4.5,3,5.7,2.6,7.6c3.9-1.4-0.4-7.9,6.5-7.8
						c0.2,0,0.3,0,0.5,0c4.1,0.3,4.7,4.4,2.9,5.4c1.7,0,5.2-0.9,6.3-4.1c1.2-3.4-2.3-6.4,2-9.1c0.2-0.1,0.4-0.2,0.5-0.3
						c2.4-1,5.4-0.1,6.8,1.3c-0.5-2.4-2.9-5.7-6.5-5.2c-3.9,0.5-2.9,5-6.2,6.4c-0.4,0.2-0.9,0.3-1.4,0.4c-5,0.6-5.5-5.9-9-9.6
						c-1-1-2-1.8-3.2-2.3c-0.4-0.2-0.8-0.2-1.2-0.2C29.2,26.2,28.7,26.3,28.2,26.5"/>
				</defs>
				<clipPath id="SVGID_00000014635200962792062500000014917147033514567333_">
					<use xlink:href="#SVGID_00000166661790539952335370000001689622332195872153_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000173142668432913240200000016290904268404863399_" cx="-808.8828" cy="461.554" r="0.4217" gradientTransform="matrix(75.9171 0 0 75.9171 61455.6758 -34991.5273)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF589"/>
					<stop  offset="0.1319" style="stop-color:#EADC73"/>
					<stop  offset="0.1648" style="stop-color:#D8C861"/>
					<stop  offset="0.2078" style="stop-color:#CFBD57"/>
					<stop  offset="0.2832" style="stop-color:#B69F3D"/>
					<stop  offset="0.3022" style="stop-color:#AF9735"/>
					<stop  offset="0.4107" style="stop-color:#B09733"/>
					<stop  offset="0.4121" style="stop-color:#B09733"/>
					<stop  offset="0.448" style="stop-color:#B8A23C"/>
					<stop  offset="0.478" style="stop-color:#C1AF47"/>
					<stop  offset="0.4828" style="stop-color:#C8B750"/>
					<stop  offset="0.489" style="stop-color:#CCBB54"/>
					<stop  offset="0.534" style="stop-color:#D5C560"/>
					<stop  offset="0.6151" style="stop-color:#ECE080"/>
					<stop  offset="0.6703" style="stop-color:#FFF699"/>
					<stop  offset="0.7527" style="stop-color:#CDB83D"/>
					<stop  offset="0.7855" style="stop-color:#C7B132"/>
					<stop  offset="0.8297" style="stop-color:#BBA41C"/>
					<stop  offset="0.8622" style="stop-color:#C2AC22"/>
					<stop  offset="0.9116" style="stop-color:#D6C133"/>
					<stop  offset="0.9121" style="stop-color:#D6C133"/>
					<stop  offset="0.9835" style="stop-color:#FFF272"/>
					<stop  offset="1" style="stop-color:#FFF476"/>
				</radialGradient>
				
					<rect x="23.4" y="26.2" style="clip-path:url(#SVGID_00000014635200962792062500000014917147033514567333_);fill:url(#SVGID_00000173142668432913240200000016290904268404863399_);" width="33.7" height="25.1"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000047739146957765376790000014344235765333669536_" d="M28.3,26.6c-1.8,0.9-3,3.4-4.8,2.5
						c2,5,10.5-1.5,14.9,11.7c-2.7-0.2-8.9-0.6-9.5,2.7c0,0.1,0,0.2,0,0.3c-0.4,4.5,2.9,5.6,2.7,7.6c3.7-1.4-0.4-7.9,6.5-7.8
						c0.2,0,0.3,0,0.5,0c4.1,0.3,4.7,4.4,3,5.4c1.7,0,5-1,6.1-4.1c1.2-3.4-2.3-6.4,2-9c0.2-0.1,0.3-0.2,0.5-0.3
						c2.3-1,5.4-0.2,6.8,1.2c-0.6-2.3-2.9-5.5-6.5-5.1c-3.9,0.5-2.9,5-6.1,6.4c-0.4,0.2-0.9,0.3-1.4,0.4c-5,0.7-5.6-5.8-9-9.6
						c-0.9-1-1.9-1.8-3.1-2.3c-0.4-0.2-0.8-0.3-1.2-0.3C29.2,26.2,28.7,26.3,28.3,26.6"/>
				</defs>
				<clipPath id="SVGID_00000156561822900534350460000011977378690289511578_">
					<use xlink:href="#SVGID_00000047739146957765376790000014344235765333669536_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000087393334073437780570000006742627074275763343_" cx="-808.9032" cy="461.5106" r="0.4219" gradientTransform="matrix(76.0952 0 0 76.0952 61601.3906 -35070.1875)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF68A"/>
					<stop  offset="0.1319" style="stop-color:#F0E278"/>
					<stop  offset="0.1648" style="stop-color:#DECE65"/>
					<stop  offset="0.2058" style="stop-color:#D4C35B"/>
					<stop  offset="0.2778" style="stop-color:#BBA640"/>
					<stop  offset="0.3022" style="stop-color:#B19A36"/>
					<stop  offset="0.4121" style="stop-color:#B29A35"/>
					<stop  offset="0.4552" style="stop-color:#BAA53D"/>
					<stop  offset="0.478" style="stop-color:#BFAD42"/>
					<stop  offset="0.4826" style="stop-color:#C5B44A"/>
					<stop  offset="0.489" style="stop-color:#C9B84E"/>
					<stop  offset="0.532" style="stop-color:#D2C25A"/>
					<stop  offset="0.6095" style="stop-color:#E9DD7A"/>
					<stop  offset="0.6703" style="stop-color:#FFF697"/>
					<stop  offset="0.7013" style="stop-color:#ECDF75"/>
					<stop  offset="0.7527" style="stop-color:#CFBB3F"/>
					<stop  offset="0.7847" style="stop-color:#C9B434"/>
					<stop  offset="0.8297" style="stop-color:#BCA61D"/>
					<stop  offset="0.8622" style="stop-color:#C3AE24"/>
					<stop  offset="0.9116" style="stop-color:#D7C336"/>
					<stop  offset="0.9121" style="stop-color:#D7C336"/>
					<stop  offset="0.9835" style="stop-color:#FFF371"/>
					<stop  offset="1" style="stop-color:#FFF475"/>
				</radialGradient>
				
					<rect x="23.5" y="26.2" style="clip-path:url(#SVGID_00000156561822900534350460000011977378690289511578_);fill:url(#SVGID_00000087393334073437780570000006742627074275763343_);" width="33.6" height="25"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000024715311126855542710000003858450067363500690_" d="M28.4,26.6c-1.8,0.9-3,3.4-4.8,2.5
						c2,4.7,10.5-1.6,14.9,11.6c-2.7-0.1-8.9-0.6-9.4,2.6c0,0.1,0,0.2,0,0.3c-0.4,4.5,2.8,5.6,2.7,7.6c3.4-1.4-0.5-7.9,6.4-7.8
						c0.2,0,0.3,0,0.5,0c4,0.3,4.7,4.4,3.2,5.4c1.7-0.1,4.9-1.1,5.9-4.1c1.2-3.4-2.3-6.3,2-9c0.2-0.1,0.3-0.2,0.5-0.3
						c2.2-1,5.4-0.3,6.8,1.1c-0.7-2.2-2.9-5.4-6.5-4.9c-3.8,0.5-2.8,5-6.1,6.4c-0.4,0.2-0.9,0.3-1.4,0.4c-5,0.7-5.6-5.6-9-9.5
						c-0.9-1-1.9-1.9-3.1-2.4c-0.5-0.2-0.9-0.3-1.3-0.3C29.2,26.2,28.8,26.4,28.4,26.6"/>
				</defs>
				<clipPath id="SVGID_00000006686983231572138170000002511859640113741448_">
					<use xlink:href="#SVGID_00000024715311126855542710000003858450067363500690_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000101823890407713872530000002220747701131596718_" cx="-808.9254" cy="461.4699" r="0.4216" gradientTransform="matrix(76.2726 0 0 76.2726 61746.6289 -35148.6875)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF68A"/>
					<stop  offset="0.1319" style="stop-color:#F4E77D"/>
					<stop  offset="0.1648" style="stop-color:#E4D46A"/>
					<stop  offset="0.2041" style="stop-color:#DAC960"/>
					<stop  offset="0.273" style="stop-color:#C1AC45"/>
					<stop  offset="0.3022" style="stop-color:#B49D38"/>
					<stop  offset="0.4121" style="stop-color:#B59E37"/>
					<stop  offset="0.4701" style="stop-color:#BCA93D"/>
					<stop  offset="0.478" style="stop-color:#BDAB3E"/>
					<stop  offset="0.4823" style="stop-color:#C3B145"/>
					<stop  offset="0.489" style="stop-color:#C7B549"/>
					<stop  offset="0.5307" style="stop-color:#D0BF55"/>
					<stop  offset="0.6057" style="stop-color:#E7DA75"/>
					<stop  offset="0.6703" style="stop-color:#FFF695"/>
					<stop  offset="0.723" style="stop-color:#E1D05D"/>
					<stop  offset="0.7527" style="stop-color:#D2BE42"/>
					<stop  offset="0.7827" style="stop-color:#CCB737"/>
					<stop  offset="0.8297" style="stop-color:#BDA71D"/>
					<stop  offset="0.8622" style="stop-color:#C4AF24"/>
					<stop  offset="0.9116" style="stop-color:#D7C438"/>
					<stop  offset="0.9121" style="stop-color:#D7C438"/>
					<stop  offset="0.9835" style="stop-color:#FFF370"/>
					<stop  offset="1" style="stop-color:#FFF473"/>
				</radialGradient>
				
					<rect x="23.5" y="26.2" style="clip-path:url(#SVGID_00000006686983231572138170000002511859640113741448_);fill:url(#SVGID_00000101823890407713872530000002220747701131596718_);" width="33.5" height="24.9"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000103247445566392986190000015995626849133954712_" d="M28.4,26.6c-1.8,0.9-3,3.3-4.8,2.6
						c1.9,4.5,10.4-1.6,14.8,11.6c-2.7-0.1-8.8-0.6-9.4,2.6c0,0.1,0,0.2,0,0.3c-0.4,4.5,2.7,5.6,2.7,7.5c3.2-1.4-0.5-7.9,6.4-7.7
						c0.2,0,0.3,0,0.5,0c4,0.3,4.7,4.4,3.3,5.4c1.7-0.1,4.7-1.2,5.7-4.1c1.2-3.4-2.3-6.3,2-9c0.2-0.1,0.3-0.2,0.5-0.3
						c2.2-1,5.4-0.4,6.9,1c-0.8-2.1-2.9-5.2-6.4-4.8c-3.8,0.5-2.8,4.9-6,6.4c-0.4,0.2-0.9,0.3-1.4,0.4c-5,0.7-5.7-5.5-9-9.4
						c-0.9-1-1.8-1.9-3-2.4c-0.5-0.2-0.9-0.3-1.3-0.3C29.3,26.3,28.8,26.4,28.4,26.6"/>
				</defs>
				<clipPath id="SVGID_00000114068601182763537870000018175994230608461723_">
					<use xlink:href="#SVGID_00000103247445566392986190000015995626849133954712_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000024717216431488120790000007326971112247904934_" cx="-808.9446" cy="461.4223" r="0.4212" gradientTransform="matrix(76.459 0 0 76.459 61898.9336 -35230.8086)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF68B"/>
					<stop  offset="0.1319" style="stop-color:#FAED82"/>
					<stop  offset="0.1648" style="stop-color:#E9DA70"/>
					<stop  offset="0.2025" style="stop-color:#DFCF66"/>
					<stop  offset="0.2686" style="stop-color:#C6B24B"/>
					<stop  offset="0.3022" style="stop-color:#B7A03B"/>
					<stop  offset="0.4121" style="stop-color:#B8A03A"/>
					<stop  offset="0.478" style="stop-color:#BBA939"/>
					<stop  offset="0.4823" style="stop-color:#C0AE40"/>
					<stop  offset="0.489" style="stop-color:#C4B244"/>
					<stop  offset="0.5298" style="stop-color:#CDBC50"/>
					<stop  offset="0.6033" style="stop-color:#E5D870"/>
					<stop  offset="0.6703" style="stop-color:#FFF692"/>
					<stop  offset="0.6834" style="stop-color:#F7ED84"/>
					<stop  offset="0.7296" style="stop-color:#DFCE56"/>
					<stop  offset="0.7527" style="stop-color:#D5C244"/>
					<stop  offset="0.7827" style="stop-color:#CEBB39"/>
					<stop  offset="0.8297" style="stop-color:#BDA91F"/>
					<stop  offset="0.8622" style="stop-color:#C4B126"/>
					<stop  offset="0.9116" style="stop-color:#D9C63B"/>
					<stop  offset="0.9121" style="stop-color:#D9C63B"/>
					<stop  offset="0.9835" style="stop-color:#FFF470"/>
					<stop  offset="1" style="stop-color:#FFF472"/>
				</radialGradient>
				
					<rect x="23.6" y="26.3" style="clip-path:url(#SVGID_00000114068601182763537870000018175994230608461723_);fill:url(#SVGID_00000024717216431488120790000007326971112247904934_);" width="33.3" height="24.8"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000165219407083990107400000012790477311932957880_" d="M28.5,26.6c-1.8,0.9-3.1,3.3-4.8,2.6
						c1.9,4.3,10.4-1.6,14.8,11.6c-2.7-0.1-8.8-0.6-9.4,2.5c0,0.1,0,0.2,0,0.3c-0.4,4.4,2.6,5.6,2.7,7.5c3-1.5-0.6-7.9,6.3-7.7
						c0.2,0,0.3,0,0.5,0c4,0.3,4.7,4.4,3.5,5.5c1.6-0.1,4.5-1.3,5.5-4.1c1.1-3.4-2.3-6.3,2-9c0.2-0.1,0.3-0.2,0.5-0.3
						c2.1-0.9,5.4-0.5,6.9,0.9c-0.9-2.1-2.9-5-6.4-4.6c-3.7,0.5-2.7,4.9-6,6.4c-0.4,0.2-0.9,0.3-1.4,0.4c-5,0.8-5.8-5.4-9-9.3
						c-0.8-1-1.8-1.9-3-2.5c-0.5-0.2-0.9-0.3-1.4-0.3C29.3,26.3,28.9,26.4,28.5,26.6"/>
				</defs>
				<clipPath id="SVGID_00000066488960849439823600000002210070826658330269_">
					<use xlink:href="#SVGID_00000165219407083990107400000012790477311932957880_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000065782767673880031200000004098484382835297712_" cx="-808.9744" cy="461.3827" r="0.4216" gradientTransform="matrix(76.6604 0 0 76.6604 62064.2383 -35320.4648)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF68B"/>
					<stop  offset="0.1319" style="stop-color:#FFF287"/>
					<stop  offset="0.1648" style="stop-color:#EFE176"/>
					<stop  offset="0.2006" style="stop-color:#E6D66C"/>
					<stop  offset="0.2633" style="stop-color:#CCB951"/>
					<stop  offset="0.3022" style="stop-color:#BAA33D"/>
					<stop  offset="0.4107" style="stop-color:#B9A33B"/>
					<stop  offset="0.4121" style="stop-color:#B9A33B"/>
					<stop  offset="0.478" style="stop-color:#BAA734"/>
					<stop  offset="0.4819" style="stop-color:#BEAC3A"/>
					<stop  offset="0.489" style="stop-color:#C2B03E"/>
					<stop  offset="0.5286" style="stop-color:#CBBA4A"/>
					<stop  offset="0.5999" style="stop-color:#E3D66A"/>
					<stop  offset="0.6703" style="stop-color:#FFF68F"/>
					<stop  offset="0.6942" style="stop-color:#F1E576"/>
					<stop  offset="0.7322" style="stop-color:#DECE53"/>
					<stop  offset="0.7527" style="stop-color:#D7C546"/>
					<stop  offset="0.7809" style="stop-color:#D0BE3B"/>
					<stop  offset="0.8297" style="stop-color:#BFAA1E"/>
					<stop  offset="0.8614" style="stop-color:#C6B126"/>
					<stop  offset="0.9097" style="stop-color:#D9C63B"/>
					<stop  offset="0.9121" style="stop-color:#DAC73C"/>
					<stop  offset="0.9835" style="stop-color:#FFF46F"/>
					<stop  offset="1" style="stop-color:#FFF470"/>
				</radialGradient>
				
					<rect x="23.7" y="26.3" style="clip-path:url(#SVGID_00000066488960849439823600000002210070826658330269_);fill:url(#SVGID_00000065782767673880031200000004098484382835297712_);" width="33.2" height="24.7"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000174601503814539057270000012310695768219088016_" d="M26.9,27.9c-1,0.9-2,1.8-3.1,1.4
						c1.8,4.1,10.3-1.7,14.8,11.5c-2.7,0-9.2-0.7-9.5,2.7c-0.4,4.4,2.5,5.5,2.7,7.5c2.8-1.5-0.8-8.2,6.7-7.7
						c3.9,0.3,4.8,4.4,3.6,5.5c1.6-0.2,4.3-1.4,5.3-4.2c1.1-3.3-2.3-6.2,1.9-9c1.9-1.3,5.7-1.1,7.5,0.5c-1.1-2-3-4.9-6.4-4.5
						c-4.1,0.5-2.4,6-7.4,6.8c-6.2,1-6-8.9-11.9-11.8c-0.5-0.2-1-0.4-1.4-0.4C28.6,26.4,27.7,27.1,26.9,27.9"/>
				</defs>
				<clipPath id="SVGID_00000041999333024007536150000004647624964099651519_">
					<use xlink:href="#SVGID_00000174601503814539057270000012310695768219088016_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000106869019507441296340000017856503535485524383_" cx="-808.9938" cy="461.3259" r="0.4212" gradientTransform="matrix(76.8844 0 0 76.8844 62247.0039 -35419.1992)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFF68C"/>
					<stop  offset="0.1319" style="stop-color:#FFF68C"/>
					<stop  offset="0.3022" style="stop-color:#BCA63F"/>
					<stop  offset="0.4165" style="stop-color:#BCA63D"/>
					<stop  offset="0.4576" style="stop-color:#BAA536"/>
					<stop  offset="0.478" style="stop-color:#B8A52F"/>
					<stop  offset="0.5059" style="stop-color:#CABA47"/>
					<stop  offset="0.5492" style="stop-color:#E1D465"/>
					<stop  offset="0.5917" style="stop-color:#F2E77B"/>
					<stop  offset="0.6326" style="stop-color:#FCF288"/>
					<stop  offset="0.6703" style="stop-color:#FFF68C"/>
					<stop  offset="0.6985" style="stop-color:#F1E574"/>
					<stop  offset="0.7576" style="stop-color:#D6C647"/>
					<stop  offset="0.8033" style="stop-color:#C5B22A"/>
					<stop  offset="0.8297" style="stop-color:#BFAB20"/>
					<stop  offset="0.8597" style="stop-color:#C5B228"/>
					<stop  offset="0.9056" style="stop-color:#D6C63D"/>
					<stop  offset="0.9613" style="stop-color:#F2E55F"/>
					<stop  offset="0.9835" style="stop-color:#FFF46F"/>
					<stop  offset="1" style="stop-color:#FFF46F"/>
				</radialGradient>
				
					<rect x="23.8" y="26.4" style="clip-path:url(#SVGID_00000041999333024007536150000004647624964099651519_);fill:url(#SVGID_00000106869019507441296340000017856503535485524383_);" width="33.1" height="24.7"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000085225292345106997010000016214397611250306699_" d="M41.1,21.3c-2.6,0.3-4.5,2.8-4.2,5.4s2.8,4.5,5.4,4.2
						c2.6-0.3,4.5-2.8,4.2-5.4c-0.3-2.4-2.4-4.2-4.8-4.2C41.5,21.3,41.3,21.3,41.1,21.3"/>
				</defs>
				<clipPath id="SVGID_00000013173319241328510750000016732862779244759700_">
					<use xlink:href="#SVGID_00000085225292345106997010000016214397611250306699_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000183229682703045736850000009179819338470959787_" cx="-786.6917" cy="521.4365" r="0.4216" gradientTransform="matrix(17.0282 -2.2417 2.2417 17.0282 12267.3809 -10618.9033)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FFFDED"/>
					<stop  offset="6.324048e-02" style="stop-color:#FFFDE9"/>
					<stop  offset="0.1242" style="stop-color:#FFFBDE"/>
					<stop  offset="0.1374" style="stop-color:#FFFBDA"/>
					<stop  offset="0.1804" style="stop-color:#F8F3C3"/>
					<stop  offset="0.3414" style="stop-color:#E0D570"/>
					<stop  offset="0.4735" style="stop-color:#CEBF34"/>
					<stop  offset="0.57" style="stop-color:#C3B10E"/>
					<stop  offset="0.6209" style="stop-color:#BFAC00"/>
					<stop  offset="1" style="stop-color:#91741D"/>
				</radialGradient>
				
					<polygon style="clip-path:url(#SVGID_00000013173319241328510750000016732862779244759700_);fill:url(#SVGID_00000183229682703045736850000009179819338470959787_);" points="
					35.3,21.4 46.7,19.9 48.2,31 36.8,32.5 				"/>
			</g>
		</g>
		<g>
			<g>
				<defs>
					<path id="SVGID_00000086679407991625783410000016182997313625257624_" d="M51.6,43c-2.6,0.3-4.5,2.8-4.2,5.4
						c0.3,2.6,2.8,4.5,5.4,4.2c2.6-0.3,4.5-2.8,4.2-5.4c-0.3-2.4-2.4-4.2-4.8-4.2C52,43,51.8,43,51.6,43"/>
				</defs>
				<clipPath id="SVGID_00000037689676238343591400000013204612561698929305_">
					<use xlink:href="#SVGID_00000086679407991625783410000016182997313625257624_"  style="overflow:visible;"/>
				</clipPath>
				
					<radialGradient id="SVGID_00000060726153069446382360000014400526071876610716_" cx="-787.2793" cy="519.6166" r="0.4215" gradientTransform="matrix(17.028 -2.2417 2.2417 17.028 12291.7842 -10567.416)" gradientUnits="userSpaceOnUse">
					<stop  offset="0" style="stop-color:#FDFDFD"/>
					<stop  offset="0.1374" style="stop-color:#FCFCFC"/>
					<stop  offset="0.3424" style="stop-color:#CACBCC"/>
					<stop  offset="0.513" style="stop-color:#A6A9AB"/>
					<stop  offset="0.6044" style="stop-color:#999C9E"/>
					<stop  offset="1" style="stop-color:#565657"/>
				</radialGradient>
				
					<polygon style="clip-path:url(#SVGID_00000037689676238343591400000013204612561698929305_);fill:url(#SVGID_00000060726153069446382360000014400526071876610716_);" points="
					45.8,43.1 57.2,41.6 58.6,52.8 47.3,54.3 				"/>
			</g>
		</g>
		<g class="st200">
			<g>
				<g>
					<defs>
						<rect id="SVGID_00000098221108015283549020000007289773915753848252_" x="36.9" y="21.3" width="9.7" height="9.7"/>
					</defs>
					<clipPath id="SVGID_00000032642311277151887490000001124722687961724046_">
						<use xlink:href="#SVGID_00000098221108015283549020000007289773915753848252_"  style="overflow:visible;"/>
					</clipPath>
					<g style="clip-path:url(#SVGID_00000032642311277151887490000001124722687961724046_);">
						<g>
							<defs>
								<path id="SVGID_00000146477794693877941030000008866964155460682129_" d="M46.5,25.5c0.3,2.6-1.5,5.1-4.2,5.4
									c-2.6,0.3-5.1-1.5-5.4-4.2c-0.3-2.6,1.5-5.1,4.2-5.4C43.8,20.9,46.2,22.8,46.5,25.5"/>
							</defs>
							<clipPath id="SVGID_00000067924996514119111300000010000213484712846780_">
								<use xlink:href="#SVGID_00000146477794693877941030000008866964155460682129_"  style="overflow:visible;"/>
							</clipPath>
							
								<radialGradient id="SVGID_00000047753316004131882290000018155604705763072696_" cx="-786.8415" cy="520.9724" r="0.4216" gradientTransform="matrix(17.1261 -2.2546 2.2546 17.1261 12341.4346 -10672.542)" gradientUnits="userSpaceOnUse">
								<stop  offset="0" style="stop-color:#FFFDED"/>
								<stop  offset="0.1142" style="stop-color:#FFFBDE"/>
								<stop  offset="0.1374" style="stop-color:#FFFBDA"/>
								<stop  offset="0.2022" style="stop-color:#FEFAD6"/>
								<stop  offset="0.2664" style="stop-color:#FCF6CA"/>
								<stop  offset="0.3303" style="stop-color:#F7F0B6"/>
								<stop  offset="0.3942" style="stop-color:#F2E79A"/>
								<stop  offset="0.458" style="stop-color:#EADC76"/>
								<stop  offset="0.5219" style="stop-color:#E1CF4A"/>
								<stop  offset="0.5845" style="stop-color:#D6BF17"/>
								<stop  offset="0.6099" style="stop-color:#D1B800"/>
								<stop  offset="0.7409" style="stop-color:#BBA700"/>
								<stop  offset="0.8462" style="stop-color:#A79600"/>
								<stop  offset="1" style="stop-color:#918300"/>
							</radialGradient>
							
								<polygon style="clip-path:url(#SVGID_00000067924996514119111300000010000213484712846780_);fill:url(#SVGID_00000047753316004131882290000018155604705763072696_);" points="
								35.3,21.1 46.7,19.6 48.2,31 36.8,32.5 							"/>
						</g>
					</g>
				</g>
			</g>
		</g>
		<g class="st203">
			<g>
				<g>
					<defs>
						<rect id="SVGID_00000049199984962892653450000006984790016556425121_" x="47.4" y="43" width="9.7" height="9.7"/>
					</defs>
					<clipPath id="SVGID_00000099655930267102370560000000256948655823331995_">
						<use xlink:href="#SVGID_00000049199984962892653450000006984790016556425121_"  style="overflow:visible;"/>
					</clipPath>
					<g style="clip-path:url(#SVGID_00000099655930267102370560000000256948655823331995_);">
						<g>
							<defs>
								<path id="SVGID_00000145053561058025431350000002334180414836603552_" d="M57,47.2c0.3,2.6-1.5,5.1-4.2,5.4
									c-2.6,0.3-5.1-1.5-5.4-4.2c-0.3-2.6,1.5-5.1,4.2-5.4C54.2,42.7,56.7,44.5,57,47.2"/>
							</defs>
							<clipPath id="SVGID_00000124843924305581178830000008536179872634135439_">
								<use xlink:href="#SVGID_00000145053561058025431350000002334180414836603552_"  style="overflow:visible;"/>
							</clipPath>
							
								<radialGradient id="SVGID_00000114772340800578502720000009779766786312702130_" cx="-787.2595" cy="519.5775" r="0.4215" gradientTransform="matrix(17.0274 -2.2416 2.2416 17.0274 12291.1123 -10566.3096)" gradientUnits="userSpaceOnUse">
								<stop  offset="0" style="stop-color:#FFFFFF"/>
								<stop  offset="0.1538" style="stop-color:#EEEEEF"/>
								<stop  offset="0.2554" style="stop-color:#E0E1E2"/>
								<stop  offset="0.4458" style="stop-color:#BCBEC0"/>
								<stop  offset="0.6099" style="stop-color:#989C9E"/>
								<stop  offset="0.7939" style="stop-color:#7D8285"/>
								<stop  offset="0.8462" style="stop-color:#757A7D"/>
								<stop  offset="1" style="stop-color:#525557"/>
							</radialGradient>
							
								<polygon style="clip-path:url(#SVGID_00000124843924305581178830000008536179872634135439_);fill:url(#SVGID_00000114772340800578502720000009779766786312702130_);" points="
								45.8,42.8 57.2,41.3 58.7,52.8 47.3,54.3 							"/>
						</g>
					</g>
				</g>
			</g>
		</g>
	</g>
</g>
<g>
	<g>
		<path class="st206" d="M50.3,10.6c0.6,4.9-2.8,9.3-7.6,10c-4.9,0.6-9.3-2.8-10-7.6C32,8.1,35.5,3.6,40.3,3
			C45.2,2.3,49.6,5.7,50.3,10.6"/>
		<path class="st206" d="M59,19.9c-9.3,1.4-7.2,11-13,12.4c-9.5,2.3-9.8-14.1-19.6-20.2c-13.6-8.4-14.3,8.4-21.6,2.2
			c5.7,17.9,20.4,0.9,28,21.2C27.2,35.6,17,36,16.3,44.5c-0.7,10,7.9,11.6,4.2,15.7c16.3,0,3.5-13.6,15.2-14.2
			c8.1-0.4,8,7.5,0.2,8.5c3.7,1.4,17.2,2,20.1-6.7c2.3-7-4.2-13.4,4.7-16.7c5.8-2.1,9.7,3,10.7,5.2C75.9,29,67.9,18.6,59,19.9"/>
		<g>
			<g>
				<defs>
					<rect id="SVGID_00000013893260421658813750000014124133975879135925_" x="32.6" y="2.9" width="17.8" height="17.8"/>
				</defs>
				<clipPath id="SVGID_00000098187683445685574170000014265649543597352070_">
					<use xlink:href="#SVGID_00000013893260421658813750000014124133975879135925_"  style="overflow:visible;"/>
				</clipPath>
			</g>
		</g>
		<path class="st207" d="M69.5,50.5c0.6,4.9-2.8,9.3-7.6,10c-4.9,0.6-9.3-2.8-10-7.6c-0.6-4.9,2.8-9.3,7.6-10
			C64.4,42.3,68.9,45.7,69.5,50.5"/>
	</g>
	<g>
		<polygon class="st208" points="21.8,64.5 27.1,64.5 27.1,65.8 23.7,65.8 23.7,66.8 26.8,66.8 26.8,68 23.7,68 23.7,69.9 
			21.8,69.9 		"/>
		<polygon class="st208" points="40.6,64.5 45.8,64.5 45.8,65.8 42.5,65.8 42.5,66.8 45.5,66.8 45.5,68 42.5,68 42.5,69.9 
			40.6,69.9 		"/>
		<path class="st208" d="M27.6,64.5h1.9v3c0,0.3,0,0.6,0.2,0.8c0.2,0.4,0.7,0.5,1.1,0.5c0.5,0,0.8-0.2,0.9-0.3
			c0.3-0.3,0.3-0.8,0.3-1v-3h1.9v2.8c0,0.6,0,1.3-0.3,1.7c-0.6,0.9-2,1-2.8,1c-1.2,0-1.9-0.2-2.3-0.4c-0.8-0.4-0.9-1.3-0.9-1.7V64.5
			z"/>
		<path class="st208" d="M35.3,64.5h1.9v3.7c0,0.3,0,0.8-0.4,1.2c-0.4,0.4-1,0.4-1.5,0.5h-1.1v-1.3h0.4c0.4,0,0.6,0,0.7-0.6V64.5z"
			/>
		<rect x="37.9" y="64.5" class="st208" width="1.9" height="5.4"/>
		<rect x="46.4" y="64.5" class="st208" width="1.9" height="5.4"/>
		<g>
			<g>
				<path class="st208" d="M49,64.5h1.9v4.1h2.9v1.2H49V64.5z"/>
				<path class="st208" d="M57.3,69.9L56,66.2h0v3.8h-1.9v-5.4h3l0.8,2.5l0.8-2.5h3v5.4h-1.9v-3.8h0l-1.3,3.8H57.3z"/>
			</g>
		</g>
		<path class="st208" d="M4,72.9c0,0.1,0,0.1,0.1,0.2c0.1,0.2,0.7,0.3,1,0.3c0.4,0.1,0.7,0.2,1.1,0.2c0.2,0,0.5,0.1,0.7,0.2
			c0.7,0.3,0.9,0.8,0.9,1.3c0,0.4-0.1,0.7-0.3,0.9c-0.4,0.7-1.3,1-2.8,1c-0.5,0-1.6,0-2.2-0.6c-0.5-0.4-0.5-0.9-0.6-1.1l1.9-0.1
			c0.1,0.3,0.4,0.6,1.2,0.6c0.2,0,0.5,0,0.7-0.1c0.1-0.1,0.3-0.2,0.3-0.4c0-0.3-0.2-0.4-0.4-0.4c-0.2-0.1-1.4-0.3-1.7-0.3
			c-0.2,0-0.4-0.1-0.6-0.2C3,74.3,2.2,74,2.2,73.1c0-0.4,0.2-0.8,0.4-1c0.6-0.6,1.7-0.7,2.3-0.7c0.6,0,1.3,0.1,1.6,0.2
			c1,0.3,1.1,1.1,1.2,1.4L5.8,73c0-0.2-0.1-0.5-0.9-0.5C4.6,72.5,4,72.5,4,72.9"/>
		<path class="st208" d="M8.2,71.6h1.9v3c0,0.3,0,0.6,0.2,0.8c0.2,0.4,0.7,0.5,1.1,0.5c0.5,0,0.8-0.2,0.9-0.3c0.3-0.3,0.3-0.8,0.3-1
			v-3h1.9v2.8c0,0.6,0,1.3-0.3,1.7c-0.6,0.9-2,1-2.8,1c-1.2,0-1.9-0.2-2.3-0.4c-0.8-0.4-0.9-1.3-0.9-1.7V71.6z"/>
		<path class="st208" d="M17.1,72.8h1.4c0.2,0,0.3,0,0.5,0.1c0.1,0.1,0.2,0.2,0.2,0.4c0,0.5-0.5,0.5-0.8,0.5h-1.2V72.8z M15.2,76.9
			h1.9V75h1.4c0.9,0,1.7,0,2.2-0.7c0.3-0.3,0.4-0.7,0.4-1.1c0-0.7-0.3-1.1-0.5-1.3c-0.4-0.4-1-0.4-1.7-0.4h-3.6V76.9z"/>
		<polygon class="st208" points="27.2,71.6 27.2,72.8 23.5,72.8 23.5,73.6 26.9,73.6 26.9,74.9 23.5,74.9 23.5,75.7 27.2,75.7 
			27.2,76.9 21.6,76.9 21.6,71.6 		"/>
		<path class="st208" d="M29.8,72.8h1.6c0.1,0,0.3,0,0.5,0.2c0.1,0.1,0.1,0.2,0.1,0.3c0,0.5-0.5,0.5-0.8,0.5h-1.4V72.8z M27.9,76.9
			h1.9v-2h1l1.1,2H34l-1.3-2.1c0.2-0.1,0.6-0.1,0.9-0.5c0.2-0.3,0.3-0.6,0.3-1c0-0.5-0.2-1.1-0.6-1.4c-0.4-0.3-1-0.3-1.5-0.3h-3.9
			V76.9z"/>
		<path class="st208" d="M38,74.2c0,0.8,0.5,1.7,1.6,1.7c0.4,0,0.8-0.1,1.1-0.4c0.2-0.2,0.3-0.4,0.4-0.5l1.8,0.2
			c-0.1,0.2-0.1,0.4-0.3,0.7c-0.6,1-1.8,1.2-3,1.2c-0.5,0-1,0-1.5-0.2c-1.1-0.3-2-1.2-2-2.7c0-1,0.5-2.9,3.6-2.9
			c2.6,0,3,1.2,3.2,1.7L41,73.3c-0.1-0.2-0.2-0.4-0.4-0.5c-0.3-0.2-0.6-0.2-0.9-0.2C38.7,72.6,38,73.1,38,74.2"/>
		<path class="st208" d="M43.2,71.6h1.9v3c0,0.3,0,0.6,0.2,0.8c0.2,0.4,0.7,0.5,1.1,0.5c0.5,0,0.8-0.2,0.9-0.3
			c0.3-0.3,0.3-0.8,0.3-1v-3h1.9v2.8c0,0.6,0,1.3-0.3,1.7c-0.6,0.9-2,1-2.8,1c-1.2,0-1.9-0.2-2.3-0.4c-0.8-0.4-0.9-1.3-0.9-1.7V71.6
			z"/>
		<path class="st208" d="M52.1,72.8h1.4c0.2,0,0.3,0,0.5,0.1c0.1,0.1,0.2,0.2,0.2,0.4c0,0.5-0.5,0.5-0.8,0.5h-1.2V72.8z M50.2,76.9
			h1.9V75h1.4c0.9,0,1.7,0,2.2-0.7c0.3-0.3,0.4-0.7,0.4-1.1c0-0.7-0.3-1.1-0.5-1.3c-0.4-0.4-1-0.4-1.7-0.4h-3.6V76.9z"/>
		<path class="st208" d="M63.2,76.9h-5.3v-1.1c0.2-0.4,0.4-0.8,1.4-1.4c0.2-0.1,0.3-0.2,1.3-0.6c0.5-0.3,0.8-0.4,0.8-0.7
			c0-0.5-0.6-0.5-0.8-0.5c-0.2,0-0.4,0-0.6,0.1c-0.3,0.2-0.3,0.5-0.3,0.7l-1.6,0c0-0.4,0.1-0.8,0.3-1.2c0.3-0.5,0.8-0.8,2.4-0.8
			c1.1,0,1.7,0.2,2.1,0.6c0.2,0.3,0.3,0.6,0.3,0.9c0,0.8-0.6,1.2-1.2,1.5c-0.3,0.2-0.6,0.3-0.9,0.4c-0.2,0.1-0.8,0.4-1.2,0.7
			c-0.1,0-0.1,0.1-0.1,0.1c0.1,0,0.1,0,0.2,0c0.1,0,0.2,0,0.2,0h3V76.9z"/>
		<path class="st208" d="M66.7,72.6c0.5,0,0.8,0.2,0.9,0.5c0.2,0.3,0.3,0.8,0.3,1.1c0,0.3-0.1,0.8-0.3,1.2c-0.3,0.4-0.6,0.4-0.9,0.4
			c-1,0-1.1-0.9-1.1-1.2c0-0.1,0-0.3,0-0.4c0-0.5,0.1-0.9,0.3-1.1C66.1,72.7,66.4,72.6,66.7,72.6 M66.8,71.4c-1.4,0-2.7,0.4-3,1.9
			c-0.1,0.3-0.1,0.6-0.1,0.9c0,0.7,0.1,1.7,1.1,2.3c0.7,0.5,1.5,0.5,2,0.5c1.7,0,3-0.8,3-2.8c0-0.7-0.1-1.5-0.8-2.2
			C68.4,71.6,67.7,71.4,66.8,71.4"/>
		<path class="st208" d="M75.6,76.9h-5.3v-1.1c0.2-0.4,0.4-0.8,1.4-1.4c0.2-0.1,0.3-0.2,1.3-0.6c0.5-0.3,0.8-0.4,0.8-0.7
			c0-0.5-0.6-0.5-0.8-0.5c-0.2,0-0.4,0-0.6,0.1C72,72.9,72,73.2,72,73.4l-1.6,0c0-0.4,0.1-0.8,0.3-1.2c0.3-0.5,0.8-0.8,2.4-0.8
			c1.1,0,1.7,0.2,2.1,0.6c0.2,0.3,0.3,0.6,0.3,0.9c0,0.8-0.6,1.2-1.2,1.5c-0.3,0.2-0.6,0.3-0.9,0.4c-0.2,0.1-0.8,0.4-1.2,0.7
			c-0.1,0-0.1,0.1-0.1,0.1c0.1,0,0.1,0,0.2,0c0.1,0,0.2,0,0.2,0h3V76.9z"/>
		<path class="st208" d="M79.5,74.7h-1.6l1.6-1.7V74.7z M76.2,74.8v1.2h3.3v1h1.7v-1h0.9v-1.3h-0.9v-3.1h-2L76.2,74.8z"/>
	</g>
</g>
</svg>
