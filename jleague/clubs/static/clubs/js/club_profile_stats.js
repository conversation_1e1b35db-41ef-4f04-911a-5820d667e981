const clubStatsSection = (function () {
  const CSS_SELECTORS = {
    CLUB_STATS_CONTAINER: '.club-profile-stats__club-stats',
    PLAYER_STATS_CONTAINER: '.club-profile-stats__player-stats',
    SECTION_CONTAINER: '#statistics',
    CONTENT_CONTAINER: '.club-profile-stats__content',
    YEAR_SELECTOR: '.club-profile-stats__filters__year-selector',
  };

  let clubStatsContainer;
  let playerStatsContainer;
  let statsWrapper;
  let yearSelector;
  let isLoaded = false;
  let isLoading = false;

  function _fetchClubStats() {
    const url = `/clubs/${selectedTeam}/stats/${statsSelectedYear}/`;
    return ajaxRequest(url)
      .then((result) => {
        clubStatsContainer.empty();
        if (result.trim() !== '') {
          clubStatsContainer.html(result);
        }
      })
      .catch((error) => {
        console.error(error);
      });
  }

  function _fetchPlayerStats() {
    const url = `/clubs/${selectedTeam}/players/stats/${statsSelectedYear}/`;
    return ajaxRequest(url)
      .then((result) => {
        playerStatsContainer.empty();
        if (result.trim() !== '') {
          playerStatsContainer.html(result);
        }
      })
      .catch((error) => {
        console.error(error);
      });
  }

  function fetchStats() {
    yearSelector.attr('disabled', true);
    statsWrapper.addClass('loading');

    const promises = [];
    promises.push(_fetchClubStats());
    promises.push(_fetchPlayerStats());

    return Promise.all(promises).then(() => {
      statsWrapper.removeClass('loading');
      yearSelector.removeAttr('disabled');
    });
  }

  function _onFilterChange() {
    statsSelectedYear = yearSelector.val();
    fetchStats();
  }

  function _checkStatsSection() {
    if (isLoaded) return;

    if (isScrolledIntoView(CSS_SELECTORS.SECTION_CONTAINER, 10)) {
      isLoaded = true;
      if (!isLoading) {
        isLoading = true;
        fetchStats();
      }
    }
  }

  function init() {
    clubStatsContainer = $(CSS_SELECTORS.CLUB_STATS_CONTAINER);
    playerStatsContainer = $(CSS_SELECTORS.PLAYER_STATS_CONTAINER);
    statsWrapper = $(CSS_SELECTORS.CONTENT_CONTAINER);
    yearSelector = $(CSS_SELECTORS.YEAR_SELECTOR);
    if (yearSelector) yearSelector.change(_onFilterChange);

    $(window).scroll(_checkStatsSection);
  }

  return { init };
})();
