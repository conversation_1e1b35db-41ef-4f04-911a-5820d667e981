from django.conf import settings
from django.core.cache import cache
from django.db import models
from django.db.models import Field, Lookup
from django_jsonform.models.fields import <PERSON><PERSON><PERSON><PERSON>
from modelcluster.fields import <PERSON>rent<PERSON><PERSON><PERSON>
from modelcluster.models import ClusterableModel
from wagtail.admin.panels import FieldPanel, MultiFieldPanel, InlinePanel
from wagtail.models import Orderable
from wagtail.search import index
from wagtail.snippets.models import register_snippet
from .constants import TRANSLATION_VALUES_SCHEMA
from .enums import SeasonEnum, SeasonTitleEnum, GameStageName, GameStageKey


@Field.register_lookup
class NotEqualLookup(Lookup):
    lookup_name = "ne"

    def as_sql(self, compiler, connection):
        lhs, lhs_params = self.process_lhs(compiler, connection)
        rhs, rhs_params = self.process_rhs(compiler, connection)
        params = lhs_params + rhs_params
        return "%s <> %s" % (lhs, rhs), params


@register_snippet
class Country(models.Model):
    name = models.CharField(max_length=255)
    two_letter_code = models.CharField(max_length=2, db_index=True)
    three_letter_code = models.CharField(max_length=3, unique=True, db_index=True)
    local_names = JSONField(schema=TRANSLATION_VALUES_SCHEMA, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    fifa_code = models.CharField(max_length=3, unique=True, db_index=True, null=True)

    panels = [
        FieldPanel("name"),
        FieldPanel("two_letter_code"),
        FieldPanel("three_letter_code"),
        FieldPanel("local_names"),
        FieldPanel("fifa_code"),
    ]

    class Meta:
        ordering = ["name"]
        verbose_name_plural = "Countries"

    def __str__(self):
        return self.name

    autocomplete_search_field = "name"

    def autocomplete_label(self):
        return self.name

    countries = None
    country2LC = None
    country3LC = None
    countryNames = None
    countryFifaCode = None

    @staticmethod
    def update_countries_list():
        countries = list(Country.objects.all())
        Country.countries = countries
        Country.country2LC = {c.two_letter_code: c for c in countries}
        Country.country3LC = {c.three_letter_code: c for c in countries}
        Country.countryNames = {c.name: c for c in countries}
        Country.countryFifaCode = {c.fifa_code: c for c in countries if c.fifa_code is not None}


@register_snippet
class SeasonSetting(models.Model):
    label = models.CharField(max_length=255)
    is_off_season = models.BooleanField()
    next_season_started_at = models.DateTimeField()
    updated_at = models.DateTimeField(auto_now=True)

    panels = [
        FieldPanel("label"),
        FieldPanel("is_off_season"),
        FieldPanel("next_season_started_at"),
    ]

    def __str__(self) -> str:
        return self.label


@register_snippet
class Competition(models.Model):
    """
    source file: mst-gamekind.xml
    old model: data_stadium.GameKind
    """

    name = models.CharField(max_length=255)
    name_short = models.CharField(max_length=255)
    slug = models.SlugField(unique=True, db_index=True)
    category_slug = models.SlugField(blank=True, null=True)
    t_name = JSONField(schema=TRANSLATION_VALUES_SCHEMA, blank=True, null=True)
    t_name_short = JSONField(schema=TRANSLATION_VALUES_SCHEMA, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    search_fields = [
        index.SearchField("name", partial_match=True),
    ]

    panels = [
        FieldPanel("name"),
        FieldPanel("name_short"),
        FieldPanel("slug"),
        FieldPanel("t_name"),
        FieldPanel("t_name_short"),
    ]

    class Meta:
        ordering = ["name"]

    def __str__(self):
        return f"{self.name} <id={self.id};slug={self.slug}>"

    def format(self):
        return self.__dict__

    def get_local_name(self, lang="en"):
        return self.t_name.get(lang)

    competitions = None

    @staticmethod
    def update_competitions_list():
        Competition.competitions = list(Competition.objects.all())


@register_snippet
class CompetitionSystem(models.Model):

    STAGE_SCHEMA = {
        "type": "array",
        "items": {
            "type": "object",
            "keys": {
                "season_id": {
                    "type": "number",
                    "required": True,
                    "choices": [
                        {
                            "title": f"{SeasonEnum.UNDEFINED} ({SeasonTitleEnum.UNDEFINED.value})",
                            "value": SeasonEnum.UNDEFINED,
                        },
                        {
                            "title": f"{SeasonEnum.SEASON_TOTAL} ({SeasonTitleEnum.SEASON_TOTAL.value})",
                            "value": SeasonEnum.SEASON_TOTAL,
                        },
                        {
                            "title": f"{SeasonEnum.FIRST_STAGE} ({SeasonTitleEnum.FIRST_STAGE.value})",
                            "value": SeasonEnum.FIRST_STAGE,
                        },
                        {
                            "title": f"{SeasonEnum.SECOND_STAGE} ({SeasonTitleEnum.SECOND_STAGE.value})",
                            "value": SeasonEnum.SECOND_STAGE,
                        },
                        {
                            "title": f"{SeasonEnum.GROUP_STAGE} ({SeasonTitleEnum.GROUP_STAGE.value})",
                            "value": SeasonEnum.GROUP_STAGE,
                        },
                        {
                            "title": f"{SeasonEnum.KNOCKOUT_STAGE} ({SeasonTitleEnum.KNOCKOUT_STAGE.value})",
                            "value": SeasonEnum.KNOCKOUT_STAGE,
                        },
                        {
                            "title": f"{SeasonEnum.RANKING_DECISION} ({SeasonTitleEnum.RANKING_DECISION.value})",
                            "value": SeasonEnum.RANKING_DECISION,
                        },
                        {
                            "title": f"{SeasonEnum.SECOND_ROUND} ({SeasonTitleEnum.SECOND_ROUND.value})",
                            "value": SeasonEnum.SECOND_ROUND,
                        },
                        {
                            "title": f"{SeasonEnum.FINAL_ROUND} ({SeasonTitleEnum.FINAL_ROUND.value})",
                            "value": SeasonEnum.FINAL_ROUND,
                        },
                        {
                            "title": f"{SeasonEnum.FIRST_ROUND} ({SeasonTitleEnum.FIRST_ROUND.value})",
                            "value": SeasonEnum.FIRST_ROUND,
                        },
                        {
                            "title": f"{SeasonEnum.POST_SEASON} ({SeasonTitleEnum.POST_SEASON.value})",
                            "value": SeasonEnum.POST_SEASON,
                        },
                        {
                            "title": f"{SeasonEnum.ASIA_FINAL_ROUND} ({SeasonTitleEnum.ASIA_FINAL_ROUND.value})",
                            "value": SeasonEnum.ASIA_FINAL_ROUND,
                        },
                        {
                            "title": f"{SeasonEnum.THIRD_ROUND} ({SeasonTitleEnum.THIRD_ROUND.value})",
                            "value": SeasonEnum.THIRD_ROUND,
                        },
                        {
                            "title": f"{SeasonEnum.PLAYOFF_STAGE} ({SeasonTitleEnum.PLAYOFF_STAGE.value})",
                            "value": SeasonEnum.PLAYOFF_STAGE,
                        },
                        {
                            "title": f"{SeasonEnum.FOURTH_ROUND} ({SeasonTitleEnum.FOURTH_ROUND.value})",
                            "value": SeasonEnum.FOURTH_ROUND,
                        },
                        {
                            "title": f"{SeasonEnum.FIRST_ROUND_2} ({SeasonTitleEnum.FIRST_ROUND_2.value})",
                            "value": SeasonEnum.FIRST_ROUND_2,
                        },
                        {
                            "title": f"{SeasonEnum.PLAYOFF_ROUND} ({SeasonTitleEnum.PLAYOFF_ROUND.value})",
                            "value": SeasonEnum.PLAYOFF_ROUND,
                        },
                        {
                            "title": f"{SeasonEnum.PRIME_ROUND} ({SeasonTitleEnum.PRIME_ROUND.value})",
                            "value": SeasonEnum.PRIME_ROUND,
                        },
                    ],
                },
                "stage": {
                    "type": "string",
                    "required": True,
                    "choices": [
                        {
                            "title": GameStageName.FIRST_ROUND.value,
                            "value": GameStageKey.FIRST_ROUND.value,
                        },
                        {
                            "title": GameStageName.SECOND_ROUND.value,
                            "value": GameStageKey.SECOND_ROUND.value,
                        },
                        {
                            "title": GameStageName.THIRD_ROUND.value,
                            "value": GameStageKey.THIRD_ROUND.value,
                        },
                        {
                            "title": GameStageName.FOURTH_ROUND.value,
                            "value": GameStageKey.FOURTH_ROUND.value,
                        },
                        {
                            "title": GameStageName.GROUP_STAGE.value,
                            "value": GameStageKey.GROUP_STAGE.value,
                        },
                        {
                            "title": GameStageName.PLAYOFF.value,
                            "value": GameStageKey.PLAYOFF.value,
                        },
                        {
                            "title": GameStageName.ROUND_16.value,
                            "value": GameStageKey.ROUND_16.value,
                        },
                        {
                            "title": GameStageName.QUARTER_FINAL.value,
                            "value": GameStageKey.QUARTER_FINAL.value,
                        },
                        {
                            "title": GameStageName.SEMI_FINAL.value,
                            "value": GameStageKey.SEMI_FINAL.value,
                        },
                        {
                            "title": GameStageName.FINAL.value,
                            "value": GameStageKey.FINAL.value,
                        },
                    ],
                },
                "games_count": {
                    "type": "number",
                    "minimum": "1",
                },
                "stages_count": {
                    "type": "number",
                    "minimum": "1",
                },
            },
        },
    }

    title = models.CharField(max_length=255)
    competition = models.ForeignKey(Competition, null=True, on_delete=models.SET_NULL)
    year = models.IntegerField()
    stages = JSONField(schema=STAGE_SCHEMA)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    search_fields = [
        index.SearchField("name", partial_match=True),
    ]

    panels = [
        # FieldPanel("title"),
        FieldPanel("competition"),
        FieldPanel("year"),
        FieldPanel("stages"),
    ]

    def __str__(self):
        return f"{self.title}"

    def format(self):
        return self.__dict__

    def save(self):
        name = self.competition.name if self.competition else self.competition_id
        self.title = f"{self.year} - {name}"
        super().save()


@register_snippet
class Formation(models.Model):
    """
    source file: live_formation.xml
    old model: data_stadium.TeamFormation
    """

    name = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    search_fields = [
        index.SearchField("name", partial_match=True),
    ]

    panels = [
        FieldPanel("name"),
    ]

    def __str__(self):
        return self.name

    def format(self):
        return self.__dict__


class IPLocation(models.Model):
    IP_TYPES = [
        ("IPv4", "IPv4"),
        ("IPv6", "IPv6"),
    ]
    ip_address = models.GenericIPAddressField(unique=True, db_index=True)
    ip_type = models.CharField(max_length=255, choices=IP_TYPES)
    country = models.ForeignKey(
        Country,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class PartnerGroup(ClusterableModel):
    name = models.CharField(max_length=255)
    is_active = models.BooleanField(default=False)
    row = models.IntegerField(default=1)
    column = models.IntegerField(default=1)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    panels = [
        FieldPanel("name"),
        FieldPanel("row"),
        FieldPanel("column"),
        FieldPanel("is_active"),
        MultiFieldPanel(
            [
                InlinePanel("headers"),
            ],
            heading="Headers",
        ),
        MultiFieldPanel(
            [
                InlinePanel("partners"),
            ],
            heading="Partners",
        ),
    ]

    def __str__(self):
        return self.name


class PartnerGroupHeader(Orderable):
    partner_group = ParentalKey(
        PartnerGroup,
        related_name="headers",
        on_delete=models.CASCADE,
    )
    slug = models.SlugField(unique=True)
    image = models.ForeignKey(
        "wagtailimages.Image",
        related_name="+",
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    panels = [FieldPanel("slug"), FieldPanel("image")]

    def __str__(self):
        return self.slug


class Partner(Orderable):
    partner_group = ParentalKey(
        PartnerGroup,
        related_name="partners",
        null=True,
        on_delete=models.SET_NULL,
    )
    name = models.CharField(max_length=255)
    slug = models.SlugField(unique=True)
    website = models.URLField(null=True, blank=True)
    image = models.ForeignKey(
        "wagtailimages.Image",
        related_name="+",
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    is_active = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    panels = [
        FieldPanel("name"),
        FieldPanel("slug"),
        FieldPanel("website"),
        FieldPanel("image"),
        FieldPanel("partner_group"),
        FieldPanel("is_active"),
    ]

    def __str__(self):
        return self.name


class AlertLog(models.Model):
    LOG_LEVELS = [
        ("error", "ERROR"),
        ("warning", "WARNING"),
        ("info", "INFO"),
        ("debug", "DEBUG"),
    ]

    CONTENT_TYPES = [
        ("club", "Club"),
        ("player", "Player"),
        ("game", "Game"),
        ("stat", "Stat"),
    ]

    ALERT_CHANNELS = [
        ("slack", "Slack"),
        ("email", "Email"),
    ]

    message = models.TextField()
    level = models.CharField(max_length=30, choices=LOG_LEVELS)
    related_content = models.CharField(max_length=255, choices=CONTENT_TYPES)
    alert_channel = models.CharField(max_length=30, choices=ALERT_CHANNELS)
    created_at = models.DateTimeField(auto_now_add=True)

    panels = [
        FieldPanel("message"),
        FieldPanel("level"),
        FieldPanel("related_content"),
        FieldPanel("alert_channel"),
    ]

    def __str__(self):
        return self.message


class NavigationLink(Orderable):
    """The abstract model for navigation items, complete with panels"""

    label = models.CharField(max_length=255)
    link_external = models.URLField("External link", blank=True, null=True)
    link_page = models.ForeignKey(
        "wagtailcore.Page",
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="+",
        verbose_name="Page link",
    )
    django_page = models.SlugField(max_length=255, blank=True, null=True)
    django_page_params = models.JSONField(default=dict, blank=True, null=True)
    translate_key = models.CharField(max_length=255, blank=True, null=True)
    target_blank = models.BooleanField(verbose_name="Open in a new tab", default=False)
    bot_no_follow = models.BooleanField(
        verbose_name="Crawler bot will not follow",
        default=False,
    )
    parent = ParentalKey(
        "core.NavigationGroup",
        on_delete=models.CASCADE,
        related_name="navigation_links",
    )
    is_active = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    panels = [
        FieldPanel("label"),
        FieldPanel("translate_key"),
        FieldPanel("link_page"),
        FieldPanel("link_external"),
        FieldPanel("django_page"),
        FieldPanel("django_page_params"),
        FieldPanel("target_blank"),
        FieldPanel("bot_no_follow"),
    ]

    def __str__(self):
        return self.label


class NavigationGroup(ClusterableModel):
    title = models.CharField(max_length=255)
    translate_key = models.CharField(max_length=255, blank=True, null=True)
    is_active = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    panels = [
        FieldPanel("title"),
        FieldPanel("translate_key"),
        InlinePanel(
            "navigation_links", heading="Navigation Links", label="Navigation Link"
        ),
    ]

    def __str__(self):
        return self.title


class Translation(models.Model):
    """Translation for static pages"""

    key = models.CharField(
        max_length=255,
        unique=True,
        db_index=True,
        help_text="Uppercase without spacing and any special letters (only underscore is allowed)",
    )
    values = JSONField(schema=TRANSLATION_VALUES_SCHEMA)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    panels = [
        FieldPanel("key"),
        FieldPanel("values"),
        FieldPanel("is_active"),
    ]

    translations = None

    @staticmethod
    def update_translations_list():
        all_translations = Translation.objects.filter(is_active=True)
        Translation.translations = {}
        for t in all_translations:
            for code, _ in settings.LANGUAGES:
                if not Translation.translations.get(code):
                    Translation.translations[code] = {}
                if t.values.get(code):
                    Translation.translations[code][t.key] = t.values[code]
                elif t.values.get("en"):
                    Translation.translations[code][t.key] = t.values["en"]

    def __str__(self):
        return self.key

    def save(self, *args, **kwargs):
        super(Translation, self).save(*args, **kwargs)
        Translation.update_translations_list()


class InfluencerLog(models.Model):
    influencer = models.CharField(max_length=255)
    sns_link = models.CharField(max_length=30)
    country = models.CharField(max_length=255)
    timezone = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)

    panels = [
        FieldPanel("influencer"),
        FieldPanel("sns_link"),
        FieldPanel("country"),
        FieldPanel("timezone"),
    ]

    def __str__(self):
        return f"{self.influencer}|{self.sns_link}"


class Banner(models.Model):
    POSITIONS = [
        ("top", "Top"),
        ("middle", "Middle"),
        ("bottom", "Bottom"),
    ]
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    sort_order = models.IntegerField(default=999)
    position = models.CharField(max_length=20, choices=POSITIONS)
    language = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        choices=settings.LANGUAGES,
        help_text="Display only in this language if selected",
    )
    lg_image = models.ForeignKey(
        "wagtailimages.Image",
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        related_name="+",
        help_text="Banner size 1200x200px",
    )
    md_image = models.ForeignKey(
        "wagtailimages.Image",
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        related_name="+",
        help_text="Banner size 688x200px",
    )
    sm_image = models.ForeignKey(
        "wagtailimages.Image",
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        related_name="+",
        help_text="Banner size 420x200px",
    )
    target_url = models.URLField(blank=True, null=True)
    is_external_link = models.BooleanField(default=True)
    is_active = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    start_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text="This picker uses Asia/Tokyo timezone",
    )
    ended_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text="This picker uses Asia/Tokyo timezone",
    )

    panels = [
        FieldPanel("title"),
        FieldPanel("description"),
        FieldPanel("sort_order"),
        # FieldPanel("lg_image"),
        FieldPanel("md_image"),
        FieldPanel("sm_image"),
        FieldPanel("language"),
        FieldPanel("position"),
        FieldPanel("target_url"),
        FieldPanel("is_external_link"),
        FieldPanel("start_at"),
        FieldPanel("ended_at"),
        FieldPanel("is_active"),
    ]

    def save(self, *args, **kwargs):
        super(Banner, self).save(*args, **kwargs)
        cache.delete("display_banners")
