{% extends 'wagtailadmin/base.html' %}

{% load i18n wagtailimages_tags %}

{% block titletag %}
  {% trans 'Upload Staff Diecut Photos' %}
{% endblock %}

{% block content %}
  {% trans 'Upload Staff Diecut Photos' as title %}
  {% include 'wagtailadmin/shared/header.html' with title=title icon='image' %}

  <div class="nice-padding">
    <section>
      <h2>Upload</h2>

      <form
        action="{% url 'club_staff_diecut_upload' %}"
        method="POST"
        enctype="multipart/form-data"
      >
        {% csrf_token %}

        {{ form }}

        <input type="submit" value="upload" class="button" style="margin-top: 15px;" />
      </form>

      <div class="help-block help-info">
        <p>
          The zip file structure must match the tree below.
        </p>

        <small>
          *recommended to not upload more than 300~400 images each time, to avoid timeout issue*
        </small>

        <ul class="listing">
          <li>
            <div class="row row-flush">
              <div class="col6 title">
                /
              </div>

              <small class="col6">
                ......(root)
              </small>
            </div>
          </li>

          <li>
            <div class="row row-flush">
              <div class="col6">
                |-> 700666.jpg
              </div>

              <small class="col6">
                ......(file name = id of staff from DS)
              </small>
            </div>
          </li>

          <li>
            <div class="row row-flush">
              <div class="col6">
                |-> 603000.jpg
              </div>

              <small class="col6">
                ......(file name = id of staff from DS)
              </small>
            </div>
          </li>

          <li>
            <div class="row row-flush">
              <div class="col6">
                |-> 526.jpg
              </div>

              <small class="col6">
                ......(file name = id of staff from DS)
              </small>
            </div>
          </li>

          <li>
            <div class="row row-flush">
              <div class="col6">
                |-> ...
              </div>

              <small class="col6">
              </small>
            </div>
          </li>
        </ul>
      </div>
    </section>

    <section>
      <h2>Recent upload</h2>

      {% if recent_images and recent_images|length > 0 %}
        <ul class="listing horiz images">
          {% for recent_image in recent_images %}
            <li>
              {% image recent_image max-165x165 as diecut_image %}

              <figure>
                <div class="image">
                  <img
                    class="show-transparency"
                    src="{{ diecut_image.url }}"
                    height="165"
                    width="110"
                  />
                </div>


                <figcaption>
                  {{ recent_image.title }}
                </figcaption>
            </figure>
            </li>
          {% endfor %}
        </ul>
      {% endif %}
    </section>
  </div>
{% endblock %}
