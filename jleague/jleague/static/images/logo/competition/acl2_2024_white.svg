<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 260 80">
  <defs>
    <style>
      .cls-1 {
        fill: url(#radial-gradient-7);
      }

      .cls-2 {
        fill: url(#radial-gradient-6);
      }

      .cls-3 {
        fill: url(#radial-gradient-5);
      }

      .cls-4 {
        fill: url(#radial-gradient-4);
      }

      .cls-5 {
        fill: url(#radial-gradient-9);
      }

      .cls-6 {
        fill: url(#radial-gradient-8);
      }

      .cls-7 {
        fill: url(#radial-gradient-3);
      }

      .cls-8 {
        fill: url(#radial-gradient-2);
      }

      .cls-9 {
        fill: url(#linear-gradient-8);
      }

      .cls-10 {
        fill: url(#linear-gradient-7);
      }

      .cls-11 {
        fill: url(#linear-gradient-5);
      }

      .cls-12 {
        fill: url(#linear-gradient-6);
      }

      .cls-13 {
        fill: url(#linear-gradient-9);
      }

      .cls-14 {
        fill: url(#linear-gradient-4);
      }

      .cls-15 {
        fill: url(#linear-gradient-3);
      }

      .cls-16 {
        fill: url(#linear-gradient-2);
      }

      .cls-17 {
        fill: url(#radial-gradient);
      }

      .cls-18 {
        fill: url(#linear-gradient);
      }

      .cls-19 {
        fill: #00a8e1;
      }

      .cls-20 {
        fill: url(#radial-gradient-12);
      }

      .cls-21 {
        fill: url(#radial-gradient-14);
      }

      .cls-22 {
        fill: url(#radial-gradient-13);
      }

      .cls-23 {
        fill: url(#radial-gradient-10);
      }

      .cls-24 {
        fill: url(#radial-gradient-11);
      }

      .cls-25 {
        fill: #fff;
      }

      .cls-26 {
        fill: url(#linear-gradient-34);
      }

      .cls-26, .cls-27, .cls-28, .cls-29, .cls-30, .cls-31, .cls-32, .cls-33 {
        opacity: .9;
      }

      .cls-27 {
        fill: url(#linear-gradient-35);
      }

      .cls-28 {
        fill: url(#linear-gradient-33);
      }

      .cls-29 {
        fill: url(#linear-gradient-17);
      }

      .cls-30 {
        fill: url(#linear-gradient-32);
      }

      .cls-31 {
        fill: url(#linear-gradient-22);
      }

      .cls-32 {
        fill: url(#linear-gradient-40);
      }

      .cls-33 {
        fill: url(#linear-gradient-41);
      }

      .cls-34 {
        fill: url(#linear-gradient-21);
      }

      .cls-34, .cls-35 {
        opacity: .8;
      }

      .cls-35 {
        fill: url(#linear-gradient-23);
      }

      .cls-36 {
        fill: url(#linear-gradient-36);
      }

      .cls-36, .cls-37 {
        opacity: .4;
      }

      .cls-37 {
        fill: url(#linear-gradient-37);
      }

      .cls-38 {
        fill: url(#linear-gradient-42);
      }

      .cls-39 {
        fill: url(#linear-gradient-10);
      }

      .cls-40 {
        fill: url(#linear-gradient-11);
      }

      .cls-41 {
        fill: url(#linear-gradient-12);
      }

      .cls-42 {
        fill: url(#linear-gradient-13);
      }

      .cls-43 {
        fill: url(#linear-gradient-19);
      }

      .cls-44 {
        fill: url(#linear-gradient-14);
      }

      .cls-45 {
        fill: url(#linear-gradient-20);
      }

      .cls-46 {
        fill: url(#linear-gradient-28);
      }

      .cls-47 {
        fill: url(#linear-gradient-25);
      }

      .cls-48 {
        fill: url(#linear-gradient-31);
      }

      .cls-49 {
        fill: url(#linear-gradient-30);
      }

      .cls-50 {
        fill: url(#linear-gradient-15);
      }

      .cls-51 {
        fill: url(#linear-gradient-16);
      }

      .cls-52 {
        fill: url(#linear-gradient-18);
      }

      .cls-53 {
        fill: url(#linear-gradient-38);
      }

      .cls-54 {
        fill: url(#linear-gradient-27);
      }

      .cls-55 {
        fill: url(#linear-gradient-24);
      }

      .cls-56 {
        fill: url(#linear-gradient-26);
      }

      .cls-57 {
        fill: url(#linear-gradient-29);
      }

      .cls-58 {
        fill: url(#linear-gradient-39);
      }
    </style>
    <radialGradient id="radial-gradient" cx="-10405.11" cy="15.57" fx="-10405.11" fy="15.57" r="42.17" gradientTransform="translate(-10062.61) rotate(-180) scale(.97 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".32" stop-color="#cdd5ec"/>
      <stop offset=".33" stop-color="#cad3eb"/>
      <stop offset=".43" stop-color="#7facd8"/>
      <stop offset=".5" stop-color="#5093cd"/>
      <stop offset=".53" stop-color="#3f8ac9"/>
      <stop offset=".54" stop-color="#3e84c4"/>
      <stop offset=".64" stop-color="#395aa7"/>
      <stop offset=".84" stop-color="#05054c"/>
    </radialGradient>
    <radialGradient id="radial-gradient-2" cx="-10405.81" cy="14.64" fx="-10405.81" fy="14.64" r="47.22" gradientTransform="translate(-10062.61) rotate(-180) scale(.97 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".41" stop-color="#cdd5ec"/>
      <stop offset=".41" stop-color="#cad3eb"/>
      <stop offset=".47" stop-color="#7facd8"/>
      <stop offset=".51" stop-color="#5093cd"/>
      <stop offset=".53" stop-color="#3f8ac9"/>
      <stop offset=".54" stop-color="#3e84c4"/>
      <stop offset=".64" stop-color="#395aa7"/>
      <stop offset=".84" stop-color="#05054c"/>
    </radialGradient>
    <radialGradient id="radial-gradient-3" cx="-10425.26" cy="7.05" fx="-10425.26" fy="7.05" r="26.03" gradientTransform="translate(-10062.61) rotate(-180) scale(.97 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".32" stop-color="#cdd5ec"/>
      <stop offset=".33" stop-color="#cad3eb"/>
      <stop offset=".44" stop-color="#7facd8"/>
      <stop offset=".51" stop-color="#5093cd"/>
      <stop offset=".55" stop-color="#3f8ac9"/>
      <stop offset=".56" stop-color="#3e86c6"/>
      <stop offset=".68" stop-color="#365ca9"/>
      <stop offset=".84" stop-color="#06044c"/>
    </radialGradient>
    <radialGradient id="radial-gradient-4" cx="-10416.21" cy="8.66" fx="-10416.21" fy="8.66" r="35.86" gradientTransform="translate(-10062.61) rotate(-180) scale(.97 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".44" stop-color="#cdd5ec"/>
      <stop offset=".44" stop-color="#cad3eb"/>
      <stop offset=".5" stop-color="#7facd8"/>
      <stop offset=".54" stop-color="#5093cd"/>
      <stop offset=".56" stop-color="#3f8ac9"/>
      <stop offset=".57" stop-color="#3e84c4"/>
      <stop offset=".64" stop-color="#395aa7"/>
      <stop offset=".84" stop-color="#05054c"/>
    </radialGradient>
    <linearGradient id="linear-gradient" x1="-10279.08" y1="7.78" x2="-10283.83" y2="12.47" gradientTransform="translate(-10235.14) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".17" stop-color="#cdd5ec"/>
      <stop offset=".18" stop-color="#cad3eb"/>
      <stop offset=".35" stop-color="#7facd8"/>
      <stop offset=".48" stop-color="#5093cd"/>
      <stop offset=".54" stop-color="#3f8ac9"/>
      <stop offset=".57" stop-color="#3e84c4"/>
      <stop offset=".81" stop-color="#395aa7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="-10275.61" y1="28.52" x2="-10275.36" y2="-1.77" gradientTransform="translate(-10235.14) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".21" stop-color="#cdd5ec"/>
      <stop offset=".22" stop-color="#cad3eb"/>
      <stop offset=".34" stop-color="#7facd8"/>
      <stop offset=".42" stop-color="#5093cd"/>
      <stop offset=".46" stop-color="#3f8ac9"/>
      <stop offset=".5" stop-color="#3e84c4"/>
      <stop offset=".81" stop-color="#395aa7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="-10275.25" y1="37.15" x2="-10264.24" y2="-5.72" gradientTransform="translate(-10235.14) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".26" stop-color="#cdd5ec"/>
      <stop offset=".26" stop-color="#cad3eb"/>
      <stop offset=".38" stop-color="#7facd8"/>
      <stop offset=".46" stop-color="#5093cd"/>
      <stop offset=".51" stop-color="#3f8ac9"/>
      <stop offset=".53" stop-color="#3e84c4"/>
      <stop offset=".74" stop-color="#395aa7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="-10273.56" y1="49.88" x2="-10263.19" y2="-7.98" gradientTransform="translate(-10235.14) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".24" stop-color="#cdd5ec"/>
      <stop offset=".25" stop-color="#cad3eb"/>
      <stop offset=".37" stop-color="#7facd8"/>
      <stop offset=".46" stop-color="#5093cd"/>
      <stop offset=".51" stop-color="#3f8ac9"/>
      <stop offset=".53" stop-color="#3e84c4"/>
      <stop offset=".7" stop-color="#395aa7"/>
      <stop offset=".84" stop-color="#05054c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="38.88" y1="72.72" x2="11.45" y2="72.72" gradientUnits="userSpaceOnUse">
      <stop offset=".12" stop-color="#395aa7"/>
      <stop offset=".13" stop-color="#3961ac"/>
      <stop offset=".18" stop-color="#3c77bc"/>
      <stop offset=".23" stop-color="#3e85c5"/>
      <stop offset=".27" stop-color="#3f8ac9"/>
      <stop offset=".39" stop-color="#3f8ac9"/>
      <stop offset=".6" stop-color="#3f8ac9"/>
      <stop offset=".74" stop-color="#3f8ac9"/>
      <stop offset=".76" stop-color="#3e84c4"/>
      <stop offset=".9" stop-color="#395aa7"/>
    </linearGradient>
    <radialGradient id="radial-gradient-5" cx="215.43" cy="15.57" fx="215.43" fy="15.57" r="42.17" gradientTransform="translate(-172.53) scale(.97 1)" xlink:href="#radial-gradient"/>
    <radialGradient id="radial-gradient-6" cx="214.72" cy="14.64" fx="214.72" fy="14.64" r="47.22" gradientTransform="translate(-172.53) scale(.97 1)" xlink:href="#radial-gradient-2"/>
    <radialGradient id="radial-gradient-7" cx="195.28" cy="7.05" fx="195.28" fy="7.05" r="26.03" gradientTransform="translate(-172.53) scale(.97 1)" gradientUnits="userSpaceOnUse">
      <stop offset=".4" stop-color="#cdd5ec"/>
      <stop offset=".4" stop-color="#cad3eb"/>
      <stop offset=".47" stop-color="#7facd8"/>
      <stop offset=".52" stop-color="#5093cd"/>
      <stop offset=".55" stop-color="#3f8ac9"/>
      <stop offset=".56" stop-color="#3e86c6"/>
      <stop offset=".67" stop-color="#355caa"/>
      <stop offset=".84" stop-color="#06044c"/>
    </radialGradient>
    <radialGradient id="radial-gradient-8" cx="204.33" cy="8.66" fx="204.33" fy="8.66" r="35.86" gradientTransform="translate(-172.53) scale(.97 1)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-9" cx="171.75" cy="11.55" fx="171.75" fy="11.55" r="52.54" gradientTransform="translate(-172.53) scale(.97 1)" gradientUnits="userSpaceOnUse">
      <stop offset=".16" stop-color="#d0d3d3"/>
      <stop offset=".16" stop-color="#cdd0d2"/>
      <stop offset=".26" stop-color="#7d91bb"/>
      <stop offset=".32" stop-color="#4c69ac"/>
      <stop offset=".36" stop-color="#395aa7"/>
      <stop offset=".36" stop-color="#395aa7"/>
      <stop offset=".4" stop-color="#3b6fb6"/>
      <stop offset=".45" stop-color="#3d7ec0"/>
      <stop offset=".5" stop-color="#3e87c7"/>
      <stop offset=".58" stop-color="#3f8ac9"/>
      <stop offset=".76" stop-color="#172f73"/>
      <stop offset=".86" stop-color="#05054c"/>
    </radialGradient>
    <linearGradient id="linear-gradient-6" x1="20.5" y1="52.78" x2="29.76" y2="52.78" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#1c2472"/>
      <stop offset=".17" stop-color="#395aa7"/>
      <stop offset=".19" stop-color="#395ca8"/>
      <stop offset=".37" stop-color="#3a6db4"/>
      <stop offset=".5" stop-color="#3b74b9"/>
      <stop offset=".81" stop-color="#395aa7"/>
      <stop offset="1" stop-color="#1c2472"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="-2.81" y1="22.25" x2="25.15" y2="53.44" gradientUnits="userSpaceOnUse">
      <stop offset=".35" stop-color="#d0d3d3"/>
      <stop offset=".44" stop-color="#83accd"/>
      <stop offset=".51" stop-color="#5293ca"/>
      <stop offset=".54" stop-color="#3f8ac9"/>
      <stop offset=".74" stop-color="#172f73"/>
      <stop offset=".85" stop-color="#05054c"/>
    </linearGradient>
    <radialGradient id="radial-gradient-10" cx="185.5" cy="26.26" fx="185.5" fy="26.26" r="33.81" gradientTransform="translate(-172.53) scale(.97 1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#d0d3d3"/>
      <stop offset=".04" stop-color="#bbc2cd"/>
      <stop offset=".19" stop-color="#758ab8"/>
      <stop offset=".3" stop-color="#4967ab"/>
      <stop offset=".35" stop-color="#395aa7"/>
      <stop offset=".55" stop-color="#3f8ac9"/>
      <stop offset=".74" stop-color="#172f73"/>
      <stop offset=".84" stop-color="#05054c"/>
    </radialGradient>
    <linearGradient id="linear-gradient-8" x1="6.38" y1="7.78" x2="1.63" y2="12.47" gradientUnits="userSpaceOnUse">
      <stop offset=".17" stop-color="#cdd5ec"/>
      <stop offset=".17" stop-color="#cad3eb"/>
      <stop offset=".33" stop-color="#7facd8"/>
      <stop offset=".44" stop-color="#5093cd"/>
      <stop offset=".5" stop-color="#3f8ac9"/>
      <stop offset=".53" stop-color="#3e84c4"/>
      <stop offset=".81" stop-color="#395aa7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-9" x1="-1" y1="8.31" x2="10.1" y2="22.27" gradientUnits="userSpaceOnUse">
      <stop offset=".01" stop-color="#3767b0"/>
      <stop offset=".55" stop-color="#395aa7"/>
      <stop offset=".9" stop-color="#05054c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-10" x1="9.85" y1="28.52" x2="10.1" y2="-1.77" gradientTransform="matrix(1,0,0,1,0,0)" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-11" x1="10.21" y1="37.15" x2="21.22" y2="-5.72" gradientTransform="matrix(1,0,0,1,0,0)" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-12" x1="11.9" y1="49.88" x2="22.28" y2="-7.98" gradientTransform="matrix(1,0,0,1,0,0)" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-13" x1="25.11" y1="5.15" x2="14.05" y2="61" gradientUnits="userSpaceOnUse">
      <stop offset=".09" stop-color="#cdd5ec"/>
      <stop offset=".11" stop-color="#c1cfe9"/>
      <stop offset=".22" stop-color="#7baad7"/>
      <stop offset=".3" stop-color="#4f92cd"/>
      <stop offset=".34" stop-color="#3f8ac9"/>
      <stop offset=".73" stop-color="#172f74"/>
      <stop offset=".93" stop-color="#05054c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-14" x1="21.7" y1="15.7" x2="21.7" y2="64.19" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#3f8ac9"/>
      <stop offset=".02" stop-color="#3e84c4"/>
      <stop offset=".15" stop-color="#395aa7"/>
      <stop offset=".53" stop-color="#1d2c76"/>
      <stop offset=".9" stop-color="#05054c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-15" x1="16.66" y1="35.35" x2="25" y2="56.85" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#d0d3d3"/>
      <stop offset=".14" stop-color="#9ab5ca"/>
      <stop offset=".35" stop-color="#548ebe"/>
      <stop offset=".5" stop-color="#2876b7"/>
      <stop offset=".57" stop-color="#186db5"/>
      <stop offset=".93" stop-color="#05054c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-16" x1="-10243.43" y1="-50.84" x2="-10219.19" y2="-50.84" gradientTransform="translate(-10037.71 109.65) rotate(-180) scale(.98 -1.02)" gradientUnits="userSpaceOnUse">
      <stop offset=".16" stop-color="#395aa7"/>
      <stop offset=".18" stop-color="#3961ac"/>
      <stop offset=".28" stop-color="#3c77bc"/>
      <stop offset=".37" stop-color="#3e85c5"/>
      <stop offset=".45" stop-color="#3f8ac9"/>
      <stop offset=".56" stop-color="#3f8ac9"/>
      <stop offset=".59" stop-color="#3e84c4"/>
      <stop offset=".86" stop-color="#395aa7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-17" x1="-10254.23" y1="52.3" x2="-10258.79" y2="57.39" gradientTransform="translate(-10235.05) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".21" stop-color="#cdd5ec"/>
      <stop offset=".27" stop-color="#cdd5ec" stop-opacity=".86"/>
      <stop offset=".39" stop-color="#cdd5ec" stop-opacity=".6"/>
      <stop offset=".5" stop-color="#cdd5ec" stop-opacity=".38"/>
      <stop offset=".61" stop-color="#cdd5ec" stop-opacity=".22"/>
      <stop offset=".7" stop-color="#cdd5ec" stop-opacity=".1"/>
      <stop offset=".79" stop-color="#cdd5ec" stop-opacity=".03"/>
      <stop offset=".85" stop-color="#cdd5ec" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-18" x1="36.1" y1="55.72" x2="13.99" y2="55.72" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#cdd5ec"/>
      <stop offset="0" stop-color="#c5cee8"/>
      <stop offset=".04" stop-color="#93a5d1"/>
      <stop offset=".08" stop-color="#6c84be"/>
      <stop offset=".11" stop-color="#506db1"/>
      <stop offset=".14" stop-color="#3f5fa9"/>
      <stop offset=".16" stop-color="#395aa7"/>
      <stop offset=".24" stop-color="#395aa7"/>
      <stop offset=".27" stop-color="#3961ac"/>
      <stop offset=".35" stop-color="#3c77bc"/>
      <stop offset=".43" stop-color="#3e85c5"/>
      <stop offset=".5" stop-color="#3f8ac9"/>
      <stop offset=".53" stop-color="#3e84c4"/>
      <stop offset=".76" stop-color="#395aa7"/>
      <stop offset=".86" stop-color="#395aa7"/>
      <stop offset="1" stop-color="#cdd5ec"/>
    </linearGradient>
    <linearGradient id="linear-gradient-19" x1="37.93" y1="65.55" x2="12.39" y2="65.43" gradientUnits="userSpaceOnUse">
      <stop offset=".16" stop-color="#395aa7"/>
      <stop offset=".19" stop-color="#3961ac"/>
      <stop offset=".3" stop-color="#3c77bc"/>
      <stop offset=".41" stop-color="#3e85c5"/>
      <stop offset=".5" stop-color="#3f8ac9"/>
      <stop offset=".56" stop-color="#3f8ac9"/>
      <stop offset=".59" stop-color="#3e84c4"/>
      <stop offset=".86" stop-color="#395aa7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-20" x1="37.92" y1="70.74" x2="12.41" y2="70.62" gradientUnits="userSpaceOnUse">
      <stop offset=".16" stop-color="#395aa7"/>
      <stop offset=".18" stop-color="#3961ac"/>
      <stop offset=".25" stop-color="#3c77bc"/>
      <stop offset=".33" stop-color="#3e85c5"/>
      <stop offset=".39" stop-color="#3f8ac9"/>
      <stop offset=".61" stop-color="#3f8ac9"/>
      <stop offset=".64" stop-color="#3e84c4"/>
      <stop offset=".86" stop-color="#395aa7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-21" x1="34.28" y1="70.68" x2="27.86" y2="70.68" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#395aa7"/>
      <stop offset="0" stop-color="#395aa7"/>
      <stop offset=".65" stop-color="#3d7cbf"/>
      <stop offset="1" stop-color="#3f8ac9"/>
    </linearGradient>
    <linearGradient id="linear-gradient-22" x1="12.53" y1="70.68" x2="17.67" y2="70.68" gradientUnits="userSpaceOnUse">
      <stop offset=".28" stop-color="#cdd5ec" stop-opacity=".8"/>
      <stop offset=".4" stop-color="#cdd5ec" stop-opacity=".6"/>
      <stop offset=".55" stop-color="#cdd5ec" stop-opacity=".38"/>
      <stop offset=".69" stop-color="#cdd5ec" stop-opacity=".22"/>
      <stop offset=".81" stop-color="#cdd5ec" stop-opacity=".1"/>
      <stop offset=".92" stop-color="#cdd5ec" stop-opacity=".03"/>
      <stop offset="1" stop-color="#cdd5ec" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-23" x1="-10251.23" y1="70.68" x2="-10257.65" y2="70.68" gradientTransform="translate(-10235.18) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#395aa7"/>
      <stop offset="0" stop-color="#395aa7"/>
      <stop offset=".64" stop-color="#3d7cbf"/>
      <stop offset=".99" stop-color="#3f8ac9"/>
    </linearGradient>
    <radialGradient id="radial-gradient-11" cx="-10448.83" cy="11.55" fx="-10448.83" fy="11.55" r="52.54" gradientTransform="translate(-10062.65) rotate(-180) scale(.97 -1)" xlink:href="#radial-gradient-9"/>
    <linearGradient id="linear-gradient-24" x1="-10288.31" y1="22.25" x2="-10260.36" y2="53.44" gradientTransform="translate(-10235.18) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-7"/>
    <radialGradient id="radial-gradient-12" cx="-10435.08" cy="26.26" fx="-10435.08" fy="26.26" r="33.81" gradientTransform="translate(-10062.65) rotate(-180) scale(.97 -1)" xlink:href="#radial-gradient-10"/>
    <linearGradient id="linear-gradient-25" x1="-10260.4" y1="5.15" x2="-10271.46" y2="61" gradientTransform="translate(-10235.18) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".09" stop-color="#cdd5ec"/>
      <stop offset=".11" stop-color="#c1cfe9"/>
      <stop offset=".22" stop-color="#7baad7"/>
      <stop offset=".3" stop-color="#4f92cd"/>
      <stop offset=".34" stop-color="#3f8ac9"/>
      <stop offset=".73" stop-color="#182e73"/>
      <stop offset=".93" stop-color="#06044c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-26" x1="-10263.81" y1="15.7" x2="-10263.81" y2="64.19" gradientTransform="translate(-10235.18) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-14"/>
    <linearGradient id="linear-gradient-27" x1="-10268.85" y1="35.35" x2="-10260.5" y2="56.85" gradientTransform="translate(-10235.18) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#d0d3d3"/>
      <stop offset=".14" stop-color="#9ab5ca"/>
      <stop offset=".35" stop-color="#548ebe"/>
      <stop offset=".5" stop-color="#2876b7"/>
      <stop offset=".57" stop-color="#186db5"/>
      <stop offset=".93" stop-color="#000041"/>
    </linearGradient>
    <linearGradient id="linear-gradient-28" x1="25.16" y1="12.47" x2="25.16" y2="57.41" gradientUnits="userSpaceOnUse">
      <stop offset=".06" stop-color="#cdd5ec"/>
      <stop offset=".34" stop-color="#3f8ac9"/>
      <stop offset=".49" stop-color="#2960ac"/>
      <stop offset=".75" stop-color="#3f8ac9"/>
      <stop offset=".75" stop-color="#3d86c6"/>
      <stop offset=".84" stop-color="#1f4084"/>
      <stop offset=".9" stop-color="#0d145b"/>
      <stop offset=".93" stop-color="#06044c"/>
    </linearGradient>
    <radialGradient id="radial-gradient-13" cx="24.93" cy="13.51" fx="24.93" fy="13.51" r="44.33" gradientTransform="translate(31.29 -11.5) rotate(89.3) scale(1 .49)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#cdd5ec" stop-opacity="0"/>
      <stop offset=".11" stop-color="#b5c4e2" stop-opacity=".11"/>
      <stop offset=".33" stop-color="#7898ca" stop-opacity=".39"/>
      <stop offset=".61" stop-color="#1f59a7" stop-opacity=".8"/>
      <stop offset=".66" stop-color="#285faa" stop-opacity=".76"/>
      <stop offset=".74" stop-color="#4372b5" stop-opacity=".63"/>
      <stop offset=".84" stop-color="#6e91c6" stop-opacity=".43"/>
      <stop offset=".94" stop-color="#aabcde" stop-opacity=".16"/>
      <stop offset="1" stop-color="#cdd5ec" stop-opacity="0"/>
    </radialGradient>
    <radialGradient id="radial-gradient-14" cx="24.85" cy="38.72" fx="24.85" fy="38.72" r="17.49" gradientTransform="translate(55.6 13.87) rotate(90) scale(1 .79)" gradientUnits="userSpaceOnUse">
      <stop offset=".16" stop-color="#cdd5ec"/>
      <stop offset=".31" stop-color="#7d93c7"/>
      <stop offset=".42" stop-color="#4c69af"/>
      <stop offset=".48" stop-color="#395aa7"/>
      <stop offset=".49" stop-color="#3962ad"/>
      <stop offset=".51" stop-color="#3b71b7"/>
      <stop offset=".54" stop-color="#3b7abe"/>
      <stop offset=".59" stop-color="#3c7dc0"/>
      <stop offset=".75" stop-color="#182f75"/>
      <stop offset=".85" stop-color="#05054c"/>
    </radialGradient>
    <linearGradient id="linear-gradient-29" x1="31.84" y1="54.02" x2="18.5" y2="54.02" gradientUnits="userSpaceOnUse">
      <stop offset=".07" stop-color="#a9b1d9"/>
      <stop offset=".16" stop-color="#395aa7"/>
      <stop offset=".18" stop-color="#3961ac"/>
      <stop offset=".3" stop-color="#3c77bc"/>
      <stop offset=".4" stop-color="#3e85c5"/>
      <stop offset=".5" stop-color="#3f8ac9"/>
      <stop offset=".54" stop-color="#3e84c4"/>
      <stop offset=".86" stop-color="#395aa7"/>
      <stop offset=".93" stop-color="#a7afd8"/>
    </linearGradient>
    <linearGradient id="linear-gradient-30" x1="16.72" y1="65.49" x2="23.69" y2="65.49" gradientUnits="userSpaceOnUse">
      <stop offset=".17" stop-color="#395aa7"/>
      <stop offset=".17" stop-color="#395aa7"/>
      <stop offset=".7" stop-color="#3d7cbf"/>
      <stop offset=".99" stop-color="#3f8ac9"/>
    </linearGradient>
    <linearGradient id="linear-gradient-31" x1="-10268.78" y1="65.49" x2="-10262.5" y2="65.49" gradientTransform="translate(10296.8 130.99) scale(1 -1)" xlink:href="#linear-gradient-30"/>
    <linearGradient id="linear-gradient-32" x1="-10253.46" y1="50.17" x2="-10260.39" y2="56.82" gradientTransform="translate(-10235.24) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".29" stop-color="#cdd5ec"/>
      <stop offset=".32" stop-color="#cdd5ec" stop-opacity=".86"/>
      <stop offset=".38" stop-color="#cdd5ec" stop-opacity=".6"/>
      <stop offset=".44" stop-color="#cdd5ec" stop-opacity=".38"/>
      <stop offset=".49" stop-color="#cdd5ec" stop-opacity=".22"/>
      <stop offset=".54" stop-color="#cdd5ec" stop-opacity=".1"/>
      <stop offset=".58" stop-color="#cdd5ec" stop-opacity=".03"/>
      <stop offset=".61" stop-color="#cdd5ec" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-33" x1="-2463.75" y1="52.3" x2="-2468.31" y2="57.39" gradientTransform="translate(2494.89)" xlink:href="#linear-gradient-17"/>
    <linearGradient id="linear-gradient-34" x1="-2462.98" y1="50.17" x2="-2469.91" y2="56.82" gradientTransform="translate(2495.08)" xlink:href="#linear-gradient-32"/>
    <linearGradient id="linear-gradient-35" x1="11.76" y1="72.72" x2="16.82" y2="72.72" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#cdd5ec" stop-opacity=".8"/>
      <stop offset=".17" stop-color="#cdd5ec" stop-opacity=".6"/>
      <stop offset=".37" stop-color="#cdd5ec" stop-opacity=".38"/>
      <stop offset=".56" stop-color="#cdd5ec" stop-opacity=".22"/>
      <stop offset=".74" stop-color="#cdd5ec" stop-opacity=".1"/>
      <stop offset=".89" stop-color="#cdd5ec" stop-opacity=".03"/>
      <stop offset="1" stop-color="#cdd5ec" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-36" x1="15.58" y1="72.72" x2="20.27" y2="72.72" xlink:href="#linear-gradient-21"/>
    <linearGradient id="linear-gradient-37" x1="-10269.93" y1="72.72" x2="-10265.24" y2="72.72" gradientTransform="translate(10301.71 145.43) scale(1 -1)" xlink:href="#linear-gradient-21"/>
    <linearGradient id="linear-gradient-38" x1="-10286.46" y1="8.31" x2="-10275.36" y2="22.27" gradientTransform="translate(-10235.14) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#3767b0"/>
      <stop offset=".55" stop-color="#395aa7"/>
      <stop offset=".9" stop-color="#05054c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-39" x1="12.53" y1="65.49" x2="21.03" y2="65.49" gradientUnits="userSpaceOnUse">
      <stop offset=".19" stop-color="#cdd5ec" stop-opacity=".8"/>
      <stop offset=".33" stop-color="#cdd5ec" stop-opacity=".6"/>
      <stop offset=".49" stop-color="#cdd5ec" stop-opacity=".38"/>
      <stop offset=".65" stop-color="#cdd5ec" stop-opacity=".22"/>
      <stop offset=".79" stop-color="#cdd5ec" stop-opacity=".1"/>
      <stop offset=".91" stop-color="#cdd5ec" stop-opacity=".03"/>
      <stop offset="1" stop-color="#cdd5ec" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-40" x1="-7777.95" y1="70.68" x2="-7772.82" y2="70.68" gradientTransform="translate(-7740.16) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-22"/>
    <linearGradient id="linear-gradient-41" x1="-7778.72" y1="72.72" x2="-7773.66" y2="72.72" gradientTransform="translate(-7740.16) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#cdd5ec" stop-opacity=".8"/>
      <stop offset=".17" stop-color="#cdd5ec" stop-opacity=".6"/>
      <stop offset=".37" stop-color="#cdd5ec" stop-opacity=".38"/>
      <stop offset=".56" stop-color="#cdd5ec" stop-opacity=".22"/>
      <stop offset=".74" stop-color="#cdd5ec" stop-opacity=".1"/>
      <stop offset=".89" stop-color="#cdd5ec" stop-opacity=".03"/>
      <stop offset="1" stop-color="#cdd5ec" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-42" x1="-7777.96" y1="65.49" x2="-7769.46" y2="65.49" gradientTransform="translate(-7740.16) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-39"/>
  </defs>
  <g>
    <g>
      <g>
        <polygon class="cls-25" points="79.56 20.67 79.56 18.5 72.29 18.5 72.29 28.55 74.88 28.55 74.88 25.36 74.59 24.53 78.3 24.53 78.3 22.49 74.88 22.49 74.88 20.67 79.56 20.67"/>
        <path class="cls-25" d="M82.73,28.05c-.86-.41-1.52-1.01-1.98-1.79-.47-.78-.7-1.69-.7-2.73,0-1.05.23-1.96.7-2.73.46-.78,1.13-1.38,1.98-1.8.86-.42,1.85-.63,2.98-.63.74,0,1.41.07,1.99.21.58.14,1.12.36,1.62.66v2.37c-.44-.33-.93-.57-1.48-.72-.55-.15-1.18-.23-1.89-.23-1.02,0-1.81.25-2.36.76-.55.5-.83,1.2-.83,2.1,0,.9.28,1.6.84,2.1.56.51,1.34.76,2.35.76.71,0,1.35-.08,1.92-.24.56-.16,1.09-.41,1.57-.76v2.36c-.94.61-2.18.92-3.73.92-1.13,0-2.12-.21-2.98-.62"/>
        <path class="cls-25" d="M64.57,24.53l1.3-3.38,1.29,3.38h-2.59ZM71.41,28.55l-4.22-10.05h-3.51l.35,1.23-3.71,8.82h2.75l.5-1.23-.29-.82h4.53s.85,2.06.85,2.06h2.74Z"/>
      </g>
      <g>
        <path class="cls-25" d="M86.26,45.62h-3.85v-5.64h-4.44v5.64h-3.85v-13.83h3.85v5.13h4.44v-5.13h3.85v13.83Z"/>
        <path class="cls-25" d="M63.17,44.96c-1.18-.57-2.1-1.39-2.74-2.47-.64-1.07-.97-2.33-.97-3.78,0-1.44.32-2.7.96-3.78.64-1.07,1.55-1.9,2.74-2.48,1.18-.58,2.55-.87,4.11-.87,1.03,0,1.94.1,2.75.29.81.2,1.55.5,2.24.92v3.28c-.6-.45-1.28-.79-2.04-.99-.76-.21-1.63-.31-2.62-.31-1.41,0-2.5.35-3.26,1.05-.77.7-1.15,1.66-1.15,2.9,0,1.24.39,2.2,1.16,2.91.77.7,1.86,1.05,3.25,1.05.98,0,1.87-.11,2.65-.33.78-.22,1.5-.57,2.17-1.05v3.26c-1.3.85-3.01,1.27-5.15,1.27-1.56,0-2.93-.28-4.11-.86"/>
        <path class="cls-25" d="M93.17,40.09l1.79-4.65,1.77,4.65h-3.56ZM102.58,45.62l-5.81-13.83h-4.82l.48,1.7-5.1,12.13h3.79l.69-1.7-.39-1.13h6.23s1.17,2.83,1.17,2.83h3.76Z"/>
        <path class="cls-25" d="M126.32,37.94c-.39.27-.93.41-1.64.41h-2.02v-3.61h2.02c.72,0,1.27.13,1.65.39.38.26.57.73.57,1.42s-.19,1.13-.58,1.4M130.2,39.18c.47-.71.68-1.6.68-2.63,0-1.53-.46-2.71-1.39-3.53-.93-.82-2.21-1.23-3.85-1.23h-6.29v13.83h3.65v-3.17l-.39-1.15h3.13c.8,0,1.79-.14,2.54-.5.98-.46,1.65-1.21,1.92-1.62"/>
        <polygon class="cls-25" points="136.48 31.79 131.86 31.79 132.33 33.49 132.34 43.92 131.87 45.62 136.47 45.62 135.99 43.92 136 33.49 136.48 31.79"/>
        <path class="cls-25" d="M137.45,38.63c0-3.88,3.36-7.02,7.6-7.02s7.62,3.15,7.62,7.02-3.34,7.02-7.62,7.02-7.6-3.13-7.6-7.02ZM149.11,38.63c0-2.1-1.75-3.82-4.05-3.82s-4.01,1.72-4.01,3.82,1.67,3.8,4.01,3.8,4.05-1.72,4.05-3.8Z"/>
        <path class="cls-25" d="M102.58,31.79h3.42l4.06,5.17,4.19-5.17h3.28v13.83h-3.51v-8.77l-3.98,4.91-3.94-4.89v8.75h-3.53v-13.83Z"/>
        <polygon class="cls-25" points="166.36 45.52 166.35 31.79 162.7 31.79 162.7 39.96 157.39 31.79 153.77 31.79 153.78 45.52 157.44 45.52 157.43 37.33 162.76 45.52 166.36 45.52"/>
        <path class="cls-25" d="M168.6,42.7l-1.34,2.83h6.04c1.85,0,3.25-.35,4.18-1.04,1-.73,1.54-1.98,1.29-3.18-.59-2.83-4.04-3.33-6.34-4.69-.38-.23-.58-.52-.58-.89,0-.36.16-.65.53-.84.38-.2.92-.29,1.55-.29h3.31l1.02-2.8h-4.99c-1.03,0-1.95.11-2.74.44-.79.33-1.41.79-1.86,1.38-.44.59-.66,1.27-.66,2.02,0,.92.29,1.69.87,2.32.62.67,1.56,1.07,2.39,1.43,1.08.47,2.33.84,3.29,1.53.22.16.42.42.43.68,0,.31-.08.52-.29.68-.67.51-1.57.42-2.39.42h-3.74Z"/>
      </g>
      <g>
        <polygon class="cls-25" points="188.78 42.37 188.78 31.41 185.06 31.41 185.06 45.52 185.21 45.52 188.78 45.52 195.3 45.52 195.3 42.37 188.78 42.37"/>
        <polygon class="cls-25" points="196.56 45.52 206.76 45.52 206.76 42.51 200.2 42.51 200.2 39.86 204.99 39.86 204.99 37.05 200.2 37.05 200.2 34.4 206.76 34.4 206.76 31.41 196.56 31.41 196.56 45.52"/>
        <path class="cls-25" d="M212.72,39.88l1.82-4.75,1.81,4.75h-3.63ZM222.32,45.52l-5.93-14.11h-4.92l.49,1.73-5.2,12.38h3.86l.71-1.73-.4-1.16h6.36s1.19,2.89,1.19,2.89h3.84Z"/>
        <path class="cls-25" d="M241.9,45.74c1.77,0,3.76-.5,4.85-1.63,1.09-1.12,1.63-2.63,1.63-4.52v-8.18h-3.62v8.22c0,.94-.25,1.65-.76,2.14-.5.48-1.21.73-2.11.73s-1.6-.24-2.11-.73c-.5-.48-.76-1.2-.76-2.14v-8.22h-3.62v8.18c0,1.88.54,3.39,1.63,4.52,1.09,1.13,3.09,1.63,4.85,1.63"/>
        <polygon class="cls-25" points="249.8 45.52 260 45.52 260 42.51 253.44 42.51 253.44 39.86 258.23 39.86 258.23 37.05 253.44 37.05 253.44 34.4 260 34.4 260 31.41 249.8 31.41 249.8 45.52"/>
        <path class="cls-25" d="M234.48,38.84h-3.6v2.92l.83.49s-.33.13-.86.21c-.42.05-.86.08-1.33.08-1.41,0-2.5-.36-3.29-1.07-.78-.71-1.17-1.7-1.17-2.96,0-1.26.39-2.24,1.16-2.95.77-.71,1.87-1.06,3.29-1.06,1,0,1.88.11,2.64.32.77.21,1.45.55,2.06,1.01v-3.33c-.69-.42-1.45-.73-2.26-.93-.82-.2-1.74-.3-2.78-.3-1.58,0-2.96.3-4.16.89-1.2.59-2.12,1.43-2.77,2.52-.65,1.09-.97,2.37-.97,3.84,0,1.47.33,2.75.98,3.84.65,1.09,1.57,1.93,2.77,2.51,1.2.58,2.58.87,4.16.87,2.16,0,3.97-.45,5.28-1.31v-.03s0,0,0,0v-5.55Z"/>
      </g>
    </g>
    <g>
      <g>
        <path class="cls-19" d="M93.15,56.69c0-4,3.39-7.24,7.66-7.24s7.68,3.24,7.68,7.24-3.37,7.24-7.68,7.24-7.66-3.22-7.66-7.24ZM104.89,56.69c0-2.16-1.77-3.94-4.08-3.94s-4.04,1.78-4.04,3.94,1.68,3.92,4.04,3.92,4.08-1.78,4.08-3.92Z"/>
        <path class="cls-19" d="M72.27,49.71h3.92l2.34,8.61,2.05-6.9-.58-1.72h4.49l2.57,8.63,2.34-8.63h3.88l-4.15,13.97h-4.21l-2.13-7.7-2.09,7.7h-4.33l-4.09-13.97Z"/>
        <polygon class="cls-19" points="72.27 49.7 60.33 49.7 60.33 52.87 64.49 52.87 64.49 63.68 68.18 63.68 68.18 52.87 72.27 52.87 72.27 49.7"/>
      </g>
      <path class="cls-19" d="M111.02,51.99h-.4v-2.3h.35l.75,1.02.71-.97h.4v2.25h-.44v-1.55l-.62.84h-.09l-.66-.84v1.55ZM109.52,50.14v1.86h-.44v-1.86h-.62v-.44h1.72v.44h-.66Z"/>
    </g>
  </g>
  <g>
    <path class="cls-17" d="M30.35,12.6s.03.05.05.1c0,0,0,0,0,0,1.53,2.67,4.92,9.76,4.92,19.65,0,1-.13,2.37-.37,3.97.7-2.05,4.91-15.28.34-25.49-.09-.2-.16-.25-.43-.16-.17.05-4.24,1.51-4.4,1.57-.16.06-.19.21-.11.37Z"/>
    <path class="cls-8" d="M34.95,9.86c.06.2.15.47.24.8.04.03.07.09.1.16,3.43,7.66,1.92,17.03.65,22.09,1.36-3.37,7.17-18.37,4.61-23.93-.1-.22-.28-.27-.42-.25-.16.02-4.76.68-4.95.71-.23.04-.31.18-.23.42Z"/>
    <path class="cls-7" d="M41.4,20.89c1.86-2.61,4.69-6.19,8.22-9.48,0-.02.09-.56-.07-1.14-.3-1.08-1.42-1.99-3.27-2.01-.6,0-1.09.02-1.28.03-.14.01-.21.06-.22.2-.02.14-.1.87-.1.87,0,0-.02.08-.06.23.5-.02.86-.03.92-.03.2,0,.33.08.34.3,0,.19,0,.47,0,.86-.11,2.35-1.04,4.8-4.46,10.16Z"/>
    <path class="cls-4" d="M38.86,24.74l.15-.24c.23-.4,1.06-1.75,2.38-3.61,3.42-5.36,4.35-7.81,4.46-10.16.02-.4.02-.68,0-.86,0-.22-.14-.3-.34-.3s-3.91.13-4.28.15c-.22.01-.33.04-.39.13,1,3.57-.4,9.74-2,14.9Z"/>
    <path class="cls-18" d="M44.61,9.59c.5-.02.86-.03.92-.03h0c.03-.23.04-.45.06-.68.01-.25.02-.45.01-.62-.28,0-.49.02-.61.03-.14.01-.21.06-.22.2-.02.14-.1.87-.1.87,0,0-.02.08-.06.23Z"/>
    <path class="cls-16" d="M38.86,24.74c1.81-4.3,3.56-11.14,2.91-15.05-.26,0-.45.02-.52.02-.22.01-.33.04-.39.13,1,3.57-.4,9.74-2,14.9Z"/>
    <path class="cls-15" d="M34.95,9.86c.06.2.15.47.24.8.04.03.07.09.1.16,3.43,7.66,1.92,17.03.65,22.09.16-.39.37-.93.63-1.6,1.29-5.58,2.43-14.45-.8-21.89-.01-.03-.02-.05-.04-.07-.31.05-.52.08-.56.08-.23.04-.31.18-.23.42Z"/>
    <path class="cls-14" d="M30.35,12.6s.03.05.05.1c0,0,0,0,0,0,1.53,2.67,4.92,9.76,4.92,19.65,0,1-.13,2.37-.37,3.97.33-1.01.8-4.3.8-5.28,0-9.23-2.95-16.02-4.59-19.06-.39.14-.66.24-.7.26-.16.06-.19.21-.11.37Z"/>
    <path class="cls-11" d="M38.88,71.88v1.96H11.45v-1.96c0-.23.05-.3.31-.3h26.82c.26,0,.31.08.31.3Z"/>
    <path class="cls-3" d="M19.97,12.6s-.03.05-.05.1c0,0,0,0,0,0-1.53,2.67-4.92,9.76-4.92,19.65,0,1,.13,2.37.37,3.97-.7-2.05-4.91-15.28-.34-25.49.09-.2.16-.25.43-.16.17.05,4.24,1.51,4.4,1.57.16.06.19.21.11.37Z"/>
    <path class="cls-2" d="M15.37,9.86c-.06.2-.15.47-.24.8-.04.03-.07.09-.1.16-3.43,7.66-1.92,17.03-.65,22.09-1.36-3.37-7.17-18.37-4.61-23.93.1-.22.28-.27.42-.25.16.02,4.76.68,4.95.71.23.04.31.18.23.42Z"/>
    <path class="cls-1" d="M8.93,20.89c-1.86-2.61-4.69-6.19-8.22-9.48,0-.02-.09-.56.07-1.14.3-1.08,1.42-1.99,3.27-2.01.6,0,1.09.02,1.28.03.14.01.21.06.22.2.02.14.1.87.1.87,0,0,.02.08.06.23-.5-.02-.86-.03-.92-.03-.2,0-.33.08-.34.3,0,.19,0,.47,0,.86.11,2.35,1.04,4.8,4.46,10.16Z"/>
    <path class="cls-6" d="M11.46,24.74l-.15-.24c-.23-.4-1.06-1.75-2.38-3.61-3.42-5.36-4.35-7.81-4.46-10.16-.02-.4-.02-.68,0-.86,0-.22.14-.3.34-.3s3.91.13,4.28.15c.22.01.33.04.39.13-1,3.57.4,9.74,2,14.9Z"/>
    <path class="cls-5" d="M18.2,47.81c-.08-.05-5.46-3.68-10.23-9.94-2.02-2.58-3.97-5.69-5.39-9.32C1.18,25.09.26,21.23.22,17.05c-.04-1.3.03-2.29.09-2.89,0,0,0,0,0,0,.11-1.33.38-2.74.39-2.74,6.04,5.63,10.04,12.14,10.6,13.09l.15.24c.06.21.13.42.19.62,0,0,0,0,0,0-.52-.89-3.37-3.75-5.24-5.53-1.86-1.78-3.6-3.16-3.6-3.16-.43,10,3.24,16.77,6.87,21.02,3.25,3.81,7.22,5.87,7.22,5.87,0,.03,1.29,4.25,1.29,4.25Z"/>
    <path class="cls-12" d="M29.78,52.13v1.3h-9.23v-1.3h9.23Z"/>
    <path class="cls-10" d="M18.2,47.81c-.08-.05-5.46-3.68-10.23-9.94-2.02-2.58-3.97-5.69-5.39-9.32-.78-1.93-1.41-3.98-1.82-6.14.91,3.24,1.33,4.68,2.31,6.79,1.61,3.55,3.71,6.55,5.86,9.02,3.27,3.85,6.74,6.6,8.82,8.09.25.82.46,1.51.46,1.51Z"/>
    <path class="cls-23" d="M16.91,43.56s-3.68-1.24-8.15-6.42c-3.83-4.43-7.46-11.62-6.92-22.1,0,0,1.8,1.47,3.74,3.35,1.94,1.89,5.65,5.95,6.08,6.97,0,0,0,0,0,0-.52-.89-3.37-3.75-5.24-5.53-1.86-1.78-3.6-3.16-3.6-3.16-.43,10,3.24,16.77,6.87,21.02,3.25,3.81,7.22,5.87,7.22,5.87Z"/>
    <path class="cls-9" d="M5.71,9.59c-.5-.02-.86-.03-.92-.03h0c-.03-.23-.04-.45-.06-.68-.01-.25-.02-.45-.01-.62.28,0,.49.02.61.03.14.01.21.06.22.2.02.14.1.87.1.87,0,0,.02.08.06.23Z"/>
    <path class="cls-13" d="M8.29,20.02c-1.84-2.5-4.44-5.67-7.59-8.61,0-.02-.09-.56.07-1.14.04-.15.1-.31.18-.45,1.05,1.84,5.67,7.37,7.34,10.2Z"/>
    <path class="cls-39" d="M11.46,24.74c-1.81-4.3-3.56-11.14-2.91-15.05.26,0,.45.02.52.02.22.01.33.04.39.13-1,3.57.4,9.74,2,14.9Z"/>
    <path class="cls-40" d="M15.37,9.86c-.06.2-.15.47-.24.8-.04.03-.07.09-.1.16-3.43,7.66-1.92,17.03-.65,22.09-.16-.39-.37-.93-.63-1.6-1.29-5.58-2.43-14.45.8-21.89.01-.03.02-.05.04-.07.31.05.52.08.56.08.23.04.31.18.23.42Z"/>
    <path class="cls-41" d="M19.97,12.6s-.03.05-.05.1c0,0,0,0,0,0-1.53,2.67-4.92,9.76-4.92,19.65,0,1,.13,2.37.37,3.97-.33-1.01-.8-4.3-.8-5.28,0-9.23,2.95-16.02,4.59-19.06.39.14.66.24.7.26.16.06.19.21.11.37Z"/>
    <path class="cls-42" d="M24.92,14.04h0s-.05-.08-.1-.11c-.17-.1-3.63-2.09-3.86-2.23-.23-.13-.38-.08-.51.12-1.02,1.57-5.45,9.21-5.45,20.52,0,3.6,1.64,12.06,4.33,18.4.01.03.04.07.07.12.01.02.03.04.05.06.01.01.02.02.03.03.02.02.04.04.06.05.02.01.03.03.05.04.12.1.42.35.99.15.74-.26,1.63-1.25,1.27-3.09-.11-.57-.9-3.63-1.41-6.37-1.51-8.06-1.08-15.7,4.24-27.03.05-.1.12-.19.18-.28.08-.14.11-.29.06-.4Z"/>
    <path class="cls-44" d="M20.44,41.75c-1.51-8.06-1.08-15.7,4.24-27.03-6.14,10.69-6.79,17.85-5.28,25.91.51,2.73,1.56,6.46,1.72,7.02.82,3.01-1.56,3.38-1.58,3.36.02.01.03.03.05.04.12.1.42.35.99.15.74-.26,1.63-1.25,1.27-3.09-.11-.57-.9-3.63-1.41-6.37Z"/>
    <path class="cls-50" d="M21.12,47.65c-.15-.56-1.2-4.29-1.72-7.02-.54-2.85-.8-5.59-.65-8.4-.15,2.2-.24,4.88.36,8.19.5,2.74,1.49,6.54,1.64,7.09.81,2.99-1.44,3.18-1.47,3.16,0,.03.02.05.03.08.01.03.04.07.07.12.01.02.03.04.05.06.01.01.02.02.03.03.02.02.04.04.06.05.02.01,2.4-.35,1.58-3.36Z"/>
    <path class="cls-51" d="M35.04,56.82c.8,0,1.46,2.3,2.03,2.3s-24.39,0-23.81,0,1.23-2.3,2.03-2.3,18.95,0,19.75,0Z"/>
    <path class="cls-29" d="M15.29,56.82c-.8,0-1.46,2.3-2.03,2.3s24.39,0,23.81,0-1.23-2.3-2.03-2.3-18.95,0-19.75,0Z"/>
    <path class="cls-52" d="M25.16,56.82h9.87c-2.06-.37-1.34-2.21-3.27-2.21s-6.6,0-6.6,0h-6.6c-1.94,0-1.21,1.84-3.27,2.21h9.88Z"/>
    <path class="cls-43" d="M37.91,61.47v8.32H12.41v-8.32c0-.21.04-.26.25-.26h25c.21,0,.25.06.25.26Z"/>
    <rect class="cls-45" x="12.41" y="69.78" width="25.5" height="1.8"/>
    <rect class="cls-34" x="28.73" y="69.78" width="5.58" height="1.8"/>
    <rect class="cls-31" x="12.41" y="69.78" width="3.6" height="1.8"/>
    <rect class="cls-35" x="16.01" y="69.78" width="5.58" height="1.8"/>
    <path class="cls-24" d="M32.12,47.81c.08-.05,5.46-3.68,10.23-9.94,2.02-2.58,3.97-5.69,5.39-9.32,1.4-3.46,2.32-7.31,2.35-11.5.04-1.3-.03-2.29-.09-2.89,0,0,0,0,0,0-.11-1.33-.38-2.74-.39-2.74-6.04,5.63-10.04,12.14-10.6,13.09l-.15.24c-.06.21-.13.42-.19.62,0,0,0,0,0,0,.52-.89,3.37-3.75,5.24-5.53,1.86-1.78,3.6-3.16,3.6-3.16.43,10-3.24,16.77-6.87,21.02-3.25,3.81-7.22,5.87-7.22,5.87,0,.03-1.29,4.25-1.29,4.25Z"/>
    <path class="cls-55" d="M32.12,47.81c.08-.05,5.46-3.68,10.23-9.94,2.02-2.58,3.97-5.69,5.39-9.32.78-1.93,1.41-3.98,1.82-6.14-.91,3.24-1.33,4.68-2.31,6.79-1.61,3.55-3.71,6.55-5.86,9.02-3.27,3.85-6.74,6.6-8.82,8.09-.25.82-.46,1.51-.46,1.51Z"/>
    <path class="cls-20" d="M33.41,43.56s3.68-1.24,8.15-6.42c3.83-4.43,7.46-11.62,6.92-22.1,0,0-1.8,1.47-3.74,3.35-1.94,1.89-5.65,5.95-6.08,6.97,0,0,0,0,0,0,.52-.89,3.37-3.75,5.24-5.53,1.86-1.78,3.6-3.16,3.6-3.16.43,10-3.24,16.77-6.87,21.02-3.25,3.81-7.22,5.87-7.22,5.87Z"/>
    <path class="cls-47" d="M25.47,14.44c.06.09.13.18.18.28,5.32,11.33,5.76,18.97,4.24,27.03-.51,2.73-1.3,5.8-1.41,6.37-.36,1.84.53,2.82,1.27,3.09.57.2.87-.05.99-.15.02-.01.04-.03.05-.04.02-.02.04-.04.06-.05.01-.01.02-.02.03-.03.02-.02.04-.04.05-.06.04-.05.06-.09.07-.12,2.69-6.35,4.33-14.8,4.33-18.4,0-11.31-4.43-18.95-5.45-20.52-.13-.2-.28-.26-.51-.12-.23.13-3.69,2.12-3.86,2.23-.05.03-.08.06-.1.11h0c-.05.11-.02.26.06.4Z"/>
    <path class="cls-56" d="M28.47,48.12c-.36,1.84.53,2.82,1.27,3.09.57.2.87-.05.99-.15.02-.01.04-.03.05-.04-.02.01-2.4-.35-1.58-3.36.15-.56,1.2-4.29,1.72-7.02,1.52-8.06.87-15.22-5.28-25.91,5.32,11.33,5.76,18.97,4.24,27.03-.51,2.73-1.3,5.8-1.41,6.37Z"/>
    <path class="cls-54" d="M30.79,51.01s.04-.04.06-.05c.01-.01.02-.02.03-.03.02-.02.04-.04.05-.06.04-.05.06-.09.07-.12.01-.02.02-.05.03-.08-.03.02-2.28-.17-1.47-3.16.15-.56,1.14-4.36,1.64-7.09.61-3.31.52-5.99.36-8.19.15,2.82-.11,5.56-.65,8.4-.51,2.73-1.56,6.46-1.72,7.02-.82,3.01,1.56,3.38,1.58,3.36Z"/>
    <path class="cls-46" d="M30.25,51.27c.13.37-.23.85-.47.85h-.39c-.46,0-1.19-.57-1.43-1.05-.32-.63-1.04-1.25-2.8-1.25v-.02.02c-1.77,0-2.49.62-2.81,1.25-.24.48-.97,1.05-1.43,1.05h-.39c-.24,0-.6-.48-.47-.85.14.01.31,0,.51-.07.74-.26,1.63-1.25,1.27-3.09-.11-.57-.9-3.63-1.41-6.37-1.51-8.06-1.08-15.7,4.24-27.03.05-.1.12-.19.18-.28.08-.14.11-.29.06-.4h0s0,0,0,0c.07.03.16.05.24.05h0c.08,0,.17-.03.24-.05,0,0,0,0,0,0h0c-.05.11-.02.26.06.4.06.09.13.18.18.28,5.32,11.33,5.76,18.97,4.24,27.03-.51,2.73-1.3,5.8-1.41,6.37-.36,1.84.53,2.82,1.27,3.09.2.07.37.09.51.07Z"/>
    <path class="cls-22" d="M30.25,51.27c.13.37-.23.85-.47.85h-.39c-.46,0-1.19-.57-1.43-1.05-.32-.63-1.04-1.25-2.8-1.25v-.02.02c-1.77,0-2.49.62-2.81,1.25-.24.48-.97,1.05-1.43,1.05h-.39c-.24,0-.6-.48-.47-.85.14.01.31,0,.51-.07.74-.26,1.63-1.25,1.27-3.09-.11-.57-.9-3.63-1.41-6.37-1.51-8.06-1.08-15.7,4.24-27.03.05-.1.12-.19.18-.28.08-.14.11-.29.06-.4h0s0,0,0,0c.07.03.16.05.24.05h0c.08,0,.17-.03.24-.05,0,0,0,0,0,0h0c-.05.11-.02.26.06.4.06.09.13.18.18.28,5.32,11.33,5.76,18.97,4.24,27.03-.51,2.73-1.3,5.8-1.41,6.37-.36,1.84.53,2.82,1.27,3.09.2.07.37.09.51.07Z"/>
    <path class="cls-21" d="M29.39,52.13h-8.46c.46,0,1.19-.57,1.43-1.05.32-.63,1.04-1.25,2.8-1.25s2.49.62,2.81,1.25c.24.48.97,1.05,1.43,1.05Z"/>
    <path class="cls-57" d="M31.59,54.61h-12.86c.49,0,.55-1.18,1.37-1.18h10.11c.82,0,.89,1.18,1.37,1.18Z"/>
    <rect class="cls-49" x="16.72" y="61.2" width="5.58" height="8.58"/>
    <rect class="cls-48" x="28.02" y="61.2" width="5.58" height="8.58" transform="translate(61.62 130.99) rotate(-180)"/>
    <path class="cls-30" d="M25.16,56.82h-9.87c2.06-.37,1.34-2.21,3.27-2.21h6.6s4.66,0,6.6,0,1.21,1.84,3.27,2.21h-9.87s0,0,0,0Z"/>
    <path class="cls-28" d="M35.04,56.82c.8,0,1.46,2.3,2.03,2.3s-24.39,0-23.81,0,1.23-2.3,2.03-2.3,18.95,0,19.75,0Z"/>
    <path class="cls-26" d="M25.17,56.82h9.87c-2.06-.37-1.34-2.21-3.27-2.21s-6.6,0-6.6,0h-6.6c-1.94,0-1.21,1.84-3.27,2.21h9.88Z"/>
    <path class="cls-27" d="M15.28,73.85h-3.84v-1.96c0-.23.05-.3.31-.3h3.53v2.26Z"/>
    <rect class="cls-36" x="15.28" y="71.58" width="3.56" height="2.26"/>
    <rect class="cls-37" x="31.48" y="71.58" width="3.56" height="2.26" transform="translate(66.52 145.43) rotate(-180)"/>
    <path class="cls-53" d="M42.03,20.02c1.84-2.5,4.44-5.67,7.59-8.61,0-.02.09-.56-.07-1.14-.04-.15-.1-.31-.18-.45-1.05,1.84-5.67,7.37-7.34,10.2Z"/>
    <path class="cls-58" d="M19.29,69.78h-6.88v-8.32c0-.21.04-.26.25-.26h6.62v8.58Z"/>
    <rect class="cls-32" x="34.31" y="69.78" width="3.6" height="1.8"/>
    <path class="cls-33" d="M35.04,73.85h3.84s0-1.74,0-1.96c0-.23-.05-.3-.31-.3h-3.53s0,2.26,0,2.26Z"/>
    <path class="cls-38" d="M31.04,69.78h6.88s0-8.11,0-8.32c0-.21-.04-.26-.25-.26h-6.62s0,8.58,0,8.58Z"/>
  </g>
</svg>