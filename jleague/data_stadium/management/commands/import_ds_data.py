import asyncio
from django.core.management.base import BaseCommand

from ...ftp_import.main import run_import, run_live_import


class Command(BaseCommand):
    def add_arguments(self, parser):
        parser.add_argument("always_check_children", nargs=1, type=str)
        parser.add_argument("import_all_live_games", nargs=1, type=str)

    def handle(self, *args, **options):
        always_check_children = options.get("always_check_children")[0] == "yes"
        import_all_live_games = options.get("import_all_live_games")[0] == "yes"
        if import_all_live_games:
            asyncio.run(run_import(always_check_children))
        else:
            asyncio.run(run_live_import(always_check_children=always_check_children))
