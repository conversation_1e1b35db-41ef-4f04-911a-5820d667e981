# Generated by Django 3.1.13 on 2021-10-08 17:24

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ('wagtailcore', '0060_fix_workflow_unique_constraint'),
        ('data_stadium', '0032_gamekindteamstats_clean_sheet'),
        ('home', '0025_teaminfo_president'),
    ]

    operations = [
        migrations.CreateModel(
            name='LiveVideo',
            fields=[
                (
                    'page_ptr',
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to='wagtailcore.page',
                    ),
                ),
                ('embed_url', models.URLField()),
                ('html', models.TextField()),
                (
                    'game',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='game',
                        to='data_stadium.game',
                    ),
                ),
            ],
            options={
                'abstract': False,
            },
            bases=('wagtailcore.page',),
        ),
    ]
