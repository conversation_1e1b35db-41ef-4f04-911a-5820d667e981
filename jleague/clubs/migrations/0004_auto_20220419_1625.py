# Generated by Django 3.1.13 on 2022-04-19 07:25

from django.db import migrations


def patch_four_latter_names(apps, schema_editor):
    four_letter_names = (
        # team_id, 4-letters name
        (133, 'C-<PERSON>'),
        (270, 'FCTK'),
        (128, 'G-<PERSON>'),
        (276, 'SAPP'),
        (131, 'IWAT'),
        (120, 'KASM'),
        (132, 'KASW'),
        (86, 'KA-F'),
        (134, 'KYOT'),
        (127, 'NAGO'),
        (269, 'TOSU'),
        (126, 'SHIM'),
        (130, 'SHON'),
        (122, 'URAW'),
        (136, 'KOBE'),
        (124, 'Y-FM'),
        (129, 'HIRO'),
        (135, 'FUKU'),
        (193, 'NIIG'),
        (30310, 'AKI'),
        (30314, 'OKAY'),
        (30532, 'MCD'),
        (30317, 'RYUK'),
        (30673, 'IWTE'),
        (121, 'CHIB'),
        (294, '<PERSON>AM<PERSON>'),
        (207, 'O<PERSON><PERSON>'),
        (199, 'OMIY'),
        (30851, 'R-YA'),
        (30303, 'KUMA'),
        (30103, 'GUN'),
        (30305, 'TOCH'),
        (30116, 'TOKU'),
        (123, 'TK-V'),
        (238, 'SEND'),
        (150, 'KOFU'),
        (30531, 'NGSK'),
        (296, 'Y-FC'),
        (30526, 'KANA'),
        (176, 'MITO'),
        (30674, 'FKSM'),
        (30676, 'SAGM'),
        (30535, 'NAGA'),
        (31296, 'NUMA'),
        (30148, 'EHIM'),
        (30302, 'GIFU'),
        (30974, 'IMAB'),
        (30677, 'FUJI'),
        (30000, 'TOTR'),
        (30313, 'KIKY'),
        (31201, 'IFC'),
        (31042, 'KUFC'),
        (30536, 'SANU'),
        (30308, 'TOYA'),
        (30528, 'MATS'),
        (31508, 'MYZK'),
        (31219, 'HACH'),
        (30675, 'YSCC'),
    )

    ClubExtraInfo = apps.get_model('clubs', 'ClubExtraInfo')

    for team_id, name in four_letter_names:
        ClubExtraInfo.objects.filter(team_id=team_id).update(four_letters_name=name)


class Migration(migrations.Migration):
    dependencies = [
        ('clubs', '0003_clubextrainfo_four_letters_name'),
    ]

    operations = [
        # delete non-japanese clubs
        migrations.RunSQL(
            sql="""
                DELETE FROM clubs_clubextrainfo
                WHERE team_id NOT IN (
                    133,270,128,276,131,120,132,86,134,127,269,126,130,122,136,124,129,135,193,
                    30310,30314,30532,30317,30673,121,294,207,199,30851,30303,30103,30305,30116,
                    123,238,150,30531,296,30526,176,30674,30676,30535,31296,30148,30302,30974,
                    30677,30000,30313,31201,31042,30536,30308,30528,31508,31219,30675
                );
            """
        ),
        migrations.RunPython(patch_four_latter_names),
    ]
