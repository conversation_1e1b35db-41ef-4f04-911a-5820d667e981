{% load core_tags %}

{% spaceless %}
  <a class="player-item">
    {% include 'components/player-photo.html' with player=data photo=data.avatar_image %}

    <div class="info">
      <div class="player-name">
        {% player_name data %}
      </div>

      <div class="nationality">
        <span class="flag">
          <img
            class="lazy"
            src="https://hatscripts.github.io/circle-flags/flags/{{ data.nationality_flag }}.svg"
            alt="{{ data.nationality_name }}"
            loading="lazy"
          />
        </span>

        <span class="name">
          {% translation_obj data.country_local_names %}
        </span>
      </div>
    </div>
  </a>
{% endspaceless %}
