header {
  .nav-container {
    position: relative;
    z-index: 999;
    background: linear-gradient(180deg, var(--color-black) 0%, transparentize($color-black, 0.9999) 100%);

    .navbar {
      margin: 24px auto 0;
      max-width: 1320px;
      display: flex;
      justify-content: center;
      align-items: center;
      column-gap: 20px;

      .navbar-item {
        font-family: var(--font-barlow-condensed);
        font-style: normal;
        font-weight: 500;
        font-size: 20px;
        line-height: 24px;
        letter-spacing: 2px;
        color: var(--color-white);
        text-transform: uppercase;
        padding: 3px 7px;
        text-align: center;
        white-space: nowrap;
        display: flex;
        align-items: center;
        column-gap: 4px;

        &.active {
          background: var(--color-white);
          border-radius: 1px;
          color: var(--color-black);
          font-weight: 700;
        }

        &:hover {
          opacity: 1;
        }

        &:hover:not(.active) {
          background: var(--color-white);
          border-radius: 1px;
          color: var(--color-black);
        }
      }

      .flag-icon {
        width: 24px;
        height: 24px;
      }
    }
  }

  @media screen and (max-width: 1439px) and (min-width: 1121px) {
    .nav-container {
      .navbar {
        padding: 0 64px;

        .navbar-item:nth-child(n + 8) {
          display: none;
        }

        .language-chooser {
          display: none;
        }
      }
    }
  }

  @media screen and (min-width: 1121px) {
    .nav-container {
      padding: 32px 0;

      .mobile-menu {
        display: none;
      }
    }
  }

  @media screen and (max-width: 1120px) {
    .nav-container {
      height: 88px;

      .navbar {
        display: none;
      }
    }
  }

  @media screen and (max-width: 768px) {
    .nav-container {
      height: 64px;
    }
  }
}

body.lang-id,
body.lang-th,
body.lang-vi {
  header .nav-container .navbar .navbar-item {
    letter-spacing: normal;
  }
}

body.lang-vi {
  header .nav-container .navbar .navbar-item {
    font-size: 18px;
  }
}

body.lang-th {
  header .nav-container .navbar .navbar-item {
    line-height: 1.5;
    padding-bottom: 0;
  }
}
