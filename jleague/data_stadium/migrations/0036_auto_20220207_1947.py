# Generated by Django 3.1.13 on 2022-02-07 10:47

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('data_stadium', '0035_playerphoto_team_slug'),
    ]

    operations = [
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='aerial_battles_won_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='aerial_battles_won_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='assist_count_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='assist_count_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='blocks_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='blocks_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='chances_created_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='chances_created_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='clean_sheet_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='corners_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='corners_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='crosses_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='crosses_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='distance_km_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='distance_km_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='dribbles_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='dribbles_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='duels_won_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='duels_won_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='fouls_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='fouls_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='freekicks_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='freekicks_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='goals_count_rank_league',
            field=models.IntegerField(null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='goals_count_rank_team',
            field=models.IntegerField(null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='goals_from_freekick_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='goals_from_freekick_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='goals_from_head_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='goals_from_head_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='goals_from_left_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='goals_from_left_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='goals_from_penalty_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='goals_from_penalty_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='goals_from_right_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='goals_from_right_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='interceptions_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='interceptions_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='passes_per_90_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='passes_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='passes_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='recoveries_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='recoveries_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='red_cards_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='red_cards_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='save_count_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='shots_count_rank_league',
            field=models.IntegerField(null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='shots_count_rank_team',
            field=models.IntegerField(null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='shots_on_target_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='shots_on_target_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='shots_per_90_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='sprint_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='sprint_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='tackle_won_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='tackle_won_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='through_balls_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='through_balls_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='top_speed_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='top_speed_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='yellow_cards_rank_league',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='yellow_cards_rank_team',
            field=models.IntegerField(blank=True, null=True),
        ),
    ]
