from django.db import migrations
from core.constants import FAKE_TEAM_ID


def create_fakes(apps, schema_editor):
    Team = apps.get_model('data_stadium', 'Team')

    Team.objects.create(
        year=2021,
        team_id=FAKE_TEAM_ID,
        name='Undecided',
        name_short='undecided',
        slug='undecided',
        home_stadium_id=0,  # Unknown stadium
    )


class Migration(migrations.Migration):
    dependencies = [
        ('data_stadium', '0012_game_schedule_no'),
    ]

    operations = [migrations.RunPython(create_fakes)]
