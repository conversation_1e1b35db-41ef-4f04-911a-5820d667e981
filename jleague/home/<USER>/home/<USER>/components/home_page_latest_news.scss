@import 'setup/variable';
@import 'setup/mixin';

.home__latest-news-container {
  .home__latest-news-content {
    background: var(--color-white);
    border-radius: 3px;
    overflow: hidden;
    display: flex;
    flex-direction: row;

    &.loading {
      min-height: 415px;
    }

    .home__latest-news-content__header {
      flex: 1;
      overflow: hidden;
      max-width: 680px;
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center;

      &.lazy {
        min-height: 100%;
      }
    }

    .home__latest-news-content__body {
      flex: 1;
      padding: 32px;
    }

    .home__latest-news-article {
      .home__latest-news-article__category {
        font-family: var(--font-barlow-condensed);
        font-style: normal;
        font-weight: 700;
        font-size: 20px;
        line-height: 24px;
        letter-spacing: 1.67px;
        color: var(--color-j1);
        position: relative;
        padding-bottom: 22px;
        margin-bottom: 16px;

        &::after {
          content: '';
          background: linear-gradient(90deg, rgba(255, 255, 255, 0.0001) 0%, var(--color-red) 99.69%);
          position: absolute;
          bottom: 0;
          left: 0;
          height: 6px;
          width: 60px;
        }
      }

      .home__latest-news-article__title {
        font-family: var(--font-overpass);
        font-style: normal;
        font-weight: 900;
        font-size: 28px;
        line-height: 36px;
        color: var(--color-black);
        margin-bottom: 16px;
        @include text_ellipsis(3, break-word);

        &:hover {
          text-decoration: underline;
          opacity: 1;
        }
      }

      .home__latest-news-article__content {
        font-family: var(--font-overpass);
        font-style: normal;
        font-weight: 400;
        font-size: 20px;
        line-height: 28px;
        color: var(--color-drawn);
        @include text_ellipsis(3, break-word);
      }

      .home__latest-news-article__read-more-button {
        display: inline-block;
        min-width: 100px;
        height: 48px;
        padding: 0 24px;
        background: var(--color-black);
        border-radius: 24px;
        border: none;
        // outline: none;
        font-family: var(--font-barlow-condensed);
        font-style: normal;
        font-weight: 600;
        font-size: 20px;
        line-height: 48px;
        letter-spacing: 1.67px;
        text-transform: uppercase;
        color: var(--color-white);
        cursor: pointer;
        margin-top: 32px;

        &:hover {
          background: var(--color-red);
          color: var(--color-white);
          opacity: 1;
        }
      }
    }
  }

  @media screen and (min-width: 1440px) {
    .home__latest-news-content {
      .home__latest-news-content__header {
        height: 454px;
      }
    }
  }

  @media screen and (max-width: 1439px) and (min-width: 1121px) {
    .home__latest-news-content {
      .home__latest-news-content__header {
        height: 414px;
      }
    }
  }

  @media screen and (max-width: 1120px) {
    .home__latest-news-content {
      flex-direction: column;

      .home__latest-news-content__header {
        flex: auto;
        width: 100%;
        max-width: none;
      }

      .home__latest-news-article {
        .home__latest-news-article__read-more-button {
          display: none;
        }
      }
    }
  }

  @media screen and (max-width: 1120px) and (min-width: 769px) {
    .home__latest-news-content {
      &.loading {
        min-height: 714px;
      }

      .home__latest-news-content__header {
        height: 452px;
      }
    }
  }

  @media screen and (min-width: 769px) {
    margin-top: 32px;
  }

  @media screen and (max-width: 768px) {
    margin-top: 24px;

    .home__latest-news-content {
      &.loading {
        min-height: 650px;
      }

      .home__latest-news-content__header {
        height: 354px;
      }
    }
  }

  @media screen and (max-width: 576px) {
    .home__latest-news-content {
      .home__latest-news-article {
        .home__latest-news-article__title {
          font-size: 24px;
          line-height: 28px;
        }

        .home__latest-news-article__content {
          font-size: 16px;
          line-height: 20px;
        }
      }
    }
  }
}

body.lang-vi {
  .home__latest-news-container .home__latest-news-content .home__latest-news-article .home__latest-news-article__title {
    font-weight: 600;
  }
}
