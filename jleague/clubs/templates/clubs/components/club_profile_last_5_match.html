{% load core_tags %}

<div class="stat-item last-5-matches">
  <div class="stat-item__body info-container">
    <div>{% translation 'CLUB_PROFILE_PAGE_LAST_5_MATCHES' %}</div>

    <div class="match-list">
      {% if not last_5_matches %}
        {% include 'components/data-empty-state.html' %}
      {% else %}
        {% for match in last_5_matches %}
          <a
            class="match-item no-decoration"
            href="{% url 'match_overview' match.competition.slug match.id %}"
            onclick="sendSelectContentEvent('match', '{{ match.competition.slug }}_{{ match.id }}');"
          >
            {% if match.home.slug == club.slug %}
              {% include 'components/club-emblem.html' with slug=match.away.slug %}

              <span class="team-name">
                {% club_name match.away %}
              </span>

              <div class="league-icon-container">
                {% include 'components/competition-logo.html' with slug=match.competition.slug year=match.year size='xsmall' compact=True %}
              </div>

              <span class="score">{{ match.away.score }}-{{ match.home.score }}</span>

              <div class="result {{ match.home.short_result }}">
                <span>{{ match.home.short_result }}</span>
              </div>

            {% else %}
              {% include 'components/club-emblem.html' with slug=match.home.slug %}

              <span class="team-name">
                {% club_name match.home %}
              </span>

              <div class="league-icon-container">
                {% include 'components/competition-logo.html' with slug=match.competition.slug year=match.year size='xsmall' compact=True %}
              </div>

              <span class="score">{{ match.home.score }}-{{ match.away.score }}</span>

              <div class="result {{ match.away.short_result }}">
                <span>{{ match.away.short_result }}</span>
              </div>
            {% endif %}
          </a>
        {% endfor %}
      {% endif %}
    </div>
  </div>

  <div class="stat-item__footer">
    {% if club %}
      <a
        class="jl-button"
        href="{% url 'fixtures_search_team' 'all' selected_year 'all' club.slug %}"
        onclick="sendSelectContentEvent('page', 'fixtures');"
      >
        <span class="jl-button__label">
          {% translation 'CLUB_PROFILE_PAGE_VIEW_ALL_RESULTS' %}
        </span>
      </a>
    {% endif %}
  </div>
</div>
