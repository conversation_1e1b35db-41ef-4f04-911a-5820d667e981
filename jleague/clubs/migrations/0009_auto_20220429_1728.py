# Generated by Django 3.1.13 on 2022-04-29 08:28

from django.db import migrations


def patch_club_extra_infos(apps, schema_editor):
    ClubExtraInfo = apps.get_model('clubs', 'ClubExtraInfo')
    TeamInfo = apps.get_model('home', 'TeamInfo')
    team_infos = TeamInfo.objects.filter(team_id__isnull=False)

    for team_info in team_infos:
        club_extra_infos = ClubExtraInfo.objects.filter(team_id=team_info.team.team_id)
        if club_extra_infos and len(club_extra_infos):
            for club_extra_info in club_extra_infos:
                club_extra_info.address = team_info.address
                club_extra_info.president = team_info.president
                club_extra_info.phone_number = team_info.phone_number
                club_extra_info.introduction = team_info.introduction
                club_extra_info.practice_ground = team_info.practice_ground
                club_extra_info.save()


class Migration(migrations.Migration):
    dependencies = [
        ('clubs', '0008_auto_20220429_1705'),
    ]

    operations = [
        migrations.RunPython(patch_club_extra_infos),
    ]
