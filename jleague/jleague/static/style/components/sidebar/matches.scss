@import 'setup/variable';
@import 'setup/mixin';

.sidebar-matches {
  &.loading {
    min-height: 480px;
  }

  .sidebar-matches__header {
  }
  .sidebar-matches__body {
  }

  .matchweek-nav {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 24px;

    .matchweek-nav__item {
      color: var(--color-black);
      font-family: var(--font-barlow-condensed);
      font-weight: 700;
      font-size: 14px;
      line-height: 16px;
      letter-spacing: 1.67;
      text-align: center;
      position: relative;
      display: flex;
      align-items: center;
      height: 32px;
      white-space: nowrap;

      &.matchweek-nav__item--current {
        cursor: default;
        color: var(--color-earth-4);
      }

      &.matchweek-nav__item--latest {
        color: var(--color-earth-4);
        padding: 4px 6px;
        text-transform: uppercase;
      }

      &.matchweek-nav__item--prev,
      &.matchweek-nav__item--next {
        padding: 4px;

        i {
          color: var(--color-black);
          font-size: 24px;
        }
      }

      &.matchweek-nav__item--prev {
        i {
          margin-right: 2px;
        }
      }

      &.matchweek-nav__item--next {
        i {
          margin-left: 2px;
        }
      }

      &:hover:not(.matchweek-nav__item--current) {
        background: var(--color-red);
        border-radius: 40px;
        color: var(--color-white);
        cursor: pointer;

        i {
          color: var(--color-white);
        }
      }
    }

    &.disabled {
      cursor: not-allowed;

      .matchweek-nav__item {
        pointer-events: none;
      }
    }
  }

  .matchweek-group {
    .matchweek-group__date {
      color: var(--color-black);
      font-family: var(--font-overpass);
      font-style: normal;
      font-weight: 900;
      font-size: 16px;
      line-height: 24px;
      letter-spacing: 0px;
      margin-bottom: 8px;
    }

    .matchweek-group__games {
      overflow-x: auto;
    }

    & + .matchweek-group {
      margin-top: 8px;
    }
  }
}

.sidebar-match-card {
  display: flex;
  gap: 6px;
  overflow: hidden;
  padding: 8px;
  position: relative;
  border-radius: 8px;
  backdrop-filter: blur(12px);

  .clubs {
    width: 100%;

    .clubs__item {
      display: flex;
      gap: 4px;
      align-items: center;

      .club-emblem {
        width: 32px;
        height: 32px;
      }

      .club-emblem--tokushima-alt {
        display: none;
      }

      .club-emblem--tokyov-alt {
        display: none;
      }

      .club-name {
        color: var(--color-black);
        font-family: var(--font-barlow-condensed);
        font-style: normal;
        font-weight: 500;
        font-size: 16px;
        line-height: normal;
        flex: 1;
        @include text_ellipsis();
        padding: 3px 0;
      }

      .club-score {
        color: var(--color-black);
        font-family: var(--font-jleague-kick);
        font-size: 20px;
        font-weight: 700;
        line-height: 24px;
        letter-spacing: 0px;
        text-align: center;
        flex: 0 0 12px;
      }
    }
  }

  .situation {
    color: var(--color-earth-4);
    font-family: var(--font-barlow-condensed);
    font-style: normal;
    font-weight: 700;
    font-size: 14px;
    line-height: 16px;
    letter-spacing: 1px;
    text-transform: uppercase;
    width: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .match-detail-situation {
      color: var(--color-drawn);
    }

    .ytlive-icon {
      display: flex;
      height: 24px;
      width: 24px;
    }

    &.situation--live {
      .match-detail-situation {
        color: var(--color-lost);
        font-family: var(--font-jleague-kick);
        font-size: 16px;
        line-height: 20px;
      }
    }
  }

  &:hover {
    opacity: 1;

    &:not(.sidebar-match-card--selected) {
      background: transparentize($color-white, 0.2);
      cursor: pointer;
    }
  }

  &.sidebar-match-card--selected {
    background: var(--color-black);

    .clubs {
      .clubs__item {
        .club-emblem.club-emblem--tokushima {
          display: none;
        }

        .club-emblem--tokushima-alt {
          display: block;
        }

        .club-emblem.club-emblem--tokyov {
          display: none;
        }

        .club-emblem--tokyov-alt {
          display: block;
        }
        .club-name,
        .club-score {
          color: var(--color-white);
        }
      }
    }

    .situation {
      color: var(--color-earth-3);

      .match-detail-situation {
        color: var(--color-earth-3);
      }
    }
  }
}

.match-info-modal {
  display: none;
  width: 360px;
  flex-direction: column;
  align-items: center;
  border-radius: 40px;
  background: transparentize($color-black, 0.6);
  border: 1px solid transparentize($color-white, 0.7);
  -webkit-backdrop-filter: blur(64px);
  backdrop-filter: blur(64px);
  overflow: hidden;
  padding: 24px;

  .match-summary {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .score-board {
      .score-board__score {
        height: 32px;
        width: 54px;

        .score-board__score__away,
        .score-board__score__home,
        .score-board__score__divider {
          font-size: 24px;
          line-height: 24px;
        }

        .score-board__score__divider {
          margin: 0 3px;
        }
      }

      .score-board__team {
        height: auto;

        .team-info {
          height: auto;

          .club-emblem {
            height: 32px;
            width: 32px;
          }
        }

        &.score-board__team--away {
          padding-left: 30px;
        }

        &.score-board__team--home {
          padding-right: 30px;
        }
      }
    }

    .summary-details {
      display: flex;
      gap: 16px;

      .summary-events {
        flex: 1;

        .summary-events__item {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          gap: 4px;

          .event-player-name {
            color: var(--color-white);
            font-family: var(--font-barlow-condensed);
            font-style: normal;
            font-weight: 500;
            font-size: 16px;
            line-height: 1;
            @include text_ellipsis();
          }

          .event-time {
            color: transparentize($color-white, 0.4);
            font-family: var(--font-barlow-condensed);
            font-style: normal;
            font-weight: 600;
            font-size: 14px;
            line-height: 16px;
            letter-spacing: 0.4px;
          }

          .match-event-icon {
            margin: 0;
          }

          & + .summary-events__item {
            margin-top: 8px;
          }
        }

        &.summary-events--home {
          .summary-events__item {
            .event-player-name,
            .event-time {
              text-align: right;
            }
          }
        }

        &.summary-events--away {
          .summary-events__item {
            flex-direction: row-reverse;

            .event-player-name,
            .event-time {
              text-align: left;
            }
          }
        }
      }
    }
  }

  .match-live {
    .match-live__body {
      iframe {
        width: 312px;
        height: auto;
        aspect-ratio: 16/9;
      }
    }

    &:has(.match-live__body) {
      margin-top: 24px;
    }
  }

  .match-highlights {
    position: relative;
    // margin-top: 24px;
    width: 100%;
    height: auto;

    .videos-list {
      .video-item {
        border-radius: 16px;
      }

      &:has(.video-item) {
        margin-top: 24px;
      }
    }
  }

  &[data-show] {
    display: flex;
  }

  &.loading {
    min-height: 120px;
  }
}

body.lang-th {
  .sidebar-matches .matchweek-group .matchweek-group__date {
    font-weight: 700;
  }
}

body.lang-vi {
  .sidebar-matches .matchweek-group .matchweek-group__date {
    font-weight: 600;
  }
}

@media screen and (max-width: 576px) {
  .sidebar-match-card {
    backdrop-filter: none;
  }
}
