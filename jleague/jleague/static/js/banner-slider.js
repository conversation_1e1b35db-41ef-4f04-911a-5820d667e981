const bannerSwiper = (function () {
  const POSITIONS = {
    TOP: 'top',
    BOTTOM: 'bottom',
    MIDDLE: 'middle',
  };
  const BANNER_SELECTORS = {
    TOP: '.banners--top',
    BOTTOM: '.banners--middle',
    MIDDLE: '.banners--bottom',
  };

  let swiper;

  function _getSelector(position) {
    switch (position) {
      case POSITIONS.TOP:
        return BANNER_SELECTORS.TOP;
      case POSITIONS.BOTTOM:
        return BANNER_SELECTORS.BOTTOM;
      case POSITIONS.MIDDLE:
        return BANNER_SELECTORS.MIDDLE;
      default:
        return;
    }
  }

  function init(position) {
    swiper = new Swiper(_getSelector(position), {
      slidesPerView: 'auto',
      centeredSlides: true,
      effect: 'coverflow',
      coverflowEffect: {
        rotate: 0,
        stretch: 16,
        depth: 0,
        modifier: 1,
        slideShadows: true,
      },
      pagination: {
        el: ".swiper-pagination",
        clickable: true,
      },
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
        pauseOnMouseEnter: true,
        // waitForTransition: false,
      },
      breakpoints: {
        // when window width is >= 320px
        320: {
          spaceBetween: 24,
        },
        // when window width is >= 577
        577: {
          spaceBetween: 32,
        },
      },
      on: {
        // init: function (s) {},
        click: function (s, e) {
          const target = e.target;
          if (target.classList.contains('swiper-slide-prev')) {
            swiper.slidePrev();
          } else if (target.classList.contains('swiper-slide-next')) {
            swiper.slideNext();
          }
        },
      },
    });
    return swiper;
  }

  return { init };
})();

bannerSwiper.init('top')
