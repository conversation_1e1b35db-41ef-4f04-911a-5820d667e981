@import 'setup/variable';
@import 'setup/mixin';

.player-rank-board {
  flex: 1;
  border-top: 1px solid var(--color-j1);
  transition: all 0.5s ease-in-out;
  min-width: 300px;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .player-rank-board__header {
    .player-rank-board__header__title {
      margin-top: 24px;
      color: var(--color-black);
      font-family: var(--font-overpass);
      font-style: normal;
      font-weight: 900;
      font-size: 28px;
      line-height: 32px;
    }
  }

  .player-rank-board__body {
    margin-top: 24px;
    flex: 1;
  }

  .ranking-columns,
  .ranking-item {
    padding: 0 16px;
    border-bottom: 1px solid var(--color-earth-2);
    height: 96px;
    display: flex;
    align-items: center;

    .rank {
      flex: 0 0 auto;
      width: 16px;
      text-align: center;
      color: var(--color-earth-4);
      font-family: var(--font-jleague-kick);
      font-style: normal;
      font-weight: 700;
      font-size: 16px;
      line-height: 20px;
    }

    .player-photo {
      flex: 0 0 auto;
      margin-left: 12px;
      width: 64px;
      height: auto;
      border-radius: 32px;
    }

    .info {
      flex: auto;
      margin: 0 12px;

      .player {
        .player-name {
          color: var(--color-black);
          font-family: var(--font-overpass);
          font-style: normal;
          font-weight: 900;
          font-size: 18px;
          line-height: 20px;
          @include text_ellipsis();

          &:hover {
            opacity: 1;

            &[href] {
              text-decoration: underline;
            }
          }
        }
      }

      .team {
        margin-top: 4px;
        display: flex;
        align-items: center;

        .club-emblem {
          flex: 0 0 auto;
          margin-right: 4px;
          width: 32px;
          height: 32px;
        }

        .team-name {
          flex: auto;
          color: var(--color-earth-4);
          font-family: var(--font-jleague-kick);
          font-style: normal;
          font-weight: 700;
          font-size: 16px;
          line-height: 16px;
          @include text_ellipsis();

          &:hover {
            opacity: 1;

            &[href] {
              text-decoration: underline;
            }
          }
        }
      }
    }

    .value {
      flex: 0 0 auto;
      color: var(--color-black);
      font-family: var(--font-jleague-kick);
      font-style: normal;
      font-weight: 700;
      font-size: 64px;
      line-height: 64px;
    }

    &.ranking-item--winner {
      position: relative;
      background: var(--color-black);

      .rank {
        color: var(--color-earth-3);
      }

      .info {
        .player {
          .player-name {
            color: var(--color-white);
          }
        }

        .team {
          .team-name {
            color: var(--color-earth-3);
          }
        }
      }

      .value {
        color: var(--color-white);
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 8px;
        background: linear-gradient(90deg, rgba(26, 25, 25, 0.0001) 0%, var(--color-red 99.69%));
      }
    }

    &.ranking-item--distance,
    &.ranking-item--sprints {
      .value {
        font-size: 32px;
        line-height: 32px;
      }
    }
  }

  .ranking-columns {
    height: auto;
    border: none;

    .rank,
    .info,
    .value {
      color: var(--color-earth-4);
      font-family: var(--font-jleague-kick);
      font-weight: 700;
      font-size: 14px;
      line-height: 20px;
      text-transform: uppercase;
    }

    .tooltiptext {
      background-color: var(--color-black);
      color: var(--color-white);
      font-family: var(--font-jleague-kick);
      text-align: center;
      padding: 3px 5px 1px;
      border-radius: 4px;
    }
  }

  @media screen and (max-width: 1199px) {
    .ranking-item {
      height: 72px;

      .player-photo {
        width: 40px;
      }

      .info {
        .player {
          .player-name {
            font-size: 16px;
          }
        }

        .team {
          .club-emblem {
            display: none;
          }
        }
      }

      .value {
        font-size: 32px;
        line-height: 32px;
      }
    }
  }

  @media screen and (max-width: 767px) {
    .player-rank-board__header {
      .player-rank-board__header__title {
        font-size: 20px;
        line-height: 24px;
      }
    }
  }
}

body.lang-th {
  .player-rank-board {
    .player-rank-board__header .player-rank-board__header__title {
      font-weight: 700;
      line-height: 1.5;
    }

    .ranking-item .info {
      .player .player-name,
      .team .team-name {
        font-weight: 700;
        line-height: 1.5;
      }
    }
  }
}

body.lang-vi {
  .player-rank-board {
    .player-rank-board__header .player-rank-board__header__title {
      font-weight: 600;
    }

    .ranking-item .info {
      .player .player-name,
      .team .team-name {
        font-weight: 600;
      }
    }
  }
}
