import aioftp
import logging
import xml.etree.ElementTree as ET
from decimal import Decimal
from ..helpers import (
    async_find_first,
    async_upsert,
    process_path_if_needed,
    read_file_to_str,
    xml_value,
)
from clubs.models import Club
from core.models import Competition
from match.models import Game
from stats.models import GameClubStats

logger = logging.getLogger(__name__)


async def import_game_club_stat_distance(ftp_client: aioftp.Client, file_path: str, file_stat: dict):
    """Import game club stats:distance from DS
    The file which outputs the top 20 clubs that have the highest distance stat on each game of the season.
    source file: ranking/rank_tracking_distance_team-{COMPETITION_ID}.xml
    """
    async with process_path_if_needed(file_path, file_stat) as should_process:
        if not should_process:
            return

        # read XML file
        file_content = await read_file_to_str(ftp_client, file_path)
        root = ET.fromstring(file_content)  # Football node
        report_node = root.find('RankTrackingDistanceReport')
        year = xml_value(report_node, 'Year', int)
        competition_id = xml_value(report_node, 'GameKindID', int)

        # check competition exists
        competition = await async_find_first(Competition, id=competition_id)
        if not competition:
            logger.warn('Competition not found (id=%s)', competition_id)
            return

        for rank_info in report_node.findall('RankInfo'):
            club_id = xml_value(rank_info, 'TeamID', int)
            game_id = xml_value(rank_info, 'GameID', int)

            # check game exists
            game = await async_find_first(Game, id=game_id)
            if not game:
                logger.warn('Game not found (id=%s)', game_id)
                continue

            # check club exists
            club = await async_find_first(Club, ds_club_id=club_id)
            if not club:
                logger.warn('Club not found (ds_club_id=%s)', club_id)
                continue

            distance = xml_value(rank_info, 'Distance', Decimal)

            # update game club stats:distance
            await async_upsert(
                GameClubStats, year=year, competition=competition, club=club, game=game, defaults={'distance': distance}
            )
            logger.info(
                'Processed club game stats:distance (year=%s, game_id=%s, ds_club_id=%s)', year, game_id, club_id
            )


async def import_game_club_stat_sprints(ftp_client: aioftp.Client, file_path: str, file_stat: dict):
    """Import game club stats:sprints from DS
    The file which outputs the top 20 clubs that have the highest sprints stat on each game of the season.
    source file: ranking/rank_tracking_sprint_team-{COMPETITION_ID}.xml
    """
    async with process_path_if_needed(file_path, file_stat) as should_process:
        if not should_process:
            return

        # read XML file
        file_content = await read_file_to_str(ftp_client, file_path)
        root = ET.fromstring(file_content)  # Football node
        report_node = root.find('RankTrackingSprintReport')
        year = xml_value(report_node, 'Year', int)
        competition_id = xml_value(report_node, 'GameKindID', int)

        # check competition exists
        competition = await async_find_first(Competition, id=competition_id)
        if not competition:
            logger.warn('Competition not found (id=%s)', competition_id)
            return

        for rank_info in report_node.findall('RankInfo'):
            club_id = xml_value(rank_info, 'TeamID', int)
            game_id = xml_value(rank_info, 'GameID', int)

            # check game exists
            game = await async_find_first(Game, id=game_id)
            if not game:
                logger.warn('Game not found (id=%s)', game_id)
                continue

            # check club exists
            club = await async_find_first(Club, ds_club_id=club_id)
            if not club:
                logger.warn('Club not found (ds_club_id=%s)', club_id)
                continue

            sprints = xml_value(rank_info, 'SprintCnt', int)

            # update game club stats:sprints
            await async_upsert(
                GameClubStats, year=year, competition=competition, club=club, game=game, defaults={'sprints': sprints}
            )
            logger.info(
                'Processed club game stats:sprints (year=%s, game_id=%s, ds_club_id=%s)', year, game_id, club_id
            )
