<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 260 80">
  <defs>
    <style>
      .cls-1 {
        fill: url(#radial-gradient-6);
        opacity: .05;
      }

      .cls-2 {
        fill: url(#radial-gradient-2);
      }

      .cls-2, .cls-3 {
        opacity: .2;
      }

      .cls-3 {
        fill: url(#radial-gradient-3);
      }

      .cls-4 {
        fill: url(#radial-gradient-5);
      }

      .cls-5 {
        fill: url(#radial-gradient-4);
      }

      .cls-6 {
        fill: url(#linear-gradient-8);
      }

      .cls-7 {
        fill: url(#linear-gradient-7);
      }

      .cls-8 {
        fill: url(#linear-gradient-5);
      }

      .cls-9 {
        fill: url(#linear-gradient-6);
      }

      .cls-10 {
        fill: url(#linear-gradient-9);
      }

      .cls-11 {
        fill: url(#linear-gradient-4);
      }

      .cls-12 {
        fill: url(#linear-gradient-3);
      }

      .cls-13 {
        fill: url(#linear-gradient-2);
      }

      .cls-14 {
        fill: url(#linear-gradient);
      }

      .cls-15 {
        fill: url(#radial-gradient);
      }

      .cls-15, .cls-16 {
        opacity: .6;
      }

      .cls-17 {
        fill: #fff;
      }

      .cls-18 {
        fill: url(#linear-gradient-39);
        opacity: .8;
      }

      .cls-16 {
        fill: url(#linear-gradient-11);
      }

      .cls-19 {
        fill: url(#linear-gradient-38);
        opacity: .4;
      }

      .cls-20 {
        fill: url(#linear-gradient-43);
      }

      .cls-21 {
        fill: url(#linear-gradient-40);
      }

      .cls-22 {
        fill: url(#linear-gradient-41);
      }

      .cls-23 {
        fill: url(#linear-gradient-42);
      }

      .cls-24 {
        fill: url(#linear-gradient-45);
      }

      .cls-25 {
        fill: url(#linear-gradient-44);
      }

      .cls-26 {
        fill: url(#linear-gradient-46);
      }

      .cls-27 {
        fill: url(#linear-gradient-10);
      }

      .cls-28 {
        fill: url(#linear-gradient-12);
      }

      .cls-29 {
        fill: url(#linear-gradient-13);
      }

      .cls-30 {
        fill: url(#linear-gradient-19);
      }

      .cls-31 {
        fill: url(#linear-gradient-14);
      }

      .cls-32 {
        fill: url(#linear-gradient-21);
      }

      .cls-33 {
        fill: url(#linear-gradient-20);
      }

      .cls-34 {
        fill: url(#linear-gradient-22);
      }

      .cls-35 {
        fill: url(#linear-gradient-23);
      }

      .cls-36 {
        fill: url(#linear-gradient-28);
      }

      .cls-37 {
        fill: url(#linear-gradient-25);
      }

      .cls-38 {
        fill: url(#linear-gradient-32);
      }

      .cls-39 {
        fill: url(#linear-gradient-31);
      }

      .cls-40 {
        fill: url(#linear-gradient-33);
      }

      .cls-41 {
        fill: url(#linear-gradient-30);
      }

      .cls-42 {
        fill: url(#linear-gradient-15);
      }

      .cls-43 {
        fill: url(#linear-gradient-16);
      }

      .cls-44 {
        fill: url(#linear-gradient-17);
      }

      .cls-45 {
        fill: url(#linear-gradient-18);
      }

      .cls-46 {
        fill: url(#linear-gradient-35);
      }

      .cls-47 {
        fill: url(#linear-gradient-34);
      }

      .cls-48 {
        fill: url(#linear-gradient-36);
      }

      .cls-49 {
        fill: url(#linear-gradient-27);
      }

      .cls-50 {
        fill: url(#linear-gradient-24);
      }

      .cls-51 {
        fill: url(#linear-gradient-26);
      }

      .cls-52 {
        fill: url(#linear-gradient-29);
      }

      .cls-53 {
        fill: url(#linear-gradient-37);
      }

      .cls-54 {
        fill: #af9870;
      }
    </style>
    <linearGradient id="linear-gradient" x1="24.09" y1="1.85" x2="24.09" y2="77.21" gradientUnits="userSpaceOnUse">
      <stop offset=".28" stop-color="#5e4c8f"/>
      <stop offset=".29" stop-color="#645395"/>
      <stop offset=".33" stop-color="#7869a9"/>
      <stop offset=".37" stop-color="#8477b5"/>
      <stop offset=".4" stop-color="#897cba"/>
      <stop offset=".48" stop-color="#9b92c6"/>
      <stop offset=".57" stop-color="#897cba"/>
      <stop offset=".59" stop-color="#8376b4"/>
      <stop offset=".67" stop-color="#5e4c8f"/>
      <stop offset=".89" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="6179.63" y1="11.49" x2="6189.75" y2="28.97" gradientTransform="translate(6225.16) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".06" stop-color="#cfd2d2"/>
      <stop offset=".28" stop-color="#897cba"/>
      <stop offset="1" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="6182.41" y1="8.22" x2="6192.24" y2="37.3" gradientTransform="translate(6225.16) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".08" stop-color="#cfd2d2"/>
      <stop offset=".28" stop-color="#5e4c8f"/>
      <stop offset=".38" stop-color="#5e4c8f"/>
      <stop offset=".55" stop-color="#897cba"/>
      <stop offset=".61" stop-color="#6e5d9f"/>
      <stop offset=".65" stop-color="#5e4c8f"/>
      <stop offset="1" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="6187.92" y1="5.8" x2="6191.63" y2="40.45" gradientTransform="translate(6225.16) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".05" stop-color="#cfd2d2"/>
      <stop offset=".28" stop-color="#5e4c8f"/>
      <stop offset=".38" stop-color="#5e4c8f"/>
      <stop offset=".55" stop-color="#897cba"/>
      <stop offset=".61" stop-color="#6e5d9f"/>
      <stop offset=".65" stop-color="#5e4c8f"/>
      <stop offset="1" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="2.64" y1="11.49" x2="12.77" y2="28.97" gradientTransform="matrix(1,0,0,1,0,0)" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-6" x1="5.42" y1="8.22" x2="15.26" y2="37.3" gradientTransform="matrix(1,0,0,1,0,0)" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-7" x1="10.94" y1="5.8" x2="14.65" y2="40.45" gradientTransform="matrix(1,0,0,1,0,0)" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-8" x1="24.09" y1="2.91" x2="24.09" y2="56.26" gradientUnits="userSpaceOnUse">
      <stop offset=".1" stop-color="#cfd2d2"/>
      <stop offset=".22" stop-color="#5e4c8f"/>
      <stop offset=".32" stop-color="#5e4c8f"/>
      <stop offset=".42" stop-color="#897cb9"/>
      <stop offset=".46" stop-color="#9789c0"/>
      <stop offset=".51" stop-color="#a99aca"/>
      <stop offset=".54" stop-color="#9a8cc2"/>
      <stop offset=".59" stop-color="#897cb9"/>
      <stop offset=".78" stop-color="#100133"/>
    </linearGradient>
    <radialGradient id="radial-gradient" cx="-310.57" cy="-98.93" fx="-310.57" fy="-98.93" r="65.52" gradientTransform="translate(-107.36 337.15) rotate(90) scale(.93 1.32)" gradientUnits="userSpaceOnUse">
      <stop offset=".46" stop-color="#ae9770" stop-opacity="0"/>
      <stop offset=".49" stop-color="#c1ab83" stop-opacity=".24"/>
      <stop offset=".57" stop-color="#fdedc0"/>
      <stop offset=".85" stop-color="#fdedc0"/>
      <stop offset=".91" stop-color="#dcc99f" stop-opacity=".59"/>
      <stop offset=".97" stop-color="#bba57d" stop-opacity=".17"/>
      <stop offset="1" stop-color="#ae9770" stop-opacity="0"/>
    </radialGradient>
    <radialGradient id="radial-gradient-2" cx="24.22" cy="49.92" fx="24.22" fy="49.92" r="19.89" gradientUnits="userSpaceOnUse">
      <stop offset=".32" stop-color="#fff" stop-opacity="0"/>
      <stop offset=".35" stop-color="#fff" stop-opacity=".25"/>
      <stop offset=".47" stop-color="#fff"/>
      <stop offset=".51" stop-color="#fff" stop-opacity=".58"/>
      <stop offset=".56" stop-color="#fff" stop-opacity=".17"/>
      <stop offset=".58" stop-color="#fff" stop-opacity="0"/>
    </radialGradient>
    <linearGradient id="linear-gradient-9" x1="24.09" y1="49.05" x2="24.09" y2="83.26" gradientUnits="userSpaceOnUse">
      <stop offset=".08" stop-color="#a99aca" stop-opacity=".5"/>
      <stop offset=".34" stop-color="#5e4c8f" stop-opacity="0"/>
    </linearGradient>
    <radialGradient id="radial-gradient-3" cx="24.22" cy="44.97" fx="24.22" fy="44.97" r="26.16" gradientUnits="userSpaceOnUse">
      <stop offset=".37" stop-color="#fff" stop-opacity="0"/>
      <stop offset=".4" stop-color="#fff" stop-opacity=".25"/>
      <stop offset=".48" stop-color="#fff"/>
      <stop offset=".53" stop-color="#fef7e4" stop-opacity=".58"/>
      <stop offset=".57" stop-color="#fdf0ca" stop-opacity=".17"/>
      <stop offset=".59" stop-color="#fdedc0" stop-opacity="0"/>
    </radialGradient>
    <linearGradient id="linear-gradient-10" x1="0" y1="11.91" x2="48.17" y2="11.91" gradientUnits="userSpaceOnUse">
      <stop offset=".05" stop-color="#a99aca"/>
      <stop offset=".07" stop-color="#a495c5"/>
      <stop offset=".09" stop-color="#9687b8"/>
      <stop offset=".11" stop-color="#8070a3"/>
      <stop offset=".13" stop-color="#615084"/>
      <stop offset=".15" stop-color="#39285e"/>
      <stop offset=".16" stop-color="#301e55"/>
      <stop offset=".28" stop-color="#5f4f8a"/>
      <stop offset=".4" stop-color="#897cb9"/>
      <stop offset=".41" stop-color="#8c81bc"/>
      <stop offset=".5" stop-color="#a9b0d6"/>
      <stop offset=".51" stop-color="#a5aad2"/>
      <stop offset=".6" stop-color="#897cb9"/>
      <stop offset=".72" stop-color="#594a83"/>
      <stop offset=".84" stop-color="#301e55"/>
      <stop offset=".85" stop-color="#39285e"/>
      <stop offset=".87" stop-color="#615084"/>
      <stop offset=".89" stop-color="#8070a3"/>
      <stop offset=".91" stop-color="#9687b8"/>
      <stop offset=".93" stop-color="#a495c5"/>
      <stop offset=".95" stop-color="#a99aca"/>
    </linearGradient>
    <linearGradient id="linear-gradient-11" x1="0" y1="11.91" x2="48.17" y2="11.91" gradientUnits="userSpaceOnUse">
      <stop offset=".18" stop-color="#ae9770" stop-opacity="0"/>
      <stop offset=".43" stop-color="#d5ba82"/>
      <stop offset=".57" stop-color="#d5b981"/>
      <stop offset=".82" stop-color="#ae9770" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-12" x1=".1" y1="13.06" x2="48.08" y2="13.06" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#34215b"/>
      <stop offset=".17" stop-color="#5e4c8f"/>
      <stop offset=".5" stop-color="#7769ae"/>
      <stop offset=".83" stop-color="#5e4c8f"/>
      <stop offset="1" stop-color="#34215b"/>
    </linearGradient>
    <radialGradient id="radial-gradient-4" cx="1180.14" cy="-147.22" fx="1180.14" fy="-147.22" r="100.16" gradientTransform="translate(-127.18 -1103.36) rotate(75.74) scale(.93 -1.03)" gradientUnits="userSpaceOnUse">
      <stop offset=".12" stop-color="#cfd2d2"/>
      <stop offset=".16" stop-color="#c0c0cc"/>
      <stop offset=".29" stop-color="#988fbe"/>
      <stop offset=".35" stop-color="#897cb9"/>
      <stop offset=".41" stop-color="#71639e"/>
      <stop offset=".51" stop-color="#504078"/>
      <stop offset=".61" stop-color="#36255b"/>
      <stop offset=".71" stop-color="#231245"/>
      <stop offset=".81" stop-color="#180639"/>
      <stop offset=".9" stop-color="#150335"/>
    </radialGradient>
    <radialGradient id="radial-gradient-5" cx="4558.11" cy="-147.22" fx="4558.11" fy="-147.22" r="100.16" gradientTransform="translate(952.26 -4160.64) rotate(104.26) scale(.93 1.03)" xlink:href="#radial-gradient-4"/>
    <linearGradient id="linear-gradient-13" x1="11.44" y1="32.1" x2="16.79" y2="22.92" gradientUnits="userSpaceOnUse">
      <stop offset=".6" stop-color="#cfd2d2" stop-opacity="0"/>
      <stop offset=".68" stop-color="#948caf" stop-opacity=".52"/>
      <stop offset=".75" stop-color="#6d5d97" stop-opacity=".87"/>
      <stop offset=".78" stop-color="#5e4c8f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-14" x1="10.91" y1="70.39" x2="37.23" y2="70.8" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#100133"/>
      <stop offset=".36" stop-color="#6c5d90"/>
      <stop offset=".48" stop-color="#a091c5"/>
      <stop offset=".52" stop-color="#a091c5"/>
      <stop offset=".73" stop-color="#433467"/>
      <stop offset=".85" stop-color="#100133"/>
    </linearGradient>
    <linearGradient id="linear-gradient-15" x1="10.3" y1="66.99" x2="23.05" y2="73.33" gradientUnits="userSpaceOnUse">
      <stop offset=".1" stop-color="#fdedc0"/>
      <stop offset=".29" stop-color="#d5b981"/>
      <stop offset=".59" stop-color="#fdedc0" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-16" x1="-3144.11" y1="66.99" x2="-3131.36" y2="73.33" gradientTransform="translate(-3106.24) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-15"/>
    <linearGradient id="linear-gradient-17" x1="17.95" y1="63.26" x2="30.22" y2="63.26" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#5e4c8f"/>
      <stop offset=".18" stop-color="#645395"/>
      <stop offset=".29" stop-color="#7869a9"/>
      <stop offset=".4" stop-color="#8477b4"/>
      <stop offset=".5" stop-color="#897cb9"/>
      <stop offset=".54" stop-color="#8376b3"/>
      <stop offset=".85" stop-color="#5e4c8f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-18" x1="24.09" y1="-3.76" x2="24.09" y2="54.26" gradientUnits="userSpaceOnUse">
      <stop offset=".17" stop-color="#5e4c8f"/>
      <stop offset="1" stop-color="#261749"/>
    </linearGradient>
    <linearGradient id="linear-gradient-19" x1="24.09" y1="20.77" x2="24.09" y2="59.06" gradientUnits="userSpaceOnUse">
      <stop offset=".44" stop-color="#4c3679"/>
      <stop offset=".99" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-20" x1="4.87" y1="11.4" x2="4.87" y2="42.67" gradientUnits="userSpaceOnUse">
      <stop offset=".34" stop-color="#d5b981"/>
      <stop offset=".62" stop-color="#fdedc0"/>
      <stop offset=".8" stop-color="#d5b981"/>
      <stop offset=".94" stop-color="#ae9770"/>
    </linearGradient>
    <linearGradient id="linear-gradient-21" x1=".12" y1="39.46" x2="17.37" y2="39.46" gradientUnits="userSpaceOnUse">
      <stop offset=".02" stop-color="#fdedc0"/>
      <stop offset=".11" stop-color="#e6e0c8"/>
      <stop offset=".22" stop-color="#d5d5cf"/>
      <stop offset=".29" stop-color="#cfd2d2"/>
      <stop offset=".44" stop-color="#d5b981"/>
      <stop offset=".55" stop-color="#bfa677"/>
      <stop offset=".66" stop-color="#ae9770"/>
      <stop offset="1" stop-color="#fdedc0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-22" x1="6177.11" y1="39.46" x2="6194.35" y2="39.46" gradientTransform="translate(6225.16) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-21"/>
    <linearGradient id="linear-gradient-23" x1="8.01" y1="11.94" x2="8.01" y2="38.06" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#d5b981"/>
      <stop offset=".05" stop-color="#dfc690"/>
      <stop offset=".12" stop-color="#ecd7a5"/>
      <stop offset=".2" stop-color="#f5e3b4"/>
      <stop offset=".29" stop-color="#fbeabd"/>
      <stop offset=".44" stop-color="#fdedc0"/>
      <stop offset=".48" stop-color="#e6d6be"/>
      <stop offset=".55" stop-color="#beafbc"/>
      <stop offset=".62" stop-color="#a093bb"/>
      <stop offset=".68" stop-color="#8f82ba"/>
      <stop offset=".72" stop-color="#897cba"/>
    </linearGradient>
    <linearGradient id="linear-gradient-24" x1="9.09" y1="10.32" x2="9.09" y2="43.76" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#897cba"/>
      <stop offset=".03" stop-color="#9585b0"/>
      <stop offset=".08" stop-color="#b59f98"/>
      <stop offset=".12" stop-color="#d5b981"/>
      <stop offset=".13" stop-color="#dbc18b"/>
      <stop offset=".17" stop-color="#edd9a8"/>
      <stop offset=".2" stop-color="#f9e7b9"/>
      <stop offset=".22" stop-color="#fdedc0"/>
      <stop offset=".53" stop-color="#897cba"/>
      <stop offset=".56" stop-color="#7264a0"/>
      <stop offset=".62" stop-color="#56467f"/>
      <stop offset=".67" stop-color="#3e2e64"/>
      <stop offset=".73" stop-color="#2b1a4f"/>
      <stop offset=".8" stop-color="#1e0d40"/>
      <stop offset=".88" stop-color="#170537"/>
      <stop offset="1" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-25" x1="11.53" y1="8.89" x2="11.53" y2="59.79" xlink:href="#linear-gradient-24"/>
    <linearGradient id="linear-gradient-26" x1="15.28" y1="8.98" x2="15.28" y2="76.66" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#897cba"/>
      <stop offset=".03" stop-color="#9585b0"/>
      <stop offset=".08" stop-color="#b59f98"/>
      <stop offset=".12" stop-color="#d5b981"/>
      <stop offset=".13" stop-color="#dbc18b"/>
      <stop offset=".17" stop-color="#edd9a8"/>
      <stop offset=".2" stop-color="#f9e7b9"/>
      <stop offset=".22" stop-color="#fdedc0"/>
      <stop offset=".43" stop-color="#897cba"/>
      <stop offset=".45" stop-color="#7264a0"/>
      <stop offset=".48" stop-color="#56467f"/>
      <stop offset=".52" stop-color="#3e2e64"/>
      <stop offset=".55" stop-color="#2b1a4f"/>
      <stop offset=".59" stop-color="#1e0d40"/>
      <stop offset=".64" stop-color="#170537"/>
      <stop offset=".71" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-27" x1="6184.99" y1="11.94" x2="6184.99" y2="38.06" gradientTransform="translate(6225.16) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-23"/>
    <linearGradient id="linear-gradient-28" x1="6186.07" y1="10.32" x2="6186.07" y2="43.76" gradientTransform="translate(6225.16) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-24"/>
    <linearGradient id="linear-gradient-29" x1="6188.51" y1="8.89" x2="6188.51" y2="59.79" gradientTransform="translate(6225.16) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-24"/>
    <linearGradient id="linear-gradient-30" x1="6192.27" y1="8.98" x2="6192.27" y2="76.66" gradientTransform="translate(6225.16) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#897cba"/>
      <stop offset=".03" stop-color="#9585b0"/>
      <stop offset=".08" stop-color="#b59f98"/>
      <stop offset=".12" stop-color="#d5b981"/>
      <stop offset=".13" stop-color="#dbc18b"/>
      <stop offset=".17" stop-color="#edd9a8"/>
      <stop offset=".2" stop-color="#f9e7b9"/>
      <stop offset=".22" stop-color="#fdedc0"/>
      <stop offset=".43" stop-color="#897cba"/>
      <stop offset=".45" stop-color="#7264a0"/>
      <stop offset=".48" stop-color="#56467f"/>
      <stop offset=".52" stop-color="#3e2e64"/>
      <stop offset=".55" stop-color="#2b1a4f"/>
      <stop offset=".59" stop-color="#1e0d40"/>
      <stop offset=".64" stop-color="#170537"/>
      <stop offset=".71" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-31" x1="19.09" y1="26.37" x2="19.09" y2="49.93" gradientUnits="userSpaceOnUse">
      <stop offset=".1" stop-color="#fdedc0"/>
      <stop offset=".19" stop-color="#ecdbaf"/>
      <stop offset=".44" stop-color="#bfaa81"/>
      <stop offset=".57" stop-color="#ae9770"/>
      <stop offset=".58" stop-color="#a89172"/>
      <stop offset=".62" stop-color="#8d787c"/>
      <stop offset=".67" stop-color="#786484"/>
      <stop offset=".73" stop-color="#69568a"/>
      <stop offset=".79" stop-color="#604e8d"/>
      <stop offset=".9" stop-color="#5e4c8f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-32" x1="19.94" y1="30.67" x2="19.94" y2="64.42" gradientUnits="userSpaceOnUse">
      <stop offset=".1" stop-color="#fdedc0"/>
      <stop offset=".19" stop-color="#ecdbaf"/>
      <stop offset=".45" stop-color="#bfaa81"/>
      <stop offset=".59" stop-color="#ae9770"/>
      <stop offset=".69" stop-color="#7e695d"/>
      <stop offset=".9" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-33" x1="29.09" y1="26.37" x2="29.09" y2="49.93" xlink:href="#linear-gradient-31"/>
    <linearGradient id="linear-gradient-34" x1="28.23" y1="30.67" x2="28.23" y2="64.42" xlink:href="#linear-gradient-32"/>
    <linearGradient id="linear-gradient-35" x1="31.97" y1="63.26" x2="21.31" y2="63.26" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#897cb9"/>
      <stop offset=".66" stop-color="#3a295f"/>
      <stop offset="1" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-36" x1="6188.42" y1="32.1" x2="6193.77" y2="22.92" gradientTransform="translate(6225.16) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-13"/>
    <linearGradient id="linear-gradient-37" x1="6181.85" y1="11.4" x2="6181.85" y2="42.67" gradientTransform="translate(6225.16) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".48" stop-color="#d5b981"/>
      <stop offset=".62" stop-color="#fdedc0"/>
      <stop offset=".8" stop-color="#d5b981"/>
      <stop offset=".94" stop-color="#ae9770"/>
    </linearGradient>
    <linearGradient id="linear-gradient-38" x1="7308.58" y1="73.71" x2="7334.96" y2="73.71" gradientTransform="translate(7345.86) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#150335"/>
      <stop offset=".04" stop-color="#251448"/>
      <stop offset=".14" stop-color="#493970"/>
      <stop offset=".24" stop-color="#655690"/>
      <stop offset=".33" stop-color="#786ba6"/>
      <stop offset=".42" stop-color="#8477b4"/>
      <stop offset=".5" stop-color="#897cb9"/>
      <stop offset=".64" stop-color="#8679b6"/>
      <stop offset=".72" stop-color="#7f72ae"/>
      <stop offset=".79" stop-color="#7365a0"/>
      <stop offset=".85" stop-color="#61528b"/>
      <stop offset=".91" stop-color="#4a3a71"/>
      <stop offset=".96" stop-color="#2e1d52"/>
      <stop offset="1" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-39" x1="18.5" y1="62.1" x2="29.19" y2="68.61" gradientUnits="userSpaceOnUse">
      <stop offset=".08" stop-color="#e1ca93"/>
      <stop offset=".43" stop-color="#9688b3"/>
      <stop offset=".5" stop-color="#897cba"/>
      <stop offset=".73" stop-color="#3a295f"/>
      <stop offset=".86" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-40" x1="24.09" y1="36.89" x2="24.09" y2="62.48" xlink:href="#linear-gradient-19"/>
    <linearGradient id="linear-gradient-41" x1="20.84" y1="37.96" x2="20.84" y2="62.47" gradientUnits="userSpaceOnUse">
      <stop offset=".1" stop-color="#fdedc0"/>
      <stop offset=".4" stop-color="#e1c894"/>
      <stop offset=".57" stop-color="#d5b981"/>
      <stop offset=".59" stop-color="#c9ae82"/>
      <stop offset=".63" stop-color="#a89086"/>
      <stop offset=".69" stop-color="#8d7789"/>
      <stop offset=".75" stop-color="#78648b"/>
      <stop offset=".81" stop-color="#69568d"/>
      <stop offset=".88" stop-color="#604e8e"/>
      <stop offset="1" stop-color="#5e4c8f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-42" x1="5545.03" y1="37.96" x2="5545.03" y2="62.47" gradientTransform="translate(5572.36) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-41"/>
    <linearGradient id="linear-gradient-43" x1="24.09" y1="48.23" x2="24.09" y2="64.31" gradientUnits="userSpaceOnUse">
      <stop offset=".02" stop-color="#150335"/>
      <stop offset=".08" stop-color="#301e55"/>
      <stop offset=".22" stop-color="#3c2a64"/>
      <stop offset=".63" stop-color="#5e4c8f"/>
      <stop offset=".68" stop-color="#513f7f"/>
      <stop offset=".83" stop-color="#301e55"/>
      <stop offset=".98" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-44" x1="22.07" y1="44.46" x2="22.07" y2="59.79" xlink:href="#linear-gradient-41"/>
    <linearGradient id="linear-gradient-45" x1="-768.27" y1="44.46" x2="-768.27" y2="59.79" gradientTransform="translate(-742.18) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-41"/>
    <linearGradient id="linear-gradient-46" x1="24.09" y1="2.05" x2="24.09" y2="58.37" gradientUnits="userSpaceOnUse">
      <stop offset=".53" stop-color="#9b92c6" stop-opacity="0"/>
      <stop offset=".59" stop-color="#7f74a8" stop-opacity=".2"/>
      <stop offset=".85" stop-color="#150335"/>
    </linearGradient>
    <radialGradient id="radial-gradient-6" cx="24.12" cy="38.2" fx="24.12" fy="38.2" r="38.74" gradientTransform="translate(56.01 13.75) rotate(89.41) scale(1 .84)" gradientUnits="userSpaceOnUse">
      <stop offset=".42" stop-color="#fff" stop-opacity="0"/>
      <stop offset=".43" stop-color="#fff" stop-opacity=".25"/>
      <stop offset=".46" stop-color="#fff"/>
      <stop offset=".48" stop-color="#fff" stop-opacity=".58"/>
      <stop offset=".5" stop-color="#fff" stop-opacity=".17"/>
      <stop offset=".51" stop-color="#fff" stop-opacity="0"/>
    </radialGradient>
  </defs>
  <g>
    <g>
      <polygon class="cls-17" points="78.25 20.52 78.25 18.33 70.93 18.33 70.93 28.46 73.54 28.46 73.54 25.25 73.24 24.41 76.98 24.41 76.98 22.36 73.54 22.36 73.54 20.52 78.25 20.52"/>
      <path class="cls-17" d="M81.44,27.96c-.86-.42-1.53-1.02-2-1.8-.47-.78-.7-1.7-.7-2.75,0-1.05.23-1.97.7-2.75.47-.78,1.13-1.39,2-1.81.86-.42,1.86-.63,3-.63.75,0,1.42.07,2,.21.59.14,1.13.37,1.63.67v2.39c-.44-.33-.93-.57-1.49-.72-.55-.15-1.19-.23-1.91-.23-1.03,0-1.82.25-2.38.76-.56.51-.84,1.21-.84,2.11,0,.9.28,1.61.85,2.12.56.51,1.35.77,2.37.77.72,0,1.36-.08,1.93-.24.57-.16,1.1-.42,1.59-.77v2.38c-.95.62-2.2.93-3.75.93-1.14,0-2.14-.21-3-.62"/>
      <path class="cls-17" d="M63.15,24.41l1.31-3.41,1.3,3.4h-2.6ZM70.04,28.46l-4.26-10.12h-3.53l.35,1.24-3.73,8.88h2.77l.51-1.24-.29-.83h4.56s.85,2.07.85,2.07h2.76Z"/>
    </g>
    <g>
      <path class="cls-17" d="M85,45.66h-3.87v-5.68h-4.47v5.68h-3.87v-13.93h3.87v5.17h4.47v-5.17h3.87v13.93Z"/>
      <path class="cls-17" d="M61.74,44.99c-1.19-.58-2.11-1.4-2.76-2.48-.65-1.08-.97-2.35-.97-3.8,0-1.45.32-2.72.97-3.8.65-1.08,1.57-1.91,2.76-2.5,1.19-.58,2.57-.88,4.14-.88,1.03,0,1.96.1,2.77.29.81.2,1.56.5,2.25.92v3.3c-.61-.46-1.29-.79-2.06-1-.76-.21-1.64-.31-2.63-.31-1.42,0-2.51.35-3.28,1.05-.77.7-1.16,1.67-1.16,2.92,0,1.25.39,2.22,1.17,2.93.78.71,1.87,1.06,3.28,1.06.99,0,1.88-.11,2.67-.34.78-.22,1.52-.58,2.19-1.06v3.28c-1.31.85-3.04,1.28-5.18,1.28-1.57,0-2.95-.29-4.14-.86"/>
      <path class="cls-17" d="M91.96,40.09l1.8-4.69,1.78,4.68h-3.58ZM101.43,45.66l-5.85-13.93h-4.86l.49,1.71-5.13,12.22h3.81l.7-1.71-.4-1.14h6.28s1.17,2.85,1.17,2.85h3.79Z"/>
      <path class="cls-17" d="M125.35,37.92c-.39.27-.94.41-1.65.41h-2.03v-3.63h2.03c.73,0,1.28.13,1.66.39.38.26.57.74.57,1.43s-.19,1.13-.58,1.41M129.25,39.17c.48-.72.69-1.62.69-2.65,0-1.54-.47-2.73-1.4-3.55-.93-.82-2.23-1.24-3.88-1.24h-6.33v13.93h3.67v-3.2l-.39-1.15h3.15c.81,0,1.8-.14,2.56-.5.98-.47,1.66-1.22,1.94-1.64"/>
      <polygon class="cls-17" points="135.58 31.73 130.93 31.73 131.4 33.44 131.41 43.94 130.93 45.65 135.57 45.65 135.09 43.94 135.1 33.44 135.58 31.73"/>
      <path class="cls-17" d="M136.56,38.62c0-3.9,3.39-7.07,7.65-7.07s7.68,3.17,7.68,7.07-3.37,7.07-7.68,7.07-7.65-3.15-7.65-7.07ZM148.3,38.62c0-2.11-1.76-3.85-4.08-3.85s-4.04,1.73-4.04,3.85,1.68,3.83,4.04,3.83,4.08-1.73,4.08-3.83Z"/>
      <path class="cls-17" d="M101.43,31.73h3.45l4.09,5.21,4.22-5.21h3.3v13.93h-3.53v-8.84l-4.01,4.94-3.97-4.92v8.82h-3.55v-13.93Z"/>
      <polygon class="cls-17" points="165.67 45.56 165.67 31.73 161.98 31.73 161.99 39.96 156.64 31.73 153 31.73 153 45.56 156.69 45.56 156.68 37.31 162.05 45.56 165.67 45.56"/>
      <path class="cls-17" d="M167.93,42.71l-1.35,2.85h6.09c1.87,0,3.27-.35,4.21-1.04,1-.74,1.55-2,1.3-3.21-.59-2.85-4.07-3.36-6.38-4.72-.39-.23-.58-.52-.58-.89,0-.37.16-.65.54-.85.38-.2.92-.3,1.56-.3h3.33l1.03-2.82h-5.03c-1.04,0-1.96.11-2.76.44-.8.33-1.42.79-1.87,1.39-.45.6-.67,1.28-.67,2.04,0,.93.29,1.71.88,2.34.63.68,1.57,1.08,2.41,1.44,1.09.47,2.35.84,3.32,1.54.22.16.43.42.43.69,0,.31-.08.53-.29.69-.67.52-1.59.42-2.41.42h-3.77Z"/>
    </g>
    <g>
      <polygon class="cls-17" points="188.26 42.38 188.26 31.34 184.51 31.34 184.51 45.56 184.66 45.56 188.26 45.56 194.83 45.56 194.83 42.38 188.26 42.38"/>
      <polygon class="cls-17" points="196.09 45.55 206.37 45.55 206.37 42.52 199.76 42.52 199.76 39.85 204.59 39.85 204.59 37.02 199.76 37.02 199.76 34.35 206.37 34.35 206.37 31.34 196.09 31.34 196.09 45.55"/>
      <path class="cls-17" d="M212.37,39.87l1.84-4.78,1.82,4.78h-3.66ZM222.04,45.56l-5.98-14.22h-4.96l.5,1.75-5.24,12.47h3.89l.71-1.75-.41-1.17h6.41s1.2,2.91,1.2,2.91h3.87Z"/>
      <path class="cls-17" d="M241.76,45.77c1.78,0,3.79-.5,4.89-1.64,1.1-1.13,1.64-2.65,1.64-4.55v-8.24h-3.64v8.28c0,.95-.25,1.67-.76,2.15-.51.49-1.21.73-2.12.73s-1.61-.24-2.12-.73c-.51-.49-.76-1.21-.76-2.15v-8.28h-3.64v8.24c0,1.9.54,3.42,1.64,4.55,1.1,1.14,3.11,1.64,4.89,1.64"/>
      <polygon class="cls-17" points="249.71 45.55 259.99 45.55 259.99 42.52 253.38 42.52 253.38 39.85 258.21 39.85 258.21 37.02 253.38 37.02 253.38 34.35 259.99 34.35 259.99 31.34 249.71 31.34 249.71 45.55"/>
      <path class="cls-17" d="M234.28,38.83h-3.62v2.95l.84.49s-.33.13-.87.21c-.42.05-.87.08-1.34.08-1.42,0-2.52-.36-3.31-1.08-.79-.72-1.18-1.71-1.18-2.98,0-1.27.39-2.26,1.17-2.97.78-.71,1.89-1.07,3.32-1.07,1,0,1.89.11,2.66.32.77.21,1.46.55,2.08,1.02v-3.36c-.7-.43-1.46-.74-2.28-.94-.82-.2-1.75-.3-2.8-.3-1.59,0-2.98.3-4.19.89-1.2.59-2.13,1.44-2.79,2.54-.65,1.1-.98,2.39-.98,3.87,0,1.48.33,2.77.98,3.87.65,1.1,1.58,1.94,2.79,2.53,1.2.59,2.6.88,4.19.88,2.17,0,4-.45,5.32-1.32v-.03s0,0,0,0v-5.59Z"/>
    </g>
    <g>
      <g>
        <polygon class="cls-54" points="86.37 49.71 81.6 49.71 82.09 51.45 82.09 62.15 81.61 63.9 86.36 63.9 85.87 62.15 85.88 51.45 86.37 49.71"/>
        <polygon class="cls-54" points="74.11 60.74 74.11 49.76 70.39 49.76 70.39 63.9 70.53 63.9 74.11 63.9 80.64 63.9 80.64 60.74 74.11 60.74"/>
        <polygon class="cls-54" points="58.88 63.9 69.1 63.9 69.1 60.88 62.52 60.88 62.52 58.23 67.33 58.23 67.33 55.41 62.52 55.41 62.52 52.76 69.1 52.76 69.1 49.76 58.88 49.76 58.88 63.9"/>
        <polygon class="cls-54" points="100.06 49.71 87.43 49.71 87.43 52.99 91.83 52.99 91.83 63.9 95.73 63.9 95.73 52.99 100.06 52.99 100.06 49.71"/>
        <polygon class="cls-54" points="101.35 63.9 111.61 63.9 111.61 60.87 105.01 60.87 105.01 58.21 109.83 58.21 109.83 55.38 105.01 55.38 105.01 52.72 111.61 52.72 111.61 49.71 101.35 49.71 101.35 63.9"/>
      </g>
      <path class="cls-54" d="M115.99,52.05h-.41v-2.35h.36l.77,1.04.72-.99h.41v2.3h-.45v-1.58l-.63.86h-.09l-.68-.86v1.58ZM114.45,50.16v1.9h-.45v-1.9h-.63v-.45h1.76v.45h-.68Z"/>
    </g>
  </g>
  <g>
    <path class="cls-14" d="M31.61,54.79c.13-4.79-1.4-9.37-2.81-14.85-1.68-6.52-3.41-11.74-4.25-13.62,0,0,0,0,0,0-.25-.49-.41-.75-.46-.75s-.21.26-.46.75h0s0,0,0,0c-.84,1.88-2.57,7.1-4.25,13.62-1.42,5.48-2.94,10.06-2.81,14.85h0c.12.63.28,1.35.51,2.06.78,2.44,1.76,4.26,3.09,4.82.91.38,3.49.58,3.92.58s3.01-.2,3.92-.58c1.33-.56,2.31-2.37,3.09-4.82.23-.71.39-1.43.51-2.06h0ZM24.53,62.21s-.03,0-.04,0c-.13,0-.26.01-.4.01s-.27,0-.4-.01c-.01,0-.03,0-.04,0-3.56-.22-5.69-3.07-5.69-7.18,0-3.06,1.01-5.96,2.15-9.45,1.71-5.22,3.82-9.1,3.99-9.1s2.28,3.88,3.99,9.1c1.14,3.48,2.15,6.38,2.15,9.45,0,4.11-2.13,6.96-5.69,7.18ZM18.21,55.72c0-2.99,1.2-6.04,2.28-9.44,1.64-5.19,3.34-9.25,3.6-9.25s1.96,4.06,3.6,9.25c1.08,3.41,2.28,6.45,2.28,9.44,0,3.31-2.17,6.5-5.88,6.5-3.71,0-5.88-3.19-5.88-6.5ZM21.35,56.68c0-4.97,2.58-8.56,2.73-8.56s2.73,3.59,2.73,8.56c0,2.38-.65,5.54-2.73,5.54-2.08,0-2.73-3.16-2.73-5.54ZM24.09,13.9c.22,0,4.63,7.4,6.76,17.98,1.37,6.8,1.09,5.61,1.07,18.17h0c-.24-4.09-1.5-8.02-2.78-12.72-2.13-7.82-4.83-13.62-5.05-13.62s-2.92,5.8-5.05,13.62c-1.28,4.7-2.55,8.63-2.78,12.72h0c-.02-12.56-.3-11.36,1.07-18.17,2.13-10.58,6.55-17.98,6.76-17.98Z"/>
    <path class="cls-13" d="M35.67,28.74c.54-1.56,1.15-3.1,1.81-4.57,2.46-5.43,5.66-6.68,7.18-7.09-.03-.44-.06-.87-.09-1.29-.08-.39-.34-.77-.78-1.12-1.2.4-4.25,1.86-6.08,6.65-.8,2.1-.9,3.77-2.03,7.43Z"/>
    <path class="cls-12" d="M34.39,32.88c.37-1.36.8-2.74,1.28-4.12h0c1.13-3.68,1.23-5.35,2.04-7.45,1.83-4.79,4.88-6.25,6.08-6.65-.76-.62-2.04-1.2-3.73-1.69-.57.35-2.82,1.92-4.08,5.77-1.63,4.97-.39,8.87-1.59,14.14Z"/>
    <path class="cls-11" d="M33.23,37.79c.29-1.52.68-3.18,1.15-4.9h0c1.2-5.28-.04-9.17,1.59-14.15,1.26-3.85,3.52-5.43,4.08-5.77-1.66-.48-3.71-.89-6.07-1.18-.16.24-.94,1.46-1.26,3.1-.71,3.62.07,6.61.79,11.91.33,2.44.21,6.41-.29,11Z"/>
    <path class="cls-8" d="M12.5,28.74c-.54-1.56-1.15-3.1-1.81-4.57-2.46-5.43-5.66-6.68-7.18-7.09.03-.44.06-.87.09-1.29.08-.39.34-.77.78-1.12,1.2.4,4.25,1.86,6.08,6.65.8,2.1.9,3.77,2.03,7.43Z"/>
    <path class="cls-9" d="M13.79,32.88c-.37-1.36-.8-2.74-1.28-4.12h0c-1.13-3.68-1.23-5.35-2.04-7.45-1.83-4.79-4.88-6.25-6.08-6.65.76-.62,2.04-1.2,3.73-1.69.57.35,2.82,1.92,4.08,5.77,1.63,4.97.39,8.87,1.59,14.14Z"/>
    <path class="cls-7" d="M14.94,37.79c-.29-1.52-.68-3.18-1.15-4.9h0c-1.2-5.28.04-9.17-1.59-14.15-1.26-3.85-3.52-5.43-4.08-5.77,1.66-.48,3.71-.89,6.07-1.18.16.24.94,1.46,1.26,3.1.71,3.62-.07,6.61-.79,11.91-.33,2.44-.21,6.41.29,11Z"/>
    <path class="cls-6" d="M33.52,26.79c.13.92.19,2.05.19,3.36,0,2.16-.17,4.78-.48,7.65-.27,1.42-.47,2.72-.57,3.82-.15,1.56-.29,3.45-.41,5.22-.12,1.78-.23,3.44-.29,4.54,0-.45-.01-.89-.04-1.34.02-12.56.3-11.36-1.07-18.17-2.13-10.58-6.55-17.98-6.76-17.98s-4.63,7.4-6.76,17.98c-.95,4.71-1.11,5.59-1.11,9.85,0,1.89.03,4.45.04,8.31-.03.44-.04.89-.04,1.34-.13-2.2-.41-6.65-.71-9.76-.1-1.1-.3-2.4-.57-3.82h0c-.5-4.59-.62-8.57-.29-11.01.72-5.29,1.5-8.29.79-11.91-.32-1.63-1.1-2.86-1.26-3.1,2.91-.37,6.27-.58,9.9-.58s6.99.21,9.9.58c-.16.24-.94,1.46-1.26,3.1-.71,3.62.07,6.61.79,11.91Z"/>
    <path class="cls-15" d="M33.23,37.8c-.27,1.42-.47,2.72-.57,3.82-.15,1.56-.29,3.45-.41,5.22-.12,1.78-.23,3.44-.29,4.54-.01.1-.02.2-.03.28,0,.11,0,.21,0,.31-.03.47-.04.74-.04.74,0,0-.12,2.09-.78,4.14-.78,2.44-1.76,4.26-3.09,4.82-.91.38-3.49.58-3.92.58s-3.01-.2-3.92-.58c-1.33-.56-2.31-2.37-3.09-4.82-.66-2.05-.78-4.14-.78-4.14,0,0,0-.07-.01-.19,0-.13-.02-.31-.03-.54,0-.12,0-.24,0-.36-.01-.07-.02-.15-.03-.23-.13-2.2-.41-6.65-.71-9.76-.1-1.1-.3-2.4-.57-3.82h0s0,0,0,0c-.29-1.52-.68-3.18-1.15-4.89h0s0-.01,0-.02c-.37-1.35-.8-2.74-1.28-4.11h0s0-.02,0-.03c-.54-1.56-1.15-3.1-1.81-4.57-2.46-5.43-5.66-6.68-7.18-7.09.03-.44.06-.87.09-1.29.08-.39.34-.77.78-1.12.76-.62,2.04-1.2,3.73-1.69,1.66-.48,3.71-.89,6.07-1.18,2.91-.37,6.27-.58,9.9-.58s6.99.21,9.9.58c2.36.3,4.41.7,6.07,1.18,1.69.49,2.97,1.06,3.73,1.69.44.36.71.74.78,1.12.03.42.06.85.09,1.29-1.52.41-4.72,1.66-7.18,7.09-.66,1.46-1.27,3.01-1.81,4.57,0,0,0,.01,0,.02h0c-.48,1.39-.9,2.77-1.28,4.12,0,0,0,0,0,.01h0c-.47,1.72-.86,3.38-1.15,4.9h0Z"/>
    <path class="cls-2" d="M21.35,56.68c0-4.97,2.58-8.56,2.73-8.56s2.73,3.59,2.73,8.56c0,2.38-.65,5.54-2.73,5.54-2.08,0-2.73-3.16-2.73-5.54Z"/>
    <path class="cls-10" d="M21.35,56.68c0-4.97,2.58-8.56,2.73-8.56s2.73,3.59,2.73,8.56c0,2.38-.65,5.54-2.73,5.54-2.08,0-2.73-3.16-2.73-5.54Z"/>
    <path class="cls-3" d="M27.69,46.28c-1.64-5.19-3.34-9.25-3.6-9.25s-1.96,4.06-3.6,9.25c-1.08,3.41-2.28,6.45-2.28,9.44,0,3.31,2.17,6.5,5.88,6.5s5.88-3.19,5.88-6.5c0-2.99-1.2-6.04-2.28-9.44ZM27.29,59.5c-.51,1.46-1.46,2.73-3.2,2.73s-2.67-1.24-3.18-2.69c-.62-.94-.87-2.49-.84-4.24.07-4.02,3.85-7.36,4.02-7.47h0s.03.03.07.06c0,0,0,0,0,0,.56.48,3.88,3.65,3.95,7.41.03,1.73-.22,3.26-.82,4.2Z"/>
    <path class="cls-27" d="M24.09,10.34c13.21,0,23.99,2.44,23.99,5.45l.1-2.29c0-3.02-10.82-5.47-24.09-5.47C10.82,8.02,0,10.47,0,13.5l.1,2.29c0-3.01,10.78-5.45,23.99-5.45Z"/>
    <path class="cls-16" d="M24.09,10.34c13.21,0,23.99,2.44,23.99,5.45l.1-2.29c0-3.02-10.82-5.47-24.09-5.47C10.82,8.02,0,10.47,0,13.5l.1,2.29c0-3.01,10.78-5.45,23.99-5.45Z"/>
    <path class="cls-28" d="M24.09,11.21c11.3,0,19.98,2.03,20.48,4.58h3.51c0-3.01-10.78-5.45-23.99-5.45C10.87,10.34.1,12.78.1,15.79h3.51c.5-2.55,9.18-4.58,20.48-4.58Z"/>
    <path class="cls-5" d="M17.37,57.7c-.5-.53-2.31-2.49-5.11-5.77-3.07-3.61-6.54-9.41-9.08-15.95C1.06,30.53-.27,23.14.1,15.79h3.51c-.36,5.46-.92,12.06,1.24,19.27,2.16,7.21,9.65,14.99,11.41,16.74,0,.06,0,.12,0,.18.03.47.04.74.04.74,0,0,.12,2.09.78,4.14.09.29.19.57.29.85Z"/>
    <path class="cls-4" d="M30.81,57.7c.5-.53,2.31-2.49,5.11-5.77,3.07-3.61,6.54-9.41,9.08-15.95,2.12-5.46,3.45-12.84,3.08-20.19h-3.51c.36,5.46.92,12.06-1.24,19.27-2.16,7.21-9.65,14.99-11.41,16.74,0,.06,0,.12,0,.18-.03.47-.04.74-.04.74,0,0-.12,2.09-.78,4.14-.09.29-.19.57-.29.85Z"/>
    <path class="cls-29" d="M17.37,57.7c-.5-.53-2.31-2.49-5.11-5.77-3.07-3.61-6.54-9.41-9.08-15.95C1.06,30.53-.27,23.14.1,15.79h3.51c-.36,5.46-.92,12.06,1.24,19.27,2.16,7.21,9.65,14.99,11.41,16.74,0,.06,0,.12,0,.18.03.47.04.74.04.74,0,0,.12,2.09.78,4.14.09.29.19.57.29.85Z"/>
    <path class="cls-31" d="M24.09,74.08h13.19s0-.64,0-.64c0,0,0-.12-.15-.12h-.33s-2.95-5.86-2.95-5.86c0,0-.19-.41-.42-.41s-9.34,0-9.34,0h-9.34c-.23,0-.42.41-.42.41l-2.95,5.86h-.33c-.14,0-.15.12-.15.12v.64h13.19Z"/>
    <path class="cls-42" d="M24.09,67.06h-9.34c-.23,0-.42.41-.42.41l-2.95,5.86h-.33c-.14,0-.15.12-.15.12v.64h13.19v-7.03Z"/>
    <path class="cls-43" d="M24.09,67.06h0s9.11,0,9.34,0,.42.41.42.41l2.95,5.86h.33c.14,0,.15.12.15.12v.64h-13.19s0,0,0,0v-7.03Z"/>
    <path class="cls-44" d="M30.22,65.01h-12.27l1.89-3.51c.11.06.22.12.33.17.91.38,3.49.58,3.92.58s3.01-.2,3.92-.58c.11-.05.22-.1.33-.17l1.89,3.51Z"/>
    <path class="cls-45" d="M24.09,13.9c.22,0,4.63,7.4,6.76,17.98.71,3.52.98,4.9,1.07,7.15h0c.22-1.71.01-5.41-.49-7.77-2.31-10.92-7.11-18.55-7.34-18.55s-5.03,7.63-7.34,18.55c-.5,2.36-.7,6.06-.49,7.77h0c.09-2.25.36-3.63,1.07-7.15,2.13-10.58,6.55-17.98,6.76-17.98"/>
    <path class="cls-30" d="M24.09,23.7c.22,0,2.92,5.8,5.05,13.62,1.42,5.21,2.82,9.47,2.82,14.05-.01.22-.03.42-.04.6h0c0-4.36-1.44-8.49-2.86-13.45-2.13-7.44-4.76-12.97-4.98-12.97s-2.85,5.52-4.98,12.97c-1.42,4.96-2.86,9.09-2.86,13.45h0c-.01-.17-.02-.37-.04-.6,0-4.58,1.4-8.85,2.82-14.05,2.13-7.82,4.83-13.62,5.05-13.62Z"/>
    <path class="cls-33" d="M4.84,35.06c.5,1.68,1.29,3.38,2.23,5.03-1.09-1.58-2.07-3.49-2.77-5.8-2.05-6.83-1.76-13.13-1.4-18.5h.7c-.36,5.46-.92,12.06,1.24,19.27Z"/>
    <path class="cls-32" d="M17.37,57.7c-.5-.53-2.31-2.49-5.11-5.77-3.07-3.61-6.54-9.41-9.08-15.95C1.58,31.88.44,26.67.12,21.22c.47,5.16,1.63,10.03,3.19,13.95,2.7,6.8,6.35,12.83,9.59,16.57,1.7,1.97,3.06,3.48,4,4.5.06.21.12.41.18.62.09.29.19.57.29.85Z"/>
    <path class="cls-34" d="M30.81,57.7c.5-.53,2.31-2.49,5.11-5.77,3.07-3.61,6.54-9.41,9.08-15.95,1.6-4.11,2.74-9.31,3.06-14.77-.47,5.16-1.63,10.03-3.19,13.95-2.7,6.8-6.35,12.83-9.59,16.57-1.7,1.97-3.06,3.48-4,4.5-.06.21-.12.41-.18.62-.09.29-.19.57-.29.85Z"/>
    <path class="cls-35" d="M12.5,28.74c-.54-1.56-1.15-3.1-1.81-4.57-2.46-5.43-5.66-6.68-7.18-7.09.01-.18.02-.36.04-.54,1.63.52,4.99,1.96,7.29,7.53.53,1.29,1.02,2.64,1.46,4,0,0,0,.01,0,.02.06.21.13.42.2.65Z"/>
    <path class="cls-50" d="M13.79,32.88c-.37-1.36-.8-2.74-1.28-4.12h0c-1.13-3.68-1.23-5.35-2.04-7.45-1.83-4.79-4.88-6.25-6.08-6.65.14-.11.29-.22.45-.33,1.6.67,4.33,2.29,5.92,6.44.84,2.2.94,3.95,2.13,7.79h0c.15.44.29.87.43,1.3.09.97.23,1.97.47,3.03Z"/>
    <path class="cls-37" d="M14.94,37.79c-.29-1.52-.68-3.18-1.15-4.9h0c-1.2-5.28.04-9.17-1.59-14.15-1.26-3.85-3.52-5.43-4.08-5.77.45-.13.93-.26,1.44-.37.94.96,2.11,2.53,2.9,4.96,1.73,5.29.73,9.68,1.69,15.03h0c.18.64.34,1.27.49,1.89.08,1.06.18,2.17.3,3.31Z"/>
    <path class="cls-51" d="M15.18,26.67c-.37,2.43-.29,6.41.14,11.01h0c.25,1.43.43,2.73.52,3.83.25,3.11.27,7.68.37,9.87-.13-2.2-.41-6.65-.71-9.76-.1-1.1-.3-2.4-.57-3.82h0c-.5-4.59-.62-8.57-.29-11.01.72-5.29,1.5-8.29.79-11.91-.32-1.63-1.1-2.86-1.26-3.1.25-.03.5-.06.76-.09.2.31.91,1.51,1.19,3.07.66,3.63-.16,6.61-.95,11.89Z"/>
    <path class="cls-49" d="M35.67,28.74c.54-1.56,1.15-3.1,1.81-4.57,2.46-5.43,5.66-6.68,7.18-7.09-.01-.18-.02-.36-.04-.54-1.63.52-4.99,1.96-7.29,7.53-.53,1.29-1.02,2.64-1.46,4,0,0,0,.01,0,.02-.06.21-.13.42-.2.65Z"/>
    <path class="cls-36" d="M34.39,32.88c.37-1.36.8-2.74,1.28-4.12h0c1.13-3.68,1.23-5.35,2.04-7.45,1.83-4.79,4.88-6.25,6.08-6.65-.14-.11-.29-.22-.45-.33-1.6.67-4.33,2.29-5.92,6.44-.84,2.2-.94,3.95-2.13,7.79h0c-.15.44-.29.87-.43,1.3-.09.97-.23,1.97-.47,3.03Z"/>
    <path class="cls-52" d="M33.24,37.79c.29-1.52.68-3.18,1.15-4.9h0c1.2-5.28-.04-9.17,1.59-14.15,1.26-3.85,3.52-5.43,4.08-5.77-.45-.13-.93-.26-1.44-.37-.94.96-2.11,2.53-2.9,4.96-1.73,5.29-.73,9.68-1.69,15.03h0c-.18.64-.34,1.27-.49,1.89-.08,1.06-.18,2.17-.3,3.31Z"/>
    <path class="cls-41" d="M32.99,26.67c.37,2.43.29,6.41-.14,11.01h0c-.25,1.43-.43,2.73-.52,3.83-.25,3.11-.27,7.68-.37,9.87.13-2.2.41-6.65.71-9.76.1-1.1.3-2.4.57-3.82h0c.5-4.59.62-8.57.29-11.01-.72-5.29-1.5-8.29-.79-11.91.32-1.63,1.1-2.86,1.26-3.1-.25-.03-.5-.06-.76-.09-.2.31-.91,1.51-1.19,3.07-.66,3.63.16,6.61.95,11.89Z"/>
    <path class="cls-39" d="M21.95,17.76c-1.22,2.87-3.07,8.09-4.49,15.17-.72,3.56-1.09,5.82-1.23,7.8.03-3.44.23-4.54,1.1-8.86,1.2-5.97,3.13-10.93,4.63-14.12Z"/>
    <path class="cls-38" d="M19.38,39.94c-1.42,5.48-2.94,10.06-2.81,14.85h0c-.22-1.19-.27-2.08-.27-2.08,0,0-.01-.27-.04-.74,0-4.36,1.44-8.49,2.86-13.45,1.67-5.81,3.64-10.46,4.52-12.21-.84,1.88-2.57,7.1-4.25,13.63Z"/>
    <path class="cls-40" d="M26.22,17.76c1.22,2.87,3.07,8.09,4.49,15.17.72,3.56,1.09,5.82,1.23,7.8-.03-3.44-.23-4.54-1.1-8.86-1.2-5.97-3.13-10.93-4.63-14.12Z"/>
    <path class="cls-47" d="M28.8,39.94c1.42,5.48,2.94,10.06,2.81,14.85h0c.22-1.19.27-2.08.27-2.08,0,0,.01-.27.04-.74,0-4.36-1.44-8.49-2.86-13.45-1.67-5.81-3.64-10.46-4.52-12.21.84,1.88,2.57,7.1,4.25,13.63Z"/>
    <path class="cls-46" d="M24.09,62.25c.43,0,3.01-.2,3.92-.58.11-.05.22-.1.33-.17l1.89,3.51h-6.13v-2.76Z"/>
    <path class="cls-48" d="M30.81,57.7c.5-.53,2.31-2.49,5.11-5.77,3.07-3.61,6.54-9.41,9.08-15.95,2.12-5.46,3.45-12.84,3.08-20.19h-3.51c.36,5.46.92,12.06-1.24,19.27-2.16,7.21-9.65,14.99-11.41,16.74,0,.06,0,.12,0,.18-.03.47-.04.74-.04.74,0,0-.12,2.09-.78,4.14-.09.29-.19.57-.29.85Z"/>
    <path class="cls-53" d="M43.33,35.06c-.5,1.68-1.29,3.38-2.23,5.03,1.09-1.58,2.07-3.49,2.77-5.8,2.05-6.83,1.76-13.13,1.4-18.5h-.7c.36,5.46.92,12.06-1.24,19.27Z"/>
    <path class="cls-19" d="M11.38,73.33h-.33c-.14,0-.15.12-.15.12v.64h26.38s0-.64,0-.64c0,0,0-.12-.15-.12h-.33s-25.42,0-25.42,0Z"/>
    <path class="cls-18" d="M24.09,62.25c-.43,0-3.01-.2-3.92-.58-.11-.05-.22-.1-.33-.17l-1.89,3.51h6.13v-2.76Z"/>
    <path class="cls-21" d="M24.49,62.21c3.47-.22,5.48-3.29,5.48-6.49,0-2.99-1.2-6.04-2.28-9.44-1.64-5.19-3.34-9.25-3.6-9.25s-1.96,4.06-3.6,9.25c-1.08,3.41-2.28,6.45-2.28,9.44,0,3.19,2.01,6.27,5.48,6.49-3.59-.2-5.73-3.05-5.73-7.18,0-3.06,1.01-5.96,2.15-9.45,1.71-5.22,3.82-9.1,3.99-9.1s2.28,3.88,3.99,9.1c1.14,3.48,2.15,6.38,2.15,9.45,0,4.13-2.15,6.99-5.73,7.18Z"/>
    <path class="cls-22" d="M23.73,37.56c-.64,1.26-1.96,4.64-3.25,8.72-1.08,3.41-2.28,6.45-2.28,9.44,0,3.19,2.01,6.27,5.48,6.49-3.59-.2-5.73-3.05-5.73-7.18,0-3.06,1.01-5.96,2.15-9.45.95-2.9,3.63-8.02,3.63-8.02Z"/>
    <path class="cls-23" d="M24.44,37.56c.64,1.26,1.96,4.64,3.25,8.72,1.08,3.41,2.28,6.45,2.28,9.44,0,3.19-2.01,6.27-5.48,6.49,3.59-.2,5.73-3.05,5.73-7.18,0-3.06-1.01-5.96-2.15-9.45-.95-2.9-3.63-8.02-3.63-8.02Z"/>
    <path class="cls-20" d="M27.83,56.05c0,1.89-.55,6.17-3.74,6.17,2.08,0,2.73-3.16,2.73-5.54,0-4.97-2.58-8.56-2.73-8.56s-2.74,3.59-2.74,8.56c0,2.38.65,5.54,2.74,5.54-3.19,0-3.74-4.28-3.74-6.17,0-4.31,3.69-8.23,3.74-8.23s3.74,3.91,3.74,8.23Z"/>
    <path class="cls-25" d="M20.35,56.05c0,.87.12,2.25.56,3.48-.62-.94-.87-2.49-.84-4.24.07-4.02,3.85-7.36,4.02-7.47-.17.13-3.74,3.98-3.74,8.22Z"/>
    <path class="cls-24" d="M27.82,56.05c0,.87-.12,2.25-.56,3.48.62-.94.87-2.49.84-4.24-.07-4.02-3.85-7.36-4.02-7.47.17.13,3.74,3.98,3.74,8.22Z"/>
    <path class="cls-26" d="M24.09,13.9c.22,0,4.63,7.4,6.76,17.98,1.37,6.8,1.09,5.61,1.07,18.17h0c-.24-4.09-1.5-8.02-2.78-12.72-2.13-7.82-4.83-13.62-5.05-13.62s-2.92,5.8-5.05,13.62c-1.28,4.7-2.55,8.63-2.78,12.72h0c-.02-12.56-.3-11.36,1.07-18.17,2.13-10.58,6.55-17.98,6.76-17.98Z"/>
    <path class="cls-1" d="M31.61,54.79c.13-4.79-1.4-9.37-2.81-14.85-1.68-6.52-3.41-11.74-4.25-13.62,0,0,0,0,0,0-.25-.49-.41-.75-.46-.75s-.21.26-.46.75h0s0,0,0,0c-.84,1.88-2.57,7.1-4.25,13.62-1.42,5.48-2.94,10.06-2.81,14.85h0c.12.63.28,1.35.51,2.06.78,2.44,1.76,4.26,3.09,4.82.91.38,3.49.58,3.92.58s3.01-.2,3.92-.58c1.33-.56,2.31-2.37,3.09-4.82.23-.71.39-1.43.51-2.06h0ZM24.53,62.21s-.03,0-.04,0c-.13,0-.26.01-.4.01s-.27,0-.4-.01c-.01,0-.03,0-.04,0-3.56-.22-5.69-3.07-5.69-7.18,0-3.06,1.01-5.96,2.15-9.45,1.71-5.22,3.82-9.1,3.99-9.1s2.28,3.88,3.99,9.1c1.14,3.48,2.15,6.38,2.15,9.45,0,4.11-2.13,6.96-5.69,7.18Z"/>
  </g>
</svg>