{% load core_tags %}

{% spaceless %}
  {% if goal_keepers or defenders or midfielders or forwards or manager %}
    <div class="players-item">
      <div class="label">
        {% translation 'CLUB_PROFILE_PAGE_GOALKEEPERS' %}
      </div>

      <div class="player-list">
        {% if goal_keepers|length > 0 %}
          {% for player in goal_keepers %}
            {% include 'clubs/components/club_profile_player_item.html' with data=player %}
          {% endfor %}
        {% endif %}
      </div>
    </div>

    <div class="players-item">
      <div class="label">
        {% translation 'CLUB_PROFILE_PAGE_DEFENDERS' %}
      </div>

      <div class="player-list">
        {% if defenders and defenders|length > 0 %}
          {% for player in defenders %}
            {% include 'clubs/components/club_profile_player_item.html' with data=player %}
          {% endfor %}
        {% endif %}
      </div>
    </div>

    <div class="players-item">
      <div class="label">
        {% translation 'CLUB_PROFILE_PAGE_MIDFIELDERS' %}
      </div>

      <div class="player-list">
        {% if midfielders and midfielders|length > 0 %}
          {% for player in midfielders %}
            {% include 'clubs/components/club_profile_player_item.html' with data=player %}
          {% endfor %}
        {% endif %}
      </div>
    </div>

    <div class="players-item">
      <div class="label">
        {% translation 'CLUB_PROFILE_PAGE_FORWARDS' %}
      </div>

      <div class="player-list">
        {% if forwards and forwards|length > 0 %}
          {% for player in forwards %}
            {% include 'clubs/components/club_profile_player_item.html' with data=player %}
          {% endfor %}
        {% endif %}
      </div>
    </div>

    <div class="players-item">
      <div class="label">
        {% translation 'CLUB_PROFILE_PAGE_MANAGER' %}
      </div>

      <div class="player-list">
        {% if manager %}
          {% include 'clubs/components/club_profile_manager_item.html' with data=manager %}
        {% endif %}
      </div>
    </div>
  {% else %}
    {% include 'components/data-empty-state.html' %}
  {% endif %}
{% endspaceless %}
