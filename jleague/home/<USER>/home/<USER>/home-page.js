// widgets
const todayMatchesContainerSelector = '.home__today-games';
const goalHighlightsContainerSelector = '.goal-highlights__body';

const newsContainerSelector = '.home__news-content'; //'.home__news-container';
const photosContainerSelector = '.home__photos-content';
const videosContainerSelector = '.home__videos-content';

const newsKey = 'news';
const photosKey = 'photos';
const videosKey = 'videos';
const todayMatchesKey = 'today_games';

// competition dropdowns
const goalHighlightsCompetitionSelector = '.home-goal-highlights-competition';

const screenBreakpoint = 991;
let contentMenuButton;
let contentLatestContainer;
let contentMatchesContainer;
let isMatchesContentLoaded = false;
let windowResizeTimeout;
let upcomingStageSelectedTimeout;
let upcomingSelectedCompetition = 'j1';
let upcomingSelectedStage;
let homeGoalHighlights;

function loadContent(contentKey, containerClass, competition_slug, stage) {
  const container = $(containerClass);
  let url = `/widgets/home/<USER>/`;
  url += competition_slug ? `${competition_slug}/` : '';

  if (contentKey !== todayMatchesKey) {
    container.addClass('loading');
  }

  return ajaxRequest(url)
    .then((result) => {
      if (contentKey !== newsKey) {
        container.empty();
      }

      if (result && result.trim().length !== 0) {
        container.append(result);
      } else {
        if (contentKey === todayMatchesKey) {
          $(todayMatchesContainerSelector).remove();
        }
      }

      container.removeClass('loading');
    })
    .catch((error) => {
      console.error(error);
    });
}

function checkHomeLive() {
  const liveContainer = $('.home__live');
  ajaxRequest('/widgets/home/<USER>/')
    .then((result) => {
      if (result && result.trim().length !== 0) {
        liveContainer.html(result);
      }
    })
    .catch((error) => {
      console.error(error);
    });
}

function _onDropdownSelected(competitionSlug, selector) {
  // load data
  switch (selector) {
    case goalHighlightsCompetitionSelector:
      homeGoalHighlights.setCompetition(competitionSlug);
      homeGoalHighlights.loadVideos(0, true);
      break;
    default:
      break;
  }
}

$(document).ready(() => {
  // fetch today games
  loadContent(todayMatchesKey, todayMatchesContainerSelector);
  loadContent(newsKey, newsContainerSelector);
  loadContent(photosKey, photosContainerSelector);
  loadContent(videosKey, videosContainerSelector);
  // match live streaming
  checkHomeLive();

  // latest goal highlights
  homeGoalHighlights = new GoalHighlights(goalHighlightsContainerSelector);
  homeGoalHighlights.setCompetition('j1');
  setTimeout(() => {
    homeGoalHighlights.loadVideos();
  });

  // widget dropdowns
  competitionDropdown.init(goalHighlightsCompetitionSelector, _onDropdownSelected);
});
