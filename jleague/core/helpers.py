import datetime
import re
import pytz

from django.utils import dateformat
from django.utils.formats import get_format
from django.utils.translation import (
    get_language,
    get_language_info,
    override,
)
from .models import Competition, Country, Translation, Banner


def get_active_lang_code():
    lang = get_language()
    lang_info = get_language_info(lang)
    lang_code = lang_info.get("code") or "en"
    return lang_code


def get_static_translations(language_code: str):
    if Translation.translations is None:
        Translation.update_translations_list()
    if Translation.translations.get(language_code):
        return Translation.translations[language_code]
    return {}


def _apply_translation_options(value: str, **kwargs):
    options = ["uppercase", "lowercase", "titlecase"]
    new_value = str(value)

    for option in options:
        option_value = kwargs.get(option)

        if option == "uppercase" and option_value is True:
            new_value = new_value.upper()
        elif option == "lowercase" and option_value is True:
            new_value = new_value.lower()
        elif option == "titlecase" and option_value is True:
            new_value = new_value.title()

    return new_value


def get_translation_value(key, **kwargs):
    value = key
    lang_code = (
        kwargs["lang_code"] if kwargs.get("lang_code") else get_active_lang_code()
    )
    translations = get_static_translations(lang_code)

    if key and translations:
        options = ["uppercase", "lowercase", "titlecase", "lang_code"]
        key_prefix = kwargs.get("prefix")
        if key_prefix:
            translated_value = translations.get(key_prefix + key)
        else:
            translated_value = translations.get(key)

        # patch placeholder
        if translated_value:
            for key, val in kwargs.items():
                if key in options:
                    continue
                translated_value = translated_value.replace("{{" + key + "}}", str(val))
            value = translated_value
        value = _apply_translation_options(value, **kwargs)
    return value


def get_translation_obj_value(data, **kwargs):
    value = ""
    if isinstance(data, dict) and data:
        lang_code = (
            kwargs["lang_code"] if kwargs.get("lang_code") else get_active_lang_code()
        )
        if data.get(lang_code):
            value = data.get(lang_code)
            value = _apply_translation_options(value, **kwargs)

        if value == "" and data.get("en"):
            value = data.get("en")

    if value == "" and kwargs.get("default"):
        value = kwargs.get("default")
    return value


def get_competition_name(competition, use_short=False, **kwargs):
    local_name = ""

    if isinstance(competition, dict):
        local_name = (
            competition.get("name_short")
            if use_short is True
            else competition.get("name")
        )
        lang_code = (
            kwargs["lang_code"] if kwargs.get("lang_code") else get_active_lang_code()
        )

        if (
            use_short is True
            and competition.get("t_name_short")
            and competition["t_name_short"]
            and competition["t_name_short"].get(lang_code)
        ):
            local_name = competition["t_name_short"][lang_code]
        elif (
            competition.get("t_name")
            and competition["t_name"]
            and competition["t_name"].get(lang_code)
        ):
            local_name = competition["t_name"][lang_code]

    return local_name


def get_club_name(club, use_short=False, use_four_letters=False, **kwargs):
    local_name = ""

    if isinstance(club, dict):
        local_name = club.get("name_short") if use_short is True else club.get("name")
        lang_code = (
            kwargs["lang_code"] if kwargs.get("lang_code") else get_active_lang_code()
        )

        if use_four_letters is True and club.get("four_letters_name"):
            local_name = club["four_letters_name"]
        elif (
            use_short is True
            and club.get("t_name_short")
            and club["t_name_short"]
            and club["t_name_short"].get(lang_code)
        ):
            local_name = club["t_name_short"][lang_code]
        elif club.get("t_name") and club["t_name"] and club["t_name"].get(lang_code):
            local_name = club["t_name"][lang_code]

    return local_name


def get_player_name(player, use_short=False, **kwargs):
    local_name = ""

    if isinstance(player, dict):
        local_name = (
            player.get("name_short") if use_short is True else player.get("name")
        )
        lang_code = (
            kwargs["lang_code"] if kwargs.get("lang_code") else get_active_lang_code()
        )

        if (
            use_short is True
            and player.get("t_name_short")
            and player["t_name_short"]
            and player["t_name_short"].get(lang_code)
        ):
            local_name = player["t_name_short"][lang_code]
        elif (
            player.get("t_name")
            and player["t_name"]
            and player["t_name"].get(lang_code)
        ):
            local_name = player["t_name"][lang_code]

    return local_name


def get_local_time(time, time_format="TIME_FORMAT", timezone=None, **kwargs):
    if not isinstance(time, datetime.datetime):
        return ""
    default_tz = pytz.timezone("Asia/Tokyo")
    jp_time = time.astimezone(default_tz)
    local_time = None
    # try:
    if timezone:
        if re.match(r"-?\d{1,}", timezone):
            local_time = time.astimezone(pytz.FixedOffset(int(timezone) * -1))
        else:
            local_tz = pytz.timezone(timezone)
            if local_tz:
                local_time = time.astimezone(local_tz)
    # except pytz.UnknownTimeZoneError:
    #     print("ERROR TIMEZONE:", timezone)
    #     pass
    if (
        local_time is None
        or (jp_time.hour == 0 and jp_time.minute == 0)
        or "00:00:00+09:00" in str(jp_time)
    ):
        # fallback to JST
        local_time = time.astimezone(default_tz)
    if kwargs.get("raw"):
        return local_time if local_time else time
    lang_code = (
        kwargs["lang_code"] if kwargs.get("lang_code") else get_active_lang_code()
    )
    local_time_format = get_format(time_format, lang_code)
    with override(lang_code):
        if local_time:
            local_time = dateformat.format(local_time, local_time_format)
        else:
            local_time = dateformat.format(time, local_time_format)
    return local_time


def get_countries(**kwargs):
    if Country.countries is None:
        Country.update_countries_list()
    if kwargs.get("two_letter_dict"):
        return Country.country2LC
    elif kwargs.get("three_letter_dict"):
        return Country.country3LC
    elif kwargs.get("name_dict"):
        return Country.countryNames
    elif kwargs.get("fifa_code_dict"):
        return Country.countryFifaCode
    return Country.countries


def get_competitions():
    if Competition.competitions is None:
        Competition.update_competitions_list()
    return Competition.competitions


def get_sidebar_banner():
    # JWC Banner
    sidebar_banner = Banner.objects.filter(title="Sidebar").first()
    return sidebar_banner
