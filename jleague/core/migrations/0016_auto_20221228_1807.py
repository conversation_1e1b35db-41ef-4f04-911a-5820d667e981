# Generated by Django 4.1.4 on 2022-12-28 09:07

from django.db import migrations


def patch_navigation_links(model, data, parent_id):
    for item in data:
        model.objects.create(
            label=item.label,
            link_external=item.link_external,
            link_page=item.link_page,
            is_active=True,
            parent_id=parent_id,
        )


def patch_navigation_groups(apps, schema_editor):
    NavigationGroup = apps.get_model('core', 'NavigationGroup')
    NavigationLink = apps.get_model('core', 'NavigationLink')
    AboutNavigationGroup = apps.get_model('about', 'NavigationGroup')
    AboutNavigationLink = apps.get_model('about', 'NavigationLink')

    groups = AboutNavigationGroup.objects.all()
    links = AboutNavigationLink.objects.all()

    for group in groups:
        NavigationGroup.objects.create(
            id=group.id,
            title=group.title,
            is_active=True,
        )
        group_links = [l for l in links if l.parent_id == group.id]
        patch_navigation_links(NavigationLink, group_links, group.id)


class Migration(migrations.Migration):
    dependencies = [
        ("core", "0015_navigationgroup_navigationlink"),
    ]

    operations = [
        migrations.RunPython(patch_navigation_groups),
    ]
