(function ($) {
  $(document).ready(function () {
    const ticketsContainer = $('.ticket-fixtures');

    function _afterFetchFixtures() {
      ticketsModal.init('.tickets-modal-trigger');
    }

    function _fetchFixtures() {
      ticketsContainer.addClass('loading');
      const url = `/tickets/data/${selectedCompetition}/`;

      return ajaxRequest(url)
        .then((result) => {
          ticketsContainer.empty();

          if (result.trim() !== '') {
            ticketsContainer.html(result);
          }

          _afterFetchFixtures();
          ticketsContainer.removeClass('loading');
        })
        .catch((error) => {
          console.error(error);
        });
    }

    _fetchFixtures();
  });
})(jQuery);
