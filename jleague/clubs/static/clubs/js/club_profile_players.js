const clubPlayersSection = (function () {
  const CSS_SELECTORS = {
    CONTENT_CONTAINER: '.club-profile-players__content',
    SECTION_CONTAINER: '#players',
    YEAR_SELECTOR: '.club-profile-players__filters__year-selector',
  };

  let isLoaded = false;
  let isLoading = false;
  let playersContainer;
  let yearSelector;

  function fetchClubPlayers() {
    yearSelector.attr('disabled', true);
    playersContainer.addClass('loading');

    const url = `/clubs/${selectedTeam}/players/${playersSelectedYear}/`;

    return ajaxRequest(url)
      .then((result) => {
        playersContainer.empty();
        if (result.trim() !== '') {
          playersContainer.html(result);
        }
        playersContainer.removeClass('loading');
        yearSelector.removeAttr('disabled');
      })
      .catch((error) => {
        console.error(error);
      });
  }

  function _onFilterChange() {
    playersSelectedYear = yearSelector.val();
    fetchClubPlayers();
  }

  function _checkPlayersSection() {
    if (isLoaded) return;

    if (isScrolledIntoView(CSS_SELECTORS.SECTION_CONTAINER, 10)) {
      isLoaded = true;
      if (!isLoading) {
        isLoading = true;
        fetchClubPlayers();
      }
    }
  }

  function init() {
    playersContainer = $(CSS_SELECTORS.CONTENT_CONTAINER);
    yearSelector = $(CSS_SELECTORS.YEAR_SELECTOR);
    if (yearSelector) yearSelector.change(_onFilterChange);

    $(window).scroll(_checkPlayersSection);
  }

  return { init };
})();
