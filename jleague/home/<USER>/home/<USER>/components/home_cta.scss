.section--cta {
  background: #f4f4fe;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  margin: 0 auto;
  position: relative;
  padding: 40px 16px;

  .section__body {
    max-width: 568px;
  }

  .text-body {
    text-align: left;
    width: 100%;
  }

  .text-body-special {
    text-align: center;
    font-size: 20px;
    font-family: var(--font-overpass);
    font-weight: 900;
  }

  .about-image {
    width: 48px;
    height: 48px;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

#ticket-cta.section.section--cta {
  background: var(--color-earth-1);

  .jl-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    height: 48px;
    background: var(--color-white);
    border: 1px solid var(--color-black);
    border-radius: 25px;
    transition: all 0.3s linear;

    .jl-button__label {
      color: var(--color-black);
      font-family: var(--font-barlow-condensed);
      font-style: normal;
      font-weight: 700;
      font-size: 20px;
      line-height: 24px;
      text-align: center;
      leading-trim: both;
      text-edge: cap;
      font-feature-settings: 'clig' off, 'liga' off;
      letter-spacing: 1.667px;
      text-transform: uppercase;
      white-space: nowrap;
      @include text_ellipsis();
      transition: all 0.3s linear;
    }

    &.jl-button--inverse {
      background: var(--color-black);
      border-color: var(--color-black);

      .jl-button__label {
        color: var(--color-white);
      }
    }

    &:hover:not(:disabled) {
      opacity: 1;
      background: $color-red;
      border-color: $color-red;
      cursor: pointer;

      .jl-button__label {
        color: var(--color-white);
      }
    }

    &.jl-button--red {
      background: var(--color-j1);
      border-color: var(--color-j1);

      .jl-button__label {
        color: var(--color-white);
      }

      &:hover:not(:disabled) {
        background-color: var(--color-white);
        border-color: var(--color-black);

        .jl-button__label {
          color: var(--color-black);
        }
      }
    }

    &.jl-button--white-nobg {
      background: transparent;
      border-color: var(--color-white);

      .jl-button__label {
        color: var(--color-white);
      }

      &:hover:not(:disabled) {
        background-color: var(--color-j1);
        border-color: var(--color-j1);
      }
    }

    &.jl-button--live-link {
      background-color: var(--color-j1);
      border-color: var(--color-j1);

      .jl-button__label {
        color: var(--color-white);
      }

      &::before {
        content: '';
        transition: all 0.3s linear;
        width: 24px;
        height: 24px;
        display: block;
        background: url('/static/images/icons/play-FFFFFF.svg');
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
      }

      &:hover:not(:disabled) {
        background-color: var(--color-white);
        border-color: var(--color-white);

        .jl-button__label {
          color: var(--color-black);
        }

        &::before {
          background: url('/static/images/icons/play-1A1919.svg');
          background-position: center;
          background-repeat: no-repeat;
          background-size: contain;
        }
      }
    }

    &.jl-button--medium {
      height: 40px;
      padding: 8px 24px;

      .jl-button__label {
        font-size: 16px;
        line-height: 24px;
      }
    }

    &.jl-button--small {
      height: 32px;
      padding: 8px 16px;

      .jl-button__label {
        font-size: 14px;
        line-height: 16px;
      }
    }
  }

  .jl-button-special {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    height: 48px;
    background-color: var(--color-white);
    border: 1px solid var(--color-black);
    border-radius: 25px;
    transition: all 0.3s linear;

    .jl-button__label-special {
      color: var(--color-black);
      font-family: var(--font-barlow-condensed);
      font-style: normal;
      font-weight: 700;
      font-size: 20px;
      line-height: 24px;
      text-align: center;
      leading-trim: both;
      text-edge: cap;
      font-feature-settings: 'clig' off, 'liga' off;
      letter-spacing: 1.667px;
      text-transform: uppercase;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      word-break: break-all;
      transition: all 0.3s;
    }
  }
}

#ticket-cta.section.section--cta {
  .button-wrapper {
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: 20px;

    @media (max-width: 480px) {
      flex-direction: column;
    }
  }

  .jl-button {
    background-color: var(--color-black);

    .jl-button__label {
      color: var(--color-white);
    }
  }

  #match-detail.jl-button- {
    background-color: var(--color-white);

    .jl-button__label {
      color: var(--color-black);
    }
  }
}

@media (max-width: 768px) {
  .section__body {
    max-width: 100%;
  }
  .section--cta .text-body-special {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .about-image {
    width: 60%;
    height: auto;
    margin: auto;
  }
}

body.lang-th {
  #ticket-cta.section.section--cta .jl-button .jl-button__label {
    @include font-thai();
    line-height: 2;
  }
}
