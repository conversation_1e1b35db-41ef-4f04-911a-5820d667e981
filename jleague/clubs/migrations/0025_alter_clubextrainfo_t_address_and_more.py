# Generated by Django 4.1.4 on 2023-08-25 05:10

from django.db import migrations
import django_jsonform.models.fields


class Migration(migrations.Migration):
    dependencies = [
        ("clubs", "0024_clubstaff_t_name"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="clubextrainfo",
            name="t_address",
            field=django_jsonform.models.fields.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="clubextrainfo",
            name="t_name",
            field=django_jsonform.models.fields.J<PERSON><PERSON>ield(blank=True, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="clubextrainfo",
            name="t_name_short",
            field=django_jsonform.models.fields.JSONField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="clubextrainfo",
            name="t_practice_ground",
            field=django_jsonform.models.fields.<PERSON><PERSON><PERSON>ield(blank=True, null=True),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name="clubextrainfo",
            name="t_president",
            field=django_jsonform.models.fields.J<PERSON><PERSON>ield(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="clubstaff",
            name="t_name",
            field=django_jsonform.models.fields.JSONField(blank=True, null=True),
        ),
    ]
