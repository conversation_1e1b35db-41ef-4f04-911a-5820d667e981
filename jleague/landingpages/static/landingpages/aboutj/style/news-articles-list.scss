@import 'setup/variable';
@import 'setup/mixin';

.news-articles-list {
  display: flex;
  flex-direction: column;

  .news-articles-list__item {
    position: relative;
    width: 100%;
  }

  .news-article {
    display: grid;

    .news-article__image {
      border-radius: 3px;
      position: relative;
      overflow: hidden;
      width: 420px;
      height: auto;
      aspect-ratio: 3/2;

      .news-article__image__image {
        height: 100%;
        width: 100%;
        object-fit: cover;
        object-position: 50% 50%;
      }

      .news-article__image__overlay {
        position: absolute;
        top: 0;
        bottom: 0;
        right: 0;
        left: 0;
        background-image: linear-gradient(225deg, rgba(26, 25, 25, 0.425) 0%, rgba(37, 0, 0, 0.4) 100%);
        opacity: 0;
        transition: opacity 1s ease-in-out;
      }
    }

    .news-article__details {
      padding-left: 32px;

      .news-article__details__category {
        color: $color-j1;
        font-family: $font-barlow-condensed;
        font-style: normal;
        font-weight: bold;
        font-size: 20px;
        line-height: 24px;
        letter-spacing: 1.66667px;
        text-transform: uppercase;
        padding-bottom: 22px;
        margin-bottom: 16px;
        position: relative;

        &::after {
          content: '';
          background: linear-gradient(90deg, rgba(255, 255, 255, 0.0001) 0%, $color-red 99.69%);
          position: absolute;
          bottom: 0;
          left: 0;
          height: 6px;
          width: 60px;
        }
      }

      .news-article__details__title {
        color: $color-black;
        font-family: $font-overpass;
        font-style: normal;
        font-weight: 900;
        font-size: 24px;
        line-height: 36px;
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        margin-bottom: 16px;
      }

      .news-article__details__content {
        color: $color-drawn;
        font-family: $font-overpass;
        font-style: normal;
        font-weight: 400;
        font-size: 20px;
        line-height: 28px;
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        margin-bottom: 16px;
      }

      .news-article__details__post-date {
        color: $color-drawn;
        font-family: $font-barlow-condensed;
        font-style: normal;
        font-weight: 700;
        font-size: 18px;
        line-height: 22px;
        letter-spacing: 0.5px;
        text-transform: uppercase;
      }
    }

    &:hover {
      opacity: 1;

      .news-article__image {
        .news-article__image__overlay {
          opacity: 1;
        }
      }

      .news-article__details {
        .news-article__details__title {
          text-decoration: underline;
        }
      }
    }
  }

  &.news-articles-list--grid {
    flex-direction: row;

    .news-articles-list__item {
      width: 50%;
      overflow: hidden;
      padding-bottom: 40px;

      & + .news-articles-list__item {
        margin-top: 0;
      }
    }

    .news-article {
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .news-article__image {
        width: 100%;
      }

      .news-article__details {
        padding-left: 0;

        .news-article__details__category {
          margin-top: 16px;
        }

        .news-article__details__content {
          display: none;
        }
      }
    }
  }
}

@media screen and (min-width: 769px) {
  .news-articles-list {
    .news-articles-list__item {
      & + .news-articles-list__item {
        margin-top: 40px;
      }
    }

    .news-article {
      grid-template-columns: 420px auto;
    }
  }
}

@media screen and (max-width: 768px) {
  .news-articles-list {
    .news-article {
      align-items: center;

      .news-article__details {
        .news-article__details__post-date {
          font-size: 14px;
          line-height: 20px;
        }
      }
    }
  }
}

@media screen and (max-width: 768px) and (min-width: 577px) {
  .news-articles-list {
    .news-articles-list__item {
      & + .news-articles-list__item {
        margin-top: 32px;
      }
    }

    .news-article {
      grid-template-columns: 240px auto;

      .news-article__image {
        width: 240px;
      }

      .news-article__details {
        padding-left: 24px;

        .news-article__details__category {
          font-size: 16px;
          line-height: 19px;
          padding-bottom: 18px;
          margin-bottom: 12px;
        }

        .news-article__details__title {
          font-size: 24px;
          line-height: 28px;
        }

        .news-article__details__content {
          font-size: 16px;
          line-height: 20px;
          -webkit-line-clamp: 3;
        }

        .news-article__details__content,
        .news-article__details__title {
          margin-bottom: 12px;
        }
      }
    }
  }
}

@media screen and (max-width: 576px) {
  .news-articles-list {
    .news-articles-list__item {
      & + .news-articles-list__item {
        margin-top: 24px;
      }
    }

    .news-article {
      grid-template-columns: 120px auto;

      .news-article__image {
        width: 120px;
      }

      .news-article__details {
        padding-left: 16px;

        .news-article__details__category {
          font-size: 14px;
          line-height: 17px;
          padding-bottom: 14px;
          margin-bottom: 8px;
        }

        .news-article__details__title {
          font-size: 16px;
          line-height: 20px;
        }

        .news-article__details__content {
          display: none;
        }

        .news-article__details__content,
        .news-article__details__title {
          margin-bottom: 8px;
        }
      }
    }

    &.news-articles-list--grid {
      flex-direction: column;

      .news-articles-list__item {
        width: 100%;
        padding-bottom: 0;

        & + .news-articles-list__item {
          margin-top: 24px;
        }

        &:nth-child(odd) {
          padding-right: 0;
        }

        &:nth-child(even) {
          padding-left: 0;
        }
      }

      .news-article {
        display: grid;

        .news-article__details {
          padding-left: 16px;
        }
      }
    }
  }
}

body.lang-th {
  .news-articles-list {
    .news-article {
      .news-article__details {
        .news-article__details__category,
        .news-article__details__title,
        .news-article__details__post-date {
          @include font-thai();
        }

        .news-article__details__title {
          font-weight: 700;
        }

        .news-article__details__content {
          @include font-thai(true);
        }
      }
    }
  }
}
