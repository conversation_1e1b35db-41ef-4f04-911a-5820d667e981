log:
  level: INFO

entryPoints:
  web:
    # http
    address: ':80'
    http:
      # https://docs.traefik.io/routing/entrypoints/#entrypoint
      redirections:
        entryPoint:
          to: web-secure

  web-secure:
    # https
    address: ':443'

certificatesResolvers:
  cloudflare:
    # https://go-acme.github.io/lego/dns/cloudflare/
    acme:
      email: '<EMAIL>'
      storage: /etc/traefik/acme/acme.json
      # https://doc.traefik.io/traefik/master/https/acme/#dnschallenge
      dnsChallenge:
        provider: cloudflare
        resolvers:
          - '*******:53'
          - '*******:53'
        # disablePropagationCheck: true
        # delayBeforeCheck: 0

http:
  routers:
    jl-web-router:
      rule: 'Host(`dev-jleague.turfmapp.com`)'
      entryPoints:
        - web-secure
      middlewares:
        - jl-auth
        - csrf
      service: jl-web
      tls:
        # https://docs.traefik.io/master/routing/routers/#certresolver
        certResolver: cloudflare

    # jl-stories-router:
    #   rule: 'Host(`stories.jleague.co`)'
    #   entryPoints:
    #     - web-secure
    #   middlewares:
    #     - jl-stories
    #   service: jl-stories
    #   tls:
    #     # https://docs.traefik.io/master/routing/routers/#certresolver
    #     certResolver: cloudflare

  middlewares:
    csrf:
      # https://docs.traefik.io/master/middlewares/headers/#hostsproxyheaders
      # https://docs.djangoproject.com/en/dev/ref/csrf/#ajax
      headers:
        hostsProxyHeaders: ['X-CSRFToken']

    jl-auth:
      # https://doc.traefik.io/traefik/middlewares/http/digestauth/#digestauth
      digestAuth:
        users:
          - jleague:traefik:ac01e1a446989d1d80f04950c99b3cdb

    # jl-stories:
    #   headers:
    #     customResponseHeaders:
    #       Access-Control-Allow-Credentials: 'true'
    #       Access-Control-Allow-Origin: 'https://stories-jleague-co.cdn.ampproject.org'
    #       AMP-Same-Origin: 'true'

  services:
    jl-web:
      loadBalancer:
        servers:
          - url: http://jleague:8000

    # jl-stories:
    #   loadBalancer:
    #     servers:
    #       - url: 'https://wscstoriesjleague.blob.core.windows.net'

providers:
  # https://docs.traefik.io/master/providers/file/
  file:
    filename: /etc/traefik/traefik.yml
    watch: true
