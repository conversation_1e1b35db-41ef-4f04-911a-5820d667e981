# Generated by Django 3.1.13 on 2022-05-31 08:19

from django.db import migrations, models
import django.db.models.deletion
import wagtail.search.index


class Migration(migrations.Migration):
    dependencies = [
        ('data_stadium', '0050_remove_player_team_member_type'),
        ('clubs', '0010_auto_20220429_1849'),
        ('core', '0006_auto_20220601_2114'),
        ('stadiums', '0005_auto_20220601_2103'),
    ]

    operations = [
        migrations.CreateModel(
            name='Club',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ds_club_id', models.IntegerField(db_index=True, unique=True)),
                ('name', models.CharField(max_length=255)),
                ('name_short', models.CharField(max_length=255)),
                ('slug', models.SlugField()),
                ('federation_id', models.IntegerField(blank=True, null=True)),
                ('founded', models.IntegerField(null=True)),
                ('website_url', models.URLField(max_length=255, null=True)),
                ('twitter_url', models.URLField(max_length=255, null=True)),
                ('facebook_url', models.URLField(max_length=255, null=True)),
                ('instagram_url', models.URLField(max_length=255, null=True)),
                ('j1_license', models.BooleanField(null=True)),
                ('j2_license', models.BooleanField(null=True)),
                ('j3_license', models.BooleanField(null=True)),
                ('average_age', models.DecimalField(decimal_places=1, max_digits=3, null=True)),
                ('average_height', models.DecimalField(decimal_places=1, max_digits=4, null=True)),
                ('average_weight', models.DecimalField(decimal_places=1, max_digits=4, null=True)),
                ('hometowner_count', models.IntegerField(null=True)),
                ('hometowner_rate', models.DecimalField(decimal_places=1, max_digits=4, null=True)),
                ('domestic_league_count', models.IntegerField(blank=True, null=True)),
                ('domestic_league_rate', models.DecimalField(blank=True, decimal_places=1, max_digits=4, null=True)),
                ('country_id', models.CharField(max_length=255, null=True)),
                ('country_name', models.CharField(max_length=255, null=True)),
                ('prefecture', models.CharField(max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('stadium', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='stadiums.stadium')),
            ],
            options={
                'ordering': ['name'],
            },
            bases=(wagtail.search.index.Indexed, models.Model),
        ),
        migrations.CreateModel(
            name='ClubCompetition',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField(db_index=True)),
                ('season_id', models.IntegerField()),
                ('group_id', models.CharField(max_length=255, null=True)),
                ('ordering', models.IntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                (
                    'club',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, related_name='club_competition', to='clubs.club'
                    ),
                ),
                (
                    'competition',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='competition_clubs',
                        to='core.competition',
                    ),
                ),
            ],
        ),
        migrations.RenameField(
            model_name='clubextrainfo',
            old_name='team_id',
            new_name='ds_club_id',
        ),
        migrations.AddField(
            model_name='clubextrainfo',
            name='club',
            field=models.ForeignKey(
                default=None,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='extra_info',
                to='clubs.club',
            ),
        ),
        migrations.AddField(
            model_name='clubextrainfo',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='clubextrainfo',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
    ]
