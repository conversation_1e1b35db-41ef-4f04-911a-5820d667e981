from django.conf import settings
from django.core.cache import cache
from django.http import HttpResponse, JsonResponse
from django.middleware.csrf import get_token
from django.shortcuts import redirect
from django.templatetags.static import static
from django.urls import reverse
from django.utils.decorators import method_decorator
from django.views.decorators.cache import never_cache
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.views.generic.base import TemplateView
from utils.helpers.etc import get_user_country
from .models import InfluencerLog


# from pywebpush import webpush, WebPushException
# import json


@never_cache
@csrf_exempt
@require_http_methods(["GET"])
def health(request):
    return HttpResponse("ok", content_type="text/plain")


@never_cache
@require_http_methods(["GET"])
def influencer_count(request, sns, name):
    url = "https://jleague.co/"

    if sns == "j30":
        url = "https://www.jleague.co/j30/"
    elif sns == "yt":
        url = "https://www.youtube.com/c/JLEAGUEInternational?sub_confirmation=1"
    elif sns == "fb":
        url = "https://www.facebook.com/jleagueofficial.English"
    elif sns == "fb_th":
        url = "https://facebook.com/jleagueofficial.thai"
    elif sns == "ig":
        url = "https://instagram.com/jleagueintl"
    elif sns == "ig_th":
        url = "https://www.instagram.com/jleagueth/"
    elif sns == "tt":
        url = "https://www.tiktok.com/@jleagueintl"
    elif sns == "tw":
        url = "https://twitter.com/J_League_En"

    user_tz = request.TIMEZONE

    user_country = get_user_country(request)

    InfluencerLog.objects.create(
        influencer=name,
        sns_link=sns,
        country=user_country,
        timezone=user_tz,
    )

    return redirect(url)


@csrf_exempt
@require_http_methods(["POST"])
def tool_cache_clear(request):
    cache.clear()
    return HttpResponse("acknowledge.", content_type="text/plain")


@csrf_exempt
@require_http_methods(["POST"])
def tool_cache_delete(request):
    key = request.POST.get("key")
    cache.delete(key)
    return HttpResponse("acknowledge", content_type="text/plain")


@csrf_exempt
@require_http_methods(["POST"])
def tool_caches(request):
    # print(caches["default"])
    try:
        caches = cache.keys("*")
    except:
        caches = []
    return JsonResponse({"caches": caches})


@csrf_exempt
@require_http_methods(["POST"])
def generate_token(request):
    token = get_token(request)
    return HttpResponse(token, content_type="text/plain")


@method_decorator(csrf_exempt, name="dispatch")
@method_decorator(require_http_methods(["GET"]), name="dispatch")
class RobotsTxtView(TemplateView):
    template_name = "core/robots.txt"
    content_type = "text/plain"

    if not settings.CURR_ENV.endswith("prod"):
        template_name = "core/robots.dev.txt"



class OfflineView(TemplateView):
    template_name = "core/offline.html"


@method_decorator(never_cache, name="dispatch")
class ServiceWorkerView(TemplateView):
    template_name = "core/sw.js"
    content_type = "application/javascript"

    def get_context_data(self, **kwargs):
        return {
            "version": "1.0.7",
            "icon_path": static("icons/icon-512x512.png"),
            "home_path": "/",
            "offline_path": reverse("offline"),
            "manifest_path": static("manifest.json"),
        }


# def sendNotifications(subscriptions):
#     # Create the notification content.
#     # Send a push message to each client specified in the subscriptions array.
#     for subscription in subscriptions:
#         endpoint = subscription['endpoint']
#         id = endpoint[len(endpoint) - 8: len(endpoint)]
#         data = json.dumps({
#             'title': 'Hello, Notifications!',
#             'options': {
#                 'body': f'ID: {id}'
#             }
#         })
#         try:
#             # .SEND(DATA, HEADERS={}, TTL=0, GCM_KEY="", REG_ID="", CONTENT_ENCODING="AES128GCM", CURL=FALSE, TIMEOUT=NONE)
#             # wp =  WebPusher(subscription_info).send(data, headers, ttl, gcm_key)
#             webpush(
#                 subscription_info=subscription,
#                 data=data,
#                 vapid_private_key='KC90...',  # generated from https://web-push-codelab.glitch.me/
#                 vapid_claims={'sub': 'mailto:<EMAIL>'}
#             )
#         except WebPushException as ex:
#             print(f'Endpoint ID: {id}')
#             print(ex)
#             # Mozilla returns additional information in the body of the response.
#             if ex.response and ex.response.json():
#                 extra = ex.response.json()
#                 print(f'Remote service replied with a {extra.code}:{extra.errno}, {extra.message}')


# @require_http_methods(['POST'])
# def notify_me(request):
#     # TODO: get data from DB
#     # subscription = {
#     #     'endpoint': 'https://...',
#     #     'expirationTime': None,
#     #     'keys': {
#     #         'p256dh': 'ABC...',
#     #         'auth': 'XYZ...'
#     #     }
#     # }
#     # sendNotifications([subscription])
#     return HttpResponse('acknowledge', content_type='text/plain')


# @require_http_methods(['POST'])
# def add_subscription(request):
#     subscription = request.POST.get('subscription')
#     # print(json.loads(subscription))
#     # {'endpoint': 'https://...', 'expirationTime': None, 'keys': {'p256dh': 'ABC...', 'auth': 'XYZ...'}}
#     # TODO: add data into DB
#     return HttpResponse('acknowledge', content_type='text/plain')


# @require_http_methods(['POST'])
# def remove_subscription(request):
#     subscription = request.POST.get('subscription')
#     # print(json.loads(subscription))
#     # {'endpoint': 'https://...', 'expirationTime': None, 'keys': {'p256dh': 'ABC...', 'auth': 'XYZ...'}}
#     # TODO: delete data from DB
#     return HttpResponse('acknowledge', content_type='text/plain')
