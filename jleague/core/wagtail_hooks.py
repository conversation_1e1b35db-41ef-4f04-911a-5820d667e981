from django.urls import path, reverse
from wagtail import hooks
from .admin_views import translation_import
from wagtail.admin.menu import AdminOnlyMenuItem, Menu, SubmenuMenuItem


@hooks.register('register_admin_urls')
def register_translation_url():
    return [
        path('core/translation/import/', translation_import, name='translation_import'),
    ]


@hooks.register('register_core_translation_menu_item')
def register_core_import_translation_menu_item():
    return AdminOnlyMenuItem('Import', reverse('translation_import'), icon_name='download', order=2)


@hooks.register('register_admin_menu_item')
def register_core_translation_menu():
    translation_menu = Menu(
        register_hook_name='register_core_translation_menu_item', construct_hook_name='construct_core_translation_menu'
    )
    return SubmenuMenuItem('Translation', translation_menu, icon_name='group', order=999)


# @hooks.register('construct_main_menu')
# def hide_unused_menu(request, menu_items):
#     # print('user:', request.user._wrapped.__dict__)
#     print('menu_items:', menu_items)
#     print('groups:', request.user.groups)

#     hidden_items = ['documents', 'reports']  # 'explorer' 'custom-pages'
#     if not request.user.is_superuser:
#         hidden_items += ['snippets', 'settings']
#     allow_items = []
#     for item in menu_items:
#         if item.name in hidden_items:
#             continue
#         allow_items.append(item)
#     menu_items[:] = allow_items
