user nginx;
worker_processes 1;

error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
  worker_connections 1024;
}

http {
  include /etc/nginx/mime.types;
  # https://nginx.org/en/docs/http/ngx_http_core_module.html#default_type
  default_type application/octet-stream;

  # https://nginx.org/en/docs/http/ngx_http_log_module.html#log_format
  log_format main '$remote_addr - $remote_user [$time_local] "$request" '
  '$status $body_bytes_sent "$http_referer" '
  '"$http_user_agent" "$http_x_forwarded_for"';
  # https://nginx.org/en/docs/http/ngx_http_log_module.html#access_log
  access_log /var/log/nginx/access.log main;
  # https://nginx.org/en/docs/http/ngx_http_core_module.html#client_max_body_size
  client_max_body_size 100m;
  # https://nginx.org/en/docs/http/ngx_http_gzip_module.html#gzip
  gzip on;
  # https://nginx.org/en/docs/http/ngx_http_core_module.html#keepalive_timeout
  keepalive_timeout 600;
  # https://nginx.org/en/docs/http/ngx_http_core_module.html#sendfile
  sendfile on;

  server {
    listen 127.0.0.1:8999;
    listen [::]:8999;

    location /stub_status {
      # https://nginx.org/en/docs/http/ngx_http_stub_status_module.html#stub_status
      stub_status;
      access_log off;
      allow 127.0.0.1;
      deny all;
    }
  }

  server {
    listen 80;
    listen [::]:80;
    server_name staging.jleague.co;
    return 301 $scheme://staging.jleague.co$request_uri;
  }

  # https://nginx.org/en/docs/http/ngx_http_core_module.html#server
  server {
    # prevent Guzzle client from accessing the server.
    if ($http_user_agent ~* (GuzzleHttp)) {
      return 403;
    }

    # https://nginx.org/en/docs/http/ngx_http_core_module.html#listen
    listen 80 default_server;
    listen [::]:80 default_server;
    listen 443 ssl;
    # https://nginx.org/en/docs/http/ngx_http_core_module.html#server_name
    server_name staging.jleague.co;
    # https://nginx.org/en/docs/http/ngx_http_ssl_module.html#ssl_certificate
    ssl_certificate /etc/ssl/staging.jleague.co.pem;
    # https://nginx.org/en/docs/http/ngx_http_ssl_module.html#ssl_certificate_key
    ssl_certificate_key /etc/ssl/staging.jleague.co.key;
    # https://nginx.org/en/docs/http/ngx_http_ssl_module.html#ssl_ciphers
    ssl_ciphers "ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384";
    # https://nginx.org/en/docs/http/ngx_http_ssl_module.html#ssl_protocols
    ssl_protocols TLSv1.2 TLSv1.3;
    # https://nginx.org/en/docs/http/ngx_http_ssl_module.html#ssl_session_cache
    ssl_session_cache shared:le_nginx_SSL:10m;
    # https://nginx.org/en/docs/http/ngx_http_ssl_module.html#ssl_session_tickets
    ssl_session_tickets off;
    # https://nginx.org/en/docs/http/ngx_http_ssl_module.html#ssl_session_timeout
    ssl_session_timeout 1440m;

    # https://nginx.org/en/docs/http/ngx_http_core_module.html#location
    location / {
      # Basic Authentication
      auth_basic "Restricted Content";
      auth_basic_user_file /etc/nginx/.htpasswd;

      # https://nginx.org/en/docs/http/ngx_http_proxy_module.html#proxy_pass
      proxy_pass http://jleague:8000;
      # https://nginx.org/en/docs/http/ngx_http_proxy_module.html#proxy_set_header
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-Proto $scheme;

      # Increase timeout for player photos uploading (experimental)
      # ------------------------------------------------------------------
      # https://nginx.org/en/docs/http/ngx_http_proxy_module.html#proxy_connect_timeout
      proxy_connect_timeout 600;
      # https://nginx.org/en/docs/http/ngx_http_proxy_module.html#proxy_read_timeout
      proxy_read_timeout 600;
      # https://nginx.org/en/docs/http/ngx_http_proxy_module.html#proxy_send_timeout
      proxy_send_timeout 600;
    }

  }
}
