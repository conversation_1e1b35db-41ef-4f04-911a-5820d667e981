#! /bin/sh

set -e

export PGPASSWORD=$POSTGRES_PASSWORD
export AWS_ACCESS_KEY_ID=$STORAGE_AWS_ACCESS_KEY_ID
export AWS_SECRET_ACCESS_KEY=$STORAGE_AWS_SECRET_ACCESS_KEY

LATEST_BACKUP=$(s3cmd ls s3://jleague-co/backup/ -c /root/.s3cfg | sort | tail -n 1 | awk '{ print $4 }')

echo "Fetching ${LATEST_BACKUP}"
s3cmd get ${LATEST_BACKUP} dump.sql.gz
gzip -d dump.sql.gz

echo "Restoring ${LATEST_BACKUP}"
psql -h db -U $POSTGRES_USER -d $POSTGRES_DB < dump.sql

echo "Restore completed"
