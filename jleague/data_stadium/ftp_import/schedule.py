import aioftp
import logging
import xml.etree.ElementTree as ET
from datetime import datetime
from decimal import Decimal
from django.utils.timezone import make_naive
from clubs.models import Club
from core.constants import FAKE_TEAM_ID
from core.enums import GameSituationEnum
from core.models import Competition
from match.models import Game
from .helpers import (
    async_delete,
    async_find_first,
    async_save_item,
    async_select_ids,
    async_upsert,
    parse_date,
    parse_datetime,
    process_path_if_needed,
    read_file_to_str,
    xml_value,
)

logger = logging.getLogger(__name__)


async def import_schedule(ftp_client: aioftp.Client, file_path: str, file_stat: dict):
    """Import last 5 games of club by competition from DS
    source file: /master/schedule-{COMPETITION_ID}.xml
    """
    async with process_path_if_needed(file_path, file_stat) as should_process:
        if not should_process:
            return

        # read XML file
        file_content = await read_file_to_str(ftp_client, file_path)
        root = ET.fromstring(file_content)  # Football node
        game_schedule_node = root.find('GameSchedule')
        year = xml_value(game_schedule_node, 'Year', int)
        competition_id = xml_value(game_schedule_node, 'GameKindID', int)

        # check competition exists
        competition = await async_find_first(Competition, id=competition_id)
        if not competition:
            logger.warn('Competition not found (id=%s)', competition_id)
            return

        game_category_nodes = game_schedule_node.findall('GameCategory')

        # NOTE: data reconciliation, DS may remove previously synced games for various reasons
        # fetch game_ids from db
        games_ids = await async_select_ids(Game, year=year, competition_id=competition_id)
        # collect game_ids from file
        ftp_games_ids = []
        for game_category_node in game_category_nodes:
            for game_node in game_category_node.findall('ScheduleInfo'):
                game_id = xml_value(game_node, 'GameID', int)
                if game_id:
                    ftp_games_ids.append(game_id)

        # delete already synced games
        ids_diff = set(games_ids) - set(ftp_games_ids)
        if ftp_games_ids and ids_diff:
            await async_delete(Game, id__in=ids_diff)
            logger.info('Reconciled %d games', len(ids_diff))

        # process schedules
        for game_category_node in game_category_nodes:
            season_id = xml_value(game_category_node, 'SeasonID', int)

            for game_node in game_category_node.findall('ScheduleInfo'):
                game_id = xml_value(game_node, 'GameID', int)
                if not game_id:
                    logger.warn('GameID is undefined, skipped')
                    continue

                match_number = xml_value(game_node, 'MatchNumber', int)
                game_date_str = xml_value(game_node, 'GameDate')
                game_date = parse_date(game_date_str) if game_date_str else None
                game_time_str = xml_value(game_node, 'GameTime')  # HHMM, #HMM
                if game_date:
                    game_full_date = parse_datetime(game_date_str, game_time_str)
                else:
                    game_full_date = None
                schedule_no = int(game_node.attrib['ScheduleNO'])
                occasion_no = xml_value(game_node, 'OccasionNo', int)
                round = xml_value(game_node, 'Round', int)
                group_id = xml_value(game_node, 'GroupID')
                day_night = xml_value(game_node, 'DayNight', int)
                stadium_id = xml_value(game_node, 'StadiumID', int)
                site = xml_value(game_node, 'Site', str)
                home_team_id = xml_value(game_node, 'HomeTeamID', int)
                home_previous_game_id = xml_value(game_node, 'HomePreviousGameID', int)
                away_team_id = xml_value(game_node, 'AwayTeamID', int)
                away_previous_game_id = xml_value(game_node, 'AwayPreviousGameID', int)

                fake_team = await async_find_first(Club, ds_club_id=FAKE_TEAM_ID)
                home_team = await async_find_first(Club, ds_club_id=home_team_id)
                if not home_team:
                    logger.warn('Home team not found %d', home_team_id)
                    if not game_date or make_naive(game_date) < datetime.now():
                        continue
                    home_team = fake_team
                away_team = await async_find_first(Club, ds_club_id=away_team_id)
                if not away_team:
                    logger.warn('Away team not found %d', away_team_id)
                    if not game_date or make_naive(game_date) < datetime.now():
                        continue
                    away_team = fake_team

                home_previous_game = await async_find_first(Game, id=home_previous_game_id)
                away_previous_game = await async_find_first(Game, id=away_previous_game_id)

                game = await async_find_first(Game, id=game_id)
                if game:
                    game.year = year
                    game.competition_id = competition_id
                    game.match_number = match_number
                    game.season_id = season_id
                    game.schedule_no = schedule_no

                    _should_update_info = game.situation_id is None or game.situation_id in {  # Game not started
                        1,
                        6,
                        7,
                    }  # noqa Game not started or moved to another date

                    if not game.game_date or _should_update_info:
                        game.game_date = game_date
                    if not game.game_full_date or _should_update_info:
                        game.game_full_date = game_full_date
                    if not game.game_date_raw or _should_update_info:
                        game.game_date_raw = game_date_str
                    if not game.game_time_raw or _should_update_info:
                        game.game_time_raw = game_time_str
                    if not game.occasion_no or _should_update_info:
                        game.occasion_no = occasion_no
                    if not game.round or _should_update_info:
                        game.round = round
                    if not game.group_id or _should_update_info:
                        game.group_id = group_id
                    if not game.day_night or _should_update_info:
                        game.day_night = day_night
                    game.stadium_id = stadium_id
                    game.site = site
                    game.home_team = home_team
                    game.home_previous_game = home_previous_game
                    game.away_team = away_team
                    game.away_previous_game = away_previous_game

                    await async_save_item(game)
                else:
                    await async_upsert(
                        Game,
                        id=game_id,
                        defaults={
                            'year': year,
                            'competition_id': competition_id,
                            'match_number': match_number,
                            'schedule_no': schedule_no,
                            'season_id': season_id,
                            'game_date': game_date,
                            'game_full_date': game_full_date,
                            'game_date_raw': game_date_str,
                            'game_time_raw': game_time_str,
                            'occasion_no': occasion_no,
                            'round': round,
                            'group_id': group_id,
                            'day_night': day_night,
                            'stadium_id': stadium_id,
                            'site': site,
                            'home_team': home_team,
                            'home_previous_game': home_previous_game,
                            'away_team': away_team,
                            'away_previous_game': away_previous_game,
                        },
                    )

        logger.info('Processed schedules')


async def import_last_5_games(ftp_client: aioftp.Client, file_path: str, file_stat: dict):
    """Import last 5 games of club by competition from DS
    source file: /total/team/team_5thGame-{COMPETITION_ID}_{DS_CLUB_ID}.xml
    """
    async with process_path_if_needed(file_path, file_stat) as should_process:
        if not should_process:
            return

        # read XML file
        file_content = await read_file_to_str(ftp_client, file_path)
        root = ET.fromstring(file_content)  # Football node

        # process the last 5 game results
        for result_info_node in root.findall('Team5thGameReport/ResultInfo'):
            game_id = xml_value(result_info_node, 'GameID', int)
            if not game_id:
                logger.warn('GameID is undefined, skipped')
                continue

            # check game exists
            game: Game = await async_find_first(Game, id=game_id)
            if not game:
                logger.warn('Game not found (id=%s)', game_id)
                continue

            # update game score
            score = xml_value(result_info_node, 'Score', int)
            lost = xml_value(result_info_node, 'Lose', int)
            stat_for_home_team = xml_value(result_info_node, 'HV', lambda x: x == '1')

            if stat_for_home_team:
                game.home_score = score
                game.away_score = lost
            else:
                game.home_score = lost
                game.away_score = score

            game_result = xml_value(result_info_node, 'GameResult')
            if game_result in ('W', 'L', 'D') and not game.situation_id:
                # update game situation to finished
                game.situation_id = GameSituationEnum.FINISHED

            await async_save_item(game)

        logger.info('Processed the last 5 game result of club (file=%s)', file_path)


async def import_game_records(ftp_client: aioftp.Client, file_path: str, file_stat: dict):
    """Import game results of club by competition from DS
    source file: /total/team/match_record-{COMPETITION_ID}_{DS_CLUB_ID}.xml
    """
    async with process_path_if_needed(file_path, file_stat) as should_process:
        if not should_process:
            return

        # read XML file
        file_content = await read_file_to_str(ftp_client, file_path)
        root = ET.fromstring(file_content)  # Football node

        for game_node in root.findall('SecGameRecordReport/GameCategory/GameInfo'):
            game_id = xml_value(game_node, 'GameID', int)
            if not game_id:
                logger.warn('GameID is undefined, skipped')
                continue

            # check game exists
            game: Game = await async_find_first(Game, id=game_id)
            if not game:
                logger.warn('Game not found, game_id=%d', game_id)
                continue

            score = xml_value(game_node, 'Score', int)
            lost = xml_value(game_node, 'Lost', int)
            pk_score = xml_value(game_node, 'PKScore', int)
            pk_lost = xml_value(game_node, 'PKLost', int)

            # update game score
            stat_for_home_team = xml_value(game_node, 'HV', lambda x: x == '1')
            if stat_for_home_team:
                game.home_score = score
                game.away_score = lost
                game.home_pk_score = pk_score
                game.away_pk_score = pk_lost
            else:
                game.home_score = lost
                game.away_score = score
                game.home_pk_score = pk_score
                game.away_pk_score = pk_lost

            game_result = xml_value(game_node, 'GameResult')
            if game_result in ('W', 'L', 'D') and not game.situation_id:
                # update game situation to finished
                game.situation_id = GameSituationEnum.FINISHED

            # update game general info
            game.temperature = xml_value(game_node, 'Temperature', Decimal)
            game.spectators = xml_value(game_node, 'Spectators', int)
            game.humidity = xml_value(game_node, 'Humidity')

            await async_save_item(game)

        logger.info('Processed the game results (file=%s)', file_path)
