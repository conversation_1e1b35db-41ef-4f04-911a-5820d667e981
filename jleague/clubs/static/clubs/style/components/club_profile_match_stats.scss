@import 'setup/variable';
@import 'setup/mixin';

.body-club-profile {
  .match-stats {
    .header {
      display: flex;
      align-items: center;

      @media screen and (min-width: 1200px) {
        justify-content: space-between;
        margin-bottom: 180px;
      }

      @media screen and (max-width: 1199px) {
        flex-direction: column;
        margin-bottom: 150px;
      }

      @media screen and (max-width: 576px) {
        margin-bottom: 150px;
      }

      .league-info {
        display: inline-flex;
        align-items: center;

        .league-name {
          font-family: $font-overpass;
          font-style: normal;
          font-weight: 900;
          font-size: 24px;
          line-height: 28px;
          @include text_ellipsis();
          color: $color-black;
          margin-left: 16px;

          @media screen and (max-width: 767px) {
            font-size: 20px;
            line-height: 24px;
          }

          @media screen and (max-width: 576px) {
            font-size: 16px;
            line-height: 20px;
          }
        }
      }

      .club-info {
        display: flex;
        flex-direction: column;

        .club-emblem {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          top: -182px;
          overflow: hidden;
          text-align: center;
          width: 400px;
          height: 400px;

          @media screen and (max-width: 1199px) {
            top: -120px;
            width: 300px;
            height: 300px;
          }

          @media screen and (max-width: 767px) {
            top: -103px;
            width: 250px;
            height: 250px;
          }

          .club-emblem {
            height: auto;
            width: 100%;

            &.club-emblem--placeholder {
              width: 90%;
            }
          }
        }

        .club-links-wrapper {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          top: 180px;
          width: 400px;
          overflow: hidden;

          .club-links {
            display: flex;
            justify-content: center;
            gap: 16px;
          }

          .club-link-intro {
            color: $color-earth-4;
            font-family: $font-barlow-condensed;
            font-style: normal;
            font-weight: 600;
            font-size: 18px;
            line-height: 20px;
            text-align: center;
            letter-spacing: 1.5px;
            text-transform: uppercase;
            margin-bottom: 16px;
          }

          .jl-button {
            height: 40px;
            flex: 0 0 180px;

            .jl-button__label {
              font-size: 16px;
              line-height: 20px;
            }
          }

          @media screen and (max-width: 1199px) {
            top: 280px;
          }

          @media screen and (max-width: 576px) {
            top: 260px;
            width: 100%;

            .club-links {
              flex-direction: column;
              padding: 0 26px;
            }

            .jl-button {
              flex: auto;
            }
          }
        }
      }

      .social-container {
        display: flex;
        align-items: center;
        gap: 16px;

        @media screen and (max-width: 1199px) {
          margin-top: 18px;
        }

        .btn-img-circle {
          & > svg {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: auto;
            max-height: 60%;
          }

          &:hover {
            & > svg path {
              fill: $color-black;
            }
          }
        }

        @media screen and (max-width: 576px) {
          gap: 8px;
        }
      }
    }

    .stat-list {
      @media screen and (min-width: 1200px) {
        margin-top: 62px;

        display: flex;
        justify-content: space-between;

        .stat-item {
          width: calc((100% - 23px * 2) / 3);
        }
      }

      @media screen and (max-width: 1199px) and (min-width: 768px) {
        margin-top: 40px;

        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: stretch;

        .stat-item:nth-child(1) {
          width: 100%;

          .info-container {
            height: unset;
          }
        }

        .stat-item:nth-child(2),
        .stat-item:nth-child(3) {
          margin-top: 32px;
          width: calc((100% - 20px) / 2);

          .info-container {
            flex: auto;
            height: unset;
          }
        }
      }

      @media screen and (max-width: 767px) {
        margin-top: 32px;

        .stat-item + .stat-item {
          margin-top: 20px;
        }
      }

      @media screen and (max-width: 576px) {
        margin-top: 0;
      }
    }

    .stat-item {
      flex: 0 0 auto;
      display: flex;
      flex-direction: column;
      align-items: center;

      .stat-item__footer {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        justify-content: center;
        gap: 16px;
        padding-top: 32px;

        @media screen and (max-width: 1199px) {
          padding-top: 18px;
        }
      }

      .info-container {
        width: 100%;
        height: 300px;
        padding: 25px;
        background: $color-earth-1;
        border-radius: 13px;

        @media screen and (max-width: 767px) {
          height: unset;
        }

        &:hover {
          opacity: 1;
        }

        & > div:nth-child(1) {
          color: $color-black;
          font-family: $font-overpass;
          font-style: normal;
          font-weight: 700;
          font-size: 24px;
          line-height: normal;

          @media screen and (max-width: 1199px) {
            font-size: 20px;
          }

          @media screen and (max-width: 767px) {
            font-size: 18px;
            line-height: 18px;
          }
        }
      }

      .jl-button {
        height: 40px;
        padding-top: 0;
        padding-bottom: 0;

        .jl-button__label {
          font-size: 16px;
          line-height: 20px;
        }
      }

      @media screen and (max-width: 1199px) and (min-width: 768px) {
        &:not(.next-match) {
          .stat-item__footer {
            height: 58px;
          }
        }
      }
    }

    .stat-item.next-match {
      .info-container {
        .league-container {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .competition-logo {
            width: auto;
            height: 32px;

            @media screen and (max-width: 1199px) {
              height: 24px;
            }
          }
        }

        .occasion-container {
          margin-top: 10px;

          @media screen and (max-width: 1199px) {
            margin-top: 16px;
          }

          @media screen and (max-width: 767px) {
            display: flex;
            justify-content: space-around;
            align-items: center;
          }

          .occasion-no {
            text-align: center;
            color: $color-earth-4;
            font-family: $font-overpass;
            font-style: normal;
            font-weight: 900;
            font-size: 18px;
            line-height: 28px;

            @media screen and (max-width: 767px) {
              font-size: 16px;
              line-height: 16px;
            }
          }

          .date {
            text-align: center;
            color: $color-black;
            font-family: $font-jleague-kick;
            font-style: normal;
            font-weight: 700;
            font-size: 18px;
            line-height: 22px;

            @media screen and (max-width: 1199px) and (min-width: 768px) {
              margin-top: 7px;
            }

            @media screen and (max-width: 767px) {
              font-size: 16px;
              line-height: 16px;
            }
          }
        }

        .match-info {
          display: flex;
          align-items: center;
          justify-content: center;

          @media screen and (min-width: 1200px) {
            margin-top: -12px;
          }

          .team {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1 1;

            .club-emblem {
              width: 120px;
              height: 120px;

              @media screen and (max-width: 576px) {
                width: 80px;
                height: 80px;
              }
            }

            .team-name {
              color: $color-black;
              font-family: $font-jleague-kick;
              font-style: normal;
              font-weight: 700;
              font-size: 22px;
              line-height: 26px;
              @include text_ellipsis();
              margin-top: -12px;
            }
          }

          .time-panel {
            flex: 0 0 auto;
            width: 54px;
            height: 32px;
            background: $color-earth-3;
            border-radius: 16px;
            color: $color-black;
            font-family: $font-jleague-kick;
            font-style: normal;
            font-weight: 700;
            font-size: 20px;
            line-height: 20px;
            display: inline-flex;
            justify-content: center;
            align-items: center;
          }
        }

        .stadium {
          margin-top: 12px;
          display: flex;
          justify-content: center;
          align-items: center;

          & > img {
            margin-right: 4px;
            width: auto;
            height: 24px;
          }

          & > span {
            color: $color-black;
            font-family: $font-barlow-condensed;
            font-style: normal;
            font-weight: 500;
            font-size: 18px;
            line-height: 22px;
            @include text_ellipsis();
          }
        }
      }
    }

    .stat-item.last-5-matches {
      .match-list {
        margin-top: 8px;
      }

      .match-item {
        display: flex;
        align-items: center;

        .club-emblem {
          flex: 0 0 auto;
          width: 40px;
          height: 40px;
        }

        .team-name {
          flex: auto;
          margin-left: 8px;
          font-family: $font-jleague-kick;
          font-style: normal;
          font-weight: 700;
          font-size: 20px;
          line-height: 24px;
          color: $color-black;
          white-space: nowrap;
          text-overflow: ellipsis;
          max-width: 100%;
          overflow: hidden;
        }

        .league-icon-container {
          flex: 0 0 auto;
          width: 32px;
          height: 32px;
          display: flex;
          justify-content: center;
          align-items: center;

          .league-icon {
            width: auto;
            height: 24px;
          }
        }

        .score {
          flex: 0 0 auto;
          width: 32px;
          font-family: $font-jleague-kick;
          font-style: normal;
          font-weight: 700;
          font-size: 24px;
          line-height: 24px;
          text-align: center;
          color: $color-black;
        }

        .result {
          flex: 0 0 auto;
          margin-left: 8px;
          width: 32px;
          height: 32px;
          border-radius: 16px;
          display: flex;
          justify-content: center;
          align-items: center;

          & > span {
            font-family: $font-overpass;
            font-style: normal;
            font-weight: 900;
            font-size: 16px;
            line-height: normal;
            color: $color-white;
          }

          &.W {
            background: $color-won;
          }

          &.L {
            background: $color-lost;
          }

          &.D {
            background: $color-drawn;
          }
        }
      }

      .match-item + .match-item {
        margin-top: 2px;
      }
    }

    .stat-item.current-standing {
      .standing-list {
        margin-top: 8px;
      }

      .standing-item {
        display: flex;
        align-items: center;

        .ranking {
          flex: 0 0 auto;
          width: 14px;
          text-align: right;
          color: $color-earth-4;
        }

        .standing-replace {
          flex: 0 0 auto;
          margin-left: 8px;
        }

        .club-emblem {
          flex: 0 0 auto;
          margin-left: 8px;
          width: 40px;
          height: 40px;
        }

        .club-info__link {
          display: flex;
          flex: 1;
          align-items: center;
          overflow: hidden;
        }

        .team-name,
        .games-count,
        .goal-difference,
        .ranking,
        .points {
          font-family: $font-jleague-kick;
          font-style: normal;
          font-weight: 700;
          font-size: 16px;
          line-height: 19px;
          word-break: break-word;
        }

        .team-name {
          flex: auto;
          margin-left: 3px;
          font-size: 20px;
          line-height: 24px;
          color: $color-black;
          @include text_ellipsis();
        }

        .games-count {
          flex: 0 0 auto;
          width: 20px;
          color: $color-earth-4;
          text-align: right;
        }

        .goal-difference {
          flex: 0 0 auto;
          margin-left: 18px;
          width: 20px;
          color: $color-earth-4;
          text-align: right;
        }

        .points {
          flex: 0 0 auto;
          margin-left: 18px;
          width: 20px;
          color: $color-black;
          text-align: right;
        }
      }

      .standing-item.highlight {
        background: $color-white;
        box-shadow: 0px -1px 0px 0px $color-earth-2 inset;
      }

      .standing-item:not(:last-child) {
        box-shadow: inset 0px -1px 0px $color-earth-2;
      }
    }

    @media screen and (min-width: 1200px) {
      .stat-item {
        &.current-standing {
          .info-container {
            padding: 25px 20px;
          }

          .standing-item {
            height: 42px;
            padding: 0 18px;
          }
        }
      }
    }

    @media screen and (max-width: 1199px) and (min-width: 768px) {
      .stat-item {
        &.last-5-matches {
          .match-item {
            height: 60px;
          }
        }

        &.current-standing {
          .info-container {
            height: unset;
          }

          .standing-item {
            height: 60px;
            padding: 0 24px;
          }
        }
      }
    }

    @media screen and (max-width: 767px) {
    }

    @media screen and (max-width: 576px) {
      .stat-item {
        &.last-5-matches {
          .match-item {
            .score {
              font-size: 20px;
              width: 48px;
              margin: 0;
            }

            .club-emblem {
              height: 32px;
              width: 32px;
              margin: 0 2px;
            }

            .team-name {
              font-size: 16px;
              line-height: 20px;
            }
          }
        }

        &.current-standing {
          .standing-item {
            .standing-replace {
              max-height: 24px;
              max-width: 24px;
              height: 24px;
              width: 24px;
            }

            .club-emblem {
              height: 32px;
              width: 32px;
              margin: 0 2px;
            }

            .team-name {
              font-size: 16px;
              line-height: 20px;
            }
          }
        }
      }
    }

    @media screen and (max-width: 370px) {
      .stat-item {
        &.current-standing {
          .standing-item {
            .standing-replace {
              margin-left: 0;
            }
          }
        }
      }
    }
  }
}

body.lang-id {
  .match-stats {
    .stat-item.next-match {
      .info-container {
        .league-container {
          & > span {
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
      }
    }
  }
}

body.lang-th {
  .match-stats {
    .header {
      .league-info .league-name,
      .club-info .club-links-wrapper .club-link-intro,
      .jl-button .jl-button__label {
        @include font-thai();
      }
    }

    .stat-item {
      .info-container {
        & > div:nth-child(1) {
          @include font-thai();
        }
      }

      .jl-button .jl-button__label {
        @include font-thai();
      }

      &.next-match {
        .info-container {
          .occasion-container .occasion-no,
          .occasion-container .date,
          .match-info .team .team-name,
          .stadium > span {
            @include font-thai();
          }
        }
      }

      &.last-5-matches {
        .match-item .team-name {
          @include font-thai();
        }
      }

      &.current-standing {
        .standing-item .team-name {
          @include font-thai();
        }
      }
    }
  }
}

body.lang-vi {
  .match-stats {
    .header {
      .league-info .league-name,
      .club-info .club-links-wrapper .club-link-intro,
      .jl-button .jl-button__label {
        @include font-vietnamese();
      }

      .league-info .league-name {
        font-weight: 600;
      }
    }

    .stat-item {
      .info-container {
        & > div:nth-child(1) {
          @include font-vietnamese();
          font-weight: 600;
        }
      }

      .jl-button .jl-button__label {
        @include font-vietnamese();
      }

      &.next-match {
        .info-container {
          .occasion-container .occasion-no,
          .occasion-container .date,
          .match-info .team .team-name,
          .stadium > span {
            @include font-vietnamese();
            font-weight: 600;
          }
        }
      }

      &.last-5-matches {
        .match-item .team-name {
          @include font-vietnamese();
          font-weight: 600;
        }
      }

      &.current-standing {
        .standing-item .team-name {
          @include font-vietnamese();
          font-weight: 600;
        }
      }
    }
  }
}
