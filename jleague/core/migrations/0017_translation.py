# Generated by Django 4.1.4 on 2023-01-05 09:42

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("core", "0016_auto_20221228_1807"),
    ]

    operations = [
        migrations.CreateModel(
            name="Translation",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "key",
                    models.CharField(
                        db_index=True,
                        help_text="Uppercase without spacing and any special letters (only underscore is allowed)",
                        max_length=255,
                        unique=True,
                    ),
                ),
                ("values", models.JSO<PERSON>ield(default=dict)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
