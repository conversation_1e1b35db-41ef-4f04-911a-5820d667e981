# Django built-in
DATE_FORMAT = "l, j F, Y"  # 'Wednesday, 1 March, 2023'
DATETIME_FORMAT = "l, j F, Y · H:i"  # 'Wednesday, 1 March, 2023 · 13:05'
DECIMAL_SEPARATOR = "."
SHORT_DATE_FORMAT = "D, j M"  # 'Wed, 13 Mar'
SHORT_DATETIME_FORMAT = "D, j M, Y · H:i"  # 'Wed, 13 Mar, 2023 · 13:05'
THOUSAND_SEPARATOR = ","
TIME_FORMAT = "H:i"  # 13:05
MONTH_DAY_FORMAT = "F j"  # March 1
YEAR_MONTH_FORMAT = "F Y"  # March 2023
# DATE_INPUT_FORMATS = [
#     '%Y-%m-%d',  # '2006-10-25'
#     '%m/%d/%Y',  # '10/25/2006'
#     '%m/%d/%y',  # '10/25/06'
#     '%b %d %Y',  # 'Oct 25 2006'
#     '%b %d, %Y',  # 'Oct 25, 2006'
#     '%d %b %Y',  # '25 Oct 2006'
#     '%d %b, %Y',  # '25 Oct, 2006'
#     '%B %d %Y',  # 'October 25 2006'
#     '%B %d, %Y',  # 'October 25, 2006'
#     '%d %B %Y',  # '25 October 2006'
#     '%d %B, %Y',  # '25 October, 2006'
# ]
# DATETIME_INPUT_FORMATS = [
#     '%Y-%m-%d %H:%M:%S',     # '2006-10-25 14:30:59'
#     '%Y-%m-%d %H:%M:%S.%f',  # '2006-10-25 14:30:59.000200'
#     '%Y-%m-%d %H:%M',        # '2006-10-25 14:30'
#     '%m/%d/%Y %H:%M:%S',     # '10/25/2006 14:30:59'
#     '%m/%d/%Y %H:%M:%S.%f',  # '10/25/2006 14:30:59.000200'
#     '%m/%d/%Y %H:%M',        # '10/25/2006 14:30'
#     '%m/%d/%y %H:%M:%S',     # '10/25/06 14:30:59'
#     '%m/%d/%y %H:%M:%S.%f',  # '10/25/06 14:30:59.000200'
#     '%m/%d/%y %H:%M',        # '10/25/06 14:30'
# ]
# TIME_INPUT_FORMATS = [
#     '%H:%M:%S',     # '14:30:59'
#     '%H:%M:%S.%f',  # '14:30:59.000200'
#     '%H:%M',        # '14:30'
# ]
# FIRST_DAY_OF_WEEK = 0  # Sunday
# NUMBER_GROUPING = 0

# Custom Format
UPCOMING_DATE_FORMAT = "l, j M"  # 'Wednesday, 1 Mar'
FIXTURE_DATE_FORMAT = "l, j M, Y"  # 'Wednesday, 1 Mar 2023'
FIXTURE_SHORT_DATE_FORMAT = "D, j M"  # 'Wed, 13 Mar'
SHORT_DATE_YEAR_FORMAT = "j M, Y"  # '1 Mar 2023'
FULL_SHORT_DATE_FORMAT = "D, j M, Y"  # 'Wed, 13 Mar 2023'
TIME_ZONE_FORMAT = "H:i T"  # 13:05 JST
HOUR_FORMAT = "H"  # 01..59
MINUTE_FORMAT = "i"  # 01..59
TIME_ON_DATE_FORMAT = "H:i T, j F"  # 15:00 JST on 1 May
YEAR_FORMAT = "Y"  # 2023

TMINTERNAL_FIXTURE_FORMAT = "\\N\g\à\y d.m.Y"  # Ngày 05.08.2023
JERSEYS_WINNER_FORMAT = "j\\/n"  # 31/8
# CEREZO_MDTOOL_WEEKDAY_FORMAT = "l"  # 'Wednesday'
# CEREZO_MDTOOL_MATCH_DATE_FORMAT = "j/m"  # '21/10'
