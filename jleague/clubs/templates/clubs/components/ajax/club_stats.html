{% load core_tags %}

{% if statistics %}
  {% with club_slug=statistics.club.slug %}
    <div class="overall-stats team-bg team-bg--{{ club_slug }}">
      <div class="league-name team-text team-text--{{ club_slug }}">
        {% translation_obj statistics.competition.t_name %}
      </div>

      <div class="stats-list">
        <div class="stats-item">
          <div class="stats">
            <div class="stats__label team-text team-text--{{ club_slug }}">
              {% translation 'CLUB_PROFILE_PAGE_MATCHES_PLAYED' %}
            </div>

            <div class="stats__value team-text team-text--{{ club_slug }}">
              {{ statistics.games_played }}
            </div>
          </div>

          <div class="stats">
            <div class="stats__label team-text team-text--{{ club_slug }}">
              {% translation 'CLUB_PROFILE_PAGE_WINS' %}
            </div>

            <div class="stats__value team-text team-text--{{ club_slug }}">
              {{ statistics.games_won }}
            </div>
          </div>

          <div class="stats">
            <div class="stats__label team-text team-text--{{ club_slug }}">
              {% translation 'CLUB_PROFILE_PAGE_DRAWS' %}
            </div>

            <div class="stats__value team-text team-text--{{ club_slug }}">
              {{ statistics.games_drawn }}
            </div>
          </div>

          <div class="stats">
            <div class="stats__label team-text team-text--{{ club_slug }}">
              {% translation 'CLUB_PROFILE_PAGE_LOSSES' %}
            </div>

            <div class="stats__value team-text team-text--{{ club_slug }}">
              {{ statistics.games_lost }}
            </div>
          </div>
        </div>

        <div class="stats-item">
          <div class="stats">
            <div class="stats__label team-text team-text--{{ club_slug }}">
              {% translation 'CLUB_PROFILE_PAGE_GOALS_FOR' %}
            </div>

            <a class="stats__value team-text team-text--{{ club_slug }}" href="{% url 'stats_club' statistics.competition.slug selected_year 'goals' %}">
              {{ statistics.goals }}
            </a>
          </div>

          <div class="stats">
            <div class="stats__label team-text team-text--{{ club_slug }}">
              {% translation 'CLUB_PROFILE_PAGE_GOALS_AGAINST' %}
            </div>

            <a class="stats__value team-text team-text--{{ club_slug }}" href="{% url 'stats_club' statistics.competition.slug selected_year 'goals_conceded' %}">
              {{ statistics.goals_conceded }}
            </a>
          </div>

          <div class="stats">
            <div class="stats__label team-text team-text--{{ club_slug }}">
              {% translation 'CLUB_PROFILE_PAGE_GOALS_DIFFERENCE' %}
            </div>

            <div class="stats__value team-text team-text--{{ club_slug }}">
              {% if statistics.goal_difference > 0 %}+{% endif %}{{ statistics.goal_difference }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="wins-stats">
      <div class="home-wins team-bg team-bg--{{ club_slug }}">
        <div class="wins-stat-label team-text team-text--{{ club_slug }}">
          {% translation 'CLUB_PROFILE_PAGE_HOME_WINS' %}
        </div>

        <div class="wins-stat-value team-text team-text--{{ club_slug }}">
          <span>{{ statistics.home_wins }}</span>%
        </div>
      </div>

      <div class="away-wins team-bg team-bg--{{ club_slug }}">
        <div class="wins-stat-label team-text team-text--{{ club_slug }}">
          {% translation 'CLUB_PROFILE_PAGE_AWAY_WINS' %}
        </div>

        <div class="wins-stat-value team-text team-text--{{ club_slug }}">
          <span>{{ statistics.away_wins }}</span>%
        </div>
      </div>
    </div>
  {% endwith %}
{% else %}
  {% include 'components/data-empty-state.html' %}
{% endif %}
