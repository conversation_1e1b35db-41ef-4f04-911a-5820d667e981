from django.views.decorators.cache import cache_page
from django.http import Http404
from django.shortcuts import render
from core.helpers import get_translation_value, get_club_name
from utils.helpers import get_display_data_year, get_year_selector_choices
from utils.helpers.seo import update_seo_data_django_view
from utils.helpers.view_context import simple_page_context, custom_page_context
from .parsers import ClubParser
from .helper import get_league_clubs, send_alert_and_create_log



@cache_page(60 * 60 * 12)
def clubs(request):
    """View handler of Clubs
    url: /clubs/
    """

    context = simple_page_context("Clubs")
    context["club_leagues"] = get_league_clubs()

    # the seo part should be located at the bottom
    title = get_translation_value("SEO_CLUB_LIST_PAGE_TITLE")
    description = get_translation_value("SEO_CLUB_LIST_PAGE_DESCRIPTION")

    context = update_seo_data_django_view(
        context,
        {
            "title": title,
            "description": description,
        },
    )

    return render(request, "clubs/club_list_page.html", context)

@cache_page(60 * 60)
def club_profile(request, team_name=None):
    """Club profile view handler"""
    club = ClubParser(name_slug=team_name)
    if not club.club:
        raise Http404()

    club_info = club.format()
    send_alert_and_create_log(club_info)
    context = custom_page_context(
        template="clubs/components/club_profile_header.html",
        context={
            "page_slug": "club_profile",
            "years": get_year_selector_choices(),
            "selected_team": club.club.slug,
            "selected_year": get_display_data_year(),
            "next_match": club.next_match(),
            "last_5_matches": club.last_matches(),
            "standings": club.standings(),
            "club_leagues": get_league_clubs(),
            **club_info,  # include: competition, club, stadiums, latest_video, videos, mascots
        },
    )

    # the seo part should be located at the bottom
    club_slug = context["club"]["slug"]
    club_name = context["club"]["name"]

    club = get_club_name(context["club"])
    title = get_translation_value(
        "SEO_CLUB_PROFILE_PAGE_TITLE",
        club=club,
    )
    description = get_translation_value(
        "SEO_CLUB_PROFILE_PAGE_DESCRIPTION",
        club=club,
    )

    context = update_seo_data_django_view(
        context,
        {
            "title": title,
            "description": description,
            "keywords": f"J.LEAGUE,{club_name},{club_slug}",
        },
    )
    context["og_type"] = "profile"
    context["og_profile_username"] = club_slug
    context["og_profile_first_name"] = club_name

    return render(request, "clubs/club_profile_page.html", context)


@cache_page(60 * 60)
def club_stats(request, team_slug, year=None):
    """Ajax handler of Club's stats

    URL: /clubs/<team_slug>/stats/<year>/
    """
    club = ClubParser(slug=team_slug)
    if not club.club:
        raise Http404()

    year = year or get_display_data_year()

    context = {
        "selected_year": year,
        "statistics": club.statistics(year),
    }

    return render(request, "clubs/components/ajax/club_stats.html", context)


@cache_page(60 * 60)
def club_player_stats(request, team_slug, year=None):
    """Ajax handler of Club's player stats

    URL: /clubs/<team_slug>/players/stats/<year>/
    """
    club = ClubParser(slug=team_slug)
    if not club.club:
        raise Http404()

    year = year or get_display_data_year()

    context = {
        "player_stats": club.player_stats(year),
        "selected_year": year,
    }

    return render(request, "clubs/components/ajax/club_player_stats.html", context)


@cache_page(60 * 60)
def club_players(request, team_slug, year=None):
    """Ajax handler of Club's players

    URL: /clubs/<team_slug>/players/<year>/
    """
    club = ClubParser(slug=team_slug)
    if not club.club:
        raise Http404()

    year = year or get_display_data_year()

    context = club.players(year)

    return render(request, "clubs/components/ajax/club_players.html", context)
