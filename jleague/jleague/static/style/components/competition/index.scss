@import 'setup/variable';

.competition-logo {
  display: flex;

  img {
    width: 100%;
    height: auto;
  }

  &.competition-logo--j1-entry-play-off,
  &.competition-logo--friendly-match,
  &.competition-logo--afs {
    display: none;
  }
}

.competition-bg {
  &--j1,
  &--levain {
    background-color: $color-j1;
  }

  &--j1-entry-play-off {
    background-image: linear-gradient(90deg, $color-j1 0%, $color-j2 100%);
  }

  &--asia-challenge {
    background-color: $color-jlac-blue;
  }

  &--j2 {
    background-color: $color-j2;
  }

  &--j3 {
    background-color: $color-j3;
  }

  &--acl {
    background-color: $color-acl;
  }

  &--jwc {
    background-image: linear-gradient(90deg, $color-j1 0%, $color-black 100%);
  }

  &--friendly-match {
    background-color: $color-jl-red;
  }
}

@import 'j1';
@import 'j2';
@import 'j3';
@import 'levain';
@import 'acl';
@import 'emperor';
@import 'ffsc';
@import 'asia-challenge';
@import 'jwc';
@import 'friendly-match';
