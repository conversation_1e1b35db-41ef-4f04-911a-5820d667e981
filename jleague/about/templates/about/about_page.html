{% extends 'base.html' %}

{% load pipeline static wagtailcore_tags wagtailuserbar core_tags %}

{% block extra_styles %}
  {% stylesheet 'about-page' %}
{% endblock %}

{% block extra_scripts %}
  {% if '/cookies-policy/' in request.path %}
  <script>
    document.addEventListener('DOMContentLoaded', function (event) {
      if (cookieSettings) cookieSettings.initCookieSettings(true, '#cookie-policy-settings #cookie-settings');
    });
  </script>
  {% endif %}
{% endblock %}

{% block active_body_class %}template-about-page{% endblock %}

{% block content %}
  {% wagtailuserbar 'top-right' %}

  <main class="content-container">
    <section class="content-container__header">
      {% if not hide_breadcrumb %}
        {% include 'about/components/about_breadcrumb.html' with current_page=page.title %}
      {% endif %}

      {% if nav_items and nav_items|length > 0 %}
        {% include 'about/components/about_navigation.html' with items=nav_items selected=page.id %}
      {% endif %}
    </section>

    <article class="content-container__body">
      {% include_block page.body %}
    </article>

    {% if '/cookies-policy/' in request.path %}
    <div id="cookie-policy-settings" style="display:flex; justify-content:center;" >
      <button id="cookie-settings" class="jl-button button-settings" type="button">
        <span class="jl-button__label">{% translation 'GLOBAL_COOKIE_SETTINGS' %}</span>
      </button>
    </div>
    {% endif %}
  </main>
{% endblock %}
