import logging
import pytz
import typing as t
import xml.etree.ElementTree as ET
from asgiref.sync import sync_to_async

japan_tiimezone = pytz.timezone('Asia/Tokyo')
logger = logging.getLogger(__name__)


async def read_xml_file(file_path: str) -> str:
    xml_content = ET.parse(file_path)
    root = xml_content.getroot()
    return root


def xml_value(current_node, lookup_field, coerce_fn=str, default=None):
    node = current_node.find(lookup_field)
    if node is None or not node.text or not node.text.strip():
        return default
    return coerce_fn(node.text.replace('　', ' ').strip())


@sync_to_async
def async_upsert(model_klass, **kwargs) -> t.Tuple[t.Any, bool]:
    defaults = kwargs.pop('defaults')
    return model_klass.objects.update_or_create(
        defaults=defaults,
        **kwargs,
    )


@sync_to_async
def async_find_all(model_klass, **kwargs):
    return list(model_klass.objects.filter(**kwargs))


@sync_to_async
def async_find_first(model_klass, **kwargs):
    obj = model_klass.objects
    if 'select_related' in kwargs:
        select_related = kwargs.pop('select_related')
        if select_related:
            obj = obj.select_related(*select_related)
    return obj.filter(**kwargs).first()


@sync_to_async
def async_save_item(model_instance):
    return model_instance.save()


@sync_to_async
def async_delete_item(model_instance):
    return model_instance.delete()


@sync_to_async
def async_select_ids(model_klass, **kwargs):
    return list(model_klass.objects.filter(**kwargs).values_list('id', flat=True))


@sync_to_async
def async_delete(model_klass, **kwargs):
    return model_klass.objects.filter(**kwargs).delete()


def get_team_side(key):
    if key == '1':
        return 'home'
    elif key == '2':
        return 'away'
    return None
