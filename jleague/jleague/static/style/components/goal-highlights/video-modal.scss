.video-modal {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.25s ease-in-out;

  .video-modal__backdrop {
    background-color: transparentize($color-black, 0.1);
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    pointer-events: auto;
  }

  .video-modal__close-button {
    position: absolute;
    top: -20px;
    right: -20px;
    background: var(--color-white);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    z-index: 1001;
    display: flex;
    align-items: center;
    justify-content: center;

    i.mi-close {
      color: var(--color-black);
      font-size: 22px;
    }

    &:hover {
      cursor: pointer;

      i.mi-close {
        opacity: 0.6;
      }
    }
  }

  .video-modal__content {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    z-index: 1000;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    pointer-events: none;
  }

  .highlight-video-wrapper {
    height: auto;
    width: calc(100% - 100px);
    margin: auto;
    max-width: calc((100vh - 100px) * 1.77 - 280px) !important;
    pointer-events: auto;
    position: relative;
  }

  .highlight-video {
    overflow: hidden;
    margin: 0 auto;

    .highlight-video__body {
      display: flex;
      flex-direction: column;
      overflow: auto;
      position: relative;

      .highlight-video-player {
        aspect-ratio: 16/9;
        width: 100%;
        height: auto;
        border-radius: 16px 16px 0px 0px;

        .vjs-tech {
          border-radius: 16px 16px 0px 0px;
        }

        .vjs-control-bar {
          background: transparent;
        }
      }
    }

    .highlight-video__footer {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 56px;
      padding: 16px;
      background-color: var(--color-white);
      border-radius: 0px 0px 16px 16px;

      .highlight-video__footer__left {
      }

      .highlight-video__footer__right {
        .competition-matchweek__title {
          font-family: var(--font-barlow-condensed);
          font-style: normal;
          font-weight: 600;
          font-size: 12px;
          line-height: 16px;
          letter-spacing: 0.87px;
          text-transform: uppercase;
          color: var(--color-earth-4);

          &:hover {
            opacity: 1;
            text-decoration: underline;
          }
        }
      }
    }

    .actions {
      display: flex;
      gap: 16px;
      align-items: center;

      .actions__item {
      }

      .actions__separator {
        background-color: var(--color-earth-2);
        height: 4px;
        width: 4px;
        border-radius: 50%;
      }

      .action-button {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        background-color: transparent;
        border: none;
        position: relative;

        .action-button__icon {
          height: 24px;
          width: 24px;

          &::before {
            content: '';
            display: block;
            width: 100%;
            height: 100%;
            background-position: center;
            background-repeat: no-repeat;
            background-size: contain;
          }
        }

        .action-button__overlay-text,
        .action-button__text {
          color: var(--color-black);
          font-family: var(--font-barlow-condensed);
          font-style: normal;
          font-weight: 700;
          font-size: 14px;
          line-height: 17px;
          letter-spacing: 1.67px;
          text-transform: uppercase;
        }

        .action-button__overlay-text {
          display: none;
          position: absolute;
          top: 0px;
          left: 30px;
          background: var(--color-white);
          padding: 4px 8px;
          white-space: nowrap;
        }

        &.action-button--like {
          .action-button__icon {
            &::before {
              background-image: url('/static/images/icons/heart-outlined-1A1919.svg');
            }
          }

          &.action-button--highlight {
            .action-button__icon {
              &::before {
                background-image: url('/static/images/icons/heart-filled-D8232A.svg');
              }
            }

            .action-button__text {
              color: var(--color-red);
            }
          }
        }

        &.action-button--share {
          .action-button__icon {
            &::before {
              background-image: url('/static/images/icons/share-1A1919.svg');
            }
          }
        }

        &:hover {
          cursor: pointer;

          &.action-button--like {
            .action-button__icon {
              &::before {
                background-image: url('/static/images/icons/heart-outlined-D8232A.svg');
              }
            }
          }

          &.action-button--share {
            .action-button__icon {
              &::before {
                background-image: url('/static/images/icons/share-D8232A.svg');
              }
            }
          }

          .action-button__text {
            color: var(--color-red);
          }
        }
      }
    }
  }

  .highlight-video-nav {
    height: 152px;

    .videos-list {
      padding-top: 16px;

      .video-item {
        max-height: 136px;
        max-width: 240px;
        border-radius: 16px;

        .video-item__body {
          &::before {
            content: '';
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            height: 8px;
            width: 100%;
            transition: all 500ms ease-in-out;
          }
        }

        .score-board {
          overflow: hidden;

          .score-board__score {
            &:hover {
              opacity: 1;
              background: var(--color-earth-4);
            }
          }
        }

        &.video-item--active {
          background: var(--color-white);
          cursor: default;

          .video-item__body {
            &::before {
              background: var(--color-red);
            }

            .score-board,
            .video-info {
              pointer-events: auto;
            }
          }
        }

        &:hover {
          &:not(.video-item--active) {
            .video-item__body {
              &::before {
                background: var(--color-earth-3);
              }
            }
          }
        }
      }
    }
  }

  @media screen and (max-width: 767px) {
    .highlight-video {
      .highlight-video__footer {
        flex-direction: column;
        align-items: center;
        height: 104px;
        position: relative;

        .divider-line {
          &::before {
            content: '';
            position: absolute;
            left: 10px;
            right: 10px;
            height: 1px;
            background-color: var(--color-earth-2);
          }
        }
      }
    }
  }

  @media screen and (max-width: 576px) {
    .highlight-video-wrapper {
      width: calc(100% - 32px);
    }

    .video-modal__close-button {
      i.mi-close {
        font-size: 28px;
      }
    }

    .highlight-video {
      .highlight-video__footer {
        i.mi-heart,
        i.mi-share {
          font-size: 24px;
        }

        .cta-like,
        .cta-share {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }
    }
  }
}
