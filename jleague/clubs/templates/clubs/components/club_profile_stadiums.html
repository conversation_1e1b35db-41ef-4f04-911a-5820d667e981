{% load static wagtailcore_tags wagtailimages_tags humanize core_tags %}
{% translation_obj stadium.t_name as stadium_name %}
{% if stadium.featured_image %}
  {% image stadium.featured_image width-1440 format-webp-lossless as featured_image %}
{% endif %}

{% spaceless %}
<section id="stadiums" class="club-profile-stadiums">
  <div class="club-profile-stadiums__content">
    <div class="stadium-item">
      <div class="stadium-photo">
        <div class="swiper stadium-photo-slider">
          <div class="swiper-wrapper">
            {% if stadium.gallery_images %}
              {% for gallery_image in stadium.gallery_images %}
                <div class="swiper-slide stadium-image">
                  {% image gallery_image.image width-1200 format-webp-lossless as gallery_image_image %}
                  <img
                    src="{{ gallery_image_image.url }}"
                    {% if gallery_image.description %}
                      alt="{{ gallery_image.description }}"
                    {% else %}
                      alt="{{ stadium_name }}"
                    {% endif %}
                    loading="lazy"
                  />
                </div>
              {% endfor %}
            {% endif %}
          </div>

          <div class="swiper-button-next"></div>
          <div class="swiper-button-prev"></div>
          <div class="swiper-pagination"></div>
        </div>
      </div>

      <div
        class="stadium-info{% if featured_image %} lazy{% endif %}"
        {% if featured_image %}data-src="url({{ featured_image.url }})"{% endif %}
      >
        <div class="stadium-info__bg-overlay team-bg team-bg--{{ club.slug }}"></div>

        <div class="stadium-info__content content-container">
          <div class="info-grid">
            <div class="stadium-name team-text team-text--{{ club.slug }}">
              {{ stadium_name|name_space_break }}
            </div>

            <div class="stadium-city">
              <svg
                class="icon team-svg-color team-svg-color--{{ club.slug }}"
                width="32"
                height="41"
                viewBox="0 0 32 41"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M16 0.59082C7.2 0.59082 0 7.79082 0 16.5908C0 27.3908 14 39.5908 14.6 40.1908C15 40.3908 15.6 40.5908 16 40.5908C16.4 40.5908 17 40.3908 17.4 40.1908C18 39.5908 32 27.3908 32 16.5908C32 7.79082 24.8 0.59082 16 0.59082ZM16 35.9908C11.8 31.9908 4 23.3908 4 16.5908C4 9.99082 9.4 4.59082 16 4.59082C22.6 4.59082 28 9.99082 28 16.5908C28 23.1908 20.2 31.9908 16 35.9908ZM16 8.59082C11.6 8.59082 8 12.1908 8 16.5908C8 20.9908 11.6 24.5908 16 24.5908C20.4 24.5908 24 20.9908 24 16.5908C24 12.1908 20.4 8.59082 16 8.59082ZM16 20.5908C13.8 20.5908 12 18.7908 12 16.5908C12 14.3908 13.8 12.5908 16 12.5908C18.2 12.5908 20 14.3908 20 16.5908C20 18.7908 18.2 20.5908 16 20.5908Z"
                  fill="#FFFFFF"
                />
              </svg>

              <span class="name team-text team-text--{{ club.slug }}">
                {% if stadium.t_city %}
                  {% translation_obj stadium.t_city %}
                {% elif stadium.city %}
                  {{ stadium.city }}
                {% endif %}
              </span>
            </div>

            <div class="stadium-skeleton">
              {% if stadium.skeleton_image %}
                {% image stadium.skeleton_image fill-240x144 format-webp-lossless as skeleton_image %}
                <img
                  class="image lazy"
                  src="{{ skeleton_image.url }}"
                  alt="{{ stadium_name }}"
                  loading="lazy"
                />
              {% endif %}
            </div>

            <div class="stadium-capacity">
              <div class="label team-text team-text--{{ club.slug }}">
                {% translation 'CLUB_PROFILE_PAGE_BIO_CAPACITY' %}
              </div>

              <div class="value team-text team-text--{{ club.slug }}">
                {{ stadium.capacity|intcomma }}
              </div>
            </div>

            {% if stadium.t_address %}
              <div class="stadium-address">
                <div class="label team-text team-text--{{ club.slug }}">
                  {% translation 'CLUB_PROFILE_PAGE_BIO_ADDRESS' %}
                </div>

                <div class="value team-text team-text--{{ club.slug }}">
                  {% translation_obj stadium.t_address %}
                </div>
              </div>
            {% endif %}

            {% if stadium.t_stations %}
              <div class="stadium-station">
                <div class="label team-text team-text--{{ club.slug }}">
                  {% translation 'CLUB_PROFILE_PAGE_BIO_THE_NEAREST_STATION' %}
                </div>

                <div class="value team-text team-text--{{ club.slug }}">
                  {% translation_obj stadium.t_stations %}
                </div>
              </div>
            {% endif %}
          </div>
        </div>
      </div>

      <div class="stadium-map" id="map">
        <iframe
          {% if stadium.lat and stadium.lon %}
            src="https://maps.google.com/maps?output=embed&q={{ stadium.lat }},{{ stadium.lon }}&t=m&hl=en&z=14"
          {% elif stadium.address %}
            src="https://maps.google.com/maps?output=embed&q={{ stadium.name }}, {{ stadium.address }}&t=m&hl=en&z=14"
          {% elif stadium.city %}
            src="https://maps.google.com/maps?output=embed&q={{ stadium.name }}, {{ stadium.city }}&t=m&hl=en&z=14"
          {% endif %}
          allowfullscreen=""
          frameborder="0"
        ></iframe>
      </div>
    </div>
  </div>
</section>
{% endspaceless %}

