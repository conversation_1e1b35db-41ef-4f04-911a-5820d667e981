import logging
import aioftp
from ..helpers import process_path_if_needed

logger = logging.getLogger(__name__)


async def import_live_stats(ftp_client: aioftp.Client, file_path: str, file_stat: dict):
    async with process_path_if_needed(file_path, file_stat) as should_process:
        if not should_process:
            return
        logger.info('Importing live_stats.xml, but not implemented yet.')
