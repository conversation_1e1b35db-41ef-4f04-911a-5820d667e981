# Generated by Django 3.1.13 on 2022-05-24 08:53

from django.db import migrations
from core.enums import TeamMemberTypeEnum


def patch_game_details(apps, schema_editor):
    Game = apps.get_model('data_stadium', 'Game')

    games = Game.objects.all()

    for game in games:
        details = game.details
        curr_substitutions = details.get('substitutions')
        curr_goals = details.get('goals')
        curr_warnings = details.get('warnings')
        curr_penalties = details.get('penalties')
        curr_home_team_staffs = details.get('home_team_staffs')
        curr_away_team_staffs = details.get('away_team_staffs')

        substitutions = []
        goals = []
        warnings = []
        penalties = []
        home_team_staffs = []
        away_team_staffs = []

        # substitutions
        if curr_substitutions and len(curr_substitutions):
            for s in curr_substitutions:
                if s.get('in') is not None and s.get('out') is not None:
                    substitutions.append(
                        {
                            'state_id': s['state_id'],
                            'time': s['time'],
                            'team': s['team'],
                            'in_player_id': s['in']['player_id'],
                            'in_uniform_no': s['in']['uniform_no'],
                            'out_player_id': s['out']['player_id'],
                            'out_uniform_no': s['out']['uniform_no'],
                        }
                    )

            details['substitutions'] = substitutions

        # goals
        if curr_goals and len(curr_goals):
            for g in curr_goals:
                if g.get('player') is not None:
                    goals.append(
                        {
                            'state_id': g['state_id'],
                            'time': g['time'],
                            'player_id': g['player']['player_id'],
                            'uniform_no': g['player']['uniform_no'],
                            'team_id': g['team_id'],
                            'team': g['team'],
                            'is_penalty_goal': g['is_penalty_goal'],
                            'is_own_goal': g['is_own_goal'],
                            'score_team_id': g['score_team_id'],
                        }
                    )

            details['goals'] = goals

        # warnings
        if curr_warnings and len(curr_warnings):
            for w in curr_warnings:
                if w.get('player') is not None:
                    warnings.append(
                        {
                            'state_id': w['state_id'],
                            'time': w['time'],
                            'player_id': w['player']['player_id'],
                            'uniform_no': w['player']['uniform_no'],
                            'player_name': w['player']['name'],
                            'team_id': w['team_id'],
                            'team': w['team'],
                            'type': w['type'],
                            'is_second_yellow_card': w['is_second_yellow_card'],
                        }
                    )

            details['warnings'] = warnings

        # penalties
        if curr_penalties and len(curr_penalties):
            for p in curr_penalties:
                if p.get('player') is not None:
                    penalties.append(
                        {
                            'no': p['no'],
                            'serial_no': p['serial_no'],
                            'player_id': p['player']['player_id'],
                            'uniform_no': p['player']['uniform_no'],
                            'team_id': p['team_id'],
                            'team': p['team'],
                            'success': p['success'],
                        }
                    )

            details['penalties'] = penalties

        # home staffs
        if curr_home_team_staffs and len(curr_home_team_staffs):
            for s in curr_home_team_staffs:
                if s.get('post') == 'ヘッドコーチ':
                    home_team_staffs.append(
                        {
                            'staff_id': s['id'],
                            'team': s['team'],
                            'position': TeamMemberTypeEnum.HEAD_COACH.value,
                        }
                    )

            details['home_team_staffs'] = home_team_staffs

        # away staffs
        if curr_away_team_staffs and len(curr_away_team_staffs):
            for s in curr_away_team_staffs:
                if s.get('post') == 'ヘッドコーチ':
                    away_team_staffs.append(
                        {
                            'staff_id': s['id'],
                            'team': s['team'],
                            'position': TeamMemberTypeEnum.HEAD_COACH.value,
                        }
                    )

            details['away_team_staffs'] = away_team_staffs

        game.details = details
        game.save()


class Migration(migrations.Migration):
    dependencies = [
        ('data_stadium', '0048_delete_playerphoto'),
    ]

    operations = [migrations.RunPython(patch_game_details)]
