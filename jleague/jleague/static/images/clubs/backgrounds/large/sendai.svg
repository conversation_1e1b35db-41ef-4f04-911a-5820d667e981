<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 2800 1400" style="enable-background:new 0 0 2800 1400;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#C32F2A;}
	.st1{fill:#2F277E;}
	.st2{fill:#CA772B;}
	.st3{fill:none;}
	.st4{clip-path:url(#SVGID_4_);}
	.st5{fill:#0091C7;}
	.st6{fill:#DC6F2C;}
	.st7{fill:#003F6C;}
	.st8{fill:#A12340;}
	.st9{fill:#2D2860;}
	.st10{fill:#BA9A55;}
	.st11{fill:#8F273F;}
	.st12{fill:#E5AD33;}
	.st13{fill:#D9622B;}
	.st14{clip-path:url(#SVGID_6_);fill:#FFFFFF;}
	.st15{fill:#FFEE3A;}
	.st16{fill:#1D6735;}
	.st17{clip-path:url(#SVGID_10_);fill:#F5C246;}
	.st18{clip-path:url(#SVGID_10_);}
	.st19{fill:#C02B22;}
	.st20{fill:#FFFFFF;}
	.st21{fill:#3D4390;}
	.st22{fill:#005BA4;}
	.st23{opacity:0.6;clip-path:url(#SVGID_12_);}
	.st24{fill:#23408F;}
	.st25{clip-path:url(#SVGID_14_);fill:#004417;}
	.st26{clip-path:url(#SVGID_14_);fill:#1D6735;}
	.st27{clip-path:url(#SVGID_16_);fill:#1C2761;}
	.st28{clip-path:url(#SVGID_16_);}
	.st29{fill:#00588D;}
	.st30{clip-path:url(#SVGID_16_);fill:url(#SVGID_17_);}
	.st31{clip-path:url(#SVGID_16_);fill:url(#SVGID_18_);}
	.st32{fill:#183B86;}
	.st33{fill:#A82F42;}
	.st34{fill:#D7592A;}
	.st35{fill:#005CA3;}
	.st36{fill:#E59532;}
	.st37{fill:#A13221;}
	.st38{fill:#1D1D1B;}
	.st39{fill:#FDDE39;}
	.st40{fill:#C42E41;}
	.st41{fill:#008B60;}
	.st42{clip-path:url(#SVGID_20_);fill:#2A3486;}
	.st43{clip-path:url(#SVGID_20_);fill:#ECEFFD;}
	.st44{clip-path:url(#SVGID_20_);fill:#7CA93F;}
	.st45{clip-path:url(#SVGID_22_);}
	.st46{opacity:0.8;fill:#F7F7F7;}
	.st47{fill:#F2F2F2;}
	.st48{clip-path:url(#SVGID_24_);fill:#385779;}
	.st49{clip-path:url(#SVGID_24_);fill:url(#SVGID_25_);}
	.st50{clip-path:url(#SVGID_24_);fill:url(#SVGID_26_);}
	.st51{clip-path:url(#SVGID_24_);}
	.st52{fill:#1E1B2F;}
	.st53{clip-path:url(#SVGID_24_);fill:#FBDA39;}
	.st54{clip-path:url(#SVGID_24_);fill:none;}
	.st55{clip-path:url(#SVGID_30_);fill:#4BABE8;}
	.st56{clip-path:url(#SVGID_30_);}
	.st57{fill:#74CEFF;}
	.st58{clip-path:url(#SVGID_32_);}
	.st59{fill:#AE002A;}
	.st60{fill:#C40012;}
	.st61{clip-path:url(#SVGID_34_);fill:#154E99;}
	.st62{clip-path:url(#SVGID_34_);}
	.st63{fill:#2A3486;}
	.st64{fill:#0061A9;}
	.st65{fill:none;stroke:#2F87D0;stroke-width:2;stroke-miterlimit:10;}
	.st66{clip-path:url(#SVGID_34_);fill:none;}
</style>
<g>
	<defs>
		<rect id="SVGID_9_" width="2800" height="1401.1"/>
	</defs>
	<clipPath id="SVGID_2_">
		<use xlink:href="#SVGID_9_"  style="overflow:visible;"/>
	</clipPath>
	<rect x="0" y="0" style="clip-path:url(#SVGID_2_);fill:#F5C246;" width="2800" height="1401.1"/>
	<g style="clip-path:url(#SVGID_2_);">
		<path class="st19" d="M2800.8-1671.8c-779.8,154.5-1193.6,1131.5-1650,1226c170.6-267,360.4-296.6,668.9-619.9
			c185.7-194.6,264-391.5,320.3-602.1c-145.3,105.3-350,127.5-668.9,372.3C1081.7-996.7,1027.7-617.8,514.6-72
			C356.5,96,176.4,167.2,114.1,123.7C70.2,93,113.9,4,209-58.7c65.3-43,140.9-63.8,249.2-157.2c123.9-106.9,127.5-212.1,132-269.9
			C550.1-403,249.6-513,49.8-293.5C-176.3-45-351,0.6-375.3-27.6c-37.9-43.9,71.2-213.3,342.6-324.6
			c271.4-111.2,649.3-153.2,912.1-357.7c159.6-124.2,248.2-368.8,327.3-493.4c-41.4,21.1-209.7,62.8-359.1,117.6
			c-312.2,114.7-517.2,482-1122,660C-1190-156.1-1635,534.2-1866.1,982.9c159-206.6,485.2-312.2,580.2-241
			c94.9,71.2-39.5,163.6,120.7,272.4c160.2,108.8,377.8,37.2,490.9-16c100.9-47.5,183.9-124.6,183.9-213.6
			c0-148.3-188.7-268.1-268.8-326c23.1-2.8,45.4-10.2,65.7-23c-71.9-34.8-123.2-105.1-131.5-188.1c-0.2-2.2-0.2-4.5-0.2-6.8
			c0-53.3,39.5-76.4,137.5-76.4c91.8,0,255.7,20.2,315.8,22.4l-54.9,30.4C-300.2,230.5-86.6,341.7-5,365.5
			c-29.7,6.9-106.7,19.8-154.2,24.7c138.2,87.7,302.2,138.4,478,138.4c409.3,0,719.2-241.9,860.2-464.2
			c175-275.9,433.1-358.9,505-410.8c-33.4,78.6-71.9,268.5-267.7,452.4c-195.8,183.9-597.4,341.2-913.6,505
			c-450.9,233.5-753.4,900.9-955.1,930.6c388.6,54.9,891.3-100.8,1293.3-628.8c314.9-413.7,579.4-575.4,682.2-611
			c112.4-38.9,195-31.2,189.8,16.3c-7.9,72.7-173,99.1-350,185.4c-158.2,77.1-274.9,197.7-355.9,369.8
			c191.2-236.3,429.6-338.1,949.2-236.3c349.3,68.4,679.4,40.1,844.7-99.6V-1671.8z"/>
	</g>
	<g style="clip-path:url(#SVGID_2_);">
		<path class="st21" d="M1589.4-657.6C1315.4-408,1113.8-297.8,946.3-214l-19.5,9.7l4.7-21.3c63.2-286,195.2-418,335-557.8
			c193.9-193.9,271.4-304,338.2-400.1c-142.9,71.3-365.3,311.3-694.1,748.4C729.6-194.4,550-12.7,391.2,90.3
			c-132.7,86.1-246,113-311.1,74C52.9,148,37.8,120.2,36.3,83.9C34,30.2,61-41.5,112.3-117.8c44.7-66.5,102.7-129,165.3-178.7
			c-82.2,10.9-207,84.6-379.5,223.9c-118.9,96-248.4,127.9-314.9,109.8c-24.6-6.7-41.3-20.1-48.4-38.8c-3.4-9.1-9.2-34.9,11.2-78.5
			C-416.8-159.4-281.2-318,210.6-490C540.7-605.5,739-752.2,849.2-963.4c-94.8,39-190.1,109.6-299.1,190.4
			c-184.4,136.6-414,306.7-740.5,386.3C-848.6-226.3-1408.6,213-1735.8,823.1c119.8-101.2,307.5-175.1,447.5-171.5
			c70.9,1.8,125.8,23.8,158.6,63.5c41.9,50.7,15.9,97.6-5,135.2c-20.8,37.5-32.3,61.7-8.3,86.8c63.6,66.4,278.1-5.5,380.3-64.5
			c44-25.4,147.1-84.9,147.1-155.1c0-114.2-189.4-217.6-275.8-247.2c-68.7-23.6-123.3-27.4-153.5-10.8c-12.7,7-20.5,17.1-23.8,31.1
			l-5.1,21.2l-13.2-17.3c-40.8-53.2-50.6-111.9-27.6-165.2c32.4-75.2,126.7-131.6,241-144.6c25.3-31.3,72.9-54.5,128.6-62.3
			c94.5-13.2,247.7-4.1,370.8,3.3c68.5,4.1,127.7,7.7,153.3,5.5l28.8-2.4l-20.6,20.2c-17,16.6-43.3,33.9-61.9,45.3
			c49.6,13.7,107.1,33.2,167.6,53.7c102.7,34.9,208.9,70.9,298.2,86.5l25.2,4.4l-21.1,14.4c-18.6,12.7-61.2,28.1-90,37.7
			c95.4,34.3,195.6,51.7,298.2,51.7c456.3,0,629.3-224,793.5-494.7c122.1-201.3,281.7-366.7,461.7-478.4
			c177.5-110.2,374.8-168.4,570.7-168.4h36.1l-30.6,19.2c-140.8,88.3-229,220.1-331.1,372.6c-161.3,241-344.2,514.2-797.8,701.6
			c-341.5,141-589,267.7-767.5,463c-53.8,58.8-117.6,149.1-179.4,236.4C100.3,1176.7,44.2,1256,0.5,1302.7
			c178.1-61.6,356.1-159,542.7-296.7C719,876.2,871.4,734,1005.8,608.5c246.8-230.3,494.9-389.1,680.9-435.5
			c171.5-42.9,294.4-24.5,320.8,48.1c27.7,76.3-82.6,128.5-189.2,179c-44.3,21-107,38.3-173.3,56.6c-79.8,22.1-167.9,46.4-236.7,81
			c118.9-16.8,283.9,7.7,399.7,25l31.1,4.6c385.1,56.2,667.2,5.9,961.9-413.4v-472.4c-99.8,48.9-219.2,75.7-329.8,100.3
			c-76.5,17.1-148.8,33.2-208.8,55l-81.2,29.6l71.9-48c44.4-29.6,89.3-72.7,141.4-122.5c102.6-98.3,228.6-219,406.5-281.1v-693.1
			C2396.7-1178.2,1859.9-904.1,1589.4-657.6z"/>
	</g>
</g>
<g>
	<defs>
		<rect id="SVGID_13_" y="3200" width="2800" height="1400"/>
	</defs>
	<clipPath id="SVGID_4_">
		<use xlink:href="#SVGID_13_"  style="overflow:visible;"/>
	</clipPath>
	<polygon style="clip-path:url(#SVGID_4_);fill:#1D6735;" points="2362.7,469.4 2352.2,451.9 -316.6,1466.2 -298.4,1514.4 	"/>
	<polygon style="clip-path:url(#SVGID_4_);fill:#1D6735;" points="2414.8,574.8 2404.1,555.8 -309.9,1575.7 -292.2,1623.4 	"/>
	<polygon style="clip-path:url(#SVGID_4_);fill:#1D6735;" points="2467,680.3 2456,659.8 -303.3,1685.1 -286.1,1732.3 	"/>
	<polygon style="clip-path:url(#SVGID_4_);fill:#1D6735;" points="2519.2,785.7 2507.9,763.7 -296.7,1794.6 -279.9,1841.2 	"/>
	<polygon style="clip-path:url(#SVGID_4_);fill:#1D6735;" points="2571.3,891.1 2559.8,867.6 -290,1904 -273.7,1950.1 	"/>
	<polygon style="clip-path:url(#SVGID_4_);fill:#1D6735;" points="2623.5,996.6 2611.7,971.5 -283.4,2013.5 -267.5,2059.1 	"/>
	<polygon style="clip-path:url(#SVGID_4_);fill:#1D6735;" points="2675.7,1102 2663.6,1075.4 -276.8,2122.9 -261.3,2168 	"/>
	<polygon style="clip-path:url(#SVGID_4_);fill:#1D6735;" points="2727.8,1207.4 2715.5,1179.4 -270.2,2232.4 -255.1,2276.9 	"/>
	<polygon style="clip-path:url(#SVGID_4_);fill:#1D6735;" points="-263.5,2341.9 -248.9,2385.9 2448.3,1430.4 2519,1370 	"/>
	<polygon style="clip-path:url(#SVGID_4_);fill:#1D6735;" points="2832.2,1418.3 2819.3,1387.2 2683.8,1434.1 2689.9,1468.1 	"/>
	<polygon style="clip-path:url(#SVGID_4_);fill:#1D6735;" points="2669.2,1352.1 2448.3,1430.4 2280.5,1573.6 2683.8,1434.1 	"/>
	<polygon style="clip-path:url(#SVGID_4_);fill:#004417;" points="2663.4,1319.6 2519,1370 2448.3,1430.4 2669.2,1352.1 	"/>
</g>
</svg>
