import aioftp
import json
import logging
import xml.etree.ElementTree as ET
from decimal import Decimal
from core.enums import TeamMemberTypeEnum
from clubs.models import Club, ClubExtraInfo, ClubStaff, ClubPlayer
from players.models import Player, PlayerExtraInfo
from ..helpers import (
    async_delete_item,
    async_find_first,
    async_find_all,
    async_upsert,
    parse_date,
    process_path_if_needed,
    read_file_to_str,
    xml_value,
)

logger = logging.getLogger(__name__)


async def import_players(ftp_client: aioftp.Client, file_path: str, file_stat: dict):
    """Import players from DS
    The master file which carries the information of the player ID, names,
    birthday and records, etc.
    source file: master/directory-{DS_CLUB_ID}.xml
    doc file: https://docs.google.com/spreadsheets/d/1IGAUQ9qBTbq6XEJ9gJoYbKlL4NBtE2Y-/edit?rtpof=true
    """
    async with process_path_if_needed(file_path, file_stat) as should_process:
        if not should_process:
            return

        # read XML file
        file_content = await read_file_to_str(ftp_client, file_path)
        root = ET.fromstring(file_content)  # Football node
        directory_node = root.find("Directory")
        club_id = xml_value(directory_node, "TeamID", int)

        # check club exists
        club = await async_find_first(Club, ds_club_id=club_id)
        if not club:
            logger.warn("Club not found (ds_club_id=%s)", club_id)
            return

        # process players
        for player_node in directory_node.findall("PlayerInfo"):
            # get player info
            # NOTE: player.class = 1, manager = 10 (why they are mixing!!)
            player_id = xml_value(player_node, "PlayerID", int)
            player_name = xml_value(player_node, "PlayerName") or xml_value(
                player_node, "PlayerNameS"
            )
            player_info = {
                "ds_player_id": player_id,
                "name": player_name,
                "name_short": xml_value(player_node, "PlayerNameS")
                or xml_value(player_node, "PlayerName"),
                "class_value": xml_value(player_node, "Class", int),
                "is_hg_player": xml_value(player_node, "HGPlayerF", lambda x: x == "1"),
                "is_international": xml_value(
                    player_node, "InternationalF", lambda x: x == "1"
                ),
                "is_afc_member": xml_value(
                    player_node, "AFCmemberF", lambda x: x == "1"
                ),
                "is_partner_nations": xml_value(
                    player_node, "PartnerNationsF", lambda x: x == "1"
                ),
                "birth_place": xml_value(player_node, "BirthPlace"),
                "height": xml_value(player_node, "Height", Decimal),
                "weight": xml_value(player_node, "Weight", Decimal),
                "birthday": xml_value(player_node, "Birthday", parse_date),
                "game_count_j1": xml_value(player_node, "GameCountJ1", int),
                "game_count_j2": xml_value(player_node, "GameCountJ2", int),
                "game_count_j3": xml_value(player_node, "GameCountJ3", int),
                "game_count_cup": xml_value(player_node, "GameCountCup", int),
                "goal_count_j1": xml_value(player_node, "GoalCountJ1", int),
                "goal_count_j2": xml_value(player_node, "GoalCountJ2", int),
                "goal_count_j3": xml_value(player_node, "GoalCountJ3", int),
                "goal_count_cup": xml_value(player_node, "GoalCountCup", int),
                "first_game_date": xml_value(player_node, "FirstGameDate", parse_date),
                "first_goal_date": xml_value(player_node, "FirstGoalDate", parse_date),
            }

            # update player
            player, _ = await async_upsert(
                Player, ds_player_id=player_id, defaults=player_info
            )

            # update player extra info
            logging.info("player_name: %s; ds_player_id=%s", player_name, player_id)
            extra, is_created = await async_upsert(
                PlayerExtraInfo,
                player=player,
                defaults={"title": f"{player_name} [{club.name}]"},
            )

            # patch t_name and t_name_short on new record
            # if is_created is True:
            #     extra.t_name = {"en": player_info.get("name")}
            #     extra.t_name_short = {"en": player_info.get("name_short")}
            #     extra.save()

            logger.info(
                "Processed player (ds_player_id=%s, ds_club_id=%s)", player_id, club_id
            )


async def import_players_master_info(
    ftp_client: aioftp.Client, file_path: str, file_stat: dict
):
    """Import master info players from DS
    The master file which carries the information of the player ID, names,
    nationality, manager, team staff, etc.
    source file: master/mst_player-{DS_CLUB_ID}.xml
    doc file: https://docs.google.com/spreadsheets/d/1ENezy_eV0P94g8zjNw-Kr7TyeCEo8rz7/edit#gid=1552106313
    """
    async with process_path_if_needed(file_path, file_stat) as should_process:
        if not should_process:
            return

        # read XML file
        file_content = await read_file_to_str(ftp_client, file_path)
        root = ET.fromstring(file_content)  # Football node
        player_mst_node = root.find("PlayerMST")
        year = int(xml_value(player_mst_node, "SendDate")[:4])
        club_id = xml_value(player_mst_node, "TeamID", int)

        # check club exists
        club = await async_find_first(Club, ds_club_id=club_id)
        if not club:
            logger.warn("Club not found (ds_club_id=%s)", club_id)
            return

        # check club extra info exists
        club_extra_info = await async_find_first(
            ClubExtraInfo, club__ds_club_id=club_id
        )
        if not club_extra_info:
            club_extra_info, _ = await async_upsert(
                ClubExtraInfo, club=club, defaults={"title": club.name}
            )

        # process players
        for player_node in player_mst_node.findall("PlayerInfo"):
            # get player info
            player_id = xml_value(player_node, "PlayerID", int)
            player_name = xml_value(player_node, "PlayerName")
            player_name_short = xml_value(player_node, "PlayerNameS")
            position = xml_value(player_node, "Position")
            uniform_no = xml_value(player_node, "UniformNo", int)
            nationality_name = xml_value(player_node, "NationalityName")
            nationality_name_short = xml_value(player_node, "NationalityNameS")
            registerF = xml_value(
                player_node, "RegisterF", int
            )  # registered=1; not registered=0

            # create or update player
            player, _ = await async_upsert(
                Player,
                ds_player_id=player_id,
                defaults={
                    "name": player_name,
                    "name_short": player_name_short,
                    "nationality_name": nationality_name,
                    "nationality_name_short": nationality_name_short,
                },
            )

            # create or update club player
            await async_upsert(
                ClubPlayer,
                club=club,
                player=player,
                year=year,
                defaults={
                    "position": position,
                    "jersey_no": uniform_no,
                    "is_registered": True if registerF == 1 else False,
                },
            )

            # create or update player extra info
            logging.info("player_name: %s; ds_player_id=%s", player_name, player_id)
            await async_upsert(
                PlayerExtraInfo,
                player=player,
                defaults={"title": f"{player_name} [{club.name}]"},
            )
            logger.info(
                "Processed player master info (ds_player_id=%s, ds_club_id=%s)",
                player_id,
                club_id,
            )

        staffs = []
        staff_ids = []

        # process managers
        manager_nodes = player_mst_node.findall("HeadCoachInfo")
        if manager_nodes and len(manager_nodes):
            for manager_node in manager_nodes:
                staffs.append(
                    {
                        "staff_id": xml_value(manager_node, "PlayerID", int),
                        "name": xml_value(manager_node, "PlayerName"),
                        "name_short": xml_value(manager_node, "PlayerNameS"),
                        "nationality_name": xml_value(manager_node, "NationalityName"),
                        "position": TeamMemberTypeEnum.MANAGER.value,
                        "club_extra_info": club_extra_info,
                    }
                )

        # process staffs
        staff_nodes = player_mst_node.findall("TeamStaffInfo")
        if staff_nodes and len(staff_nodes):
            for staff_node in staff_nodes:
                staffs.append(
                    {
                        "staff_id": xml_value(staff_node, "PlayerID", int),
                        "name": xml_value(staff_node, "PlayerName"),
                        "name_short": xml_value(staff_node, "PlayerNameS"),
                        "nationality_name": xml_value(staff_node, "NationalityName"),
                        "position": xml_value(staff_node, "PostName"),
                        "club_extra_info": club_extra_info,
                    }
                )

        # update staff
        for staff in staffs:
            if staff.get("club_extra_info") is None:
                continue

            # collect staff_id
            staff_ids.append(staff["staff_id"])

            await async_upsert(ClubStaff, staff_id=staff["staff_id"], defaults=staff)
            logger.info(
                "Processed staff (ds_staff_id=%s, ds_club_id=%s)",
                staff["staff_id"],
                club_id,
            )

        # remove old staffs
        club_staffs = await async_find_all(ClubStaff, club_extra_info=club_extra_info)
        if club_staffs and len(club_staffs) and staff_ids and len(staff_ids):
            for club_staff in club_staffs:
                if club_staff.staff_id not in staff_ids:
                    await async_delete_item(club_staff)
            logger.info("Removed old staffs")


async def import_players_additional_info(
    ftp_client: aioftp.Client, file_path: str, file_stat: dict
):
    """Import additional info players from DS
    The master file which carries the information of the player ID, names,
    international match appearances and index, etc.
    source file: master/directory_add-{DS_CLUB_ID}.xml
    doc file: https://docs.google.com/spreadsheets/d/110xemAah8KKzWxYVBU7Azq1tKf515Wo4/edit?rtpof=true#gid=2020851459
    """
    async with process_path_if_needed(file_path, file_stat) as should_process:
        if not should_process:
            return

        # read XML file
        file_content = await read_file_to_str(ftp_client, file_path)
        root = ET.fromstring(file_content)  # Football node
        directory_node = root.find("Directory")
        club_id = xml_value(directory_node, "TeamID", int)

        # check club exists
        club = await async_find_first(Club, ds_club_id=club_id)
        if not club:
            logger.warn("Club not found (ds_club_id=%s)", club_id)
            return

        # process players
        for player_node in directory_node.findall("PlayerInfo"):
            # get player info
            player_id = xml_value(player_node, "PlayerID", int)
            national_team_appearances = xml_value(player_node, "Acap", int)
            national_team_goals = xml_value(player_node, "Agoal", int)
            dominant_foot = xml_value(player_node, "DominantFoot", str)
            twitter = xml_value(player_node, "TwitterID", str)

            # update player
            await async_upsert(
                Player,
                ds_player_id=player_id,
                defaults={
                    "national_team_appearances": national_team_appearances,
                    "national_team_goals": national_team_goals,
                    "dominant_foot": dominant_foot,
                    "twitter": twitter,
                },
            )
            logger.info(
                "Processed players additional info (ds_player_id=%s, ds_club_id=%s)",
                player_id,
                club_id,
            )
