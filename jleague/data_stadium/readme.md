## DATA STADIUM

### Import data locally:

DS Integration is working over FTP protocol and only 2 IP addresses added to the whitelist (for dev and prod servers) so if you want to import data locally you have to set up SOCKS5 proxy first with the command below:

```
ssh -ND 1234 <EMAIL>
```

_\*Port 1234 is hardcoded in `jleague/jleague/settings/local.py`._

And then run the import command with the options (required both):
`python manage.py import_ds_data <always_check_children> <import_all_live_games>`

- `always_check_children` - `yes` is used to always check all nested files so if they were changed they can be updated
- `import_all_live_games` - `yes` is used to import all live games or only the fresh ones (for quick data updating)

```
python manage.py import_ds_data yes no
```

### FTP files structure:

```
/root
|-master
|-ranking
|-real
|-test
|-total
|-{MATCH ID}
|-{ANOTHER MATCH ID}
|-...
```

`{MATCH ID}` - {YYYYMMDD}{2 DIGITS WITH ZERO PAD}

### Re-sync the data from <PERSON>, run the query below (Do it at your own risk!!!)

_Before runs the query:_

- _\*\*\*Please double check the path **EVERY TIME!!**_
- _\*\*\*Please double do the DB backup **EVERY TIME!!**_

```sql
UPDATE data_stadium_pathsynclog
SET
  last_modified_at = (last_modified_at - interval '1 hour'),
  last_synced_at = (last_modified_at - interval '2 hour')
WHERE path like '/total/player/%'
```
