import aioftp
import logging
import xml.etree.ElementTree as ET
from .helpers import (
    async_upsert,
    process_path_if_needed,
    read_file_to_str,
    xml_value,
)
from core.models import Competition
from utils.helpers.etc import get_competition_slug, get_competition_category_slug

logger = logging.getLogger(__name__)


async def import_game_kinds(ftp_client: aioftp.Client, file_path: str, file_stat: dict):
    """Import competitions from DS
    source file: master/mst_gamekind.xml
    doc file: https://docs.google.com/spreadsheets/d/1nPke-CT9DXENV-b-qSaG72qqNKoZl4Fi/edit#gid=1262301103
    """
    async with process_path_if_needed(file_path, file_stat) as should_process:
        if not should_process:
            return

        # read XML file
        file_content = await read_file_to_str(ftp_client, file_path)
        root = ET.fromstring(file_content)

        # process competitions
        for node in root.findall("GameKindMST/GameKindInfo"):
            id = xml_value(node, "GameKindID", int)
            name = xml_value(node, "GameKindName")
            name_short = xml_value(node, "GameKindNameS")

            await async_upsert(
                Competition,
                id=id,
                defaults={
                    "name": name,
                    "name_short": name_short,
                    "slug": get_competition_slug(id, name_short),
                    "category_slug": get_competition_category_slug(id, name_short),
                },
            )
        logger.info("Processed competitions")
