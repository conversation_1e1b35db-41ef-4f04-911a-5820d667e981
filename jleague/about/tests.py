from django.test import TestCase
from .helper import get_broadcast_areas

# Create your tests here.


class AboutTestCase(TestCase):
    def test_get_broadcast_areas(self):
        """BROADCAST AREA"""

        broadcast_map, broadcast_links, broadcast_details = get_broadcast_areas()

        self.assertEqual(len(broadcast_details), 18, "The total of Region is not to 18")
        # self.assertTrue('China' in broadcast_details[0]['countries'], '')
