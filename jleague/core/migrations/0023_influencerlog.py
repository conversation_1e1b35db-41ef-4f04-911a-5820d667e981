# Generated by Django 4.1.4 on 2023-05-10 03:17

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('core', '0022_rename_local_names_competition_t_name_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='InfluencerLog',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('influencer', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('sns_link', models.Char<PERSON><PERSON>(max_length=30)),
                ('country', models.Char<PERSON>ield(max_length=255)),
                ('timezone', models.Char<PERSON>ield(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
