import aioftp
import json
import logging
import xml.etree.ElementTree as ET
from decimal import Decimal
from clubs.models import Club, ClubExtraInfo, ClubCompetition
from core.models import Competition
from utils.helpers.etc import get_team_slug
from ..helpers import (
    async_find_first,
    async_upsert,
    process_path_if_needed,
    read_file_to_str,
    xml_value,
)


logger = logging.getLogger(__name__)


async def import_clubs(ftp_client: aioftp.Client, file_path: str, file_stat: dict):
    """Import clubs from DS
    The file which outputs list of clubs with detail by competition.
    source file: /master/team_directory-{COMPETITION_ID}.xml
    """
    async with process_path_if_needed(file_path, file_stat) as should_process:
        if not should_process:
            return

        # read XML file
        file_content = await read_file_to_str(ftp_client, file_path)
        root = ET.fromstring(file_content)  # Football node

        for team_node in root.findall("TeamDirectory/TeamInfo"):
            club_id = xml_value(team_node, "TeamID", int)
            name = xml_value(team_node, "TeamName")
            name_short = xml_value(team_node, "TeamNameS")
            federation_id = xml_value(team_node, "FederationID", int)
            home_stadium_id = xml_value(team_node, "HomeStadiumID", int)
            founded = xml_value(team_node, "Founded", int)
            website_url = xml_value(team_node, "OfficialWebsite")
            twitter_url = xml_value(team_node, "OfficialTwitter")
            facebook_url = xml_value(team_node, "OfficialFacebook")
            instagram_url = xml_value(team_node, "OfficialInstagram")
            j1_license = xml_value(team_node, "J1Licence", lambda x: x == "1")
            j2_license = xml_value(team_node, "J2Licence", lambda x: x == "1")
            j3_license = xml_value(team_node, "J3Licence", lambda x: x == "1")

            average_age = xml_value(team_node, "AverageAge", Decimal)
            average_height = xml_value(team_node, "AverageHeight", Decimal)
            average_weight = xml_value(team_node, "AverageWeight", Decimal)

            hometowner_count = xml_value(team_node, "HometownerC", int)
            hometowner_rate = xml_value(team_node, "HometownerRate", Decimal)
            domestic_league_count = xml_value(team_node, "DomesticLeagueC", int)
            domestic_league_rate = xml_value(team_node, "DomesticLeagueRate", Decimal)

            # create or update club
            club, _ = await async_upsert(
                Club,
                ds_club_id=club_id,
                defaults={
                    "name": name,
                    "name_short": name_short,
                    "slug": get_team_slug(club_id, name_short),
                    "federation_id": federation_id,
                    "home_stadium_id": home_stadium_id,
                    "founded": founded,
                    "website_url": website_url,
                    "twitter_url": twitter_url,
                    "facebook_url": facebook_url,
                    "instagram_url": instagram_url,
                    "j1_license": j1_license,
                    "j2_license": j2_license,
                    "j3_license": j3_license,
                    "average_age": average_age,
                    "average_height": average_height,
                    "average_weight": average_weight,
                    "hometowner_count": hometowner_count,
                    "hometowner_rate": hometowner_rate,
                    "domestic_league_count": domestic_league_count,
                    "domestic_league_rate": domestic_league_rate,
                },
            )

            # create or update club extra info
            extra, is_created = await async_upsert(
                ClubExtraInfo,
                club=club,
                defaults={"title": name},
            )
            # patch t_name and t_name_short on new record
            # if is_created is True:
            #     extra.t_name = {"en": name}
            #     extra.t_name_short = {"en": name_short}
            #     extra.save()

            logger.info("Processed club (ds_club_id=%s)", club_id)


async def import_club_competitions(
    ftp_client: aioftp.Client, file_path: str, file_stat: dict
):
    """Import club competitions and order from DS
    The file which outputs list of clubs in competition by order.
    source file: /master/mst_team-{COMPETITION_ID}.xml
    """
    async with process_path_if_needed(file_path, file_stat) as should_process:
        if not should_process:
            return

        # read XML file
        file_content = await read_file_to_str(ftp_client, file_path)
        root = ET.fromstring(file_content)  # Football node
        year = xml_value(root, "TeamMST/Year", int)

        for category_node in root.findall("TeamMST/Category"):
            competition_id = xml_value(category_node, "GameKindID", int)
            season_id = xml_value(category_node, "SeasonID", int)

            # check competition
            competition = await async_find_first(Competition, id=competition_id)
            if not competition:
                logger.warn("Competition not found (id=%s).", competition_id)
                return

            for team_node in category_node.findall("TeamInfo"):
                club_id = xml_value(team_node, "TeamID", int)
                group_id = xml_value(team_node, "GroupID")
                country_id = xml_value(team_node, "CountryID", int)
                ordering = xml_value(team_node, "Order", int)
                country_name = xml_value(team_node, "CountryName")
                prefecture = xml_value(team_node, "Prefecture")
                name = xml_value(team_node, "TeamName")
                name_short = xml_value(team_node, "TeamNameS")

                # update club
                club, _ = await async_upsert(
                    Club,
                    ds_club_id=club_id,
                    defaults={
                        "name": name,
                        "name_short": name_short,
                        "slug": get_team_slug(club_id, name_short),
                        "country_id": country_id,
                        "country_name": country_name,
                        "prefecture": prefecture,
                    },
                )

                # update club competition
                await async_upsert(
                    ClubCompetition,
                    year=year,
                    club=club,
                    competition_id=competition_id,
                    season_id=season_id,
                    defaults={
                        "group_id": group_id,
                        "ordering": ordering or 0,
                    },
                )
                logger.info("Processed club competitions (ds_club_id=%s)", club_id)
