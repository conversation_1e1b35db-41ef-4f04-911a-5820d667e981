# Generated by Django 3.1.7 on 2021-04-27 18:29

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('home', '0013_auto_20210407_0051'),
    ]

    operations = [
        migrations.CreateModel(
            name='VideoItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('video_url', models.URLField(verbose_name='URL')),
                ('thumbnail_url', models.URLField()),
                ('embed_url', models.URLField()),
                ('html', models.TextField()),
                ('author_name', models.Char<PERSON>ield(max_length=255, null=True)),
                ('provider_name', models.Char<PERSON>ield(max_length=255)),
                ('date', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTime<PERSON>ield(auto_now=True)),
            ],
        ),
    ]
