# Generated by Django 3.1.7 on 2021-04-24 20:25

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ('data_stadium', '0005_auto_20210424_0830'),
    ]

    operations = [
        migrations.CreateModel(
            name='TeamRanking',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField()),
                ('season_id', models.IntegerField()),
                ('group_id', models.CharField(max_length=255, null=True)),
                ('replace', models.IntegerField()),
                ('ranking', models.IntegerField()),
                ('points', models.IntegerField()),
                ('games_count', models.IntegerField()),
                ('wins_count', models.IntegerField()),
                ('draws_count', models.IntegerField()),
                ('losses_count', models.IntegerField()),
                ('scored_goals', models.IntegerField()),
                ('missed_goals', models.IntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                (
                    'game_kind',
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_stadium.gamekind'),
                ),
                ('team', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_stadium.team')),
            ],
        ),
        migrations.AddIndex(
            model_name='teamranking',
            index=models.Index(fields=['year', 'game_kind_id', 'team_id'], name='data_stadiu_year_2f019f_idx'),
        ),
        migrations.AddConstraint(
            model_name='teamranking',
            constraint=models.UniqueConstraint(fields=('year', 'game_kind_id', 'team_id'), name='team_ranking_uniq'),
        ),
    ]
