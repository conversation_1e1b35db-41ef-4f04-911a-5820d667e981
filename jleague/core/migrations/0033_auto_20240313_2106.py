# Generated by Django 4.1.4 on 2024-03-13 12:06

from django.db import migrations

dataset = [
    {
        "title": "2021 - AFC Champions League",
        "competition_id": 46,
        "year": 2021,
        "stages": [
            {"stage": "group", "season_id": 4, "games_count": 120, "stages_count": 6},
            {"stage": "round-16", "season_id": 5, "games_count": 8, "stages_count": 1},
            {
                "stage": "quarter-finals",
                "season_id": 5,
                "games_count": 4,
                "stages_count": 1,
            },
            {
                "stage": "semi-finals",
                "season_id": 5,
                "games_count": 2,
                "stages_count": 1,
            },
            {"stage": "final", "season_id": 5, "games_count": 1, "stages_count": 1},
        ],
    },
    {
        "title": "2022 - AFC Champions League",
        "competition_id": 46,
        "year": 2022,
        "stages": [
            {"stage": "group", "season_id": 4, "games_count": 114, "stages_count": 6},
            {"stage": "round-16", "season_id": 5, "games_count": 8, "stages_count": 1},
            {
                "stage": "quarter-finals",
                "season_id": 5,
                "games_count": 4,
                "stages_count": 1,
            },
            {
                "stage": "semi-finals",
                "season_id": 5,
                "games_count": 2,
                "stages_count": 1,
            },
            {"stage": "final", "season_id": 5, "games_count": 2, "stages_count": 2},
        ],
    },
    {
        "title": "2023 - AFC Champions League",
        "competition_id": 46,
        "year": 2023,
        "stages": [
            {"stage": "group", "season_id": 4, "games_count": 120, "stages_count": 6},
            {"stage": "round-16", "season_id": 5, "games_count": 16, "stages_count": 2},
            {
                "stage": "quarter-finals",
                "season_id": 5,
                "games_count": 8,
                "stages_count": 2,
            },
            {
                "stage": "semi-finals",
                "season_id": 5,
                "games_count": 4,
                "stages_count": 2,
            },
            {"stage": "final", "season_id": 5, "games_count": 2, "stages_count": 2},
        ],
    },
    {
        "title": "2021 - J.LEAGUE YBC Levain CUP",
        "competition_id": 4,
        "year": 2021,
        "stages": [
            {"stage": "group", "season_id": 4, "games_count": 48, "stages_count": 6},
            {"stage": "playoff", "season_id": 13, "games_count": 8, "stages_count": 2},
            {
                "stage": "quarter-finals",
                "season_id": 5,
                "games_count": 8,
                "stages_count": 2,
            },
            {
                "stage": "semi-finals",
                "season_id": 5,
                "games_count": 4,
                "stages_count": 2,
            },
            {"stage": "final", "season_id": 5, "games_count": 1, "stages_count": 1},
        ],
    },
    {
        "title": "2022 - J.LEAGUE YBC Levain CUP",
        "competition_id": 4,
        "year": 2022,
        "stages": [
            {"stage": "group", "season_id": 4, "games_count": 48, "stages_count": 6},
            {"stage": "playoff", "season_id": 13, "games_count": 8, "stages_count": 2},
            {
                "stage": "quarter-finals",
                "season_id": 5,
                "games_count": 8,
                "stages_count": 2,
            },
            {
                "stage": "semi-finals",
                "season_id": 5,
                "games_count": 4,
                "stages_count": 2,
            },
            {"stage": "final", "season_id": 5, "games_count": 1, "stages_count": 1},
        ],
    },
    {
        "title": "2023 - J.LEAGUE YBC Levain CUP",
        "competition_id": 4,
        "year": 2023,
        "stages": [
            {"stage": "group", "season_id": 4, "games_count": 60, "stages_count": 6},
            {
                "stage": "quarter-finals",
                "season_id": 5,
                "games_count": 8,
                "stages_count": 2,
            },
            {
                "stage": "semi-finals",
                "season_id": 5,
                "games_count": 4,
                "stages_count": 2,
            },
            {"stage": "final", "season_id": 5, "games_count": 1, "stages_count": 1},
        ],
    },
    {
        "title": "2024 - J.LEAGUE YBC Levain CUP",
        "competition_id": 4,
        "year": 2024,
        "stages": [
            {
                "stage": "first-round",
                "season_id": 15,
                "games_count": 47,
                "stages_count": 3,
            },
            {"stage": "playoff", "season_id": 13, "games_count": 10, "stages_count": 2},
            {
                "stage": "quarter-finals",
                "season_id": 5,
                "games_count": 8,
                "stages_count": 2,
            },
            {
                "stage": "semi-finals",
                "season_id": 5,
                "games_count": 4,
                "stages_count": 2,
            },
            {"stage": "final", "season_id": 5, "games_count": 1, "stages_count": 1},
        ],
    },
]


def insert_competition_systems(apps, schema_editor):
    CompetitionSystem = apps.get_model("core", "CompetitionSystem")

    for data in dataset:
        CompetitionSystem.objects.create(
            title=data["title"],
            competition_id=data["competition_id"],
            year=data["year"],
            stages=data["stages"],
        )


class Migration(migrations.Migration):
    dependencies = [
        ("core", "0032_competitionsystem"),
    ]

    operations = [
        migrations.RunPython(insert_competition_systems),
    ]
