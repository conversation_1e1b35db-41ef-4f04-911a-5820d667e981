import os
import tempfile
import zipfile
import csv
from enum import IntEnum
from django import forms
from django.contrib import messages
from django.core.files.images import ImageFile
from django.shortcuts import redirect, render
from django.conf import settings
from pathlib import Path
from wagtail.images.models import Image
from .models import ClubStaff, ClubExtraInfo


class PhotoTypeEnum(IntEnum):
    AVATAR = 1
    DIECUT = 2


class UploadFileForm(forms.Form):
    file = forms.FileField(label='ZIP Archive')


def _get_redirect_url(photo_type: PhotoTypeEnum):
    if photo_type == PhotoTypeEnum.AVATAR:
        redirect_view = 'club_staff_avatar_upload'
    elif photo_type == PhotoTypeEnum.DIECUT:
        redirect_view = 'club_staff_diecut_upload'
    else:
        redirect_view = 'wagtailimages:index'
    return redirect_view


def _get_zipfile(request, photo_type: PhotoTypeEnum):
    form = UploadFileForm(request.POST, request.FILES)

    # check the form
    if not form.is_valid():
        messages.error(request, 'Could not process the form')
        return redirect(_get_redirect_url(photo_type))

    # get uploaded staff photos zip file
    zip_archive_file = request.FILES.get('file')
    # check is uploaded file a zip file
    if not zipfile.is_zipfile(zip_archive_file):
        messages.error(request, 'Unsupported archive format, should be ZIP')
        return redirect(_get_redirect_url(photo_type))

    # zip file
    return zipfile.ZipFile(zip_archive_file)


def _get_staffs_list(photos_zip):
    staff_ids = []
    photo_paths = {}
    skipped_photos = []

    # list of photo files in zip file
    for file_name in photos_zip.namelist():
        if file_name.endswith('.jpg') or file_name.endswith('.png'):
            staff_id = Path(file_name).stem
            if staff_id.isdigit():
                staff_ids.append(int(staff_id))
                photo_paths[staff_id] = file_name
            else:
                skipped_photos.append(str(staff_id))

    # get staffs from db by ids
    db_staffs = ClubStaff.objects.filter(staff_id__in=staff_ids)

    return {
        'staff_ids': staff_ids,
        'photo_paths': photo_paths,
        'skipped_photos': skipped_photos,
        'db_staffs': db_staffs,
    }


def _patch_staff_photo(staff, user_id, photo_path, photo_type):
    # read photo file
    photo_file = open(photo_path, 'rb')
    photo_file.seek(0, os.SEEK_END)

    # delete an exist staff image
    if photo_type == PhotoTypeEnum.AVATAR and staff.avatar_image_id:
        Image.objects.filter(id=staff.avatar_image_id).delete()
    elif photo_type == PhotoTypeEnum.DIECUT and staff.diecut_image_id:
        Image.objects.filter(id=staff.diecut_image_id).delete()

    # set staff image data
    photo_type_label = ''
    if photo_type == PhotoTypeEnum.AVATAR:
        photo_type_label = 'avatar'
    elif photo_type == PhotoTypeEnum.DIECUT:
        photo_type_label = 'diecut'

    photo_name = f'{staff.staff_id}_{staff.name}_{photo_type_label}_staff'
    image_data = {
        'title': photo_name,
        'file': ImageFile(photo_file, photo_name),
        'file_size': photo_file.tell(),
        'uploaded_by_user_id': user_id,
    }

    # set face area of avatar image (required when cropping image)
    if photo_type == PhotoTypeEnum.AVATAR:
        image_data['focal_point_x'] = 150
        image_data['focal_point_y'] = 150
        image_data['focal_point_height'] = 300
        image_data['focal_point_width'] = 300

    # create and upload a new staff image
    staff_image = Image.objects.create(**image_data)

    # close the read photo file
    photo_file.close()

    # update staff avatar or diecut image of club staff
    if staff_image:
        if photo_type == PhotoTypeEnum.AVATAR:
            staff.avatar_image = staff_image
        elif photo_type == PhotoTypeEnum.DIECUT:
            staff.diecut_image = staff_image
        staff.save()


def _pre_patch_staff_photos(photos_zip, photo_type, photo_paths, db_staffs, user_id):
    not_exist_staff_ids = []
    done_staff_ids = []

    tmp_dir = tempfile.TemporaryDirectory()
    tmp_dir_path = Path(tmp_dir.name)

    for staff in db_staffs:
        # extract photo from zip file
        photo_src = photo_paths.get(str(staff.staff_id))

        if photo_src:
            photos_zip.extract(photo_src, path=tmp_dir_path)
            photo_path = tmp_dir_path / photo_src

            # patch staff photo
            _patch_staff_photo(
                staff=staff,
                user_id=user_id,
                photo_path=photo_path,
                photo_type=photo_type,
            )

            # collect duplicate staffs
            done_staff_ids.append(str(staff.staff_id))
        else:
            # collect non-exist staffs
            not_exist_staff_ids.append(str(staff.staff_id))

    tmp_dir.cleanup()

    # display messages to user
    if len(not_exist_staff_ids):
        print(f'{len(not_exist_staff_ids)} Staff ids ({", ".join(not_exist_staff_ids)}) did not exist.')
    print(f'Updated {len(done_staff_ids)} staff photos done!!!')


def _upload_files(request, photo_type: PhotoTypeEnum):
    # get zip file
    photos_zip = _get_zipfile(request, photo_type)
    staffs_list = _get_staffs_list(photos_zip)
    staff_ids = staffs_list['staff_ids']
    photo_paths = staffs_list['photo_paths']
    skipped_photos = staffs_list['skipped_photos']
    db_staffs = staffs_list['db_staffs']

    if staff_ids and len(staff_ids):
        messages.info(request, 'Uploading is running in background, please come back and check later.')
        # TODO: run in background
        print('Start updating the staff photos...')

        # display message to user
        if skipped_photos and len(skipped_photos):
            print(f'{len(skipped_photos)} Staff ids ({", ".join(skipped_photos)}) were skipped.')

        _pre_patch_staff_photos(
            photos_zip=photos_zip,
            photo_type=photo_type,
            photo_paths=photo_paths,
            db_staffs=db_staffs,
            user_id=request.user.id,
        )
    else:
        print('Cannot find any JPG or JPEG files or Photo file name is not a staff id. Please check you zip archive.')
        messages.error(
            request,
            'Cannot find any JPG or JPEG files or Photo file name is not a staff id. Please check you zip archive.',
        )

    return redirect(_get_redirect_url(photo_type))


def club_staff_avatar(request):
    form = UploadFileForm()
    if request.method == 'POST':
        return _upload_files(request, PhotoTypeEnum.AVATAR)

    context = {'form': form}
    context['recent_images'] = Image.objects.filter(title__icontains='_avatar_staff').order_by('-created_at')[:10]

    return render(request, 'clubs/admin_views/club_staff_avatar.html', context)


def club_staff_diecut(request):
    form = UploadFileForm()
    if request.method == 'POST':
        return _upload_files(request, PhotoTypeEnum.DIECUT)

    context = {'form': form}
    context['recent_images'] = Image.objects.filter(title__icontains='_diecut_staff').order_by('-created_at')[:10]

    return render(request, 'clubs/admin_views/club_staff_diecut.html', context)
