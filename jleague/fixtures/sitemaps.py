from django.contrib.sitemaps import Sitemap
from django.urls import reverse
from core.constants import (
    ALLOW_COMPETITIONS,
    LEAGUE_COMPETITIONS,
    CUP_COMPETITIONS,
)
from utils.helpers import years_list, months_list
from clubs.helper import get_competition_clubs
from fixtures.helper import get_fixture_stage_selector_choices


class FixturesViewSitemap(Sitemap):
    changefreq = "weekly"
    priority = 0.6
    protocol = "https"

    def items(self):
        return ["fixtures"]

    def location(self, item):
        return reverse(item)


class FixtureSearchViewSitemap(Sitemap):
    changefreq = "weekly"
    priority = 0.7
    protocol = "https"

    def items(self):
        paths = []
        competitions = ALLOW_COMPETITIONS + ["all"]
        months = months_list()
        years = years_list()

        for competition in competitions:
            stages = get_fixture_stage_selector_choices(competition)
            teams = get_competition_clubs(competition)

            for year in years:
                # <competition_slug>/<year>/<month>/; name=fixtures_search
                # <competition_slug>/<year>/<month>/<team_slug>/; name=fixtures_search_team
                for month in months:
                    paths.append(
                        {"name": "fixtures_search", "args": [competition, year, month]}
                    )

                    for team in teams:
                        paths.append(
                            {
                                "name": "fixtures_search_team",
                                "args": [competition, year, month, team.slug],
                            }
                        )

                # stage/<competition_slug>/<year>/<stage>/<stage_round>/; name=fixtures_stage_cup
                # stage/<competition_slug>/<year>/<stage>/<stage_round>/<team_slug>/; name=fixtures_stage_cup_team
                # stage/<competition_slug>/<year>/<occasion_no>/; name=fixtures_stage_tournament
                # stage/<competition_slug>/<year>/<occasion_no>/<team_slug>/; name=fixtures_stage_tournament_team
                for stage in stages:
                    stage_info = []

                    if competition in LEAGUE_COMPETITIONS:
                        paths.append(
                            {
                                "name": "fixtures_stage_tournament",
                                "args": [competition, year, stage["value"]],
                            }
                        )

                        for team in teams:
                            paths.append(
                                {
                                    "name": "fixtures_stage_tournament_team",
                                    "args": [
                                        competition,
                                        year,
                                        stage["value"],
                                        team.slug,
                                    ],
                                }
                            )

                    elif competition in CUP_COMPETITIONS:
                        stage_info = stage["value"].split("|")
                        paths.append(
                            {
                                "name": "fixtures_stage_cup",
                                "args": [
                                    competition,
                                    year,
                                    stage_info[0],
                                    stage_info[1],
                                ],
                            }
                        )

                        for team in teams:
                            paths.append(
                                {
                                    "name": "fixtures_stage_cup_team",
                                    "args": [
                                        competition,
                                        year,
                                        stage_info[0],
                                        stage_info[1],
                                        team.slug,
                                    ],
                                }
                            )

        return paths

    def location(self, item):
        return reverse(item["name"], args=item["args"])


sitemaps = {
    "fixtures": FixturesViewSitemap,
    "fixture_search": FixtureSearchViewSitemap,
}
