<?xml version="1.0" encoding="UTF-8"?>
<svg width="30" height="35"  id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 58 80">
  <defs>
    <style>
      .cls-1 {
        fill: url(#radial-gradient-6);
        opacity: .05;
      }

      .cls-2 {
        fill: url(#radial-gradient-2);
      }

      .cls-2, .cls-3 {
        opacity: .2;
      }

      .cls-3 {
        fill: url(#radial-gradient-3);
      }

      .cls-4 {
        fill: #150335;
      }

      .cls-5 {
        fill: url(#radial-gradient-5);
      }

      .cls-6 {
        fill: url(#radial-gradient-4);
      }

      .cls-7 {
        fill: url(#linear-gradient-8);
      }

      .cls-8 {
        fill: url(#linear-gradient-7);
      }

      .cls-9 {
        fill: url(#linear-gradient-5);
      }

      .cls-10 {
        fill: url(#linear-gradient-6);
      }

      .cls-11 {
        fill: url(#linear-gradient-9);
      }

      .cls-12 {
        fill: url(#linear-gradient-4);
      }

      .cls-13 {
        fill: url(#linear-gradient-3);
      }

      .cls-14 {
        fill: url(#linear-gradient-2);
      }

      .cls-15 {
        fill: url(#linear-gradient);
      }

      .cls-16 {
        fill: url(#radial-gradient);
      }

      .cls-16, .cls-17 {
        opacity: .6;
      }

      .cls-18 {
        fill: #ae9770;
      }

      .cls-19 {
        fill: url(#linear-gradient-39);
        opacity: .8;
      }

      .cls-17 {
        fill: url(#linear-gradient-11);
      }

      .cls-20 {
        fill: url(#linear-gradient-38);
        opacity: .4;
      }

      .cls-21 {
        fill: url(#linear-gradient-43);
      }

      .cls-22 {
        fill: url(#linear-gradient-40);
      }

      .cls-23 {
        fill: url(#linear-gradient-41);
      }

      .cls-24 {
        fill: url(#linear-gradient-42);
      }

      .cls-25 {
        fill: url(#linear-gradient-45);
      }

      .cls-26 {
        fill: url(#linear-gradient-44);
      }

      .cls-27 {
        fill: url(#linear-gradient-46);
      }

      .cls-28 {
        fill: url(#linear-gradient-10);
      }

      .cls-29 {
        fill: url(#linear-gradient-12);
      }

      .cls-30 {
        fill: url(#linear-gradient-13);
      }

      .cls-31 {
        fill: url(#linear-gradient-19);
      }

      .cls-32 {
        fill: url(#linear-gradient-14);
      }

      .cls-33 {
        fill: url(#linear-gradient-21);
      }

      .cls-34 {
        fill: url(#linear-gradient-20);
      }

      .cls-35 {
        fill: url(#linear-gradient-22);
      }

      .cls-36 {
        fill: url(#linear-gradient-23);
      }

      .cls-37 {
        fill: url(#linear-gradient-28);
      }

      .cls-38 {
        fill: url(#linear-gradient-25);
      }

      .cls-39 {
        fill: url(#linear-gradient-32);
      }

      .cls-40 {
        fill: url(#linear-gradient-31);
      }

      .cls-41 {
        fill: url(#linear-gradient-33);
      }

      .cls-42 {
        fill: url(#linear-gradient-30);
      }

      .cls-43 {
        fill: url(#linear-gradient-15);
      }

      .cls-44 {
        fill: url(#linear-gradient-16);
      }

      .cls-45 {
        fill: url(#linear-gradient-17);
      }

      .cls-46 {
        fill: url(#linear-gradient-18);
      }

      .cls-47 {
        fill: url(#linear-gradient-35);
      }

      .cls-48 {
        fill: url(#linear-gradient-34);
      }

      .cls-49 {
        fill: url(#linear-gradient-36);
      }

      .cls-50 {
        fill: url(#linear-gradient-27);
      }

      .cls-51 {
        fill: url(#linear-gradient-24);
      }

      .cls-52 {
        fill: url(#linear-gradient-26);
      }

      .cls-53 {
        fill: url(#linear-gradient-29);
      }

      .cls-54 {
        fill: url(#linear-gradient-37);
      }
    </style>
    <linearGradient id="linear-gradient" x1="35.07" y1="-3.68" x2="35.07" y2="53.44" gradientUnits="userSpaceOnUse">
      <stop offset=".28" stop-color="#5e4c8f"/>
      <stop offset=".29" stop-color="#645395"/>
      <stop offset=".33" stop-color="#7869a9"/>
      <stop offset=".37" stop-color="#8477b5"/>
      <stop offset=".4" stop-color="#897cba"/>
      <stop offset=".48" stop-color="#9b92c6"/>
      <stop offset=".57" stop-color="#897cba"/>
      <stop offset=".59" stop-color="#8376b4"/>
      <stop offset=".67" stop-color="#5e4c8f"/>
      <stop offset=".89" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="5815.84" y1="3.63" x2="5823.51" y2="16.87" gradientTransform="translate(5867.16) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".06" stop-color="#cfd2d2"/>
      <stop offset=".28" stop-color="#897cba"/>
      <stop offset="1" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="5817.94" y1="1.15" x2="5825.4" y2="23.19" gradientTransform="translate(5867.16) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".08" stop-color="#cfd2d2"/>
      <stop offset=".28" stop-color="#5e4c8f"/>
      <stop offset=".38" stop-color="#5e4c8f"/>
      <stop offset=".55" stop-color="#897cba"/>
      <stop offset=".61" stop-color="#6e5d9f"/>
      <stop offset=".65" stop-color="#5e4c8f"/>
      <stop offset="1" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="5822.12" y1="-.69" x2="5824.93" y2="25.58" gradientTransform="translate(5867.16) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".05" stop-color="#cfd2d2"/>
      <stop offset=".28" stop-color="#5e4c8f"/>
      <stop offset=".38" stop-color="#5e4c8f"/>
      <stop offset=".55" stop-color="#897cba"/>
      <stop offset=".61" stop-color="#6e5d9f"/>
      <stop offset=".65" stop-color="#5e4c8f"/>
      <stop offset="1" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="18.81" y1="3.63" x2="26.49" y2="16.87" gradientTransform="matrix(1,0,0,1,0,0)" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-6" x1="20.92" y1="1.15" x2="28.38" y2="23.19" gradientTransform="matrix(1,0,0,1,0,0)" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-7" x1="25.1" y1="-.69" x2="27.91" y2="25.58" gradientTransform="matrix(1,0,0,1,0,0)" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-8" x1="35.07" y1="-2.88" x2="35.07" y2="37.56" gradientUnits="userSpaceOnUse">
      <stop offset=".1" stop-color="#cfd2d2"/>
      <stop offset=".22" stop-color="#5e4c8f"/>
      <stop offset=".32" stop-color="#5e4c8f"/>
      <stop offset=".42" stop-color="#897cb9"/>
      <stop offset=".46" stop-color="#9789c0"/>
      <stop offset=".51" stop-color="#a99aca"/>
      <stop offset=".54" stop-color="#9a8cc2"/>
      <stop offset=".59" stop-color="#897cb9"/>
      <stop offset=".78" stop-color="#100133"/>
    </linearGradient>
    <radialGradient id="radial-gradient" cx="-285.89" cy="-144.22" fx="-285.89" fy="-144.22" r="49.67" gradientTransform="translate(-155.91 297.62) rotate(90) scale(.93 1.32)" gradientUnits="userSpaceOnUse">
      <stop offset=".46" stop-color="#ae9770" stop-opacity="0"/>
      <stop offset=".49" stop-color="#c1ab83" stop-opacity=".24"/>
      <stop offset=".57" stop-color="#fdedc0"/>
      <stop offset=".85" stop-color="#fdedc0"/>
      <stop offset=".91" stop-color="#dcc99f" stop-opacity=".59"/>
      <stop offset=".97" stop-color="#bba57d" stop-opacity=".17"/>
      <stop offset="1" stop-color="#ae9770" stop-opacity="0"/>
    </radialGradient>
    <radialGradient id="radial-gradient-2" cx="35.17" cy="32.75" fx="35.17" fy="32.75" r="15.08" gradientUnits="userSpaceOnUse">
      <stop offset=".32" stop-color="#fff" stop-opacity="0"/>
      <stop offset=".35" stop-color="#fff" stop-opacity=".25"/>
      <stop offset=".47" stop-color="#fff"/>
      <stop offset=".51" stop-color="#fff" stop-opacity=".58"/>
      <stop offset=".56" stop-color="#fff" stop-opacity=".17"/>
      <stop offset=".58" stop-color="#fff" stop-opacity="0"/>
    </radialGradient>
    <linearGradient id="linear-gradient-9" x1="35.07" y1="32.1" x2="35.07" y2="58.03" gradientUnits="userSpaceOnUse">
      <stop offset=".08" stop-color="#a99aca" stop-opacity=".5"/>
      <stop offset=".34" stop-color="#5e4c8f" stop-opacity="0"/>
    </linearGradient>
    <radialGradient id="radial-gradient-3" cx="35.17" cy="29" fx="35.17" fy="29" r="19.83" gradientUnits="userSpaceOnUse">
      <stop offset=".37" stop-color="#fff" stop-opacity="0"/>
      <stop offset=".4" stop-color="#fff" stop-opacity=".25"/>
      <stop offset=".48" stop-color="#fff"/>
      <stop offset=".53" stop-color="#fef7e4" stop-opacity=".58"/>
      <stop offset=".57" stop-color="#fdf0ca" stop-opacity=".17"/>
      <stop offset=".59" stop-color="#fdedc0" stop-opacity="0"/>
    </radialGradient>
    <linearGradient id="linear-gradient-10" x1="16.81" y1="3.94" x2="53.33" y2="3.94" gradientUnits="userSpaceOnUse">
      <stop offset=".05" stop-color="#a99aca"/>
      <stop offset=".07" stop-color="#a495c5"/>
      <stop offset=".09" stop-color="#9687b8"/>
      <stop offset=".11" stop-color="#8070a3"/>
      <stop offset=".13" stop-color="#615084"/>
      <stop offset=".15" stop-color="#39285e"/>
      <stop offset=".16" stop-color="#301e55"/>
      <stop offset=".28" stop-color="#5f4f8a"/>
      <stop offset=".4" stop-color="#897cb9"/>
      <stop offset=".41" stop-color="#8c81bc"/>
      <stop offset=".5" stop-color="#a9b0d6"/>
      <stop offset=".51" stop-color="#a5aad2"/>
      <stop offset=".6" stop-color="#897cb9"/>
      <stop offset=".72" stop-color="#594a83"/>
      <stop offset=".84" stop-color="#301e55"/>
      <stop offset=".85" stop-color="#39285e"/>
      <stop offset=".87" stop-color="#615084"/>
      <stop offset=".89" stop-color="#8070a3"/>
      <stop offset=".91" stop-color="#9687b8"/>
      <stop offset=".93" stop-color="#a495c5"/>
      <stop offset=".95" stop-color="#a99aca"/>
    </linearGradient>
    <linearGradient id="linear-gradient-11" x1="16.81" y1="3.94" x2="53.33" y2="3.94" gradientUnits="userSpaceOnUse">
      <stop offset=".18" stop-color="#ae9770" stop-opacity="0"/>
      <stop offset=".43" stop-color="#d5ba82"/>
      <stop offset=".57" stop-color="#d5b981"/>
      <stop offset=".82" stop-color="#ae9770" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-12" x1="16.88" y1="4.82" x2="53.25" y2="4.82" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#34215b"/>
      <stop offset=".17" stop-color="#5e4c8f"/>
      <stop offset=".5" stop-color="#7769ae"/>
      <stop offset=".83" stop-color="#5e4c8f"/>
      <stop offset="1" stop-color="#34215b"/>
    </linearGradient>
    <radialGradient id="radial-gradient-4" cx="790.81" cy="-180.83" fx="790.81" fy="-180.83" r="75.92" gradientTransform="translate(13.28 -765.08) rotate(75.74) scale(.93 -1.03)" gradientUnits="userSpaceOnUse">
      <stop offset=".12" stop-color="#cfd2d2"/>
      <stop offset=".16" stop-color="#c0c0cc"/>
      <stop offset=".29" stop-color="#988fbe"/>
      <stop offset=".35" stop-color="#897cb9"/>
      <stop offset=".41" stop-color="#71639e"/>
      <stop offset=".51" stop-color="#504078"/>
      <stop offset=".61" stop-color="#36255b"/>
      <stop offset=".71" stop-color="#231245"/>
      <stop offset=".81" stop-color="#180639"/>
      <stop offset=".9" stop-color="#150335"/>
    </radialGradient>
    <radialGradient id="radial-gradient-5" cx="4575.66" cy="-180.83" fx="4575.66" fy="-180.83" r="75.92" gradientTransform="translate(927.35 -4190.61) rotate(104.26) scale(.93 1.03)" xlink:href="#radial-gradient-4"/>
    <linearGradient id="linear-gradient-13" x1="25.48" y1="19.25" x2="29.54" y2="12.29" gradientUnits="userSpaceOnUse">
      <stop offset=".6" stop-color="#cfd2d2" stop-opacity="0"/>
      <stop offset=".68" stop-color="#948caf" stop-opacity=".52"/>
      <stop offset=".75" stop-color="#6d5d97" stop-opacity=".87"/>
      <stop offset=".78" stop-color="#5e4c8f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-14" x1="25.08" y1="48.28" x2="45.03" y2="48.59" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#100133"/>
      <stop offset=".36" stop-color="#6c5d90"/>
      <stop offset=".48" stop-color="#a091c5"/>
      <stop offset=".52" stop-color="#a091c5"/>
      <stop offset=".73" stop-color="#433467"/>
      <stop offset=".85" stop-color="#100133"/>
    </linearGradient>
    <linearGradient id="linear-gradient-15" x1="24.62" y1="45.7" x2="34.28" y2="50.5" gradientUnits="userSpaceOnUse">
      <stop offset=".1" stop-color="#fdedc0"/>
      <stop offset=".29" stop-color="#d5b981"/>
      <stop offset=".59" stop-color="#fdedc0" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-16" x1="-3509.76" y1="45.7" x2="-3500.09" y2="50.5" gradientTransform="translate(-3464.24) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-15"/>
    <linearGradient id="linear-gradient-17" x1="30.42" y1="42.87" x2="39.72" y2="42.87" gradientUnits="userSpaceOnUse">
      <stop offset=".15" stop-color="#5e4c8f"/>
      <stop offset=".18" stop-color="#645395"/>
      <stop offset=".29" stop-color="#7869a9"/>
      <stop offset=".4" stop-color="#8477b4"/>
      <stop offset=".5" stop-color="#897cb9"/>
      <stop offset=".54" stop-color="#8376b3"/>
      <stop offset=".85" stop-color="#5e4c8f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-18" x1="35.07" y1="-7.94" x2="35.07" y2="36.05" gradientUnits="userSpaceOnUse">
      <stop offset=".17" stop-color="#5e4c8f"/>
      <stop offset="1" stop-color="#261749"/>
    </linearGradient>
    <linearGradient id="linear-gradient-19" x1="35.07" y1="10.66" x2="35.07" y2="39.69" gradientUnits="userSpaceOnUse">
      <stop offset=".44" stop-color="#4c3679"/>
      <stop offset=".99" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-20" x1="20.5" y1="3.56" x2="20.5" y2="27.26" gradientUnits="userSpaceOnUse">
      <stop offset=".34" stop-color="#d5b981"/>
      <stop offset=".62" stop-color="#fdedc0"/>
      <stop offset=".8" stop-color="#d5b981"/>
      <stop offset=".94" stop-color="#ae9770"/>
    </linearGradient>
    <linearGradient id="linear-gradient-21" x1="16.9" y1="24.83" x2="29.98" y2="24.83" gradientUnits="userSpaceOnUse">
      <stop offset=".02" stop-color="#fdedc0"/>
      <stop offset=".11" stop-color="#e6e0c8"/>
      <stop offset=".22" stop-color="#d5d5cf"/>
      <stop offset=".29" stop-color="#cfd2d2"/>
      <stop offset=".44" stop-color="#d5b981"/>
      <stop offset=".55" stop-color="#bfa677"/>
      <stop offset=".66" stop-color="#ae9770"/>
      <stop offset="1" stop-color="#fdedc0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-22" x1="5813.92" y1="24.83" x2="5827" y2="24.83" gradientTransform="translate(5867.16) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-21"/>
    <linearGradient id="linear-gradient-23" x1="22.88" y1="3.97" x2="22.88" y2="23.77" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#d5b981"/>
      <stop offset=".05" stop-color="#dfc690"/>
      <stop offset=".12" stop-color="#ecd7a5"/>
      <stop offset=".2" stop-color="#f5e3b4"/>
      <stop offset=".29" stop-color="#fbeabd"/>
      <stop offset=".44" stop-color="#fdedc0"/>
      <stop offset=".48" stop-color="#e6d6be"/>
      <stop offset=".55" stop-color="#beafbc"/>
      <stop offset=".62" stop-color="#a093bb"/>
      <stop offset=".68" stop-color="#8f82ba"/>
      <stop offset=".72" stop-color="#897cba"/>
    </linearGradient>
    <linearGradient id="linear-gradient-24" x1="23.7" y1="2.74" x2="23.7" y2="28.09" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#897cba"/>
      <stop offset=".03" stop-color="#9585b0"/>
      <stop offset=".08" stop-color="#b59f98"/>
      <stop offset=".12" stop-color="#d5b981"/>
      <stop offset=".13" stop-color="#dbc18b"/>
      <stop offset=".17" stop-color="#edd9a8"/>
      <stop offset=".2" stop-color="#f9e7b9"/>
      <stop offset=".22" stop-color="#fdedc0"/>
      <stop offset=".53" stop-color="#897cba"/>
      <stop offset=".56" stop-color="#7264a0"/>
      <stop offset=".62" stop-color="#56467f"/>
      <stop offset=".67" stop-color="#3e2e64"/>
      <stop offset=".73" stop-color="#2b1a4f"/>
      <stop offset=".8" stop-color="#1e0d40"/>
      <stop offset=".88" stop-color="#170537"/>
      <stop offset="1" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-25" x1="25.55" y1="1.66" x2="25.55" y2="40.24" xlink:href="#linear-gradient-24"/>
    <linearGradient id="linear-gradient-26" x1="28.4" y1="1.72" x2="28.4" y2="53.03" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#897cba"/>
      <stop offset=".03" stop-color="#9585b0"/>
      <stop offset=".08" stop-color="#b59f98"/>
      <stop offset=".12" stop-color="#d5b981"/>
      <stop offset=".13" stop-color="#dbc18b"/>
      <stop offset=".17" stop-color="#edd9a8"/>
      <stop offset=".2" stop-color="#f9e7b9"/>
      <stop offset=".22" stop-color="#fdedc0"/>
      <stop offset=".43" stop-color="#897cba"/>
      <stop offset=".45" stop-color="#7264a0"/>
      <stop offset=".48" stop-color="#56467f"/>
      <stop offset=".52" stop-color="#3e2e64"/>
      <stop offset=".55" stop-color="#2b1a4f"/>
      <stop offset=".59" stop-color="#1e0d40"/>
      <stop offset=".64" stop-color="#170537"/>
      <stop offset=".71" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-27" x1="5819.9" y1="3.97" x2="5819.9" y2="23.77" gradientTransform="translate(5867.16) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-23"/>
    <linearGradient id="linear-gradient-28" x1="5820.72" y1="2.74" x2="5820.72" y2="28.09" gradientTransform="translate(5867.16) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-24"/>
    <linearGradient id="linear-gradient-29" x1="5822.57" y1="1.66" x2="5822.57" y2="40.24" gradientTransform="translate(5867.16) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-24"/>
    <linearGradient id="linear-gradient-30" x1="5825.42" y1="1.72" x2="5825.42" y2="53.03" gradientTransform="translate(5867.16) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#897cba"/>
      <stop offset=".03" stop-color="#9585b0"/>
      <stop offset=".08" stop-color="#b59f98"/>
      <stop offset=".12" stop-color="#d5b981"/>
      <stop offset=".13" stop-color="#dbc18b"/>
      <stop offset=".17" stop-color="#edd9a8"/>
      <stop offset=".2" stop-color="#f9e7b9"/>
      <stop offset=".22" stop-color="#fdedc0"/>
      <stop offset=".43" stop-color="#897cba"/>
      <stop offset=".45" stop-color="#7264a0"/>
      <stop offset=".48" stop-color="#56467f"/>
      <stop offset=".52" stop-color="#3e2e64"/>
      <stop offset=".55" stop-color="#2b1a4f"/>
      <stop offset=".59" stop-color="#1e0d40"/>
      <stop offset=".64" stop-color="#170537"/>
      <stop offset=".71" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-31" x1="31.28" y1="14.9" x2="31.28" y2="32.77" gradientUnits="userSpaceOnUse">
      <stop offset=".1" stop-color="#fdedc0"/>
      <stop offset=".19" stop-color="#ecdbaf"/>
      <stop offset=".44" stop-color="#bfaa81"/>
      <stop offset=".57" stop-color="#ae9770"/>
      <stop offset=".58" stop-color="#a89172"/>
      <stop offset=".62" stop-color="#8d787c"/>
      <stop offset=".67" stop-color="#786484"/>
      <stop offset=".73" stop-color="#69568a"/>
      <stop offset=".79" stop-color="#604e8d"/>
      <stop offset=".9" stop-color="#5e4c8f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-32" x1="31.93" y1="18.16" x2="31.93" y2="43.75" gradientUnits="userSpaceOnUse">
      <stop offset=".1" stop-color="#fdedc0"/>
      <stop offset=".19" stop-color="#ecdbaf"/>
      <stop offset=".45" stop-color="#bfaa81"/>
      <stop offset=".59" stop-color="#ae9770"/>
      <stop offset=".69" stop-color="#7e695d"/>
      <stop offset=".9" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-33" x1="38.86" y1="14.9" x2="38.86" y2="32.77" xlink:href="#linear-gradient-31"/>
    <linearGradient id="linear-gradient-34" x1="38.21" y1="18.16" x2="38.21" y2="43.75" xlink:href="#linear-gradient-32"/>
    <linearGradient id="linear-gradient-35" x1="41.04" y1="42.87" x2="32.96" y2="42.87" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#897cb9"/>
      <stop offset=".66" stop-color="#3a295f"/>
      <stop offset="1" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-36" x1="5822.5" y1="19.25" x2="5826.56" y2="12.29" gradientTransform="translate(5867.16) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-13"/>
    <linearGradient id="linear-gradient-37" x1="5817.52" y1="3.56" x2="5817.52" y2="27.26" gradientTransform="translate(5867.16) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".48" stop-color="#d5b981"/>
      <stop offset=".62" stop-color="#fdedc0"/>
      <stop offset=".8" stop-color="#d5b981"/>
      <stop offset=".94" stop-color="#ae9770"/>
    </linearGradient>
    <linearGradient id="linear-gradient-38" x1="6942.79" y1="50.79" x2="6962.78" y2="50.79" gradientTransform="translate(6987.86) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#150335"/>
      <stop offset=".04" stop-color="#251448"/>
      <stop offset=".14" stop-color="#493970"/>
      <stop offset=".24" stop-color="#655690"/>
      <stop offset=".33" stop-color="#786ba6"/>
      <stop offset=".42" stop-color="#8477b4"/>
      <stop offset=".5" stop-color="#897cb9"/>
      <stop offset=".64" stop-color="#8679b6"/>
      <stop offset=".72" stop-color="#7f72ae"/>
      <stop offset=".79" stop-color="#7365a0"/>
      <stop offset=".85" stop-color="#61528b"/>
      <stop offset=".91" stop-color="#4a3a71"/>
      <stop offset=".96" stop-color="#2e1d52"/>
      <stop offset="1" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-39" x1="30.83" y1="41.99" x2="38.94" y2="46.92" gradientUnits="userSpaceOnUse">
      <stop offset=".08" stop-color="#e1ca93"/>
      <stop offset=".43" stop-color="#9688b3"/>
      <stop offset=".5" stop-color="#897cba"/>
      <stop offset=".73" stop-color="#3a295f"/>
      <stop offset=".86" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-40" x1="35.07" y1="22.88" x2="35.07" y2="42.27" xlink:href="#linear-gradient-19"/>
    <linearGradient id="linear-gradient-41" x1="32.61" y1="23.69" x2="32.61" y2="42.27" gradientUnits="userSpaceOnUse">
      <stop offset=".1" stop-color="#fdedc0"/>
      <stop offset=".4" stop-color="#e1c894"/>
      <stop offset=".57" stop-color="#d5b981"/>
      <stop offset=".59" stop-color="#c9ae82"/>
      <stop offset=".63" stop-color="#a89086"/>
      <stop offset=".69" stop-color="#8d7789"/>
      <stop offset=".75" stop-color="#78648b"/>
      <stop offset=".81" stop-color="#69568d"/>
      <stop offset=".88" stop-color="#604e8e"/>
      <stop offset="1" stop-color="#5e4c8f"/>
    </linearGradient>
    <linearGradient id="linear-gradient-42" x1="5176.83" y1="23.69" x2="5176.83" y2="42.27" gradientTransform="translate(5214.36) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-41"/>
    <linearGradient id="linear-gradient-43" x1="35.07" y1="31.47" x2="35.07" y2="43.66" gradientUnits="userSpaceOnUse">
      <stop offset=".02" stop-color="#150335"/>
      <stop offset=".08" stop-color="#301e55"/>
      <stop offset=".22" stop-color="#3c2a64"/>
      <stop offset=".63" stop-color="#5e4c8f"/>
      <stop offset=".68" stop-color="#513f7f"/>
      <stop offset=".83" stop-color="#301e55"/>
      <stop offset=".98" stop-color="#150335"/>
    </linearGradient>
    <linearGradient id="linear-gradient-44" x1="33.54" y1="28.62" x2="33.54" y2="40.24" xlink:href="#linear-gradient-41"/>
    <linearGradient id="linear-gradient-45" x1="-1136.77" y1="28.62" x2="-1136.77" y2="40.24" gradientTransform="translate(-1100.18) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-41"/>
    <linearGradient id="linear-gradient-46" x1="35.07" y1="-3.53" x2="35.07" y2="39.16" gradientUnits="userSpaceOnUse">
      <stop offset=".53" stop-color="#9b92c6" stop-opacity="0"/>
      <stop offset=".59" stop-color="#7f74a8" stop-opacity=".2"/>
      <stop offset=".85" stop-color="#150335"/>
    </linearGradient>
    <radialGradient id="radial-gradient-6" cx="35.09" cy="23.87" fx="35.09" fy="23.87" r="29.36" gradientTransform="translate(54.82 -11.43) rotate(89.41) scale(1 .84)" gradientUnits="userSpaceOnUse">
      <stop offset=".42" stop-color="#fff" stop-opacity="0"/>
      <stop offset=".43" stop-color="#fff" stop-opacity=".25"/>
      <stop offset=".46" stop-color="#fff"/>
      <stop offset=".48" stop-color="#fff" stop-opacity=".58"/>
      <stop offset=".5" stop-color="#fff" stop-opacity=".17"/>
      <stop offset=".51" stop-color="#fff" stop-opacity="0"/>
    </radialGradient>
  </defs>
  <g>
    <g>
      <polygon class="cls-4" points="36.92 55.25 36.92 54.39 34.04 54.39 34.04 58.37 35.07 58.37 35.07 57.11 34.95 56.78 36.42 56.78 36.42 55.97 35.07 55.97 35.07 55.25 36.92 55.25"/>
      <path class="cls-4" d="M38.18,58.17c-.34-.16-.6-.4-.78-.71-.18-.31-.28-.67-.28-1.08,0-.41.09-.77.28-1.08.18-.31.45-.54.78-.71.34-.17.73-.25,1.18-.25.29,0,.56.03.79.08.23.06.44.14.64.26v.94c-.17-.13-.37-.23-.58-.28s-.47-.09-.75-.09c-.4,0-.72.1-.93.3-.22.2-.33.48-.33.83,0,.35.11.63.33.83.22.2.53.3.93.3.28,0,.53-.03.76-.1.22-.06.43-.16.62-.3v.93c-.37.24-.86.36-1.47.36-.45,0-.84-.08-1.18-.25"/>
      <path class="cls-4" d="M30.99,56.78l.51-1.34.51,1.34h-1.02ZM33.7,58.37l-1.67-3.98h-1.39l.14.49-1.47,3.49h1.09l.2-.49-.11-.33h1.79s.34.81.34.81h1.08Z"/>
    </g>
    <g>
      <path class="cls-4" d="M22.06,65.13h-1.52v-2.23h-1.76v2.23h-1.52v-5.47h1.52v2.03h1.76v-2.03h1.52v5.47Z"/>
      <path class="cls-4" d="M12.92,64.87c-.47-.23-.83-.55-1.08-.98-.25-.42-.38-.92-.38-1.49,0-.57.13-1.07.38-1.49.25-.42.62-.75,1.08-.98.47-.23,1.01-.34,1.63-.34.41,0,.77.04,1.09.12.32.08.61.2.89.36v1.3c-.24-.18-.51-.31-.81-.39s-.65-.12-1.04-.12c-.56,0-.99.14-1.29.41-.3.28-.45.66-.45,1.15,0,.49.15.87.46,1.15.31.28.73.42,1.29.42.39,0,.74-.04,1.05-.13.31-.09.6-.23.86-.42v1.29c-.51.33-1.19.5-2.04.5-.62,0-1.16-.11-1.63-.34"/>
      <path class="cls-4" d="M24.79,62.94l.71-1.84.7,1.84h-1.41ZM28.51,65.13l-2.3-5.47h-1.91l.19.67-2.02,4.8h1.5l.27-.67-.16-.45h2.47s.46,1.12.46,1.12h1.49Z"/>
      <path class="cls-4" d="M37.91,62.09c-.15.11-.37.16-.65.16h-.8v-1.43h.8c.29,0,.5.05.65.15.15.1.23.29.23.56s-.08.45-.23.55M39.44,62.58c.19-.28.27-.63.27-1.04,0-.61-.18-1.07-.55-1.4-.37-.32-.88-.49-1.52-.49h-2.49v5.47h1.44v-1.26l-.15-.45h1.24c.32,0,.71-.06,1.01-.2.39-.18.65-.48.76-.64"/>
      <polygon class="cls-4" points="41.93 59.66 40.1 59.66 40.29 60.33 40.29 64.46 40.1 65.13 41.92 65.13 41.73 64.46 41.74 60.33 41.93 59.66"/>
      <path class="cls-4" d="M42.31,62.36c0-1.53,1.33-2.78,3.01-2.78s3.02,1.24,3.02,2.78-1.32,2.78-3.02,2.78-3.01-1.24-3.01-2.78ZM46.92,62.36c0-.83-.69-1.51-1.6-1.51s-1.59.68-1.59,1.51.66,1.5,1.59,1.5,1.6-.68,1.6-1.5Z"/>
      <path class="cls-4" d="M28.51,59.66h1.35l1.61,2.05,1.66-2.05h1.3v5.47h-1.39v-3.47l-1.57,1.94-1.56-1.93v3.46h-1.4v-5.47Z"/>
      <polygon class="cls-4" points="53.75 65.09 53.75 59.66 52.3 59.66 52.3 62.89 50.2 59.66 48.77 59.66 48.77 65.09 50.22 65.09 50.22 61.85 52.33 65.09 53.75 65.09"/>
      <path class="cls-4" d="M54.64,63.97l-.53,1.12h2.39c.73,0,1.28-.14,1.65-.41.39-.29.61-.78.51-1.26-.23-1.12-1.6-1.32-2.51-1.86-.15-.09-.23-.21-.23-.35,0-.14.06-.26.21-.33.15-.08.36-.12.61-.12h1.31l.4-1.11h-1.97c-.41,0-.77.04-1.08.17-.31.13-.56.31-.73.55-.18.23-.26.5-.26.8,0,.36.12.67.35.92.25.27.62.42.95.56.43.19.92.33,1.3.6.09.06.17.17.17.27,0,.12-.03.21-.11.27-.26.2-.62.17-.95.17h-1.48Z"/>
    </g>
    <g>
      <polygon class="cls-4" points="21.71 71.05 21.71 66.71 20.24 66.71 20.24 72.29 20.3 72.29 21.71 72.29 24.29 72.29 24.29 71.05 21.71 71.05"/>
      <polygon class="cls-4" points="24.79 72.29 28.83 72.29 28.83 71.1 26.23 71.1 26.23 70.05 28.13 70.05 28.13 68.94 26.23 68.94 26.23 67.89 28.83 67.89 28.83 66.71 24.79 66.71 24.79 72.29"/>
      <path class="cls-4" d="M31.19,70.06l.72-1.88.72,1.88h-1.44ZM34.98,72.29l-2.35-5.59h-1.95l.2.69-2.06,4.9h1.53l.28-.69-.16-.46h2.52s.47,1.14.47,1.14h1.52Z"/>
      <path class="cls-4" d="M42.73,72.38c.7,0,1.49-.2,1.92-.64.43-.45.64-1.04.64-1.79v-3.24h-1.43v3.25c0,.37-.1.65-.3.85-.2.19-.48.29-.83.29s-.63-.1-.83-.29c-.2-.19-.3-.47-.3-.85v-3.25h-1.43v3.24c0,.74.21,1.34.64,1.79.43.45,1.22.64,1.92.64"/>
      <polygon class="cls-4" points="45.86 72.29 49.9 72.29 49.9 71.1 47.3 71.1 47.3 70.05 49.2 70.05 49.2 68.94 47.3 68.94 47.3 67.89 49.9 67.89 49.9 66.71 45.86 66.71 45.86 72.29"/>
      <path class="cls-4" d="M39.8,69.65h-1.42v1.16l.33.19s-.13.05-.34.08c-.17.02-.34.03-.53.03-.56,0-.99-.14-1.3-.42-.31-.28-.46-.67-.46-1.17,0-.5.15-.89.46-1.17.31-.28.74-.42,1.3-.42.39,0,.74.04,1.05.13.3.08.58.22.82.4v-1.32c-.27-.17-.57-.29-.89-.37-.32-.08-.69-.12-1.1-.12-.62,0-1.17.12-1.64.35-.47.23-.84.57-1.09,1-.26.43-.38.94-.38,1.52,0,.58.13,1.09.39,1.52.26.43.62.76,1.1.99.47.23,1.02.35,1.64.34.85,0,1.57-.18,2.09-.52h0s0-.01,0-.01v-2.2Z"/>
    </g>
    <g>
      <g>
        <polygon class="cls-18" points="35.51 73.9 33.64 73.9 33.83 74.58 33.83 78.79 33.64 79.47 35.51 79.47 35.32 78.79 35.32 74.58 35.51 73.9"/>
        <polygon class="cls-18" points="30.7 78.23 30.7 73.92 29.23 73.92 29.23 79.47 29.29 79.47 30.7 79.47 33.26 79.47 33.26 78.23 30.7 78.23"/>
        <polygon class="cls-18" points="24.71 79.47 28.73 79.47 28.73 78.29 26.14 78.29 26.14 77.24 28.03 77.24 28.03 76.14 26.14 76.14 26.14 75.09 28.73 75.09 28.73 73.92 24.71 73.92 24.71 79.47"/>
        <polygon class="cls-18" points="40.89 73.9 35.93 73.9 35.93 75.19 37.66 75.19 37.66 79.47 39.19 79.47 39.19 75.19 40.89 75.19 40.89 73.9"/>
        <polygon class="cls-18" points="41.4 79.47 45.43 79.47 45.43 78.28 42.84 78.28 42.84 77.24 44.73 77.24 44.73 76.13 42.84 76.13 42.84 75.08 45.43 75.08 45.43 73.9 41.4 73.9 41.4 79.47"/>
      </g>
      <path class="cls-18" d="M47.15,74.82h-.16v-.92h.14l.3.41.28-.39h.16v.9h-.18v-.62l-.25.34h-.04l-.27-.34v.62ZM46.54,74.07v.74h-.18v-.74h-.25v-.18h.69v.18h-.27Z"/>
    </g>
  </g>
  <g>
    <path class="cls-15" d="M40.77,36.45c.1-3.63-1.06-7.1-2.13-11.26-1.28-4.94-2.58-8.9-3.22-10.33,0,0,0,0,0,0-.19-.37-.31-.57-.35-.57s-.16.2-.35.57h0s0,0,0,0c-.64,1.43-1.94,5.38-3.22,10.33-1.07,4.16-2.23,7.63-2.13,11.26h0c.09.48.21,1.02.39,1.56.59,1.85,1.34,3.23,2.34,3.65.69.29,2.65.44,2.97.44s2.28-.15,2.97-.44c1.01-.42,1.75-1.8,2.34-3.65.17-.54.3-1.08.39-1.56h0ZM35.4,42.07s-.02,0-.03,0c-.1,0-.2,0-.3,0s-.2,0-.3,0c-.01,0-.02,0-.03,0-2.7-.16-4.32-2.32-4.32-5.44,0-2.32.77-4.52,1.63-7.16,1.29-3.96,2.89-6.9,3.02-6.9s1.73,2.94,3.02,6.9c.86,2.64,1.63,4.84,1.63,7.16,0,3.12-1.61,5.28-4.32,5.44ZM30.61,37.16c0-2.27.91-4.57,1.73-7.16,1.24-3.94,2.53-7.01,2.73-7.01s1.49,3.08,2.73,7.01c.82,2.58,1.73,4.89,1.73,7.16,0,2.51-1.64,4.93-4.46,4.93-2.81,0-4.46-2.42-4.46-4.93ZM33,37.88c0-3.77,1.96-6.48,2.07-6.48s2.07,2.72,2.07,6.48c0,1.8-.5,4.2-2.07,4.2-1.58,0-2.07-2.4-2.07-4.2ZM35.07,5.45c.16,0,3.51,5.61,5.13,13.63,1.04,5.16.83,4.25.81,13.77h0c-.18-3.1-1.14-6.08-2.11-9.64-1.61-5.93-3.66-10.32-3.83-10.32s-2.21,4.4-3.83,10.32c-.97,3.56-1.93,6.54-2.11,9.64h0c-.02-9.52-.23-8.61.81-13.77,1.61-8.02,4.96-13.63,5.13-13.63Z"/>
    <path class="cls-14" d="M43.85,16.7c.41-1.18.87-2.35,1.37-3.46,1.86-4.12,4.29-5.06,5.44-5.37-.02-.33-.04-.66-.07-.98-.06-.29-.26-.58-.59-.85-.91.31-3.22,1.41-4.61,5.04-.61,1.59-.68,2.85-1.54,5.63Z"/>
    <path class="cls-13" d="M42.88,19.84c.28-1.03.61-2.08.97-3.12h0c.86-2.79.93-4.05,1.54-5.65,1.39-3.63,3.7-4.74,4.61-5.04-.58-.47-1.55-.91-2.83-1.28-.43.26-2.14,1.46-3.1,4.37-1.23,3.77-.3,6.72-1.2,10.72Z"/>
    <path class="cls-12" d="M42,23.56c.22-1.15.51-2.41.87-3.71h0c.91-4-.03-6.95,1.2-10.72.95-2.92,2.67-4.11,3.1-4.37-1.26-.37-2.82-.67-4.6-.9-.12.18-.71,1.11-.95,2.35-.54,2.74.05,5.01.6,9.02.25,1.85.16,4.86-.22,8.34Z"/>
    <path class="cls-9" d="M26.29,16.7c-.41-1.18-.87-2.35-1.37-3.46-1.86-4.12-4.29-5.06-5.44-5.37.02-.33.04-.66.07-.98.06-.29.26-.58.59-.85.91.31,3.22,1.41,4.61,5.04.61,1.59.68,2.85,1.54,5.63Z"/>
    <path class="cls-10" d="M27.26,19.84c-.28-1.03-.61-2.08-.97-3.12h0c-.86-2.79-.93-4.05-1.54-5.65-1.39-3.63-3.7-4.74-4.61-5.04.58-.47,1.55-.91,2.83-1.28.43.26,2.14,1.46,3.1,4.37,1.23,3.77.3,6.72,1.2,10.72Z"/>
    <path class="cls-8" d="M28.14,23.56c-.22-1.15-.51-2.41-.87-3.71h0c-.91-4,.03-6.95-1.2-10.72-.95-2.92-2.67-4.11-3.1-4.37,1.26-.37,2.82-.67,4.6-.9.12.18.71,1.11.95,2.35.54,2.74-.05,5.01-.6,9.02-.25,1.85-.16,4.86.22,8.34Z"/>
    <path class="cls-7" d="M42.22,15.23c.1.7.14,1.56.14,2.55,0,1.64-.13,3.63-.36,5.8-.21,1.08-.35,2.06-.43,2.9-.11,1.18-.22,2.61-.31,3.96-.09,1.35-.17,2.61-.22,3.44,0-.34-.01-.68-.03-1.01.02-9.52.23-8.61-.81-13.77-1.62-8.02-4.96-13.63-5.13-13.63s-3.51,5.61-5.13,13.63c-.72,3.57-.84,4.23-.84,7.47,0,1.43.02,3.37.03,6.3-.02.34-.03.68-.03,1.02-.1-1.66-.31-5.04-.54-7.4-.08-.84-.22-1.82-.43-2.9h0c-.38-3.48-.47-6.49-.22-8.34.55-4.01,1.14-6.28.6-9.02-.24-1.24-.83-2.17-.95-2.35,2.2-.28,4.75-.44,7.5-.44s5.3.16,7.5.44c-.12.18-.71,1.11-.95,2.35-.54,2.74.05,5.01.6,9.02Z"/>
    <path class="cls-16" d="M42,23.57c-.21,1.08-.35,2.06-.43,2.9-.11,1.18-.22,2.61-.31,3.96-.09,1.35-.17,2.61-.22,3.44,0,.08-.02.15-.02.21,0,.08,0,.16,0,.24-.02.35-.03.56-.03.56,0,0-.09,1.59-.59,3.14-.59,1.85-1.34,3.23-2.34,3.65-.69.29-2.65.44-2.97.44s-2.28-.15-2.97-.44c-1.01-.42-1.75-1.8-2.34-3.65-.5-1.56-.59-3.14-.59-3.14,0,0,0-.05,0-.15,0-.1-.01-.23-.02-.41,0-.09,0-.18,0-.28,0-.05-.02-.11-.03-.18-.1-1.66-.31-5.04-.54-7.4-.08-.84-.22-1.82-.43-2.9h0s0,0,0,0c-.22-1.15-.51-2.41-.87-3.71h0s0,0,0-.01c-.28-1.03-.61-2.08-.97-3.12h0s0-.02,0-.02c-.41-1.18-.87-2.35-1.37-3.46-1.86-4.12-4.29-5.06-5.44-5.37.02-.33.04-.66.07-.98.06-.29.26-.58.59-.85.58-.47,1.55-.91,2.83-1.28,1.26-.37,2.82-.67,4.6-.9,2.2-.28,4.75-.44,7.5-.44s5.3.16,7.5.44c1.79.23,3.34.53,4.6.9,1.28.37,2.25.81,2.83,1.28.33.27.53.56.59.85.02.32.04.65.07.98-1.15.31-3.58,1.26-5.44,5.37-.5,1.11-.96,2.28-1.37,3.46,0,0,0,0,0,.01h0c-.36,1.05-.68,2.1-.97,3.13,0,0,0,0,0,0h0c-.36,1.3-.65,2.56-.87,3.71h0Z"/>
    <path class="cls-2" d="M33,37.88c0-3.77,1.96-6.48,2.07-6.48s2.07,2.72,2.07,6.48c0,1.8-.5,4.2-2.07,4.2-1.58,0-2.07-2.4-2.07-4.2Z"/>
    <path class="cls-11" d="M33,37.88c0-3.77,1.96-6.48,2.07-6.48s2.07,2.72,2.07,6.48c0,1.8-.5,4.2-2.07,4.2-1.58,0-2.07-2.4-2.07-4.2Z"/>
    <path class="cls-3" d="M37.8,30c-1.24-3.94-2.53-7.01-2.73-7.01s-1.49,3.08-2.73,7.01c-.82,2.58-1.73,4.89-1.73,7.16,0,2.51,1.64,4.93,4.46,4.93s4.46-2.42,4.46-4.93c0-2.27-.91-4.57-1.73-7.16ZM37.49,40.02c-.39,1.11-1.11,2.07-2.42,2.07s-2.02-.94-2.41-2.04c-.47-.71-.66-1.88-.64-3.21.05-3.04,2.92-5.58,3.05-5.66h0s.03.02.05.04c0,0,0,0,0,0,.42.36,2.94,2.76,2.99,5.62.02,1.31-.16,2.47-.62,3.18Z"/>
    <path class="cls-28" d="M35.07,2.75c10.02,0,18.19,1.85,18.19,4.13l.07-1.74c0-2.29-8.2-4.15-18.26-4.15-10.06,0-18.26,1.86-18.26,4.15l.07,1.74c0-2.28,8.17-4.13,18.19-4.13Z"/>
    <path class="cls-17" d="M35.07,2.75c10.02,0,18.19,1.85,18.19,4.13l.07-1.74c0-2.29-8.2-4.15-18.26-4.15-10.06,0-18.26,1.86-18.26,4.15l.07,1.74c0-2.28,8.17-4.13,18.19-4.13Z"/>
    <path class="cls-29" d="M35.07,3.42c8.57,0,15.14,1.54,15.53,3.47h2.66c0-2.28-8.17-4.13-18.19-4.13-10.02,0-18.19,1.85-18.19,4.13h2.66c.38-1.93,6.96-3.47,15.53-3.47Z"/>
    <path class="cls-6" d="M29.98,38.66c-.38-.4-1.75-1.89-3.87-4.37-2.33-2.73-4.95-7.14-6.88-12.09-1.61-4.14-2.61-9.74-2.34-15.31h2.66c-.27,4.14-.7,9.14.94,14.6,1.64,5.46,7.31,11.36,8.65,12.69,0,.05,0,.09,0,.13.02.35.03.56.03.56,0,0,.09,1.59.59,3.14.07.22.14.44.22.64Z"/>
    <path class="cls-5" d="M40.16,38.66c.38-.4,1.75-1.89,3.87-4.37,2.33-2.73,4.95-7.14,6.88-12.09,1.61-4.14,2.61-9.74,2.34-15.31h-2.66c.27,4.14.7,9.14-.94,14.6-1.64,5.46-7.31,11.36-8.65,12.69,0,.05,0,.09,0,.13-.02.35-.03.56-.03.56,0,0-.09,1.59-.59,3.14-.07.22-.14.44-.22.64Z"/>
    <path class="cls-30" d="M29.98,38.66c-.38-.4-1.75-1.89-3.87-4.37-2.33-2.73-4.95-7.14-6.88-12.09-1.61-4.14-2.61-9.74-2.34-15.31h2.66c-.27,4.14-.7,9.14.94,14.6,1.64,5.46,7.31,11.36,8.65,12.69,0,.05,0,.09,0,.13.02.35.03.56.03.56,0,0,.09,1.59.59,3.14.07.22.14.44.22.64Z"/>
    <path class="cls-32" d="M35.07,51.07h10s0-.48,0-.48c0,0,0-.09-.11-.09h-.25s-2.23-4.44-2.23-4.44c0,0-.14-.31-.32-.31h-7.08s0,0,0,0h-7.08c-.17,0-.32.31-.32.31l-2.23,4.44h-.25c-.11,0-.11.09-.11.09v.48h10Z"/>
    <path class="cls-43" d="M35.07,45.75h-7.08c-.17,0-.32.31-.32.31l-2.23,4.44h-.25c-.11,0-.11.09-.11.09v.48h10v-5.33Z"/>
    <path class="cls-44" d="M35.07,45.75h7.08c.17,0,.32.31.32.31l2.23,4.44h.25c.11,0,.11.09.11.09v.48h-10s0,0,0,0v-5.33Z"/>
    <path class="cls-45" d="M39.72,44.2h-9.3l1.43-2.66c.08.05.16.09.25.13.69.29,2.65.44,2.97.44s2.28-.15,2.97-.44c.09-.04.17-.08.25-.13l1.43,2.66Z"/>
    <path class="cls-46" d="M35.07,5.45c.16,0,3.51,5.61,5.13,13.63.54,2.67.74,3.71.81,5.42h0c.17-1.3.01-4.1-.37-5.89-1.75-8.27-5.39-14.06-5.57-14.06s-3.81,5.79-5.57,14.06c-.38,1.79-.53,4.59-.37,5.89h0c.07-1.71.27-2.75.81-5.42,1.61-8.02,4.96-13.63,5.13-13.63"/>
    <path class="cls-31" d="M35.07,12.89c.16,0,2.21,4.4,3.83,10.32,1.08,3.95,2.14,7.18,2.14,10.65-.01.17-.02.32-.03.45h0c0-3.31-1.09-6.44-2.16-10.2-1.61-5.64-3.61-9.83-3.77-9.83s-2.16,4.19-3.77,9.83c-1.08,3.76-2.16,6.89-2.16,10.2h0c0-.13-.02-.28-.03-.45,0-3.47,1.06-6.71,2.14-10.65,1.61-5.93,3.66-10.32,3.83-10.32Z"/>
    <path class="cls-34" d="M20.48,21.49c.38,1.27.98,2.56,1.69,3.82-.82-1.2-1.57-2.64-2.1-4.4-1.55-5.18-1.33-9.95-1.06-14.02h.53c-.27,4.14-.7,9.14.94,14.6Z"/>
    <path class="cls-33" d="M29.98,38.66c-.38-.4-1.75-1.89-3.87-4.37-2.33-2.73-4.95-7.14-6.88-12.09-1.21-3.11-2.08-7.06-2.32-11.19.35,3.91,1.24,7.61,2.42,10.57,2.05,5.15,4.82,9.72,7.27,12.56,1.29,1.49,2.32,2.64,3.03,3.41.04.16.09.31.14.47.07.22.14.44.22.64Z"/>
    <path class="cls-35" d="M40.16,38.66c.38-.4,1.75-1.89,3.87-4.37,2.33-2.73,4.95-7.14,6.88-12.09,1.21-3.11,2.08-7.06,2.32-11.19-.35,3.91-1.24,7.61-2.42,10.57-2.05,5.15-4.82,9.72-7.27,12.56-1.29,1.49-2.32,2.64-3.03,3.41-.04.16-.09.31-.14.47-.07.22-.14.44-.22.64Z"/>
    <path class="cls-36" d="M26.29,16.7c-.41-1.18-.87-2.35-1.37-3.46-1.86-4.12-4.29-5.06-5.44-5.37,0-.14.02-.27.03-.41,1.23.39,3.78,1.49,5.52,5.71.4.98.77,2,1.11,3.04,0,0,0,0,0,.01.05.16.1.32.15.49Z"/>
    <path class="cls-51" d="M27.26,19.84c-.28-1.03-.61-2.08-.97-3.12h0c-.86-2.79-.93-4.05-1.54-5.65-1.39-3.63-3.7-4.74-4.61-5.04.1-.08.22-.17.34-.25,1.22.51,3.28,1.73,4.48,4.88.64,1.67.72,2.99,1.62,5.9h0c.11.33.22.66.33.98.07.73.17,1.49.36,2.29Z"/>
    <path class="cls-38" d="M28.14,23.56c-.22-1.15-.51-2.41-.87-3.71h0c-.91-4,.03-6.95-1.2-10.72-.95-2.92-2.67-4.11-3.1-4.37.34-.1.7-.19,1.09-.28.71.73,1.6,1.92,2.2,3.76,1.31,4.01.55,7.34,1.28,11.39h0c.13.49.26.96.37,1.43.06.8.14,1.64.23,2.51Z"/>
    <path class="cls-52" d="M28.32,15.13c-.28,1.84-.22,4.86.11,8.34h0c.19,1.08.32,2.07.39,2.9.19,2.36.2,5.82.28,7.48-.1-1.66-.31-5.04-.54-7.4-.08-.84-.22-1.82-.43-2.9h0c-.38-3.48-.47-6.49-.22-8.34.55-4.01,1.14-6.28.6-9.02-.24-1.24-.83-2.17-.95-2.35.19-.02.38-.05.57-.07.15.24.69,1.14.9,2.33.5,2.75-.12,5.01-.72,9.02Z"/>
    <path class="cls-50" d="M43.85,16.7c.41-1.18.87-2.35,1.37-3.46,1.86-4.12,4.29-5.06,5.44-5.37,0-.14-.02-.27-.03-.41-1.23.39-3.78,1.49-5.52,5.71-.4.98-.77,2-1.11,3.04,0,0,0,0,0,.01-.05.16-.1.32-.15.49Z"/>
    <path class="cls-37" d="M42.88,19.84c.28-1.03.61-2.08.97-3.12h0c.86-2.79.93-4.05,1.54-5.65,1.39-3.63,3.7-4.74,4.61-5.04-.1-.08-.22-.17-.34-.25-1.22.51-3.28,1.73-4.48,4.88-.64,1.67-.72,2.99-1.62,5.9h0c-.11.33-.22.66-.33.98-.07.73-.17,1.49-.36,2.29Z"/>
    <path class="cls-53" d="M42,23.56c.22-1.15.51-2.41.87-3.71h0c.91-4-.03-6.95,1.2-10.72.95-2.92,2.67-4.11,3.1-4.37-.34-.1-.7-.19-1.09-.28-.71.73-1.6,1.92-2.2,3.76-1.31,4.01-.55,7.34-1.28,11.39h0c-.13.49-.26.96-.37,1.43-.06.8-.14,1.64-.23,2.51Z"/>
    <path class="cls-42" d="M41.82,15.13c.28,1.84.22,4.86-.11,8.34h0c-.19,1.08-.32,2.07-.39,2.9-.19,2.36-.2,5.82-.28,7.48.1-1.66.31-5.04.54-7.4.08-.84.22-1.82.43-2.9h0c.38-3.48.47-6.49.22-8.34-.55-4.01-1.14-6.28-.6-9.02.24-1.24.83-2.17.95-2.35-.19-.02-.38-.05-.57-.07-.15.24-.69,1.14-.9,2.33-.5,2.75.12,5.01.72,9.02Z"/>
    <path class="cls-40" d="M33.45,8.38c-.93,2.18-2.32,6.13-3.41,11.5-.54,2.7-.83,4.41-.94,5.91.02-2.61.18-3.44.84-6.71.91-4.53,2.38-8.28,3.51-10.7Z"/>
    <path class="cls-39" d="M31.5,25.19c-1.07,4.16-2.23,7.63-2.13,11.26h0c-.17-.9-.21-1.58-.21-1.58,0,0-.01-.2-.03-.56,0-3.31,1.09-6.44,2.16-10.2,1.26-4.41,2.76-7.93,3.43-9.26-.63,1.43-1.94,5.38-3.22,10.33Z"/>
    <path class="cls-41" d="M36.69,8.38c.93,2.18,2.32,6.13,3.41,11.5.54,2.7.83,4.41.94,5.91-.02-2.61-.18-3.44-.84-6.71-.91-4.53-2.38-8.28-3.51-10.7Z"/>
    <path class="cls-48" d="M38.64,25.19c1.07,4.16,2.23,7.63,2.13,11.26h0c.17-.9.21-1.58.21-1.58,0,0,.01-.2.03-.56,0-3.31-1.09-6.44-2.16-10.2-1.26-4.41-2.76-7.93-3.43-9.26.63,1.43,1.94,5.38,3.22,10.33Z"/>
    <path class="cls-47" d="M35.07,42.11c.32,0,2.28-.15,2.97-.44.08-.04.17-.08.25-.13l1.43,2.66h-4.65v-2.09Z"/>
    <path class="cls-49" d="M40.16,38.66c.38-.4,1.75-1.89,3.87-4.37,2.33-2.73,4.95-7.14,6.88-12.09,1.61-4.14,2.61-9.74,2.34-15.31h-2.66c.27,4.14.7,9.14-.94,14.6-1.64,5.46-7.31,11.36-8.65,12.69,0,.05,0,.09,0,.13-.02.35-.03.56-.03.56,0,0-.09,1.59-.59,3.14-.07.22-.14.44-.22.64Z"/>
    <path class="cls-54" d="M49.66,21.49c-.38,1.27-.98,2.56-1.69,3.82.82-1.2,1.57-2.64,2.1-4.4,1.55-5.18,1.33-9.95,1.06-14.02h-.53c.27,4.14.7,9.14-.94,14.6Z"/>
    <path class="cls-20" d="M25.44,50.5h-.25c-.11,0-.11.09-.11.09v.48h19.99s0-.48,0-.48c0,0,0-.09-.11-.09h-.25s-19.27,0-19.27,0Z"/>
    <path class="cls-19" d="M35.07,42.11c-.32,0-2.28-.15-2.97-.44-.08-.04-.17-.08-.25-.13l-1.43,2.66h4.65v-2.09Z"/>
    <path class="cls-22" d="M35.37,42.07c2.63-.17,4.15-2.5,4.15-4.92,0-2.27-.91-4.57-1.73-7.16-1.24-3.94-2.53-7.01-2.73-7.01s-1.49,3.08-2.73,7.01c-.82,2.58-1.73,4.89-1.73,7.16,0,2.42,1.53,4.75,4.15,4.92-2.72-.15-4.35-2.31-4.35-5.44,0-2.32.77-4.52,1.63-7.16,1.29-3.96,2.89-6.9,3.02-6.9s1.73,2.94,3.02,6.9c.86,2.64,1.63,4.84,1.63,7.16,0,3.13-1.63,5.3-4.35,5.44Z"/>
    <path class="cls-23" d="M34.8,23.39c-.48.96-1.49,3.52-2.46,6.61-.82,2.58-1.73,4.89-1.73,7.16,0,2.42,1.53,4.75,4.15,4.92-2.72-.15-4.35-2.31-4.35-5.44,0-2.32.77-4.52,1.63-7.16.72-2.2,2.75-6.08,2.75-6.08Z"/>
    <path class="cls-24" d="M35.34,23.39c.48.96,1.49,3.52,2.46,6.61.82,2.58,1.73,4.89,1.73,7.16,0,2.42-1.53,4.75-4.15,4.92,2.72-.15,4.35-2.31,4.35-5.44,0-2.32-.77-4.52-1.63-7.16-.72-2.2-2.75-6.08-2.75-6.08Z"/>
    <path class="cls-21" d="M37.9,37.4c0,1.43-.42,4.68-2.84,4.68,1.58,0,2.07-2.4,2.07-4.2,0-3.77-1.96-6.49-2.07-6.49s-2.07,2.72-2.07,6.49c0,1.8.5,4.2,2.07,4.2-2.42,0-2.84-3.24-2.84-4.68,0-3.27,2.8-6.24,2.84-6.24s2.84,2.97,2.84,6.24Z"/>
    <path class="cls-26" d="M32.23,37.4c0,.66.09,1.71.42,2.64-.47-.71-.66-1.88-.64-3.21.05-3.04,2.92-5.58,3.05-5.66-.13.1-2.83,3.02-2.83,6.23Z"/>
    <path class="cls-25" d="M37.9,37.4c0,.66-.09,1.71-.42,2.64.47-.71.66-1.88.64-3.21-.05-3.04-2.92-5.58-3.05-5.66.13.1,2.83,3.02,2.83,6.23Z"/>
    <path class="cls-27" d="M35.07,5.45c.16,0,3.51,5.61,5.13,13.63,1.04,5.16.83,4.25.81,13.77h0c-.18-3.1-1.14-6.08-2.11-9.64-1.61-5.93-3.66-10.32-3.83-10.32s-2.21,4.4-3.83,10.32c-.97,3.56-1.93,6.54-2.11,9.64h0c-.02-9.52-.23-8.61.81-13.77,1.61-8.02,4.96-13.63,5.13-13.63Z"/>
    <path class="cls-1" d="M40.77,36.45c.1-3.63-1.06-7.1-2.13-11.26-1.28-4.94-2.58-8.9-3.22-10.33,0,0,0,0,0,0-.19-.37-.31-.57-.35-.57s-.16.2-.35.57h0s0,0,0,0c-.64,1.43-1.94,5.38-3.22,10.33-1.07,4.16-2.23,7.63-2.13,11.26h0c.09.48.21,1.02.39,1.56.59,1.85,1.34,3.23,2.34,3.65.69.29,2.65.44,2.97.44s2.28-.15,2.97-.44c1.01-.42,1.75-1.8,2.34-3.65.17-.54.3-1.08.39-1.56h0ZM35.4,42.07s-.02,0-.03,0c-.1,0-.2,0-.3,0s-.2,0-.3,0c-.01,0-.02,0-.03,0-2.7-.16-4.32-2.32-4.32-5.44,0-2.32.77-4.52,1.63-7.16,1.29-3.96,2.89-6.9,3.02-6.9s1.73,2.94,3.02,6.9c.86,2.64,1.63,4.84,1.63,7.16,0,3.12-1.61,5.28-4.32,5.44Z"/>
  </g>
</svg>
