from django.db import models
from wagtail.snippets.models import register_snippet


@register_snippet
class PathSyncLog(models.Model):
    path = models.CharField(max_length=255, unique=True, db_index=True)
    last_modified_at = models.DateTimeField()
    first_synced_at = models.DateTimeField(auto_now_add=True)
    last_synced_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.path
