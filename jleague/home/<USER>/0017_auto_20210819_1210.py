# Generated by Django 3.1.13 on 2021-08-19 16:10

from django.db import migrations, models
import django.db.models.deletion
import modelcluster.fields
import wagtail.fields


class Migration(migrations.Migration):
    dependencies = [
        ('wagtailimages', '0023_add_choose_permissions'),
        ('wagtailcore', '0060_fix_workflow_unique_constraint'),
        ('data_stadium', '0028_auto_20210819_1210'),
        ('home', '0016_auto_20210819_1137'),
    ]

    operations = [
        migrations.AddField(
            model_name='stadiuminfo',
            name='stations',
            field=wagtail.fields.RichTextField(null=True),
        ),
        migrations.CreateModel(
            name='TeamInfo',
            fields=[
                (
                    'page_ptr',
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to='wagtailcore.page',
                    ),
                ),
                ('address', models.TextField(null=True)),
                ('introduction', wagtail.fields.RichTextField(null=True)),
                ('practice_ground', models.TextField(null=True)),
                (
                    'bg_image',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='bg_image',
                        to='wagtailimages.image',
                    ),
                ),
                (
                    'mascotes',
                    modelcluster.fields.ParentalManyToManyField(
                        blank=True, related_name='mascotes', to='home.PhotoItem'
                    ),
                ),
                (
                    'team',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='team',
                        to='data_stadium.team',
                    ),
                ),
            ],
            options={
                'abstract': False,
            },
            bases=('wagtailcore.page',),
        ),
    ]
