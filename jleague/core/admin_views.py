import csv
from django import forms
from django.contrib import messages
from django.shortcuts import redirect, render
from django.conf import settings
from utils.translation.translation import import_translation
from .models import Translation


class UploadFileForm(forms.Form):
    DATA_TYPE_CHOICES = (
        ("default", "Default"),
        ("club_bio", "Club Bio"),
        ("competition", "Competition"),
        ("country", "Country"),
        ("mascot", "Mascot"),
        ("news_category", "News Category"),
        ("player_name", "Player Name"),
        ("player_name_short", "Player Short Name"),
        ("stadium", "Stadium"),
        ("manager_name", "Manager Name"),
        # ('club_name_short', 'Club Short Name'),
    )

    file = forms.FileField(label="Translation File (CSV format)")
    data_type = forms.ChoiceField(label="Data Type", choices=DATA_TYPE_CHOICES)


def _check_translation_file(request):
    form = UploadFileForm(request.POST, request.FILES)

    # check the form
    if not form.is_valid():
        messages.error(request, "Could not process, please double check the form.")
        return redirect("translation_import")

    # check is uploaded file a csv file
    csv_file = request.FILES.get("file")
    if not csv_file.name.endswith(".csv"):
        messages.error(request, "Unsupported Translation File, It must be a CSV file.")
        return redirect("translation_import")

    return csv_file


def _process_translation_file(request):
    data_type = request.POST.get("data_type")

    translation_file = _check_translation_file(request)
    decoded_file = translation_file.read().decode("utf-8").splitlines()
    rows = csv.DictReader(decoded_file)

    if data_type == "default":
        # process default translation file
        for row in rows:
            if not row["KEY"]:
                continue

            values = {}
            for lang_code, lang_name in settings.LANGUAGES:
                if row.get(lang_name) and row[lang_name].strip():
                    values[lang_code] = row[lang_name].strip()

            Translation.objects.update_or_create(
                key=row["KEY"], defaults={"values": values}
            )
    else:
        # process by data type
        _, error = import_translation(data_type, rows)
        if error:
            messages.error(request, error)
            return redirect("translation_import")

    messages.success(request, "Translation Importing is successfully done.")
    Translation.update_translations_list()
    return redirect("translation_import")


def translation_import(request):
    form = UploadFileForm()

    if request.method == "POST":
        messages.info(request, "Translation Importing is running in the background.")
        return _process_translation_file(request)

    context = {"form": form}

    return render(request, "core/admin_views/translation.html", context)
