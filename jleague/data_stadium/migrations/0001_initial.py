# Generated by Django 3.1.7 on 2021-04-19 22:33

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ('wagtailimages', '0023_add_choose_permissions'),
    ]

    operations = [
        migrations.CreateModel(
            name='GameKind',
            fields=[
                ('id', models.IntegerField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('name_short', models.CharField(max_length=255)),
                ('slug', models.SlugField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='PathSyncLog',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('path', models.Char<PERSON><PERSON>(db_index=True, max_length=255, unique=True)),
                ('last_modified_at', models.DateTimeField()),
                ('first_synced_at', models.DateTimeField(auto_now_add=True)),
                ('last_synced_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Stadium',
            fields=[
                ('id', models.IntegerField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('name_short', models.CharField(max_length=255)),
                ('capacity', models.IntegerField(null=True)),
                ('nationality', models.CharField(max_length=255, null=True)),
                ('lat', models.FloatField(null=True)),
                ('lon', models.FloatField(null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Team',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField(db_index=True)),
                ('team_id', models.IntegerField(db_index=True)),
                ('name', models.CharField(max_length=255)),
                ('name_short', models.CharField(max_length=255)),
                ('slug', models.SlugField()),
                ('federation_id', models.IntegerField(null=True)),
                ('founded', models.IntegerField(null=True)),
                ('website_url', models.URLField(max_length=255, null=True)),
                ('twitter_url', models.URLField(max_length=255, null=True)),
                ('facebook_url', models.URLField(max_length=255, null=True)),
                ('instagram_url', models.URLField(max_length=255, null=True)),
                ('j1_license', models.BooleanField(null=True)),
                ('j2_license', models.BooleanField(null=True)),
                ('j3_license', models.BooleanField(null=True)),
                ('average_age', models.DecimalField(decimal_places=1, max_digits=3, null=True)),
                ('average_height', models.DecimalField(decimal_places=1, max_digits=4, null=True)),
                ('average_weight', models.DecimalField(decimal_places=1, max_digits=4, null=True)),
                ('hometowner_count', models.IntegerField(null=True)),
                ('hometowner_rate', models.DecimalField(decimal_places=1, max_digits=4, null=True)),
                ('domestic_league_count', models.IntegerField(null=True)),
                ('domestic_league_rate', models.DecimalField(decimal_places=1, max_digits=4, null=True)),
                ('country_id', models.CharField(max_length=255, null=True)),
                ('country_name', models.CharField(max_length=255, null=True)),
                ('prefecture', models.CharField(max_length=255, null=True)),
                ('staff', models.JSONField(null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                (
                    'home_stadium',
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_stadium.stadium'),
                ),
            ],
        ),
        migrations.CreateModel(
            name='PlayerPhoto',
            fields=[
                ('player_id', models.IntegerField(primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                (
                    'photo',
                    models.ForeignKey(
                        null=True, on_delete=django.db.models.deletion.SET_NULL, to='wagtailimages.image'
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='Player',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField(db_index=True)),
                ('player_id', models.IntegerField(db_index=True)),
                ('name', models.CharField(max_length=255)),
                ('name_short', models.CharField(max_length=255)),
                ('uniform_no', models.IntegerField(null=True)),
                ('position', models.CharField(max_length=255, null=True)),
                ('class_value', models.IntegerField(null=True)),
                ('is_hg_player', models.BooleanField(null=True)),
                ('is_international', models.BooleanField(null=True)),
                ('is_afc_member', models.BooleanField(null=True)),
                ('is_partner_nations', models.BooleanField(null=True)),
                ('birth_place', models.CharField(max_length=255, null=True)),
                ('height', models.DecimalField(decimal_places=1, max_digits=4, null=True)),
                ('weight', models.DecimalField(decimal_places=1, max_digits=4, null=True)),
                ('birthday', models.DateField(null=True)),
                ('game_count_j1', models.IntegerField(null=True)),
                ('game_count_j2', models.IntegerField(null=True)),
                ('game_count_j3', models.IntegerField(null=True)),
                ('game_count_cup', models.IntegerField(null=True)),
                ('goal_count_j1', models.IntegerField(null=True)),
                ('goal_count_j2', models.IntegerField(null=True)),
                ('goal_count_j3', models.IntegerField(null=True)),
                ('goal_count_cup', models.IntegerField(null=True)),
                ('first_game_date', models.DateField(null=True)),
                ('first_goal_date', models.DateField(null=True)),
                ('nationality_id', models.IntegerField(null=True)),
                ('nationality_name', models.CharField(max_length=255, null=True)),
                ('nationality_name_short', models.CharField(max_length=255, null=True)),
                ('national_team_appearances', models.IntegerField(null=True)),
                ('national_team_goals', models.IntegerField(null=True)),
                ('games_played', models.IntegerField(null=True)),
                ('goals_scored', models.IntegerField(null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                (
                    'player_photo',
                    models.ForeignKey(
                        null=True, on_delete=django.db.models.deletion.SET_NULL, to='data_stadium.playerphoto'
                    ),
                ),
                (
                    'team',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, related_name='players', to='data_stadium.team'
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='GameKindSeasonTeam',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField(db_index=True)),
                ('season_id', models.IntegerField()),
                ('group_id', models.CharField(max_length=255, null=True)),
                ('ordering', models.IntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                (
                    'game_kind',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='season_teams',
                        to='data_stadium.gamekind',
                    ),
                ),
                (
                    'team',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='season_game_kinds',
                        to='data_stadium.team',
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='Game',
            fields=[
                ('id', models.IntegerField(primary_key=True, serialize=False)),
                ('year', models.IntegerField()),
                ('season_id', models.IntegerField()),
                ('match_number', models.IntegerField(null=True)),
                ('game_date', models.DateField(db_index=True, null=True)),
                ('game_full_date', models.DateTimeField(null=True)),
                ('occasion_no', models.IntegerField(null=True)),
                ('round', models.IntegerField(null=True)),
                ('group_id', models.CharField(max_length=255, null=True)),
                ('day_night', models.IntegerField(null=True)),
                ('site', models.CharField(max_length=255, null=True)),
                ('situation_id', models.IntegerField(null=True)),
                ('home_score', models.IntegerField(null=True)),
                ('away_score', models.IntegerField(null=True)),
                ('temperature', models.DecimalField(decimal_places=1, max_digits=5, null=True)),
                ('spectators', models.IntegerField(null=True)),
                ('humidity', models.DecimalField(decimal_places=1, max_digits=4, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                (
                    'away_previous_game',
                    models.OneToOneField(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='next_away_game',
                        to='data_stadium.game',
                    ),
                ),
                (
                    'away_team',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, related_name='away_games', to='data_stadium.team'
                    ),
                ),
                (
                    'game_kind',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, related_name='games', to='data_stadium.gamekind'
                    ),
                ),
                (
                    'home_previous_game',
                    models.OneToOneField(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='next_home_game',
                        to='data_stadium.game',
                    ),
                ),
                (
                    'home_team',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, related_name='home_games', to='data_stadium.team'
                    ),
                ),
                (
                    'stadium',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, related_name='games', to='data_stadium.stadium'
                    ),
                ),
            ],
        ),
        migrations.AddIndex(
            model_name='team',
            index=models.Index(fields=['year', 'team_id'], name='data_stadiu_year_5bbb9d_idx'),
        ),
        migrations.AddConstraint(
            model_name='team',
            constraint=models.UniqueConstraint(fields=('year', 'team_id'), name='team_uniq_year_team'),
        ),
        migrations.AddIndex(
            model_name='player',
            index=models.Index(fields=['year', 'team_id', 'player_id'], name='data_stadiu_year_a853d7_idx'),
        ),
        migrations.AddIndex(
            model_name='player',
            index=models.Index(fields=['team_id', 'class_value'], name='data_stadiu_team_id_0117e1_idx'),
        ),
        migrations.AddConstraint(
            model_name='player',
            constraint=models.UniqueConstraint(
                fields=('year', 'team_id', 'player_id'), name='player_uniq_primary_keys'
            ),
        ),
        migrations.AddIndex(
            model_name='gamekindseasonteam',
            index=models.Index(
                fields=['year', 'game_kind_id', 'season_id', 'ordering'], name='data_stadiu_year_5732f4_idx'
            ),
        ),
        migrations.AddConstraint(
            model_name='gamekindseasonteam',
            constraint=models.UniqueConstraint(
                fields=('year', 'game_kind_id', 'season_id', 'team_id'), name='game_kind_season_team_uniq_primary_keys'
            ),
        ),
    ]
