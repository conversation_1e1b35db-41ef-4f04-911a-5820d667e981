.about-breadcrumb {
  margin-top: 20px;

  .about-breadcrumb-list {
    display: flex;
    flex-direction: row;

    .about-breadcrumb-list__item {
      position: relative;
      padding-right: 25px;

      .about-breadcrumb-list__item__link {
        font-family: var(--font-barlow-condensed);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        color: var(--color-black);
        display: flex;
        height: 25px;
        align-items: center;
      }

      &.about-breadcrumb-list__item--first {
        .about-breadcrumb-list__item__link {
          width: 24px;
          overflow: hidden;

          img {
            height: 100%;
            width: auto;
          }
        }
      }

      &.active {
        &:hover {
          .about-breadcrumb-list__item__link {
            color: var(--color-red);
            cursor: default;
            opacity: 1;
          }
        }

        .about-breadcrumb-list__item__link {
          border-color: var(--color-red);
          color: var(--color-red);
        }
      }

      &:not(:last-child):after {
        content: '>';
        position: absolute;
        right: 10px;
        top: 3px;
        display: block;
      }
    }

    @media screen and (max-width: 767px) {
      .about-breadcrumb-list__item {
        .about-breadcrumb-list__item__link {
          font-size: 20px;
          line-height: 24px;
        }
      }
    }

    @media screen and (max-width: 576px) {
      .about-breadcrumb-list__item {
        .about-breadcrumb-list__item__link {
          font-size: 16px;
          line-height: 20px;
        }
      }
    }
  }
}
