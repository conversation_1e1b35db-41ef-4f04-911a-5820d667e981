@import 'setup/variable';
@import 'setup/mixin';

.cookie-consent {
  position: fixed;
  bottom: 16px;
  left: 16px;
  background-color: $color-earth-1;
  border-radius: 3px;
  padding: 24px;
  z-index: 9999;
  max-width: 540px;
  display: none;
  flex-direction: column;

  .cookie-consent__header,
  .cookie-consent__content {
    font-family: $font-overpass;
    font-style: normal;
    color: $color-black;
  }

  .cookie-consent__header {
    font-weight: 700;
    font-size: 16px;
    line-height: 20px;
    margin-bottom: 8px;
  }

  .cookie-consent__content {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    flex: 1;
    margin-bottom: 16px;

    a {
      text-decoration: underline;
      color: $color-black;

      &:hover {
        opacity: 1;
      }
    }
  }

  .cookie-consent__actions {
    display: flex;
    justify-content: flex-end;
    gap: 19px;

    .jl-button {
      padding: 7px 16px 8px;
      cursor: pointer;

      .jl-button__label {
        font-size: 14px;
        line-height: 17px;
      }

      &.button-settings {
        background: transparent;

        &:hover {
          background: $color-red;
        }
      }

      &.button-accept,
      &.button-settings {
        height: 32px;
      }
    }
  }

  &.cookie-consent--show {
    display: flex;
  }

  @media screen and (max-width: 600px) {
    max-width: none;
    width: 100%;
    bottom: 0;
    left: 0;
    padding: 16px;
  }

  @media screen and (max-width: 400px) {
    .cookie-consent__actions {
      flex-direction: column;
      justify-content: center;
      gap: 8px;
    }
  }
}

body.lang-th {
  .cookie-consent {
    .cookie-consent__header,
    .cookie-consent__content,
    .jl-button .jl-button__label {
      @include font-thai();
    }
  }
}
