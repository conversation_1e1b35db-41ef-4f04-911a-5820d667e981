(function ($) {
  $.extend(true, $.magnificPopup.defaults, {
    // Allow embed youtube (playlist, etc)
    iframe: {
      patterns: {
        youtube: {
          index: 'youtube.com',
          id: function (src) {
            // link pattern
            var mt = src.match(/v=([^&]+)(&(.*))?/);
            if (mt) return mt[1] + '?' + mt[3];

            // embed pattern
            mt = src.match(/embed\/([^\?\/]+)(\?(.*))?/);
            if (mt) return mt[1] + '?' + mt[3];
          },
          src: 'https://www.youtube.com/embed/%id%&autoplay=1&rel=0',
        },
      },
    },

    // When elemened is focused, some mobile browsers in some cases zoom in
    // It looks not nice, so we disable it:
    callbacks: {
      beforeOpen: function () {
        if ($(window).width() < 700) this.st.focus = false;
      },
    },
  });
})(jQuery);
