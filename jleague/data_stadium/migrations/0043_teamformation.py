# Generated by Django 3.1.13 on 2022-03-17 05:08

from django.db import migrations, models
import wagtail.search.index


class Migration(migrations.Migration):
    dependencies = [
        ('data_stadium', '0042_auto_20220311_1313'),
    ]

    operations = [
        migrations.CreateModel(
            name='TeamFormation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('formation_id', models.IntegerField(unique=True)),
                ('name', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['formation_id'],
            },
            bases=(wagtail.search.index.Indexed, models.Model),
        ),
    ]
