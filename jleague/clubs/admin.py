from django.contrib import admin
from wagtail.contrib.modeladmin.options import modeladmin_register
from core.admin import CustomHookModelAdmin
from .models import Club, ClubExtraInfo, ClubCompetition, ClubStaff


class ClubAdmin(CustomHookModelAdmin):
    """Menu: Clubs -> List"""

    model = Club
    menu_icon = "list-ul"
    menu_label = "List"
    menu_order = 1
    list_display = ("name", "slug", "ds_club_id", "id")
    search_fields = ("name", "slug", "ds_club_id", "id")
    hook = "register_clubs_menu_item"


class ClubExtraInfoAdmin(CustomHookModelAdmin):
    """Menu: Clubs -> Extra"""

    model = ClubExtraInfo
    menu_icon = "list-ul"
    menu_label = "Extra"
    menu_order = 1
    list_display = ("club", "four_letters_name", "id")
    search_fields = ("club__id", "club__name", "club__ds_club_id", "four_letters_name")
    hook = "register_clubs_menu_item"


class ClubCompetitionAdmin(CustomHookModelAdmin):
    """Menu: Clubs -> Competition"""

    model = ClubCompetition
    menu_icon = "list-ul"
    menu_label = "Competition"
    menu_order = 1
    list_display = ("club", "competition", "year", "id")
    search_fields = ("club__id", "club__name", "club__ds_club_id")
    list_filter = ("year", "competition")
    hook = "register_clubs_menu_item"


class ClubStaffAdmin(CustomHookModelAdmin):
    """Menu: Clubs -> Staffs"""

    model = ClubStaff
    menu_icon = "list-ul"
    menu_label = "Staffs"
    menu_order = 1
    list_display = ("name", "club_extra_info", "staff_id", "id")
    search_fields = ("name", "staff_id", "id", "club_extra_info__club__name")
    list_filter = ("position",)
    hook = "register_clubs_menu_item"


# wagtail register
modeladmin_register(ClubAdmin)
modeladmin_register(ClubExtraInfoAdmin)
modeladmin_register(ClubCompetitionAdmin)
modeladmin_register(ClubStaffAdmin)

# django register
admin.site.register(Club)
admin.site.register(ClubExtraInfo)
admin.site.register(ClubCompetition)
admin.site.register(ClubStaff)
