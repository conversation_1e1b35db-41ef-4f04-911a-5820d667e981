from django.db.models import Q
from core.constants import LEAGUE_COMPETITIONS
from core.helpers import get_countries
from core.models import Competition
from utils.helpers import get_display_data_year
from match.helper import get_last_matches_result, get_next_match
from players.models import PlayerExtraInfo
from standings.helper import get_standing
from stats.models import ClubStats
from stats.parsers import StatsParser
from .helper import get_club_competition, get_club_name_by_name_slug
from .models import Club, ClubExtraInfo, ClubCompetition
from match.models import Game


class ClubParser(object):
    competition: Competition = None
    club: Club = None
    club_extra_info: ClubExtraInfo = None

    def __init__(self, id=None, slug=None, name_slug=None, year=None):
        self.id = id
        self.slug = slug
        self.name_slug = name_slug
        self.name = get_club_name_by_name_slug(name_slug) if name_slug else None
        self.year = year or get_display_data_year()
        self._get_club()

    def _get_club(self):
        """Get team by slug and year
        *NOTE: as we have duplicate team (different Team.id and Team.year but same Team.team_id)
                so year is important now for get the right information
        """
        filter_by = Q(competition__slug__in=LEAGUE_COMPETITIONS)
        if self.id is not None:
            filter_by &= Q(club_id=self.id)
        elif self.name is not None:
            filter_by &= Q(club__name=self.name)
        elif self.slug is not None:
            filter_by &= Q(club__slug=self.slug)

        if self.year:
            filter_by &= Q(year=self.year)

        club_competition = (
            ClubCompetition.objects.select_related(
                "club", "competition", "club__stadium"
            )
            .filter(filter_by)
            .order_by("-year", "-created_at")
            .first()
        )

        if club_competition:
            self.club = club_competition.club
            self.competition = club_competition.competition
            self.club_extra_info = self.club.extra_info.first()
            self.name_slug = self.club.get_name_slug()

    def next_match(self):
        return get_next_match(self.club.id)

    def last_matches(self, competition_slug: str = None, limit: int = 5):
        return get_last_matches_result(
            club_id=self.club.id, competition_slug=competition_slug, limit=limit
        )

    def wins(self, year=None):
        year = year or get_display_data_year()
        competition = get_club_competition(self.club.slug, year)
        filter_by = (
            Q(year=year)
            & Q(competition__slug=competition["slug"])  # noqa
            & (
                Q(home_team_id=self.club.id) | Q(away_team_id=self.club.id)
            )  # noqa  # noqa
        )
        games = Game.objects.filter(filter_by)

        finished_home_games = 0
        finished_away_games = 0
        win_home_games = 0
        win_away_games = 0
        home_win_rate = 0
        away_win_rate = 0

        for game in games:
            if not game.is_upcoming():
                if game.home_team.id == self.club.id:
                    finished_home_games += 1

                    if game.is_win("home"):
                        win_home_games += 1
                elif game.away_team.id == self.club.id:
                    finished_away_games += 1

                    if game.is_win("away"):
                        win_away_games += 1

        if win_home_games and finished_home_games:
            home_win_rate = round(win_home_games / finished_home_games * 100)
        if win_away_games and finished_away_games:
            away_win_rate = round(win_away_games / finished_away_games * 100)

        return {
            "finished_home_games": finished_home_games,
            "finished_away_games": finished_away_games,
            "win_home_games": win_home_games,
            "win_away_games": win_away_games,
            "home_win_rate": home_win_rate,
            "away_win_rate": away_win_rate,
        }

    def players(self, year: int = None):
        result = {
            "goal_keepers": [],
            "defenders": [],
            "midfielders": [],
            "forwards": [],
            "manager": [],
        }

        if self.club:
            year = year or get_display_data_year()
            countries = get_countries(two_letter_dict=True)
            players = list(
                self.club.club_players.filter(
                    year=year, is_registered=True, jersey_no__isnull=False
                )
            )

            if players and len(players):
                goal_keepers = []
                defenders = []
                midfielders = []
                forwards = []
                formatted_club = self.club.format()
                formatted_competition = self.competition.format()
                player_ids = [p.player_id for p in players]
                extra_infos = list(
                    PlayerExtraInfo.objects.select_related(
                        "player", "avatar_image"
                    ).filter(player_id__in=player_ids)
                )

                for p in players:
                    player = p.player.format(include_club=False)
                    player["jersey_no"] = p.jersey_no
                    player["position"] = p.position
                    player["club"] = formatted_club
                    player["club_competition"] = formatted_competition
                    player["allow_view_profile"] = (
                        formatted_club.get("allow_view_profile") == True
                    )

                    if player.get("nationality_flag"):
                        country = countries.get(player["nationality_flag"].upper())
                        if country:
                            player["country_local_names"] = country.local_names

                    for ei in extra_infos:
                        if ei.player_id != p.player_id:
                            continue

                        player["t_name"] = ei.t_name

                        if (
                            ei.avatar_image
                            and ei.avatar_team_slug
                            and self.club.slug == ei.avatar_team_slug
                        ):
                            player["avatar_image"] = ei.avatar_image
                            break

                    if player["position"] == "GK":
                        goal_keepers.append(player)
                    elif player["position"] == "DF":
                        defenders.append(player)
                    elif player["position"] == "MF":
                        midfielders.append(player)
                    elif player["position"] == "FW":
                        forwards.append(player)

                result["goal_keepers"] = sorted(
                    goal_keepers, key=lambda p: p["jersey_no"]
                )
                result["defenders"] = sorted(defenders, key=lambda p: p["jersey_no"])
                result["midfielders"] = sorted(
                    midfielders, key=lambda p: p["jersey_no"]
                )
                result["forwards"] = sorted(forwards, key=lambda p: p["jersey_no"])

            # get club manager from club staffs
            if self.club_extra_info:
                manager = self.club_extra_info.get_manager()
                if manager:
                    result["manager"] = manager.format()
                    if result["manager"].get("nationality_flag"):
                        country = countries.get(
                            result["manager"]["nationality_flag"].upper()
                        )
                        if country:
                            result["manager"][
                                "country_local_names"
                            ] = country.local_names

        return result

    def _example_statistics(self):
        return {
            "competition": self.competition.format(),
            "club": self.club.format(include_competition=False),
            "games_played": 0,
            "games_won": 0,
            "games_drawn": 0,
            "games_lost": 0,
            "goals": 0,
            "goals_conceded": 0,
            "goal_difference": 0,
            "home_wins": 0,
            "away_wins": 0,
        }

    def statistics(self, year: int = None):
        if not self.club:
            return None

        year = year or get_display_data_year()
        competition = get_club_competition(self.club.slug, year)
        result = None
        filter_by = {
            "competition__slug": competition["slug"],
            "club_id": self.club.id,
            "year": year,
        }
        stats = ClubStats.objects.filter(**filter_by).first()

        if stats:
            win_detail = self.wins(year)
            result = stats.format()
            result["home_wins"] = win_detail["home_win_rate"]
            result["away_wins"] = win_detail["away_win_rate"]
            result["goals"] = result["goals"] or 0
            result["goals_conceded"] = result["goals_conceded"] or 0
            result["goal_difference"] = result["goals"] - result["goals_conceded"]
        else:
            result = self._example_statistics()

        return result

    def player_stats(self, year: int):
        competition = self.competition
        stats = ["goals", "assists", "tackles", "saves", "distance", "sprints"]
        player_stats = []
        for stat in stats:
            players_stat = StatsParser.top_5_players(
                year, competition.slug, stat, [self.club.slug]
            )
            player_stats.append(players_stat)
        return player_stats

    def standings(self, year: int = None):
        if self.competition and self.club:
            return get_standing(self.competition.slug, self.club.slug, year)
        return []

    def format(self):
        data = {}
        club = self.club.format() if self.club else {}
        club["name_slug"] = self.name_slug

        if self.club_extra_info:
            videos = self.club_extra_info.get_videos()
            mascots = self.club_extra_info.get_mascots()

            if videos and len(videos):
                videos = list(map(lambda v: v.video, videos))
                data["latest_video"] = videos[0]
                data["videos"] = videos[1:]

            if mascots and len(mascots):
                data["mascot"] = mascots[0].format()

            club["ticket_link"] = self.club_extra_info.ticket_link
            club["address"] = self.club_extra_info.address
            club["phone_number"] = self.club_extra_info.phone_number
            club["introduction"] = self.club_extra_info.introduction
            club["president"] = self.club_extra_info.president
            club["practice_ground"] = self.club_extra_info.practice_ground
            club["t_name"] = self.club_extra_info.t_name
            club["t_address"] = self.club_extra_info.t_address
            club["t_president"] = self.club_extra_info.t_president
            club["t_practice_ground"] = self.club_extra_info.t_practice_ground

        data["competition"] = self.competition.format() if self.competition else None
        data["club"] = club

        stadium = self.club.stadium.extra_info.first()
        if stadium:
            data["stadium"] = stadium.format(True)
            data["stadium"]["t_name"] = stadium.t_name
            data["stadium"]["t_introduction"] = stadium.t_introduction
            data["stadium"]["t_address"] = stadium.t_address
            data["stadium"]["t_city"] = stadium.t_city
            data["stadium"]["t_stations"] = stadium.t_stations
        return data
