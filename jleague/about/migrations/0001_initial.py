# Generated by Django 3.1.13 on 2022-05-10 03:38

from django.db import migrations, models
import wagtail.search.index


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ('core', '0004_auto_20220510_1239'),
        ('data_stadium', '0048_delete_playerphoto'),
    ]

    operations = [
        migrations.CreateModel(
            name='Broadcaster',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('website', models.URLField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['name'],
            },
            bases=(models.Model, wagtail.search.index.Indexed),
        ),
        migrations.CreateModel(
            name='BroadcastArea',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('country_note', models.TextField(blank=True, null=True)),
                ('competition_note', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('broadcasters', models.ManyToManyField(to='about.Broadcaster')),
                ('competitions', models.ManyToManyField(to='data_stadium.GameKind')),
                ('countries', models.ManyToManyField(to='core.Country')),
            ],
            options={
                'ordering': ['name'],
            },
            bases=(models.Model, wagtail.search.index.Indexed),
        ),
    ]
