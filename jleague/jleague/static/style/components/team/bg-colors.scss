@import 'setup/variable';

.team-bg {
  background-color: $color-white;

  @each $name, $color in $club-colors {
    &.team-bg--#{$name} {
      background-color: $color !important;

      &.team-bg--transparent90 {
        background-color: transparentize($color, 0.9) !important;
      }
    }
  }

  @each $name, $color in $club-secondary-colors {
    &.team-bg--#{$name}.team-bg--secondary {
      background-color: $color !important;
    }
  }
}

.team-bg-gradient {
  background-image: linear-gradient(0deg, $color-white 0%, rgba($color-white, 0.01) 100%);

  &.team-bg-gradient--reverse {
    background-image: linear-gradient(180deg, $color-white 0%, rgba($color-white, 0.01) 100%);
  }

  @each $name, $color in $club-colors {
    &.team-bg-gradient--#{$name} {
      background-image: linear-gradient(0deg, $color 0%, rgba($color, 0.01) 100%);

      &.team-bg-gradient--reverse {
        background-image: linear-gradient(180deg, $color 0%, rgba($color, 0.01) 100%);
      }
    }
  }
}
