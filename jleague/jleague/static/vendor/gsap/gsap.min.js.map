{"version": 3, "file": "gsap.min.js", "sources": ["../src/gsap-core.js", "../src/CSSPlugin.js", "../src/index.js"], "sourcesContent": ["/*!\n * GSAP 3.12.2\n * https://greensock.com\n *\n * @license Copyright 2008-2023, GreenSock. All rights reserved.\n * Subject to the terms at https://greensock.com/standard-license or for\n * Club GreenSock members, the agreement issued with that membership.\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet _config = {\n\t\tautoSleep: 120,\n\t\tforce3D: \"auto\",\n\t\tnullTargetWarn: 1,\n\t\tunits: {lineHeight:\"\"}\n\t},\n\t_defaults = {\n\t\tduration: .5,\n\t\toverwrite: false,\n\t\tdelay: 0\n\t},\n\t_suppressOverwrites,\n\t_reverting, _context,\n\t_bigNum = 1e8,\n\t_tinyNum = 1 / _bigNum,\n\t_2PI = Math.PI * 2,\n\t_HALF_PI = _2PI / 4,\n\t_gsID = 0,\n\t_sqrt = Math.sqrt,\n\t_cos = Math.cos,\n\t_sin = Math.sin,\n\t_isString = value => typeof(value) === \"string\",\n\t_isFunction = value => typeof(value) === \"function\",\n\t_isNumber = value => typeof(value) === \"number\",\n\t_isUndefined = value => typeof(value) === \"undefined\",\n\t_isObject = value => typeof(value) === \"object\",\n\t_isNotFalse = value => value !== false,\n\t_windowExists = () => typeof(window) !== \"undefined\",\n\t_isFuncOrString = value => _isFunction(value) || _isString(value),\n\t_isTypedArray = (typeof ArrayBuffer === \"function\" && ArrayBuffer.isView) || function() {}, // note: IE10 has ArrayBuffer, but NOT ArrayBuffer.isView().\n\t_isArray = Array.isArray,\n\t_strictNumExp = /(?:-?\\.?\\d|\\.)+/gi, //only numbers (including negatives and decimals) but NOT relative values.\n\t_numExp = /[-+=.]*\\d+[.e\\-+]*\\d*[e\\-+]*\\d*/g, //finds any numbers, including ones that start with += or -=, negative numbers, and ones in scientific notation like 1e-8.\n\t_numWithUnitExp = /[-+=.]*\\d+[.e-]*\\d*[a-z%]*/g,\n\t_complexStringNumExp = /[-+=.]*\\d+\\.?\\d*(?:e-|e\\+)?\\d*/gi, //duplicate so that while we're looping through matches from exec(), it doesn't contaminate the lastIndex of _numExp which we use to search for colors too.\n\t_relExp = /[+-]=-?[.\\d]+/,\n\t_delimitedValueExp = /[^,'\"\\[\\]\\s]+/gi, // previously /[#\\-+.]*\\b[a-z\\d\\-=+%.]+/gi but didn't catch special characters.\n\t_unitExp = /^[+\\-=e\\s\\d]*\\d+[.\\d]*([a-z]*|%)\\s*$/i,\n\t_globalTimeline, _win, _coreInitted, _doc,\n\t_globals = {},\n\t_installScope = {},\n\t_coreReady,\n\t_install = scope => (_installScope = _merge(scope, _globals)) && gsap,\n\t_missingPlugin = (property, value) => console.warn(\"Invalid property\", property, \"set to\", value, \"Missing plugin? gsap.registerPlugin()\"),\n\t_warn = (message, suppress) => !suppress && console.warn(message),\n\t_addGlobal = (name, obj) => (name && (_globals[name] = obj) && (_installScope && (_installScope[name] = obj))) || _globals,\n\t_emptyFunc = () => 0,\n\t_startAtRevertConfig = {suppressEvents: true, isStart: true, kill: false},\n\t_revertConfigNoKill = {suppressEvents: true, kill: false},\n\t_revertConfig = {suppressEvents: true},\n\t_reservedProps = {},\n\t_lazyTweens = [],\n\t_lazyLookup = {},\n\t_lastRenderedFrame,\n\t_plugins = {},\n\t_effects = {},\n\t_nextGCFrame = 30,\n\t_harnessPlugins = [],\n\t_callbackNames = \"\",\n\t_harness = targets => {\n\t\tlet target = targets[0],\n\t\t\tharnessPlugin, i;\n\t\t_isObject(target) || _isFunction(target) || (targets = [targets]);\n\t\tif (!(harnessPlugin = (target._gsap || {}).harness)) { // find the first target with a harness. We assume targets passed into an animation will be of similar type, meaning the same kind of harness can be used for them all (performance optimization)\n\t\t\ti = _harnessPlugins.length;\n\t\t\twhile (i-- && !_harnessPlugins[i].targetTest(target)) {\t}\n\t\t\tharnessPlugin = _harnessPlugins[i];\n\t\t}\n\t\ti = targets.length;\n\t\twhile (i--) {\n\t\t\t(targets[i] && (targets[i]._gsap || (targets[i]._gsap = new GSCache(targets[i], harnessPlugin)))) || targets.splice(i, 1);\n\t\t}\n\t\treturn targets;\n\t},\n\t_getCache = target => target._gsap || _harness(toArray(target))[0]._gsap,\n\t_getProperty = (target, property, v) => (v = target[property]) && _isFunction(v) ? target[property]() : (_isUndefined(v) && target.getAttribute && target.getAttribute(property)) || v,\n\t_forEachName = (names, func) => ((names = names.split(\",\")).forEach(func)) || names, //split a comma-delimited list of names into an array, then run a forEach() function and return the split array (this is just a way to consolidate/shorten some code).\n\t_round = value => Math.round(value * 100000) / 100000 || 0,\n\t_roundPrecise = value => Math.round(value * 10000000) / 10000000 || 0, // increased precision mostly for timing values.\n\t_parseRelative = (start, value) => {\n\t\tlet operator = value.charAt(0),\n\t\t\tend = parseFloat(value.substr(2));\n\t\tstart = parseFloat(start);\n\t\treturn operator === \"+\" ? start + end : operator === \"-\" ? start - end : operator === \"*\" ? start * end : start / end;\n\t},\n\t_arrayContainsAny = (toSearch, toFind) => { //searches one array to find matches for any of the items in the toFind array. As soon as one is found, it returns true. It does NOT return all the matches; it's simply a boolean search.\n\t\tlet l = toFind.length,\n\t\t\ti = 0;\n\t\tfor (; toSearch.indexOf(toFind[i]) < 0 && ++i < l;) { }\n\t\treturn (i < l);\n\t},\n\t_lazyRender = () => {\n\t\tlet l = _lazyTweens.length,\n\t\t\ta = _lazyTweens.slice(0),\n\t\t\ti, tween;\n\t\t_lazyLookup = {};\n\t\t_lazyTweens.length = 0;\n\t\tfor (i = 0; i < l; i++) {\n\t\t\ttween = a[i];\n\t\t\ttween && tween._lazy && (tween.render(tween._lazy[0], tween._lazy[1], true)._lazy = 0);\n\t\t}\n\t},\n\t_lazySafeRender = (animation, time, suppressEvents, force) => {\n\t\t_lazyTweens.length && !_reverting && _lazyRender();\n\t\tanimation.render(time, suppressEvents, force || (_reverting && time < 0 && (animation._initted || animation._startAt)));\n\t\t_lazyTweens.length && !_reverting && _lazyRender(); //in case rendering caused any tweens to lazy-init, we should render them because typically when someone calls seek() or time() or progress(), they expect an immediate render.\n\t},\n\t_numericIfPossible = value => {\n\t\tlet n = parseFloat(value);\n\t\treturn (n || n === 0) && (value + \"\").match(_delimitedValueExp).length < 2 ? n : _isString(value) ? value.trim() : value;\n\t},\n\t_passThrough = p => p,\n\t_setDefaults = (obj, defaults) => {\n\t\tfor (let p in defaults) {\n\t\t\t(p in obj) || (obj[p] = defaults[p]);\n\t\t}\n\t\treturn obj;\n\t},\n\t_setKeyframeDefaults = excludeDuration => (obj, defaults) => {\n\t\tfor (let p in defaults) {\n\t\t\t(p in obj) || (p === \"duration\" && excludeDuration) || p === \"ease\" || (obj[p] = defaults[p]);\n\t\t}\n\t},\n\t_merge = (base, toMerge) => {\n\t\tfor (let p in toMerge) {\n\t\t\tbase[p] = toMerge[p];\n\t\t}\n\t\treturn base;\n\t},\n\t_mergeDeep = (base, toMerge) => {\n\t\tfor (let p in toMerge) {\n\t\t\tp !== \"__proto__\" && p !== \"constructor\" && p !== \"prototype\" && (base[p] = _isObject(toMerge[p]) ? _mergeDeep(base[p] || (base[p] = {}), toMerge[p]) : toMerge[p]);\n\t\t}\n\t\treturn base;\n\t},\n\t_copyExcluding = (obj, excluding) => {\n\t\tlet copy = {},\n\t\t\tp;\n\t\tfor (p in obj) {\n\t\t\t(p in excluding) || (copy[p] = obj[p]);\n\t\t}\n\t\treturn copy;\n\t},\n\t_inheritDefaults = vars => {\n\t\tlet parent = vars.parent || _globalTimeline,\n\t\t\tfunc = vars.keyframes ? _setKeyframeDefaults(_isArray(vars.keyframes)) : _setDefaults;\n\t\tif (_isNotFalse(vars.inherit)) {\n\t\t\twhile (parent) {\n\t\t\t\tfunc(vars, parent.vars.defaults);\n\t\t\t\tparent = parent.parent || parent._dp;\n\t\t\t}\n\t\t}\n\t\treturn vars;\n\t},\n\t_arraysMatch = (a1, a2) => {\n\t\tlet i = a1.length,\n\t\t\tmatch = i === a2.length;\n\t\twhile (match && i-- && a1[i] === a2[i]) { }\n\t\treturn i < 0;\n\t},\n\t_addLinkedListItem = (parent, child, firstProp = \"_first\", lastProp = \"_last\", sortBy) => {\n\t\tlet prev = parent[lastProp],\n\t\t\tt;\n\t\tif (sortBy) {\n\t\t\tt = child[sortBy];\n\t\t\twhile (prev && prev[sortBy] > t) {\n\t\t\t\tprev = prev._prev;\n\t\t\t}\n\t\t}\n\t\tif (prev) {\n\t\t\tchild._next = prev._next;\n\t\t\tprev._next = child;\n\t\t} else {\n\t\t\tchild._next = parent[firstProp];\n\t\t\tparent[firstProp] = child;\n\t\t}\n\t\tif (child._next) {\n\t\t\tchild._next._prev = child;\n\t\t} else {\n\t\t\tparent[lastProp] = child;\n\t\t}\n\t\tchild._prev = prev;\n\t\tchild.parent = child._dp = parent;\n\t\treturn child;\n\t},\n\t_removeLinkedListItem = (parent, child, firstProp = \"_first\", lastProp = \"_last\") => {\n\t\tlet prev = child._prev,\n\t\t\tnext = child._next;\n\t\tif (prev) {\n\t\t\tprev._next = next;\n\t\t} else if (parent[firstProp] === child) {\n\t\t\tparent[firstProp] = next;\n\t\t}\n\t\tif (next) {\n\t\t\tnext._prev = prev;\n\t\t} else if (parent[lastProp] === child) {\n\t\t\tparent[lastProp] = prev;\n\t\t}\n\t\tchild._next = child._prev = child.parent = null; // don't delete the _dp just so we can revert if necessary. But parent should be null to indicate the item isn't in a linked list.\n\t},\n\t_removeFromParent = (child, onlyIfParentHasAutoRemove) => {\n\t\tchild.parent && (!onlyIfParentHasAutoRemove || child.parent.autoRemoveChildren) && child.parent.remove && child.parent.remove(child);\n\t\tchild._act = 0;\n\t},\n\t_uncache = (animation, child) => {\n\t\tif (animation && (!child || child._end > animation._dur || child._start < 0)) { // performance optimization: if a child animation is passed in we should only uncache if that child EXTENDS the animation (its end time is beyond the end)\n\t\t\tlet a = animation;\n\t\t\twhile (a) {\n\t\t\t\ta._dirty = 1;\n\t\t\t\ta = a.parent;\n\t\t\t}\n\t\t}\n\t\treturn animation;\n\t},\n\t_recacheAncestors = animation => {\n\t\tlet parent = animation.parent;\n\t\twhile (parent && parent.parent) { //sometimes we must force a re-sort of all children and update the duration/totalDuration of all ancestor timelines immediately in case, for example, in the middle of a render loop, one tween alters another tween's timeScale which shoves its startTime before 0, forcing the parent timeline to shift around and shiftChildren() which could affect that next tween's render (startTime). Doesn't matter for the root timeline though.\n\t\t\tparent._dirty = 1;\n\t\t\tparent.totalDuration();\n\t\t\tparent = parent.parent;\n\t\t}\n\t\treturn animation;\n\t},\n\t_rewindStartAt = (tween, totalTime, suppressEvents, force) => tween._startAt && (_reverting ? tween._startAt.revert(_revertConfigNoKill) : (tween.vars.immediateRender && !tween.vars.autoRevert) || tween._startAt.render(totalTime, true, force)),\n\t_hasNoPausedAncestors = animation => !animation || (animation._ts && _hasNoPausedAncestors(animation.parent)),\n\t_elapsedCycleDuration = animation => animation._repeat ? _animationCycle(animation._tTime, (animation = animation.duration() + animation._rDelay)) * animation : 0,\n\t// feed in the totalTime and cycleDuration and it'll return the cycle (iteration minus 1) and if the playhead is exactly at the very END, it will NOT bump up to the next cycle.\n\t_animationCycle = (tTime, cycleDuration) => {\n\t\tlet whole = Math.floor(tTime /= cycleDuration);\n\t\treturn tTime && (whole === tTime) ? whole - 1 : whole;\n\t},\n\t_parentToChildTotalTime = (parentTime, child) => (parentTime - child._start) * child._ts + (child._ts >= 0 ? 0 : (child._dirty ? child.totalDuration() : child._tDur)),\n\t_setEnd = animation => (animation._end = _roundPrecise(animation._start + ((animation._tDur / Math.abs(animation._ts || animation._rts || _tinyNum)) || 0))),\n\t_alignPlayhead = (animation, totalTime) => { // adjusts the animation's _start and _end according to the provided totalTime (only if the parent's smoothChildTiming is true and the animation isn't paused). It doesn't do any rendering or forcing things back into parent timelines, etc. - that's what totalTime() is for.\n\t\tlet parent = animation._dp;\n\t\tif (parent && parent.smoothChildTiming && animation._ts) {\n\t\t\tanimation._start = _roundPrecise(parent._time - (animation._ts > 0 ? totalTime / animation._ts : ((animation._dirty ? animation.totalDuration() : animation._tDur) - totalTime) / -animation._ts));\n\t\t\t_setEnd(animation);\n\t\t\tparent._dirty || _uncache(parent, animation); //for performance improvement. If the parent's cache is already dirty, it already took care of marking the ancestors as dirty too, so skip the function call here.\n\t\t}\n\t\treturn animation;\n\t},\n\t/*\n\t_totalTimeToTime = (clampedTotalTime, duration, repeat, repeatDelay, yoyo) => {\n\t\tlet cycleDuration = duration + repeatDelay,\n\t\t\ttime = _round(clampedTotalTime % cycleDuration);\n\t\tif (time > duration) {\n\t\t\ttime = duration;\n\t\t}\n\t\treturn (yoyo && (~~(clampedTotalTime / cycleDuration) & 1)) ? duration - time : time;\n\t},\n\t*/\n\t_postAddChecks = (timeline, child) => {\n\t\tlet t;\n\t\tif (child._time || (!child._dur && child._initted) || (child._start < timeline._time && (child._dur || !child.add))) { // in case, for example, the _start is moved on a tween that has already rendered, or if it's being inserted into a timeline BEFORE where the playhead is currently. Imagine it's at its end state, then the startTime is moved WAY later (after the end of this timeline), it should render at its beginning. Special case: if it's a timeline (has .add() method) and no duration, we can skip rendering because the user may be populating it AFTER adding it to a parent timeline (unconventional, but possible, and we wouldn't want it to get removed if the parent's autoRemoveChildren is true).\n\t\t\tt = _parentToChildTotalTime(timeline.rawTime(), child);\n\t\t\tif (!child._dur || _clamp(0, child.totalDuration(), t) - child._tTime > _tinyNum) {\n\t\t\t\tchild.render(t, true);\n\t\t\t}\n\t\t}\n\t\t//if the timeline has already ended but the inserted tween/timeline extends the duration, we should enable this timeline again so that it renders properly. We should also align the playhead with the parent timeline's when appropriate.\n\t\tif (_uncache(timeline, child)._dp && timeline._initted && timeline._time >= timeline._dur && timeline._ts) {\n\t\t\t//in case any of the ancestors had completed but should now be enabled...\n\t\t\tif (timeline._dur < timeline.duration()) {\n\t\t\t\tt = timeline;\n\t\t\t\twhile (t._dp) {\n\t\t\t\t\t(t.rawTime() >= 0) && t.totalTime(t._tTime); //moves the timeline (shifts its startTime) if necessary, and also enables it. If it's currently zero, though, it may not be scheduled to render until later so there's no need to force it to align with the current playhead position. Only move to catch up with the playhead.\n\t\t\t\t\tt = t._dp;\n\t\t\t\t}\n\t\t\t}\n\t\t\ttimeline._zTime = -_tinyNum; // helps ensure that the next render() will be forced (crossingStart = true in render()), even if the duration hasn't changed (we're adding a child which would need to get rendered). Definitely an edge case. Note: we MUST do this AFTER the loop above where the totalTime() might trigger a render() because this _addToTimeline() method gets called from the Animation constructor, BEFORE tweens even record their targets, etc. so we wouldn't want things to get triggered in the wrong order.\n\t\t}\n\t},\n\t_addToTimeline = (timeline, child, position, skipChecks) => {\n\t\tchild.parent && _removeFromParent(child);\n\t\tchild._start = _roundPrecise((_isNumber(position) ? position : position || timeline !== _globalTimeline ? _parsePosition(timeline, position, child) : timeline._time) + child._delay);\n\t\tchild._end = _roundPrecise(child._start + ((child.totalDuration() / Math.abs(child.timeScale())) || 0));\n\t\t_addLinkedListItem(timeline, child, \"_first\", \"_last\", timeline._sort ? \"_start\" : 0);\n\t\t_isFromOrFromStart(child) || (timeline._recent = child);\n\t\tskipChecks || _postAddChecks(timeline, child);\n\t\ttimeline._ts < 0 && _alignPlayhead(timeline, timeline._tTime); // if the timeline is reversed and the new child makes it longer, we may need to adjust the parent's _start (push it back)\n\t\treturn timeline;\n\t},\n\t_scrollTrigger = (animation, trigger) => (_globals.ScrollTrigger || _missingPlugin(\"scrollTrigger\", trigger)) && _globals.ScrollTrigger.create(trigger, animation),\n\t_attemptInitTween = (tween, time, force, suppressEvents, tTime) => {\n\t\t_initTween(tween, time, tTime);\n\t\tif (!tween._initted) {\n\t\t\treturn 1;\n\t\t}\n\t\tif (!force && tween._pt && !_reverting && ((tween._dur && tween.vars.lazy !== false) || (!tween._dur && tween.vars.lazy)) && _lastRenderedFrame !== _ticker.frame) {\n\t\t\t_lazyTweens.push(tween);\n\t\t\ttween._lazy = [tTime, suppressEvents];\n\t\t\treturn 1;\n\t\t}\n\t},\n\t_parentPlayheadIsBeforeStart = ({parent}) => parent && parent._ts && parent._initted && !parent._lock && (parent.rawTime() < 0 || _parentPlayheadIsBeforeStart(parent)), // check parent's _lock because when a timeline repeats/yoyos and does its artificial wrapping, we shouldn't force the ratio back to 0\n\t_isFromOrFromStart = ({data}) => data === \"isFromStart\" || data === \"isStart\",\n\t_renderZeroDurationTween = (tween, totalTime, suppressEvents, force) => {\n\t\tlet prevRatio = tween.ratio,\n\t\t\tratio = totalTime < 0 || (!totalTime && ((!tween._start && _parentPlayheadIsBeforeStart(tween) && !(!tween._initted && _isFromOrFromStart(tween))) || ((tween._ts < 0 || tween._dp._ts < 0) && !_isFromOrFromStart(tween)))) ? 0 : 1, // if the tween or its parent is reversed and the totalTime is 0, we should go to a ratio of 0. Edge case: if a from() or fromTo() stagger tween is placed later in a timeline, the \"startAt\" zero-duration tween could initially render at a time when the parent timeline's playhead is technically BEFORE where this tween is, so make sure that any \"from\" and \"fromTo\" startAt tweens are rendered the first time at a ratio of 1.\n\t\t\trepeatDelay = tween._rDelay,\n\t\t\ttTime = 0,\n\t\t\tpt, iteration, prevIteration;\n\t\tif (repeatDelay && tween._repeat) { // in case there's a zero-duration tween that has a repeat with a repeatDelay\n\t\t\ttTime = _clamp(0, tween._tDur, totalTime);\n\t\t\titeration = _animationCycle(tTime, repeatDelay);\n\t\t\ttween._yoyo && (iteration & 1) && (ratio = 1 - ratio);\n\t\t\tif (iteration !== _animationCycle(tween._tTime, repeatDelay)) { // if iteration changed\n\t\t\t\tprevRatio = 1 - ratio;\n\t\t\t\ttween.vars.repeatRefresh && tween._initted && tween.invalidate();\n\t\t\t}\n\t\t}\n\t\tif (ratio !== prevRatio || _reverting || force || tween._zTime === _tinyNum || (!totalTime && tween._zTime)) {\n\t\t\tif (!tween._initted && _attemptInitTween(tween, totalTime, force, suppressEvents, tTime)) { // if we render the very beginning (time == 0) of a fromTo(), we must force the render (normal tweens wouldn't need to render at a time of 0 when the prevTime was also 0). This is also mandatory to make sure overwriting kicks in immediately.\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tprevIteration = tween._zTime;\n\t\t\ttween._zTime = totalTime || (suppressEvents ? _tinyNum : 0); // when the playhead arrives at EXACTLY time 0 (right on top) of a zero-duration tween, we need to discern if events are suppressed so that when the playhead moves again (next time), it'll trigger the callback. If events are NOT suppressed, obviously the callback would be triggered in this render. Basically, the callback should fire either when the playhead ARRIVES or LEAVES this exact spot, not both. Imagine doing a timeline.seek(0) and there's a callback that sits at 0. Since events are suppressed on that seek() by default, nothing will fire, but when the playhead moves off of that position, the callback should fire. This behavior is what people intuitively expect.\n\t\t\tsuppressEvents || (suppressEvents = totalTime && !prevIteration); // if it was rendered previously at exactly 0 (_zTime) and now the playhead is moving away, DON'T fire callbacks otherwise they'll seem like duplicates.\n\t\t\ttween.ratio = ratio;\n\t\t\ttween._from && (ratio = 1 - ratio);\n\t\t\ttween._time = 0;\n\t\t\ttween._tTime = tTime;\n\t\t\tpt = tween._pt;\n\t\t\twhile (pt) {\n\t\t\t\tpt.r(ratio, pt.d);\n\t\t\t\tpt = pt._next;\n\t\t\t}\n\t\t\ttotalTime < 0 && _rewindStartAt(tween, totalTime, suppressEvents, true);\n\t\t\ttween._onUpdate && !suppressEvents && _callback(tween, \"onUpdate\");\n\t\t\ttTime && tween._repeat && !suppressEvents && tween.parent && _callback(tween, \"onRepeat\");\n\t\t\tif ((totalTime >= tween._tDur || totalTime < 0) && tween.ratio === ratio) {\n\t\t\t\tratio && _removeFromParent(tween, 1);\n\t\t\t\tif (!suppressEvents && !_reverting) {\n\t\t\t\t\t_callback(tween, (ratio ? \"onComplete\" : \"onReverseComplete\"), true);\n\t\t\t\t\ttween._prom && tween._prom();\n\t\t\t\t}\n\t\t\t}\n\t\t} else if (!tween._zTime) {\n\t\t\ttween._zTime = totalTime;\n\t\t}\n\t},\n\t_findNextPauseTween = (animation, prevTime, time) => {\n\t\tlet child;\n\t\tif (time > prevTime) {\n\t\t\tchild = animation._first;\n\t\t\twhile (child && child._start <= time) {\n\t\t\t\tif (child.data === \"isPause\" && child._start > prevTime) {\n\t\t\t\t\treturn child;\n\t\t\t\t}\n\t\t\t\tchild = child._next;\n\t\t\t}\n\t\t} else {\n\t\t\tchild = animation._last;\n\t\t\twhile (child && child._start >= time) {\n\t\t\t\tif (child.data === \"isPause\" && child._start < prevTime) {\n\t\t\t\t\treturn child;\n\t\t\t\t}\n\t\t\t\tchild = child._prev;\n\t\t\t}\n\t\t}\n\t},\n\t_setDuration = (animation, duration, skipUncache, leavePlayhead) => {\n\t\tlet repeat = animation._repeat,\n\t\t\tdur = _roundPrecise(duration) || 0,\n\t\t\ttotalProgress = animation._tTime / animation._tDur;\n\t\ttotalProgress && !leavePlayhead && (animation._time *= dur / animation._dur);\n\t\tanimation._dur = dur;\n\t\tanimation._tDur = !repeat ? dur : repeat < 0 ? 1e10 : _roundPrecise(dur * (repeat + 1) + (animation._rDelay * repeat));\n\t\ttotalProgress > 0 && !leavePlayhead && _alignPlayhead(animation, (animation._tTime = animation._tDur * totalProgress));\n\t\tanimation.parent && _setEnd(animation);\n\t\tskipUncache || _uncache(animation.parent, animation);\n\t\treturn animation;\n\t},\n\t_onUpdateTotalDuration = animation => (animation instanceof Timeline) ? _uncache(animation) : _setDuration(animation, animation._dur),\n\t_zeroPosition = {_start:0, endTime:_emptyFunc, totalDuration:_emptyFunc},\n\t_parsePosition = (animation, position, percentAnimation) => {\n\t\tlet labels = animation.labels,\n\t\t\trecent = animation._recent || _zeroPosition,\n\t\t\tclippedDuration = animation.duration() >= _bigNum ? recent.endTime(false) : animation._dur, //in case there's a child that infinitely repeats, users almost never intend for the insertion point of a new child to be based on a SUPER long value like that so we clip it and assume the most recently-added child's endTime should be used instead.\n\t\t\ti, offset, isPercent;\n\t\tif (_isString(position) && (isNaN(position) || (position in labels))) { //if the string is a number like \"1\", check to see if there's a label with that name, otherwise interpret it as a number (absolute value).\n\t\t\toffset = position.charAt(0);\n\t\t\tisPercent = position.substr(-1) === \"%\";\n\t\t\ti = position.indexOf(\"=\");\n\t\t\tif (offset === \"<\" || offset === \">\") {\n\t\t\t\ti >= 0 && (position = position.replace(/=/, \"\"));\n\t\t\t\treturn (offset === \"<\" ? recent._start : recent.endTime(recent._repeat >= 0)) + (parseFloat(position.substr(1)) || 0) * (isPercent ? (i < 0 ? recent : percentAnimation).totalDuration() / 100 : 1);\n\t\t\t}\n\t\t\tif (i < 0) {\n\t\t\t\t(position in labels) || (labels[position] = clippedDuration);\n\t\t\t\treturn labels[position];\n\t\t\t}\n\t\t\toffset = parseFloat(position.charAt(i-1) + position.substr(i+1));\n\t\t\tif (isPercent && percentAnimation) {\n\t\t\t\toffset = offset / 100 * (_isArray(percentAnimation) ? percentAnimation[0] : percentAnimation).totalDuration();\n\t\t\t}\n\t\t\treturn (i > 1) ? _parsePosition(animation, position.substr(0, i-1), percentAnimation) + offset : clippedDuration + offset;\n\t\t}\n\t\treturn (position == null) ? clippedDuration : +position;\n\t},\n\t_createTweenType = (type, params, timeline) => {\n\t\tlet isLegacy = _isNumber(params[1]),\n\t\t\tvarsIndex = (isLegacy ? 2 : 1) + (type < 2 ? 0 : 1),\n\t\t\tvars = params[varsIndex],\n\t\t\tirVars, parent;\n\t\tisLegacy && (vars.duration = params[1]);\n\t\tvars.parent = timeline;\n\t\tif (type) {\n\t\t\tirVars = vars;\n\t\t\tparent = timeline;\n\t\t\twhile (parent && !(\"immediateRender\" in irVars)) { // inheritance hasn't happened yet, but someone may have set a default in an ancestor timeline. We could do vars.immediateRender = _isNotFalse(_inheritDefaults(vars).immediateRender) but that'd exact a slight performance penalty because _inheritDefaults() also runs in the Tween constructor. We're paying a small kb price here to gain speed.\n\t\t\t\tirVars = parent.vars.defaults || {};\n\t\t\t\tparent = _isNotFalse(parent.vars.inherit) && parent.parent;\n\t\t\t}\n\t\t\tvars.immediateRender = _isNotFalse(irVars.immediateRender);\n\t\t\ttype < 2 ? (vars.runBackwards = 1) : (vars.startAt = params[varsIndex - 1]); // \"from\" vars\n\t\t}\n\t\treturn new Tween(params[0], vars, params[varsIndex + 1]);\n\t},\n\t_conditionalReturn = (value, func) => value || value === 0 ? func(value) : func,\n\t_clamp = (min, max, value) => value < min ? min : value > max ? max : value,\n\tgetUnit = (value, v) => !_isString(value) || !(v = _unitExp.exec(value)) ? \"\" : v[1], // note: protect against padded numbers as strings, like \"100.100\". That shouldn't return \"00\" as the unit. If it's numeric, return no unit.\n\tclamp = (min, max, value) => _conditionalReturn(value, v => _clamp(min, max, v)),\n\t_slice = [].slice,\n\t_isArrayLike = (value, nonEmpty) => value && (_isObject(value) && \"length\" in value && ((!nonEmpty && !value.length) || ((value.length - 1) in value && _isObject(value[0]))) && !value.nodeType && value !== _win),\n\t_flatten = (ar, leaveStrings, accumulator = []) => ar.forEach(value => (_isString(value) && !leaveStrings) || _isArrayLike(value, 1) ? accumulator.push(...toArray(value)) : accumulator.push(value)) || accumulator,\n\t//takes any value and returns an array. If it's a string (and leaveStrings isn't true), it'll use document.querySelectorAll() and convert that to an array. It'll also accept iterables like jQuery objects.\n\ttoArray = (value, scope, leaveStrings) => _context && !scope && _context.selector ? _context.selector(value) : _isString(value) && !leaveStrings && (_coreInitted || !_wake()) ? _slice.call((scope || _doc).querySelectorAll(value), 0) : _isArray(value) ? _flatten(value, leaveStrings) : _isArrayLike(value) ? _slice.call(value, 0) : value ? [value] : [],\n\tselector = value => {\n\t\tvalue = toArray(value)[0] || _warn(\"Invalid scope\") || {};\n\t\treturn v => {\n\t\t\tlet el = value.current || value.nativeElement || value;\n\t\t\treturn toArray(v, el.querySelectorAll ? el : el === value ? _warn(\"Invalid scope\") || _doc.createElement(\"div\") : value);\n\t\t};\n\t},\n\tshuffle = a => a.sort(() => .5 - Math.random()), // alternative that's a bit faster and more reliably diverse but bigger:   for (let j, v, i = a.length; i; j = Math.floor(Math.random() * i), v = a[--i], a[i] = a[j], a[j] = v); return a;\n\t//for distributing values across an array. Can accept a number, a function or (most commonly) a function which can contain the following properties: {base, amount, from, ease, grid, axis, length, each}. Returns a function that expects the following parameters: index, target, array. Recognizes the following\n\tdistribute = v => {\n\t\tif (_isFunction(v)) {\n\t\t\treturn v;\n\t\t}\n\t\tlet vars = _isObject(v) ? v : {each:v}, //n:1 is just to indicate v was a number; we leverage that later to set v according to the length we get. If a number is passed in, we treat it like the old stagger value where 0.1, for example, would mean that things would be distributed with 0.1 between each element in the array rather than a total \"amount\" that's chunked out among them all.\n\t\t\tease = _parseEase(vars.ease),\n\t\t\tfrom = vars.from || 0,\n\t\t\tbase = parseFloat(vars.base) || 0,\n\t\t\tcache = {},\n\t\t\tisDecimal = (from > 0 && from < 1),\n\t\t\tratios = isNaN(from) || isDecimal,\n\t\t\taxis = vars.axis,\n\t\t\tratioX = from,\n\t\t\tratioY = from;\n\t\tif (_isString(from)) {\n\t\t\tratioX = ratioY = {center:.5, edges:.5, end:1}[from] || 0;\n\t\t} else if (!isDecimal && ratios) {\n\t\t\tratioX = from[0];\n\t\t\tratioY = from[1];\n\t\t}\n\t\treturn (i, target, a) => {\n\t\t\tlet l = (a || vars).length,\n\t\t\t\tdistances = cache[l],\n\t\t\t\toriginX, originY, x, y, d, j, max, min, wrapAt;\n\t\t\tif (!distances) {\n\t\t\t\twrapAt = (vars.grid === \"auto\") ? 0 : (vars.grid || [1, _bigNum])[1];\n\t\t\t\tif (!wrapAt) {\n\t\t\t\t\tmax = -_bigNum;\n\t\t\t\t\twhile (max < (max = a[wrapAt++].getBoundingClientRect().left) && wrapAt < l) { }\n\t\t\t\t\twrapAt--;\n\t\t\t\t}\n\t\t\t\tdistances = cache[l] = [];\n\t\t\t\toriginX = ratios ? (Math.min(wrapAt, l) * ratioX) - .5 : from % wrapAt;\n\t\t\t\toriginY = wrapAt === _bigNum ? 0 : ratios ? l * ratioY / wrapAt - .5 : (from / wrapAt) | 0;\n\t\t\t\tmax = 0;\n\t\t\t\tmin = _bigNum;\n\t\t\t\tfor (j = 0; j < l; j++) {\n\t\t\t\t\tx = (j % wrapAt) - originX;\n\t\t\t\t\ty = originY - ((j / wrapAt) | 0);\n\t\t\t\t\tdistances[j] = d = !axis ? _sqrt(x * x + y * y) : Math.abs((axis === \"y\") ? y : x);\n\t\t\t\t\t(d > max) && (max = d);\n\t\t\t\t\t(d < min) && (min = d);\n\t\t\t\t}\n\t\t\t\t(from === \"random\") && shuffle(distances);\n\t\t\t\tdistances.max = max - min;\n\t\t\t\tdistances.min = min;\n\t\t\t\tdistances.v = l = (parseFloat(vars.amount) || (parseFloat(vars.each) * (wrapAt > l ? l - 1 : !axis ? Math.max(wrapAt, l / wrapAt) : axis === \"y\" ? l / wrapAt : wrapAt)) || 0) * (from === \"edges\" ? -1 : 1);\n\t\t\t\tdistances.b = (l < 0) ? base - l : base;\n\t\t\t\tdistances.u = getUnit(vars.amount || vars.each) || 0; //unit\n\t\t\t\tease = (ease && l < 0) ? _invertEase(ease) : ease;\n\t\t\t}\n\t\t\tl = ((distances[i] - distances.min) / distances.max) || 0;\n\t\t\treturn _roundPrecise(distances.b + (ease ? ease(l) : l) * distances.v) + distances.u; //round in order to work around floating point errors\n\t\t};\n\t},\n\t_roundModifier = v => { //pass in 0.1 get a function that'll round to the nearest tenth, or 5 to round to the closest 5, or 0.001 to the closest 1000th, etc.\n\t\tlet p = Math.pow(10, ((v + \"\").split(\".\")[1] || \"\").length); //to avoid floating point math errors (like 24 * 0.1 == 2.4000000000000004), we chop off at a specific number of decimal places (much faster than toFixed())\n\t\treturn raw => {\n\t\t\tlet n = _roundPrecise(Math.round(parseFloat(raw) / v) * v * p);\n\t\t\treturn (n - n % 1) / p + (_isNumber(raw) ? 0 : getUnit(raw)); // n - n % 1 replaces Math.floor() in order to handle negative values properly. For example, Math.floor(-150.00000000000003) is 151!\n\t\t};\n\t},\n\tsnap = (snapTo, value) => {\n\t\tlet isArray = _isArray(snapTo),\n\t\t\tradius, is2D;\n\t\tif (!isArray && _isObject(snapTo)) {\n\t\t\tradius = isArray = snapTo.radius || _bigNum;\n\t\t\tif (snapTo.values) {\n\t\t\t\tsnapTo = toArray(snapTo.values);\n\t\t\t\tif ((is2D = !_isNumber(snapTo[0]))) {\n\t\t\t\t\tradius *= radius; //performance optimization so we don't have to Math.sqrt() in the loop.\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tsnapTo = _roundModifier(snapTo.increment);\n\t\t\t}\n\t\t}\n\t\treturn _conditionalReturn(value, !isArray ? _roundModifier(snapTo) : _isFunction(snapTo) ? raw => {is2D = snapTo(raw); return Math.abs(is2D - raw) <= radius ? is2D : raw; } : raw => {\n\t\t\tlet x = parseFloat(is2D ? raw.x : raw),\n\t\t\t\ty = parseFloat(is2D ? raw.y : 0),\n\t\t\t\tmin = _bigNum,\n\t\t\t\tclosest = 0,\n\t\t\t\ti = snapTo.length,\n\t\t\t\tdx, dy;\n\t\t\twhile (i--) {\n\t\t\t\tif (is2D) {\n\t\t\t\t\tdx = snapTo[i].x - x;\n\t\t\t\t\tdy = snapTo[i].y - y;\n\t\t\t\t\tdx = dx * dx + dy * dy;\n\t\t\t\t} else {\n\t\t\t\t\tdx = Math.abs(snapTo[i] - x);\n\t\t\t\t}\n\t\t\t\tif (dx < min) {\n\t\t\t\t\tmin = dx;\n\t\t\t\t\tclosest = i;\n\t\t\t\t}\n\t\t\t}\n\t\t\tclosest = (!radius || min <= radius) ? snapTo[closest] : raw;\n\t\t\treturn (is2D || closest === raw || _isNumber(raw)) ? closest : closest + getUnit(raw);\n\t\t});\n\t},\n\trandom = (min, max, roundingIncrement, returnFunction) => _conditionalReturn(_isArray(min) ? !max : roundingIncrement === true ? !!(roundingIncrement = 0) : !returnFunction, () => _isArray(min) ? min[~~(Math.random() * min.length)] : (roundingIncrement = roundingIncrement || 1e-5) && (returnFunction = roundingIncrement < 1 ? 10 ** ((roundingIncrement + \"\").length - 2) : 1) && (Math.floor(Math.round((min - roundingIncrement / 2 + Math.random() * (max - min + roundingIncrement * .99)) / roundingIncrement) * roundingIncrement * returnFunction) / returnFunction)),\n\tpipe = (...functions) => value => functions.reduce((v, f) => f(v), value),\n\tunitize = (func, unit) => value => func(parseFloat(value)) + (unit || getUnit(value)),\n\tnormalize = (min, max, value) => mapRange(min, max, 0, 1, value),\n\t_wrapArray = (a, wrapper, value) => _conditionalReturn(value, index => a[~~wrapper(index)]),\n\twrap = function(min, max, value) { // NOTE: wrap() CANNOT be an arrow function! A very odd compiling bug causes problems (unrelated to GSAP).\n\t\tlet range = max - min;\n\t\treturn _isArray(min) ? _wrapArray(min, wrap(0, min.length), max) : _conditionalReturn(value, value => ((range + (value - min) % range) % range) + min);\n\t},\n\twrapYoyo = (min, max, value) => {\n\t\tlet range = max - min,\n\t\t\ttotal = range * 2;\n\t\treturn _isArray(min) ? _wrapArray(min, wrapYoyo(0, min.length - 1), max) : _conditionalReturn(value, value => {\n\t\t\tvalue = (total + (value - min) % total) % total || 0;\n\t\t\treturn min + ((value > range) ? (total - value) : value);\n\t\t});\n\t},\n\t_replaceRandom = value => { //replaces all occurrences of random(...) in a string with the calculated random value. can be a range like random(-100, 100, 5) or an array like random([0, 100, 500])\n\t\tlet prev = 0,\n\t\t\ts = \"\",\n\t\t\ti, nums, end, isArray;\n\t\twhile (~(i = value.indexOf(\"random(\", prev))) {\n\t\t\tend = value.indexOf(\")\", i);\n\t\t\tisArray = value.charAt(i + 7) === \"[\";\n\t\t\tnums = value.substr(i + 7, end - i - 7).match(isArray ? _delimitedValueExp : _strictNumExp);\n\t\t\ts += value.substr(prev, i - prev) + random(isArray ? nums : +nums[0], isArray ? 0 : +nums[1], +nums[2] || 1e-5);\n\t\t\tprev = end + 1;\n\t\t}\n\t\treturn s + value.substr(prev, value.length - prev);\n\t},\n\tmapRange = (inMin, inMax, outMin, outMax, value) => {\n\t\tlet inRange = inMax - inMin,\n\t\t\toutRange = outMax - outMin;\n\t\treturn _conditionalReturn(value, value => outMin + ((((value - inMin) / inRange) * outRange) || 0));\n\t},\n\tinterpolate = (start, end, progress, mutate) => {\n\t\tlet func = isNaN(start + end) ? 0 : p => (1 - p) * start + p * end;\n\t\tif (!func) {\n\t\t\tlet isString = _isString(start),\n\t\t\t\tmaster = {},\n\t\t\t\tp, i, interpolators, l, il;\n\t\t\tprogress === true && (mutate = 1) && (progress = null);\n\t\t\tif (isString) {\n\t\t\t\tstart = {p: start};\n\t\t\t\tend = {p: end};\n\n\t\t\t} else if (_isArray(start) && !_isArray(end)) {\n\t\t\t\tinterpolators = [];\n\t\t\t\tl = start.length;\n\t\t\t\til = l - 2;\n\t\t\t\tfor (i = 1; i < l; i++) {\n\t\t\t\t\tinterpolators.push(interpolate(start[i-1], start[i])); //build the interpolators up front as a performance optimization so that when the function is called many times, it can just reuse them.\n\t\t\t\t}\n\t\t\t\tl--;\n\t\t\t\tfunc = p => {\n\t\t\t\t\tp *= l;\n\t\t\t\t\tlet i = Math.min(il, ~~p);\n\t\t\t\t\treturn interpolators[i](p - i);\n\t\t\t\t};\n\t\t\t\tprogress = end;\n\t\t\t} else if (!mutate) {\n\t\t\t\tstart = _merge(_isArray(start) ? [] : {}, start);\n\t\t\t}\n\t\t\tif (!interpolators) {\n\t\t\t\tfor (p in end) {\n\t\t\t\t\t_addPropTween.call(master, start, p, \"get\", end[p]);\n\t\t\t\t}\n\t\t\t\tfunc = p => _renderPropTweens(p, master) || (isString ? start.p : start);\n\t\t\t}\n\t\t}\n\t\treturn _conditionalReturn(progress, func);\n\t},\n\t_getLabelInDirection = (timeline, fromTime, backward) => { //used for nextLabel() and previousLabel()\n\t\tlet labels = timeline.labels,\n\t\t\tmin = _bigNum,\n\t\t\tp, distance, label;\n\t\tfor (p in labels) {\n\t\t\tdistance = labels[p] - fromTime;\n\t\t\tif ((distance < 0) === !!backward && distance && min > (distance = Math.abs(distance))) {\n\t\t\t\tlabel = p;\n\t\t\t\tmin = distance;\n\t\t\t}\n\t\t}\n\t\treturn label;\n\t},\n\t_callback = (animation, type, executeLazyFirst) => {\n\t\tlet v = animation.vars,\n\t\t\tcallback = v[type],\n\t\t\tprevContext = _context,\n\t\t\tcontext = animation._ctx,\n\t\t\tparams, scope, result;\n\t\tif (!callback) {\n\t\t\treturn;\n\t\t}\n\t\tparams = v[type + \"Params\"];\n\t\tscope = v.callbackScope || animation;\n\t\texecuteLazyFirst && _lazyTweens.length && _lazyRender(); //in case rendering caused any tweens to lazy-init, we should render them because typically when a timeline finishes, users expect things to have rendered fully. Imagine an onUpdate on a timeline that reports/checks tweened values.\n\t\tcontext && (_context = context);\n\t\tresult = params ? callback.apply(scope, params) : callback.call(scope);\n\t\t_context = prevContext;\n\t\treturn result;\n\t},\n\t_interrupt = animation => {\n\t\t_removeFromParent(animation);\n\t\tanimation.scrollTrigger && animation.scrollTrigger.kill(!!_reverting);\n\t\tanimation.progress() < 1 && _callback(animation, \"onInterrupt\");\n\t\treturn animation;\n\t},\n\t_quickTween,\n\t_registerPluginQueue = [],\n\t_createPlugin = config => {\n\t\tif (_windowExists() && config) { // edge case: some build tools may pass in a null/undefined value\n\t\t\tconfig = !config.name && config.default || config; //UMD packaging wraps things oddly, so for example MotionPathHelper becomes {MotionPathHelper:MotionPathHelper, default:MotionPathHelper}.\n\t\t\tlet name = config.name,\n\t\t\t\tisFunc = _isFunction(config),\n\t\t\t\tPlugin = (name && !isFunc && config.init) ? function () {\n\t\t\t\t\tthis._props = [];\n\t\t\t\t} : config, //in case someone passes in an object that's not a plugin, like CustomEase\n\t\t\t\tinstanceDefaults = {init: _emptyFunc, render: _renderPropTweens, add: _addPropTween, kill: _killPropTweensOf, modifier: _addPluginModifier, rawVars: 0},\n\t\t\t\tstatics = {targetTest: 0, get: 0, getSetter: _getSetter, aliases: {}, register: 0};\n\t\t\t_wake();\n\t\t\tif (config !== Plugin) {\n\t\t\t\tif (_plugins[name]) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t_setDefaults(Plugin, _setDefaults(_copyExcluding(config, instanceDefaults), statics)); //static methods\n\t\t\t\t_merge(Plugin.prototype, _merge(instanceDefaults, _copyExcluding(config, statics))); //instance methods\n\t\t\t\t_plugins[(Plugin.prop = name)] = Plugin;\n\t\t\t\tif (config.targetTest) {\n\t\t\t\t\t_harnessPlugins.push(Plugin);\n\t\t\t\t\t_reservedProps[name] = 1;\n\t\t\t\t}\n\t\t\t\tname = (name === \"css\" ? \"CSS\" : name.charAt(0).toUpperCase() + name.substr(1)) + \"Plugin\"; //for the global name. \"motionPath\" should become MotionPathPlugin\n\t\t\t}\n\t\t\t_addGlobal(name, Plugin);\n\t\t\tconfig.register && config.register(gsap, Plugin, PropTween);\n\t\t} else {\n\t\t\tconfig && _registerPluginQueue.push(config);\n\t\t}\n\t},\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/*\n * --------------------------------------------------------------------------------------\n * COLORS\n * --------------------------------------------------------------------------------------\n */\n\n\t_255 = 255,\n\t_colorLookup = {\n\t\taqua:[0,_255,_255],\n\t\tlime:[0,_255,0],\n\t\tsilver:[192,192,192],\n\t\tblack:[0,0,0],\n\t\tmaroon:[128,0,0],\n\t\tteal:[0,128,128],\n\t\tblue:[0,0,_255],\n\t\tnavy:[0,0,128],\n\t\twhite:[_255,_255,_255],\n\t\tolive:[128,128,0],\n\t\tyellow:[_255,_255,0],\n\t\torange:[_255,165,0],\n\t\tgray:[128,128,128],\n\t\tpurple:[128,0,128],\n\t\tgreen:[0,128,0],\n\t\tred:[_255,0,0],\n\t\tpink:[_255,192,203],\n\t\tcyan:[0,_255,_255],\n\t\ttransparent:[_255,_255,_255,0]\n\t},\n\t// possible future idea to replace the hard-coded color name values - put this in the ticker.wake() where we set the _doc:\n\t// let ctx = _doc.createElement(\"canvas\").getContext(\"2d\");\n\t// _forEachName(\"aqua,lime,silver,black,maroon,teal,blue,navy,white,olive,yellow,orange,gray,purple,green,red,pink,cyan\", color => {ctx.fillStyle = color; _colorLookup[color] = splitColor(ctx.fillStyle)});\n\t_hue = (h, m1, m2) => {\n\t\th += h < 0 ? 1 : h > 1 ? -1 : 0;\n\t\treturn ((((h * 6 < 1) ? m1 + (m2 - m1) * h * 6 : h < .5 ? m2 : (h * 3 < 2) ? m1 + (m2 - m1) * (2 / 3 - h) * 6 : m1) * _255) + .5) | 0;\n\t},\n\tsplitColor = (v, toHSL, forceAlpha) => {\n\t\tlet a = !v ? _colorLookup.black : _isNumber(v) ? [v >> 16, (v >> 8) & _255, v & _255] : 0,\n\t\t\tr, g, b, h, s, l, max, min, d, wasHSL;\n\t\tif (!a) {\n\t\t\tif (v.substr(-1) === \",\") { //sometimes a trailing comma is included and we should chop it off (typically from a comma-delimited list of values like a textShadow:\"2px 2px 2px blue, 5px 5px 5px rgb(255,0,0)\" - in this example \"blue,\" has a trailing comma. We could strip it out inside parseComplex() but we'd need to do it to the beginning and ending values plus it wouldn't provide protection from other potential scenarios like if the user passes in a similar value.\n\t\t\t\tv = v.substr(0, v.length - 1);\n\t\t\t}\n\t\t\tif (_colorLookup[v]) {\n\t\t\t\ta = _colorLookup[v];\n\t\t\t} else if (v.charAt(0) === \"#\") {\n\t\t\t\tif (v.length < 6) { //for shorthand like #9F0 or #9F0F (could have alpha)\n\t\t\t\t\tr = v.charAt(1);\n\t\t\t\t\tg = v.charAt(2);\n\t\t\t\t\tb = v.charAt(3);\n\t\t\t\t\tv = \"#\" + r + r + g + g + b + b + (v.length === 5 ? v.charAt(4) + v.charAt(4) : \"\");\n\t\t\t\t}\n\t\t\t\tif (v.length === 9) { // hex with alpha, like #fd5e53ff\n\t\t\t\t\ta = parseInt(v.substr(1, 6), 16);\n\t\t\t\t\treturn [a >> 16, (a >> 8) & _255, a & _255, parseInt(v.substr(7), 16) / 255];\n\t\t\t\t}\n\t\t\t\tv = parseInt(v.substr(1), 16);\n\t\t\t\ta = [v >> 16, (v >> 8) & _255, v & _255];\n\t\t\t} else if (v.substr(0, 3) === \"hsl\") {\n\t\t\t\ta = wasHSL = v.match(_strictNumExp);\n\t\t\t\tif (!toHSL) {\n\t\t\t\t\th = (+a[0] % 360) / 360;\n\t\t\t\t\ts = +a[1] / 100;\n\t\t\t\t\tl = +a[2] / 100;\n\t\t\t\t\tg = (l <= .5) ? l * (s + 1) : l + s - l * s;\n\t\t\t\t\tr = l * 2 - g;\n\t\t\t\t\ta.length > 3 && (a[3] *= 1); //cast as number\n\t\t\t\t\ta[0] = _hue(h + 1 / 3, r, g);\n\t\t\t\t\ta[1] = _hue(h, r, g);\n\t\t\t\t\ta[2] = _hue(h - 1 / 3, r, g);\n\t\t\t\t} else if (~v.indexOf(\"=\")) { //if relative values are found, just return the raw strings with the relative prefixes in place.\n\t\t\t\t\ta = v.match(_numExp);\n\t\t\t\t\tforceAlpha && a.length < 4 && (a[3] = 1);\n\t\t\t\t\treturn a;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\ta = v.match(_strictNumExp) || _colorLookup.transparent;\n\t\t\t}\n\t\t\ta = a.map(Number);\n\t\t}\n\t\tif (toHSL && !wasHSL) {\n\t\t\tr = a[0] / _255;\n\t\t\tg = a[1] / _255;\n\t\t\tb = a[2] / _255;\n\t\t\tmax = Math.max(r, g, b);\n\t\t\tmin = Math.min(r, g, b);\n\t\t\tl = (max + min) / 2;\n\t\t\tif (max === min) {\n\t\t\t\th = s = 0;\n\t\t\t} else {\n\t\t\t\td = max - min;\n\t\t\t\ts = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n\t\t\t\th = max === r ? (g - b) / d + (g < b ? 6 : 0) : max === g ? (b - r) / d + 2 : (r - g) / d + 4;\n\t\t\t\th *= 60;\n\t\t\t}\n\t\t\ta[0] = ~~(h + .5);\n\t\t\ta[1] = ~~(s * 100 + .5);\n\t\t\ta[2] = ~~(l * 100 + .5);\n\t\t}\n\t\tforceAlpha && a.length < 4 && (a[3] = 1);\n\t\treturn a;\n\t},\n\t_colorOrderData = v => { // strips out the colors from the string, finds all the numeric slots (with units) and returns an array of those. The Array also has a \"c\" property which is an Array of the index values where the colors belong. This is to help work around issues where there's a mis-matched order of color/numeric data like drop-shadow(#f00 0px 1px 2px) and drop-shadow(0x 1px 2px #f00). This is basically a helper function used in _formatColors()\n\t\tlet values = [],\n\t\t\tc = [],\n\t\t\ti = -1;\n\t\tv.split(_colorExp).forEach(v => {\n\t\t\tlet a = v.match(_numWithUnitExp) || [];\n\t\t\tvalues.push(...a);\n\t\t\tc.push(i += a.length + 1);\n\t\t});\n\t\tvalues.c = c;\n\t\treturn values;\n\t},\n\t_formatColors = (s, toHSL, orderMatchData) => {\n\t\tlet result = \"\",\n\t\t\tcolors = (s + result).match(_colorExp),\n\t\t\ttype = toHSL ? \"hsla(\" : \"rgba(\",\n\t\t\ti = 0,\n\t\t\tc, shell, d, l;\n\t\tif (!colors) {\n\t\t\treturn s;\n\t\t}\n\t\tcolors = colors.map(color => (color = splitColor(color, toHSL, 1)) && type + (toHSL ? color[0] + \",\" + color[1] + \"%,\" + color[2] + \"%,\" + color[3] : color.join(\",\")) + \")\");\n\t\tif (orderMatchData) {\n\t\t\td = _colorOrderData(s);\n\t\t\tc = orderMatchData.c;\n\t\t\tif (c.join(result) !== d.c.join(result)) {\n\t\t\t\tshell = s.replace(_colorExp, \"1\").split(_numWithUnitExp);\n\t\t\t\tl = shell.length - 1;\n\t\t\t\tfor (; i < l; i++) {\n\t\t\t\t\tresult += shell[i] + (~c.indexOf(i) ? colors.shift() || type + \"0,0,0,0)\" : (d.length ? d : colors.length ? colors : orderMatchData).shift());\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (!shell) {\n\t\t\tshell = s.split(_colorExp);\n\t\t\tl = shell.length - 1;\n\t\t\tfor (; i < l; i++) {\n\t\t\t\tresult += shell[i] + colors[i];\n\t\t\t}\n\t\t}\n\t\treturn result + shell[l];\n\t},\n\t_colorExp = (function() {\n\t\tlet s = \"(?:\\\\b(?:(?:rgb|rgba|hsl|hsla)\\\\(.+?\\\\))|\\\\B#(?:[0-9a-f]{3,4}){1,2}\\\\b\", //we'll dynamically build this Regular Expression to conserve file size. After building it, it will be able to find rgb(), rgba(), # (hexadecimal), and named color values like red, blue, purple, etc.,\n\t\t\tp;\n\t\tfor (p in _colorLookup) {\n\t\t\ts += \"|\" + p + \"\\\\b\";\n\t\t}\n\t\treturn new RegExp(s + \")\", \"gi\");\n\t})(),\n\t_hslExp = /hsl[a]?\\(/,\n\t_colorStringFilter = a => {\n\t\tlet combined = a.join(\" \"),\n\t\t\ttoHSL;\n\t\t_colorExp.lastIndex = 0;\n\t\tif (_colorExp.test(combined)) {\n\t\t\ttoHSL = _hslExp.test(combined);\n\t\t\ta[1] = _formatColors(a[1], toHSL);\n\t\t\ta[0] = _formatColors(a[0], toHSL, _colorOrderData(a[1])); // make sure the order of numbers/colors match with the END value.\n\t\t\treturn true;\n\t\t}\n\t},\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/*\n * --------------------------------------------------------------------------------------\n * TICKER\n * --------------------------------------------------------------------------------------\n */\n\t_tickerActive,\n\t_ticker = (function() {\n\t\tlet _getTime = Date.now,\n\t\t\t_lagThreshold = 500,\n\t\t\t_adjustedLag = 33,\n\t\t\t_startTime = _getTime(),\n\t\t\t_lastUpdate = _startTime,\n\t\t\t_gap = 1000 / 240,\n\t\t\t_nextTime = _gap,\n\t\t\t_listeners = [],\n\t\t\t_id, _req, _raf, _self, _delta, _i,\n\t\t\t_tick = v => {\n\t\t\t\tlet elapsed = _getTime() - _lastUpdate,\n\t\t\t\t\tmanual = v === true,\n\t\t\t\t\toverlap, dispatch, time, frame;\n\t\t\t\telapsed > _lagThreshold && (_startTime += elapsed - _adjustedLag);\n\t\t\t\t_lastUpdate += elapsed;\n\t\t\t\ttime = _lastUpdate - _startTime;\n\t\t\t\toverlap = time - _nextTime;\n\t\t\t\tif (overlap > 0 || manual) {\n\t\t\t\t\tframe = ++_self.frame;\n\t\t\t\t\t_delta = time - _self.time * 1000;\n\t\t\t\t\t_self.time = time = time / 1000;\n\t\t\t\t\t_nextTime += overlap + (overlap >= _gap ? 4 : _gap - overlap);\n\t\t\t\t\tdispatch = 1;\n\t\t\t\t}\n\t\t\t\tmanual || (_id = _req(_tick)); //make sure the request is made before we dispatch the \"tick\" event so that timing is maintained. Otherwise, if processing the \"tick\" requires a bunch of time (like 15ms) and we're using a setTimeout() that's based on 16.7ms, it'd technically take 31.7ms between frames otherwise.\n\t\t\t\tif (dispatch) {\n\t\t\t\t\tfor (_i = 0; _i < _listeners.length; _i++) { // use _i and check _listeners.length instead of a variable because a listener could get removed during the loop, and if that happens to an element less than the current index, it'd throw things off in the loop.\n\t\t\t\t\t\t_listeners[_i](time, _delta, frame, v);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t};\n\t\t_self = {\n\t\t\ttime:0,\n\t\t\tframe:0,\n\t\t\ttick() {\n\t\t\t\t_tick(true);\n\t\t\t},\n\t\t\tdeltaRatio(fps) {\n\t\t\t\treturn _delta / (1000 / (fps || 60));\n\t\t\t},\n\t\t\twake() {\n\t\t\t\tif (_coreReady) {\n\t\t\t\t\tif (!_coreInitted && _windowExists()) {\n\t\t\t\t\t\t_win = _coreInitted = window;\n\t\t\t\t\t\t_doc = _win.document || {};\n\t\t\t\t\t\t_globals.gsap = gsap;\n\t\t\t\t\t\t(_win.gsapVersions || (_win.gsapVersions = [])).push(gsap.version);\n\t\t\t\t\t\t_install(_installScope || _win.GreenSockGlobals || (!_win.gsap && _win) || {});\n\t\t\t\t\t\t_raf = _win.requestAnimationFrame;\n\t\t\t\t\t\t_registerPluginQueue.forEach(_createPlugin);\n\t\t\t\t\t}\n\t\t\t\t\t_id && _self.sleep();\n\t\t\t\t\t_req = _raf || (f => setTimeout(f, (_nextTime - _self.time * 1000 + 1) | 0));\n\t\t\t\t\t_tickerActive = 1;\n\t\t\t\t\t_tick(2);\n\t\t\t\t}\n\t\t\t},\n\t\t\tsleep() {\n\t\t\t\t(_raf ? _win.cancelAnimationFrame : clearTimeout)(_id);\n\t\t\t\t_tickerActive = 0;\n\t\t\t\t_req = _emptyFunc;\n\t\t\t},\n\t\t\tlagSmoothing(threshold, adjustedLag) {\n\t\t\t\t_lagThreshold = threshold || Infinity; // zero should be interpreted as basically unlimited\n\t\t\t\t_adjustedLag = Math.min(adjustedLag || 33, _lagThreshold);\n\t\t\t},\n\t\t\tfps(fps) {\n\t\t\t\t_gap = 1000 / (fps || 240);\n\t\t\t\t_nextTime = _self.time * 1000 + _gap;\n\t\t\t},\n\t\t\tadd(callback, once, prioritize) {\n\t\t\t\tlet func = once ? (t, d, f, v) => {callback(t, d, f, v); _self.remove(func);} : callback;\n\t\t\t\t_self.remove(callback);\n\t\t\t\t_listeners[prioritize ? \"unshift\" : \"push\"](func);\n\t\t\t\t_wake();\n\t\t\t\treturn func;\n\t\t\t},\n\t\t\tremove(callback, i) {\n\t\t\t\t~(i = _listeners.indexOf(callback)) && _listeners.splice(i, 1) && _i >= i && _i--;\n\t\t\t},\n\t\t\t_listeners:_listeners\n\t\t};\n\t\treturn _self;\n\t})(),\n\t_wake = () => !_tickerActive && _ticker.wake(), //also ensures the core classes are initialized.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/*\n* -------------------------------------------------\n* EASING\n* -------------------------------------------------\n*/\n\t_easeMap = {},\n\t_customEaseExp = /^[\\d.\\-M][\\d.\\-,\\s]/,\n\t_quotesExp = /[\"']/g,\n\t_parseObjectInString = value => { //takes a string like \"{wiggles:10, type:anticipate})\" and turns it into a real object. Notice it ends in \")\" and includes the {} wrappers. This is because we only use this function for parsing ease configs and prioritized optimization rather than reusability.\n\t\tlet obj = {},\n\t\t\tsplit = value.substr(1, value.length-3).split(\":\"),\n\t\t\tkey = split[0],\n\t\t\ti = 1,\n\t\t\tl = split.length,\n\t\t\tindex, val, parsedVal;\n\t\tfor (; i < l; i++) {\n\t\t\tval = split[i];\n\t\t\tindex = i !== l-1 ? val.lastIndexOf(\",\") : val.length;\n\t\t\tparsedVal = val.substr(0, index);\n\t\t\tobj[key] = isNaN(parsedVal) ? parsedVal.replace(_quotesExp, \"\").trim() : +parsedVal;\n\t\t\tkey = val.substr(index+1).trim();\n\t\t}\n\t\treturn obj;\n\t},\n\t_valueInParentheses = value => {\n\t\tlet open = value.indexOf(\"(\") + 1,\n\t\t\tclose = value.indexOf(\")\"),\n\t\t\tnested = value.indexOf(\"(\", open);\n\t\treturn value.substring(open, ~nested && nested < close ? value.indexOf(\")\", close + 1) : close);\n\t},\n\t_configEaseFromString = name => { //name can be a string like \"elastic.out(1,0.5)\", and pass in _easeMap as obj and it'll parse it out and call the actual function like _easeMap.Elastic.easeOut.config(1,0.5). It will also parse custom ease strings as long as CustomEase is loaded and registered (internally as _easeMap._CE).\n\t\tlet split = (name + \"\").split(\"(\"),\n\t\t\tease = _easeMap[split[0]];\n\t\treturn (ease && split.length > 1 && ease.config) ? ease.config.apply(null, ~name.indexOf(\"{\") ? [_parseObjectInString(split[1])] : _valueInParentheses(name).split(\",\").map(_numericIfPossible)) : (_easeMap._CE && _customEaseExp.test(name)) ? _easeMap._CE(\"\", name) : ease;\n\t},\n\t_invertEase = ease => p => 1 - ease(1 - p),\n\t// allow yoyoEase to be set in children and have those affected when the parent/ancestor timeline yoyos.\n\t_propagateYoyoEase = (timeline, isYoyo) => {\n\t\tlet child = timeline._first, ease;\n\t\twhile (child) {\n\t\t\tif (child instanceof Timeline) {\n\t\t\t\t_propagateYoyoEase(child, isYoyo);\n\t\t\t} else if (child.vars.yoyoEase && (!child._yoyo || !child._repeat) && child._yoyo !== isYoyo) {\n\t\t\t\tif (child.timeline) {\n\t\t\t\t\t_propagateYoyoEase(child.timeline, isYoyo);\n\t\t\t\t} else {\n\t\t\t\t\tease = child._ease;\n\t\t\t\t\tchild._ease = child._yEase;\n\t\t\t\t\tchild._yEase = ease;\n\t\t\t\t\tchild._yoyo = isYoyo;\n\t\t\t\t}\n\t\t\t}\n\t\t\tchild = child._next;\n\t\t}\n\t},\n\t_parseEase = (ease, defaultEase) => !ease ? defaultEase : (_isFunction(ease) ? ease : _easeMap[ease] || _configEaseFromString(ease)) || defaultEase,\n\t_insertEase = (names, easeIn, easeOut = p => 1 - easeIn(1 - p), easeInOut = (p => p < .5 ? easeIn(p * 2) / 2 : 1 - easeIn((1 - p) * 2) / 2)) => {\n\t\tlet ease = {easeIn, easeOut, easeInOut},\n\t\t\tlowercaseName;\n\t\t_forEachName(names, name => {\n\t\t\t_easeMap[name] = _globals[name] = ease;\n\t\t\t_easeMap[(lowercaseName = name.toLowerCase())] = easeOut;\n\t\t\tfor (let p in ease) {\n\t\t\t\t_easeMap[lowercaseName + (p === \"easeIn\" ? \".in\" : p === \"easeOut\" ? \".out\" : \".inOut\")] = _easeMap[name + \".\" + p] = ease[p];\n\t\t\t}\n\t\t});\n\t\treturn ease;\n\t},\n\t_easeInOutFromOut = easeOut => (p => p < .5 ? (1 - easeOut(1 - (p * 2))) / 2 : .5 + easeOut((p - .5) * 2) / 2),\n\t_configElastic = (type, amplitude, period) => {\n\t\tlet p1 = (amplitude >= 1) ? amplitude : 1, //note: if amplitude is < 1, we simply adjust the period for a more natural feel. Otherwise the math doesn't work right and the curve starts at 1.\n\t\t\tp2 = (period || (type ? .3 : .45)) / (amplitude < 1 ? amplitude : 1),\n\t\t\tp3 = p2 / _2PI * (Math.asin(1 / p1) || 0),\n\t\t\teaseOut = p => p === 1 ? 1 : p1 * (2 ** (-10 * p)) * _sin((p - p3) * p2) + 1,\n\t\t\tease = (type === \"out\") ? easeOut : (type === \"in\") ? p => 1 - easeOut(1 - p) : _easeInOutFromOut(easeOut);\n\t\tp2 = _2PI / p2; //precalculate to optimize\n\t\tease.config = (amplitude, period) => _configElastic(type, amplitude, period);\n\t\treturn ease;\n\t},\n\t_configBack = (type, overshoot = 1.70158) => {\n\t\tlet easeOut = p => p ? ((--p) * p * ((overshoot + 1) * p + overshoot) + 1) : 0,\n\t\t\tease = type === \"out\" ? easeOut : type === \"in\" ? p => 1 - easeOut(1 - p) : _easeInOutFromOut(easeOut);\n\t\tease.config = overshoot => _configBack(type, overshoot);\n\t\treturn ease;\n\t};\n\t// a cheaper (kb and cpu) but more mild way to get a parameterized weighted ease by feeding in a value between -1 (easeIn) and 1 (easeOut) where 0 is linear.\n\t// _weightedEase = ratio => {\n\t// \tlet y = 0.5 + ratio / 2;\n\t// \treturn p => (2 * (1 - p) * p * y + p * p);\n\t// },\n\t// a stronger (but more expensive kb/cpu) parameterized weighted ease that lets you feed in a value between -1 (easeIn) and 1 (easeOut) where 0 is linear.\n\t// _weightedEaseStrong = ratio => {\n\t// \tratio = .5 + ratio / 2;\n\t// \tlet o = 1 / 3 * (ratio < .5 ? ratio : 1 - ratio),\n\t// \t\tb = ratio - o,\n\t// \t\tc = ratio + o;\n\t// \treturn p => p === 1 ? p : 3 * b * (1 - p) * (1 - p) * p + 3 * c * (1 - p) * p * p + p * p * p;\n\t// };\n\n_forEachName(\"Linear,Quad,Cubic,Quart,Quint,Strong\", (name, i) => {\n\tlet power = i < 5 ? i + 1 : i;\n\t_insertEase(name + \",Power\" + (power - 1), i ? p => p ** power : p => p, p => 1 - (1 - p) ** power, p => p < .5 ? (p * 2) ** power / 2 : 1 - ((1 - p) * 2) ** power / 2);\n});\n_easeMap.Linear.easeNone = _easeMap.none = _easeMap.Linear.easeIn;\n_insertEase(\"Elastic\", _configElastic(\"in\"), _configElastic(\"out\"), _configElastic());\n((n, c) => {\n\tlet n1 = 1 / c,\n\t\tn2 = 2 * n1,\n\t\tn3 = 2.5 * n1,\n\t\teaseOut = p => (p < n1) ? n * p * p : (p < n2) ? n * (p - 1.5 / c) ** 2 + .75 : (p < n3) ? n * (p -= 2.25 / c) * p + .9375 : n * (p - 2.625 / c) ** 2 + .984375;\n\t_insertEase(\"Bounce\", p => 1 - easeOut(1 - p), easeOut);\n})(7.5625, 2.75);\n_insertEase(\"Expo\", p => p ? 2 ** (10 * (p - 1)) : 0);\n_insertEase(\"Circ\", p => -(_sqrt(1 - (p * p)) - 1));\n_insertEase(\"Sine\", p => p === 1 ? 1 : -_cos(p * _HALF_PI) + 1);\n_insertEase(\"Back\", _configBack(\"in\"), _configBack(\"out\"), _configBack());\n_easeMap.SteppedEase = _easeMap.steps = _globals.SteppedEase = {\n\tconfig(steps = 1, immediateStart) {\n\t\tlet p1 = 1 / steps,\n\t\t\tp2 = steps + (immediateStart ? 0 : 1),\n\t\t\tp3 = immediateStart ? 1 : 0,\n\t\t\tmax = 1 - _tinyNum;\n\t\treturn p => (((p2 * _clamp(0, max, p)) | 0) + p3) * p1;\n\t}\n};\n_defaults.ease = _easeMap[\"quad.out\"];\n\n\n_forEachName(\"onComplete,onUpdate,onStart,onRepeat,onReverseComplete,onInterrupt\", name => _callbackNames += name + \",\" + name + \"Params,\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/*\n * --------------------------------------------------------------------------------------\n * CACHE\n * --------------------------------------------------------------------------------------\n */\nexport class GSCache {\n\n\tconstructor(target, harness) {\n\t\tthis.id = _gsID++;\n\t\ttarget._gsap = this;\n\t\tthis.target = target;\n\t\tthis.harness = harness;\n\t\tthis.get = harness ? harness.get : _getProperty;\n\t\tthis.set = harness ? harness.getSetter : _getSetter;\n\t}\n\n}\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/*\n * --------------------------------------------------------------------------------------\n * ANIMATION\n * --------------------------------------------------------------------------------------\n */\n\nexport class Animation {\n\n\tconstructor(vars) {\n\t\tthis.vars = vars;\n\t\tthis._delay = +vars.delay || 0;\n\t\tif ((this._repeat = vars.repeat === Infinity ? -2 : vars.repeat || 0)) { // TODO: repeat: Infinity on a timeline's children must flag that timeline internally and affect its totalDuration, otherwise it'll stop in the negative direction when reaching the start.\n\t\t\tthis._rDelay = vars.repeatDelay || 0;\n\t\t\tthis._yoyo = !!vars.yoyo || !!vars.yoyoEase;\n\t\t}\n\t\tthis._ts = 1;\n\t\t_setDuration(this, +vars.duration, 1, 1);\n\t\tthis.data = vars.data;\n\t\tif (_context) {\n\t\t\tthis._ctx = _context;\n\t\t\t_context.data.push(this);\n\t\t}\n\t\t_tickerActive || _ticker.wake();\n\t}\n\n\tdelay(value) {\n\t\tif (value || value === 0) {\n\t\t\tthis.parent && this.parent.smoothChildTiming && (this.startTime(this._start + value - this._delay));\n\t\t\tthis._delay = value;\n\t\t\treturn this;\n\t\t}\n\t\treturn this._delay;\n\t}\n\n\tduration(value) {\n\t\treturn arguments.length ? this.totalDuration(this._repeat > 0 ? value + (value + this._rDelay) * this._repeat : value) : this.totalDuration() && this._dur;\n\t}\n\n\ttotalDuration(value) {\n\t\tif (!arguments.length) {\n\t\t\treturn this._tDur;\n\t\t}\n\t\tthis._dirty = 0;\n\t\treturn _setDuration(this, this._repeat < 0 ? value : (value - (this._repeat * this._rDelay)) / (this._repeat + 1));\n\t}\n\n\ttotalTime(totalTime, suppressEvents) {\n\t\t_wake();\n\t\tif (!arguments.length) {\n\t\t\treturn this._tTime;\n\t\t}\n\t\tlet parent = this._dp;\n\t\tif (parent && parent.smoothChildTiming && this._ts) {\n\t\t\t_alignPlayhead(this, totalTime);\n\t\t\t!parent._dp || parent.parent || _postAddChecks(parent, this); // edge case: if this is a child of a timeline that already completed, for example, we must re-activate the parent.\n\t\t\t//in case any of the ancestor timelines had completed but should now be enabled, we should reset their totalTime() which will also ensure that they're lined up properly and enabled. Skip for animations that are on the root (wasteful). Example: a TimelineLite.exportRoot() is performed when there's a paused tween on the root, the export will not complete until that tween is unpaused, but imagine a child gets restarted later, after all [unpaused] tweens have completed. The start of that child would get pushed out, but one of the ancestors may have completed.\n\t\t\twhile (parent && parent.parent) {\n\t\t\t\tif (parent.parent._time !== parent._start + (parent._ts >= 0 ? parent._tTime / parent._ts : (parent.totalDuration() - parent._tTime) / -parent._ts)) {\n\t\t\t\t\tparent.totalTime(parent._tTime, true);\n\t\t\t\t}\n\t\t\t\tparent = parent.parent;\n\t\t\t}\n\t\t\tif (!this.parent && this._dp.autoRemoveChildren && ((this._ts > 0 && totalTime < this._tDur) || (this._ts < 0 && totalTime > 0) || (!this._tDur && !totalTime) )) { //if the animation doesn't have a parent, put it back into its last parent (recorded as _dp for exactly cases like this). Limit to parents with autoRemoveChildren (like globalTimeline) so that if the user manually removes an animation from a timeline and then alters its playhead, it doesn't get added back in.\n\t\t\t\t_addToTimeline(this._dp, this, this._start - this._delay);\n\t\t\t}\n\t\t}\n        if (this._tTime !== totalTime || (!this._dur && !suppressEvents) || (this._initted && Math.abs(this._zTime) === _tinyNum) || (!totalTime && !this._initted && (this.add || this._ptLookup))) { // check for _ptLookup on a Tween instance to ensure it has actually finished being instantiated, otherwise if this.reverse() gets called in the Animation constructor, it could trigger a render() here even though the _targets weren't populated, thus when _init() is called there won't be any PropTweens (it'll act like the tween is non-functional)\n        \tthis._ts || (this._pTime = totalTime); // otherwise, if an animation is paused, then the playhead is moved back to zero, then resumed, it'd revert back to the original time at the pause\n\t        //if (!this._lock) { // avoid endless recursion (not sure we need this yet or if it's worth the performance hit)\n\t\t    //   this._lock = 1;\n\t\t        _lazySafeRender(this, totalTime, suppressEvents);\n\t\t    //   this._lock = 0;\n\t        //}\n\t\t}\n\t\treturn this;\n\t}\n\n\ttime(value, suppressEvents) {\n\t\treturn arguments.length ? this.totalTime((Math.min(this.totalDuration(), value + _elapsedCycleDuration(this)) % (this._dur + this._rDelay)) || (value ? this._dur : 0), suppressEvents) : this._time; // note: if the modulus results in 0, the playhead could be exactly at the end or the beginning, and we always defer to the END with a non-zero value, otherwise if you set the time() to the very end (duration()), it would render at the START!\n\t}\n\n\ttotalProgress(value, suppressEvents) {\n\t\treturn arguments.length ? this.totalTime( this.totalDuration() * value, suppressEvents) : this.totalDuration() ? Math.min(1, this._tTime / this._tDur) : this.ratio;\n\t}\n\n\tprogress(value, suppressEvents) {\n\t\treturn arguments.length ? this.totalTime( this.duration() * (this._yoyo && !(this.iteration() & 1) ? 1 - value : value) + _elapsedCycleDuration(this), suppressEvents) : (this.duration() ? Math.min(1, this._time / this._dur) : this.ratio);\n\t}\n\n\titeration(value, suppressEvents) {\n\t\tlet cycleDuration = this.duration() + this._rDelay;\n\t\treturn arguments.length ? this.totalTime(this._time + (value - 1) * cycleDuration, suppressEvents) : this._repeat ? _animationCycle(this._tTime, cycleDuration) + 1 : 1;\n\t}\n\n\t// potential future addition:\n\t// isPlayingBackwards() {\n\t// \tlet animation = this,\n\t// \t\torientation = 1; // 1 = forward, -1 = backward\n\t// \twhile (animation) {\n\t// \t\torientation *= animation.reversed() || (animation.repeat() && !(animation.iteration() & 1)) ? -1 : 1;\n\t// \t\tanimation = animation.parent;\n\t// \t}\n\t// \treturn orientation < 0;\n\t// }\n\n\ttimeScale(value) {\n\t\tif (!arguments.length) {\n\t\t\treturn this._rts === -_tinyNum ? 0 : this._rts; // recorded timeScale. Special case: if someone calls reverse() on an animation with timeScale of 0, we assign it -_tinyNum to remember it's reversed.\n\t\t}\n\t\tif (this._rts === value) {\n\t\t\treturn this;\n\t\t}\n\t\tlet tTime = this.parent && this._ts ? _parentToChildTotalTime(this.parent._time, this) : this._tTime; // make sure to do the parentToChildTotalTime() BEFORE setting the new _ts because the old one must be used in that calculation.\n\n\t\t// future addition? Up side: fast and minimal file size. Down side: only works on this animation; if a timeline is reversed, for example, its childrens' onReverse wouldn't get called.\n\t\t//(+value < 0 && this._rts >= 0) && _callback(this, \"onReverse\", true);\n\n\t\t// prioritize rendering where the parent's playhead lines up instead of this._tTime because there could be a tween that's animating another tween's timeScale in the same rendering loop (same parent), thus if the timeScale tween renders first, it would alter _start BEFORE _tTime was set on that tick (in the rendering loop), effectively freezing it until the timeScale tween finishes.\n\t\tthis._rts = +value || 0;\n\t\tthis._ts = (this._ps || value === -_tinyNum) ? 0 : this._rts; // _ts is the functional timeScale which would be 0 if the animation is paused.\n\t\tthis.totalTime(_clamp(-Math.abs(this._delay), this._tDur, tTime), true);\n\t\t_setEnd(this); // if parent.smoothChildTiming was false, the end time didn't get updated in the _alignPlayhead() method, so do it here.\n\t\treturn _recacheAncestors(this);\n\t}\n\n\tpaused(value) {\n\t\tif (!arguments.length) {\n\t\t\treturn this._ps;\n\t\t}\n\t\tif (this._ps !== value) {\n\t\t\tthis._ps = value;\n\t\t\tif (value) {\n\t\t\t\tthis._pTime = this._tTime || Math.max(-this._delay, this.rawTime()); // if the pause occurs during the delay phase, make sure that's factored in when resuming.\n\t\t\t\tthis._ts = this._act = 0; // _ts is the functional timeScale, so a paused tween would effectively have a timeScale of 0. We record the \"real\" timeScale as _rts (recorded time scale)\n\t\t\t} else {\n\t\t\t\t_wake();\n\t\t\t\tthis._ts = this._rts;\n\t\t\t\t//only defer to _pTime (pauseTime) if tTime is zero. Remember, someone could pause() an animation, then scrub the playhead and resume(). If the parent doesn't have smoothChildTiming, we render at the rawTime() because the startTime won't get updated.\n\t\t\t\tthis.totalTime(this.parent && !this.parent.smoothChildTiming ? this.rawTime() : this._tTime || this._pTime, (this.progress() === 1) && Math.abs(this._zTime) !== _tinyNum && (this._tTime -= _tinyNum)); // edge case: animation.progress(1).pause().play() wouldn't render again because the playhead is already at the end, but the call to totalTime() below will add it back to its parent...and not remove it again (since removing only happens upon rendering at a new time). Offsetting the _tTime slightly is done simply to cause the final render in totalTime() that'll pop it off its timeline (if autoRemoveChildren is true, of course). Check to make sure _zTime isn't -_tinyNum to avoid an edge case where the playhead is pushed to the end but INSIDE a tween/callback, the timeline itself is paused thus halting rendering and leaving a few unrendered. When resuming, it wouldn't render those otherwise.\n\t\t\t}\n\t\t}\n\t\treturn this;\n\t}\n\n\tstartTime(value) {\n\t\tif (arguments.length) {\n\t\t\tthis._start = value;\n\t\t\tlet parent = this.parent || this._dp;\n\t\t\tparent && (parent._sort || !this.parent) && _addToTimeline(parent, this, value - this._delay);\n\t\t\treturn this;\n\t\t}\n\t\treturn this._start;\n\t}\n\n\tendTime(includeRepeats) {\n\t\treturn this._start + (_isNotFalse(includeRepeats) ? this.totalDuration() : this.duration()) / Math.abs(this._ts || 1);\n\t}\n\n\trawTime(wrapRepeats) {\n\t\tlet parent = this.parent || this._dp; // _dp = detached parent\n\t\treturn !parent ? this._tTime : (wrapRepeats && (!this._ts || (this._repeat && this._time && this.totalProgress() < 1))) ? this._tTime % (this._dur + this._rDelay) : !this._ts ? this._tTime : _parentToChildTotalTime(parent.rawTime(wrapRepeats), this);\n\t}\n\n\trevert(config= _revertConfig) {\n\t\tlet prevIsReverting = _reverting;\n\t\t_reverting = config;\n\t\tif (this._initted || this._startAt) {\n\t\t\tthis.timeline && this.timeline.revert(config);\n\t\t\tthis.totalTime(-0.01, config.suppressEvents);\n\t\t}\n\t\tthis.data !== \"nested\" && config.kill !== false && this.kill();\n\t\t_reverting = prevIsReverting;\n\t\treturn this;\n\t}\n\n\tglobalTime(rawTime) {\n\t\tlet animation = this,\n\t\t\ttime = arguments.length ? rawTime : animation.rawTime();\n\t\twhile (animation) {\n\t\t\ttime = animation._start + time / (animation._ts || 1);\n\t\t\tanimation = animation._dp;\n\t\t}\n\t\treturn !this.parent && this._sat ? (this._sat.vars.immediateRender ? -Infinity : this._sat.globalTime(rawTime)) : time; // the _startAt tweens for .fromTo() and .from() that have immediateRender should always be FIRST in the timeline (important for context.revert()). \"_sat\" stands for _startAtTween, referring to the parent tween that created the _startAt. We must discern if that tween had immediateRender so that we can know whether or not to prioritize it in revert().\n\t}\n\n\trepeat(value) {\n\t\tif (arguments.length) {\n\t\t\tthis._repeat = value === Infinity ? -2 : value;\n\t\t\treturn _onUpdateTotalDuration(this);\n\t\t}\n\t\treturn this._repeat === -2 ? Infinity : this._repeat;\n\t}\n\n\trepeatDelay(value) {\n\t\tif (arguments.length) {\n\t\t\tlet time = this._time;\n\t\t\tthis._rDelay = value;\n\t\t\t_onUpdateTotalDuration(this);\n\t\t\treturn time ? this.time(time) : this;\n\t\t}\n\t\treturn this._rDelay;\n\t}\n\n\tyoyo(value) {\n\t\tif (arguments.length) {\n\t\t\tthis._yoyo = value;\n\t\t\treturn this;\n\t\t}\n\t\treturn this._yoyo;\n\t}\n\n\tseek(position, suppressEvents) {\n\t\treturn this.totalTime(_parsePosition(this, position), _isNotFalse(suppressEvents));\n\t}\n\n\trestart(includeDelay, suppressEvents) {\n\t\treturn this.play().totalTime(includeDelay ? -this._delay : 0, _isNotFalse(suppressEvents));\n\t}\n\n\tplay(from, suppressEvents) {\n\t\tfrom != null && this.seek(from, suppressEvents);\n\t\treturn this.reversed(false).paused(false);\n\t}\n\n\treverse(from, suppressEvents) {\n\t\tfrom != null && this.seek(from || this.totalDuration(), suppressEvents);\n\t\treturn this.reversed(true).paused(false);\n\t}\n\n\tpause(atTime, suppressEvents) {\n\t\tatTime != null && this.seek(atTime, suppressEvents);\n\t\treturn this.paused(true);\n\t}\n\n\tresume() {\n\t\treturn this.paused(false);\n\t}\n\n\treversed(value) {\n\t\tif (arguments.length) {\n\t\t\t!!value !== this.reversed() && this.timeScale(-this._rts || (value ? -_tinyNum : 0)); // in case timeScale is zero, reversing would have no effect so we use _tinyNum.\n\t\t\treturn this;\n\t\t}\n\t\treturn this._rts < 0;\n\t}\n\n\tinvalidate() {\n\t\tthis._initted = this._act = 0;\n\t\tthis._zTime = -_tinyNum;\n\t\treturn this;\n\t}\n\n\tisActive() {\n\t\tlet parent = this.parent || this._dp,\n\t\t\tstart = this._start,\n\t\t\trawTime;\n\t\treturn !!(!parent || (this._ts && this._initted && parent.isActive() && (rawTime = parent.rawTime(true)) >= start && rawTime < this.endTime(true) - _tinyNum));\n\t}\n\n\teventCallback(type, callback, params) {\n\t\tlet vars = this.vars;\n\t\tif (arguments.length > 1) {\n\t\t\tif (!callback) {\n\t\t\t\tdelete vars[type];\n\t\t\t} else {\n\t\t\t\tvars[type] = callback;\n\t\t\t\tparams && (vars[type + \"Params\"] = params);\n\t\t\t\ttype === \"onUpdate\" && (this._onUpdate = callback);\n\t\t\t}\n\t\t\treturn this;\n\t\t}\n\t\treturn vars[type];\n\t}\n\n\tthen(onFulfilled) {\n\t\tlet self = this;\n\t\treturn new Promise(resolve => {\n\t\t\tlet f = _isFunction(onFulfilled) ? onFulfilled : _passThrough,\n\t\t\t\t_resolve = () => {\n\t\t\t\t\tlet _then = self.then;\n\t\t\t\t\tself.then = null; // temporarily null the then() method to avoid an infinite loop (see https://github.com/greensock/GSAP/issues/322)\n\t\t\t\t\t_isFunction(f) && (f = f(self)) && (f.then || f === self) && (self.then = _then);\n\t\t\t\t\tresolve(f);\n\t\t\t\t\tself.then = _then;\n\t\t\t\t};\n\t\t\tif (self._initted && (self.totalProgress() === 1 && self._ts >= 0) || (!self._tTime && self._ts < 0)) {\n\t\t\t\t_resolve();\n\t\t\t} else {\n\t\t\t\tself._prom = _resolve;\n\t\t\t}\n\t\t});\n\t}\n\n\tkill() {\n\t\t_interrupt(this);\n\t}\n\n}\n\n_setDefaults(Animation.prototype, {_time:0, _start:0, _end:0, _tTime:0, _tDur:0, _dirty:0, _repeat:0, _yoyo:false, parent:null, _initted:false, _rDelay:0, _ts:1, _dp:0, ratio:0, _zTime:-_tinyNum, _prom:0, _ps:false, _rts:1});\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/*\n * -------------------------------------------------\n * TIMELINE\n * -------------------------------------------------\n */\n\nexport class Timeline extends Animation {\n\n\tconstructor(vars = {}, position) {\n\t\tsuper(vars);\n\t\tthis.labels = {};\n\t\tthis.smoothChildTiming = !!vars.smoothChildTiming;\n\t\tthis.autoRemoveChildren = !!vars.autoRemoveChildren;\n\t\tthis._sort = _isNotFalse(vars.sortChildren);\n\t\t_globalTimeline && _addToTimeline(vars.parent || _globalTimeline, this, position);\n\t\tvars.reversed && this.reverse();\n\t\tvars.paused && this.paused(true);\n\t\tvars.scrollTrigger && _scrollTrigger(this, vars.scrollTrigger);\n\t}\n\n\tto(targets, vars, position) {\n\t\t_createTweenType(0, arguments, this);\n\t\treturn this;\n\t}\n\n\tfrom(targets, vars, position) {\n\t\t_createTweenType(1, arguments, this);\n\t\treturn this;\n\t}\n\n\tfromTo(targets, fromVars, toVars, position) {\n\t\t_createTweenType(2, arguments, this);\n\t\treturn this;\n\t}\n\n\tset(targets, vars, position) {\n\t\tvars.duration = 0;\n\t\tvars.parent = this;\n\t\t_inheritDefaults(vars).repeatDelay || (vars.repeat = 0);\n\t\tvars.immediateRender = !!vars.immediateRender;\n\t\tnew Tween(targets, vars, _parsePosition(this, position), 1);\n\t\treturn this;\n\t}\n\n\tcall(callback, params, position) {\n\t\treturn _addToTimeline(this, Tween.delayedCall(0, callback, params), position);\n\t}\n\n\t//ONLY for backward compatibility! Maybe delete?\n\tstaggerTo(targets, duration, vars, stagger, position, onCompleteAll, onCompleteAllParams) {\n\t\tvars.duration = duration;\n\t\tvars.stagger = vars.stagger || stagger;\n\t\tvars.onComplete = onCompleteAll;\n\t\tvars.onCompleteParams = onCompleteAllParams;\n\t\tvars.parent = this;\n\t\tnew Tween(targets, vars, _parsePosition(this, position));\n\t\treturn this;\n\t}\n\n\tstaggerFrom(targets, duration, vars, stagger, position, onCompleteAll, onCompleteAllParams) {\n\t\tvars.runBackwards = 1;\n\t\t_inheritDefaults(vars).immediateRender = _isNotFalse(vars.immediateRender);\n\t\treturn this.staggerTo(targets, duration, vars, stagger, position, onCompleteAll, onCompleteAllParams);\n\t}\n\n\tstaggerFromTo(targets, duration, fromVars, toVars, stagger, position, onCompleteAll, onCompleteAllParams) {\n\t\ttoVars.startAt = fromVars;\n\t\t_inheritDefaults(toVars).immediateRender = _isNotFalse(toVars.immediateRender);\n\t\treturn this.staggerTo(targets, duration, toVars, stagger, position, onCompleteAll, onCompleteAllParams);\n\t}\n\n\trender(totalTime, suppressEvents, force) {\n\t\tlet prevTime = this._time,\n\t\t\ttDur = this._dirty ? this.totalDuration() : this._tDur,\n\t\t\tdur = this._dur,\n\t\t\ttTime = totalTime <= 0 ? 0 : _roundPrecise(totalTime), // if a paused timeline is resumed (or its _start is updated for another reason...which rounds it), that could result in the playhead shifting a **tiny** amount and a zero-duration child at that spot may get rendered at a different ratio, like its totalTime in render() may be 1e-17 instead of 0, for example.\n\t\t\tcrossingStart = (this._zTime < 0) !== (totalTime < 0) && (this._initted || !dur),\n\t\t\ttime, child, next, iteration, cycleDuration, prevPaused, pauseTween, timeScale, prevStart, prevIteration, yoyo, isYoyo;\n\t\tthis !== _globalTimeline && tTime > tDur && totalTime >= 0 && (tTime = tDur);\n\t\tif (tTime !== this._tTime || force || crossingStart) {\n\t\t\tif (prevTime !== this._time && dur) { //if totalDuration() finds a child with a negative startTime and smoothChildTiming is true, things get shifted around internally so we need to adjust the time accordingly. For example, if a tween starts at -30 we must shift EVERYTHING forward 30 seconds and move this timeline's startTime backward by 30 seconds so that things align with the playhead (no jump).\n\t\t\t\ttTime += this._time - prevTime;\n\t\t\t\ttotalTime += this._time - prevTime;\n\t\t\t}\n\t\t\ttime = tTime;\n\t\t\tprevStart = this._start;\n\t\t\ttimeScale = this._ts;\n\t\t\tprevPaused = !timeScale;\n\t\t\tif (crossingStart) {\n\t\t\t\tdur || (prevTime = this._zTime);\n\t\t\t\t //when the playhead arrives at EXACTLY time 0 (right on top) of a zero-duration timeline, we need to discern if events are suppressed so that when the playhead moves again (next time), it'll trigger the callback. If events are NOT suppressed, obviously the callback would be triggered in this render. Basically, the callback should fire either when the playhead ARRIVES or LEAVES this exact spot, not both. Imagine doing a timeline.seek(0) and there's a callback that sits at 0. Since events are suppressed on that seek() by default, nothing will fire, but when the playhead moves off of that position, the callback should fire. This behavior is what people intuitively expect.\n\t\t\t\t(totalTime || !suppressEvents) && (this._zTime = totalTime);\n\t\t\t}\n\t\t\tif (this._repeat) { //adjust the time for repeats and yoyos\n\t\t\t\tyoyo = this._yoyo;\n\t\t\t\tcycleDuration = dur + this._rDelay;\n\t\t\t\tif (this._repeat < -1 && totalTime < 0) {\n\t\t\t\t\treturn this.totalTime(cycleDuration * 100 + totalTime, suppressEvents, force);\n\t\t\t\t}\n\t\t\t\ttime = _roundPrecise(tTime % cycleDuration); //round to avoid floating point errors. (4 % 0.8 should be 0 but some browsers report it as 0.79999999!)\n\t\t\t\tif (tTime === tDur) { // the tDur === tTime is for edge cases where there's a lengthy decimal on the duration and it may reach the very end but the time is rendered as not-quite-there (remember, tDur is rounded to 4 decimals whereas dur isn't)\n\t\t\t\t\titeration = this._repeat;\n\t\t\t\t\ttime = dur;\n\t\t\t\t} else {\n\t\t\t\t\titeration = ~~(tTime / cycleDuration);\n\t\t\t\t\tif (iteration && iteration === tTime / cycleDuration) {\n\t\t\t\t\t\ttime = dur;\n\t\t\t\t\t\titeration--;\n\t\t\t\t\t}\n\t\t\t\t\ttime > dur && (time = dur);\n\t\t\t\t}\n\t\t\t\tprevIteration = _animationCycle(this._tTime, cycleDuration);\n\t\t\t\t!prevTime && this._tTime && prevIteration !== iteration && this._tTime - prevIteration * cycleDuration - this._dur <= 0 && (prevIteration = iteration); // edge case - if someone does addPause() at the very beginning of a repeating timeline, that pause is technically at the same spot as the end which causes this._time to get set to 0 when the totalTime would normally place the playhead at the end. See https://greensock.com/forums/topic/23823-closing-nav-animation-not-working-on-ie-and-iphone-6-maybe-other-older-browser/?tab=comments#comment-113005 also, this._tTime - prevIteration * cycleDuration - this._dur <= 0 just checks to make sure it wasn't previously in the \"repeatDelay\" portion\n\t\t\t\tif (yoyo && (iteration & 1)) {\n\t\t\t\t\ttime = dur - time;\n\t\t\t\t\tisYoyo = 1;\n\t\t\t\t}\n\t\t\t\t/*\n\t\t\t\tmake sure children at the end/beginning of the timeline are rendered properly. If, for example,\n\t\t\t\ta 3-second long timeline rendered at 2.9 seconds previously, and now renders at 3.2 seconds (which\n\t\t\t\twould get translated to 2.8 seconds if the timeline yoyos or 0.2 seconds if it just repeats), there\n\t\t\t\tcould be a callback or a short tween that's at 2.95 or 3 seconds in which wouldn't render. So\n\t\t\t\twe need to push the timeline to the end (and/or beginning depending on its yoyo value). Also we must\n\t\t\t\tensure that zero-duration tweens at the very beginning or end of the Timeline work.\n\t\t\t\t*/\n\t\t\t\tif (iteration !== prevIteration && !this._lock) {\n\t\t\t\t\tlet rewinding = (yoyo && (prevIteration & 1)),\n\t\t\t\t\t\tdoesWrap = (rewinding === (yoyo && (iteration & 1)));\n\t\t\t\t\titeration < prevIteration && (rewinding = !rewinding);\n\t\t\t\t\tprevTime = rewinding ? 0 : tTime % dur ? dur : tTime; // if the playhead is landing exactly at the end of an iteration, use that totalTime rather than only the duration, otherwise it'll skip the 2nd render since it's effectively at the same time.\n\t\t\t\t\tthis._lock = 1;\n\t\t\t\t\tthis.render(prevTime || (isYoyo ? 0 : _roundPrecise(iteration * cycleDuration)), suppressEvents, !dur)._lock = 0;\n\t\t\t\t\tthis._tTime = tTime; // if a user gets the iteration() inside the onRepeat, for example, it should be accurate.\n\t\t\t\t\t!suppressEvents && this.parent && _callback(this, \"onRepeat\");\n\t\t\t\t\tthis.vars.repeatRefresh && !isYoyo && (this.invalidate()._lock = 1);\n\t\t\t\t\tif ((prevTime && prevTime !== this._time) || prevPaused !== !this._ts || (this.vars.onRepeat && !this.parent && !this._act)) { // if prevTime is 0 and we render at the very end, _time will be the end, thus won't match. So in this edge case, prevTime won't match _time but that's okay. If it gets killed in the onRepeat, eject as well.\n\t\t\t\t\t\treturn this;\n\t\t\t\t\t}\n\t\t\t\t\tdur = this._dur; // in case the duration changed in the onRepeat\n\t\t\t\t\ttDur = this._tDur;\n\t\t\t\t\tif (doesWrap) {\n\t\t\t\t\t\tthis._lock = 2;\n\t\t\t\t\t\tprevTime = rewinding ? dur : -0.0001;\n\t\t\t\t\t\tthis.render(prevTime, true);\n\t\t\t\t\t\tthis.vars.repeatRefresh && !isYoyo && this.invalidate();\n\t\t\t\t\t}\n\t\t\t\t\tthis._lock = 0;\n\t\t\t\t\tif (!this._ts && !prevPaused) {\n\t\t\t\t\t\treturn this;\n\t\t\t\t\t}\n\t\t\t\t\t//in order for yoyoEase to work properly when there's a stagger, we must swap out the ease in each sub-tween.\n\t\t\t\t\t_propagateYoyoEase(this, isYoyo);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (this._hasPause && !this._forcing && this._lock < 2) {\n\t\t\t\tpauseTween = _findNextPauseTween(this, _roundPrecise(prevTime), _roundPrecise(time));\n\t\t\t\tif (pauseTween) {\n\t\t\t\t\ttTime -= time - (time = pauseTween._start);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tthis._tTime = tTime;\n\t\t\tthis._time = time;\n\t\t\tthis._act = !timeScale; //as long as it's not paused, force it to be active so that if the user renders independent of the parent timeline, it'll be forced to re-render on the next tick.\n\n\t\t\tif (!this._initted) {\n\t\t\t\tthis._onUpdate = this.vars.onUpdate;\n\t\t\t\tthis._initted = 1;\n\t\t\t\tthis._zTime = totalTime;\n\t\t\t\tprevTime = 0; // upon init, the playhead should always go forward; someone could invalidate() a completed timeline and then if they restart(), that would make child tweens render in reverse order which could lock in the wrong starting values if they build on each other, like tl.to(obj, {x: 100}).to(obj, {x: 0}).\n\t\t\t}\n\t\t\tif (!prevTime && time && !suppressEvents && !iteration) {\n\t\t\t\t_callback(this, \"onStart\");\n\t\t\t\tif (this._tTime !== tTime) { // in case the onStart triggered a render at a different spot, eject. Like if someone did animation.pause(0.5) or something inside the onStart.\n\t\t\t\t\treturn this;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (time >= prevTime && totalTime >= 0) {\n\t\t\t\tchild = this._first;\n\t\t\t\twhile (child) {\n\t\t\t\t\tnext = child._next;\n\t\t\t\t\tif ((child._act || time >= child._start) && child._ts && pauseTween !== child) {\n\t\t\t\t\t\tif (child.parent !== this) { // an extreme edge case - the child's render could do something like kill() the \"next\" one in the linked list, or reparent it. In that case we must re-initiate the whole render to be safe.\n\t\t\t\t\t\t\treturn this.render(totalTime, suppressEvents, force);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tchild.render(child._ts > 0 ? (time - child._start) * child._ts : (child._dirty ? child.totalDuration() : child._tDur) + (time - child._start) * child._ts, suppressEvents, force);\n\t\t\t\t\t\tif (time !== this._time || (!this._ts && !prevPaused)) { //in case a tween pauses or seeks the timeline when rendering, like inside of an onUpdate/onComplete\n\t\t\t\t\t\t\tpauseTween = 0;\n\t\t\t\t\t\t\tnext && (tTime += (this._zTime = -_tinyNum));  // it didn't finish rendering, so flag zTime as negative so that so that the next time render() is called it'll be forced (to render any remaining children)\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tchild = next;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tchild = this._last;\n\t\t\t\tlet adjustedTime = totalTime < 0 ? totalTime : time; //when the playhead goes backward beyond the start of this timeline, we must pass that information down to the child animations so that zero-duration tweens know whether to render their starting or ending values.\n\t\t\t\twhile (child) {\n\t\t\t\t\tnext = child._prev;\n\t\t\t\t\tif ((child._act || adjustedTime <= child._end) && child._ts && pauseTween !== child) {\n\t\t\t\t\t\tif (child.parent !== this) { // an extreme edge case - the child's render could do something like kill() the \"next\" one in the linked list, or reparent it. In that case we must re-initiate the whole render to be safe.\n\t\t\t\t\t\t\treturn this.render(totalTime, suppressEvents, force);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tchild.render(child._ts > 0 ? (adjustedTime - child._start) * child._ts : (child._dirty ? child.totalDuration() : child._tDur) + (adjustedTime - child._start) * child._ts, suppressEvents, force || (_reverting && (child._initted || child._startAt)));  // if reverting, we should always force renders of initted tweens (but remember that .fromTo() or .from() may have a _startAt but not _initted yet). If, for example, a .fromTo() tween with a stagger (which creates an internal timeline) gets reverted BEFORE some of its child tweens render for the first time, it may not properly trigger them to revert.\n\t\t\t\t\t\tif (time !== this._time || (!this._ts && !prevPaused)) { //in case a tween pauses or seeks the timeline when rendering, like inside of an onUpdate/onComplete\n\t\t\t\t\t\t\tpauseTween = 0;\n\t\t\t\t\t\t\tnext && (tTime += (this._zTime = adjustedTime ? -_tinyNum : _tinyNum)); // it didn't finish rendering, so adjust zTime so that so that the next time render() is called it'll be forced (to render any remaining children)\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tchild = next;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (pauseTween && !suppressEvents) {\n\t\t\t\tthis.pause();\n\t\t\t\tpauseTween.render(time >= prevTime ? 0 : -_tinyNum)._zTime = time >= prevTime ? 1 : -1;\n\t\t\t\tif (this._ts) { //the callback resumed playback! So since we may have held back the playhead due to where the pause is positioned, go ahead and jump to where it's SUPPOSED to be (if no pause happened).\n\t\t\t\t\tthis._start = prevStart; //if the pause was at an earlier time and the user resumed in the callback, it could reposition the timeline (changing its startTime), throwing things off slightly, so we make sure the _start doesn't shift.\n\t\t\t\t\t_setEnd(this);\n\t\t\t\t\treturn this.render(totalTime, suppressEvents, force);\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis._onUpdate && !suppressEvents && _callback(this, \"onUpdate\", true);\n\t\t\tif ((tTime === tDur && this._tTime >= this.totalDuration()) || (!tTime && prevTime)) if (prevStart === this._start || Math.abs(timeScale) !== Math.abs(this._ts)) if (!this._lock) { // remember, a child's callback may alter this timeline's playhead or timeScale which is why we need to add some of these checks.\n\t\t\t\t(totalTime || !dur) && ((tTime === tDur && this._ts > 0) || (!tTime && this._ts < 0)) && _removeFromParent(this, 1); // don't remove if the timeline is reversed and the playhead isn't at 0, otherwise tl.progress(1).reverse() won't work. Only remove if the playhead is at the end and timeScale is positive, or if the playhead is at 0 and the timeScale is negative.\n\t\t\t\tif (!suppressEvents && !(totalTime < 0 && !prevTime) && (tTime || prevTime || !tDur)) {\n\t\t\t\t\t_callback(this, (tTime === tDur && totalTime >= 0 ? \"onComplete\" : \"onReverseComplete\"), true);\n\t\t\t\t\tthis._prom && !(tTime < tDur && this.timeScale() > 0) && this._prom();\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn this;\n\t}\n\n\tadd(child, position) {\n\t\t_isNumber(position) || (position = _parsePosition(this, position, child));\n\t\tif (!(child instanceof Animation)) {\n\t\t\tif (_isArray(child)) {\n\t\t\t\tchild.forEach(obj => this.add(obj, position));\n\t\t\t\treturn this;\n\t\t\t}\n\t\t\tif (_isString(child)) {\n\t\t\t\treturn this.addLabel(child, position);\n\t\t\t}\n\t\t\tif (_isFunction(child)) {\n\t\t\t\tchild = Tween.delayedCall(0, child);\n\t\t\t} else {\n\t\t\t\treturn this;\n\t\t\t}\n\t\t}\n\t\treturn this !== child ? _addToTimeline(this, child, position) : this; //don't allow a timeline to be added to itself as a child!\n\t}\n\n\tgetChildren(nested = true, tweens = true, timelines = true, ignoreBeforeTime = -_bigNum) {\n\t\tlet a = [],\n\t\t\tchild = this._first;\n\t\twhile (child) {\n\t\t\tif (child._start >= ignoreBeforeTime) {\n\t\t\t\tif (child instanceof Tween) {\n\t\t\t\t\ttweens && a.push(child);\n\t\t\t\t} else {\n\t\t\t\t\ttimelines && a.push(child);\n\t\t\t\t\tnested && a.push(...child.getChildren(true, tweens, timelines));\n\t\t\t\t}\n\t\t\t}\n\t\t\tchild = child._next;\n\t\t}\n\t\treturn a;\n\t}\n\n\tgetById(id) {\n\t\tlet animations = this.getChildren(1, 1, 1),\n\t\t\ti = animations.length;\n\t\twhile(i--) {\n\t\t\tif (animations[i].vars.id === id) {\n\t\t\t\treturn animations[i];\n\t\t\t}\n\t\t}\n\t}\n\n\tremove(child) {\n\t\tif (_isString(child)) {\n\t\t\treturn this.removeLabel(child);\n\t\t}\n\t\tif (_isFunction(child)) {\n\t\t\treturn this.killTweensOf(child);\n\t\t}\n\t\t_removeLinkedListItem(this, child);\n\t\tif (child === this._recent) {\n\t\t\tthis._recent = this._last;\n\t\t}\n\t\treturn _uncache(this);\n\t}\n\n\ttotalTime(totalTime, suppressEvents) {\n\t\tif (!arguments.length) {\n\t\t\treturn this._tTime;\n\t\t}\n\t\tthis._forcing = 1;\n\t\tif (!this._dp && this._ts) { //special case for the global timeline (or any other that has no parent or detached parent).\n\t\t\tthis._start = _roundPrecise(_ticker.time - (this._ts > 0 ? totalTime / this._ts : (this.totalDuration() - totalTime) / -this._ts));\n\t\t}\n\t\tsuper.totalTime(totalTime, suppressEvents);\n\t\tthis._forcing = 0;\n\t\treturn this;\n\t}\n\n\taddLabel(label, position) {\n\t\tthis.labels[label] = _parsePosition(this, position);\n\t\treturn this;\n\t}\n\n\tremoveLabel(label) {\n\t\tdelete this.labels[label];\n\t\treturn this;\n\t}\n\n\taddPause(position, callback, params) {\n\t\tlet t = Tween.delayedCall(0, callback || _emptyFunc, params);\n\t\tt.data = \"isPause\";\n\t\tthis._hasPause = 1;\n\t\treturn _addToTimeline(this, t, _parsePosition(this, position));\n\t}\n\n\tremovePause(position) {\n\t\tlet child = this._first;\n\t\tposition = _parsePosition(this, position);\n\t\twhile (child) {\n\t\t\tif (child._start === position && child.data === \"isPause\") {\n\t\t\t\t_removeFromParent(child);\n\t\t\t}\n\t\t\tchild = child._next;\n\t\t}\n\t}\n\n\tkillTweensOf(targets, props, onlyActive) {\n\t\tlet tweens = this.getTweensOf(targets, onlyActive),\n\t\t\ti = tweens.length;\n\t\twhile (i--) {\n\t\t\t(_overwritingTween !== tweens[i]) && tweens[i].kill(targets, props);\n\t\t}\n\t\treturn this;\n\t}\n\n\tgetTweensOf(targets, onlyActive) {\n\t\tlet a = [],\n\t\t\tparsedTargets = toArray(targets),\n\t\t\tchild = this._first,\n\t\t\tisGlobalTime = _isNumber(onlyActive), // a number is interpreted as a global time. If the animation spans\n\t\t\tchildren;\n\t\twhile (child) {\n\t\t\tif (child instanceof Tween) {\n\t\t\t\tif (_arrayContainsAny(child._targets, parsedTargets) && (isGlobalTime ? (!_overwritingTween || (child._initted && child._ts)) && child.globalTime(0) <= onlyActive && child.globalTime(child.totalDuration()) > onlyActive : !onlyActive || child.isActive())) { // note: if this is for overwriting, it should only be for tweens that aren't paused and are initted.\n\t\t\t\t\ta.push(child);\n\t\t\t\t}\n\t\t\t} else if ((children = child.getTweensOf(parsedTargets, onlyActive)).length) {\n\t\t\t\ta.push(...children);\n\t\t\t}\n\t\t\tchild = child._next;\n\t\t}\n\t\treturn a;\n\t}\n\n\t// potential future feature - targets() on timelines\n\t// targets() {\n\t// \tlet result = [];\n\t// \tthis.getChildren(true, true, false).forEach(t => result.push(...t.targets()));\n\t// \treturn result.filter((v, i) => result.indexOf(v) === i);\n\t// }\n\n\ttweenTo(position, vars) {\n\t\tvars = vars || {};\n\t\tlet tl = this,\n\t\t\tendTime = _parsePosition(tl, position),\n\t\t\t{ startAt, onStart, onStartParams, immediateRender } = vars,\n\t\t\tinitted,\n\t\t\ttween = Tween.to(tl, _setDefaults({\n\t\t\t\tease: vars.ease || \"none\",\n\t\t\t\tlazy: false,\n\t\t\t\timmediateRender: false,\n\t\t\t\ttime: endTime,\n\t\t\t\toverwrite: \"auto\",\n\t\t\t\tduration: vars.duration || (Math.abs((endTime - ((startAt && \"time\" in startAt) ? startAt.time : tl._time)) / tl.timeScale())) || _tinyNum,\n\t\t\t\tonStart: () => {\n\t\t\t\t\ttl.pause();\n\t\t\t\t\tif (!initted) {\n\t\t\t\t\t\tlet duration = vars.duration || Math.abs((endTime - ((startAt && \"time\" in startAt) ? startAt.time : tl._time)) / tl.timeScale());\n\t\t\t\t\t\t(tween._dur !== duration) && _setDuration(tween, duration, 0, 1).render(tween._time, true, true);\n\t\t\t\t\t\tinitted = 1;\n\t\t\t\t\t}\n\t\t\t\t\tonStart && onStart.apply(tween, onStartParams || []); //in case the user had an onStart in the vars - we don't want to overwrite it.\n\t\t\t\t}\n\t\t\t}, vars));\n\t\treturn immediateRender ? tween.render(0) : tween;\n\t}\n\n\ttweenFromTo(fromPosition, toPosition, vars) {\n\t\treturn this.tweenTo(toPosition, _setDefaults({startAt:{time:_parsePosition(this, fromPosition)}}, vars));\n\t}\n\n\trecent() {\n\t\treturn this._recent;\n\t}\n\n\tnextLabel(afterTime = this._time) {\n\t\treturn _getLabelInDirection(this, _parsePosition(this, afterTime));\n\t}\n\n\tpreviousLabel(beforeTime = this._time) {\n\t\treturn _getLabelInDirection(this, _parsePosition(this, beforeTime), 1);\n\t}\n\n\tcurrentLabel(value) {\n\t\treturn arguments.length ? this.seek(value, true) : this.previousLabel(this._time + _tinyNum);\n\t}\n\n\tshiftChildren(amount, adjustLabels, ignoreBeforeTime = 0) {\n\t\tlet child = this._first,\n\t\t\tlabels = this.labels,\n\t\t\tp;\n\t\twhile (child) {\n\t\t\tif (child._start >= ignoreBeforeTime) {\n\t\t\t\tchild._start += amount;\n\t\t\t\tchild._end += amount;\n\t\t\t}\n\t\t\tchild = child._next;\n\t\t}\n\t\tif (adjustLabels) {\n\t\t\tfor (p in labels) {\n\t\t\t\tif (labels[p] >= ignoreBeforeTime) {\n\t\t\t\t\tlabels[p] += amount;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn _uncache(this);\n\t}\n\n\tinvalidate(soft) {\n\t\tlet child = this._first;\n\t\tthis._lock = 0;\n\t\twhile (child) {\n\t\t\tchild.invalidate(soft);\n\t\t\tchild = child._next;\n\t\t}\n\t\treturn super.invalidate(soft);\n\t}\n\n\tclear(includeLabels = true) {\n\t\tlet child = this._first,\n\t\t\tnext;\n\t\twhile (child) {\n\t\t\tnext = child._next;\n\t\t\tthis.remove(child);\n\t\t\tchild = next;\n\t\t}\n\t\tthis._dp && (this._time = this._tTime = this._pTime = 0);\n\t\tincludeLabels && (this.labels = {});\n\t\treturn _uncache(this);\n\t}\n\n\ttotalDuration(value) {\n\t\tlet max = 0,\n\t\t\tself = this,\n\t\t\tchild = self._last,\n\t\t\tprevStart = _bigNum,\n\t\t\tprev, start, parent;\n\t\tif (arguments.length) {\n\t\t\treturn self.timeScale((self._repeat < 0 ? self.duration() : self.totalDuration()) / (self.reversed() ? -value : value));\n\t\t}\n\t\tif (self._dirty) {\n\t\t\tparent = self.parent;\n\t\t\twhile (child) {\n\t\t\t\tprev = child._prev; //record it here in case the tween changes position in the sequence...\n\t\t\t\tchild._dirty && child.totalDuration(); //could change the tween._startTime, so make sure the animation's cache is clean before analyzing it.\n\t\t\t\tstart = child._start;\n\t\t\t\tif (start > prevStart && self._sort && child._ts && !self._lock) { //in case one of the tweens shifted out of order, it needs to be re-inserted into the correct position in the sequence\n\t\t\t\t\tself._lock = 1; //prevent endless recursive calls - there are methods that get triggered that check duration/totalDuration when we add().\n\t\t\t\t\t_addToTimeline(self, child, start - child._delay, 1)._lock = 0;\n\t\t\t\t} else {\n\t\t\t\t\tprevStart = start;\n\t\t\t\t}\n\t\t\t\tif (start < 0 && child._ts) { //children aren't allowed to have negative startTimes unless smoothChildTiming is true, so adjust here if one is found.\n\t\t\t\t\tmax -= start;\n\t\t\t\t\tif ((!parent && !self._dp) || (parent && parent.smoothChildTiming)) {\n\t\t\t\t\t\tself._start += start / self._ts;\n\t\t\t\t\t\tself._time -= start;\n\t\t\t\t\t\tself._tTime -= start;\n\t\t\t\t\t}\n\t\t\t\t\tself.shiftChildren(-start, false, -1e999);\n\t\t\t\t\tprevStart = 0;\n\t\t\t\t}\n\t\t\t\tchild._end > max && child._ts && (max = child._end);\n\t\t\t\tchild = prev;\n\t\t\t}\n\t\t\t_setDuration(self, (self === _globalTimeline && self._time > max) ? self._time : max, 1, 1);\n\t\t\tself._dirty = 0;\n\t\t}\n\t\treturn self._tDur;\n\t}\n\n\tstatic updateRoot(time) {\n\t\tif (_globalTimeline._ts) {\n\t\t\t_lazySafeRender(_globalTimeline, _parentToChildTotalTime(time, _globalTimeline));\n\t\t\t_lastRenderedFrame = _ticker.frame;\n\t\t}\n\t\tif (_ticker.frame >= _nextGCFrame) {\n\t\t\t_nextGCFrame += _config.autoSleep || 120;\n\t\t\tlet child = _globalTimeline._first;\n\t\t\tif (!child || !child._ts) if (_config.autoSleep && _ticker._listeners.length < 2) {\n\t\t\t\twhile (child && !child._ts) {\n\t\t\t\t\tchild = child._next;\n\t\t\t\t}\n\t\t\t\tchild || _ticker.sleep();\n\t\t\t}\n\t\t}\n\t}\n\n}\n\n_setDefaults(Timeline.prototype, {_lock:0, _hasPause:0, _forcing:0});\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nlet _addComplexStringPropTween = function(target, prop, start, end, setter, stringFilter, funcParam) { //note: we call _addComplexStringPropTween.call(tweenInstance...) to ensure that it's scoped properly. We may call it from within a plugin too, thus \"this\" would refer to the plugin.\n\t\tlet pt = new PropTween(this._pt, target, prop, 0, 1, _renderComplexString, null, setter),\n\t\t\tindex = 0,\n\t\t\tmatchIndex = 0,\n\t\t\tresult,\tstartNums, color, endNum, chunk, startNum, hasRandom, a;\n\t\tpt.b = start;\n\t\tpt.e = end;\n\t\tstart += \"\"; //ensure values are strings\n\t\tend += \"\";\n\t\tif ((hasRandom = ~end.indexOf(\"random(\"))) {\n\t\t\tend = _replaceRandom(end);\n\t\t}\n\t\tif (stringFilter) {\n\t\t\ta = [start, end];\n\t\t\tstringFilter(a, target, prop); //pass an array with the starting and ending values and let the filter do whatever it needs to the values.\n\t\t\tstart = a[0];\n\t\t\tend = a[1];\n\t\t}\n\t\tstartNums = start.match(_complexStringNumExp) || [];\n\t\twhile ((result = _complexStringNumExp.exec(end))) {\n\t\t\tendNum = result[0];\n\t\t\tchunk = end.substring(index, result.index);\n\t\t\tif (color) {\n\t\t\t\tcolor = (color + 1) % 5;\n\t\t\t} else if (chunk.substr(-5) === \"rgba(\") {\n\t\t\t\tcolor = 1;\n\t\t\t}\n\t\t\tif (endNum !== startNums[matchIndex++]) {\n\t\t\t\tstartNum = parseFloat(startNums[matchIndex-1]) || 0;\n\t\t\t\t//these nested PropTweens are handled in a special way - we'll never actually call a render or setter method on them. We'll just loop through them in the parent complex string PropTween's render method.\n\t\t\t\tpt._pt = {\n\t\t\t\t\t_next: pt._pt,\n\t\t\t\t\tp: (chunk || matchIndex === 1) ? chunk : \",\", //note: SVG spec allows omission of comma/space when a negative sign is wedged between two numbers, like 2.5-5.3 instead of 2.5,-5.3 but when tweening, the negative value may switch to positive, so we insert the comma just in case.\n\t\t\t\t\ts: startNum,\n\t\t\t\t\tc: endNum.charAt(1) === \"=\" ? _parseRelative(startNum, endNum) - startNum : parseFloat(endNum) - startNum,\n\t\t\t\t\tm: (color && color < 4) ? Math.round : 0\n\t\t\t\t};\n\t\t\t\tindex = _complexStringNumExp.lastIndex;\n\t\t\t}\n\t\t}\n\t\tpt.c = (index < end.length) ? end.substring(index, end.length) : \"\"; //we use the \"c\" of the PropTween to store the final part of the string (after the last number)\n\t\tpt.fp = funcParam;\n\t\tif (_relExp.test(end) || hasRandom) {\n\t\t\tpt.e = 0; //if the end string contains relative values or dynamic random(...) values, delete the end it so that on the final render we don't actually set it to the string with += or -= characters (forces it to use the calculated value).\n\t\t}\n\t\tthis._pt = pt; //start the linked list with this new PropTween. Remember, we call _addComplexStringPropTween.call(tweenInstance...) to ensure that it's scoped properly. We may call it from within a plugin too, thus \"this\" would refer to the plugin.\n\t\treturn pt;\n\t},\n\t_addPropTween = function(target, prop, start, end, index, targets, modifier, stringFilter, funcParam, optional) {\n\t\t_isFunction(end) && (end = end(index || 0, target, targets));\n\t\tlet currentValue = target[prop],\n\t\t\tparsedStart = (start !== \"get\") ? start : !_isFunction(currentValue) ? currentValue : (funcParam ? target[(prop.indexOf(\"set\") || !_isFunction(target[\"get\" + prop.substr(3)])) ? prop : \"get\" + prop.substr(3)](funcParam) : target[prop]()),\n\t\t\tsetter = !_isFunction(currentValue) ? _setterPlain : funcParam ? _setterFuncWithParam : _setterFunc,\n\t\t\tpt;\n\t\tif (_isString(end)) {\n\t\t\tif (~end.indexOf(\"random(\")) {\n\t\t\t\tend = _replaceRandom(end);\n\t\t\t}\n\t\t\tif (end.charAt(1) === \"=\") {\n\t\t\t\tpt = _parseRelative(parsedStart, end) + (getUnit(parsedStart) || 0);\n\t\t\t\tif (pt || pt === 0) { // to avoid isNaN, like if someone passes in a value like \"!= whatever\"\n\t\t\t\t\tend = pt;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (!optional || parsedStart !== end || _forceAllPropTweens) {\n\t\t\tif (!isNaN(parsedStart * end) && end !== \"\") { // fun fact: any number multiplied by \"\" is evaluated as the number 0!\n\t\t\t\tpt = new PropTween(this._pt, target, prop, +parsedStart || 0, end - (parsedStart || 0), typeof(currentValue) === \"boolean\" ? _renderBoolean : _renderPlain, 0, setter);\n\t\t\t\tfuncParam && (pt.fp = funcParam);\n\t\t\t\tmodifier && pt.modifier(modifier, this, target);\n\t\t\t\treturn (this._pt = pt);\n\t\t\t}\n\t\t\t!currentValue && !(prop in target) && _missingPlugin(prop, end);\n\t\t\treturn _addComplexStringPropTween.call(this, target, prop, parsedStart, end, setter, stringFilter || _config.stringFilter, funcParam);\n\t\t}\n\t},\n\t//creates a copy of the vars object and processes any function-based values (putting the resulting values directly into the copy) as well as strings with \"random()\" in them. It does NOT process relative values.\n\t_processVars = (vars, index, target, targets, tween) => {\n\t\t_isFunction(vars) && (vars = _parseFuncOrString(vars, tween, index, target, targets));\n\t\tif (!_isObject(vars) || (vars.style && vars.nodeType) || _isArray(vars) || _isTypedArray(vars)) {\n\t\t\treturn _isString(vars) ? _parseFuncOrString(vars, tween, index, target, targets) : vars;\n\t\t}\n\t\tlet copy = {},\n\t\t\tp;\n\t\tfor (p in vars) {\n\t\t\tcopy[p] = _parseFuncOrString(vars[p], tween, index, target, targets);\n\t\t}\n\t\treturn copy;\n\t},\n\t_checkPlugin = (property, vars, tween, index, target, targets) => {\n\t\tlet plugin, pt, ptLookup, i;\n\t\tif (_plugins[property] && (plugin = new _plugins[property]()).init(target, plugin.rawVars ? vars[property] : _processVars(vars[property], index, target, targets, tween), tween, index, targets) !== false) {\n\t\t\ttween._pt = pt = new PropTween(tween._pt, target, property, 0, 1, plugin.render, plugin, 0, plugin.priority);\n\t\t\tif (tween !== _quickTween) {\n\t\t\t\tptLookup = tween._ptLookup[tween._targets.indexOf(target)]; //note: we can't use tween._ptLookup[index] because for staggered tweens, the index from the fullTargets array won't match what it is in each individual tween that spawns from the stagger.\n\t\t\t\ti = plugin._props.length;\n\t\t\t\twhile (i--) {\n\t\t\t\t\tptLookup[plugin._props[i]] = pt;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn plugin;\n\t},\n\t_overwritingTween, //store a reference temporarily so we can avoid overwriting itself.\n\t_forceAllPropTweens,\n\t_initTween = (tween, time, tTime) => {\n\t\tlet vars = tween.vars,\n\t\t\t{ ease, startAt, immediateRender, lazy, onUpdate, onUpdateParams, callbackScope, runBackwards, yoyoEase, keyframes, autoRevert } = vars,\n\t\t\tdur = tween._dur,\n\t\t\tprevStartAt = tween._startAt,\n\t\t\ttargets = tween._targets,\n\t\t\tparent = tween.parent,\n\t\t\t//when a stagger (or function-based duration/delay) is on a Tween instance, we create a nested timeline which means that the \"targets\" of that tween don't reflect the parent. This function allows us to discern when it's a nested tween and in that case, return the full targets array so that function-based values get calculated properly. Also remember that if the tween has a stagger AND keyframes, it could be multiple levels deep which is why we store the targets Array in the vars of the timeline.\n\t\t\tfullTargets = (parent && parent.data === \"nested\") ? parent.vars.targets : targets,\n\t\t\tautoOverwrite = (tween._overwrite === \"auto\") && !_suppressOverwrites,\n\t\t\ttl = tween.timeline,\n\t\t\tcleanVars, i, p, pt, target, hasPriority, gsData, harness, plugin, ptLookup, index, harnessVars, overwritten;\n\t\ttl && (!keyframes || !ease) && (ease = \"none\");\n\t\ttween._ease = _parseEase(ease, _defaults.ease);\n\t\ttween._yEase = yoyoEase ? _invertEase(_parseEase(yoyoEase === true ? ease : yoyoEase, _defaults.ease)) : 0;\n\t\tif (yoyoEase && tween._yoyo && !tween._repeat) { //there must have been a parent timeline with yoyo:true that is currently in its yoyo phase, so flip the eases.\n\t\t\tyoyoEase = tween._yEase;\n\t\t\ttween._yEase = tween._ease;\n\t\t\ttween._ease = yoyoEase;\n\t\t}\n\t\ttween._from = !tl && !!vars.runBackwards; //nested timelines should never run backwards - the backwards-ness is in the child tweens.\n\t\tif (!tl || (keyframes && !vars.stagger)) { //if there's an internal timeline, skip all the parsing because we passed that task down the chain.\n\t\t\tharness = targets[0] ? _getCache(targets[0]).harness : 0;\n\t\t\tharnessVars = harness && vars[harness.prop]; //someone may need to specify CSS-specific values AND non-CSS values, like if the element has an \"x\" property plus it's a standard DOM element. We allow people to distinguish by wrapping plugin-specific stuff in a css:{} object for example.\n\t\t\tcleanVars = _copyExcluding(vars, _reservedProps);\n\t\t\tif (prevStartAt) {\n\t\t\t\tprevStartAt._zTime < 0 && prevStartAt.progress(1); // in case it's a lazy startAt that hasn't rendered yet.\n\t\t\t\t(time < 0 && runBackwards && immediateRender && !autoRevert) ? prevStartAt.render(-1, true) : prevStartAt.revert(runBackwards && dur ? _revertConfigNoKill : _startAtRevertConfig); // if it's a \"startAt\" (not \"from()\" or runBackwards: true), we only need to do a shallow revert (keep transforms cached in CSSPlugin)\n\t\t\t\t// don't just _removeFromParent(prevStartAt.render(-1, true)) because that'll leave inline styles. We're creating a new _startAt for \"startAt\" tweens that re-capture things to ensure that if the pre-tween values changed since the tween was created, they're recorded.\n\t\t\t\tprevStartAt._lazy = 0;\n\t\t\t}\n\t\t\tif (startAt) {\n\t\t\t\t_removeFromParent(tween._startAt = Tween.set(targets, _setDefaults({data: \"isStart\", overwrite: false, parent: parent, immediateRender: true, lazy: !prevStartAt && _isNotFalse(lazy), startAt: null, delay: 0, onUpdate: onUpdate, onUpdateParams: onUpdateParams, callbackScope: callbackScope, stagger: 0}, startAt))); //copy the properties/values into a new object to avoid collisions, like var to = {x:0}, from = {x:500}; timeline.fromTo(e, from, to).fromTo(e, to, from);\n\t\t\t\ttween._startAt._dp = 0; // don't allow it to get put back into root timeline! Like when revert() is called and totalTime() gets set.\n\t\t\t\ttween._startAt._sat = tween; // used in globalTime(). _sat stands for _startAtTween\n\t\t\t\t(time < 0 && (_reverting || (!immediateRender && !autoRevert))) && tween._startAt.revert(_revertConfigNoKill); // rare edge case, like if a render is forced in the negative direction of a non-initted tween.\n\t\t\t\tif (immediateRender) {\n\t\t\t\t\tif (dur && time <= 0 && tTime <= 0) { // check tTime here because in the case of a yoyo tween whose playhead gets pushed to the end like tween.progress(1), we should allow it through so that the onComplete gets fired properly.\n\t\t\t\t\t\ttime && (tween._zTime = time);\n\t\t\t\t\t\treturn; //we skip initialization here so that overwriting doesn't occur until the tween actually begins. Otherwise, if you create several immediateRender:true tweens of the same target/properties to drop into a Timeline, the last one created would overwrite the first ones because they didn't get placed into the timeline yet before the first render occurs and kicks in overwriting.\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else if (runBackwards && dur) {\n\t\t\t\t//from() tweens must be handled uniquely: their beginning values must be rendered but we don't want overwriting to occur yet (when time is still 0). Wait until the tween actually begins before doing all the routines like overwriting. At that time, we should render at the END of the tween to ensure that things initialize correctly (remember, from() tweens go backwards)\n\t\t\t\tif (!prevStartAt) {\n\t\t\t\t\ttime && (immediateRender = false); //in rare cases (like if a from() tween runs and then is invalidate()-ed), immediateRender could be true but the initial forced-render gets skipped, so there's no need to force the render in this context when the _time is greater than 0\n\t\t\t\t\tp = _setDefaults({\n\t\t\t\t\t\toverwrite: false,\n\t\t\t\t\t\tdata: \"isFromStart\", //we tag the tween with as \"isFromStart\" so that if [inside a plugin] we need to only do something at the very END of a tween, we have a way of identifying this tween as merely the one that's setting the beginning values for a \"from()\" tween. For example, clearProps in CSSPlugin should only get applied at the very END of a tween and without this tag, from(...{height:100, clearProps:\"height\", delay:1}) would wipe the height at the beginning of the tween and after 1 second, it'd kick back in.\n\t\t\t\t\t\tlazy: immediateRender && !prevStartAt && _isNotFalse(lazy),\n\t\t\t\t\t\timmediateRender: immediateRender, //zero-duration tweens render immediately by default, but if we're not specifically instructed to render this tween immediately, we should skip this and merely _init() to record the starting values (rendering them immediately would push them to completion which is wasteful in that case - we'd have to render(-1) immediately after)\n\t\t\t\t\t\tstagger: 0,\n\t\t\t\t\t\tparent: parent //ensures that nested tweens that had a stagger are handled properly, like gsap.from(\".class\", {y: gsap.utils.wrap([-100,100]), stagger: 0.5})\n\t\t\t\t\t}, cleanVars);\n\t\t\t\t\tharnessVars && (p[harness.prop] = harnessVars); // in case someone does something like .from(..., {css:{}})\n\t\t\t\t\t_removeFromParent(tween._startAt = Tween.set(targets, p));\n\t\t\t\t\ttween._startAt._dp = 0; // don't allow it to get put back into root timeline!\n\t\t\t\t\ttween._startAt._sat = tween; // used in globalTime()\n\t\t\t\t\t(time < 0) && (_reverting ? tween._startAt.revert(_revertConfigNoKill) : tween._startAt.render(-1, true));\n\t\t\t\t\ttween._zTime = time;\n\t\t\t\t\tif (!immediateRender) {\n\t\t\t\t\t\t_initTween(tween._startAt, _tinyNum, _tinyNum); //ensures that the initial values are recorded\n\t\t\t\t\t} else if (!time) {\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\ttween._pt = tween._ptCache = 0;\n\t\t\tlazy = (dur && _isNotFalse(lazy)) || (lazy && !dur);\n\t\t\tfor (i = 0; i < targets.length; i++) {\n\t\t\t\ttarget = targets[i];\n\t\t\t\tgsData = target._gsap || _harness(targets)[i]._gsap;\n\t\t\t\ttween._ptLookup[i] = ptLookup = {};\n\t\t\t\t_lazyLookup[gsData.id] && _lazyTweens.length && _lazyRender(); //if other tweens of the same target have recently initted but haven't rendered yet, we've got to force the render so that the starting values are correct (imagine populating a timeline with a bunch of sequential tweens and then jumping to the end)\n\t\t\t\tindex = fullTargets === targets ? i : fullTargets.indexOf(target);\n\t\t\t\tif (harness && (plugin = new harness()).init(target, harnessVars || cleanVars, tween, index, fullTargets) !== false) {\n\t\t\t\t\ttween._pt = pt = new PropTween(tween._pt, target, plugin.name, 0, 1, plugin.render, plugin, 0, plugin.priority);\n\t\t\t\t\tplugin._props.forEach(name => {ptLookup[name] = pt;});\n\t\t\t\t\tplugin.priority && (hasPriority = 1);\n\t\t\t\t}\n\t\t\t\tif (!harness || harnessVars) {\n\t\t\t\t\tfor (p in cleanVars) {\n\t\t\t\t\t\tif (_plugins[p] && (plugin = _checkPlugin(p, cleanVars, tween, index, target, fullTargets))) {\n\t\t\t\t\t\t\tplugin.priority && (hasPriority = 1);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tptLookup[p] = pt = _addPropTween.call(tween, target, p, \"get\", cleanVars[p], index, fullTargets, 0, vars.stringFilter);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\ttween._op && tween._op[i] && tween.kill(target, tween._op[i]);\n\t\t\t\tif (autoOverwrite && tween._pt) {\n\t\t\t\t\t_overwritingTween = tween;\n\t\t\t\t\t_globalTimeline.killTweensOf(target, ptLookup, tween.globalTime(time)); // make sure the overwriting doesn't overwrite THIS tween!!!\n\t\t\t\t\toverwritten = !tween.parent;\n\t\t\t\t\t_overwritingTween = 0;\n\t\t\t\t}\n\t\t\t\ttween._pt && lazy && (_lazyLookup[gsData.id] = 1);\n\t\t\t}\n\t\t\thasPriority && _sortPropTweensByPriority(tween);\n\t\t\ttween._onInit && tween._onInit(tween); //plugins like RoundProps must wait until ALL of the PropTweens are instantiated. In the plugin's init() function, it sets the _onInit on the tween instance. May not be pretty/intuitive, but it's fast and keeps file size down.\n\t\t}\n\t\ttween._onUpdate = onUpdate;\n\t\ttween._initted = (!tween._op || tween._pt) && !overwritten; // if overwrittenProps resulted in the entire tween being killed, do NOT flag it as initted or else it may render for one tick.\n\t\t(keyframes && time <= 0) && tl.render(_bigNum, true, true); // if there's a 0% keyframe, it'll render in the \"before\" state for any staggered/delayed animations thus when the following tween initializes, it'll use the \"before\" state instead of the \"after\" state as the initial values.\n\t},\n\t_updatePropTweens = (tween, property, value, start, startIsRelative, ratio, time) => {\n\t\tlet ptCache = ((tween._pt && tween._ptCache) || (tween._ptCache = {}))[property],\n\t\t\tpt, rootPT, lookup, i;\n\t\tif (!ptCache) {\n\t\t\tptCache = tween._ptCache[property] = [];\n\t\t\tlookup = tween._ptLookup;\n\t\t\ti = tween._targets.length;\n\t\t\twhile (i--) {\n\t\t\t\tpt = lookup[i][property];\n\t\t\t\tif (pt && pt.d && pt.d._pt) { // it's a plugin, so find the nested PropTween\n\t\t\t\t\tpt = pt.d._pt;\n\t\t\t\t\twhile (pt && pt.p !== property && pt.fp !== property) { // \"fp\" is functionParam for things like setting CSS variables which require .setProperty(\"--var-name\", value)\n\t\t\t\t\t\tpt = pt._next;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (!pt) { // there is no PropTween associated with that property, so we must FORCE one to be created and ditch out of this\n\t\t\t\t\t// if the tween has other properties that already rendered at new positions, we'd normally have to rewind to put them back like tween.render(0, true) before forcing an _initTween(), but that can create another edge case like tweening a timeline's progress would trigger onUpdates to fire which could move other things around. It's better to just inform users that .resetTo() should ONLY be used for tweens that already have that property. For example, you can't gsap.to(...{ y: 0 }) and then tween.restTo(\"x\", 200) for example.\n\t\t\t\t\t_forceAllPropTweens = 1; // otherwise, when we _addPropTween() and it finds no change between the start and end values, it skips creating a PropTween (for efficiency...why tween when there's no difference?) but in this case we NEED that PropTween created so we can edit it.\n\t\t\t\t\ttween.vars[property] = \"+=0\";\n\t\t\t\t\t_initTween(tween, time);\n\t\t\t\t\t_forceAllPropTweens = 0;\n\t\t\t\t\treturn 1;\n\t\t\t\t}\n\t\t\t\tptCache.push(pt);\n\t\t\t}\n\t\t}\n\t\ti = ptCache.length;\n\t\twhile (i--) {\n\t\t\trootPT = ptCache[i];\n\t\t\tpt = rootPT._pt || rootPT; // complex values may have nested PropTweens. We only accommodate the FIRST value.\n\t\t\tpt.s = (start || start === 0) && !startIsRelative ? start : pt.s + (start || 0) + ratio * pt.c;\n\t\t\tpt.c = value - pt.s;\n\t\t\trootPT.e && (rootPT.e = _round(value) + getUnit(rootPT.e)); // mainly for CSSPlugin (end value)\n\t\t\trootPT.b && (rootPT.b = pt.s + getUnit(rootPT.b));          // (beginning value)\n\t\t}\n\t},\n\t_addAliasesToVars = (targets, vars) => {\n\t\tlet harness = targets[0] ? _getCache(targets[0]).harness : 0,\n\t\t\tpropertyAliases = (harness && harness.aliases),\n\t\t\tcopy, p, i, aliases;\n\t\tif (!propertyAliases) {\n\t\t\treturn vars;\n\t\t}\n\t\tcopy = _merge({}, vars);\n\t\tfor (p in propertyAliases) {\n\t\t\tif (p in copy) {\n\t\t\t\taliases = propertyAliases[p].split(\",\");\n\t\t\t\ti = aliases.length;\n\t\t\t\twhile(i--) {\n\t\t\t\t\tcopy[aliases[i]] = copy[p];\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\t\treturn copy;\n\t},\n\t// parses multiple formats, like {\"0%\": {x: 100}, {\"50%\": {x: -20}} and { x: {\"0%\": 100, \"50%\": -20} }, and an \"ease\" can be set on any object. We populate an \"allProps\" object with an Array for each property, like {x: [{}, {}], y:[{}, {}]} with data for each property tween. The objects have a \"t\" (time), \"v\", (value), and \"e\" (ease) property. This allows us to piece together a timeline later.\n\t_parseKeyframe = (prop, obj, allProps, easeEach) => {\n\t\tlet ease = obj.ease || easeEach || \"power1.inOut\",\n\t\t\tp, a;\n\t\tif (_isArray(obj)) {\n\t\t\ta = allProps[prop] || (allProps[prop] = []);\n\t\t\t// t = time (out of 100), v = value, e = ease\n\t\t\tobj.forEach((value, i) => a.push({t: i / (obj.length - 1) * 100, v: value, e: ease}));\n\t\t} else {\n\t\t\tfor (p in obj) {\n\t\t\t\ta = allProps[p] || (allProps[p] = []);\n\t\t\t\tp === \"ease\" || a.push({t: parseFloat(prop), v: obj[p], e: ease});\n\t\t\t}\n\t\t}\n\t},\n\t_parseFuncOrString = (value, tween, i, target, targets) => (_isFunction(value) ? value.call(tween, i, target, targets) : (_isString(value) && ~value.indexOf(\"random(\")) ? _replaceRandom(value) : value),\n\t_staggerTweenProps = _callbackNames + \"repeat,repeatDelay,yoyo,repeatRefresh,yoyoEase,autoRevert\",\n\t_staggerPropsToSkip = {};\n_forEachName(_staggerTweenProps + \",id,stagger,delay,duration,paused,scrollTrigger\", name => _staggerPropsToSkip[name] = 1);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/*\n * --------------------------------------------------------------------------------------\n * TWEEN\n * --------------------------------------------------------------------------------------\n */\n\nexport class Tween extends Animation {\n\n\tconstructor(targets, vars, position, skipInherit) {\n\t\tif (typeof(vars) === \"number\") {\n\t\t\tposition.duration = vars;\n\t\t\tvars = position;\n\t\t\tposition = null;\n\t\t}\n\t\tsuper(skipInherit ? vars : _inheritDefaults(vars));\n\t\tlet { duration, delay, immediateRender, stagger, overwrite, keyframes, defaults, scrollTrigger, yoyoEase } = this.vars,\n\t\t\tparent = vars.parent || _globalTimeline,\n\t\t\tparsedTargets = (_isArray(targets) || _isTypedArray(targets) ? _isNumber(targets[0]) : (\"length\" in vars)) ? [targets] : toArray(targets), // edge case: someone might try animating the \"length\" of an object with a \"length\" property that's initially set to 0 so don't interpret that as an empty Array-like object.\n\t\t\ttl, i, copy, l, p, curTarget, staggerFunc, staggerVarsToMerge;\n\t\tthis._targets = parsedTargets.length ? _harness(parsedTargets) : _warn(\"GSAP target \" + targets + \" not found. https://greensock.com\", !_config.nullTargetWarn) || [];\n\t\tthis._ptLookup = []; //PropTween lookup. An array containing an object for each target, having keys for each tweening property\n\t\tthis._overwrite = overwrite;\n\t\tif (keyframes || stagger || _isFuncOrString(duration) || _isFuncOrString(delay)) {\n\t\t\tvars = this.vars;\n\t\t\ttl = this.timeline = new Timeline({data: \"nested\", defaults: defaults || {}, targets: parent && parent.data === \"nested\" ? parent.vars.targets : parsedTargets}); // we need to store the targets because for staggers and keyframes, we end up creating an individual tween for each but function-based values need to know the index and the whole Array of targets.\n\t\t\ttl.kill();\n\t\t\ttl.parent = tl._dp = this;\n\t\t\ttl._start = 0;\n\t\t\tif (stagger || _isFuncOrString(duration) || _isFuncOrString(delay)) {\n\t\t\t\tl = parsedTargets.length;\n\t\t\t\tstaggerFunc = stagger && distribute(stagger);\n\t\t\t\tif (_isObject(stagger)) { //users can pass in callbacks like onStart/onComplete in the stagger object. These should fire with each individual tween.\n\t\t\t\t\tfor (p in stagger) {\n\t\t\t\t\t\tif (~_staggerTweenProps.indexOf(p)) {\n\t\t\t\t\t\t\tstaggerVarsToMerge || (staggerVarsToMerge = {});\n\t\t\t\t\t\t\tstaggerVarsToMerge[p] = stagger[p];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tfor (i = 0; i < l; i++) {\n\t\t\t\t\tcopy = _copyExcluding(vars, _staggerPropsToSkip);\n\t\t\t\t\tcopy.stagger = 0;\n\t\t\t\t\tyoyoEase && (copy.yoyoEase = yoyoEase);\n\t\t\t\t\tstaggerVarsToMerge && _merge(copy, staggerVarsToMerge);\n\t\t\t\t\tcurTarget = parsedTargets[i];\n\t\t\t\t\t//don't just copy duration or delay because if they're a string or function, we'd end up in an infinite loop because _isFuncOrString() would evaluate as true in the child tweens, entering this loop, etc. So we parse the value straight from vars and default to 0.\n\t\t\t\t\tcopy.duration = +_parseFuncOrString(duration, this, i, curTarget, parsedTargets);\n\t\t\t\t\tcopy.delay = (+_parseFuncOrString(delay, this, i, curTarget, parsedTargets) || 0) - this._delay;\n\t\t\t\t\tif (!stagger && l === 1 && copy.delay) { // if someone does delay:\"random(1, 5)\", repeat:-1, for example, the delay shouldn't be inside the repeat.\n\t\t\t\t\t\tthis._delay = delay = copy.delay;\n\t\t\t\t\t\tthis._start += delay;\n\t\t\t\t\t\tcopy.delay = 0;\n\t\t\t\t\t}\n\t\t\t\t\ttl.to(curTarget, copy, staggerFunc ? staggerFunc(i, curTarget, parsedTargets) : 0);\n\t\t\t\t\ttl._ease = _easeMap.none;\n\t\t\t\t}\n\t\t\t\ttl.duration() ? (duration = delay = 0) : (this.timeline = 0); // if the timeline's duration is 0, we don't need a timeline internally!\n\t\t\t} else if (keyframes) {\n\t\t\t\t_inheritDefaults(_setDefaults(tl.vars.defaults, {ease:\"none\"}));\n\t\t\t\ttl._ease = _parseEase(keyframes.ease || vars.ease || \"none\");\n\t\t\t\tlet time = 0,\n\t\t\t\t\ta, kf, v;\n\t\t\t\tif (_isArray(keyframes)) {\n\t\t\t\t\tkeyframes.forEach(frame => tl.to(parsedTargets, frame, \">\"));\n\t\t\t\t\ttl.duration(); // to ensure tl._dur is cached because we tap into it for performance purposes in the render() method.\n\t\t\t\t} else {\n\t\t\t\t\tcopy = {};\n\t\t\t\t\tfor (p in keyframes) {\n\t\t\t\t\t\tp === \"ease\" || p === \"easeEach\" || _parseKeyframe(p, keyframes[p], copy, keyframes.easeEach);\n\t\t\t\t\t}\n\t\t\t\t\tfor (p in copy) {\n\t\t\t\t\t\ta = copy[p].sort((a, b) => a.t - b.t);\n\t\t\t\t\t\ttime = 0;\n\t\t\t\t\t\tfor (i = 0; i < a.length; i++) {\n\t\t\t\t\t\t\tkf = a[i];\n\t\t\t\t\t\t\tv = {ease: kf.e, duration: (kf.t - (i ? a[i - 1].t : 0)) / 100 * duration};\n\t\t\t\t\t\t\tv[p] = kf.v;\n\t\t\t\t\t\t\ttl.to(parsedTargets, v, time);\n\t\t\t\t\t\t\ttime += v.duration;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\ttl.duration() < duration && tl.to({}, {duration: duration - tl.duration()}); // in case keyframes didn't go to 100%\n\t\t\t\t}\n\t\t\t}\n\t\t\tduration || this.duration((duration = tl.duration()));\n\n\t\t} else {\n\t\t\tthis.timeline = 0; //speed optimization, faster lookups (no going up the prototype chain)\n\t\t}\n\n\t\tif (overwrite === true && !_suppressOverwrites) {\n\t\t\t_overwritingTween = this;\n\t\t\t_globalTimeline.killTweensOf(parsedTargets);\n\t\t\t_overwritingTween = 0;\n\t\t}\n\t\t_addToTimeline(parent, this, position);\n\t\tvars.reversed && this.reverse();\n\t\tvars.paused && this.paused(true);\n\t\tif (immediateRender || (!duration && !keyframes && this._start === _roundPrecise(parent._time) && _isNotFalse(immediateRender) && _hasNoPausedAncestors(this) && parent.data !== \"nested\")) {\n\t\t\tthis._tTime = -_tinyNum; //forces a render without having to set the render() \"force\" parameter to true because we want to allow lazying by default (using the \"force\" parameter always forces an immediate full render)\n\t\t\tthis.render(Math.max(0, -delay) || 0); //in case delay is negative\n\t\t}\n\t\tscrollTrigger && _scrollTrigger(this, scrollTrigger);\n\t}\n\n\trender(totalTime, suppressEvents, force) {\n\t\tlet prevTime = this._time,\n\t\t\ttDur = this._tDur,\n\t\t\tdur = this._dur,\n\t\t\tisNegative = totalTime < 0,\n\t\t\ttTime = (totalTime > tDur - _tinyNum && !isNegative) ? tDur : (totalTime < _tinyNum) ? 0 : totalTime,\n\t\t\ttime, pt, iteration, cycleDuration, prevIteration, isYoyo, ratio, timeline, yoyoEase;\n\t\tif (!dur) {\n\t\t\t_renderZeroDurationTween(this, totalTime, suppressEvents, force);\n\t\t} else if (tTime !== this._tTime || !totalTime || force || (!this._initted && this._tTime) || (this._startAt && (this._zTime < 0) !== isNegative)) { //this senses if we're crossing over the start time, in which case we must record _zTime and force the render, but we do it in this lengthy conditional way for performance reasons (usually we can skip the calculations): this._initted && (this._zTime < 0) !== (totalTime < 0)\n\t\t\ttime = tTime;\n\t\t\ttimeline = this.timeline;\n\t\t\tif (this._repeat) { //adjust the time for repeats and yoyos\n\t\t\t\tcycleDuration = dur + this._rDelay;\n\t\t\t\tif (this._repeat < -1 && isNegative) {\n\t\t\t\t\treturn this.totalTime(cycleDuration * 100 + totalTime, suppressEvents, force);\n\t\t\t\t}\n\t\t\t\ttime = _roundPrecise(tTime % cycleDuration); //round to avoid floating point errors. (4 % 0.8 should be 0 but some browsers report it as 0.79999999!)\n\t\t\t\tif (tTime === tDur) { // the tDur === tTime is for edge cases where there's a lengthy decimal on the duration and it may reach the very end but the time is rendered as not-quite-there (remember, tDur is rounded to 4 decimals whereas dur isn't)\n\t\t\t\t\titeration = this._repeat;\n\t\t\t\t\ttime = dur;\n\t\t\t\t} else {\n\t\t\t\t\titeration = ~~(tTime / cycleDuration);\n\t\t\t\t\tif (iteration && iteration === tTime / cycleDuration) {\n\t\t\t\t\t\ttime = dur;\n\t\t\t\t\t\titeration--;\n\t\t\t\t\t}\n\t\t\t\t\ttime > dur && (time = dur);\n\t\t\t\t}\n\t\t\t\tisYoyo = this._yoyo && (iteration & 1);\n\t\t\t\tif (isYoyo) {\n\t\t\t\t\tyoyoEase = this._yEase;\n\t\t\t\t\ttime = dur - time;\n\t\t\t\t}\n\t\t\t\tprevIteration = _animationCycle(this._tTime, cycleDuration);\n\t\t\t\tif (time === prevTime && !force && this._initted) {\n\t\t\t\t\t//could be during the repeatDelay part. No need to render and fire callbacks.\n\t\t\t\t\tthis._tTime = tTime;\n\t\t\t\t\treturn this;\n\t\t\t\t}\n\t\t\t\tif (iteration !== prevIteration) {\n\t\t\t\t\ttimeline && this._yEase && _propagateYoyoEase(timeline, isYoyo);\n\t\t\t\t\t//repeatRefresh functionality\n\t\t\t\t\tif (this.vars.repeatRefresh && !isYoyo && !this._lock) {\n\t\t\t\t\t\tthis._lock = force = 1; //force, otherwise if lazy is true, the _attemptInitTween() will return and we'll jump out and get caught bouncing on each tick.\n\t\t\t\t\t\tthis.render(_roundPrecise(cycleDuration * iteration), true).invalidate()._lock = 0;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (!this._initted) {\n\t\t\t\tif (_attemptInitTween(this, isNegative ? totalTime : time, force, suppressEvents, tTime)) {\n\t\t\t\t\tthis._tTime = 0; // in constructor if immediateRender is true, we set _tTime to -_tinyNum to have the playhead cross the starting point but we can't leave _tTime as a negative number.\n\t\t\t\t\treturn this;\n\t\t\t\t}\n\t\t\t\tif (prevTime !== this._time) { // rare edge case - during initialization, an onUpdate in the _startAt (.fromTo()) might force this tween to render at a different spot in which case we should ditch this render() call so that it doesn't revert the values.\n\t\t\t\t\treturn this;\n\t\t\t\t}\n\t\t\t\tif (dur !== this._dur) { // while initting, a plugin like InertiaPlugin might alter the duration, so rerun from the start to ensure everything renders as it should.\n\t\t\t\t\treturn this.render(totalTime, suppressEvents, force);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tthis._tTime = tTime;\n\t\t\tthis._time = time;\n\n\t\t\tif (!this._act && this._ts) {\n\t\t\t\tthis._act = 1; //as long as it's not paused, force it to be active so that if the user renders independent of the parent timeline, it'll be forced to re-render on the next tick.\n\t\t\t\tthis._lazy = 0;\n\t\t\t}\n\n\t\t\tthis.ratio = ratio = (yoyoEase || this._ease)(time / dur);\n\t\t\tif (this._from) {\n\t\t\t\tthis.ratio = ratio = 1 - ratio;\n\t\t\t}\n\n\t\t\tif (time && !prevTime && !suppressEvents && !iteration) {\n\t\t\t\t_callback(this, \"onStart\");\n\t\t\t\tif (this._tTime !== tTime) { // in case the onStart triggered a render at a different spot, eject. Like if someone did animation.pause(0.5) or something inside the onStart.\n\t\t\t\t\treturn this;\n\t\t\t\t}\n\t\t\t}\n\t\t\tpt = this._pt;\n\t\t\twhile (pt) {\n\t\t\t\tpt.r(ratio, pt.d);\n\t\t\t\tpt = pt._next;\n\t\t\t}\n\t\t\t(timeline && timeline.render(totalTime < 0 ? totalTime : !time && isYoyo ? -_tinyNum : timeline._dur * timeline._ease(time / this._dur), suppressEvents, force)) || (this._startAt && (this._zTime = totalTime));\n\n\t\t\tif (this._onUpdate && !suppressEvents) {\n\t\t\t\tisNegative && _rewindStartAt(this, totalTime, suppressEvents, force); //note: for performance reasons, we tuck this conditional logic inside less traveled areas (most tweens don't have an onUpdate). We'd just have it at the end before the onComplete, but the values should be updated before any onUpdate is called, so we ALSO put it here and then if it's not called, we do so later near the onComplete.\n\t\t\t\t_callback(this, \"onUpdate\");\n\t\t\t}\n\n\t\t\tthis._repeat && iteration !== prevIteration && this.vars.onRepeat && !suppressEvents && this.parent && _callback(this, \"onRepeat\");\n\n\t\t\tif ((tTime === this._tDur || !tTime) && this._tTime === tTime) {\n\t\t\t\tisNegative && !this._onUpdate && _rewindStartAt(this, totalTime, true, true);\n\t\t\t\t(totalTime || !dur) && ((tTime === this._tDur && this._ts > 0) || (!tTime && this._ts < 0)) && _removeFromParent(this, 1); // don't remove if we're rendering at exactly a time of 0, as there could be autoRevert values that should get set on the next tick (if the playhead goes backward beyond the startTime, negative totalTime). Don't remove if the timeline is reversed and the playhead isn't at 0, otherwise tl.progress(1).reverse() won't work. Only remove if the playhead is at the end and timeScale is positive, or if the playhead is at 0 and the timeScale is negative.\n\t\t\t    if (!suppressEvents && !(isNegative && !prevTime) && (tTime || prevTime || isYoyo)) { // if prevTime and tTime are zero, we shouldn't fire the onReverseComplete. This could happen if you gsap.to(... {paused:true}).play();\n\t\t\t\t\t_callback(this, (tTime === tDur ? \"onComplete\" : \"onReverseComplete\"), true);\n\t\t\t\t\tthis._prom && !(tTime < tDur && this.timeScale() > 0) && this._prom();\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\t\treturn this;\n\t}\n\n\ttargets() {\n\t\treturn this._targets;\n\t}\n\n\tinvalidate(soft) { // \"soft\" gives us a way to clear out everything EXCEPT the recorded pre-\"from\" portion of from() tweens. Otherwise, for example, if you tween.progress(1).render(0, true true).invalidate(), the \"from\" values would persist and then on the next render, the from() tweens would initialize and the current value would match the \"from\" values, thus animate from the same value to the same value (no animation). We tap into this in ScrollTrigger's refresh() where we must push a tween to completion and then back again but honor its init state in case the tween is dependent on another tween further up on the page.\n\t\t(!soft || !this.vars.runBackwards) && (this._startAt = 0)\n\t\tthis._pt = this._op = this._onUpdate = this._lazy = this.ratio = 0;\n\t\tthis._ptLookup = [];\n\t\tthis.timeline && this.timeline.invalidate(soft);\n\t\treturn super.invalidate(soft);\n\t}\n\n\tresetTo(property, value, start, startIsRelative) {\n\t\t_tickerActive || _ticker.wake();\n\t\tthis._ts || this.play();\n\t\tlet time = Math.min(this._dur, (this._dp._time - this._start) * this._ts),\n\t\t\tratio;\n\t\tthis._initted || _initTween(this, time);\n\t\tratio = this._ease(time / this._dur); // don't just get tween.ratio because it may not have rendered yet.\n\t\t// possible future addition to allow an object with multiple values to update, like tween.resetTo({x: 100, y: 200}); At this point, it doesn't seem worth the added kb given the fact that most users will likely opt for the convenient gsap.quickTo() way of interacting with this method.\n\t\t// if (_isObject(property)) { // performance optimization\n\t\t// \tfor (p in property) {\n\t\t// \t\tif (_updatePropTweens(this, p, property[p], value ? value[p] : null, start, ratio, time)) {\n\t\t// \t\t\treturn this.resetTo(property, value, start, startIsRelative); // if a PropTween wasn't found for the property, it'll get forced with a re-initialization so we need to jump out and start over again.\n\t\t// \t\t}\n\t\t// \t}\n\t\t// } else {\n\t\t\tif (_updatePropTweens(this, property, value, start, startIsRelative, ratio, time)) {\n\t\t\t\treturn this.resetTo(property, value, start, startIsRelative); // if a PropTween wasn't found for the property, it'll get forced with a re-initialization so we need to jump out and start over again.\n\t\t\t}\n\t\t//}\n\t\t_alignPlayhead(this, 0);\n\t\tthis.parent || _addLinkedListItem(this._dp, this, \"_first\", \"_last\", this._dp._sort ? \"_start\" : 0);\n\t\treturn this.render(0);\n\t}\n\n\tkill(targets, vars = \"all\") {\n\t\tif (!targets && (!vars || vars === \"all\")) {\n\t\t\tthis._lazy = this._pt = 0;\n\t\t\treturn this.parent ? _interrupt(this) : this;\n\t\t}\n\t\tif (this.timeline) {\n\t\t\tlet tDur = this.timeline.totalDuration();\n\t\t\tthis.timeline.killTweensOf(targets, vars, _overwritingTween && _overwritingTween.vars.overwrite !== true)._first || _interrupt(this); // if nothing is left tweening, interrupt.\n\t\t\tthis.parent && tDur !== this.timeline.totalDuration() && _setDuration(this, this._dur * this.timeline._tDur / tDur, 0, 1); // if a nested tween is killed that changes the duration, it should affect this tween's duration. We must use the ratio, though, because sometimes the internal timeline is stretched like for keyframes where they don't all add up to whatever the parent tween's duration was set to.\n\t\t\treturn this;\n\t\t}\n\t\tlet parsedTargets = this._targets,\n\t\t\tkillingTargets = targets ? toArray(targets) : parsedTargets,\n\t\t\tpropTweenLookup = this._ptLookup,\n\t\t\tfirstPT = this._pt,\n\t\t\toverwrittenProps, curLookup, curOverwriteProps, props, p, pt, i;\n\t\tif ((!vars || vars === \"all\") && _arraysMatch(parsedTargets, killingTargets)) {\n\t\t\tvars === \"all\" && (this._pt = 0);\n\t\t\treturn _interrupt(this);\n\t\t}\n\t\toverwrittenProps = this._op = this._op || [];\n\t\tif (vars !== \"all\") { //so people can pass in a comma-delimited list of property names\n\t\t\tif (_isString(vars)) {\n\t\t\t\tp = {};\n\t\t\t\t_forEachName(vars, name => p[name] = 1);\n\t\t\t\tvars = p;\n\t\t\t}\n\t\t\tvars = _addAliasesToVars(parsedTargets, vars);\n\t\t}\n\t\ti = parsedTargets.length;\n\t\twhile (i--) {\n\t\t\tif (~killingTargets.indexOf(parsedTargets[i])) {\n\t\t\t\tcurLookup = propTweenLookup[i];\n\t\t\t\tif (vars === \"all\") {\n\t\t\t\t\toverwrittenProps[i] = vars;\n\t\t\t\t\tprops = curLookup;\n\t\t\t\t\tcurOverwriteProps = {};\n\t\t\t\t} else {\n\t\t\t\t\tcurOverwriteProps = overwrittenProps[i] = overwrittenProps[i] || {};\n\t\t\t\t\tprops = vars;\n\t\t\t\t}\n\t\t\t\tfor (p in props) {\n\t\t\t\t\tpt = curLookup && curLookup[p];\n\t\t\t\t\tif (pt) {\n\t\t\t\t\t\tif (!(\"kill\" in pt.d) || pt.d.kill(p) === true) {\n\t\t\t\t\t\t\t_removeLinkedListItem(this, pt, \"_pt\");\n\t\t\t\t\t\t}\n\t\t\t\t\t\tdelete curLookup[p];\n\t\t\t\t\t}\n\t\t\t\t\tif (curOverwriteProps !== \"all\") {\n\t\t\t\t\t\tcurOverwriteProps[p] = 1;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tthis._initted && !this._pt && firstPT && _interrupt(this); //if all tweening properties are killed, kill the tween. Without this line, if there's a tween with multiple targets and then you killTweensOf() each target individually, the tween would technically still remain active and fire its onComplete even though there aren't any more properties tweening.\n\t\treturn this;\n\t}\n\n\n\tstatic to(targets, vars) {\n\t\treturn new Tween(targets, vars, arguments[2]);\n\t}\n\n\tstatic from(targets, vars) {\n\t\treturn _createTweenType(1, arguments);\n\t}\n\n\tstatic delayedCall(delay, callback, params, scope) {\n\t\treturn new Tween(callback, 0, {immediateRender:false, lazy:false, overwrite:false, delay:delay, onComplete:callback, onReverseComplete:callback, onCompleteParams:params, onReverseCompleteParams:params, callbackScope:scope}); // we must use onReverseComplete too for things like timeline.add(() => {...}) which should be triggered in BOTH directions (forward and reverse)\n\t}\n\n\tstatic fromTo(targets, fromVars, toVars) {\n\t\treturn _createTweenType(2, arguments);\n\t}\n\n\tstatic set(targets, vars) {\n\t\tvars.duration = 0;\n\t\tvars.repeatDelay || (vars.repeat = 0);\n\t\treturn new Tween(targets, vars);\n\t}\n\n\tstatic killTweensOf(targets, props, onlyActive) {\n\t\treturn _globalTimeline.killTweensOf(targets, props, onlyActive);\n\t}\n}\n\n_setDefaults(Tween.prototype, {_targets:[], _lazy:0, _startAt:0, _op:0, _onInit:0});\n\n//add the pertinent timeline methods to Tween instances so that users can chain conveniently and create a timeline automatically. (removed due to concerns that it'd ultimately add to more confusion especially for beginners)\n// _forEachName(\"to,from,fromTo,set,call,add,addLabel,addPause\", name => {\n// \tTween.prototype[name] = function() {\n// \t\tlet tl = new Timeline();\n// \t\treturn _addToTimeline(tl, this)[name].apply(tl, toArray(arguments));\n// \t}\n// });\n\n//for backward compatibility. Leverage the timeline calls.\n_forEachName(\"staggerTo,staggerFrom,staggerFromTo\", name => {\n\tTween[name] = function() {\n\t\tlet tl = new Timeline(),\n\t\t\tparams = _slice.call(arguments, 0);\n\t\tparams.splice(name === \"staggerFromTo\" ? 5 : 4, 0, 0);\n\t\treturn tl[name].apply(tl, params);\n\t}\n});\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/*\n * --------------------------------------------------------------------------------------\n * PROPTWEEN\n * --------------------------------------------------------------------------------------\n */\nlet _setterPlain = (target, property, value) => target[property] = value,\n\t_setterFunc = (target, property, value) => target[property](value),\n\t_setterFuncWithParam = (target, property, value, data) => target[property](data.fp, value),\n\t_setterAttribute = (target, property, value) => target.setAttribute(property, value),\n\t_getSetter = (target, property) => _isFunction(target[property]) ? _setterFunc : _isUndefined(target[property]) && target.setAttribute ? _setterAttribute : _setterPlain,\n\t_renderPlain = (ratio, data) => data.set(data.t, data.p, Math.round((data.s + data.c * ratio) * 1000000) / 1000000, data),\n\t_renderBoolean = (ratio, data) => data.set(data.t, data.p, !!(data.s + data.c * ratio), data),\n\t_renderComplexString = function(ratio, data) {\n\t\tlet pt = data._pt,\n\t\t\ts = \"\";\n\t\tif (!ratio && data.b) { //b = beginning string\n\t\t\ts = data.b;\n\t\t} else if (ratio === 1 && data.e) { //e = ending string\n\t\t\ts = data.e;\n\t\t} else {\n\t\t\twhile (pt) {\n\t\t\t\ts = pt.p + (pt.m ? pt.m(pt.s + pt.c * ratio) : (Math.round((pt.s + pt.c * ratio) * 10000) / 10000)) + s; //we use the \"p\" property for the text inbetween (like a suffix). And in the context of a complex string, the modifier (m) is typically just Math.round(), like for RGB colors.\n\t\t\t\tpt = pt._next;\n\t\t\t}\n\t\t\ts += data.c; //we use the \"c\" of the PropTween to store the final chunk of non-numeric text.\n\t\t}\n\t\tdata.set(data.t, data.p, s, data);\n\t},\n\t_renderPropTweens = function(ratio, data) {\n\t\tlet pt = data._pt;\n\t\twhile (pt) {\n\t\t\tpt.r(ratio, pt.d);\n\t\t\tpt = pt._next;\n\t\t}\n\t},\n\t_addPluginModifier = function(modifier, tween, target, property) {\n\t\tlet pt = this._pt,\n\t\t\tnext;\n\t\twhile (pt) {\n\t\t\tnext = pt._next;\n\t\t\tpt.p === property && pt.modifier(modifier, tween, target);\n\t\t\tpt = next;\n\t\t}\n\t},\n\t_killPropTweensOf = function(property) {\n\t\tlet pt = this._pt,\n\t\t\thasNonDependentRemaining, next;\n\t\twhile (pt) {\n\t\t\tnext = pt._next;\n\t\t\tif ((pt.p === property && !pt.op) || pt.op === property) {\n\t\t\t\t_removeLinkedListItem(this, pt, \"_pt\");\n\t\t\t} else if (!pt.dep) {\n\t\t\t\thasNonDependentRemaining = 1;\n\t\t\t}\n\t\t\tpt = next;\n\t\t}\n\t\treturn !hasNonDependentRemaining;\n\t},\n\t_setterWithModifier = (target, property, value, data) => {\n\t\tdata.mSet(target, property, data.m.call(data.tween, value, data.mt), data);\n\t},\n\t_sortPropTweensByPriority = parent => {\n\t\tlet pt = parent._pt,\n\t\t\tnext, pt2, first, last;\n\t\t//sorts the PropTween linked list in order of priority because some plugins need to do their work after ALL of the PropTweens were created (like RoundPropsPlugin and ModifiersPlugin)\n\t\twhile (pt) {\n\t\t\tnext = pt._next;\n\t\t\tpt2 = first;\n\t\t\twhile (pt2 && pt2.pr > pt.pr) {\n\t\t\t\tpt2 = pt2._next;\n\t\t\t}\n\t\t\tif ((pt._prev = pt2 ? pt2._prev : last)) {\n\t\t\t\tpt._prev._next = pt;\n\t\t\t} else {\n\t\t\t\tfirst = pt;\n\t\t\t}\n\t\t\tif ((pt._next = pt2)) {\n\t\t\t\tpt2._prev = pt;\n\t\t\t} else {\n\t\t\t\tlast = pt;\n\t\t\t}\n\t\t\tpt = next;\n\t\t}\n\t\tparent._pt = first;\n\t};\n\n//PropTween key: t = target, p = prop, r = renderer, d = data, s = start, c = change, op = overwriteProperty (ONLY populated when it's different than p), pr = priority, _next/_prev for the linked list siblings, set = setter, m = modifier, mSet = modifierSetter (the original setter, before a modifier was added)\nexport class PropTween {\n\n\tconstructor(next, target, prop, start, change, renderer, data, setter, priority) {\n\t\tthis.t = target;\n\t\tthis.s = start;\n\t\tthis.c = change;\n\t\tthis.p = prop;\n\t\tthis.r = renderer || _renderPlain;\n\t\tthis.d = data || this;\n\t\tthis.set = setter || _setterPlain;\n\t\tthis.pr = priority || 0;\n\t\tthis._next = next;\n\t\tif (next) {\n\t\t\tnext._prev = this;\n\t\t}\n\t}\n\n\tmodifier(func, tween, target) {\n\t\tthis.mSet = this.mSet || this.set; //in case it was already set (a PropTween can only have one modifier)\n\t\tthis.set = _setterWithModifier;\n\t\tthis.m = func;\n\t\tthis.mt = target; //modifier target\n\t\tthis.tween = tween;\n\t}\n}\n\n\n\n//Initialization tasks\n_forEachName(_callbackNames + \"parent,duration,ease,delay,overwrite,runBackwards,startAt,yoyo,immediateRender,repeat,repeatDelay,data,paused,reversed,lazy,callbackScope,stringFilter,id,yoyoEase,stagger,inherit,repeatRefresh,keyframes,autoRevert,scrollTrigger\", name => _reservedProps[name] = 1);\n_globals.TweenMax = _globals.TweenLite = Tween;\n_globals.TimelineLite = _globals.TimelineMax = Timeline;\n_globalTimeline = new Timeline({sortChildren: false, defaults: _defaults, autoRemoveChildren: true, id:\"root\", smoothChildTiming: true});\n_config.stringFilter = _colorStringFilter;\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nlet _media = [],\n\t_listeners = {},\n\t_emptyArray = [],\n\t_lastMediaTime = 0,\n\t_contextID = 0,\n\t_dispatch = type => (_listeners[type] || _emptyArray).map(f => f()),\n\t_onMediaChange = () => {\n\t\tlet time = Date.now(),\n\t\t\tmatches = [];\n\t\tif (time - _lastMediaTime > 2) {\n\t\t\t_dispatch(\"matchMediaInit\");\n\t\t\t_media.forEach(c => {\n\t\t\t\tlet queries = c.queries,\n\t\t\t\t\tconditions = c.conditions,\n\t\t\t\t\tmatch, p, anyMatch, toggled;\n\t\t\t\tfor (p in queries) {\n\t\t\t\t\tmatch = _win.matchMedia(queries[p]).matches; // Firefox doesn't update the \"matches\" property of the MediaQueryList object correctly - it only does so as it calls its change handler - so we must re-create a media query here to ensure it's accurate.\n\t\t\t\t\tmatch && (anyMatch = 1);\n\t\t\t\t\tif (match !== conditions[p]) {\n\t\t\t\t\t\tconditions[p] = match;\n\t\t\t\t\t\ttoggled = 1;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (toggled) {\n\t\t\t\t\tc.revert();\n\t\t\t\t\tanyMatch && matches.push(c);\n\t\t\t\t}\n\t\t\t});\n\t\t\t_dispatch(\"matchMediaRevert\");\n\t\t\tmatches.forEach(c => c.onMatch(c));\n\t\t\t_lastMediaTime = time;\n\t\t\t_dispatch(\"matchMedia\");\n\t\t}\n\t};\n\nclass Context {\n\tconstructor(func, scope) {\n\t\tthis.selector = scope && selector(scope);\n\t\tthis.data = [];\n\t\tthis._r = []; // returned/cleanup functions\n\t\tthis.isReverted = false;\n\t\tthis.id = _contextID++; // to work around issues that frameworks like Vue cause by making things into Proxies which make it impossible to do something like _media.indexOf(this) because \"this\" would no longer refer to the Context instance itself - it'd refer to a Proxy! We needed a way to identify the context uniquely\n\t\tfunc && this.add(func);\n\t}\n\tadd(name, func, scope) {\n\t\t// possible future addition if we need the ability to add() an animation to a context and for whatever reason cannot create that animation inside of a context.add(() => {...}) function.\n\t\t// if (name && _isFunction(name.revert)) {\n\t\t// \tthis.data.push(name);\n\t\t// \treturn (name._ctx = this);\n\t\t// }\n\t\tif (_isFunction(name)) {\n\t\t\tscope = func;\n\t\t\tfunc = name;\n\t\t\tname = _isFunction;\n\t\t}\n\t\tlet self = this,\n\t\t\tf = function() {\n\t\t\t\tlet prev = _context,\n\t\t\t\t\tprevSelector = self.selector,\n\t\t\t\t\tresult;\n\t\t\t\tprev && prev !== self && prev.data.push(self);\n\t\t\t\tscope && (self.selector = selector(scope));\n\t\t\t\t_context = self;\n\t\t\t\tresult = func.apply(self, arguments);\n\t\t\t\t_isFunction(result) && self._r.push(result);\n\t\t\t\t_context = prev;\n\t\t\t\tself.selector = prevSelector;\n\t\t\t\tself.isReverted = false;\n\t\t\t\treturn result;\n\t\t\t};\n\t\tself.last = f;\n\t\treturn name === _isFunction ? f(self) : name ? (self[name] = f) : f;\n\t}\n\tignore(func) {\n\t\tlet prev = _context;\n\t\t_context = null;\n\t\tfunc(this);\n\t\t_context = prev;\n\t}\n\tgetTweens() {\n\t\tlet a = [];\n\t\tthis.data.forEach(e => (e instanceof Context) ? a.push(...e.getTweens()) : (e instanceof Tween) && !(e.parent && e.parent.data === \"nested\") && a.push(e));\n\t\treturn a;\n\t}\n\tclear() {\n\t\tthis._r.length = this.data.length = 0;\n\t}\n\tkill(revert, matchMedia) {\n\t\tif (revert) {\n\t\t\tlet tweens = this.getTweens();\n\t\t\tthis.data.forEach(t => { // Flip plugin tweens are very different in that they should actually be pushed to their end. The plugin replaces the timeline's .revert() method to do exactly that. But we also need to remove any of those nested tweens inside the flip timeline so that they don't get individually reverted.\n\t\t\t\tif (t.data === \"isFlip\") {\n\t\t\t\t\tt.revert();\n\t\t\t\t\tt.getChildren(true, true, false).forEach(tween => tweens.splice(tweens.indexOf(tween), 1));\n\t\t\t\t}\n\t\t\t});\n\t\t\t// save as an object so that we can cache the globalTime for each tween to optimize performance during the sort\n\t\t\ttweens.map(t => { return {g: t.globalTime(0), t}}).sort((a, b) => b.g - a.g || -Infinity).forEach(o => o.t.revert(revert)); // note: all of the _startAt tweens should be reverted in reverse order that they were created, and they'll all have the same globalTime (-1) so the \" || -1\" in the sort keeps the order properly.\n\t\t\tthis.data.forEach(e => !(e instanceof Tween) && e.revert && e.revert(revert));\n\t\t\tthis._r.forEach(f => f(revert, this));\n\t\t\tthis.isReverted = true;\n\t\t} else {\n\t\t\tthis.data.forEach(e => e.kill && e.kill());\n\t\t}\n\t\tthis.clear();\n\t\tif (matchMedia) {\n\t\t\tlet i = _media.length;\n\t\t\twhile (i--) { // previously, we checked _media.indexOf(this), but some frameworks like Vue enforce Proxy objects that make it impossible to get the proper result that way, so we must use a unique ID number instead.\n\t\t\t\t_media[i].id === this.id && _media.splice(i, 1);\n\t\t\t}\n\t\t}\n\t}\n\trevert(config) {\n\t\tthis.kill(config || {});\n\t}\n}\n\n\n\n\nclass MatchMedia {\n\tconstructor(scope) {\n\t\tthis.contexts = [];\n\t\tthis.scope = scope;\n\t}\n\tadd(conditions, func, scope) {\n\t\t_isObject(conditions) || (conditions = {matches: conditions});\n\t\tlet context = new Context(0, scope || this.scope),\n\t\t\tcond = context.conditions = {},\n\t\t\tmq, p, active;\n\t\t_context && !context.selector && (context.selector = _context.selector); // in case a context is created inside a context. Like a gsap.matchMedia() that's inside a scoped gsap.context()\n\t\tthis.contexts.push(context);\n\t\tfunc = context.add(\"onMatch\", func);\n\t\tcontext.queries = conditions;\n\t\tfor (p in conditions) {\n\t\t\tif (p === \"all\") {\n\t\t\t\tactive = 1;\n\t\t\t} else {\n\t\t\t\tmq = _win.matchMedia(conditions[p]);\n\t\t\t\tif (mq) {\n\t\t\t\t\t_media.indexOf(context) < 0 && _media.push(context);\n\t\t\t\t\t(cond[p] = mq.matches) && (active = 1);\n\t\t\t\t\tmq.addListener ? mq.addListener(_onMediaChange) : mq.addEventListener(\"change\", _onMediaChange);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tactive && func(context);\n\t\treturn this;\n\t}\n\t// refresh() {\n\t// \tlet time = _lastMediaTime,\n\t// \t\tmedia = _media;\n\t// \t_lastMediaTime = -1;\n\t// \t_media = this.contexts;\n\t// \t_onMediaChange();\n\t// \t_lastMediaTime = time;\n\t// \t_media = media;\n\t// }\n\trevert(config) {\n\t\tthis.kill(config || {});\n\t}\n\tkill(revert) {\n\t\tthis.contexts.forEach(c => c.kill(revert, true));\n\t}\n}\n\n\n\n/*\n * --------------------------------------------------------------------------------------\n * GSAP\n * --------------------------------------------------------------------------------------\n */\nconst _gsap = {\n\tregisterPlugin(...args) {\n\t\targs.forEach(config => _createPlugin(config));\n\t},\n\ttimeline(vars) {\n\t\treturn new Timeline(vars);\n\t},\n\tgetTweensOf(targets, onlyActive) {\n\t\treturn _globalTimeline.getTweensOf(targets, onlyActive);\n\t},\n\tgetProperty(target, property, unit, uncache) {\n\t\t_isString(target) && (target = toArray(target)[0]); //in case selector text or an array is passed in\n\t\tlet getter = _getCache(target || {}).get,\n\t\t\tformat = unit ? _passThrough : _numericIfPossible;\n\t\tunit === \"native\" && (unit = \"\");\n\t\treturn !target ? target : !property ? (property, unit, uncache) => format(((_plugins[property] && _plugins[property].get) || getter)(target, property, unit, uncache)) : format(((_plugins[property] && _plugins[property].get) || getter)(target, property, unit, uncache));\n\t},\n\tquickSetter(target, property, unit) {\n\t\ttarget = toArray(target);\n\t\tif (target.length > 1) {\n\t\t\tlet setters = target.map(t => gsap.quickSetter(t, property, unit)),\n\t\t\t\tl = setters.length;\n\t\t\treturn value => {\n\t\t\t\tlet i = l;\n\t\t\t\twhile(i--) {\n\t\t\t\t\tsetters[i](value);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\ttarget = target[0] || {};\n\t\tlet Plugin = _plugins[property],\n\t\t\tcache = _getCache(target),\n\t\t\tp = (cache.harness && (cache.harness.aliases || {})[property]) || property, // in case it's an alias, like \"rotate\" for \"rotation\".\n\t\t\tsetter = Plugin ? value => {\n\t\t\t\tlet p = new Plugin();\n\t\t\t\t_quickTween._pt = 0;\n\t\t\t\tp.init(target, unit ? value + unit : value, _quickTween, 0, [target]);\n\t\t\t\tp.render(1, p);\n\t\t\t\t_quickTween._pt && _renderPropTweens(1, _quickTween);\n\t\t\t} : cache.set(target, p);\n\t\treturn Plugin ? setter : value => setter(target, p, unit ? value + unit : value, cache, 1);\n\t},\n\tquickTo(target, property, vars) {\n\t\tlet tween = gsap.to(target, _merge({[property]: \"+=0.1\", paused: true}, vars || {})),\n\t\t\tfunc = (value, start, startIsRelative) => tween.resetTo(property, value, start, startIsRelative);\n\t\tfunc.tween = tween;\n\t\treturn func;\n\t},\n\tisTweening(targets) {\n\t\treturn _globalTimeline.getTweensOf(targets, true).length > 0;\n\t},\n\tdefaults(value) {\n\t\tvalue && value.ease && (value.ease = _parseEase(value.ease, _defaults.ease));\n\t\treturn _mergeDeep(_defaults, value || {});\n\t},\n\tconfig(value) {\n\t\treturn _mergeDeep(_config, value || {});\n\t},\n\tregisterEffect({name, effect, plugins, defaults, extendTimeline}) {\n\t\t(plugins || \"\").split(\",\").forEach(pluginName => pluginName && !_plugins[pluginName] && !_globals[pluginName] && _warn(name + \" effect requires \" + pluginName + \" plugin.\"));\n\t\t_effects[name] = (targets, vars, tl) => effect(toArray(targets), _setDefaults(vars || {}, defaults), tl);\n\t\tif (extendTimeline) {\n\t\t\tTimeline.prototype[name] = function(targets, vars, position) {\n\t\t\t\treturn this.add(_effects[name](targets, _isObject(vars) ? vars : (position = vars) && {}, this), position);\n\t\t\t};\n\t\t}\n\t},\n\tregisterEase(name, ease) {\n\t\t_easeMap[name] = _parseEase(ease);\n\t},\n\tparseEase(ease, defaultEase) {\n\t\treturn arguments.length ? _parseEase(ease, defaultEase) : _easeMap;\n\t},\n\tgetById(id) {\n\t\treturn _globalTimeline.getById(id);\n\t},\n\texportRoot(vars = {}, includeDelayedCalls) {\n\t\tlet tl = new Timeline(vars),\n\t\t\tchild, next;\n\t\ttl.smoothChildTiming = _isNotFalse(vars.smoothChildTiming);\n\t\t_globalTimeline.remove(tl);\n\t\ttl._dp = 0; //otherwise it'll get re-activated when adding children and be re-introduced into _globalTimeline's linked list (then added to itself).\n\t\ttl._time = tl._tTime = _globalTimeline._time;\n\t\tchild = _globalTimeline._first;\n\t\twhile (child) {\n\t\t\tnext = child._next;\n\t\t\tif (includeDelayedCalls || !(!child._dur && child instanceof Tween && child.vars.onComplete === child._targets[0])) {\n\t\t\t\t_addToTimeline(tl, child, child._start - child._delay);\n\t\t\t}\n\t\t\tchild = next;\n\t\t}\n\t\t_addToTimeline(_globalTimeline, tl, 0);\n\t\treturn tl;\n\t},\n\tcontext: (func, scope) => func ? new Context(func, scope) : _context,\n\tmatchMedia: scope => new MatchMedia(scope),\n\tmatchMediaRefresh: () => _media.forEach(c => {\n\t\tlet cond = c.conditions,\n\t\t\tfound, p;\n\t\tfor (p in cond) {\n\t\t\tif (cond[p]) {\n\t\t\t\tcond[p] = false;\n\t\t\t\tfound = 1;\n\t\t\t}\n\t\t}\n\t\tfound && c.revert();\n\t}) || _onMediaChange(),\n\taddEventListener(type, callback) {\n\t\tlet a = _listeners[type] || (_listeners[type] = []);\n\t\t~a.indexOf(callback) || a.push(callback);\n\t},\n\tremoveEventListener(type, callback) {\n\t\tlet a = _listeners[type],\n\t\t\ti = a && a.indexOf(callback);\n\t\ti >= 0 && a.splice(i, 1);\n\t},\n\tutils: { wrap, wrapYoyo, distribute, random, snap, normalize, getUnit, clamp, splitColor, toArray, selector, mapRange, pipe, unitize, interpolate, shuffle },\n\tinstall: _install,\n\teffects: _effects,\n\tticker: _ticker,\n\tupdateRoot: Timeline.updateRoot,\n\tplugins: _plugins,\n\tglobalTimeline: _globalTimeline,\n\tcore: {PropTween, globals: _addGlobal, Tween, Timeline, Animation, getCache: _getCache, _removeLinkedListItem, reverting: () => _reverting, context: toAdd => {if (toAdd && _context) { _context.data.push(toAdd); toAdd._ctx = _context} return _context; }, suppressOverwrites: value => _suppressOverwrites = value}\n};\n\n_forEachName(\"to,from,fromTo,delayedCall,set,killTweensOf\", name => _gsap[name] = Tween[name]);\n_ticker.add(Timeline.updateRoot);\n_quickTween = _gsap.to({}, {duration:0});\n\n\n\n\n// ---- EXTRA PLUGINS --------------------------------------------------------\n\n\nlet _getPluginPropTween = (plugin, prop) => {\n\t\tlet pt = plugin._pt;\n\t\twhile (pt && pt.p !== prop && pt.op !== prop && pt.fp !== prop) {\n\t\t\tpt = pt._next;\n\t\t}\n\t\treturn pt;\n\t},\n\t_addModifiers = (tween, modifiers) => {\n\t\t\tlet\ttargets = tween._targets,\n\t\t\t\tp, i, pt;\n\t\t\tfor (p in modifiers) {\n\t\t\t\ti = targets.length;\n\t\t\t\twhile (i--) {\n\t\t\t\t\tpt = tween._ptLookup[i][p];\n\t\t\t\t\tif (pt && (pt = pt.d)) {\n\t\t\t\t\t\tif (pt._pt) { // is a plugin\n\t\t\t\t\t\t\tpt = _getPluginPropTween(pt, p);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tpt && pt.modifier && pt.modifier(modifiers[p], tween, targets[i], p);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t},\n\t_buildModifierPlugin = (name, modifier) => {\n\t\treturn {\n\t\t\tname: name,\n\t\t\trawVars: 1, //don't pre-process function-based values or \"random()\" strings.\n\t\t\tinit(target, vars, tween) {\n\t\t\t\ttween._onInit = tween => {\n\t\t\t\t\tlet temp, p;\n\t\t\t\t\tif (_isString(vars)) {\n\t\t\t\t\t\ttemp = {};\n\t\t\t\t\t\t_forEachName(vars, name => temp[name] = 1); //if the user passes in a comma-delimited list of property names to roundProps, like \"x,y\", we round to whole numbers.\n\t\t\t\t\t\tvars = temp;\n\t\t\t\t\t}\n\t\t\t\t\tif (modifier) {\n\t\t\t\t\t\ttemp = {};\n\t\t\t\t\t\tfor (p in vars) {\n\t\t\t\t\t\t\ttemp[p] = modifier(vars[p]);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tvars = temp;\n\t\t\t\t\t}\n\t\t\t\t\t_addModifiers(tween, vars);\n\t\t\t\t};\n\t\t\t}\n\t\t};\n\t};\n\n//register core plugins\nexport const gsap = _gsap.registerPlugin({\n\t\tname:\"attr\",\n\t\tinit(target, vars, tween, index, targets) {\n\t\t\tlet p, pt, v;\n\t\t\tthis.tween = tween;\n\t\t\tfor (p in vars) {\n\t\t\t\tv = target.getAttribute(p) || \"\";\n\t\t\t\tpt = this.add(target, \"setAttribute\", (v || 0) + \"\", vars[p], index, targets, 0, 0, p);\n\t\t\t\tpt.op = p;\n\t\t\t\tpt.b = v; // record the beginning value so we can revert()\n\t\t\t\tthis._props.push(p);\n\t\t\t}\n\t\t},\n\t\trender(ratio, data) {\n\t\t\tlet pt = data._pt;\n\t\t\twhile (pt) {\n\t\t\t\t_reverting ? pt.set(pt.t, pt.p, pt.b, pt) : pt.r(ratio, pt.d); // if reverting, go back to the original (pt.b)\n\t\t\t\tpt = pt._next;\n\t\t\t}\n\t\t}\n\t}, {\n\t\tname:\"endArray\",\n\t\tinit(target, value) {\n\t\t\tlet i = value.length;\n\t\t\twhile (i--) {\n\t\t\t\tthis.add(target, i, target[i] || 0, value[i], 0, 0, 0, 0, 0, 1);\n\t\t\t}\n\t\t}\n\t},\n\t_buildModifierPlugin(\"roundProps\", _roundModifier),\n\t_buildModifierPlugin(\"modifiers\"),\n\t_buildModifierPlugin(\"snap\", snap)\n) || _gsap; //to prevent the core plugins from being dropped via aggressive tree shaking, we must include them in the variable declaration in this way.\n\nTween.version = Timeline.version = gsap.version = \"3.12.2\";\n_coreReady = 1;\n_windowExists() && _wake();\n\nexport const { Power0, Power1, Power2, Power3, Power4, Linear, Quad, Cubic, Quart, Quint, Strong, Elastic, Back, SteppedEase, Bounce, Sine, Expo, Circ } = _easeMap;\nexport { Tween as TweenMax, Tween as TweenLite, Timeline as TimelineMax, Timeline as TimelineLite, gsap as default, wrap, wrapYoyo, distribute, random, snap, normalize, getUnit, clamp, splitColor, toArray, selector, mapRange, pipe, unitize, interpolate, shuffle };\n//export some internal methods/orojects for use in CSSPlugin so that we can externalize that file and allow custom builds that exclude it.\nexport { _getProperty, _numExp, _numWithUnitExp, _isString, _isUndefined, _renderComplexString, _relExp, _setDefaults, _removeLinkedListItem, _forEachName, _sortPropTweensByPriority, _colorStringFilter, _replaceRandom, _checkPlugin, _plugins, _ticker, _config, _roundModifier, _round, _missingPlugin, _getSetter, _getCache, _colorExp, _parseRelative }", "/*!\n * CSSPlugin 3.12.2\n * https://greensock.com\n *\n * Copyright 2008-2023, GreenSock. All rights reserved.\n * Subject to the terms at https://greensock.com/standard-license or for\n * Club GreenSock members, the agreement issued with that membership.\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nimport {gsap, _getProperty, _numExp, _numWithUnitExp, getUnit, _isString, _isUndefined, _renderComplexString, _relExp, _forEachName, _sortPropTweensByPriority, _colorStringFilter, _checkPlugin, _replaceRandom, _plugins, GSCache, PropTween, _config, _ticker, _round, _missingPlugin, _getSetter, _getCache, _colorExp, _parseRelative,\n\t_setDefaults, _removeLinkedListItem //for the commented-out className feature.\n} from \"./gsap-core.js\";\n\nlet _win, _doc, _docElement, _pluginInitted, _tempDiv, _tempDiv<PERSON><PERSON><PERSON>, _recentSetterPlugin, _reverting,\n\t_windowExists = () => typeof(window) !== \"undefined\",\n\t_transformProps = {},\n\t_RAD2DEG = 180 / Math.PI,\n\t_DEG2RAD = Math.PI / 180,\n\t_atan2 = Math.atan2,\n\t_bigNum = 1e8,\n\t_capsExp = /([A-Z])/g,\n\t_horizontalExp = /(left|right|width|margin|padding|x)/i,\n\t_complexExp = /[\\s,\\(]\\S/,\n\t_propertyAliases = {autoAlpha:\"opacity,visibility\", scale:\"scaleX,scaleY\", alpha:\"opacity\"},\n\t_renderCSSProp = (ratio, data) => data.set(data.t, data.p, (Math.round((data.s + data.c * ratio) * 10000) / 10000) + data.u, data),\n\t_renderPropWithEnd = (ratio, data) => data.set(data.t, data.p, ratio === 1 ? data.e : (Math.round((data.s + data.c * ratio) * 10000) / 10000) + data.u, data),\n\t_renderCSSPropWithBeginning = (ratio, data) => data.set(data.t, data.p, ratio ? (Math.round((data.s + data.c * ratio) * 10000) / 10000) + data.u : data.b, data), //if units change, we need a way to render the original unit/value when the tween goes all the way back to the beginning (ratio:0)\n\t_renderRoundedCSSProp = (ratio, data) => {\n\t\tlet value = data.s + data.c * ratio;\n\t\tdata.set(data.t, data.p, ~~(value + (value < 0 ? -.5 : .5)) + data.u, data);\n\t},\n\t_renderNonTweeningValue = (ratio, data) => data.set(data.t, data.p, ratio ? data.e : data.b, data),\n\t_renderNonTweeningValueOnlyAtEnd = (ratio, data) => data.set(data.t, data.p, ratio !== 1 ? data.b : data.e, data),\n\t_setterCSSStyle = (target, property, value) => target.style[property] = value,\n\t_setterCSSProp = (target, property, value) => target.style.setProperty(property, value),\n\t_setterTransform = (target, property, value) => target._gsap[property] = value,\n\t_setterScale = (target, property, value) => target._gsap.scaleX = target._gsap.scaleY = value,\n\t_setterScaleWithRender = (target, property, value, data, ratio) => {\n\t\tlet cache = target._gsap;\n\t\tcache.scaleX = cache.scaleY = value;\n\t\tcache.renderTransform(ratio, cache);\n\t},\n\t_setterTransformWithRender = (target, property, value, data, ratio) => {\n\t\tlet cache = target._gsap;\n\t\tcache[property] = value;\n\t\tcache.renderTransform(ratio, cache);\n\t},\n\t_transformProp = \"transform\",\n\t_transformOriginProp = _transformProp + \"Origin\",\n\t_saveStyle = function(property, isNotCSS) {\n\t\tlet target = this.target,\n\t\t\tstyle = target.style;\n\t\tif ((property in _transformProps) && style) {\n\t\t\tthis.tfm = this.tfm || {};\n\t\t\tif (property !== \"transform\") {\n\t\t\t\tproperty = _propertyAliases[property] || property;\n\t\t\t\t~property.indexOf(\",\") ? property.split(\",\").forEach(a => this.tfm[a] = _get(target, a)) : (this.tfm[property] = target._gsap.x ? target._gsap[property] : _get(target, property)); // note: scale would map to \"scaleX,scaleY\", thus we loop and apply them both.\n\t\t\t} else {\n\t\t\t\treturn _propertyAliases.transform.split(\",\").forEach(p => _saveStyle.call(this, p, isNotCSS));\n\t\t\t}\n\t\t\tif (this.props.indexOf(_transformProp) >= 0) { return; }\n\t\t\tif (target._gsap.svg) {\n\t\t\t\tthis.svgo = target.getAttribute(\"data-svg-origin\");\n\t\t\t\tthis.props.push(_transformOriginProp, isNotCSS, \"\");\n\t\t\t}\n\t\t\tproperty = _transformProp;\n\t\t}\n\t\t(style || isNotCSS) && this.props.push(property, isNotCSS, style[property]);\n\t},\n\t_removeIndependentTransforms = style => {\n\t\tif (style.translate) {\n\t\t\tstyle.removeProperty(\"translate\");\n\t\t\tstyle.removeProperty(\"scale\");\n\t\t\tstyle.removeProperty(\"rotate\");\n\t\t}\n\t},\n\t_revertStyle = function() {\n\t\tlet props = this.props,\n\t\t\ttarget = this.target,\n\t\t\tstyle = target.style,\n\t\t\tcache = target._gsap,\n\t\t\ti, p;\n\t\tfor (i = 0; i < props.length; i+=3) { // stored like this: property, isNotCSS, value\n\t\t\tprops[i+1] ? target[props[i]] = props[i+2] : props[i+2] ? (style[props[i]] = props[i+2]) : style.removeProperty(props[i].substr(0,2) === \"--\" ? props[i] : props[i].replace(_capsExp, \"-$1\").toLowerCase());\n\t\t}\n\t\tif (this.tfm) {\n\t\t\tfor (p in this.tfm) {\n\t\t\t\tcache[p] = this.tfm[p];\n\t\t\t}\n\t\t\tif (cache.svg) {\n\t\t\t\tcache.renderTransform();\n\t\t\t\ttarget.setAttribute(\"data-svg-origin\", this.svgo || \"\");\n\t\t\t}\n\t\t\ti = _reverting();\n\t\t\tif ((!i || !i.isStart) && !style[_transformProp]) {\n\t\t\t\t_removeIndependentTransforms(style);\n\t\t\t\tcache.uncache = 1; // if it's a startAt that's being reverted in the _initTween() of the core, we don't need to uncache transforms. This is purely a performance optimization.\n\t\t\t}\n\t\t}\n\t},\n\t_getStyleSaver = (target, properties) => {\n\t\tlet saver = {\n\t\t\ttarget,\n\t\t\tprops: [],\n\t\t\trevert: _revertStyle,\n\t\t\tsave: _saveStyle\n\t\t};\n\t\ttarget._gsap || gsap.core.getCache(target); // just make sure there's a _gsap cache defined because we read from it in _saveStyle() and it's more efficient to just check it here once.\n\t\tproperties && properties.split(\",\").forEach(p => saver.save(p));\n\t\treturn saver;\n\t},\n\t_supports3D,\n\t_createElement = (type, ns) => {\n\t\tlet e = _doc.createElementNS ? _doc.createElementNS((ns || \"http://www.w3.org/1999/xhtml\").replace(/^https/, \"http\"), type) : _doc.createElement(type); //some servers swap in https for http in the namespace which can break things, making \"style\" inaccessible.\n\t\treturn e.style ? e : _doc.createElement(type); //some environments won't allow access to the element's style when created with a namespace in which case we default to the standard createElement() to work around the issue. Also note that when GSAP is embedded directly inside an SVG file, createElement() won't allow access to the style object in Firefox (see https://greensock.com/forums/topic/20215-problem-using-tweenmax-in-standalone-self-containing-svg-file-err-cannot-set-property-csstext-of-undefined/).\n\t},\n\t_getComputedProperty = (target, property, skipPrefixFallback) => {\n\t\tlet cs = getComputedStyle(target);\n\t\treturn cs[property] || cs.getPropertyValue(property.replace(_capsExp, \"-$1\").toLowerCase()) || cs.getPropertyValue(property) || (!skipPrefixFallback && _getComputedProperty(target, _checkPropPrefix(property) || property, 1)) || \"\"; //css variables may not need caps swapped out for dashes and lowercase.\n\t},\n\t_prefixes = \"O,Moz,ms,Ms,Webkit\".split(\",\"),\n\t_checkPropPrefix = (property, element, preferPrefix) => {\n\t\tlet e = element || _tempDiv,\n\t\t\ts = e.style,\n\t\t\ti = 5;\n\t\tif (property in s && !preferPrefix) {\n\t\t\treturn property;\n\t\t}\n\t\tproperty = property.charAt(0).toUpperCase() + property.substr(1);\n\t\twhile (i-- && !((_prefixes[i]+property) in s)) { }\n\t\treturn (i < 0) ? null : ((i === 3) ? \"ms\" : (i >= 0) ? _prefixes[i] : \"\") + property;\n\t},\n\t_initCore = () => {\n\t\tif (_windowExists() && window.document) {\n\t\t\t_win = window;\n\t\t\t_doc = _win.document;\n\t\t\t_docElement = _doc.documentElement;\n\t\t\t_tempDiv = _createElement(\"div\") || {style:{}};\n\t\t\t_tempDivStyler = _createElement(\"div\");\n\t\t\t_transformProp = _checkPropPrefix(_transformProp);\n\t\t\t_transformOriginProp = _transformProp + \"Origin\";\n\t\t\t_tempDiv.style.cssText = \"border-width:0;line-height:0;position:absolute;padding:0\"; //make sure to override certain properties that may contaminate measurements, in case the user has overreaching style sheets.\n\t\t\t_supports3D = !!_checkPropPrefix(\"perspective\");\n\t\t\t_reverting = gsap.core.reverting;\n\t\t\t_pluginInitted = 1;\n\t\t}\n\t},\n\t_getBBoxHack = function(swapIfPossible) { //works around issues in some browsers (like Firefox) that don't correctly report getBBox() on SVG elements inside a <defs> element and/or <mask>. We try creating an SVG, adding it to the documentElement and toss the element in there so that it's definitely part of the rendering tree, then grab the bbox and if it works, we actually swap out the original getBBox() method for our own that does these extra steps whenever getBBox is needed. This helps ensure that performance is optimal (only do all these extra steps when absolutely necessary...most elements don't need it).\n\t\tlet svg = _createElement(\"svg\", (this.ownerSVGElement && this.ownerSVGElement.getAttribute(\"xmlns\")) || \"http://www.w3.org/2000/svg\"),\n\t\t\toldParent = this.parentNode,\n\t\t\toldSibling = this.nextSibling,\n\t\t\toldCSS = this.style.cssText,\n\t\t\tbbox;\n\t\t_docElement.appendChild(svg);\n\t\tsvg.appendChild(this);\n\t\tthis.style.display = \"block\";\n\t\tif (swapIfPossible) {\n\t\t\ttry {\n\t\t\t\tbbox = this.getBBox();\n\t\t\t\tthis._gsapBBox = this.getBBox; //store the original\n\t\t\t\tthis.getBBox = _getBBoxHack;\n\t\t\t} catch (e) { }\n\t\t} else if (this._gsapBBox) {\n\t\t\tbbox = this._gsapBBox();\n\t\t}\n\t\tif (oldParent) {\n\t\t\tif (oldSibling) {\n\t\t\t\toldParent.insertBefore(this, oldSibling);\n\t\t\t} else {\n\t\t\t\toldParent.appendChild(this);\n\t\t\t}\n\t\t}\n\t\t_docElement.removeChild(svg);\n\t\tthis.style.cssText = oldCSS;\n\t\treturn bbox;\n\t},\n\t_getAttributeFallbacks = (target, attributesArray) => {\n\t\tlet i = attributesArray.length;\n\t\twhile (i--) {\n\t\t\tif (target.hasAttribute(attributesArray[i])) {\n\t\t\t\treturn target.getAttribute(attributesArray[i]);\n\t\t\t}\n\t\t}\n\t},\n\t_getBBox = target => {\n\t\tlet bounds;\n\t\ttry {\n\t\t\tbounds = target.getBBox(); //Firefox throws errors if you try calling getBBox() on an SVG element that's not rendered (like in a <symbol> or <defs>). https://bugzilla.mozilla.org/show_bug.cgi?id=612118\n\t\t} catch (error) {\n\t\t\tbounds = _getBBoxHack.call(target, true);\n\t\t}\n\t\t(bounds && (bounds.width || bounds.height)) || target.getBBox === _getBBoxHack || (bounds = _getBBoxHack.call(target, true));\n\t\t//some browsers (like Firefox) misreport the bounds if the element has zero width and height (it just assumes it's at x:0, y:0), thus we need to manually grab the position in that case.\n\t\treturn (bounds && !bounds.width && !bounds.x && !bounds.y) ? {x: +_getAttributeFallbacks(target, [\"x\",\"cx\",\"x1\"]) || 0, y:+_getAttributeFallbacks(target, [\"y\",\"cy\",\"y1\"]) || 0, width:0, height:0} : bounds;\n\t},\n\t_isSVG = e => !!(e.getCTM && (!e.parentNode || e.ownerSVGElement) && _getBBox(e)), //reports if the element is an SVG on which getBBox() actually works\n\t_removeProperty = (target, property) => {\n\t\tif (property) {\n\t\t\tlet style = target.style;\n\t\t\tif (property in _transformProps && property !== _transformOriginProp) {\n\t\t\t\tproperty = _transformProp;\n\t\t\t}\n\t\t\tif (style.removeProperty) {\n\t\t\t\tif (property.substr(0,2) === \"ms\" || property.substr(0,6) === \"webkit\") { //Microsoft and some Webkit browsers don't conform to the standard of capitalizing the first prefix character, so we adjust so that when we prefix the caps with a dash, it's correct (otherwise it'd be \"ms-transform\" instead of \"-ms-transform\" for IE9, for example)\n\t\t\t\t\tproperty = \"-\" + property;\n\t\t\t\t}\n\t\t\t\tstyle.removeProperty(property.replace(_capsExp, \"-$1\").toLowerCase());\n\t\t\t} else { //note: old versions of IE use \"removeAttribute()\" instead of \"removeProperty()\"\n\t\t\t\tstyle.removeAttribute(property);\n\t\t\t}\n\t\t}\n\t},\n\t_addNonTweeningPT = (plugin, target, property, beginning, end, onlySetAtEnd) => {\n\t\tlet pt = new PropTween(plugin._pt, target, property, 0, 1, onlySetAtEnd ? _renderNonTweeningValueOnlyAtEnd : _renderNonTweeningValue);\n\t\tplugin._pt = pt;\n\t\tpt.b = beginning;\n\t\tpt.e = end;\n\t\tplugin._props.push(property);\n\t\treturn pt;\n\t},\n\t_nonConvertibleUnits = {deg:1, rad:1, turn:1},\n\t_nonStandardLayouts = {grid:1, flex:1},\n\t//takes a single value like 20px and converts it to the unit specified, like \"%\", returning only the numeric amount.\n\t_convertToUnit = (target, property, value, unit) => {\n\t\tlet curValue = parseFloat(value) || 0,\n\t\t\tcurUnit = (value + \"\").trim().substr((curValue + \"\").length) || \"px\", // some browsers leave extra whitespace at the beginning of CSS variables, hence the need to trim()\n\t\t\tstyle = _tempDiv.style,\n\t\t\thorizontal = _horizontalExp.test(property),\n\t\t\tisRootSVG = target.tagName.toLowerCase() === \"svg\",\n\t\t\tmeasureProperty = (isRootSVG ? \"client\" : \"offset\") + (horizontal ? \"Width\" : \"Height\"),\n\t\t\tamount = 100,\n\t\t\ttoPixels = unit === \"px\",\n\t\t\ttoPercent = unit === \"%\",\n\t\t\tpx, parent, cache, isSVG;\n\t\tif (unit === curUnit || !curValue || _nonConvertibleUnits[unit] || _nonConvertibleUnits[curUnit]) {\n\t\t\treturn curValue;\n\t\t}\n\t\t(curUnit !== \"px\" && !toPixels) && (curValue = _convertToUnit(target, property, value, \"px\"));\n\t\tisSVG = target.getCTM && _isSVG(target);\n\t\tif ((toPercent || curUnit === \"%\") && (_transformProps[property] || ~property.indexOf(\"adius\"))) {\n\t\t\tpx = isSVG ? target.getBBox()[horizontal ? \"width\" : \"height\"] : target[measureProperty];\n\t\t\treturn _round(toPercent ? curValue / px * amount : curValue / 100 * px);\n\t\t}\n\t\tstyle[horizontal ? \"width\" : \"height\"] = amount + (toPixels ? curUnit : unit);\n\t\tparent = (~property.indexOf(\"adius\") || (unit === \"em\" && target.appendChild && !isRootSVG)) ? target : target.parentNode;\n\t\tif (isSVG) {\n\t\t\tparent = (target.ownerSVGElement || {}).parentNode;\n\t\t}\n\t\tif (!parent || parent === _doc || !parent.appendChild) {\n\t\t\tparent = _doc.body;\n\t\t}\n\t\tcache = parent._gsap;\n\t\tif (cache && toPercent && cache.width && horizontal && cache.time === _ticker.time && !cache.uncache) {\n\t\t\treturn _round(curValue / cache.width * amount);\n\t\t} else {\n\t\t\t(toPercent || curUnit === \"%\") && !_nonStandardLayouts[_getComputedProperty(parent, \"display\")] && (style.position = _getComputedProperty(target, \"position\"));\n\t\t\t(parent === target) && (style.position = \"static\"); // like for borderRadius, if it's a % we must have it relative to the target itself but that may not have position: relative or position: absolute in which case it'd go up the chain until it finds its offsetParent (bad). position: static protects against that.\n\t\t\tparent.appendChild(_tempDiv);\n\t\t\tpx = _tempDiv[measureProperty];\n\t\t\tparent.removeChild(_tempDiv);\n\t\t\tstyle.position = \"absolute\";\n\t\t\tif (horizontal && toPercent) {\n\t\t\t\tcache = _getCache(parent);\n\t\t\t\tcache.time = _ticker.time;\n\t\t\t\tcache.width = parent[measureProperty];\n\t\t\t}\n\t\t}\n\t\treturn _round(toPixels ? px * curValue / amount : px && curValue ? amount / px * curValue : 0);\n\t},\n\t_get = (target, property, unit, uncache) => {\n\t\tlet value;\n\t\t_pluginInitted || _initCore();\n\t\tif ((property in _propertyAliases) && property !== \"transform\") {\n\t\t\tproperty = _propertyAliases[property];\n\t\t\tif (~property.indexOf(\",\")) {\n\t\t\t\tproperty = property.split(\",\")[0];\n\t\t\t}\n\t\t}\n\t\tif (_transformProps[property] && property !== \"transform\") {\n\t\t\tvalue = _parseTransform(target, uncache);\n\t\t\tvalue = (property !== \"transformOrigin\") ? value[property] : value.svg ? value.origin : _firstTwoOnly(_getComputedProperty(target, _transformOriginProp)) + \" \" + value.zOrigin + \"px\";\n\t\t} else {\n\t\t\tvalue = target.style[property];\n\t\t\tif (!value || value === \"auto\" || uncache || ~(value + \"\").indexOf(\"calc(\")) {\n\t\t\t\tvalue = (_specialProps[property] && _specialProps[property](target, property, unit)) || _getComputedProperty(target, property) || _getProperty(target, property) || (property === \"opacity\" ? 1 : 0); // note: some browsers, like Firefox, don't report borderRadius correctly! Instead, it only reports every corner like  borderTopLeftRadius\n\t\t\t}\n\t\t}\n\t\treturn unit && !~(value + \"\").trim().indexOf(\" \") ? _convertToUnit(target, property, value, unit) + unit : value;\n\n\t},\n\t_tweenComplexCSSString = function(target, prop, start, end) { // note: we call _tweenComplexCSSString.call(pluginInstance...) to ensure that it's scoped properly. We may call it from within a plugin too, thus \"this\" would refer to the plugin.\n\t\tif (!start || start === \"none\") { // some browsers like Safari actually PREFER the prefixed property and mis-report the unprefixed value like clipPath (BUG). In other words, even though clipPath exists in the style (\"clipPath\" in target.style) and it's set in the CSS properly (along with -webkit-clip-path), Safari reports clipPath as \"none\" whereas WebkitClipPath reports accurately like \"ellipse(100% 0% at 50% 0%)\", so in this case we must SWITCH to using the prefixed property instead. See https://greensock.com/forums/topic/18310-clippath-doesnt-work-on-ios/\n\t\t\tlet p = _checkPropPrefix(prop, target, 1),\n\t\t\t\ts = p && _getComputedProperty(target, p, 1);\n\t\t\tif (s && s !== start) {\n\t\t\t\tprop = p;\n\t\t\t\tstart = s;\n\t\t\t} else if (prop === \"borderColor\") {\n\t\t\t\tstart = _getComputedProperty(target, \"borderTopColor\"); // Firefox bug: always reports \"borderColor\" as \"\", so we must fall back to borderTopColor. See https://greensock.com/forums/topic/24583-how-to-return-colors-that-i-had-after-reverse/\n\t\t\t}\n\t\t}\n\t\tlet pt = new PropTween(this._pt, target.style, prop, 0, 1, _renderComplexString),\n\t\t\tindex = 0,\n\t\t\tmatchIndex = 0,\n\t\t\ta, result,\tstartValues, startNum, color, startValue, endValue, endNum, chunk, endUnit, startUnit, endValues;\n\t\tpt.b = start;\n\t\tpt.e = end;\n\t\tstart += \"\"; // ensure values are strings\n\t\tend += \"\";\n\t\tif (end === \"auto\") {\n\t\t\ttarget.style[prop] = end;\n\t\t\tend = _getComputedProperty(target, prop) || end;\n\t\t\ttarget.style[prop] = start;\n\t\t}\n\t\ta = [start, end];\n\t\t_colorStringFilter(a); // pass an array with the starting and ending values and let the filter do whatever it needs to the values. If colors are found, it returns true and then we must match where the color shows up order-wise because for things like boxShadow, sometimes the browser provides the computed values with the color FIRST, but the user provides it with the color LAST, so flip them if necessary. Same for drop-shadow().\n\t\tstart = a[0];\n\t\tend = a[1];\n\t\tstartValues = start.match(_numWithUnitExp) || [];\n\t\tendValues = end.match(_numWithUnitExp) || [];\n\t\tif (endValues.length) {\n\t\t\twhile ((result = _numWithUnitExp.exec(end))) {\n\t\t\t\tendValue = result[0];\n\t\t\t\tchunk = end.substring(index, result.index);\n\t\t\t\tif (color) {\n\t\t\t\t\tcolor = (color + 1) % 5;\n\t\t\t\t} else if (chunk.substr(-5) === \"rgba(\" || chunk.substr(-5) === \"hsla(\") {\n\t\t\t\t\tcolor = 1;\n\t\t\t\t}\n\t\t\t\tif (endValue !== (startValue = startValues[matchIndex++] || \"\")) {\n\t\t\t\t\tstartNum = parseFloat(startValue) || 0;\n\t\t\t\t\tstartUnit = startValue.substr((startNum + \"\").length);\n\t\t\t\t\t(endValue.charAt(1) === \"=\") && (endValue = _parseRelative(startNum, endValue) + startUnit);\n\t\t\t\t\tendNum = parseFloat(endValue);\n\t\t\t\t\tendUnit = endValue.substr((endNum + \"\").length);\n\t\t\t\t\tindex = _numWithUnitExp.lastIndex - endUnit.length;\n\t\t\t\t\tif (!endUnit) { //if something like \"perspective:300\" is passed in and we must add a unit to the end\n\t\t\t\t\t\tendUnit = endUnit || _config.units[prop] || startUnit;\n\t\t\t\t\t\tif (index === end.length) {\n\t\t\t\t\t\t\tend += endUnit;\n\t\t\t\t\t\t\tpt.e += endUnit;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (startUnit !== endUnit) {\n\t\t\t\t\t\tstartNum = _convertToUnit(target, prop, startValue, endUnit) || 0;\n\t\t\t\t\t}\n\t\t\t\t\t// these nested PropTweens are handled in a special way - we'll never actually call a render or setter method on them. We'll just loop through them in the parent complex string PropTween's render method.\n\t\t\t\t\tpt._pt = {\n\t\t\t\t\t\t_next: pt._pt,\n\t\t\t\t\t\tp: (chunk || (matchIndex === 1)) ? chunk : \",\", //note: SVG spec allows omission of comma/space when a negative sign is wedged between two numbers, like 2.5-5.3 instead of 2.5,-5.3 but when tweening, the negative value may switch to positive, so we insert the comma just in case.\n\t\t\t\t\t\ts: startNum,\n\t\t\t\t\t\tc: endNum - startNum,\n\t\t\t\t\t\tm: (color && color < 4) || prop === \"zIndex\" ? Math.round : 0\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\t\t\tpt.c = (index < end.length) ? end.substring(index, end.length) : \"\"; //we use the \"c\" of the PropTween to store the final part of the string (after the last number)\n\t\t} else {\n\t\t\tpt.r = prop === \"display\" && end === \"none\" ? _renderNonTweeningValueOnlyAtEnd : _renderNonTweeningValue;\n\t\t}\n\t\t_relExp.test(end) && (pt.e = 0); //if the end string contains relative values or dynamic random(...) values, delete the end it so that on the final render we don't actually set it to the string with += or -= characters (forces it to use the calculated value).\n\t\tthis._pt = pt; //start the linked list with this new PropTween. Remember, we call _tweenComplexCSSString.call(pluginInstance...) to ensure that it's scoped properly. We may call it from within another plugin too, thus \"this\" would refer to the plugin.\n\t\treturn pt;\n\t},\n\t_keywordToPercent = {top:\"0%\", bottom:\"100%\", left:\"0%\", right:\"100%\", center:\"50%\"},\n\t_convertKeywordsToPercentages = value => {\n\t\tlet split = value.split(\" \"),\n\t\t\tx = split[0],\n\t\t\ty = split[1] || \"50%\";\n\t\tif (x === \"top\" || x === \"bottom\" || y === \"left\" || y === \"right\") { //the user provided them in the wrong order, so flip them\n\t\t\tvalue = x;\n\t\t\tx = y;\n\t\t\ty = value;\n\t\t}\n\t\tsplit[0] = _keywordToPercent[x] || x;\n\t\tsplit[1] = _keywordToPercent[y] || y;\n\t\treturn split.join(\" \");\n\t},\n\t_renderClearProps = (ratio, data) => {\n\t\tif (data.tween && data.tween._time === data.tween._dur) {\n\t\t\tlet target = data.t,\n\t\t\t\tstyle = target.style,\n\t\t\t\tprops = data.u,\n\t\t\t\tcache = target._gsap,\n\t\t\t\tprop, clearTransforms, i;\n\t\t\tif (props === \"all\" || props === true) {\n\t\t\t\tstyle.cssText = \"\";\n\t\t\t\tclearTransforms = 1;\n\t\t\t} else {\n\t\t\t\tprops = props.split(\",\");\n\t\t\t\ti = props.length;\n\t\t\t\twhile (--i > -1) {\n\t\t\t\t\tprop = props[i];\n\t\t\t\t\tif (_transformProps[prop]) {\n\t\t\t\t\t\tclearTransforms = 1;\n\t\t\t\t\t\tprop = (prop === \"transformOrigin\") ? _transformOriginProp : _transformProp;\n\t\t\t\t\t}\n\t\t\t\t\t_removeProperty(target, prop);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (clearTransforms) {\n\t\t\t\t_removeProperty(target, _transformProp);\n\t\t\t\tif (cache) {\n\t\t\t\t\tcache.svg && target.removeAttribute(\"transform\");\n\t\t\t\t\t_parseTransform(target, 1); // force all the cached values back to \"normal\"/identity, otherwise if there's another tween that's already set to render transforms on this element, it could display the wrong values.\n\t\t\t\t\tcache.uncache = 1;\n\t\t\t\t\t_removeIndependentTransforms(style);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t},\n\t// note: specialProps should return 1 if (and only if) they have a non-zero priority. It indicates we need to sort the linked list.\n\t_specialProps = {\n\t\tclearProps(plugin, target, property, endValue, tween) {\n\t\t\tif (tween.data !== \"isFromStart\") {\n\t\t\t\tlet pt = plugin._pt = new PropTween(plugin._pt, target, property, 0, 0, _renderClearProps);\n\t\t\t\tpt.u = endValue;\n\t\t\t\tpt.pr = -10;\n\t\t\t\tpt.tween = tween;\n\t\t\t\tplugin._props.push(property);\n\t\t\t\treturn 1;\n\t\t\t}\n\t\t}\n\t\t/* className feature (about 0.4kb gzipped).\n\t\t, className(plugin, target, property, endValue, tween) {\n\t\t\tlet _renderClassName = (ratio, data) => {\n\t\t\t\t\tdata.css.render(ratio, data.css);\n\t\t\t\t\tif (!ratio || ratio === 1) {\n\t\t\t\t\t\tlet inline = data.rmv,\n\t\t\t\t\t\t\ttarget = data.t,\n\t\t\t\t\t\t\tp;\n\t\t\t\t\t\ttarget.setAttribute(\"class\", ratio ? data.e : data.b);\n\t\t\t\t\t\tfor (p in inline) {\n\t\t\t\t\t\t\t_removeProperty(target, p);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\t_getAllStyles = (target) => {\n\t\t\t\t\tlet styles = {},\n\t\t\t\t\t\tcomputed = getComputedStyle(target),\n\t\t\t\t\t\tp;\n\t\t\t\t\tfor (p in computed) {\n\t\t\t\t\t\tif (isNaN(p) && p !== \"cssText\" && p !== \"length\") {\n\t\t\t\t\t\t\tstyles[p] = computed[p];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t_setDefaults(styles, _parseTransform(target, 1));\n\t\t\t\t\treturn styles;\n\t\t\t\t},\n\t\t\t\tstartClassList = target.getAttribute(\"class\"),\n\t\t\t\tstyle = target.style,\n\t\t\t\tcssText = style.cssText,\n\t\t\t\tcache = target._gsap,\n\t\t\t\tclassPT = cache.classPT,\n\t\t\t\tinlineToRemoveAtEnd = {},\n\t\t\t\tdata = {t:target, plugin:plugin, rmv:inlineToRemoveAtEnd, b:startClassList, e:(endValue.charAt(1) !== \"=\") ? endValue : startClassList.replace(new RegExp(\"(?:\\\\s|^)\" + endValue.substr(2) + \"(?![\\\\w-])\"), \"\") + ((endValue.charAt(0) === \"+\") ? \" \" + endValue.substr(2) : \"\")},\n\t\t\t\tchangingVars = {},\n\t\t\t\tstartVars = _getAllStyles(target),\n\t\t\t\ttransformRelated = /(transform|perspective)/i,\n\t\t\t\tendVars, p;\n\t\t\tif (classPT) {\n\t\t\t\tclassPT.r(1, classPT.d);\n\t\t\t\t_removeLinkedListItem(classPT.d.plugin, classPT, \"_pt\");\n\t\t\t}\n\t\t\ttarget.setAttribute(\"class\", data.e);\n\t\t\tendVars = _getAllStyles(target, true);\n\t\t\ttarget.setAttribute(\"class\", startClassList);\n\t\t\tfor (p in endVars) {\n\t\t\t\tif (endVars[p] !== startVars[p] && !transformRelated.test(p)) {\n\t\t\t\t\tchangingVars[p] = endVars[p];\n\t\t\t\t\tif (!style[p] && style[p] !== \"0\") {\n\t\t\t\t\t\tinlineToRemoveAtEnd[p] = 1;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tcache.classPT = plugin._pt = new PropTween(plugin._pt, target, \"className\", 0, 0, _renderClassName, data, 0, -11);\n\t\t\tif (style.cssText !== cssText) { //only apply if things change. Otherwise, in cases like a background-image that's pulled dynamically, it could cause a refresh. See https://greensock.com/forums/topic/20368-possible-gsap-bug-switching-classnames-in-chrome/.\n\t\t\t\tstyle.cssText = cssText; //we recorded cssText before we swapped classes and ran _getAllStyles() because in cases when a className tween is overwritten, we remove all the related tweening properties from that class change (otherwise class-specific stuff can't override properties we've directly set on the target's style object due to specificity).\n\t\t\t}\n\t\t\t_parseTransform(target, true); //to clear the caching of transforms\n\t\t\tdata.css = new gsap.plugins.css();\n\t\t\tdata.css.init(target, changingVars, tween);\n\t\t\tplugin._props.push(...data.css._props);\n\t\t\treturn 1;\n\t\t}\n\t\t*/\n\t},\n\n\n\n\n\n\t/*\n\t * --------------------------------------------------------------------------------------\n\t * TRANSFORMS\n\t * --------------------------------------------------------------------------------------\n\t */\n\t_identity2DMatrix = [1,0,0,1,0,0],\n\t_rotationalProperties = {},\n\t_isNullTransform = value => (value === \"matrix(1, 0, 0, 1, 0, 0)\" || value === \"none\" || !value),\n\t_getComputedTransformMatrixAsArray = target => {\n\t\tlet matrixString = _getComputedProperty(target, _transformProp);\n\t\treturn _isNullTransform(matrixString) ? _identity2DMatrix : matrixString.substr(7).match(_numExp).map(_round);\n\t},\n\t_getMatrix = (target, force2D) => {\n\t\tlet cache = target._gsap || _getCache(target),\n\t\t\tstyle = target.style,\n\t\t\tmatrix = _getComputedTransformMatrixAsArray(target),\n\t\t\tparent, nextSibling, temp, addedToDOM;\n\t\tif (cache.svg && target.getAttribute(\"transform\")) {\n\t\t\ttemp = target.transform.baseVal.consolidate().matrix; //ensures that even complex values like \"translate(50,60) rotate(135,0,0)\" are parsed because it mashes it into a matrix.\n\t\t\tmatrix = [temp.a, temp.b, temp.c, temp.d, temp.e, temp.f];\n\t\t\treturn (matrix.join(\",\") === \"1,0,0,1,0,0\") ? _identity2DMatrix : matrix;\n\t\t} else if (matrix === _identity2DMatrix && !target.offsetParent && target !== _docElement && !cache.svg) { //note: if offsetParent is null, that means the element isn't in the normal document flow, like if it has display:none or one of its ancestors has display:none). Firefox returns null for getComputedStyle() if the element is in an iframe that has display:none. https://bugzilla.mozilla.org/show_bug.cgi?id=548397\n\t\t\t//browsers don't report transforms accurately unless the element is in the DOM and has a display value that's not \"none\". Firefox and Microsoft browsers have a partial bug where they'll report transforms even if display:none BUT not any percentage-based values like translate(-50%, 8px) will be reported as if it's translate(0, 8px).\n\t\t\ttemp = style.display;\n\t\t\tstyle.display = \"block\";\n\t\t\tparent = target.parentNode;\n\t\t\tif (!parent || !target.offsetParent) { // note: in 3.3.0 we switched target.offsetParent to _doc.body.contains(target) to avoid [sometimes unnecessary] MutationObserver calls but that wasn't adequate because there are edge cases where nested position: fixed elements need to get reparented to accurately sense transforms. See https://github.com/greensock/GSAP/issues/388 and https://github.com/greensock/GSAP/issues/375\n\t\t\t\taddedToDOM = 1; //flag\n\t\t\t\tnextSibling = target.nextElementSibling;\n\t\t\t\t_docElement.appendChild(target); //we must add it to the DOM in order to get values properly\n\t\t\t}\n\t\t\tmatrix = _getComputedTransformMatrixAsArray(target);\n\t\t\ttemp ? (style.display = temp) : _removeProperty(target, \"display\");\n\t\t\tif (addedToDOM) {\n\t\t\t\tnextSibling ? parent.insertBefore(target, nextSibling) : parent ? parent.appendChild(target) : _docElement.removeChild(target);\n\t\t\t}\n\t\t}\n\t\treturn (force2D && matrix.length > 6) ? [matrix[0], matrix[1], matrix[4], matrix[5], matrix[12], matrix[13]] : matrix;\n\t},\n\t_applySVGOrigin = (target, origin, originIsAbsolute, smooth, matrixArray, pluginToAddPropTweensTo) => {\n\t\tlet cache = target._gsap,\n\t\t\tmatrix = matrixArray || _getMatrix(target, true),\n\t\t\txOriginOld = cache.xOrigin || 0,\n\t\t\tyOriginOld = cache.yOrigin || 0,\n\t\t\txOffsetOld = cache.xOffset || 0,\n\t\t\tyOffsetOld = cache.yOffset || 0,\n\t\t\ta = matrix[0],\n\t\t\tb = matrix[1],\n\t\t\tc = matrix[2],\n\t\t\td = matrix[3],\n\t\t\ttx = matrix[4],\n\t\t\tty = matrix[5],\n\t\t\toriginSplit = origin.split(\" \"),\n\t\t\txOrigin = parseFloat(originSplit[0]) || 0,\n\t\t\tyOrigin = parseFloat(originSplit[1]) || 0,\n\t\t\tbounds, determinant, x, y;\n\t\tif (!originIsAbsolute) {\n\t\t\tbounds = _getBBox(target);\n\t\t\txOrigin = bounds.x + (~originSplit[0].indexOf(\"%\") ? xOrigin / 100 * bounds.width : xOrigin);\n\t\t\tyOrigin = bounds.y + (~((originSplit[1] || originSplit[0]).indexOf(\"%\")) ? yOrigin / 100 * bounds.height : yOrigin);\n\t\t} else if (matrix !== _identity2DMatrix && (determinant = (a * d - b * c))) { //if it's zero (like if scaleX and scaleY are zero), skip it to avoid errors with dividing by zero.\n\t\t\tx = xOrigin * (d / determinant) + yOrigin * (-c / determinant) + ((c * ty - d * tx) / determinant);\n\t\t\ty = xOrigin * (-b / determinant) + yOrigin * (a / determinant) - ((a * ty - b * tx) / determinant);\n\t\t\txOrigin = x;\n\t\t\tyOrigin = y;\n\t\t}\n\t\tif (smooth || (smooth !== false && cache.smooth)) {\n\t\t\ttx = xOrigin - xOriginOld;\n\t\t\tty = yOrigin - yOriginOld;\n\t\t\tcache.xOffset = xOffsetOld + (tx * a + ty * c) - tx;\n\t\t\tcache.yOffset = yOffsetOld + (tx * b + ty * d) - ty;\n\t\t} else {\n\t\t\tcache.xOffset = cache.yOffset = 0;\n\t\t}\n\t\tcache.xOrigin = xOrigin;\n\t\tcache.yOrigin = yOrigin;\n\t\tcache.smooth = !!smooth;\n\t\tcache.origin = origin;\n\t\tcache.originIsAbsolute = !!originIsAbsolute;\n\t\ttarget.style[_transformOriginProp] = \"0px 0px\"; //otherwise, if someone sets  an origin via CSS, it will likely interfere with the SVG transform attribute ones (because remember, we're baking the origin into the matrix() value).\n\t\tif (pluginToAddPropTweensTo) {\n\t\t\t_addNonTweeningPT(pluginToAddPropTweensTo, cache, \"xOrigin\", xOriginOld, xOrigin);\n\t\t\t_addNonTweeningPT(pluginToAddPropTweensTo, cache, \"yOrigin\", yOriginOld, yOrigin);\n\t\t\t_addNonTweeningPT(pluginToAddPropTweensTo, cache, \"xOffset\", xOffsetOld, cache.xOffset);\n\t\t\t_addNonTweeningPT(pluginToAddPropTweensTo, cache, \"yOffset\", yOffsetOld, cache.yOffset);\n\t\t}\n\t\ttarget.setAttribute(\"data-svg-origin\", xOrigin + \" \" + yOrigin);\n\t},\n\t_parseTransform = (target, uncache) => {\n\t\tlet cache = target._gsap || new GSCache(target);\n\t\tif (\"x\" in cache && !uncache && !cache.uncache) {\n\t\t\treturn cache;\n\t\t}\n\t\tlet style = target.style,\n\t\t\tinvertedScaleX = cache.scaleX < 0,\n\t\t\tpx = \"px\",\n\t\t\tdeg = \"deg\",\n\t\t\tcs = getComputedStyle(target),\n\t\t\torigin = _getComputedProperty(target, _transformOriginProp) || \"0\",\n\t\t\tx, y, z, scaleX, scaleY, rotation, rotationX, rotationY, skewX, skewY, perspective, xOrigin, yOrigin,\n\t\t\tmatrix, angle, cos, sin, a, b, c, d, a12, a22, t1, t2, t3, a13, a23, a33, a42, a43, a32;\n\t\tx = y = z = rotation = rotationX = rotationY = skewX = skewY = perspective = 0;\n\t\tscaleX = scaleY = 1;\n\t\tcache.svg = !!(target.getCTM && _isSVG(target));\n\n\t\tif (cs.translate) { // accommodate independent transforms by combining them into normal ones.\n\t\t\tif (cs.translate !== \"none\" || cs.scale !== \"none\" || cs.rotate !== \"none\") {\n\t\t\t\tstyle[_transformProp] = (cs.translate !== \"none\" ? \"translate3d(\" + (cs.translate + \" 0 0\").split(\" \").slice(0, 3).join(\", \") + \") \" : \"\") + (cs.rotate !== \"none\" ? \"rotate(\" + cs.rotate + \") \" : \"\") + (cs.scale !== \"none\" ? \"scale(\" + cs.scale.split(\" \").join(\",\") + \") \" : \"\") + (cs[_transformProp] !== \"none\" ? cs[_transformProp] : \"\");\n\t\t\t}\n\t\t\tstyle.scale = style.rotate = style.translate = \"none\";\n\t\t}\n\n\t\tmatrix = _getMatrix(target, cache.svg);\n\t\tif (cache.svg) {\n\t\t\tif (cache.uncache) { // if cache.uncache is true (and maybe if origin is 0,0), we need to set element.style.transformOrigin = (cache.xOrigin - bbox.x) + \"px \" + (cache.yOrigin - bbox.y) + \"px\". Previously we let the data-svg-origin stay instead, but when introducing revert(), it complicated things.\n\t\t\t\tt2 = target.getBBox();\n\t\t\t\torigin = (cache.xOrigin - t2.x) + \"px \" + (cache.yOrigin - t2.y) + \"px\";\n\t\t\t\tt1 = \"\";\n\t\t\t} else {\n\t\t\t\tt1 = !uncache && target.getAttribute(\"data-svg-origin\"); //  Remember, to work around browser inconsistencies we always force SVG elements' transformOrigin to 0,0 and offset the translation accordingly.\n\t\t\t}\n\t\t\t_applySVGOrigin(target, t1 || origin, !!t1 || cache.originIsAbsolute, cache.smooth !== false, matrix);\n\t\t}\n\t\txOrigin = cache.xOrigin || 0;\n\t\tyOrigin = cache.yOrigin || 0;\n\t\tif (matrix !== _identity2DMatrix) {\n\t\t\ta = matrix[0]; //a11\n\t\t\tb = matrix[1]; //a21\n\t\t\tc = matrix[2]; //a31\n\t\t\td = matrix[3]; //a41\n\t\t\tx = a12 = matrix[4];\n\t\t\ty = a22 = matrix[5];\n\n\t\t\t//2D matrix\n\t\t\tif (matrix.length === 6) {\n\t\t\t\tscaleX = Math.sqrt(a * a + b * b);\n\t\t\t\tscaleY = Math.sqrt(d * d + c * c);\n\t\t\t\trotation = (a || b) ? _atan2(b, a) * _RAD2DEG : 0; //note: if scaleX is 0, we cannot accurately measure rotation. Same for skewX with a scaleY of 0. Therefore, we default to the previously recorded value (or zero if that doesn't exist).\n\t\t\t\tskewX = (c || d) ? _atan2(c, d) * _RAD2DEG + rotation : 0;\n\t\t\t\tskewX && (scaleY *= Math.abs(Math.cos(skewX * _DEG2RAD)));\n\t\t\t\tif (cache.svg) {\n\t\t\t\t\tx -= xOrigin - (xOrigin * a + yOrigin * c);\n\t\t\t\t\ty -= yOrigin - (xOrigin * b + yOrigin * d);\n\t\t\t\t}\n\n\t\t\t//3D matrix\n\t\t\t} else {\n\t\t\t\ta32 = matrix[6];\n\t\t\t\ta42 = matrix[7];\n\t\t\t\ta13 = matrix[8];\n\t\t\t\ta23 = matrix[9];\n\t\t\t\ta33 = matrix[10];\n\t\t\t\ta43 = matrix[11];\n\t\t\t\tx = matrix[12];\n\t\t\t\ty = matrix[13];\n\t\t\t\tz = matrix[14];\n\n\t\t\t\tangle = _atan2(a32, a33);\n\t\t\t\trotationX = angle * _RAD2DEG;\n\t\t\t\t//rotationX\n\t\t\t\tif (angle) {\n\t\t\t\t\tcos = Math.cos(-angle);\n\t\t\t\t\tsin = Math.sin(-angle);\n\t\t\t\t\tt1 = a12*cos+a13*sin;\n\t\t\t\t\tt2 = a22*cos+a23*sin;\n\t\t\t\t\tt3 = a32*cos+a33*sin;\n\t\t\t\t\ta13 = a12*-sin+a13*cos;\n\t\t\t\t\ta23 = a22*-sin+a23*cos;\n\t\t\t\t\ta33 = a32*-sin+a33*cos;\n\t\t\t\t\ta43 = a42*-sin+a43*cos;\n\t\t\t\t\ta12 = t1;\n\t\t\t\t\ta22 = t2;\n\t\t\t\t\ta32 = t3;\n\t\t\t\t}\n\t\t\t\t//rotationY\n\t\t\t\tangle = _atan2(-c, a33);\n\t\t\t\trotationY = angle * _RAD2DEG;\n\t\t\t\tif (angle) {\n\t\t\t\t\tcos = Math.cos(-angle);\n\t\t\t\t\tsin = Math.sin(-angle);\n\t\t\t\t\tt1 = a*cos-a13*sin;\n\t\t\t\t\tt2 = b*cos-a23*sin;\n\t\t\t\t\tt3 = c*cos-a33*sin;\n\t\t\t\t\ta43 = d*sin+a43*cos;\n\t\t\t\t\ta = t1;\n\t\t\t\t\tb = t2;\n\t\t\t\t\tc = t3;\n\t\t\t\t}\n\t\t\t\t//rotationZ\n\t\t\t\tangle = _atan2(b, a);\n\t\t\t\trotation = angle * _RAD2DEG;\n\t\t\t\tif (angle) {\n\t\t\t\t\tcos = Math.cos(angle);\n\t\t\t\t\tsin = Math.sin(angle);\n\t\t\t\t\tt1 = a*cos+b*sin;\n\t\t\t\t\tt2 = a12*cos+a22*sin;\n\t\t\t\t\tb = b*cos-a*sin;\n\t\t\t\t\ta22 = a22*cos-a12*sin;\n\t\t\t\t\ta = t1;\n\t\t\t\t\ta12 = t2;\n\t\t\t\t}\n\n\t\t\t\tif (rotationX && Math.abs(rotationX) + Math.abs(rotation) > 359.9) { //when rotationY is set, it will often be parsed as 180 degrees different than it should be, and rotationX and rotation both being 180 (it looks the same), so we adjust for that here.\n\t\t\t\t\trotationX = rotation = 0;\n\t\t\t\t\trotationY = 180 - rotationY;\n\t\t\t\t}\n\t\t\t\tscaleX = _round(Math.sqrt(a * a + b * b + c * c));\n\t\t\t\tscaleY = _round(Math.sqrt(a22 * a22 + a32 * a32));\n\t\t\t\tangle = _atan2(a12, a22);\n\t\t\t\tskewX = (Math.abs(angle) > 0.0002) ? angle * _RAD2DEG : 0;\n\t\t\t\tperspective = a43 ? 1 / ((a43 < 0) ? -a43 : a43) : 0;\n\t\t\t}\n\n\t\t\tif (cache.svg) { //sense if there are CSS transforms applied on an SVG element in which case we must overwrite them when rendering. The transform attribute is more reliable cross-browser, but we can't just remove the CSS ones because they may be applied in a CSS rule somewhere (not just inline).\n\t\t\t\tt1 = target.getAttribute(\"transform\");\n\t\t\t\tcache.forceCSS = target.setAttribute(\"transform\", \"\") || (!_isNullTransform(_getComputedProperty(target, _transformProp)));\n\t\t\t\tt1 && target.setAttribute(\"transform\", t1);\n\t\t\t}\n\t\t}\n\n\t\tif (Math.abs(skewX) > 90 && Math.abs(skewX) < 270) {\n\t\t\tif (invertedScaleX) {\n\t\t\t\tscaleX *= -1;\n\t\t\t\tskewX += (rotation <= 0) ? 180 : -180;\n\t\t\t\trotation += (rotation <= 0) ? 180 : -180;\n\t\t\t} else {\n\t\t\t\tscaleY *= -1;\n\t\t\t\tskewX += (skewX <= 0) ? 180 : -180;\n\t\t\t}\n\t\t}\n\t\tuncache = uncache || cache.uncache;\n\t\tcache.x = x - ((cache.xPercent = x && ((!uncache && cache.xPercent) || (Math.round(target.offsetWidth / 2) === Math.round(-x) ? -50 : 0))) ? target.offsetWidth * cache.xPercent / 100 : 0) + px;\n\t\tcache.y = y - ((cache.yPercent = y && ((!uncache && cache.yPercent) || (Math.round(target.offsetHeight / 2) === Math.round(-y) ? -50 : 0))) ? target.offsetHeight * cache.yPercent / 100 : 0) + px;\n\t\tcache.z = z + px;\n\t\tcache.scaleX = _round(scaleX);\n\t\tcache.scaleY = _round(scaleY);\n\t\tcache.rotation = _round(rotation) + deg;\n\t\tcache.rotationX = _round(rotationX) + deg;\n\t\tcache.rotationY = _round(rotationY) + deg;\n\t\tcache.skewX = skewX + deg;\n\t\tcache.skewY = skewY + deg;\n\t\tcache.transformPerspective = perspective + px;\n\t\tif ((cache.zOrigin = parseFloat(origin.split(\" \")[2]) || 0)) {\n\t\t\tstyle[_transformOriginProp] = _firstTwoOnly(origin);\n\t\t}\n\t\tcache.xOffset = cache.yOffset = 0;\n\t\tcache.force3D = _config.force3D;\n\t\tcache.renderTransform = cache.svg ? _renderSVGTransforms : _supports3D ? _renderCSSTransforms : _renderNon3DTransforms;\n\t\tcache.uncache = 0;\n\t\treturn cache;\n\t},\n\t_firstTwoOnly = value => (value = value.split(\" \"))[0] + \" \" + value[1], //for handling transformOrigin values, stripping out the 3rd dimension\n\t_addPxTranslate = (target, start, value) => {\n\t\tlet unit = getUnit(start);\n\t\treturn _round(parseFloat(start) + parseFloat(_convertToUnit(target, \"x\", value + \"px\", unit))) + unit;\n\t},\n\t_renderNon3DTransforms = (ratio, cache) => {\n\t\tcache.z = \"0px\";\n\t\tcache.rotationY = cache.rotationX = \"0deg\";\n\t\tcache.force3D = 0;\n\t\t_renderCSSTransforms(ratio, cache);\n\t},\n\t_zeroDeg = \"0deg\",\n\t_zeroPx = \"0px\",\n\t_endParenthesis = \") \",\n\t_renderCSSTransforms = function(ratio, cache) {\n\t\tlet {xPercent, yPercent, x, y, z, rotation, rotationY, rotationX, skewX, skewY, scaleX, scaleY, transformPerspective, force3D, target, zOrigin} = cache || this,\n\t\t\ttransforms = \"\",\n\t\t\tuse3D = (force3D === \"auto\" && ratio && ratio !== 1) || force3D === true;\n\n\t\t// Safari has a bug that causes it not to render 3D transform-origin values properly, so we force the z origin to 0, record it in the cache, and then do the math here to offset the translate values accordingly (basically do the 3D transform-origin part manually)\n\t\tif (zOrigin && (rotationX !== _zeroDeg || rotationY !== _zeroDeg)) {\n\t\t\tlet angle = parseFloat(rotationY) * _DEG2RAD,\n\t\t\t\ta13 = Math.sin(angle),\n\t\t\t\ta33 = Math.cos(angle),\n\t\t\t\tcos;\n\t\t\tangle = parseFloat(rotationX) * _DEG2RAD;\n\t\t\tcos = Math.cos(angle);\n\t\t\tx = _addPxTranslate(target, x, a13 * cos * -zOrigin);\n\t\t\ty = _addPxTranslate(target, y, -Math.sin(angle) * -zOrigin);\n\t\t\tz = _addPxTranslate(target, z, a33 * cos * -zOrigin + zOrigin);\n\t\t}\n\n\t\tif (transformPerspective !== _zeroPx) {\n\t\t\ttransforms += \"perspective(\" + transformPerspective + _endParenthesis;\n\t\t}\n\t\tif (xPercent || yPercent) {\n\t\t\ttransforms += \"translate(\" + xPercent + \"%, \" + yPercent + \"%) \";\n\t\t}\n\t\tif (use3D || x !== _zeroPx || y !== _zeroPx || z !== _zeroPx) {\n\t\t\ttransforms += (z !== _zeroPx || use3D) ? \"translate3d(\" + x + \", \" + y + \", \" + z + \") \" : \"translate(\" + x + \", \" + y + _endParenthesis;\n\t\t}\n\t\tif (rotation !== _zeroDeg) {\n\t\t\ttransforms += \"rotate(\" + rotation + _endParenthesis;\n\t\t}\n\t\tif (rotationY !== _zeroDeg) {\n\t\t\ttransforms += \"rotateY(\" + rotationY + _endParenthesis;\n\t\t}\n\t\tif (rotationX !== _zeroDeg) {\n\t\t\ttransforms += \"rotateX(\" + rotationX + _endParenthesis;\n\t\t}\n\t\tif (skewX !== _zeroDeg || skewY !== _zeroDeg) {\n\t\t\ttransforms += \"skew(\" + skewX + \", \" + skewY + _endParenthesis;\n\t\t}\n\t\tif (scaleX !== 1 || scaleY !== 1) {\n\t\t\ttransforms += \"scale(\" + scaleX + \", \" + scaleY + _endParenthesis;\n\t\t}\n\t\ttarget.style[_transformProp] = transforms || \"translate(0, 0)\";\n\t},\n\t_renderSVGTransforms = function(ratio, cache) {\n\t\tlet {xPercent, yPercent, x, y, rotation, skewX, skewY, scaleX, scaleY, target, xOrigin, yOrigin, xOffset, yOffset, forceCSS} = cache || this,\n\t\t\ttx = parseFloat(x),\n\t\t\tty = parseFloat(y),\n\t\t\ta11, a21, a12, a22, temp;\n\t\trotation = parseFloat(rotation);\n\t\tskewX = parseFloat(skewX);\n\t\tskewY = parseFloat(skewY);\n\t\tif (skewY) { //for performance reasons, we combine all skewing into the skewX and rotation values. Remember, a skewY of 10 degrees looks the same as a rotation of 10 degrees plus a skewX of 10 degrees.\n\t\t\tskewY = parseFloat(skewY);\n\t\t\tskewX += skewY;\n\t\t\trotation += skewY;\n\t\t}\n\t\tif (rotation || skewX) {\n\t\t\trotation *= _DEG2RAD;\n\t\t\tskewX *= _DEG2RAD;\n\t\t\ta11 = Math.cos(rotation) * scaleX;\n\t\t\ta21 = Math.sin(rotation) * scaleX;\n\t\t\ta12 = Math.sin(rotation - skewX) * -scaleY;\n\t\t\ta22 = Math.cos(rotation - skewX) * scaleY;\n\t\t\tif (skewX) {\n\t\t\t\tskewY *= _DEG2RAD;\n\t\t\t\ttemp = Math.tan(skewX - skewY);\n\t\t\t\ttemp = Math.sqrt(1 + temp * temp);\n\t\t\t\ta12 *= temp;\n\t\t\t\ta22 *= temp;\n\t\t\t\tif (skewY) {\n\t\t\t\t\ttemp = Math.tan(skewY);\n\t\t\t\t\ttemp = Math.sqrt(1 + temp * temp);\n\t\t\t\t\ta11 *= temp;\n\t\t\t\t\ta21 *= temp;\n\t\t\t\t}\n\t\t\t}\n\t\t\ta11 = _round(a11);\n\t\t\ta21 = _round(a21);\n\t\t\ta12 = _round(a12);\n\t\t\ta22 = _round(a22);\n\t\t} else {\n\t\t\ta11 = scaleX;\n\t\t\ta22 = scaleY;\n\t\t\ta21 = a12 = 0;\n\t\t}\n\t\tif ((tx && !~(x + \"\").indexOf(\"px\")) || (ty && !~(y + \"\").indexOf(\"px\"))) {\n\t\t\ttx = _convertToUnit(target, \"x\", x, \"px\");\n\t\t\tty = _convertToUnit(target, \"y\", y, \"px\");\n\t\t}\n\t\tif (xOrigin || yOrigin || xOffset || yOffset) {\n\t\t\ttx = _round(tx + xOrigin - (xOrigin * a11 + yOrigin * a12) + xOffset);\n\t\t\tty = _round(ty + yOrigin - (xOrigin * a21 + yOrigin * a22) + yOffset);\n\t\t}\n\t\tif (xPercent || yPercent) {\n\t\t\t//The SVG spec doesn't support percentage-based translation in the \"transform\" attribute, so we merge it into the translation to simulate it.\n\t\t\ttemp = target.getBBox();\n\t\t\ttx = _round(tx + xPercent / 100 * temp.width);\n\t\t\tty = _round(ty + yPercent / 100 * temp.height);\n\t\t}\n\t\ttemp = \"matrix(\" + a11 + \",\" + a21 + \",\" + a12 + \",\" + a22 + \",\" + tx + \",\" + ty + \")\";\n\t\ttarget.setAttribute(\"transform\", temp);\n\t\tforceCSS && (target.style[_transformProp] = temp); //some browsers prioritize CSS transforms over the transform attribute. When we sense that the user has CSS transforms applied, we must overwrite them this way (otherwise some browser simply won't render the transform attribute changes!)\n\t},\n\t_addRotationalPropTween = function(plugin, target, property, startNum, endValue) {\n\t\tlet cap = 360,\n\t\t\tisString = _isString(endValue),\n\t\t\tendNum = parseFloat(endValue) * ((isString && ~endValue.indexOf(\"rad\")) ? _RAD2DEG : 1),\n\t\t\tchange = endNum - startNum,\n\t\t\tfinalValue = (startNum + change) + \"deg\",\n\t\t\tdirection, pt;\n\t\tif (isString) {\n\t\t\tdirection = endValue.split(\"_\")[1];\n\t\t\tif (direction === \"short\") {\n\t\t\t\tchange %= cap;\n\t\t\t\tif (change !== change % (cap / 2)) {\n\t\t\t\t\tchange += (change < 0) ? cap : -cap;\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (direction === \"cw\" && change < 0) {\n\t\t\t\tchange = ((change + cap * _bigNum) % cap) - ~~(change / cap) * cap;\n\t\t\t} else if (direction === \"ccw\" && change > 0) {\n\t\t\t\tchange = ((change - cap * _bigNum) % cap) - ~~(change / cap) * cap;\n\t\t\t}\n\t\t}\n\t\tplugin._pt = pt = new PropTween(plugin._pt, target, property, startNum, change, _renderPropWithEnd);\n\t\tpt.e = finalValue;\n\t\tpt.u = \"deg\";\n\t\tplugin._props.push(property);\n\t\treturn pt;\n\t},\n\t_assign = (target, source) => { // Internet Explorer doesn't have Object.assign(), so we recreate it here.\n\t\tfor (let p in source) {\n\t\t\ttarget[p] = source[p];\n\t\t}\n\t\treturn target;\n\t},\n\t_addRawTransformPTs = (plugin, transforms, target) => { //for handling cases where someone passes in a whole transform string, like transform: \"scale(2, 3) rotate(20deg) translateY(30em)\"\n\t\tlet startCache = _assign({}, target._gsap),\n\t\t\texclude = \"perspective,force3D,transformOrigin,svgOrigin\",\n\t\t\tstyle = target.style,\n\t\t\tendCache, p, startValue, endValue, startNum, endNum, startUnit, endUnit;\n\t\tif (startCache.svg) {\n\t\t\tstartValue = target.getAttribute(\"transform\");\n\t\t\ttarget.setAttribute(\"transform\", \"\");\n\t\t\tstyle[_transformProp] = transforms;\n\t\t\tendCache = _parseTransform(target, 1);\n\t\t\t_removeProperty(target, _transformProp);\n\t\t\ttarget.setAttribute(\"transform\", startValue);\n\t\t} else {\n\t\t\tstartValue = getComputedStyle(target)[_transformProp];\n\t\t\tstyle[_transformProp] = transforms;\n\t\t\tendCache = _parseTransform(target, 1);\n\t\t\tstyle[_transformProp] = startValue;\n\t\t}\n\t\tfor (p in _transformProps) {\n\t\t\tstartValue = startCache[p];\n\t\t\tendValue = endCache[p];\n\t\t\tif (startValue !== endValue && exclude.indexOf(p) < 0) { //tweening to no perspective gives very unintuitive results - just keep the same perspective in that case.\n\t\t\t\tstartUnit = getUnit(startValue);\n\t\t\t\tendUnit = getUnit(endValue);\n\t\t\t\tstartNum = (startUnit !== endUnit) ? _convertToUnit(target, p, startValue, endUnit) : parseFloat(startValue);\n\t\t\t\tendNum = parseFloat(endValue);\n\t\t\t\tplugin._pt = new PropTween(plugin._pt, endCache, p, startNum, endNum - startNum, _renderCSSProp);\n\t\t\t\tplugin._pt.u = endUnit || 0;\n\t\t\t\tplugin._props.push(p);\n\t\t\t}\n\t\t}\n\t\t_assign(endCache, startCache);\n\t};\n\n// handle splitting apart padding, margin, borderWidth, and borderRadius into their 4 components. Firefox, for example, won't report borderRadius correctly - it will only do borderTopLeftRadius and the other corners. We also want to handle paddingTop, marginLeft, borderRightWidth, etc.\n_forEachName(\"padding,margin,Width,Radius\", (name, index) => {\n\tlet t = \"Top\",\n\t\tr = \"Right\",\n\t\tb = \"Bottom\",\n\t\tl = \"Left\",\n\t\tprops = (index < 3 ? [t,r,b,l] : [t+l, t+r, b+r, b+l]).map(side => index < 2 ? name + side : \"border\" + side + name);\n\t_specialProps[(index > 1 ? \"border\" + name : name)] = function(plugin, target, property, endValue, tween) {\n\t\tlet a, vars;\n\t\tif (arguments.length < 4) { // getter, passed target, property, and unit (from _get())\n\t\t\ta = props.map(prop => _get(plugin, prop, property));\n\t\t\tvars = a.join(\" \");\n\t\t\treturn vars.split(a[0]).length === 5 ? a[0] : vars;\n\t\t}\n\t\ta = (endValue + \"\").split(\" \");\n\t\tvars = {};\n\t\tprops.forEach((prop, i) => vars[prop] = a[i] = a[i] || a[(((i - 1) / 2) | 0)]);\n\t\tplugin.init(target, vars, tween);\n\t}\n});\n\n\nexport const CSSPlugin = {\n\tname: \"css\",\n\tregister: _initCore,\n\ttargetTest(target) {\n\t\treturn target.style && target.nodeType;\n\t},\n\tinit(target, vars, tween, index, targets) {\n\t\tlet props = this._props,\n\t\t\tstyle = target.style,\n\t\t\tstartAt = tween.vars.startAt,\n\t\t\tstartValue, endValue, endNum, startNum, type, specialProp, p, startUnit, endUnit, relative, isTransformRelated, transformPropTween, cache, smooth, hasPriority, inlineProps;\n\t\t_pluginInitted || _initCore();\n\t\t// we may call init() multiple times on the same plugin instance, like when adding special properties, so make sure we don't overwrite the revert data or inlineProps\n\t\tthis.styles = this.styles || _getStyleSaver(target);\n\t\tinlineProps = this.styles.props;\n\t\tthis.tween = tween;\n\t\tfor (p in vars) {\n\t\t\tif (p === \"autoRound\") {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\tendValue = vars[p];\n\t\t\tif (_plugins[p] && _checkPlugin(p, vars, tween, index, target, targets)) { // plugins\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\ttype = typeof(endValue);\n\t\t\tspecialProp = _specialProps[p];\n\t\t\tif (type === \"function\") {\n\t\t\t\tendValue = endValue.call(tween, index, target, targets);\n\t\t\t\ttype = typeof(endValue);\n\t\t\t}\n\t\t\tif (type === \"string\" && ~endValue.indexOf(\"random(\")) {\n\t\t\t\tendValue = _replaceRandom(endValue);\n\t\t\t}\n\t\t\tif (specialProp) {\n\t\t\t\tspecialProp(this, target, p, endValue, tween) && (hasPriority = 1);\n\t\t\t} else if (p.substr(0,2) === \"--\") { //CSS variable\n\t\t\t\tstartValue = (getComputedStyle(target).getPropertyValue(p) + \"\").trim();\n\t\t\t\tendValue += \"\";\n\t\t\t\t_colorExp.lastIndex = 0;\n\t\t\t\tif (!_colorExp.test(startValue)) { // colors don't have units\n\t\t\t\t\tstartUnit = getUnit(startValue);\n\t\t\t\t\tendUnit = getUnit(endValue);\n\t\t\t\t}\n\t\t\t\tendUnit ? startUnit !== endUnit && (startValue = _convertToUnit(target, p, startValue, endUnit) + endUnit) : startUnit && (endValue += startUnit);\n\t\t\t\tthis.add(style, \"setProperty\", startValue, endValue, index, targets, 0, 0, p);\n\t\t\t\tprops.push(p);\n\t\t\t\tinlineProps.push(p, 0, style[p]);\n\t\t\t} else if (type !== \"undefined\") {\n\t\t\t\tif (startAt && p in startAt) { // in case someone hard-codes a complex value as the start, like top: \"calc(2vh / 2)\". Without this, it'd use the computed value (always in px)\n\t\t\t\t\tstartValue = typeof(startAt[p]) === \"function\" ? startAt[p].call(tween, index, target, targets) : startAt[p];\n\t\t\t\t\t_isString(startValue) && ~startValue.indexOf(\"random(\") && (startValue = _replaceRandom(startValue));\n\t\t\t\t\tgetUnit(startValue + \"\") || (startValue += _config.units[p] || getUnit(_get(target, p)) || \"\"); // for cases when someone passes in a unitless value like {x: 100}; if we try setting translate(100, 0px) it won't work.\n\t\t\t\t\t(startValue + \"\").charAt(1) === \"=\" && (startValue = _get(target, p)); // can't work with relative values\n\t\t\t\t} else {\n\t\t\t\t\tstartValue = _get(target, p);\n\t\t\t\t}\n\t\t\t\tstartNum = parseFloat(startValue);\n\t\t\t\trelative = (type === \"string\" && endValue.charAt(1) === \"=\") && endValue.substr(0, 2);\n\t\t\t\trelative && (endValue = endValue.substr(2));\n\t\t\t\tendNum = parseFloat(endValue);\n\t\t\t\tif (p in _propertyAliases) {\n\t\t\t\t\tif (p === \"autoAlpha\") { //special case where we control the visibility along with opacity. We still allow the opacity value to pass through and get tweened.\n\t\t\t\t\t\tif (startNum === 1 && _get(target, \"visibility\") === \"hidden\" && endNum) { //if visibility is initially set to \"hidden\", we should interpret that as intent to make opacity 0 (a convenience)\n\t\t\t\t\t\t\tstartNum = 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tinlineProps.push(\"visibility\", 0, style.visibility);\n\t\t\t\t\t\t_addNonTweeningPT(this, style, \"visibility\", startNum ? \"inherit\" : \"hidden\", endNum ? \"inherit\" : \"hidden\", !endNum);\n\t\t\t\t\t}\n\t\t\t\t\tif (p !== \"scale\" && p !== \"transform\") {\n\t\t\t\t\t\tp = _propertyAliases[p];\n\t\t\t\t\t\t~p.indexOf(\",\") && (p = p.split(\",\")[0]);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tisTransformRelated = (p in _transformProps);\n\n\t\t\t\t//--- TRANSFORM-RELATED ---\n\t\t\t\tif (isTransformRelated) {\n\t\t\t\t\tthis.styles.save(p);\n\t\t\t\t\tif (!transformPropTween) {\n\t\t\t\t\t\tcache = target._gsap;\n\t\t\t\t\t\t(cache.renderTransform && !vars.parseTransform) || _parseTransform(target, vars.parseTransform); // if, for example, gsap.set(... {transform:\"translateX(50vw)\"}), the _get() call doesn't parse the transform, thus cache.renderTransform won't be set yet so force the parsing of the transform here.\n\t\t\t\t\t\tsmooth = (vars.smoothOrigin !== false && cache.smooth);\n\t\t\t\t\t\ttransformPropTween = this._pt = new PropTween(this._pt, style, _transformProp, 0, 1, cache.renderTransform, cache, 0, -1); //the first time through, create the rendering PropTween so that it runs LAST (in the linked list, we keep adding to the beginning)\n\t\t\t\t\t\ttransformPropTween.dep = 1; //flag it as dependent so that if things get killed/overwritten and this is the only PropTween left, we can safely kill the whole tween.\n\t\t\t\t\t}\n\t\t\t\t\tif (p === \"scale\") {\n\t\t\t\t\t\tthis._pt = new PropTween(this._pt, cache, \"scaleY\", cache.scaleY, ((relative ? _parseRelative(cache.scaleY, relative + endNum) : endNum) - cache.scaleY) || 0, _renderCSSProp);\n\t\t\t\t\t\tthis._pt.u = 0;\n\t\t\t\t\t\tprops.push(\"scaleY\", p);\n\t\t\t\t\t\tp += \"X\";\n\t\t\t\t\t} else if (p === \"transformOrigin\") {\n\t\t\t\t\t\tinlineProps.push(_transformOriginProp, 0, style[_transformOriginProp]);\n\t\t\t\t\t\tendValue = _convertKeywordsToPercentages(endValue); //in case something like \"left top\" or \"bottom right\" is passed in. Convert to percentages.\n\t\t\t\t\t\tif (cache.svg) {\n\t\t\t\t\t\t\t_applySVGOrigin(target, endValue, 0, smooth, 0, this);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tendUnit = parseFloat(endValue.split(\" \")[2]) || 0; //handle the zOrigin separately!\n\t\t\t\t\t\t\tendUnit !== cache.zOrigin && _addNonTweeningPT(this, cache, \"zOrigin\", cache.zOrigin, endUnit);\n\t\t\t\t\t\t\t_addNonTweeningPT(this, style, p, _firstTwoOnly(startValue), _firstTwoOnly(endValue));\n\t\t\t\t\t\t}\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t} else if (p === \"svgOrigin\") {\n\t\t\t\t\t\t_applySVGOrigin(target, endValue, 1, smooth, 0, this);\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t} else if (p in _rotationalProperties) {\n\t\t\t\t\t\t_addRotationalPropTween(this, cache, p, startNum, relative ? _parseRelative(startNum, relative + endValue) : endValue);\n\t\t\t\t\t\tcontinue;\n\n\t\t\t\t\t} else if (p === \"smoothOrigin\") {\n\t\t\t\t\t\t_addNonTweeningPT(this, cache, \"smooth\", cache.smooth, endValue);\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t} else if (p === \"force3D\") {\n\t\t\t\t\t\tcache[p] = endValue;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t} else if (p === \"transform\") {\n\t\t\t\t\t\t_addRawTransformPTs(this, endValue, target);\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t} else if (!(p in style)) {\n\t\t\t\t\tp = _checkPropPrefix(p) || p;\n\t\t\t\t}\n\n\t\t\t\tif (isTransformRelated || ((endNum || endNum === 0) && (startNum || startNum === 0) && !_complexExp.test(endValue) && (p in style))) {\n\t\t\t\t\tstartUnit = (startValue + \"\").substr((startNum + \"\").length);\n\t\t\t\t\tendNum || (endNum = 0); // protect against NaN\n\t\t\t\t\tendUnit = getUnit(endValue) || ((p in _config.units) ? _config.units[p] : startUnit);\n\t\t\t\t\tstartUnit !== endUnit && (startNum = _convertToUnit(target, p, startValue, endUnit));\n\t\t\t\t\tthis._pt = new PropTween(this._pt, isTransformRelated ? cache : style, p, startNum, (relative ? _parseRelative(startNum, relative + endNum) : endNum) - startNum, (!isTransformRelated && (endUnit === \"px\" || p === \"zIndex\") && vars.autoRound !== false) ? _renderRoundedCSSProp : _renderCSSProp);\n\t\t\t\t\tthis._pt.u = endUnit || 0;\n\t\t\t\t\tif (startUnit !== endUnit && endUnit !== \"%\") { //when the tween goes all the way back to the beginning, we need to revert it to the OLD/ORIGINAL value (with those units). We record that as a \"b\" (beginning) property and point to a render method that handles that. (performance optimization)\n\t\t\t\t\t\tthis._pt.b = startValue;\n\t\t\t\t\t\tthis._pt.r = _renderCSSPropWithBeginning;\n\t\t\t\t\t}\n\t\t\t\t} else if (!(p in style)) {\n\t\t\t\t\tif (p in target) { //maybe it's not a style - it could be a property added directly to an element in which case we'll try to animate that.\n\t\t\t\t\t\tthis.add(target, p, startValue || target[p], relative ? relative + endValue : endValue, index, targets);\n\t\t\t\t\t} else if (p !== \"parseTransform\") {\n\t\t\t\t\t\t_missingPlugin(p, endValue);\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t_tweenComplexCSSString.call(this, target, p, startValue, relative ? relative + endValue : endValue);\n\t\t\t\t}\n\t\t\t\tisTransformRelated || (p in style ? inlineProps.push(p, 0, style[p]) : inlineProps.push(p, 1, startValue || target[p]));\n\t\t\t\tprops.push(p);\n\t\t\t}\n\t\t}\n\t\thasPriority && _sortPropTweensByPriority(this);\n\n\t},\n\trender(ratio, data) {\n\t\tif (data.tween._time || !_reverting()) {\n\t\t\tlet pt = data._pt;\n\t\t\twhile (pt) {\n\t\t\t\tpt.r(ratio, pt.d);\n\t\t\t\tpt = pt._next;\n\t\t\t}\n\t\t} else {\n\t\t\tdata.styles.revert();\n\t\t}\n\t},\n\tget: _get,\n\taliases: _propertyAliases,\n\tgetSetter(target, property, plugin) { //returns a setter function that accepts target, property, value and applies it accordingly. Remember, properties like \"x\" aren't as simple as target.style.property = value because they've got to be applied to a proxy object and then merged into a transform string in a renderer.\n\t\tlet p = _propertyAliases[property];\n\t\t(p && p.indexOf(\",\") < 0) && (property = p);\n\t\treturn (property in _transformProps && property !== _transformOriginProp && (target._gsap.x || _get(target, \"x\"))) ? (plugin && _recentSetterPlugin === plugin ? (property === \"scale\" ? _setterScale : _setterTransform) : (_recentSetterPlugin = plugin || {}) && (property === \"scale\" ? _setterScaleWithRender : _setterTransformWithRender)) : target.style && !_isUndefined(target.style[property]) ? _setterCSSStyle : ~property.indexOf(\"-\") ? _setterCSSProp : _getSetter(target, property);\n\t},\n\tcore: { _removeProperty, _getMatrix }\n\n};\n\ngsap.utils.checkPrefix = _checkPropPrefix;\ngsap.core.getStyleSaver = _getStyleSaver;\n(function(positionAndScale, rotation, others, aliases) {\n\tlet all = _forEachName(positionAndScale + \",\" + rotation + \",\" + others, name => {_transformProps[name] = 1});\n\t_forEachName(rotation, name => {_config.units[name] = \"deg\"; _rotationalProperties[name] = 1});\n\t_propertyAliases[all[13]] = positionAndScale + \",\" + rotation;\n\t_forEachName(aliases, name => {\n\t\tlet split = name.split(\":\");\n\t\t_propertyAliases[split[1]] = all[split[0]];\n\t});\n})(\"x,y,z,scale,scaleX,scaleY,xPercent,yPercent\", \"rotation,rotationX,rotationY,skewX,skewY\", \"transform,transformOrigin,svgOrigin,force3D,smoothOrigin,transformPerspective\", \"0:translateX,1:translateY,2:translateZ,8:rotate,8:rotationZ,8:rotateZ,9:rotateX,10:rotateY\");\n_forEachName(\"x,y,z,top,right,bottom,left,width,height,fontSize,padding,margin,perspective\", name => {_config.units[name] = \"px\"});\n\ngsap.registerPlugin(CSSPlugin);\n\nexport { CSSPlugin as default, _getBBox, _createElement, _checkPropPrefix as checkPrefix };", "import { gsap, Power0, Power1, Power2, Power3, Power4, Linear, Quad, Cubic, Quart, <PERSON><PERSON><PERSON>, Strong, Elastic, Back, SteppedEase, Bounce, Sine, Expo, Circ, TweenLite, TimelineLite, TimelineMax } from \"./gsap-core.js\";\nimport { CSSPlugin } from \"./CSSPlugin.js\";\n\nconst gsapWithCSS = gsap.registerPlugin(CSSPlugin) || gsap, // to protect from tree shaking\n\tTweenMaxWithCSS = gsapWithCSS.core.Tween;\n\nexport {\n\tgsapWithCSS as gsap,\n\tgsapWithCSS as default,\n\tCSSPlugin,\n\tTweenMaxWithCSS as TweenMax,\n\tTweenLite,\n\tTimelineMax,\n\tTimelineLite,\n\tPower0,\n\tPower1,\n\tPower2,\n\tPower3,\n\tPower4,\n\tLinear,\n\tQuad,\n\tCubic,\n\tQuart,\n\tQuint,\n\tStrong,\n\tElastic,\n\tBack,\n\tSteppedEase,\n\tBounce,\n\tSine,\n\tExpo,\n\tCirc\n};"], "names": ["_isString", "value", "_isFunction", "_isNumber", "_isUndefined", "_isObject", "_isNotFalse", "_windowExists", "window", "_isFuncOrString", "_install", "scope", "_installScope", "_merge", "_globals", "gsap", "_missingPlugin", "property", "console", "warn", "_warn", "message", "suppress", "_addGlobal", "name", "obj", "_emptyFunc", "_harness", "targets", "harnessPlugin", "i", "target", "_gsap", "harness", "_harnessPlugins", "length", "targetTest", "<PERSON><PERSON><PERSON>", "splice", "_getCache", "toArray", "_getProperty", "v", "getAttribute", "_forEachName", "names", "func", "split", "for<PERSON>ach", "_round", "Math", "round", "_roundPrecise", "_parseRelative", "start", "operator", "char<PERSON>t", "end", "parseFloat", "substr", "_arrayContainsAny", "toSearch", "to<PERSON><PERSON>", "l", "indexOf", "_lazy<PERSON>ender", "tween", "_lazyTweens", "a", "slice", "_lazyLookup", "_lazy", "render", "_lazySafe<PERSON>ender", "animation", "time", "suppressEvents", "force", "_reverting", "_initted", "_startAt", "_numericIfPossible", "n", "match", "_delimitedValueExp", "trim", "_passThrough", "p", "_setDefaults", "defaults", "_mergeDeep", "base", "toMerge", "_copyExcluding", "excluding", "copy", "_inheritDefaults", "vars", "parent", "_globalTimeline", "keyframes", "_setKeyframeDefaults", "excludeDuration", "_isArray", "inherit", "_dp", "_addLinkedListItem", "child", "firstProp", "lastProp", "sortBy", "t", "prev", "_prev", "_next", "_removeLinkedListItem", "next", "_removeFromParent", "onlyIfParentHasAutoRemove", "autoRemoveChildren", "remove", "_act", "_uncache", "_end", "_dur", "_start", "_dirty", "_rewindStartAt", "totalTime", "revert", "_revertConfigNoKill", "immediateRender", "autoRevert", "_elapsedCycleDuration", "_repeat", "_animationCycle", "_tTime", "duration", "_r<PERSON><PERSON><PERSON>", "_parentToChildTotalTime", "parentTime", "_ts", "totalDuration", "_tDur", "_setEnd", "abs", "_rts", "_tinyNum", "_alignPlayhead", "smooth<PERSON><PERSON>d<PERSON><PERSON>ing", "_time", "_postAdd<PERSON><PERSON><PERSON>", "timeline", "add", "rawTime", "_clamp", "_zTime", "_addToTimeline", "position", "<PERSON><PERSON><PERSON><PERSON>", "_parsePosition", "_delay", "timeScale", "_sort", "_isFromOrFromStart", "_recent", "_scrollTrigger", "trigger", "ScrollTrigger", "create", "_attemptInitTween", "tTime", "_initTween", "_pt", "lazy", "_last<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ticker", "frame", "push", "_setDuration", "<PERSON><PERSON><PERSON><PERSON>", "leavePlayhead", "repeat", "dur", "totalProgress", "_onUpdateTotalDuration", "Timeline", "_createTweenType", "type", "params", "ir<PERSON><PERSON>", "isLegacy", "varsIndex", "runBackwards", "startAt", "Tween", "_conditionalReturn", "getUnit", "_unitExp", "exec", "_isArrayLike", "nonEmpty", "nodeType", "_win", "selector", "el", "current", "nativeElement", "querySelectorAll", "_doc", "createElement", "shuffle", "sort", "random", "distribute", "each", "ease", "_parseEase", "from", "cache", "isDecimal", "ratios", "isNaN", "axis", "ratioX", "ratioY", "center", "edges", "originX", "originY", "x", "y", "d", "j", "max", "min", "wrapAt", "distances", "grid", "_bigNum", "getBoundingClientRect", "left", "_sqrt", "amount", "b", "u", "_invertEase", "_roundModifier", "pow", "raw", "snap", "snapTo", "radius", "is2D", "isArray", "values", "increment", "dx", "dy", "closest", "roundingIncrement", "returnFunction", "floor", "_wrapArray", "wrapper", "index", "_replaceRandom", "nums", "s", "_strictNumExp", "_getLabelInDirection", "fromTime", "backward", "distance", "label", "labels", "_interrupt", "scrollTrigger", "kill", "progress", "_callback", "_createPlugin", "config", "isFunc", "Plugin", "init", "_props", "instanceDefaults", "_renderPropTweens", "_addPropTween", "_killPropTweensOf", "modifier", "_addPluginModifier", "rawVars", "statics", "get", "getSetter", "_getSetter", "aliases", "register", "_wake", "_plugins", "prototype", "prop", "_reservedProps", "toUpperCase", "PropTween", "_registerPluginQueue", "_hue", "h", "m1", "m2", "_255", "splitColor", "toHSL", "forceAlpha", "r", "g", "wasHSL", "_colorLookup", "black", "parseInt", "_numExp", "transparent", "map", "Number", "_colorOrderData", "c", "_colorExp", "_numWithUnitExp", "_formatColors", "orderMatchData", "shell", "result", "colors", "color", "join", "replace", "shift", "_colorStringFilter", "combined", "lastIndex", "test", "_hslExp", "_configEaseFromString", "_easeMap", "apply", "_parseObjectInString", "val", "parsedVal", "key", "lastIndexOf", "_quotesExp", "_valueInParentheses", "open", "close", "nested", "substring", "_CE", "_customEaseExp", "_propagateYoyoEase", "isYoyo", "_first", "yoyoEase", "_yoyo", "_ease", "_yEase", "_insertEase", "easeIn", "easeOut", "easeInOut", "lowercaseName", "toLowerCase", "_easeInOutFromOut", "_configElastic", "amplitude", "period", "p1", "_sin", "p3", "p2", "_2PI", "asin", "_configBack", "overshoot", "_suppressOverwrites", "_context", "_coreInitted", "_coreReady", "_quickTween", "_tickerActive", "_id", "_req", "_raf", "_self", "_delta", "_i", "_getTime", "_lagThreshold", "_adjustedLag", "_startTime", "_lastUpdate", "_gap", "_nextTime", "_listeners", "n1", "_config", "autoSleep", "force3D", "null<PERSON><PERSON><PERSON><PERSON><PERSON>n", "units", "lineHeight", "_defaults", "overwrite", "delay", "PI", "_HALF_PI", "_gsID", "sqrt", "_cos", "cos", "sin", "_isTypedArray", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Array", "_complexStringNumExp", "_relExp", "_startAtRevertConfig", "isStart", "_revertConfig", "_effects", "_nextGCFrame", "_callbackN<PERSON>s", "cycleDuration", "whole", "data", "_zeroPosition", "endTime", "percentAnimation", "offset", "isPercent", "recent", "clippedDuration", "_slice", "leaveStrings", "_flatten", "ar", "accumulator", "call", "mapRange", "inMin", "inMax", "outMin", "outMax", "inRange", "out<PERSON><PERSON><PERSON>", "executeLazyFirst", "callback", "prevContext", "context", "_ctx", "callbackScope", "aqua", "lime", "silver", "maroon", "teal", "blue", "navy", "white", "olive", "yellow", "orange", "gray", "purple", "green", "red", "pink", "cyan", "RegExp", "Date", "now", "tick", "_tick", "deltaRatio", "fps", "wake", "document", "gsapVersions", "version", "GreenSockGlobals", "requestAnimationFrame", "sleep", "f", "setTimeout", "cancelAnimationFrame", "clearTimeout", "lagSmoothing", "threshold", "adjustedLag", "Infinity", "once", "prioritize", "defaultEase", "overlap", "dispatch", "elapsed", "manual", "power", "Linear", "easeNone", "none", "SteppedEase", "steps", "immediateStart", "id", "this", "set", "Animation", "startTime", "arguments", "_ptLookup", "_pTime", "ratio", "iteration", "_ps", "_recacheAncestors", "paused", "includeRepeats", "wrapRepeats", "prevIsReverting", "globalTime", "_sat", "repeatDelay", "yoyo", "seek", "restart", "includeDelay", "play", "reversed", "reverse", "pause", "atTime", "resume", "invalidate", "isActive", "eventCallback", "_onUpdate", "then", "onFulfilled", "self", "Promise", "resolve", "_resolve", "_then", "_prom", "sort<PERSON><PERSON><PERSON><PERSON>", "_this", "to", "fromTo", "fromVars", "to<PERSON><PERSON>", "delayedCall", "staggerTo", "stagger", "onCompleteAll", "onCompleteAllParams", "onComplete", "onCompleteParams", "staggerFrom", "staggerFromTo", "prevPaused", "pauseTween", "prevStart", "prevIteration", "prevTime", "tDur", "crossingStart", "_lock", "rewinding", "doesWrap", "repeatRefresh", "onRepeat", "_hasPause", "_forcing", "_findNextPauseTween", "_last", "onUpdate", "adjustedTime", "_this2", "addLabel", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tweens", "timelines", "ignoreBeforeTime", "getById", "animations", "<PERSON><PERSON><PERSON><PERSON>", "killTweensOf", "addPause", "removePause", "props", "onlyActive", "getTweensOf", "_overwritingTween", "children", "parsedTargets", "isGlobalTime", "_targets", "tweenTo", "initted", "tl", "onStart", "onStartParams", "tweenFromTo", "fromPosition", "toPosition", "next<PERSON><PERSON><PERSON>", "afterTime", "previousLabel", "beforeTime", "current<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "adjustLabels", "soft", "clear", "<PERSON><PERSON><PERSON><PERSON>", "updateRoot", "_checkPlugin", "plugin", "pt", "ptLookup", "_processVars", "_parseFuncOrString", "style", "priority", "_parseKeyframe", "allProps", "easeEach", "e", "_forceAllPropTweens", "stringFilter", "funcParam", "optional", "currentValue", "parsedStart", "setter", "_setterFuncWithParam", "_setterFunc", "_setter<PERSON><PERSON>", "_addComplexStringPropTween", "startNums", "endNum", "chunk", "startNum", "hasRandom", "_renderComplexString", "matchIndex", "m", "fp", "_renderBoolean", "_<PERSON><PERSON><PERSON>", "cleanVars", "hasPriority", "gsData", "harnessVars", "overwritten", "onUpdateParams", "prevStartAt", "fullTargets", "autoOverwrite", "_overwrite", "_from", "_ptCache", "_op", "_sortPropTweensByPriority", "_onInit", "_staggerTweenProps", "_staggerPropsToSkip", "skipInh<PERSON>t", "curTarget", "staggerFunc", "staggerVarsToMerge", "_this3", "kf", "_hasNoPausedAncestors", "isNegative", "_renderZeroDurationTween", "prevRatio", "_parentPlayheadIsBeforeStart", "resetTo", "startIsRelative", "_updatePropTweens", "rootPT", "lookup", "ptCache", "overwrittenProps", "cur<PERSON><PERSON><PERSON>", "curOverwriteProps", "killingTargets", "propTweenLookup", "firstPT", "_arraysMatch", "a1", "a2", "_addAliasesToVars", "propertyAliases", "onReverseComplete", "onReverseCompleteParams", "_setterAttribute", "setAttribute", "_setterWithModifier", "mSet", "mt", "hasNonDependentRemaining", "op", "dep", "pt2", "first", "last", "pr", "change", "renderer", "TweenMax", "TweenLite", "TimelineLite", "TimelineMax", "_dispatch", "_emptyArray", "_onMediaChange", "matches", "_lastMediaTime", "_media", "anyMatch", "toggled", "queries", "conditions", "matchMedia", "onMatch", "_contextID", "Context", "prevSelector", "_r", "isReverted", "ignore", "getTweens", "o", "_this4", "MatchMedia", "mq", "active", "cond", "contexts", "addListener", "addEventListener", "registerPlugin", "args", "getProperty", "unit", "uncache", "getter", "format", "quickSetter", "setters", "quickTo", "isTweening", "registerEffect", "effect", "plugins", "extendTimeline", "pluginName", "registerEase", "parseEase", "exportRoot", "includeDelayedCalls", "matchMediaRefresh", "found", "removeEventListener", "utils", "wrap", "range", "wrapYoyo", "total", "normalize", "clamp", "pipe", "functions", "reduce", "unitize", "interpolate", "mutate", "interpolators", "il", "isString", "master", "install", "effects", "ticker", "globalTimeline", "core", "globals", "getCache", "reverting", "toAdd", "suppressOverwrites", "_getPluginPropTween", "_buildModifierPlugin", "temp", "_addModifiers", "modifiers", "_renderCSSProp", "_renderPropWithEnd", "_renderCSSPropWithBeginning", "_renderRoundedCSSProp", "_renderNonTweeningValue", "_renderNonTweeningValueOnlyAtEnd", "_setterCSSStyle", "_setterCS<PERSON>rop", "setProperty", "_setterTransform", "_setterScale", "scaleX", "scaleY", "_setterScaleWithRender", "renderTransform", "_setterTransformWithRender", "_saveStyle", "isNotCSS", "_transformProps", "tfm", "_propertyAliases", "transform", "_get", "_transformProp", "svg", "svgo", "_transformOriginProp", "_removeIndependentTransforms", "translate", "removeProperty", "_revertStyle", "_capsExp", "_getStyleSaver", "properties", "saver", "save", "_createElement", "ns", "createElementNS", "_getComputedProperty", "skip<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cs", "getComputedStyle", "getPropertyValue", "_checkPropPrefix", "_initCore", "_doc<PERSON>lement", "documentElement", "_tempDiv", "cssText", "_supports3D", "_pluginInitted", "_getBBoxHack", "swapIfPossible", "bbox", "ownerSVGElement", "old<PERSON>arent", "parentNode", "old<PERSON><PERSON>ling", "nextS<PERSON>ling", "oldCSS", "append<PERSON><PERSON><PERSON>", "display", "getBBox", "_gsapBBox", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "_getAttributeFallbacks", "attributesArray", "hasAttribute", "_get<PERSON><PERSON>", "bounds", "error", "width", "height", "_isSVG", "getCTM", "_removeProperty", "removeAttribute", "_addNonTweeningPT", "beginning", "onlySetAtEnd", "_convertToUnit", "px", "isSVG", "curValue", "curUnit", "horizontal", "_horizontalExp", "isRootSVG", "tagName", "measureProperty", "toPixels", "toPercent", "_nonConvertibleUnits", "body", "_nonStandardLayouts", "_tweenComplexCSSString", "startValues", "startValue", "endValue", "endUnit", "startUnit", "_convertKeywordsToPercentages", "_keywordToPercent", "_renderClearProps", "clearTransforms", "_parseTransform", "_isNullTransform", "_getComputedTransformMatrixAsArray", "matrixString", "_identity2DMatrix", "_getMatrix", "force2D", "addedToDOM", "matrix", "baseVal", "consolidate", "offsetParent", "nextElement<PERSON><PERSON>ling", "_applySVGO<PERSON>in", "origin", "originIsAbsolute", "smooth", "matrixArray", "pluginToAddPropTweensTo", "determinant", "xOriginOld", "xOrigin", "yOriginOld", "y<PERSON><PERSON><PERSON>", "xOffsetOld", "xOffset", "yOffsetOld", "yOffset", "tx", "ty", "originSplit", "_addPxTranslate", "_addRotationalPropTween", "direction", "cap", "_RAD2DEG", "finalValue", "_assign", "source", "_addRawTransformPTs", "transforms", "endCache", "startCache", "_recentSetterPlugin", "Power0", "Power1", "Power2", "Power3", "Power4", "Quad", "Cubic", "Quart", "<PERSON><PERSON><PERSON>", "Strong", "Elastic", "Back", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Expo", "Circ", "_DEG2RAD", "_atan2", "atan2", "_complexExp", "autoAlpha", "scale", "alpha", "_prefixes", "element", "preferPrefix", "deg", "rad", "turn", "flex", "_firstTwoOnly", "<PERSON><PERSON><PERSON><PERSON>", "_specialProps", "top", "bottom", "right", "clearProps", "_rotationalProperties", "z", "rotation", "rotationX", "rotationY", "skewX", "skewY", "perspective", "angle", "a12", "a22", "t1", "t2", "t3", "a13", "a23", "a33", "a42", "a43", "a32", "invertedScaleX", "rotate", "forceCSS", "xPercent", "offsetWidth", "yPercent", "offsetHeight", "transformPerspective", "_renderSVGTransforms", "_renderCSSTransforms", "_renderNon3DTransforms", "_zeroDeg", "_zeroPx", "_endParenthesis", "use3D", "a11", "a21", "tan", "side", "positionAndScale", "all", "CSSPlugin", "specialProp", "relative", "isTransformRelated", "transformPropTween", "inlineProps", "styles", "visibility", "parseTransform", "smoothOrigin", "autoRound", "checkPrefix", "getStyleSaver", "gsapWithCSS", "TweenMaxWithCSS"], "mappings": ";;;;;;;;;ycAgCa,SAAZA,EAAYC,SAA2B,iBAAXA,EACd,SAAdC,EAAcD,SAA2B,mBAAXA,EAClB,SAAZE,EAAYF,SAA2B,iBAAXA,EACb,SAAfG,EAAeH,eAA2B,IAAXA,EACnB,SAAZI,EAAYJ,SAA2B,iBAAXA,EACd,SAAdK,EAAcL,UAAmB,IAAVA,EACP,SAAhBM,UAAyC,oBAAZC,OACX,SAAlBC,EAAkBR,UAASC,EAAYD,IAAUD,EAAUC,GAchD,SAAXS,EAAWC,UAAUC,EAAgBC,GAAOF,EAAOG,MAAcC,GAChD,SAAjBC,EAAkBC,EAAUhB,UAAUiB,QAAQC,KAAK,mBAAoBF,EAAU,SAAUhB,EAAO,yCAC1F,SAARmB,EAASC,EAASC,UAAcA,GAAYJ,QAAQC,KAAKE,GAC5C,SAAbE,EAAcC,EAAMC,UAASD,IAASV,GAASU,GAAQC,IAASb,IAAkBA,EAAcY,GAAQC,IAAUX,GACrG,SAAbY,WAAmB,EAaR,SAAXC,GAAWC,OAETC,EAAeC,EADZC,EAASH,EAAQ,MAErBvB,EAAU0B,IAAW7B,EAAY6B,KAAYH,EAAU,CAACA,MAClDC,GAAiBE,EAAOC,OAAS,IAAIC,SAAU,KACpDH,EAAII,GAAgBC,OACbL,MAAQI,GAAgBJ,GAAGM,WAAWL,KAC7CF,EAAgBK,GAAgBJ,OAEjCA,EAAIF,EAAQO,OACLL,KACLF,EAAQE,KAAOF,EAAQE,GAAGE,QAAUJ,EAAQE,GAAGE,MAAQ,IAAIK,GAAQT,EAAQE,GAAID,MAAqBD,EAAQU,OAAOR,EAAG,UAEjHF,EAEI,SAAZW,GAAYR,UAAUA,EAAOC,OAASL,GAASa,GAAQT,IAAS,GAAGC,MACpD,SAAfS,GAAgBV,EAAQd,EAAUyB,UAAOA,EAAIX,EAAOd,KAAcf,EAAYwC,GAAKX,EAAOd,KAAeb,EAAasC,IAAMX,EAAOY,cAAgBZ,EAAOY,aAAa1B,IAAcyB,EACtK,SAAfE,GAAgBC,EAAOC,UAAWD,EAAQA,EAAME,MAAM,MAAMC,QAAQF,IAAUD,EACrE,SAATI,GAAShD,UAASiD,KAAKC,MAAc,IAARlD,GAAkB,KAAU,EACzC,SAAhBmD,GAAgBnD,UAASiD,KAAKC,MAAc,IAARlD,GAAoB,KAAY,EACnD,SAAjBoD,GAAkBC,EAAOrD,OACpBsD,EAAWtD,EAAMuD,OAAO,GAC3BC,EAAMC,WAAWzD,EAAM0D,OAAO,WAC/BL,EAAQI,WAAWJ,GACC,MAAbC,EAAmBD,EAAQG,EAAmB,MAAbF,EAAmBD,EAAQG,EAAmB,MAAbF,EAAmBD,EAAQG,EAAMH,EAAQG,EAE/F,SAApBG,GAAqBC,EAAUC,WAC1BC,EAAID,EAAO3B,OACdL,EAAI,EACE+B,EAASG,QAAQF,EAAOhC,IAAM,KAAOA,EAAIiC,WACxCjC,EAAIiC,EAEC,SAAdE,SAGEnC,EAAGoC,EAFAH,EAAII,GAAYhC,OACnBiC,EAAID,GAAYE,MAAM,OAEvBC,GAAc,GAETxC,EADLqC,GAAYhC,OAAS,EACTL,EAAIiC,EAAGjC,KAClBoC,EAAQE,EAAEtC,KACDoC,EAAMK,QAAUL,EAAMM,OAAON,EAAMK,MAAM,GAAIL,EAAMK,MAAM,IAAI,GAAMA,MAAQ,GAGpE,SAAlBE,GAAmBC,EAAWC,EAAMC,EAAgBC,GACnDV,GAAYhC,SAAW2C,GAAcb,KACrCS,EAAUF,OAAOG,EAAMC,EAAgBC,GAAUC,GAAcH,EAAO,IAAMD,EAAUK,UAAYL,EAAUM,WAC5Gb,GAAYhC,SAAW2C,GAAcb,KAEjB,SAArBgB,GAAqBhF,OAChBiF,EAAIxB,WAAWzD,UACXiF,GAAW,IAANA,KAAajF,EAAQ,IAAIkF,MAAMC,IAAoBjD,OAAS,EAAI+C,EAAIlF,EAAUC,GAASA,EAAMoF,OAASpF,EAErG,SAAfqF,GAAeC,UAAKA,EACL,SAAfC,GAAgB/D,EAAKgE,OACf,IAAIF,KAAKE,EACZF,KAAK9D,IAASA,EAAI8D,GAAKE,EAASF,WAE3B9D,EAaK,SAAbiE,GAAcC,EAAMC,OACd,IAAIL,KAAKK,EACP,cAANL,GAA2B,gBAANA,GAA6B,cAANA,IAAsBI,EAAKJ,GAAKlF,EAAUuF,EAAQL,IAAMG,GAAWC,EAAKJ,KAAOI,EAAKJ,GAAK,IAAKK,EAAQL,IAAMK,EAAQL,WAE1JI,EAES,SAAjBE,GAAkBpE,EAAKqE,OAErBP,EADGQ,EAAO,OAENR,KAAK9D,EACR8D,KAAKO,IAAeC,EAAKR,GAAK9D,EAAI8D,WAE7BQ,EAEW,SAAnBC,GAAmBC,OACdC,EAASD,EAAKC,QAAUC,EAC3BrD,EAAOmD,EAAKG,UA3BS,SAAvBC,qBAAuBC,UAAmB,SAAC7E,EAAKgE,OAC1C,IAAIF,KAAKE,EACZF,KAAK9D,GAAe,aAAN8D,GAAoBe,GAA0B,SAANf,IAAiB9D,EAAI8D,GAAKE,EAASF,KAyBlEc,CAAqBE,EAASN,EAAKG,YAAcZ,MACtElF,EAAY2F,EAAKO,cACbN,GACNpD,EAAKmD,EAAMC,EAAOD,KAAKR,UACvBS,EAASA,EAAOA,QAAUA,EAAOO,WAG5BR,EAQa,SAArBS,GAAsBR,EAAQS,EAAOC,EAAsBC,EAAoBC,YAA1CF,IAAAA,EAAY,mBAAUC,IAAAA,EAAW,aAEpEE,EADGC,EAAOd,EAAOW,MAEdC,MACHC,EAAIJ,EAAMG,GACHE,GAAQA,EAAKF,GAAUC,GAC7BC,EAAOA,EAAKC,aAGVD,GACHL,EAAMO,MAAQF,EAAKE,MACnBF,EAAKE,MAAQP,IAEbA,EAAMO,MAAQhB,EAAOU,GACrBV,EAAOU,GAAaD,GAEjBA,EAAMO,MACTP,EAAMO,MAAMD,MAAQN,EAEpBT,EAAOW,GAAYF,EAEpBA,EAAMM,MAAQD,EACdL,EAAMT,OAASS,EAAMF,IAAMP,EACpBS,EAEgB,SAAxBQ,GAAyBjB,EAAQS,EAAOC,EAAsBC,YAAtBD,IAAAA,EAAY,mBAAUC,IAAAA,EAAW,aACpEG,EAAOL,EAAMM,MAChBG,EAAOT,EAAMO,MACVF,EACHA,EAAKE,MAAQE,EACHlB,EAAOU,KAAeD,IAChCT,EAAOU,GAAaQ,GAEjBA,EACHA,EAAKH,MAAQD,EACHd,EAAOW,KAAcF,IAC/BT,EAAOW,GAAYG,GAEpBL,EAAMO,MAAQP,EAAMM,MAAQN,EAAMT,OAAS,KAExB,SAApBmB,GAAqBV,EAAOW,GAC3BX,EAAMT,UAAYoB,GAA6BX,EAAMT,OAAOqB,qBAAuBZ,EAAMT,OAAOsB,QAAUb,EAAMT,OAAOsB,OAAOb,GAC9HA,EAAMc,KAAO,EAEH,SAAXC,GAAYhD,EAAWiC,MAClBjC,KAAeiC,GAASA,EAAMgB,KAAOjD,EAAUkD,MAAQjB,EAAMkB,OAAS,WACrEzD,EAAIM,EACDN,GACNA,EAAE0D,OAAS,EACX1D,EAAIA,EAAE8B,cAGDxB,EAWS,SAAjBqD,GAAkB7D,EAAO8D,EAAWpD,EAAgBC,UAAUX,EAAMc,WAAaF,EAAaZ,EAAMc,SAASiD,OAAOC,IAAwBhE,EAAM+B,KAAKkC,kBAAoBjE,EAAM+B,KAAKmC,YAAelE,EAAMc,SAASR,OAAOwD,GAAW,EAAMnD,IAEpN,SAAxBwD,GAAwB3D,UAAaA,EAAU4D,QAAUC,GAAgB7D,EAAU8D,OAAS9D,EAAYA,EAAU+D,WAAa/D,EAAUgE,SAAYhE,EAAY,EAMvI,SAA1BiE,GAA2BC,EAAYjC,UAAWiC,EAAajC,EAAMkB,QAAUlB,EAAMkC,KAAoB,GAAblC,EAAMkC,IAAW,EAAKlC,EAAMmB,OAASnB,EAAMmC,gBAAkBnC,EAAMoC,OACrJ,SAAVC,GAAUtE,UAAcA,EAAUiD,KAAOvE,GAAcsB,EAAUmD,QAAWnD,EAAUqE,MAAQ7F,KAAK+F,IAAIvE,EAAUmE,KAAOnE,EAAUwE,MAAQC,IAAc,IACvI,SAAjBC,GAAkB1E,EAAWsD,OACxB9B,EAASxB,EAAU+B,WACnBP,GAAUA,EAAOmD,mBAAqB3E,EAAUmE,MACnDnE,EAAUmD,OAASzE,GAAc8C,EAAOoD,OAAyB,EAAhB5E,EAAUmE,IAAUb,EAAYtD,EAAUmE,MAAQnE,EAAUoD,OAASpD,EAAUoE,gBAAkBpE,EAAUqE,OAASf,IAActD,EAAUmE,MAC7LG,GAAQtE,GACRwB,EAAO4B,QAAUJ,GAASxB,EAAQxB,IAE5BA,EAYS,SAAjB6E,GAAkBC,EAAU7C,OACvBI,MACAJ,EAAM2C,QAAW3C,EAAMiB,MAAQjB,EAAM5B,UAAc4B,EAAMkB,OAAS2B,EAASF,QAAU3C,EAAMiB,OAASjB,EAAM8C,QAC7G1C,EAAI4B,GAAwBa,EAASE,UAAW/C,KAC3CA,EAAMiB,MAAQ+B,GAAO,EAAGhD,EAAMmC,gBAAiB/B,GAAKJ,EAAM6B,OAASW,IACvExC,EAAMnC,OAAOuC,GAAG,IAIdW,GAAS8B,EAAU7C,GAAOF,KAAO+C,EAASzE,UAAYyE,EAASF,OAASE,EAAS5B,MAAQ4B,EAASX,IAAK,IAEtGW,EAAS5B,KAAO4B,EAASf,eAC5B1B,EAAIyC,EACGzC,EAAEN,KACQ,GAAfM,EAAE2C,WAAmB3C,EAAEiB,UAAUjB,EAAEyB,QACpCzB,EAAIA,EAAEN,IAGR+C,EAASI,QAAUT,GAGJ,SAAjBU,GAAkBL,EAAU7C,EAAOmD,EAAUC,UAC5CpD,EAAMT,QAAUmB,GAAkBV,GAClCA,EAAMkB,OAASzE,IAAejD,EAAU2J,GAAYA,EAAWA,GAAYN,IAAarD,EAAkB6D,GAAeR,EAAUM,EAAUnD,GAAS6C,EAASF,OAAS3C,EAAMsD,QAC9KtD,EAAMgB,KAAOvE,GAAcuD,EAAMkB,QAAWlB,EAAMmC,gBAAkB5F,KAAK+F,IAAItC,EAAMuD,cAAiB,IACpGxD,GAAmB8C,EAAU7C,EAAO,SAAU,QAAS6C,EAASW,MAAQ,SAAW,GACnFC,GAAmBzD,KAAW6C,EAASa,QAAU1D,GACjDoD,GAAcR,GAAeC,EAAU7C,GACvC6C,EAASX,IAAM,GAAKO,GAAeI,EAAUA,EAAShB,QAC/CgB,EAES,SAAjBc,GAAkB5F,EAAW6F,UAAazJ,GAAS0J,eAAiBxJ,EAAe,gBAAiBuJ,KAAazJ,GAAS0J,cAAcC,OAAOF,EAAS7F,GACpI,SAApBgG,GAAqBxG,EAAOS,EAAME,EAAOD,EAAgB+F,UACxDC,GAAW1G,EAAOS,EAAMgG,GACnBzG,EAAMa,UAGNF,GAASX,EAAM2G,MAAQ/F,IAAgBZ,EAAM0D,OAA4B,IAApB1D,EAAM+B,KAAK6E,OAAqB5G,EAAM0D,MAAQ1D,EAAM+B,KAAK6E,OAAUC,IAAuBC,GAAQC,OAC3J9G,GAAY+G,KAAKhH,GACjBA,EAAMK,MAAQ,CAACoG,EAAO/F,GACf,UALA,EA2EM,SAAfuG,GAAgBzG,EAAW+D,EAAU2C,EAAaC,OAC7CC,EAAS5G,EAAU4D,QACtBiD,EAAMnI,GAAcqF,IAAa,EACjC+C,EAAgB9G,EAAU8D,OAAS9D,EAAUqE,aAC9CyC,IAAkBH,IAAkB3G,EAAU4E,OAASiC,EAAM7G,EAAUkD,MACvElD,EAAUkD,KAAO2D,EACjB7G,EAAUqE,MAASuC,EAAeA,EAAS,EAAI,KAAOlI,GAAcmI,GAAOD,EAAS,GAAM5G,EAAUgE,QAAU4C,GAAlFC,EACZ,EAAhBC,IAAsBH,GAAiBjC,GAAe1E,EAAYA,EAAU8D,OAAS9D,EAAUqE,MAAQyC,GACvG9G,EAAUwB,QAAU8C,GAAQtE,GAC5B0G,GAAe1D,GAAShD,EAAUwB,OAAQxB,GACnCA,EAEiB,SAAzB+G,GAAyB/G,UAAcA,aAAqBgH,GAAYhE,GAAShD,GAAayG,GAAazG,EAAWA,EAAUkD,MA2B7G,SAAnB+D,GAAoBC,EAAMC,EAAQrC,OAIhCsC,EAAQ5F,EAHL6F,EAAW5L,EAAU0L,EAAO,IAC/BG,GAAaD,EAAW,EAAI,IAAMH,EAAO,EAAI,EAAI,GACjD3F,EAAO4F,EAAOG,MAEfD,IAAa9F,EAAKwC,SAAWoD,EAAO,IACpC5F,EAAKC,OAASsD,EACVoC,EAAM,KACTE,EAAS7F,EACTC,EAASsD,EACFtD,KAAY,oBAAqB4F,IACvCA,EAAS5F,EAAOD,KAAKR,UAAY,GACjCS,EAAS5F,EAAY4F,EAAOD,KAAKO,UAAYN,EAAOA,OAErDD,EAAKkC,gBAAkB7H,EAAYwL,EAAO3D,iBAC1CyD,EAAO,EAAK3F,EAAKgG,aAAe,EAAMhG,EAAKiG,QAAUL,EAAOG,EAAY,UAElE,IAAIG,GAAMN,EAAO,GAAI5F,EAAM4F,EAAmB,EAAZG,IAErB,SAArBI,GAAsBnM,EAAO6C,UAAS7C,GAAmB,IAAVA,EAAc6C,EAAK7C,GAAS6C,EAEjE,SAAVuJ,GAAWpM,EAAOyC,UAAO1C,EAAUC,KAAYyC,EAAI4J,GAASC,KAAKtM,IAAeyC,EAAE,GAAP,GAG5D,SAAf8J,GAAgBvM,EAAOwM,UAAaxM,GAAUI,EAAUJ,IAAU,WAAYA,KAAYwM,IAAaxM,EAAMkC,QAAalC,EAAMkC,OAAS,KAAMlC,GAASI,EAAUJ,EAAM,OAAUA,EAAMyM,UAAYzM,IAAU0M,EAInM,SAAXC,GAAW3M,UACVA,EAAQuC,GAAQvC,GAAO,IAAMmB,EAAM,kBAAoB,GAChD,SAAAsB,OACFmK,EAAK5M,EAAM6M,SAAW7M,EAAM8M,eAAiB9M,SAC1CuC,GAAQE,EAAGmK,EAAGG,iBAAmBH,EAAKA,IAAO5M,EAAQmB,EAAM,kBAAoB6L,EAAKC,cAAc,OAASjN,IAG1G,SAAVkN,GAAU/I,UAAKA,EAAEgJ,KAAK,iBAAM,GAAKlK,KAAKmK,WAEzB,SAAbC,GAAa5K,MACRxC,EAAYwC,UACRA,MAEJuD,EAAO5F,EAAUqC,GAAKA,EAAI,CAAC6K,KAAK7K,GACnC8K,EAAOC,GAAWxH,EAAKuH,MACvBE,EAAOzH,EAAKyH,MAAQ,EACpB/H,EAAOjC,WAAWuC,EAAKN,OAAS,EAChCgI,EAAQ,GACRC,EAAoB,EAAPF,GAAYA,EAAO,EAChCG,EAASC,MAAMJ,IAASE,EACxBG,EAAO9H,EAAK8H,KACZC,EAASN,EACTO,EAASP,SACN1N,EAAU0N,GACbM,EAASC,EAAS,CAACC,OAAO,GAAIC,MAAM,GAAI1K,IAAI,GAAGiK,IAAS,GAC7CE,GAAaC,IACxBG,EAASN,EAAK,GACdO,EAASP,EAAK,IAER,SAAC5L,EAAGC,EAAQqC,OAGjBgK,EAASC,EAASC,EAAGC,EAAGC,EAAGC,EAAGC,EAAKC,EAAKC,EAFrC7K,GAAKK,GAAK6B,GAAM9D,OACnB0M,EAAYlB,EAAM5J,OAEd8K,EAAW,MACfD,EAAwB,SAAd3I,EAAK6I,KAAmB,GAAK7I,EAAK6I,MAAQ,CAAC,EAAGC,IAAU,IACrD,KACZL,GAAOK,EACAL,GAAOA,EAAMtK,EAAEwK,KAAUI,wBAAwBC,OAASL,EAAS7K,IAC1E6K,QAEDC,EAAYlB,EAAM5J,GAAK,GACvBqK,EAAUP,EAAU3K,KAAKyL,IAAIC,EAAQ7K,GAAKiK,EAAU,GAAKN,EAAOkB,EAChEP,EAAUO,IAAWG,EAAU,EAAIlB,EAAS9J,EAAIkK,EAASW,EAAS,GAAMlB,EAAOkB,EAAU,EAEzFD,EAAMI,EACDN,EAFLC,EAAM,EAEMD,EAAI1K,EAAG0K,IAClBH,EAAKG,EAAIG,EAAUR,EACnBG,EAAIF,GAAYI,EAAIG,EAAU,GAC9BC,EAAUJ,GAAKD,EAAKT,EAA8B7K,KAAK+F,IAAc,MAAT8E,EAAgBQ,EAAID,GAArDY,EAAMZ,EAAIA,EAAIC,EAAIA,GACxCG,EAAJF,IAAaE,EAAMF,GACnBA,EAAIG,IAASA,EAAMH,GAEX,WAATd,GAAsBP,GAAQ0B,GAC/BA,EAAUH,IAAMA,EAAMC,EACtBE,EAAUF,IAAMA,EAChBE,EAAUnM,EAAIqB,GAAKL,WAAWuC,EAAKkJ,SAAYzL,WAAWuC,EAAKsH,OAAkBxJ,EAAT6K,EAAa7K,EAAI,EAAKgK,EAA+C,MAATA,EAAehK,EAAI6K,EAASA,EAA3D1L,KAAKwL,IAAIE,EAAQ7K,EAAI6K,KAAkD,IAAe,UAATlB,GAAoB,EAAI,GAC1MmB,EAAUO,EAAKrL,EAAI,EAAK4B,EAAO5B,EAAI4B,EACnCkJ,EAAUQ,EAAIhD,GAAQpG,EAAKkJ,QAAUlJ,EAAKsH,OAAS,EACnDC,EAAQA,GAAQzJ,EAAI,EAAKuL,GAAY9B,GAAQA,SAE9CzJ,GAAM8K,EAAU/M,GAAK+M,EAAUF,KAAOE,EAAUH,KAAQ,EACjDtL,GAAcyL,EAAUO,GAAK5B,EAAOA,EAAKzJ,GAAKA,GAAK8K,EAAUnM,GAAKmM,EAAUQ,GAGpE,SAAjBE,GAAiB7M,OACZ6C,EAAIrC,KAAKsM,IAAI,KAAM9M,EAAI,IAAIK,MAAM,KAAK,IAAM,IAAIZ,eAC7C,SAAAsN,OACFvK,EAAI9B,GAAcF,KAAKC,MAAMO,WAAW+L,GAAO/M,GAAKA,EAAI6C,UACpDL,EAAIA,EAAI,GAAKK,GAAKpF,EAAUsP,GAAO,EAAIpD,GAAQoD,KAGlD,SAAPC,GAAQC,EAAQ1P,OAEd2P,EAAQC,EADLC,EAAUvJ,EAASoJ,UAElBG,GAAWzP,EAAUsP,KACzBC,EAASE,EAAUH,EAAOC,QAAUb,EAChCY,EAAOI,QACVJ,EAASnN,GAAQmN,EAAOI,SACnBF,GAAQ1P,EAAUwP,EAAO,OAC7BC,GAAUA,IAGXD,EAASJ,GAAeI,EAAOK,YAG1B5D,GAAmBnM,EAAQ6P,EAAmC5P,EAAYyP,GAAU,SAAAF,UAAQI,EAAOF,EAAOF,GAAavM,KAAK+F,IAAI4G,EAAOJ,IAAQG,EAASC,EAAOJ,GAAS,SAAAA,WAM7KQ,EAAIC,EALD5B,EAAI5K,WAAWmM,EAAOJ,EAAInB,EAAImB,GACjClB,EAAI7K,WAAWmM,EAAOJ,EAAIlB,EAAI,GAC9BI,EAAMI,EACNoB,EAAU,EACVrO,EAAI6N,EAAOxN,OAELL,MAILmO,EAHGJ,GACHI,EAAKN,EAAO7N,GAAGwM,EAAIA,GAET2B,GADVC,EAAKP,EAAO7N,GAAGyM,EAAIA,GACC2B,EAEfhN,KAAK+F,IAAI0G,EAAO7N,GAAKwM,IAElBK,IACRA,EAAMsB,EACNE,EAAUrO,UAGZqO,GAAYP,GAAUjB,GAAOiB,EAAUD,EAAOQ,GAAWV,EACjDI,GAAQM,IAAYV,GAAOtP,EAAUsP,GAAQU,EAAUA,EAAU9D,GAAQoD,IArBtCF,GAAeI,IAwBnD,SAATtC,GAAUsB,EAAKD,EAAK0B,EAAmBC,UAAmBjE,GAAmB7F,EAASoI,IAAQD,GAA4B,IAAtB0B,KAAgCA,EAAoB,IAAMC,EAAgB,kBAAM9J,EAASoI,GAAOA,KAAOzL,KAAKmK,SAAWsB,EAAIxM,UAAYiO,EAAoBA,GAAqB,QAAUC,EAAiBD,EAAoB,WAAI,IAAQA,EAAoB,IAAIjO,OAAS,GAAK,IAAOe,KAAKoN,MAAMpN,KAAKC,OAAOwL,EAAMyB,EAAoB,EAAIlN,KAAKmK,UAAYqB,EAAMC,EAA0B,IAApByB,IAA4BA,GAAqBA,EAAoBC,GAAkBA,IAIxhB,SAAbE,GAAcnM,EAAGoM,EAASvQ,UAAUmM,GAAmBnM,EAAO,SAAAwQ,UAASrM,IAAIoM,EAAQC,MAalE,SAAjBC,GAAiBzQ,WAGf6B,EAAG6O,EAAMlN,EAAKqM,EAFX9I,EAAO,EACV4J,EAAI,KAEI9O,EAAI7B,EAAM+D,QAAQ,UAAWgD,KACrCvD,EAAMxD,EAAM+D,QAAQ,IAAKlC,GACzBgO,EAAkC,MAAxB7P,EAAMuD,OAAO1B,EAAI,GAC3B6O,EAAO1Q,EAAM0D,OAAO7B,EAAI,EAAG2B,EAAM3B,EAAI,GAAGqD,MAAM2K,EAAU1K,GAAqByL,IAC7ED,GAAK3Q,EAAM0D,OAAOqD,EAAMlF,EAAIkF,GAAQqG,GAAOyC,EAAUa,GAAQA,EAAK,GAAIb,EAAU,GAAKa,EAAK,IAAKA,EAAK,IAAM,MAC1G3J,EAAOvD,EAAM,SAEPmN,EAAI3Q,EAAM0D,OAAOqD,EAAM/G,EAAMkC,OAAS6E,GA4CvB,SAAvB8J,GAAwBtH,EAAUuH,EAAUC,OAG1CzL,EAAG0L,EAAUC,EAFVC,EAAS3H,EAAS2H,OACrBxC,EAAMI,MAEFxJ,KAAK4L,GACTF,EAAWE,EAAO5L,GAAKwL,GACP,KAASC,GAAYC,GAAYtC,GAAOsC,EAAW/N,KAAK+F,IAAIgI,MAC3EC,EAAQ3L,EACRoJ,EAAMsC,UAGDC,EAmBK,SAAbE,GAAa1M,UACZ2C,GAAkB3C,GAClBA,EAAU2M,eAAiB3M,EAAU2M,cAAcC,OAAOxM,GAC1DJ,EAAU6M,WAAa,GAAKC,GAAU9M,EAAW,eAC1CA,EAIQ,SAAhB+M,GAAgBC,MACXnR,KAAmBmR,EAAQ,KAE1BlQ,GADJkQ,GAAUA,EAAOlQ,MAAQkQ,WAAkBA,GACzBlQ,KACjBmQ,EAASzR,EAAYwR,GACrBE,EAAUpQ,IAASmQ,GAAUD,EAAOG,KAAQ,gBACtCC,OAAS,IACXJ,EACJK,EAAmB,CAACF,KAAMnQ,EAAY8C,OAAQwN,GAAmBvI,IAAKwI,GAAeX,KAAMY,GAAmBC,SAAUC,GAAoBC,QAAS,GACrJC,EAAU,CAAClQ,WAAY,EAAGmQ,IAAK,EAAGC,UAAWC,GAAYC,QAAS,GAAIC,SAAU,MACjFC,KACIlB,IAAWE,EAAQ,IAClBiB,GAASrR,UAGbgE,GAAaoM,EAAQpM,GAAaK,GAAe6L,EAAQK,GAAmBO,IAC5EzR,GAAO+Q,EAAOkB,UAAWjS,GAAOkR,EAAkBlM,GAAe6L,EAAQY,KACzEO,GAAUjB,EAAOmB,KAAOvR,GAASoQ,EAC7BF,EAAOtP,aACVF,GAAgBgJ,KAAK0G,GACrBoB,GAAexR,GAAQ,GAExBA,GAAiB,QAATA,EAAiB,MAAQA,EAAKgC,OAAO,GAAGyP,cAAgBzR,EAAKmC,OAAO,IAAM,SAEnFpC,EAAWC,EAAMoQ,GACjBF,EAAOiB,UAAYjB,EAAOiB,SAAS5R,GAAM6Q,EAAQsB,SAEjDxB,GAAUyB,GAAqBjI,KAAKwG,GAkD/B,SAAP0B,GAAQC,EAAGC,EAAIC,UAEC,GADfF,GAAKA,EAAI,EAAI,EAAQ,EAAJA,GAAS,EAAI,GACX,EAAKC,GAAMC,EAAKD,GAAMD,EAAI,EAAIA,EAAI,GAAKE,EAAU,EAAJF,EAAQ,EAAKC,GAAMC,EAAKD,IAAO,EAAI,EAAID,GAAK,EAAIC,GAAME,GAAQ,GAAM,EAExH,SAAbC,GAAc/Q,EAAGgR,EAAOC,OAEtBC,EAAGC,EAAGzE,EAAGiE,EAAGzC,EAAG7M,EAAG2K,EAAKC,EAAKH,EAAGsF,EAD5B1P,EAAK1B,EAAyBvC,EAAUuC,GAAK,CAACA,GAAK,GAAKA,GAAK,EAAK8Q,GAAM9Q,EAAI8Q,IAAQ,EAA3EO,GAAaC,UAErB5P,EAAG,IACc,MAAjB1B,EAAEiB,QAAQ,KACbjB,EAAIA,EAAEiB,OAAO,EAAGjB,EAAEP,OAAS,IAExB4R,GAAarR,GAChB0B,EAAI2P,GAAarR,QACX,GAAoB,MAAhBA,EAAEc,OAAO,GAAY,IAC3Bd,EAAEP,OAAS,IAIdO,EAAI,KAHJkR,EAAIlR,EAAEc,OAAO,IAGCoQ,GAFdC,EAAInR,EAAEc,OAAO,IAESqQ,GADtBzE,EAAI1M,EAAEc,OAAO,IACiB4L,GAAkB,IAAb1M,EAAEP,OAAeO,EAAEc,OAAO,GAAKd,EAAEc,OAAO,GAAK,KAEhE,IAAbd,EAAEP,aAEE,EADPiC,EAAI6P,SAASvR,EAAEiB,OAAO,EAAG,GAAI,MAChB,GAAKS,GAAK,EAAKoP,GAAMpP,EAAIoP,GAAMS,SAASvR,EAAEiB,OAAO,GAAI,IAAM,KAGzES,EAAI,EADJ1B,EAAIuR,SAASvR,EAAEiB,OAAO,GAAI,MAChB,GAAKjB,GAAK,EAAK8Q,GAAM9Q,EAAI8Q,SAC7B,GAAuB,QAAnB9Q,EAAEiB,OAAO,EAAG,MACtBS,EAAI0P,EAASpR,EAAEyC,MAAM0L,IAChB6C,GAUE,IAAKhR,EAAEsB,QAAQ,YACrBI,EAAI1B,EAAEyC,MAAM+O,IACZP,GAAcvP,EAAEjC,OAAS,IAAMiC,EAAE,GAAK,GAC/BA,OAZPiP,GAAMjP,EAAE,GAAK,IAAO,IACpBwM,EAAKxM,EAAE,GAAK,IAGZwP,EAAQ,GAFR7P,EAAKK,EAAE,GAAK,MACZyP,EAAK9P,GAAK,GAAMA,GAAK6M,EAAI,GAAK7M,EAAI6M,EAAI7M,EAAI6M,GAE/B,EAAXxM,EAAEjC,SAAeiC,EAAE,IAAM,GACzBA,EAAE,GAAKgP,GAAKC,EAAI,EAAI,EAAGO,EAAGC,GAC1BzP,EAAE,GAAKgP,GAAKC,EAAGO,EAAGC,GAClBzP,EAAE,GAAKgP,GAAKC,EAAI,EAAI,EAAGO,EAAGC,QAO3BzP,EAAI1B,EAAEyC,MAAM0L,KAAkBkD,GAAaI,YAE5C/P,EAAIA,EAAEgQ,IAAIC,eAEPX,IAAUI,IACbF,EAAIxP,EAAE,GAAKoP,GACXK,EAAIzP,EAAE,GAAKoP,GACXpE,EAAIhL,EAAE,GAAKoP,GAGXzP,IAFA2K,EAAMxL,KAAKwL,IAAIkF,EAAGC,EAAGzE,KACrBT,EAAMzL,KAAKyL,IAAIiF,EAAGC,EAAGzE,KACH,EACdV,IAAQC,EACX0E,EAAIzC,EAAI,GAERpC,EAAIE,EAAMC,EACViC,EAAQ,GAAJ7M,EAAUyK,GAAK,EAAIE,EAAMC,GAAOH,GAAKE,EAAMC,GAC/C0E,EAAI3E,IAAQkF,GAAKC,EAAIzE,GAAKZ,GAAKqF,EAAIzE,EAAI,EAAI,GAAKV,IAAQmF,GAAKzE,EAAIwE,GAAKpF,EAAI,GAAKoF,EAAIC,GAAKrF,EAAI,EAC5F6E,GAAK,IAENjP,EAAE,MAAQiP,EAAI,IACdjP,EAAE,MAAY,IAAJwM,EAAU,IACpBxM,EAAE,MAAY,IAAJL,EAAU,KAErB4P,GAAcvP,EAAEjC,OAAS,IAAMiC,EAAE,GAAK,GAC/BA,EAEU,SAAlBkQ,GAAkB5R,OACbqN,EAAS,GACZwE,EAAI,GACJzS,GAAK,SACNY,EAAEK,MAAMyR,IAAWxR,QAAQ,SAAAN,OACtB0B,EAAI1B,EAAEyC,MAAMsP,KAAoB,GACpC1E,EAAO7E,WAAP6E,EAAe3L,GACfmQ,EAAErJ,KAAKpJ,GAAKsC,EAAEjC,OAAS,KAExB4N,EAAOwE,EAAIA,EACJxE,EAEQ,SAAhB2E,GAAiB9D,EAAG8C,EAAOiB,OAKzBJ,EAAGK,EAAOpG,EAAGzK,EAJV8Q,EAAS,GACZC,GAAUlE,EAAIiE,GAAQ1P,MAAMqP,IAC5B5I,EAAO8H,EAAQ,QAAU,QACzB5R,EAAI,MAEAgT,SACGlE,KAERkE,EAASA,EAAOV,IAAI,SAAAW,UAAUA,EAAQtB,GAAWsB,EAAOrB,EAAO,KAAO9H,GAAQ8H,EAAQqB,EAAM,GAAK,IAAMA,EAAM,GAAK,KAAOA,EAAM,GAAK,KAAOA,EAAM,GAAKA,EAAMC,KAAK,MAAQ,MACrKL,IACHnG,EAAI8F,GAAgB1D,IACpB2D,EAAII,EAAeJ,GACbS,KAAKH,KAAYrG,EAAE+F,EAAES,KAAKH,QAE/B9Q,GADA6Q,EAAQhE,EAAEqE,QAAQT,GAAW,KAAKzR,MAAM0R,KAC9BtS,OAAS,EACZL,EAAIiC,EAAGjC,IACb+S,GAAUD,EAAM9S,KAAOyS,EAAEvQ,QAAQlC,GAAKgT,EAAOI,SAAWtJ,EAAO,YAAc4C,EAAErM,OAASqM,EAAIsG,EAAO3S,OAAS2S,EAASH,GAAgBO,aAInIN,MAEJ7Q,GADA6Q,EAAQhE,EAAE7N,MAAMyR,KACNrS,OAAS,EACZL,EAAIiC,EAAGjC,IACb+S,GAAUD,EAAM9S,GAAKgT,EAAOhT,UAGvB+S,EAASD,EAAM7Q,GAWF,SAArBoR,GAAqB/Q,OAEnBsP,EADG0B,EAAWhR,EAAE4Q,KAAK,QAEtBR,GAAUa,UAAY,EAClBb,GAAUc,KAAKF,UAClB1B,EAAQ6B,GAAQD,KAAKF,GACrBhR,EAAE,GAAKsQ,GAActQ,EAAE,GAAIsP,GAC3BtP,EAAE,GAAKsQ,GAActQ,EAAE,GAAIsP,EAAOY,GAAgBlQ,EAAE,MAC7C,EA2Je,SAAxBoR,GAAwBhU,OACnBuB,GAASvB,EAAO,IAAIuB,MAAM,KAC7ByK,EAAOiI,GAAS1S,EAAM,WACfyK,GAAuB,EAAfzK,EAAMZ,QAAcqL,EAAKkE,OAAUlE,EAAKkE,OAAOgE,MAAM,MAAOlU,EAAKwC,QAAQ,KAAO,CAzB1E,SAAvB2R,qBAAuB1V,WAMrBwQ,EAAOmF,EAAKC,EALTpU,EAAM,GACTsB,EAAQ9C,EAAM0D,OAAO,EAAG1D,EAAMkC,OAAO,GAAGY,MAAM,KAC9C+S,EAAM/S,EAAM,GACZjB,EAAI,EACJiC,EAAIhB,EAAMZ,OAEJL,EAAIiC,EAAGjC,IACb8T,EAAM7S,EAAMjB,GACZ2O,EAAQ3O,IAAMiC,EAAE,EAAI6R,EAAIG,YAAY,KAAOH,EAAIzT,OAC/C0T,EAAYD,EAAIjS,OAAO,EAAG8M,GAC1BhP,EAAIqU,GAAOhI,MAAM+H,GAAaA,EAAUZ,QAAQe,GAAY,IAAI3Q,QAAUwQ,EAC1EC,EAAMF,EAAIjS,OAAO8M,EAAM,GAAGpL,cAEpB5D,EAW0FkU,CAAqB5S,EAAM,KATvG,SAAtBkT,oBAAsBhW,OACjBiW,EAAOjW,EAAM+D,QAAQ,KAAO,EAC/BmS,EAAQlW,EAAM+D,QAAQ,KACtBoS,EAASnW,EAAM+D,QAAQ,IAAKkS,UACtBjW,EAAMoW,UAAUH,GAAOE,GAAUA,EAASD,EAAQlW,EAAM+D,QAAQ,IAAKmS,EAAQ,GAAKA,GAK0CF,CAAoBzU,GAAMuB,MAAM,KAAKqR,IAAInP,KAAwBwQ,GAASa,KAAOC,GAAejB,KAAK9T,GAASiU,GAASa,IAAI,GAAI9U,GAAQgM,EAItP,SAArBgJ,GAAsBhN,EAAUiN,WACFjJ,EAAzB7G,EAAQ6C,EAASkN,OACd/P,GACFA,aAAiB+E,GACpB8K,GAAmB7P,EAAO8P,IAChB9P,EAAMV,KAAK0Q,UAAchQ,EAAMiQ,OAAUjQ,EAAM2B,SAAY3B,EAAMiQ,QAAUH,IACjF9P,EAAM6C,SACTgN,GAAmB7P,EAAM6C,SAAUiN,IAEnCjJ,EAAO7G,EAAMkQ,MACblQ,EAAMkQ,MAAQlQ,EAAMmQ,OACpBnQ,EAAMmQ,OAAStJ,EACf7G,EAAMiQ,MAAQH,IAGhB9P,EAAQA,EAAMO,MAIF,SAAd6P,GAAelU,EAAOmU,EAAQC,EAAkCC,YAAlCD,IAAAA,EAAU,iBAAA1R,UAAK,EAAIyR,EAAO,EAAIzR,cAAI2R,IAAAA,EAAa,mBAAA3R,UAAKA,EAAI,GAAKyR,EAAW,EAAJzR,GAAS,EAAI,EAAIyR,EAAiB,GAAT,EAAIzR,IAAU,QAEvI4R,EADG3J,EAAO,CAACwJ,OAAAA,EAAQC,QAAAA,EAASC,UAAAA,UAE7BtU,GAAaC,EAAO,SAAArB,OAGd,IAAI+D,KAFTkQ,GAASjU,GAAQV,GAASU,GAAQgM,EAClCiI,GAAU0B,EAAgB3V,EAAK4V,eAAkBH,EACnCzJ,EACbiI,GAAS0B,GAAuB,WAAN5R,EAAiB,MAAc,YAANA,EAAkB,OAAS,WAAakQ,GAASjU,EAAO,IAAM+D,GAAKiI,EAAKjI,KAGtHiI,EAEY,SAApB6J,GAAoBJ,UAAY,SAAA1R,UAAKA,EAAI,IAAM,EAAI0R,EAAQ,EAAS,EAAJ1R,IAAW,EAAI,GAAK0R,EAAmB,GAAV1R,EAAI,KAAW,GAC3F,SAAjB+R,GAAkB1L,EAAM2L,EAAWC,GAIvB,SAAVP,GAAU1R,UAAW,IAANA,EAAU,EAAIkS,WAAM,GAAO,GAAKlS,GAAMmS,GAAMnS,EAAIoS,GAAMC,GAAM,MAHxEH,EAAmB,GAAbF,EAAkBA,EAAY,EACvCK,GAAMJ,IAAW5L,EAAO,GAAK,OAAS2L,EAAY,EAAIA,EAAY,GAClEI,EAAKC,EAAKC,GAAQ3U,KAAK4U,KAAK,EAAIL,IAAO,GAEvCjK,EAAiB,QAAT5B,EAAkBqL,GAAoB,OAATrL,EAAiB,SAAArG,UAAK,EAAI0R,GAAQ,EAAI1R,IAAK8R,GAAkBJ,WACnGW,EAAKC,EAAOD,EACZpK,EAAKkE,OAAS,SAAC6F,EAAWC,UAAWF,GAAe1L,EAAM2L,EAAWC,IAC9DhK,EAEM,SAAduK,GAAenM,EAAMoM,GACN,SAAVf,GAAU1R,UAAKA,IAAQA,EAAKA,IAAMyS,EAAY,GAAKzS,EAAIyS,GAAa,EAAK,WADzDA,IAAAA,EAAY,aAE/BxK,EAAgB,QAAT5B,EAAiBqL,GAAmB,OAATrL,EAAgB,SAAArG,UAAK,EAAI0R,GAAQ,EAAI1R,IAAK8R,GAAkBJ,WAC/FzJ,EAAKkE,OAAS,SAAAsG,UAAaD,GAAYnM,EAAMoM,IACtCxK,EAtiCT,IAWCyK,EACAnT,EAAYoT,EA0BZ/R,EAAiBwG,EAAMwL,EAAclL,EAErCrM,EACAwX,EAYArN,EAilBAsN,EAwOAC,EAUEC,EAAKC,EAAMC,EAAMC,EAAOC,EAAQC,EAR7BC,EACHC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EAqMDlU,EACGmU,EA7jCDC,EAAU,CACZC,UAAW,IACXC,QAAS,OACTC,eAAgB,EAChBC,MAAO,CAACC,WAAW,KAEpBC,EAAY,CACXnR,SAAU,GACVoR,WAAW,EACXC,MAAO,GAIR/K,EAAU,IACV5F,EAAW,EAAI4F,EACf8I,EAAiB,EAAV3U,KAAK6W,GACZC,EAAWnC,EAAO,EAClBoC,EAAQ,EACR/K,EAAQhM,KAAKgX,KACbC,EAAOjX,KAAKkX,IACZ1C,EAAOxU,KAAKmX,IASZC,EAAwC,mBAAhBC,aAA8BA,YAAYC,QAAW,aAC7EjU,EAAWkU,MAAM3K,QACjBe,GAAgB,oBAChBqD,GAAU,mCACVO,GAAkB,8BAClBiG,GAAuB,mCACvBC,GAAU,gBACVvV,GAAqB,kBACrBkH,GAAW,wCAEXxL,GAAW,GAQX8Z,GAAuB,CAAChW,gBAAgB,EAAMiW,SAAS,EAAMvJ,MAAM,GACnEpJ,GAAsB,CAACtD,gBAAgB,EAAM0M,MAAM,GACnDwJ,GAAgB,CAAClW,gBAAgB,GACjCoO,GAAiB,GACjB7O,GAAc,GACdG,GAAc,GAEduO,GAAW,GACXkI,GAAW,GACXC,GAAe,GACf9Y,GAAkB,GAClB+Y,GAAiB,GAiEjBpa,GAAS,SAATA,OAAU8E,EAAMC,OACV,IAAIL,KAAKK,EACbD,EAAKJ,GAAKK,EAAQL,UAEZI,GAoGR4C,GAAkB,SAAlBA,gBAAmBoC,EAAOuQ,OACrBC,EAAQjY,KAAKoN,MAAM3F,GAASuQ,UACzBvQ,GAAUwQ,IAAUxQ,EAASwQ,EAAQ,EAAIA,GAmEjD/Q,GAAqB,SAArBA,0BAAuBgR,IAAAA,WAAmB,gBAATA,GAAmC,YAATA,GA+E3DC,GAAgB,CAACxT,OAAO,EAAGyT,QAAQ5Z,EAAYoH,cAAcpH,GAC7DsI,GAAiB,SAAjBA,eAAkBtF,EAAWoF,EAAUyR,OAIrCzZ,EAAG0Z,EAAQC,EAHRtK,EAASzM,EAAUyM,OACtBuK,EAAShX,EAAU2F,SAAWgR,GAC9BM,EAAkBjX,EAAU+D,YAAcsG,EAAU2M,EAAOJ,SAAQ,GAAS5W,EAAUkD,YAEnF5H,EAAU8J,KAAcgE,MAAMhE,IAAcA,KAAYqH,IAC3DqK,EAAS1R,EAAStG,OAAO,GACzBiY,EAAoC,MAAxB3R,EAASnG,QAAQ,GAC7B7B,EAAIgI,EAAS9F,QAAQ,KACN,MAAXwX,GAA6B,MAAXA,GAChB,GAAL1Z,IAAWgI,EAAWA,EAASmL,QAAQ,IAAK,MACzB,MAAXuG,EAAiBE,EAAO7T,OAAS6T,EAAOJ,QAA0B,GAAlBI,EAAOpT,WAAkB5E,WAAWoG,EAASnG,OAAO,KAAO,IAAM8X,GAAa3Z,EAAI,EAAI4Z,EAASH,GAAkBzS,gBAAkB,IAAM,IAE9LhH,EAAI,GACNgI,KAAYqH,IAAYA,EAAOrH,GAAY6R,GACrCxK,EAAOrH,KAEf0R,EAAS9X,WAAWoG,EAAStG,OAAO1B,EAAE,GAAKgI,EAASnG,OAAO7B,EAAE,IACzD2Z,GAAaF,IAChBC,EAASA,EAAS,KAAOjV,EAASgV,GAAoBA,EAAiB,GAAKA,GAAkBzS,iBAEnF,EAAJhH,EAASkI,eAAetF,EAAWoF,EAASnG,OAAO,EAAG7B,EAAE,GAAIyZ,GAAoBC,EAASG,EAAkBH,IAEhG,MAAZ1R,EAAoB6R,GAAmB7R,GAsBhDH,GAAS,SAATA,OAAUgF,EAAKD,EAAKzO,UAAUA,EAAQ0O,EAAMA,EAAcD,EAARzO,EAAcyO,EAAMzO,GAGtE2b,GAAS,GAAGvX,MAIZ7B,GAAU,SAAVA,QAAWvC,EAAOU,EAAOkb,UAAiB3D,IAAavX,GAASuX,EAAStL,SAAWsL,EAAStL,SAAS3M,IAASD,EAAUC,IAAW4b,IAAiB1D,GAAiBvF,KAAqErM,EAAStG,GAFzO,SAAX6b,SAAYC,EAAIF,EAAcG,mBAAAA,IAAAA,EAAc,IAAOD,EAAG/Y,QAAQ,SAAA/C,UAAUD,EAAUC,KAAW4b,GAAiBrP,GAAavM,EAAO,GAAK+b,EAAY9Q,WAAZ8Q,EAAoBxZ,GAAQvC,IAAU+b,EAAY9Q,KAAKjL,MAAW+b,EAEoDF,CAAS7b,EAAO4b,GAAgBrP,GAAavM,GAAS2b,GAAOK,KAAKhc,EAAO,GAAKA,EAAQ,CAACA,GAAS,GAA5K2b,GAAOK,MAAMtb,GAASsM,GAAMD,iBAAiB/M,GAAQ,IA4ItOic,GAAW,SAAXA,SAAYC,EAAOC,EAAOC,EAAQC,EAAQrc,OACrCsc,EAAUH,EAAQD,EACrBK,EAAWF,EAASD,SACdjQ,GAAmBnM,EAAO,SAAAA,UAASoc,IAAapc,EAAQkc,GAASI,EAAWC,GAAa,MAoDjGhL,GAAY,SAAZA,UAAa9M,EAAWkH,EAAM6Q,OAK5B5Q,EAAQlL,EAAOkU,EAJZnS,EAAIgC,EAAUuB,KACjByW,EAAWha,EAAEkJ,GACb+Q,EAAczE,EACd0E,EAAUlY,EAAUmY,QAEhBH,SAGL7Q,EAASnJ,EAAEkJ,EAAO,UAClBjL,EAAQ+B,EAAEoa,eAAiBpY,EAC3B+X,GAAoBtY,GAAYhC,QAAU8B,KAC1C2Y,IAAY1E,EAAW0E,GACvB/H,EAAShJ,EAAS6Q,EAAShH,MAAM/U,EAAOkL,GAAU6Q,EAAST,KAAKtb,GAChEuX,EAAWyE,EACJ9H,GASR1B,GAAuB,GAqDvBK,GAAO,IACPO,GAAe,CACdgJ,KAAK,CAAC,EAAEvJ,GAAKA,IACbwJ,KAAK,CAAC,EAAExJ,GAAK,GACbyJ,OAAO,CAAC,IAAI,IAAI,KAChBjJ,MAAM,CAAC,EAAE,EAAE,GACXkJ,OAAO,CAAC,IAAI,EAAE,GACdC,KAAK,CAAC,EAAE,IAAI,KACZC,KAAK,CAAC,EAAE,EAAE5J,IACV6J,KAAK,CAAC,EAAE,EAAE,KACVC,MAAM,CAAC9J,GAAKA,GAAKA,IACjB+J,MAAM,CAAC,IAAI,IAAI,GACfC,OAAO,CAAChK,GAAKA,GAAK,GAClBiK,OAAO,CAACjK,GAAK,IAAI,GACjBkK,KAAK,CAAC,IAAI,IAAI,KACdC,OAAO,CAAC,IAAI,EAAE,KACdC,MAAM,CAAC,EAAE,IAAI,GACbC,IAAI,CAACrK,GAAK,EAAE,GACZsK,KAAK,CAACtK,GAAK,IAAI,KACfuK,KAAK,CAAC,EAAEvK,GAAKA,IACbW,YAAY,CAACX,GAAKA,GAAKA,GAAK,IAqH7BgB,GAAa,eAEXjP,EADGqL,EAAI,6EAEHrL,KAAKwO,GACTnD,GAAK,IAAMrL,EAAI,aAET,IAAIyY,OAAOpN,EAAI,IAAK,MANf,GAQb2E,GAAU,YAkCVvK,IACK6N,EAAWoF,KAAKC,IACnBpF,EAAgB,IAChBC,EAAe,GACfC,EAAaH,IACbI,EAAcD,EAEdG,EADAD,EAAO,IAAO,IA0BfR,EAAQ,CACP/T,KAAK,EACLsG,MAAM,EACNkT,qBACCC,IAAM,IAEPC,+BAAWC,UACH3F,GAAU,KAAQ2F,GAAO,MAEjCC,qBACKnG,KACED,GAAgB5X,MACpBoM,EAAOwL,EAAe3X,OACtByM,EAAON,EAAK6R,UAAY,GACxB1d,GAASC,KAAOA,IACf4L,EAAK8R,eAAiB9R,EAAK8R,aAAe,KAAKvT,KAAKnK,GAAK2d,SAC1Dhe,EAASE,GAAiB+L,EAAKgS,mBAAsBhS,EAAK5L,MAAQ4L,GAAS,IAC3E8L,EAAO9L,EAAKiS,sBACZzL,GAAqBnQ,QAAQyO,KAE9B8G,GAAOG,EAAMmG,QACbrG,EAAOC,GAAS,SAAAqG,UAAKC,WAAWD,EAAI3F,EAAyB,IAAbT,EAAM/T,KAAc,EAAK,IACzE2T,EAAgB,EAChB8F,GAAM,KAGRS,wBACEpG,EAAO9L,EAAKqS,qBAAuBC,cAAc1G,GAClDD,EAAgB,EAChBE,EAAO9W,GAERwd,mCAAaC,EAAWC,GACvBtG,EAAgBqG,GAAaE,EAAAA,EAC7BtG,EAAe7V,KAAKyL,IAAIyQ,GAAe,GAAItG,IAE5CwF,iBAAIA,GACHpF,EAAO,KAAQoF,GAAO,KACtBnF,EAAyB,IAAbT,EAAM/T,KAAcuU,GAEjCzP,iBAAIiT,EAAU4C,EAAMC,OACfzc,EAAOwc,EAAO,SAACvY,EAAGyH,EAAGsQ,EAAGpc,GAAOga,EAAS3V,EAAGyH,EAAGsQ,EAAGpc,GAAIgW,EAAMlR,OAAO1E,IAAU4Z,SAChFhE,EAAMlR,OAAOkV,GACbtD,EAAWmG,EAAa,UAAY,QAAQzc,GAC5C8P,KACO9P,GAER0E,uBAAOkV,EAAU5a,KACdA,EAAIsX,EAAWpV,QAAQ0Y,KAActD,EAAW9W,OAAOR,EAAG,IAAYA,GAAN8W,GAAWA,KAE9EQ,WAzEAA,EAAa,KA6EfxG,GAAQ,SAARA,eAAe0F,GAAiBtN,GAAQuT,QAoBxC9I,GAAW,GACXc,GAAiB,sBACjBP,GAAa,QA4Bb1G,GAAc,SAAdA,YAAc9B,UAAQ,SAAAjI,UAAK,EAAIiI,EAAK,EAAIjI,KAoBxCkI,GAAa,SAAbA,WAAcD,EAAMgS,UAAiBhS,IAAsBtN,EAAYsN,GAAQA,EAAOiI,GAASjI,IAASgI,GAAsBhI,KAAlFgS,GAjJlC,SAARpB,GAAQ1b,OAGN+c,EAASC,EAAU/a,EAAMsG,EAFtB0U,EAAU9G,IAAaI,EAC1B2G,GAAe,IAANld,KAEAoW,EAAV6G,IAA4B3G,GAAc2G,EAAU5G,IAItC,GADd0G,GADA9a,GADAsU,GAAe0G,GACM3G,GACJG,IACEyG,KAClB3U,IAAUyN,EAAMzN,MAChB0N,EAAShU,EAAoB,IAAb+T,EAAM/T,KACtB+T,EAAM/T,KAAOA,GAAc,IAC3BwU,GAAasG,GAAsBvG,GAAXuG,EAAkB,EAAIvG,EAAOuG,GACrDC,EAAW,GAEZE,IAAWrH,EAAMC,EAAK4F,KAClBsB,MACE9G,EAAK,EAAGA,EAAKQ,EAAWjX,OAAQyW,IACpCQ,EAAWR,GAAIjU,EAAMgU,EAAQ1N,EAAOvI,GAqL9B,SAAVuU,GAAU1R,UAAMA,EAAI8T,EAAMnU,EAAIK,EAAIA,EAAKA,EAFlC,kBAE4CL,WAAKK,EAAI,IAEjD,KAF6D,GAAI,IAAOA,EAD5E,kBACsFL,GAAKK,GAAK,KAE5F,MAFwGA,EAAI,MAAQL,WAAKK,EAAI,MAE7H,KAF2I,GAAI,QAV1J3C,GAAa,uCAAwC,SAACpB,EAAMM,OACvD+d,EAAQ/d,EAAI,EAAIA,EAAI,EAAIA,EAC5BiV,GAAYvV,EAAO,UAAYqe,EAAQ,GAAI/d,EAAI,SAAAyD,mBAAKA,EAAKsa,IAAQ,SAAAta,UAAKA,GAAG,SAAAA,UAAK,WAAK,EAAIA,EAAMsa,IAAO,SAAAta,UAAKA,EAAI,GAAKrC,SAAK,EAAJqC,EAAUsa,GAAQ,EAAI,EAAI3c,SAAW,GAAT,EAAIqC,GAAWsa,GAAQ,MAEvKpK,GAASqK,OAAOC,SAAWtK,GAASuK,KAAOvK,GAASqK,OAAO9I,OAC3DD,GAAY,UAAWO,GAAe,MAAOA,GAAe,OAAQA,MAClEpS,EAMC,OALEmU,EAAK,EAKC,KADVtC,GAAY,SAAU,SAAAxR,UAAK,EAAI0R,GAAQ,EAAI1R,IAAI0R,IAEhDF,GAAY,OAAQ,SAAAxR,UAAKA,WAAI,EAAM,IAAMA,EAAI,IAAM,IACnDwR,GAAY,OAAQ,SAAAxR,WAAO2J,EAAM,EAAK3J,EAAIA,GAAM,KAChDwR,GAAY,OAAQ,SAAAxR,UAAW,IAANA,EAAU,EAA0B,EAArB4U,EAAK5U,EAAIyU,KACjDjD,GAAY,OAAQgB,GAAY,MAAOA,GAAY,OAAQA,MAC3DtC,GAASwK,YAAcxK,GAASyK,MAAQpf,GAASmf,YAAc,CAC9DvO,uBAAOwO,EAAWC,YAAXD,IAAAA,EAAQ,OACVzI,EAAK,EAAIyI,EACZtI,EAAKsI,GAASC,EAAiB,EAAI,GACnCxI,EAAKwI,EAAiB,EAAI,SAEpB,SAAA5a,WAAQqS,EAAKjO,GAAO,EADpB,UAC4BpE,GAAM,GAAKoS,GAAMF,KAGtDmC,EAAUpM,KAAOiI,GAAS,YAG1B7S,GAAa,qEAAsE,SAAApB,UAAQyZ,IAAkBzZ,EAAO,IAAMA,EAAO,mBAoBpHa,GAEZ,iBAAYN,EAAQE,QACdme,GAAKnG,KACVlY,EAAOC,MAAQqe,MACVte,OAASA,OACTE,QAAUA,OACVsQ,IAAMtQ,EAAUA,EAAQsQ,IAAM9P,QAC9B6d,IAAMre,EAAUA,EAAQuQ,UAAYC,IAyB9B8N,6BAmBZzG,MAAA,eAAM7Z,UACDA,GAAmB,IAAVA,QACPiG,QAAUma,KAAKna,OAAOmD,mBAAsBgX,KAAKG,UAAUH,KAAKxY,OAAS5H,EAAQogB,KAAKpW,aACtFA,OAAShK,EACPogB,MAEDA,KAAKpW,WAGbxB,SAAA,kBAASxI,UACDwgB,UAAUte,OAASke,KAAKvX,cAA6B,EAAfuX,KAAK/X,QAAcrI,GAASA,EAAQogB,KAAK3X,SAAW2X,KAAK/X,QAAUrI,GAASogB,KAAKvX,iBAAmBuX,KAAKzY,SAGvJkB,cAAA,uBAAc7I,UACRwgB,UAAUte,aAGV2F,OAAS,EACPqD,GAAakV,KAAMA,KAAK/X,QAAU,EAAIrI,GAASA,EAASogB,KAAK/X,QAAU+X,KAAK3X,UAAa2X,KAAK/X,QAAU,KAHvG+X,KAAKtX,UAMdf,UAAA,mBAAUA,EAAWpD,MACpBgO,MACK6N,UAAUte,cACPke,KAAK7X,WAETtC,EAASma,KAAK5Z,OACdP,GAAUA,EAAOmD,mBAAqBgX,KAAKxX,IAAK,KACnDO,GAAeiX,KAAMrY,IACpB9B,EAAOO,KAAOP,EAAOA,QAAUqD,GAAerD,EAAQma,MAEhDna,GAAUA,EAAOA,QACnBA,EAAOA,OAAOoD,QAAUpD,EAAO2B,QAAwB,GAAd3B,EAAO2C,IAAW3C,EAAOsC,OAAStC,EAAO2C,KAAO3C,EAAO4C,gBAAkB5C,EAAOsC,SAAWtC,EAAO2C,MAC9I3C,EAAO8B,UAAU9B,EAAOsC,QAAQ,GAEjCtC,EAASA,EAAOA,QAEZma,KAAKna,QAAUma,KAAK5Z,IAAIc,qBAAmC,EAAX8Y,KAAKxX,KAAWb,EAAYqY,KAAKtX,OAAWsX,KAAKxX,IAAM,GAAiB,EAAZb,IAAoBqY,KAAKtX,QAAUf,IACnJ6B,GAAewW,KAAK5Z,IAAK4Z,KAAMA,KAAKxY,OAASwY,KAAKpW,eAG1CoW,KAAK7X,SAAWR,IAAeqY,KAAKzY,OAAShD,GAAoByb,KAAKtb,UAAY7B,KAAK+F,IAAIoX,KAAKzW,UAAYT,IAAenB,IAAcqY,KAAKtb,WAAasb,KAAK5W,KAAO4W,KAAKK,mBAC1K7X,MAAQwX,KAAKM,OAAS3Y,GAG1BvD,GAAgB4b,KAAMrY,EAAWpD,IAIlCyb,SAGR1b,KAAA,cAAK1E,EAAO2E,UACJ6b,UAAUte,OAASke,KAAKrY,UAAW9E,KAAKyL,IAAI0R,KAAKvX,gBAAiB7I,EAAQoI,GAAsBgY,QAAUA,KAAKzY,KAAOyY,KAAK3X,WAAczI,EAAQogB,KAAKzY,KAAO,GAAIhD,GAAkByb,KAAK/W,UAGhMkC,cAAA,uBAAcvL,EAAO2E,UACb6b,UAAUte,OAASke,KAAKrY,UAAWqY,KAAKvX,gBAAkB7I,EAAO2E,GAAkByb,KAAKvX,gBAAkB5F,KAAKyL,IAAI,EAAG0R,KAAK7X,OAAS6X,KAAKtX,OAASsX,KAAKO,UAG/JrP,SAAA,kBAAStR,EAAO2E,UACR6b,UAAUte,OAASke,KAAKrY,UAAWqY,KAAK5X,aAAc4X,KAAKzJ,OAA8B,EAAnByJ,KAAKQ,YAA+B5gB,EAAZ,EAAIA,GAAiBoI,GAAsBgY,MAAOzb,GAAmByb,KAAK5X,WAAavF,KAAKyL,IAAI,EAAG0R,KAAK/W,MAAQ+W,KAAKzY,MAAQyY,KAAKO,UAGxOC,UAAA,mBAAU5gB,EAAO2E,OACZsW,EAAgBmF,KAAK5X,WAAa4X,KAAK3X,eACpC+X,UAAUte,OAASke,KAAKrY,UAAUqY,KAAK/W,OAASrJ,EAAQ,GAAKib,EAAetW,GAAkByb,KAAK/X,QAAUC,GAAgB8X,KAAK7X,OAAQ0S,GAAiB,EAAI,MAcvKhR,UAAA,mBAAUjK,OACJwgB,UAAUte,cACPke,KAAKnX,QAAUC,EAAW,EAAIkX,KAAKnX,QAEvCmX,KAAKnX,OAASjJ,SACVogB,SAEJ1V,EAAQ0V,KAAKna,QAAUma,KAAKxX,IAAMF,GAAwB0X,KAAKna,OAAOoD,MAAO+W,MAAQA,KAAK7X,mBAMzFU,MAAQjJ,GAAS,OACjB4I,IAAOwX,KAAKS,KAAO7gB,KAAWkJ,EAAY,EAAIkX,KAAKnX,UACnDlB,UAAU2B,IAAQzG,KAAK+F,IAAIoX,KAAKpW,QAASoW,KAAKtX,MAAO4B,IAAQ,GAClE3B,GAAQqX,MAriCW,SAApBU,kBAAoBrc,WACfwB,EAASxB,EAAUwB,OAChBA,GAAUA,EAAOA,QACvBA,EAAO4B,OAAS,EAChB5B,EAAO4C,gBACP5C,EAASA,EAAOA,cAEVxB,EA+hCAqc,CAAkBV,UAG1BW,OAAA,gBAAO/gB,UACDwgB,UAAUte,QAGXke,KAAKS,MAAQ7gB,UACX6gB,IAAM7gB,SAEL0gB,OAASN,KAAK7X,QAAUtF,KAAKwL,KAAK2R,KAAKpW,OAAQoW,KAAK3W,gBACpDb,IAAMwX,KAAK5Y,KAAO,IAEvBmL,UACK/J,IAAMwX,KAAKnX,UAEXlB,UAAUqY,KAAKna,SAAWma,KAAKna,OAAOmD,kBAAoBgX,KAAK3W,UAAY2W,KAAK7X,QAAU6X,KAAKM,OAA6B,IAApBN,KAAK9O,YAAqBrO,KAAK+F,IAAIoX,KAAKzW,UAAYT,IAAakX,KAAK7X,QAAUW,MAGxLkX,MAdCA,KAAKS,QAiBdN,UAAA,mBAAUvgB,MACLwgB,UAAUte,OAAQ,MAChB0F,OAAS5H,MACViG,EAASma,KAAKna,QAAUma,KAAK5Z,WACjCP,IAAWA,EAAOiE,OAAUkW,KAAKna,QAAW2D,GAAe3D,EAAQma,KAAMpgB,EAAQogB,KAAKpW,QAC/EoW,YAEDA,KAAKxY,WAGbyT,QAAA,iBAAQ2F,UACAZ,KAAKxY,QAAUvH,EAAY2gB,GAAkBZ,KAAKvX,gBAAkBuX,KAAK5X,YAAcvF,KAAK+F,IAAIoX,KAAKxX,KAAO,OAGpHa,QAAA,iBAAQwX,OACHhb,EAASma,KAAKna,QAAUma,KAAK5Z,WACzBP,EAAwBgb,KAAiBb,KAAKxX,KAAQwX,KAAK/X,SAAW+X,KAAK/W,OAAS+W,KAAK7U,gBAAkB,GAAO6U,KAAK7X,QAAU6X,KAAKzY,KAAOyY,KAAK3X,SAAY2X,KAAKxX,IAAoBF,GAAwBzC,EAAOwD,QAAQwX,GAAcb,MAAnEA,KAAK7X,OAArK6X,KAAK7X,WAGvBP,OAAA,gBAAOyJ,YAAAA,IAAAA,EAAQoJ,QACVqG,EAAkBrc,SACtBA,EAAa4M,GACT2O,KAAKtb,UAAYsb,KAAKrb,iBACpBwE,UAAY6W,KAAK7W,SAASvB,OAAOyJ,QACjC1J,WAAW,IAAM0J,EAAO9M,iBAEhB,gBAATwW,OAAqC,IAAhB1J,EAAOJ,MAAkB+O,KAAK/O,OACxDxM,EAAaqc,EACNd,SAGRe,WAAA,oBAAW1X,WACNhF,EAAY2b,KACf1b,EAAO8b,UAAUte,OAASuH,EAAUhF,EAAUgF,UACxChF,GACNC,EAAOD,EAAUmD,OAASlD,GAAQD,EAAUmE,KAAO,GACnDnE,EAAYA,EAAU+B,WAEf4Z,KAAKna,QAAUma,KAAKgB,KAAQhB,KAAKgB,KAAKpb,KAAKkC,iBAAkB,EAAA,EAAYkY,KAAKgB,KAAKD,WAAW1X,GAAY/E,MAGnH2G,OAAA,gBAAOrL,UACFwgB,UAAUte,aACRmG,QAAUrI,IAAUof,EAAAA,GAAY,EAAIpf,EAClCwL,GAAuB4U,QAEN,IAAlBA,KAAK/X,QAAiB+W,EAAAA,EAAWgB,KAAK/X,YAG9CgZ,YAAA,qBAAYrhB,MACPwgB,UAAUte,OAAQ,KACjBwC,EAAO0b,KAAK/W,kBACXZ,QAAUzI,EACfwL,GAAuB4U,MAChB1b,EAAO0b,KAAK1b,KAAKA,GAAQ0b,YAE1BA,KAAK3X,YAGb6Y,KAAA,cAAKthB,UACAwgB,UAAUte,aACRyU,MAAQ3W,EACNogB,MAEDA,KAAKzJ,UAGb4K,KAAA,cAAK1X,EAAUlF,UACPyb,KAAKrY,UAAUgC,GAAeqW,KAAMvW,GAAWxJ,EAAYsE,QAGnE6c,QAAA,iBAAQC,EAAc9c,UACdyb,KAAKsB,OAAO3Z,UAAU0Z,GAAgBrB,KAAKpW,OAAS,EAAG3J,EAAYsE,QAG3E+c,KAAA,cAAKjU,EAAM9I,UACF,MAAR8I,GAAgB2S,KAAKmB,KAAK9T,EAAM9I,GACzByb,KAAKuB,UAAS,GAAOZ,QAAO,OAGpCa,QAAA,iBAAQnU,EAAM9I,UACL,MAAR8I,GAAgB2S,KAAKmB,KAAK9T,GAAQ2S,KAAKvX,gBAAiBlE,GACjDyb,KAAKuB,UAAS,GAAMZ,QAAO,OAGnCc,MAAA,eAAMC,EAAQnd,UACH,MAAVmd,GAAkB1B,KAAKmB,KAAKO,EAAQnd,GAC7Byb,KAAKW,QAAO,OAGpBgB,OAAA,yBACQ3B,KAAKW,QAAO,OAGpBY,SAAA,kBAAS3hB,UACJwgB,UAAUte,UACXlC,IAAUogB,KAAKuB,YAAcvB,KAAKnW,WAAWmW,KAAKnX,OAASjJ,GAASkJ,EAAW,IAC1EkX,MAEDA,KAAKnX,KAAO,MAGpB+Y,WAAA,kCACMld,SAAWsb,KAAK5Y,KAAO,OACvBmC,QAAUT,EACRkX,SAGR6B,SAAA,wBAGExY,EAFGxD,EAASma,KAAKna,QAAUma,KAAK5Z,IAChCnD,EAAQ+c,KAAKxY,eAEH3B,KAAWma,KAAKxX,KAAOwX,KAAKtb,UAAYmB,EAAOgc,aAAexY,EAAUxD,EAAOwD,SAAQ,KAAUpG,GAASoG,EAAU2W,KAAK/E,SAAQ,GAAQnS,QAGrJgZ,cAAA,uBAAcvW,EAAM8Q,EAAU7Q,OACzB5F,EAAOoa,KAAKpa,YACO,EAAnBwa,UAAUte,QACRua,GAGJzW,EAAK2F,GAAQ8Q,EACb7Q,IAAW5F,EAAK2F,EAAO,UAAYC,GAC1B,aAATD,IAAwByU,KAAK+B,UAAY1F,WAJlCzW,EAAK2F,GAMNyU,MAEDpa,EAAK2F,OAGbyW,KAAA,cAAKC,OACAC,EAAOlC,YACJ,IAAImC,QAAQ,SAAAC,GAEN,SAAXC,SACKC,EAAQJ,EAAKF,KACjBE,EAAKF,KAAO,KACZniB,EAAY4e,KAAOA,EAAIA,EAAEyD,MAAWzD,EAAEuD,MAAQvD,IAAMyD,KAAUA,EAAKF,KAAOM,GAC1EF,EAAQ3D,GACRyD,EAAKF,KAAOM,MANV7D,EAAI5e,EAAYoiB,GAAeA,EAAchd,GAQ7Cid,EAAKxd,UAAsC,IAAzBwd,EAAK/W,iBAAqC,GAAZ+W,EAAK1Z,MAAe0Z,EAAK/Z,QAAU+Z,EAAK1Z,IAAM,EACjG6Z,KAEAH,EAAKK,MAAQF,SAKhBpR,KAAA,gBACCF,GAAWiP,qCA9RApa,QACNA,KAAOA,OACPgE,QAAUhE,EAAK6T,OAAS,GACxBuG,KAAK/X,QAAUrC,EAAKqF,SAAW+T,EAAAA,GAAY,EAAIpZ,EAAKqF,QAAU,UAC7D5C,QAAUzC,EAAKqb,aAAe,OAC9B1K,QAAU3Q,EAAKsb,QAAUtb,EAAK0Q,eAE/B9N,IAAM,EACXsC,GAAakV,MAAOpa,EAAKwC,SAAU,EAAG,QACjC2S,KAAOnV,EAAKmV,KACblD,SACE2E,KAAO3E,GACHkD,KAAKlQ,KAAKmV,MAEpB/H,GAAiBtN,GAAQuT,OAqR3B/Y,GAAa+a,GAAUzN,UAAW,CAACxJ,MAAM,EAAGzB,OAAO,EAAGF,KAAK,EAAGa,OAAO,EAAGO,MAAM,EAAGjB,OAAO,EAAGQ,QAAQ,EAAGsO,OAAM,EAAO1Q,OAAO,KAAMnB,UAAS,EAAO2D,QAAQ,EAAGG,IAAI,EAAGpC,IAAI,EAAGma,MAAM,EAAGhX,QAAQT,EAAUyZ,MAAM,EAAG9B,KAAI,EAAO5X,KAAK,QAyBhNwC,iCAEAzF,EAAW6D,yBAAX7D,IAAAA,EAAO,mBACZA,UACDkL,OAAS,KACT9H,oBAAsBpD,EAAKoD,oBAC3B9B,qBAAuBtB,EAAKsB,qBAC5B4C,MAAQ7J,EAAY2F,EAAK4c,cAC9B1c,GAAmB0D,GAAe5D,EAAKC,QAAUC,4BAAuB2D,GACxE7D,EAAK2b,UAAYkB,EAAKjB,UACtB5b,EAAK+a,QAAU8B,EAAK9B,QAAO,GAC3B/a,EAAKoL,eAAiB/G,6BAAqBrE,EAAKoL,8EAGjD0R,GAAA,YAAGnhB,EAASqE,EAAM6D,UACjB6B,GAAiB,EAAG8U,UAAWJ,MACxBA,QAGR3S,KAAA,cAAK9L,EAASqE,EAAM6D,UACnB6B,GAAiB,EAAG8U,UAAWJ,MACxBA,QAGR2C,OAAA,gBAAOphB,EAASqhB,EAAUC,EAAQpZ,UACjC6B,GAAiB,EAAG8U,UAAWJ,MACxBA,QAGRC,IAAA,aAAI1e,EAASqE,EAAM6D,UAClB7D,EAAKwC,SAAW,EAChBxC,EAAKC,OAASma,KACdra,GAAiBC,GAAMqb,cAAgBrb,EAAKqF,OAAS,GACrDrF,EAAKkC,kBAAoBlC,EAAKkC,oBAC1BgE,GAAMvK,EAASqE,EAAM+D,GAAeqW,KAAMvW,GAAW,GAClDuW,QAGRpE,KAAA,cAAKS,EAAU7Q,EAAQ/B,UACfD,GAAewW,KAAMlU,GAAMgX,YAAY,EAAGzG,EAAU7Q,GAAS/B,MAIrEsZ,UAAA,mBAAUxhB,EAAS6G,EAAUxC,EAAMod,EAASvZ,EAAUwZ,EAAeC,UACpEtd,EAAKwC,SAAWA,EAChBxC,EAAKod,QAAUpd,EAAKod,SAAWA,EAC/Bpd,EAAKud,WAAaF,EAClBrd,EAAKwd,iBAAmBF,EACxBtd,EAAKC,OAASma,SACVlU,GAAMvK,EAASqE,EAAM+D,GAAeqW,KAAMvW,IACvCuW,QAGRqD,YAAA,qBAAY9hB,EAAS6G,EAAUxC,EAAMod,EAASvZ,EAAUwZ,EAAeC,UACtEtd,EAAKgG,aAAe,EACpBjG,GAAiBC,GAAMkC,gBAAkB7H,EAAY2F,EAAKkC,iBACnDkY,KAAK+C,UAAUxhB,EAAS6G,EAAUxC,EAAMod,EAASvZ,EAAUwZ,EAAeC,MAGlFI,cAAA,uBAAc/hB,EAAS6G,EAAUwa,EAAUC,EAAQG,EAASvZ,EAAUwZ,EAAeC,UACpFL,EAAOhX,QAAU+W,EACjBjd,GAAiBkd,GAAQ/a,gBAAkB7H,EAAY4iB,EAAO/a,iBACvDkY,KAAK+C,UAAUxhB,EAAS6G,EAAUya,EAAQG,EAASvZ,EAAUwZ,EAAeC,MAGpF/e,OAAA,gBAAOwD,EAAWpD,EAAgBC,OAMhCF,EAAMgC,EAAOS,EAAMyZ,EAAW3F,EAAe0I,EAAYC,EAAY3Z,EAAW4Z,EAAWC,EAAexC,EAAM9K,EAL7GuN,EAAW3D,KAAK/W,MACnB2a,EAAO5D,KAAKvY,OAASuY,KAAKvX,gBAAkBuX,KAAKtX,MACjDwC,EAAM8U,KAAKzY,KACX+C,EAAQ3C,GAAa,EAAI,EAAI5E,GAAc4E,GAC3Ckc,EAAiB7D,KAAKzW,OAAS,GAAQ5B,EAAY,IAAOqY,KAAKtb,WAAawG,aAEpEpF,GAA2B8d,EAARtZ,GAA6B,GAAb3C,IAAmB2C,EAAQsZ,GACnEtZ,IAAU0V,KAAK7X,QAAU3D,GAASqf,EAAe,IAChDF,IAAa3D,KAAK/W,OAASiC,IAC9BZ,GAAS0V,KAAK/W,MAAQ0a,EACtBhc,GAAaqY,KAAK/W,MAAQ0a,GAE3Brf,EAAOgG,EACPmZ,EAAYzD,KAAKxY,OAEjB+b,IADA1Z,EAAYmW,KAAKxX,KAEbqb,IACH3Y,IAAQyY,EAAW3D,KAAKzW,SAEvB5B,GAAcpD,IAAoByb,KAAKzW,OAAS5B,IAE9CqY,KAAK/X,QAAS,IACjBiZ,EAAOlB,KAAKzJ,MACZsE,EAAgB3P,EAAM8U,KAAK3X,QACvB2X,KAAK/X,SAAW,GAAKN,EAAY,SAC7BqY,KAAKrY,UAA0B,IAAhBkT,EAAsBlT,EAAWpD,EAAgBC,MAExEF,EAAOvB,GAAcuH,EAAQuQ,GACzBvQ,IAAUsZ,GACbpD,EAAYR,KAAK/X,QACjB3D,EAAO4G,KAEPsV,KAAelW,EAAQuQ,KACN2F,IAAclW,EAAQuQ,IACtCvW,EAAO4G,EACPsV,KAEMtV,EAAP5G,IAAeA,EAAO4G,IAEvBwY,EAAgBxb,GAAgB8X,KAAK7X,OAAQ0S,IAC5C8I,GAAY3D,KAAK7X,QAAUub,IAAkBlD,GAAaR,KAAK7X,OAASub,EAAgB7I,EAAgBmF,KAAKzY,MAAQ,IAAMmc,EAAgBlD,GACxIU,GAAqB,EAAZV,IACZlc,EAAO4G,EAAM5G,EACb8R,EAAS,GAUNoK,IAAckD,IAAkB1D,KAAK8D,MAAO,KAC3CC,EAAa7C,GAAyB,EAAhBwC,EACzBM,EAAYD,KAAe7C,GAAqB,EAAZV,MACrCA,EAAYkD,IAAkBK,GAAaA,GAC3CJ,EAAWI,EAAY,EAAIzZ,EAAQY,EAAMA,EAAMZ,OAC1CwZ,MAAQ,OACR3f,OAAOwf,IAAavN,EAAS,EAAIrT,GAAcyd,EAAY3F,IAAiBtW,GAAiB2G,GAAK4Y,MAAQ,OAC1G3b,OAASmC,GACb/F,GAAkByb,KAAKna,QAAUsL,GAAU6O,KAAM,iBAC7Cpa,KAAKqe,gBAAkB7N,IAAW4J,KAAK4B,aAAakC,MAAQ,GAC5DH,GAAYA,IAAa3D,KAAK/W,OAAUsa,IAAgBvD,KAAKxX,KAAQwX,KAAKpa,KAAKse,WAAalE,KAAKna,SAAWma,KAAK5Y,YAC9G4Y,QAER9U,EAAM8U,KAAKzY,KACXqc,EAAO5D,KAAKtX,MACRsb,SACEF,MAAQ,EACbH,EAAWI,EAAY7Y,GAAO,UACzB/G,OAAOwf,GAAU,QACjB/d,KAAKqe,gBAAkB7N,GAAU4J,KAAK4B,mBAEvCkC,MAAQ,GACR9D,KAAKxX,MAAQ+a,SACVvD,KAGR7J,GAAmB6J,KAAM5J,OAGvB4J,KAAKmE,YAAcnE,KAAKoE,UAAYpE,KAAK8D,MAAQ,IACpDN,EArwCmB,SAAtBa,oBAAuBhgB,EAAWsf,EAAUrf,OACvCgC,KACOqd,EAAPrf,MACHgC,EAAQjC,EAAUgS,OACX/P,GAASA,EAAMkB,QAAUlD,GAAM,IAClB,YAAfgC,EAAMyU,MAAsBzU,EAAMkB,OAASmc,SACvCrd,EAERA,EAAQA,EAAMO,eAGfP,EAAQjC,EAAUigB,MACXhe,GAASA,EAAMkB,QAAUlD,GAAM,IAClB,YAAfgC,EAAMyU,MAAsBzU,EAAMkB,OAASmc,SACvCrd,EAERA,EAAQA,EAAMM,OAqvCDyd,CAAoBrE,KAAMjd,GAAc4gB,GAAW5gB,GAAcuB,OAE7EgG,GAAShG,GAAQA,EAAOkf,EAAWhc,cAIhCW,OAASmC,OACTrB,MAAQ3E,OACR8C,MAAQyC,EAERmW,KAAKtb,gBACJqd,UAAY/B,KAAKpa,KAAK2e,cACtB7f,SAAW,OACX6E,OAAS5B,EACdgc,EAAW,IAEPA,GAAYrf,IAASC,IAAmBic,IAC5CrP,GAAU6O,KAAM,WACZA,KAAK7X,SAAWmC,UACZ0V,QAGG2D,GAARrf,GAAiC,GAAbqD,MACvBrB,EAAQ0Z,KAAK3J,OACN/P,GAAO,IACbS,EAAOT,EAAMO,OACRP,EAAMc,MAAQ9C,GAAQgC,EAAMkB,SAAWlB,EAAMkC,KAAOgb,IAAeld,EAAO,IAC1EA,EAAMT,SAAWma,YACbA,KAAK7b,OAAOwD,EAAWpD,EAAgBC,MAE/C8B,EAAMnC,OAAmB,EAAZmC,EAAMkC,KAAWlE,EAAOgC,EAAMkB,QAAUlB,EAAMkC,KAAOlC,EAAMmB,OAASnB,EAAMmC,gBAAkBnC,EAAMoC,QAAUpE,EAAOgC,EAAMkB,QAAUlB,EAAMkC,IAAKjE,EAAgBC,GACvKF,IAAS0b,KAAK/W,QAAW+W,KAAKxX,MAAQ+a,EAAa,CACtDC,EAAa,EACbzc,IAASuD,GAAU0V,KAAKzW,QAAUT,UAIpCxC,EAAQS,MAEH,CACNT,EAAQ0Z,KAAKsE,cACTE,EAAe7c,EAAY,EAAIA,EAAYrD,EACxCgC,GAAO,IACbS,EAAOT,EAAMM,OACRN,EAAMc,MAAQod,GAAgBle,EAAMgB,OAAShB,EAAMkC,KAAOgb,IAAeld,EAAO,IAChFA,EAAMT,SAAWma,YACbA,KAAK7b,OAAOwD,EAAWpD,EAAgBC,MAE/C8B,EAAMnC,OAAmB,EAAZmC,EAAMkC,KAAWgc,EAAele,EAAMkB,QAAUlB,EAAMkC,KAAOlC,EAAMmB,OAASnB,EAAMmC,gBAAkBnC,EAAMoC,QAAU8b,EAAele,EAAMkB,QAAUlB,EAAMkC,IAAKjE,EAAgBC,GAAUC,IAAe6B,EAAM5B,UAAY4B,EAAM3B,WACxOL,IAAS0b,KAAK/W,QAAW+W,KAAKxX,MAAQ+a,EAAa,CACtDC,EAAa,EACbzc,IAASuD,GAAU0V,KAAKzW,OAASib,GAAgB1b,EAAWA,UAI9DxC,EAAQS,MAGNyc,IAAejf,SACbkd,QACL+B,EAAWrf,OAAewf,GAARrf,EAAmB,GAAKwE,GAAUS,OAAiBoa,GAARrf,EAAmB,GAAK,EACjF0b,KAAKxX,iBACHhB,OAASic,EACd9a,GAAQqX,MACDA,KAAK7b,OAAOwD,EAAWpD,EAAgBC,QAG3Cud,YAAcxd,GAAkB4M,GAAU6O,KAAM,YAAY,IAC5D1V,IAAUsZ,GAAQ5D,KAAK7X,QAAU6X,KAAKvX,kBAAsB6B,GAASqZ,KAAeF,IAAczD,KAAKxY,QAAU3E,KAAK+F,IAAIiB,KAAehH,KAAK+F,IAAIoX,KAAKxX,MAAWwX,KAAK8D,SAC1Knc,GAAcuD,KAAUZ,IAAUsZ,GAAmB,EAAX5D,KAAKxX,MAAc8B,GAAS0V,KAAKxX,IAAM,IAAOxB,GAAkBgZ,KAAM,GAC5Gzb,GAAoBoD,EAAY,IAAMgc,IAAcrZ,IAASqZ,GAAaC,IAC9EzS,GAAU6O,KAAO1V,IAAUsZ,GAAqB,GAAbjc,EAAiB,aAAe,qBAAsB,SACpF4a,OAAWjY,EAAQsZ,GAA2B,EAAnB5D,KAAKnW,aAAoBmW,KAAKuC,kBAI1DvC,QAGR5W,IAAA,aAAI9C,EAAOmD,iBACV3J,EAAU2J,KAAcA,EAAWE,GAAeqW,KAAMvW,EAAUnD,MAC5DA,aAAiB4Z,IAAY,IAC9Bha,EAASI,UACZA,EAAM3D,QAAQ,SAAAvB,UAAOqjB,EAAKrb,IAAIhI,EAAKqI,KAC5BuW,QAEJrgB,EAAU2G,UACN0Z,KAAK0E,SAASpe,EAAOmD,OAEzB5J,EAAYyG,UAGR0Z,KAFP1Z,EAAQwF,GAAMgX,YAAY,EAAGxc,UAKxB0Z,OAAS1Z,EAAQkD,GAAewW,KAAM1Z,EAAOmD,GAAYuW,QAGjE2E,YAAA,qBAAY5O,EAAe6O,EAAeC,EAAkBC,YAAhD/O,IAAAA,GAAS,YAAM6O,IAAAA,GAAS,YAAMC,IAAAA,GAAY,YAAMC,IAAAA,GAAoBpW,WAC3E3K,EAAI,GACPuC,EAAQ0Z,KAAK3J,OACP/P,GACFA,EAAMkB,QAAUsd,IACfxe,aAAiBwF,GACpB8Y,GAAU7gB,EAAE8G,KAAKvE,IAEjBue,GAAa9gB,EAAE8G,KAAKvE,GACpByP,GAAUhS,EAAE8G,WAAF9G,EAAUuC,EAAMqe,aAAY,EAAMC,EAAQC,MAGtDve,EAAQA,EAAMO,aAER9C,KAGRghB,QAAA,iBAAQhF,WACHiF,EAAahF,KAAK2E,YAAY,EAAG,EAAG,GACvCljB,EAAIujB,EAAWljB,OACVL,QACDujB,EAAWvjB,GAAGmE,KAAKma,KAAOA,SACtBiF,EAAWvjB,MAKrB0F,OAAA,gBAAOb,UACF3G,EAAU2G,GACN0Z,KAAKiF,YAAY3e,GAErBzG,EAAYyG,GACR0Z,KAAKkF,aAAa5e,IAE1BQ,GAAsBkZ,KAAM1Z,GACxBA,IAAU0Z,KAAKhW,eACbA,QAAUgW,KAAKsE,OAEdjd,GAAS2Y,UAGjBrY,UAAA,mBAAUA,EAAWpD,UACf6b,UAAUte,aAGVsiB,SAAW,GACXpE,KAAK5Z,KAAO4Z,KAAKxX,WAChBhB,OAASzE,GAAc4H,GAAQrG,MAAmB,EAAX0b,KAAKxX,IAAUb,EAAYqY,KAAKxX,KAAOwX,KAAKvX,gBAAkBd,IAAcqY,KAAKxX,mBAExHb,oBAAUA,EAAWpD,QACtB6f,SAAW,EACTpE,MARCA,KAAK7X,UAWduc,SAAA,kBAAS7T,EAAOpH,eACVqH,OAAOD,GAASlH,GAAeqW,KAAMvW,GACnCuW,QAGRiF,YAAA,qBAAYpU,iBACJmP,KAAKlP,OAAOD,GACZmP,QAGRmF,SAAA,kBAAS1b,EAAU4S,EAAU7Q,OACxB9E,EAAIoF,GAAMgX,YAAY,EAAGzG,GAAYhb,EAAYmK,UACrD9E,EAAEqU,KAAO,eACJoJ,UAAY,EACV3a,GAAewW,KAAMtZ,EAAGiD,GAAeqW,KAAMvW,OAGrD2b,YAAA,qBAAY3b,OACPnD,EAAQ0Z,KAAK3J,WACjB5M,EAAWE,GAAeqW,KAAMvW,GACzBnD,GACFA,EAAMkB,SAAWiC,GAA2B,YAAfnD,EAAMyU,MACtC/T,GAAkBV,GAEnBA,EAAQA,EAAMO,SAIhBqe,aAAA,sBAAa3jB,EAAS8jB,EAAOC,WACxBV,EAAS5E,KAAKuF,YAAYhkB,EAAS+jB,GACtC7jB,EAAImjB,EAAO9iB,OACLL,KACL+jB,KAAsBZ,EAAOnjB,IAAOmjB,EAAOnjB,GAAGwP,KAAK1P,EAAS8jB,UAEvDrF,QAGRuF,YAAA,qBAAYhkB,EAAS+jB,WAKnBG,EAJG1hB,EAAI,GACP2hB,EAAgBvjB,GAAQZ,GACxB+E,EAAQ0Z,KAAK3J,OACbsP,EAAe7lB,EAAUwlB,GAEnBhf,GACFA,aAAiBwF,GAChBvI,GAAkB+C,EAAMsf,SAAUF,KAAmBC,IAAiBH,IAAsBlf,EAAM5B,UAAY4B,EAAMkC,MAASlC,EAAMya,WAAW,IAAMuE,GAAchf,EAAMya,WAAWza,EAAMmC,iBAAmB6c,GAAcA,GAAchf,EAAMub,aACjP9d,EAAE8G,KAAKvE,IAEGmf,EAAWnf,EAAMif,YAAYG,EAAeJ,IAAaxjB,QACpEiC,EAAE8G,WAAF9G,EAAU0hB,GAEXnf,EAAQA,EAAMO,aAER9C,KAUR8hB,QAAA,iBAAQpc,EAAU7D,GACjBA,EAAOA,GAAQ,OAIdkgB,EAHGC,EAAK/F,KACR/E,EAAUtR,GAAeoc,EAAItc,GAC3BoC,EAAqDjG,EAArDiG,QAASma,EAA4CpgB,EAA5CogB,QAASC,EAAmCrgB,EAAnCqgB,cAAene,EAAoBlC,EAApBkC,gBAEnCjE,EAAQiI,GAAM4W,GAAGqD,EAAI5gB,GAAa,CACjCgI,KAAMvH,EAAKuH,MAAQ,OACnB1C,MAAM,EACN3C,iBAAiB,EACjBxD,KAAM2W,EACNzB,UAAW,OACXpR,SAAUxC,EAAKwC,UAAavF,KAAK+F,KAAKqS,GAAYpP,GAAW,SAAUA,EAAWA,EAAQvH,KAAOyhB,EAAG9c,QAAU8c,EAAGlc,cAAiBf,EAClIkd,QAAS,sBACRD,EAAGtE,SACEqE,EAAS,KACT1d,EAAWxC,EAAKwC,UAAYvF,KAAK+F,KAAKqS,GAAYpP,GAAW,SAAUA,EAAWA,EAAQvH,KAAOyhB,EAAG9c,QAAU8c,EAAGlc,aACpHhG,EAAM0D,OAASa,GAAa0C,GAAajH,EAAOuE,EAAU,EAAG,GAAGjE,OAAON,EAAMoF,OAAO,GAAM,GAC3F6c,EAAU,EAEXE,GAAWA,EAAQ3Q,MAAMxR,EAAOoiB,GAAiB,MAEhDrgB,WACGkC,EAAkBjE,EAAMM,OAAO,GAAKN,KAG5CqiB,YAAA,qBAAYC,EAAcC,EAAYxgB,UAC9Boa,KAAK6F,QAAQO,EAAYjhB,GAAa,CAAC0G,QAAQ,CAACvH,KAAKqF,GAAeqW,KAAMmG,KAAiBvgB,OAGnGyV,OAAA,yBACQ2E,KAAKhW,WAGbqc,UAAA,mBAAUC,mBAAAA,IAAAA,EAAYtG,KAAK/W,OACnBwH,GAAqBuP,KAAMrW,GAAeqW,KAAMsG,OAGxDC,cAAA,uBAAcC,mBAAAA,IAAAA,EAAaxG,KAAK/W,OACxBwH,GAAqBuP,KAAMrW,GAAeqW,KAAMwG,GAAa,MAGrEC,aAAA,sBAAa7mB,UACLwgB,UAAUte,OAASke,KAAKmB,KAAKvhB,GAAO,GAAQogB,KAAKuG,cAAcvG,KAAK/W,MAAQH,MAGpF4d,cAAA,uBAAc5X,EAAQ6X,EAAc7B,YAAAA,IAAAA,EAAmB,WAGrD5f,EAFGoB,EAAQ0Z,KAAK3J,OAChBvF,EAASkP,KAAKlP,OAERxK,GACFA,EAAMkB,QAAUsd,IACnBxe,EAAMkB,QAAUsH,EAChBxI,EAAMgB,MAAQwH,GAEfxI,EAAQA,EAAMO,SAEX8f,MACEzhB,KAAK4L,EACLA,EAAO5L,IAAM4f,IAChBhU,EAAO5L,IAAM4J,UAITzH,GAAS2Y,SAGjB4B,WAAA,oBAAWgF,OACNtgB,EAAQ0Z,KAAK3J,gBACZyN,MAAQ,EACNxd,GACNA,EAAMsb,WAAWgF,GACjBtgB,EAAQA,EAAMO,yBAEF+a,qBAAWgF,MAGzBC,MAAA,eAAMC,YAAAA,IAAAA,GAAgB,WAEpB/f,EADGT,EAAQ0Z,KAAK3J,OAEV/P,GACNS,EAAOT,EAAMO,WACRM,OAAOb,GACZA,EAAQS,cAEJX,MAAQ4Z,KAAK/W,MAAQ+W,KAAK7X,OAAS6X,KAAKM,OAAS,GACtDwG,IAAkB9G,KAAKlP,OAAS,IACzBzJ,GAAS2Y,SAGjBvX,cAAA,uBAAc7I,OAKZ+G,EAAM1D,EAAO4C,EAJVwI,EAAM,EACT6T,EAAOlC,KACP1Z,EAAQ4b,EAAKoC,MACbb,EAAY/U,KAET0R,UAAUte,cACNogB,EAAKrY,WAAWqY,EAAKja,QAAU,EAAIia,EAAK9Z,WAAa8Z,EAAKzZ,kBAAoByZ,EAAKX,YAAc3hB,EAAQA,OAE7GsiB,EAAKza,OAAQ,KAChB5B,EAASqc,EAAKrc,OACPS,GACNK,EAAOL,EAAMM,MACbN,EAAMmB,QAAUnB,EAAMmC,gBAEVgb,GADZxgB,EAAQqD,EAAMkB,SACW0a,EAAKpY,OAASxD,EAAMkC,MAAQ0Z,EAAK4B,OACzD5B,EAAK4B,MAAQ,EACbta,GAAe0Y,EAAM5b,EAAOrD,EAAQqD,EAAMsD,OAAQ,GAAGka,MAAQ,GAE7DL,EAAYxgB,EAETA,EAAQ,GAAKqD,EAAMkC,MACtB6F,GAAOpL,IACD4C,IAAWqc,EAAK9b,KAASP,GAAUA,EAAOmD,qBAC/CkZ,EAAK1a,QAAUvE,EAAQif,EAAK1Z,IAC5B0Z,EAAKjZ,OAAShG,EACdif,EAAK/Z,QAAUlF,GAEhBif,EAAKwE,eAAezjB,GAAO,GAAQ,UACnCwgB,EAAY,GAEbnd,EAAMgB,KAAO+G,GAAO/H,EAAMkC,MAAQ6F,EAAM/H,EAAMgB,MAC9ChB,EAAQK,EAETmE,GAAaoX,EAAOA,IAASpc,GAAmBoc,EAAKjZ,MAAQoF,EAAO6T,EAAKjZ,MAAQoF,EAAK,EAAG,GACzF6T,EAAKza,OAAS,SAERya,EAAKxZ,gBAGNqe,WAAP,oBAAkBziB,MACbwB,EAAgB0C,MACnBpE,GAAgB0B,EAAiBwC,GAAwBhE,EAAMwB,IAC/D4E,EAAqBC,GAAQC,OAE1BD,GAAQC,OAAS+P,GAAc,CAClCA,IAAgB1B,EAAQC,WAAa,QACjC5S,EAAQR,EAAgBuQ,YACvB/P,IAAUA,EAAMkC,MAASyQ,EAAQC,WAAavO,GAAQoO,WAAWjX,OAAS,EAAG,MAC1EwE,IAAUA,EAAMkC,KACtBlC,EAAQA,EAAMO,MAEfP,GAASqE,GAAQ6T,qBA1fS0B,IAigB9B/a,GAAakG,GAASoH,UAAW,CAACqR,MAAM,EAAGK,UAAU,EAAGC,SAAS,IA8GjD,SAAf4C,GAAgBpmB,EAAUgF,EAAM/B,EAAOuM,EAAO1O,EAAQH,OACjD0lB,EAAQC,EAAIC,EAAU1lB,KACtB+Q,GAAS5R,KAAwL,KAA1KqmB,EAAS,IAAIzU,GAAS5R,IAAa4Q,KAAK9P,EAAQulB,EAAOjV,QAAUpM,EAAKhF,GAdnF,SAAfwmB,aAAgBxhB,EAAMwK,EAAO1O,EAAQH,EAASsC,MAC7ChE,EAAY+F,KAAUA,EAAOyhB,GAAmBzhB,EAAM/B,EAAOuM,EAAO1O,EAAQH,KACvEvB,EAAU4F,IAAUA,EAAK0hB,OAAS1hB,EAAKyG,UAAanG,EAASN,IAASqU,EAAcrU,UACjFjG,EAAUiG,GAAQyhB,GAAmBzhB,EAAM/B,EAAOuM,EAAO1O,EAAQH,GAAWqE,MAGnFV,EADGQ,EAAO,OAENR,KAAKU,EACTF,EAAKR,GAAKmiB,GAAmBzhB,EAAKV,GAAIrB,EAAOuM,EAAO1O,EAAQH,UAEtDmE,EAIsG0hB,CAAaxhB,EAAKhF,GAAWwP,EAAO1O,EAAQH,EAASsC,GAAQA,EAAOuM,EAAO7O,KACvLsC,EAAM2G,IAAM0c,EAAK,IAAIrU,GAAUhP,EAAM2G,IAAK9I,EAAQd,EAAU,EAAG,EAAGqmB,EAAO9iB,OAAQ8iB,EAAQ,EAAGA,EAAOM,UAC/F1jB,IAAUmU,OACbmP,EAAWtjB,EAAMwc,UAAUxc,EAAM+hB,SAASjiB,QAAQjC,IAClDD,EAAIwlB,EAAOxV,OAAO3P,OACXL,KACN0lB,EAASF,EAAOxV,OAAOhQ,IAAMylB,SAIzBD,EAsKS,SAAjBO,GAAkB9U,EAAMtR,EAAKqmB,EAAUC,OAErCxiB,EAAGnB,EADAoJ,EAAO/L,EAAI+L,MAAQua,GAAY,kBAE/BxhB,EAAS9E,GACZ2C,EAAI0jB,EAAS/U,KAAU+U,EAAS/U,GAAQ,IAExCtR,EAAIuB,QAAQ,SAAC/C,EAAO6B,UAAMsC,EAAE8G,KAAK,CAACnE,EAAGjF,GAAKL,EAAIU,OAAS,GAAK,IAAKO,EAAGzC,EAAO+nB,EAAGxa,eAEzEjI,KAAK9D,EACT2C,EAAI0jB,EAASviB,KAAOuiB,EAASviB,GAAK,IAC5B,SAANA,GAAgBnB,EAAE8G,KAAK,CAACnE,EAAGrD,WAAWqP,GAAOrQ,EAAGjB,EAAI8D,GAAIyiB,EAAGxa,IArR/D,IAuGCqY,GACAoC,GAxDAhW,GAAgB,SAAhBA,cAAyBlQ,EAAQgR,EAAMzP,EAAOG,EAAKgN,EAAO7O,EAASuQ,EAAU+V,EAAcC,EAAWC,GACrGloB,EAAYuD,KAASA,EAAMA,EAAIgN,GAAS,EAAG1O,EAAQH,QAIlD2lB,EAHGc,EAAetmB,EAAOgR,GACzBuV,EAAyB,QAAVhlB,EAAmBA,EAASpD,EAAYmoB,GAAgCF,EAAYpmB,EAAQgR,EAAK/O,QAAQ,SAAW9D,EAAY6B,EAAO,MAAQgR,EAAKpP,OAAO,KAAQoP,EAAO,MAAQA,EAAKpP,OAAO,IAAIwkB,GAAapmB,EAAOgR,KAA9JsV,EACvEE,EAAUroB,EAAYmoB,GAA+BF,EAAYK,GAAuBC,GAAlDC,MAEnC1oB,EAAUyD,MACRA,EAAIO,QAAQ,aAChBP,EAAMiN,GAAejN,IAEA,MAAlBA,EAAID,OAAO,OACd+jB,EAAKlkB,GAAeilB,EAAa7kB,IAAQ4I,GAAQic,IAAgB,KAChD,IAAPf,IACT9jB,EAAM8jB,MAIJa,GAAYE,IAAgB7kB,GAAOwkB,UAClCna,MAAMwa,EAAc7kB,IAAgB,KAARA,GAMhC4kB,GAAkBtV,KAAQhR,GAAWf,EAAe+R,EAAMtP,GAxE7B,SAA7BklB,2BAAsC5mB,EAAQgR,EAAMzP,EAAOG,EAAK8kB,EAAQL,EAAcC,OAIvFtT,EAAQ+T,EAAW7T,EAAO8T,EAAQC,EAAOC,EAAUC,EAAW5kB,EAH3DmjB,EAAK,IAAIrU,GAAUmN,KAAKxV,IAAK9I,EAAQgR,EAAM,EAAG,EAAGkW,GAAsB,KAAMV,GAChF9X,EAAQ,EACRyY,EAAa,MAEd3B,EAAGnY,EAAI9L,EACPikB,EAAGS,EAAIvkB,EACPH,GAAS,IAEJ0lB,IADLvlB,GAAO,IACeO,QAAQ,cAC7BP,EAAMiN,GAAejN,IAElBykB,IAEHA,EADA9jB,EAAI,CAACd,EAAOG,GACI1B,EAAQgR,GACxBzP,EAAQc,EAAE,GACVX,EAAMW,EAAE,IAETwkB,EAAYtlB,EAAM6B,MAAMuV,KAAyB,GACzC7F,EAAS6F,GAAqBnO,KAAK9I,IAC1ColB,EAAShU,EAAO,GAChBiU,EAAQrlB,EAAI4S,UAAU5F,EAAOoE,EAAOpE,OAChCsE,EACHA,GAASA,EAAQ,GAAK,EACS,UAArB+T,EAAMnlB,QAAQ,KACxBoR,EAAQ,GAEL8T,IAAWD,EAAUM,OACxBH,EAAWrlB,WAAWklB,EAAUM,EAAW,KAAO,EAElD3B,EAAG1c,IAAM,CACR3D,MAAOqgB,EAAG1c,IACVtF,EAAIujB,GAAwB,IAAfI,EAAoBJ,EAAQ,IACzClY,EAAGmY,EACHxU,EAAwB,MAArBsU,EAAOrlB,OAAO,GAAaH,GAAe0lB,EAAUF,GAAUE,EAAWrlB,WAAWmlB,GAAUE,EACjGI,EAAIpU,GAASA,EAAQ,EAAK7R,KAAKC,MAAQ,GAExCsN,EAAQiK,GAAqBrF,kBAG/BkS,EAAGhT,EAAK9D,EAAQhN,EAAItB,OAAUsB,EAAI4S,UAAU5F,EAAOhN,EAAItB,QAAU,GACjEolB,EAAG6B,GAAKjB,GACJxN,GAAQrF,KAAK7R,IAAQulB,KACxBzB,EAAGS,EAAI,QAEHnd,IAAM0c,GA4BwBtL,KAAKoE,KAAMte,EAAQgR,EAAMuV,EAAa7kB,EAAK8kB,EAAQL,GAAgB5O,EAAQ4O,aAAcC,KAN1HZ,EAAK,IAAIrU,GAAUmN,KAAKxV,IAAK9I,EAAQgR,GAAOuV,GAAe,EAAG7kB,GAAO6kB,GAAe,GAA6B,kBAAlBD,EAA8BgB,GAAiBC,GAAc,EAAGf,GAC/JJ,IAAcZ,EAAG6B,GAAKjB,GACtBhW,GAAYoV,EAAGpV,SAASA,EAAUkO,KAAMte,GAChCse,KAAKxV,IAAM0c,IAmCtB3c,GAAa,SAAbA,WAAc1G,EAAOS,EAAMgG,OAWzB4e,EAAWznB,EAAGyD,EAAGgiB,EAAIxlB,EAAQynB,EAAaC,EAAQxnB,EAASqlB,EAAQE,EAAU/W,EAAOiZ,EAAaC,EAV9F1jB,EAAO/B,EAAM+B,KACduH,EAAiIvH,EAAjIuH,KAAMtB,EAA2HjG,EAA3HiG,QAAS/D,EAAkHlC,EAAlHkC,gBAAiB2C,EAAiG7E,EAAjG6E,KAAM8Z,EAA2F3e,EAA3F2e,SAAUgF,EAAiF3jB,EAAjF2jB,eAAgB9M,EAAiE7W,EAAjE6W,cAAe7Q,EAAkDhG,EAAlDgG,aAAc0K,EAAoC1Q,EAApC0Q,SAAUvQ,EAA0BH,EAA1BG,UAAWgC,EAAenC,EAAfmC,WACpHmD,EAAMrH,EAAM0D,KACZiiB,EAAc3lB,EAAMc,SACpBpD,EAAUsC,EAAM+hB,SAChB/f,EAAShC,EAAMgC,OAEf4jB,EAAe5jB,GAA0B,WAAhBA,EAAOkV,KAAqBlV,EAAOD,KAAKrE,QAAUA,EAC3EmoB,EAAsC,SAArB7lB,EAAM8lB,aAA2B/R,EAClDmO,EAAKliB,EAAMsF,aAEZ4c,GAAQhgB,GAAcoH,IAAUA,EAAO,QACvCtJ,EAAM2S,MAAQpJ,GAAWD,EAAMoM,EAAUpM,MACzCtJ,EAAM4S,OAASH,EAAWrH,GAAY7B,IAAwB,IAAbkJ,EAAoBnJ,EAAOmJ,EAAUiD,EAAUpM,OAAS,EACrGmJ,GAAYzS,EAAM0S,QAAU1S,EAAMoE,UACrCqO,EAAWzS,EAAM4S,OACjB5S,EAAM4S,OAAS5S,EAAM2S,MACrB3S,EAAM2S,MAAQF,GAEfzS,EAAM+lB,OAAS7D,KAAQngB,EAAKgG,cACvBma,GAAOhgB,IAAcH,EAAKod,QAAU,IAExCqG,GADAznB,EAAUL,EAAQ,GAAKW,GAAUX,EAAQ,IAAIK,QAAU,IAC9BgE,EAAKhE,EAAQ8Q,MACtCwW,EAAY1jB,GAAeI,EAAM+M,IAC7B6W,IACHA,EAAYjgB,OAAS,GAAKigB,EAAYtY,SAAS,GAC9C5M,EAAO,GAAKsH,GAAgB9D,IAAoBC,EAAcyhB,EAAYrlB,QAAQ,GAAG,GAAQqlB,EAAY5hB,OAAOgE,GAAgBV,EAAMrD,GAAsB0S,IAE7JiP,EAAYtlB,MAAQ,GAEjB2H,MACH7E,GAAkBnD,EAAMc,SAAWmH,GAAMmU,IAAI1e,EAAS4D,GAAa,CAAC4V,KAAM,UAAWvB,WAAW,EAAO3T,OAAQA,EAAQiC,iBAAiB,EAAM2C,MAAO+e,GAAevpB,EAAYwK,GAAOoB,QAAS,KAAM4N,MAAO,EAAG8K,SAAUA,EAAUgF,eAAgBA,EAAgB9M,cAAeA,EAAeuG,QAAS,GAAInX,KAC/ShI,EAAMc,SAASyB,IAAM,EACrBvC,EAAMc,SAASqc,KAAOnd,EACrBS,EAAO,IAAMG,IAAgBqD,IAAoBC,IAAiBlE,EAAMc,SAASiD,OAAOC,IACrFC,GACCoD,GAAO5G,GAAQ,GAAKgG,GAAS,cAChChG,IAAST,EAAM0F,OAASjF,SAIpB,GAAIsH,GAAgBV,IAErBse,KACJllB,IAASwD,GAAkB,GAC3B5C,EAAIC,GAAa,CAChBqU,WAAW,EACXuB,KAAM,cACNtQ,KAAM3C,IAAoB0hB,GAAevpB,EAAYwK,GACrD3C,gBAAiBA,EACjBkb,QAAS,EACTnd,OAAQA,GACNqjB,GACHG,IAAgBnkB,EAAEtD,EAAQ8Q,MAAQ2W,GAClCriB,GAAkBnD,EAAMc,SAAWmH,GAAMmU,IAAI1e,EAAS2D,IACtDrB,EAAMc,SAASyB,IAAM,EACrBvC,EAAMc,SAASqc,KAAOnd,EACrBS,EAAO,IAAOG,EAAaZ,EAAMc,SAASiD,OAAOC,IAAuBhE,EAAMc,SAASR,QAAQ,GAAG,IACnGN,EAAM0F,OAASjF,EACVwD,GAEE,IAAKxD,cADXiG,WAAW1G,EAAMc,SAAUmE,EAAUA,OAMxCjF,EAAM2G,IAAM3G,EAAMgmB,SAAW,EAC7Bpf,EAAQS,GAAOjL,EAAYwK,IAAWA,IAASS,EAC1CzJ,EAAI,EAAGA,EAAIF,EAAQO,OAAQL,IAAK,IAEpC2nB,GADA1nB,EAASH,EAAQE,IACDE,OAASL,GAASC,GAASE,GAAGE,MAC9CkC,EAAMwc,UAAU5e,GAAK0lB,EAAW,GAChCljB,GAAYmlB,EAAOrJ,KAAOjc,GAAYhC,QAAU8B,KAChDwM,EAAQqZ,IAAgBloB,EAAUE,EAAIgoB,EAAY9lB,QAAQjC,GACtDE,IAA0G,KAA9FqlB,EAAS,IAAIrlB,GAAW4P,KAAK9P,EAAQ2nB,GAAeH,EAAWrlB,EAAOuM,EAAOqZ,KAC5F5lB,EAAM2G,IAAM0c,EAAK,IAAIrU,GAAUhP,EAAM2G,IAAK9I,EAAQulB,EAAO9lB,KAAM,EAAG,EAAG8lB,EAAO9iB,OAAQ8iB,EAAQ,EAAGA,EAAOM,UACtGN,EAAOxV,OAAO9O,QAAQ,SAAAxB,GAASgmB,EAAShmB,GAAQ+lB,IAChDD,EAAOM,WAAa4B,EAAc,KAE9BvnB,GAAWynB,MACVnkB,KAAKgkB,EACL1W,GAAStN,KAAO+hB,EAASD,GAAa9hB,EAAGgkB,EAAWrlB,EAAOuM,EAAO1O,EAAQ+nB,IAC7ExC,EAAOM,WAAa4B,EAAc,GAElChC,EAASjiB,GAAKgiB,EAAKtV,GAAcgK,KAAK/X,EAAOnC,EAAQwD,EAAG,MAAOgkB,EAAUhkB,GAAIkL,EAAOqZ,EAAa,EAAG7jB,EAAKiiB,cAI5GhkB,EAAMimB,KAAOjmB,EAAMimB,IAAIroB,IAAMoC,EAAMoN,KAAKvP,EAAQmC,EAAMimB,IAAIroB,IACtDioB,GAAiB7lB,EAAM2G,MAC1Bgb,GAAoB3hB,EACpBiC,EAAgBof,aAAaxjB,EAAQylB,EAAUtjB,EAAMkd,WAAWzc,IAChEglB,GAAezlB,EAAMgC,OACrB2f,GAAoB,GAErB3hB,EAAM2G,KAAOC,IAASxG,GAAYmlB,EAAOrJ,IAAM,GAEhDoJ,GAAeY,GAA0BlmB,GACzCA,EAAMmmB,SAAWnmB,EAAMmmB,QAAQnmB,GAEhCA,EAAMke,UAAYwC,EAClB1gB,EAAMa,WAAab,EAAMimB,KAAOjmB,EAAM2G,OAAS8e,EAC9CvjB,GAAazB,GAAQ,GAAMyhB,EAAG5hB,OAAOuK,GAAS,GAAM,IAyEtD2Y,GAAqB,SAArBA,mBAAsBznB,EAAOiE,EAAOpC,EAAGC,EAAQH,UAAa1B,EAAYD,GAASA,EAAMgc,KAAK/X,EAAOpC,EAAGC,EAAQH,GAAY5B,EAAUC,KAAWA,EAAM+D,QAAQ,WAAc0M,GAAezQ,GAASA,GACnMqqB,GAAqBrP,GAAiB,4DACtCsP,GAAsB,GACvB3nB,GAAa0nB,GAAqB,kDAAmD,SAAA9oB,UAAQ+oB,GAAoB/oB,GAAQ,QA8B5G2K,8BAEAvK,EAASqE,EAAM6D,EAAU0gB,SACf,iBAAVvkB,IACV6D,EAASrB,SAAWxC,EACpBA,EAAO6D,EACPA,EAAW,UAMXsc,EAAItkB,EAAGiE,EAAMhC,EAAGwB,EAAGklB,EAAWC,EAAaC,mBAJtCH,EAAcvkB,EAAOD,GAAiBC,WACsEA,KAA5GwC,IAAAA,SAAUqR,IAAAA,MAAO3R,IAAAA,gBAAiBkb,IAAAA,QAASxJ,IAAAA,UAAWzT,IAAAA,UAAWX,IAAAA,SAAU4L,IAAAA,cAAesF,IAAAA,SAC/FzQ,EAASD,EAAKC,QAAUC,EACxB4f,GAAiBxf,EAAS3E,IAAY0Y,EAAc1Y,GAAWzB,EAAUyB,EAAQ,IAAO,WAAYqE,GAAS,CAACrE,GAAWY,GAAQZ,QAE7HqkB,SAAWF,EAAc5jB,OAASR,GAASokB,GAAiB3kB,EAAM,eAAiBQ,EAAU,qCAAsC0X,EAAQG,iBAAmB,KAC9JiH,UAAY,KACZsJ,WAAanQ,EACdzT,GAAaid,GAAW5iB,EAAgBgI,IAAahI,EAAgBqZ,GAAQ,IAChF7T,EAAO2kB,EAAK3kB,MACZmgB,EAAKwE,EAAKphB,SAAW,IAAIkC,GAAS,CAAC0P,KAAM,SAAU3V,SAAUA,GAAY,GAAI7D,QAASsE,GAA0B,WAAhBA,EAAOkV,KAAoBlV,EAAOD,KAAKrE,QAAUmkB,KAC9IzU,OACH8U,EAAGlgB,OAASkgB,EAAG3f,8BACf2f,EAAGve,OAAS,EACRwb,GAAW5iB,EAAgBgI,IAAahI,EAAgBqZ,GAAQ,IACnE/V,EAAIgiB,EAAc5jB,OAClBuoB,EAAcrH,GAAW/V,GAAW+V,GAChChjB,EAAUgjB,OACR9d,KAAK8d,GACJiH,GAAmBtmB,QAAQuB,MACRolB,EAAvBA,GAA4C,IACzBplB,GAAK8d,EAAQ9d,QAI9BzD,EAAI,EAAGA,EAAIiC,EAAGjC,KAClBiE,EAAOF,GAAeI,EAAMskB,KACvBlH,QAAU,EACf1M,IAAa5Q,EAAK4Q,SAAWA,GAC7BgU,GAAsB9pB,GAAOkF,EAAM4kB,GACnCF,EAAY1E,EAAcjkB,GAE1BiE,EAAK0C,UAAYif,GAAmBjf,4BAAgB3G,EAAG2oB,EAAW1E,GAClEhgB,EAAK+T,QAAU4N,GAAmB5N,4BAAahY,EAAG2oB,EAAW1E,IAAkB,GAAK6E,EAAK3gB,QACpFoZ,GAAiB,IAANtf,GAAWgC,EAAK+T,UAC1B7P,OAAS6P,EAAQ/T,EAAK+T,QACtBjS,QAAUiS,EACf/T,EAAK+T,MAAQ,GAEdsM,EAAGrD,GAAG0H,EAAW1kB,EAAM2kB,EAAcA,EAAY5oB,EAAG2oB,EAAW1E,GAAiB,GAChFK,EAAGvP,MAAQpB,GAASuK,KAErBoG,EAAG3d,WAAcA,EAAWqR,EAAQ,EAAM8Q,EAAKphB,SAAW,OACpD,GAAIpD,EAAW,CACrBJ,GAAiBR,GAAa4gB,EAAGngB,KAAKR,SAAU,CAAC+H,KAAK,UACtD4Y,EAAGvP,MAAQpJ,GAAWrH,EAAUoH,MAAQvH,EAAKuH,MAAQ,YAEpDpJ,EAAGymB,EAAInoB,EADJiC,EAAO,KAEP4B,EAASH,GACZA,EAAUpD,QAAQ,SAAAiI,UAASmb,EAAGrD,GAAGgD,EAAe9a,EAAO,OACvDmb,EAAG3d,eACG,KAEDlD,KADLQ,EAAO,GACGK,EACH,SAANb,GAAsB,aAANA,GAAoBsiB,GAAetiB,EAAGa,EAAUb,GAAIQ,EAAMK,EAAU2hB,cAEhFxiB,KAAKQ,MACT3B,EAAI2B,EAAKR,GAAG6H,KAAK,SAAChJ,EAAGgL,UAAMhL,EAAE2C,EAAIqI,EAAErI,IAE9BjF,EADL6C,EAAO,EACK7C,EAAIsC,EAAEjC,OAAQL,KAEzBY,EAAI,CAAC8K,MADLqd,EAAKzmB,EAAEtC,IACOkmB,EAAGvf,UAAWoiB,EAAG9jB,GAAKjF,EAAIsC,EAAEtC,EAAI,GAAGiF,EAAI,IAAM,IAAM0B,IAC/DlD,GAAKslB,EAAGnoB,EACV0jB,EAAGrD,GAAGgD,EAAerjB,EAAGiC,GACxBA,GAAQjC,EAAE+F,SAGZ2d,EAAG3d,WAAaA,GAAY2d,EAAGrD,GAAG,GAAI,CAACta,SAAUA,EAAW2d,EAAG3d,cAGjEA,GAAYmiB,EAAKniB,SAAUA,EAAW2d,EAAG3d,mBAGpCe,SAAW,SAGC,IAAdqQ,GAAuB5B,IAC1B4N,6BACA1f,EAAgBof,aAAaQ,GAC7BF,GAAoB,GAErBhc,GAAe3D,4BAAc4D,GAC7B7D,EAAK2b,UAAYgJ,EAAK/I,UACtB5b,EAAK+a,QAAU4J,EAAK5J,QAAO,IACvB7Y,IAAqBM,IAAarC,GAAawkB,EAAK/iB,SAAWzE,GAAc8C,EAAOoD,QAAUhJ,EAAY6H,IAlpEvF,SAAxB2iB,sBAAwBpmB,UAAcA,GAAcA,EAAUmE,KAAOiiB,sBAAsBpmB,EAAUwB,QAkpE8B4kB,6BAA+C,WAAhB5kB,EAAOkV,UAClK5S,QAAUW,IACV3E,OAAOtB,KAAKwL,IAAI,GAAIoL,IAAU,IAEpCzI,GAAiB/G,6BAAqB+G,4DAGvC7M,OAAA,gBAAOwD,EAAWpD,EAAgBC,OAMhCF,EAAM4iB,EAAI1G,EAAW3F,EAAe6I,EAAetN,EAAQmK,EAAOpX,EAAUmN,EALzEqN,EAAW3D,KAAK/W,MACnB2a,EAAO5D,KAAKtX,MACZwC,EAAM8U,KAAKzY,KACXmjB,EAAa/iB,EAAY,EACzB2C,EAAqBsZ,EAAO9a,EAAnBnB,IAAgC+iB,EAAc9G,EAAQjc,EAAYmB,EAAY,EAAInB,KAEvFuD,GAEE,GAAIZ,IAAU0V,KAAK7X,SAAWR,GAAanD,IAAWwb,KAAKtb,UAAYsb,KAAK7X,QAAY6X,KAAKrb,UAAaqb,KAAKzW,OAAS,GAAOmhB,EAAa,IAClJpmB,EAAOgG,EACPnB,EAAW6W,KAAK7W,SACZ6W,KAAK/X,QAAS,IACjB4S,EAAgB3P,EAAM8U,KAAK3X,QACvB2X,KAAK/X,SAAW,GAAKyiB,SACjB1K,KAAKrY,UAA0B,IAAhBkT,EAAsBlT,EAAWpD,EAAgBC,MAExEF,EAAOvB,GAAcuH,EAAQuQ,GACzBvQ,IAAUsZ,GACbpD,EAAYR,KAAK/X,QACjB3D,EAAO4G,KAEPsV,KAAelW,EAAQuQ,KACN2F,IAAclW,EAAQuQ,IACtCvW,EAAO4G,EACPsV,KAEMtV,EAAP5G,IAAeA,EAAO4G,KAEvBkL,EAAS4J,KAAKzJ,OAAsB,EAAZiK,KAEvBlK,EAAW0J,KAAKvJ,OAChBnS,EAAO4G,EAAM5G,GAEdof,EAAgBxb,GAAgB8X,KAAK7X,OAAQ0S,GACzCvW,IAASqf,IAAanf,GAASwb,KAAKtb,qBAElCyD,OAASmC,EACP0V,KAEJQ,IAAckD,IACjBva,GAAY6W,KAAKvJ,QAAUN,GAAmBhN,EAAUiN,IAEpD4J,KAAKpa,KAAKqe,eAAkB7N,GAAW4J,KAAK8D,aAC1CA,MAAQtf,EAAQ,OAChBL,OAAOpB,GAAc8X,EAAgB2F,IAAY,GAAMoB,aAAakC,MAAQ,QAK/E9D,KAAKtb,SAAU,IACf2F,GAAkB2V,KAAM0K,EAAa/iB,EAAYrD,EAAME,EAAOD,EAAgB+F,eAC5EnC,OAAS,EACP6X,QAEJ2D,IAAa3D,KAAK/W,aACd+W,QAEJ9U,IAAQ8U,KAAKzY,YACTyY,KAAK7b,OAAOwD,EAAWpD,EAAgBC,WAI3C2D,OAASmC,OACTrB,MAAQ3E,GAER0b,KAAK5Y,MAAQ4Y,KAAKxX,WACjBpB,KAAO,OACPlD,MAAQ,QAGTqc,MAAQA,GAASjK,GAAY0J,KAAKxJ,OAAOlS,EAAO4G,GACjD8U,KAAK4J,aACHrJ,MAAQA,EAAQ,EAAIA,GAGtBjc,IAASqf,IAAapf,IAAmBic,IAC5CrP,GAAU6O,KAAM,WACZA,KAAK7X,SAAWmC,UACZ0V,SAGTkH,EAAKlH,KAAKxV,IACH0c,GACNA,EAAG3T,EAAEgN,EAAO2G,EAAG/Y,GACf+Y,EAAKA,EAAGrgB,MAERsC,GAAYA,EAAShF,OAAOwD,EAAY,EAAIA,GAAarD,GAAQ8R,GAAUtN,EAAWK,EAAS5B,KAAO4B,EAASqN,MAAMlS,EAAO0b,KAAKzY,MAAOhD,EAAgBC,IAAYwb,KAAKrb,WAAaqb,KAAKzW,OAAS5B,GAEjMqY,KAAK+B,YAAcxd,IACtBmmB,GAAchjB,GAAesY,KAAMrY,EAAWpD,EAAgBC,GAC9D2M,GAAU6O,KAAM,kBAGZ/X,SAAWuY,IAAckD,GAAiB1D,KAAKpa,KAAKse,WAAa3f,GAAkByb,KAAKna,QAAUsL,GAAU6O,KAAM,YAElH1V,IAAU0V,KAAKtX,OAAU4B,GAAU0V,KAAK7X,SAAWmC,IACvDogB,IAAe1K,KAAK+B,WAAara,GAAesY,KAAMrY,EAAW,GAAM,IACtEA,GAAcuD,KAAUZ,IAAU0V,KAAKtX,OAAoB,EAAXsX,KAAKxX,MAAc8B,GAAS0V,KAAKxX,IAAM,IAAOxB,GAAkBgZ,KAAM,GAC/Gzb,GAAoBmmB,IAAe/G,KAAcrZ,GAASqZ,GAAYvN,KAC7EjF,GAAU6O,KAAO1V,IAAUsZ,EAAO,aAAe,qBAAsB,SAClErB,OAAWjY,EAAQsZ,GAA2B,EAAnB5D,KAAKnW,aAAoBmW,KAAKuC,gBArrEvC,SAA3BoI,yBAA4B9mB,EAAO8D,EAAWpD,EAAgBC,OAK5D0iB,EAAI1G,EAAWkD,EAJZkH,EAAY/mB,EAAM0c,MACrBA,EAAQ5Y,EAAY,IAAOA,KAAgB9D,EAAM2D,QAJpB,SAA/BqjB,oCAAiChlB,IAAAA,cAAYA,GAAUA,EAAO2C,KAAO3C,EAAOnB,WAAamB,EAAOie,QAAUje,EAAOwD,UAAY,GAAKwhB,6BAA6BhlB,IAIlGglB,CAA6BhnB,KAAaA,EAAMa,WAAYqF,GAAmBlG,MAAcA,EAAM2E,IAAM,GAAK3E,EAAMuC,IAAIoC,IAAM,KAAOuB,GAAmBlG,IAAY,EAAI,EACnOod,EAAcpd,EAAMwE,QACpBiC,EAAQ,KAEL2W,GAAepd,EAAMoE,UACxBqC,EAAQhB,GAAO,EAAGzF,EAAM6E,MAAOf,GAC/B6Y,EAAYtY,GAAgBoC,EAAO2W,GACnCpd,EAAM0S,OAAsB,EAAZiK,IAAmBD,EAAQ,EAAIA,GAC3CC,IAActY,GAAgBrE,EAAMsE,OAAQ8Y,KAC/C2J,EAAY,EAAIrK,EAChB1c,EAAM+B,KAAKqe,eAAiBpgB,EAAMa,UAAYb,EAAM+d,eAGlDrB,IAAUqK,GAAanmB,GAAcD,GAASX,EAAM0F,SAAWT,IAAcnB,GAAa9D,EAAM0F,OAAS,KACvG1F,EAAMa,UAAY2F,GAAkBxG,EAAO8D,EAAWnD,EAAOD,EAAgB+F,cAGlFoZ,EAAgB7f,EAAM0F,OACtB1F,EAAM0F,OAAS5B,IAAcpD,EAAiBuE,EAAW,GACtCvE,EAAnBA,GAAoCoD,IAAc+b,EAClD7f,EAAM0c,MAAQA,EACd1c,EAAM+lB,QAAUrJ,EAAQ,EAAIA,GAC5B1c,EAAMoF,MAAQ,EACdpF,EAAMsE,OAASmC,EACf4c,EAAKrjB,EAAM2G,IACJ0c,GACNA,EAAG3T,EAAEgN,EAAO2G,EAAG/Y,GACf+Y,EAAKA,EAAGrgB,MAETc,EAAY,GAAKD,GAAe7D,EAAO8D,EAAWpD,GAAgB,GAClEV,EAAMke,YAAcxd,GAAkB4M,GAAUtN,EAAO,YACvDyG,GAASzG,EAAMoE,UAAY1D,GAAkBV,EAAMgC,QAAUsL,GAAUtN,EAAO,aACzE8D,GAAa9D,EAAM6E,OAASf,EAAY,IAAM9D,EAAM0c,QAAUA,IAClEA,GAASvZ,GAAkBnD,EAAO,GAC7BU,GAAmBE,IACvB0M,GAAUtN,EAAQ0c,EAAQ,aAAe,qBAAsB,GAC/D1c,EAAM0e,OAAS1e,EAAM0e,eAGZ1e,EAAM0F,SACjB1F,EAAM0F,OAAS5B,GA8iEfgjB,CAAyB3K,KAAMrY,EAAWpD,EAAgBC,UAkGpDwb,QAGRze,QAAA,0BACQye,KAAK4F,YAGbhE,WAAA,oBAAWgF,UACRA,GAAS5G,KAAKpa,KAAKgG,eAAkBoU,KAAKrb,SAAW,QAClD6F,IAAMwV,KAAK8J,IAAM9J,KAAK+B,UAAY/B,KAAK9b,MAAQ8b,KAAKO,MAAQ,OAC5DF,UAAY,QACZlX,UAAY6W,KAAK7W,SAASyY,WAAWgF,eAC7BhF,qBAAWgF,MAGzBkE,QAAA,iBAAQlqB,EAAUhB,EAAOqD,EAAO8nB,GAC/B9S,GAAiBtN,GAAQuT,YACpB1V,KAAOwX,KAAKsB,WAEhBf,EADGjc,EAAOzB,KAAKyL,IAAI0R,KAAKzY,MAAOyY,KAAK5Z,IAAI6C,MAAQ+W,KAAKxY,QAAUwY,KAAKxX,iBAEhE9D,UAAY6F,GAAWyV,KAAM1b,GAClCic,EAAQP,KAAKxJ,MAAMlS,EAAO0b,KAAKzY,MA1UZ,SAApByjB,kBAAqBnnB,EAAOjD,EAAUhB,EAAOqD,EAAO8nB,EAAiBxK,EAAOjc,OAE1E4iB,EAAI+D,EAAQC,EAAQzpB,EADjB0pB,GAAYtnB,EAAM2G,KAAO3G,EAAMgmB,WAAchmB,EAAMgmB,SAAW,KAAKjpB,OAElEuqB,MACJA,EAAUtnB,EAAMgmB,SAASjpB,GAAY,GACrCsqB,EAASrnB,EAAMwc,UACf5e,EAAIoC,EAAM+hB,SAAS9jB,OACZL,KAAK,KACXylB,EAAKgE,EAAOzpB,GAAGb,KACLsmB,EAAG/Y,GAAK+Y,EAAG/Y,EAAE3D,QACtB0c,EAAKA,EAAG/Y,EAAE3D,IACH0c,GAAMA,EAAGhiB,IAAMtE,GAAYsmB,EAAG6B,KAAOnoB,GAC3CsmB,EAAKA,EAAGrgB,UAGLqgB,SAEJU,GAAsB,EACtB/jB,EAAM+B,KAAKhF,GAAY,MACvB2J,GAAW1G,EAAOS,GAClBsjB,GAAsB,EACf,EAERuD,EAAQtgB,KAAKqc,OAGfzlB,EAAI0pB,EAAQrpB,OACLL,MAENylB,GADA+D,EAASE,EAAQ1pB,IACL+I,KAAOygB,GAChB1a,GAAKtN,GAAmB,IAAVA,GAAiB8nB,EAA0B7D,EAAG3W,GAAKtN,GAAS,GAAKsd,EAAQ2G,EAAGhT,EAAzCjR,EACpDikB,EAAGhT,EAAItU,EAAQsnB,EAAG3W,EAClB0a,EAAOtD,IAAMsD,EAAOtD,EAAI/kB,GAAOhD,GAASoM,GAAQif,EAAOtD,IACvDsD,EAAOlc,IAAMkc,EAAOlc,EAAImY,EAAG3W,EAAIvE,GAAQif,EAAOlc,IAkT1Cic,CAAkBhL,KAAMpf,EAAUhB,EAAOqD,EAAO8nB,EAAiBxK,EAAOjc,GACpE0b,KAAK8K,QAAQlqB,EAAUhB,EAAOqD,EAAO8nB,IAG9ChiB,GAAeiX,KAAM,QAChBna,QAAUQ,GAAmB2Z,KAAK5Z,IAAK4Z,KAAM,SAAU,QAASA,KAAK5Z,IAAI0D,MAAQ,SAAW,GAC1FkW,KAAK7b,OAAO,OAGpB8M,KAAA,cAAK1P,EAASqE,eAAAA,IAAAA,EAAO,SACfrE,GAAaqE,GAAiB,QAATA,eACpB1B,MAAQ8b,KAAKxV,IAAM,EACjBwV,KAAKna,OAASkL,GAAWiP,MAAQA,QAErCA,KAAK7W,SAAU,KACdya,EAAO5D,KAAK7W,SAASV,4BACpBU,SAAS+b,aAAa3jB,EAASqE,EAAM4f,KAA0D,IAArCA,GAAkB5f,KAAK4T,WAAoBnD,QAAUtF,GAAWiP,WAC1Hna,QAAU+d,IAAS5D,KAAK7W,SAASV,iBAAmBqC,GAAakV,KAAMA,KAAKzY,KAAOyY,KAAK7W,SAAST,MAAQkb,EAAM,EAAG,GAChH5D,SAMPoL,EAAkBC,EAAWC,EAAmBjG,EAAOngB,EAAGgiB,EAAIzlB,EAJ3DikB,EAAgB1F,KAAK4F,SACxB2F,EAAiBhqB,EAAUY,GAAQZ,GAAWmkB,EAC9C8F,EAAkBxL,KAAKK,UACvBoL,EAAUzL,KAAKxV,SAEV5E,GAAiB,QAATA,IAh4EA,SAAf8lB,aAAgBC,EAAIC,WACfnqB,EAAIkqB,EAAG7pB,OACVgD,EAAQrD,IAAMmqB,EAAG9pB,OACXgD,GAASrD,KAAOkqB,EAAGlqB,KAAOmqB,EAAGnqB,YAC7BA,EAAI,EA43EsBiqB,CAAahG,EAAe6F,SACnD,QAAT3lB,IAAmBoa,KAAKxV,IAAM,GACvBuG,GAAWiP,UAEnBoL,EAAmBpL,KAAK8J,IAAM9J,KAAK8J,KAAO,GAC7B,QAATlkB,IACCjG,EAAUiG,KACbV,EAAI,GACJ3C,GAAaqD,EAAM,SAAAzE,UAAQ+D,EAAE/D,GAAQ,IACrCyE,EAAOV,GAERU,EAnVkB,SAApBimB,kBAAqBtqB,EAASqE,OAG5BF,EAAMR,EAAGzD,EAAG4Q,EAFTzQ,EAAUL,EAAQ,GAAKW,GAAUX,EAAQ,IAAIK,QAAU,EAC1DkqB,EAAmBlqB,GAAWA,EAAQyQ,YAElCyZ,SACGlmB,MAGHV,KADLQ,EAAOlF,GAAO,GAAIoF,GACRkmB,KACL5mB,KAAKQ,MAERjE,GADA4Q,EAAUyZ,EAAgB5mB,GAAGxC,MAAM,MACvBZ,OACNL,KACLiE,EAAK2M,EAAQ5Q,IAAMiE,EAAKR,UAKpBQ,EAiUCmmB,CAAkBnG,EAAe9f,IAEzCnE,EAAIikB,EAAc5jB,OACXL,SACD8pB,EAAe5nB,QAAQ+hB,EAAcjkB,QAUpCyD,KATLmmB,EAAYG,EAAgB/pB,GACf,QAATmE,GACHwlB,EAAiB3pB,GAAKmE,EACtByf,EAAQgG,EACRC,EAAoB,KAEpBA,EAAoBF,EAAiB3pB,GAAK2pB,EAAiB3pB,IAAM,GACjE4jB,EAAQzf,GAECyf,GACT6B,EAAKmE,GAAaA,EAAUnmB,MAErB,SAAUgiB,EAAG/Y,IAAuB,IAAjB+Y,EAAG/Y,EAAE8C,KAAK/L,IAClC4B,GAAsBkZ,KAAMkH,EAAI,cAE1BmE,EAAUnmB,IAEQ,QAAtBomB,IACHA,EAAkBpmB,GAAK,eAKtBR,WAAasb,KAAKxV,KAAOihB,GAAW1a,GAAWiP,MAC7CA,YAID0C,GAAP,YAAUnhB,EAASqE,EAAnB,UACQ,IAAIkG,MAAMvK,EAASqE,EAD3B,UAIOyH,KAAP,cAAY9L,EAASqE,UACb0F,GAAiB,EAAG8U,kBAGrB0C,YAAP,qBAAmBrJ,EAAO4C,EAAU7Q,EAAQlL,UACpC,IAAIwL,MAAMuQ,EAAU,EAAG,CAACvU,iBAAgB,EAAO2C,MAAK,EAAO+O,WAAU,EAAOC,MAAMA,EAAO0J,WAAW9G,EAAU0P,kBAAkB1P,EAAU+G,iBAAiB5X,EAAQwgB,wBAAwBxgB,EAAQiR,cAAcnc,WAGlNqiB,OAAP,gBAAcphB,EAASqhB,EAAUC,UACzBvX,GAAiB,EAAG8U,kBAGrBH,IAAP,aAAW1e,EAASqE,UACnBA,EAAKwC,SAAW,EAChBxC,EAAKqb,cAAgBrb,EAAKqF,OAAS,GAC5B,IAAIa,MAAMvK,EAASqE,UAGpBsf,aAAP,sBAAoB3jB,EAAS8jB,EAAOC,UAC5Bxf,EAAgBof,aAAa3jB,EAAS8jB,EAAOC,WAvU3BpF,IA2U3B/a,GAAa2G,GAAM2G,UAAW,CAACmT,SAAS,GAAI1hB,MAAM,EAAGS,SAAS,EAAGmlB,IAAI,EAAGE,QAAQ,IAWhFznB,GAAa,sCAAuC,SAAApB,GACnD2K,GAAM3K,GAAQ,eACT4kB,EAAK,IAAI1a,GACZG,EAAS+P,GAAOK,KAAKwE,UAAW,UACjC5U,EAAOvJ,OAAgB,kBAATd,EAA2B,EAAI,EAAG,EAAG,GAC5C4kB,EAAG5kB,GAAMkU,MAAM0Q,EAAIva,MA2BR,SAAnBygB,GAAoBvqB,EAAQd,EAAUhB,UAAU8B,EAAOwqB,aAAatrB,EAAUhB,GAkDxD,SAAtBusB,GAAuBzqB,EAAQd,EAAUhB,EAAOmb,GAC/CA,EAAKqR,KAAK1qB,EAAQd,EAAUma,EAAK+N,EAAElN,KAAKb,EAAKlX,MAAOjE,EAAOmb,EAAKsR,IAAKtR,GAtDvE,IAAIsN,GAAe,SAAfA,aAAgB3mB,EAAQd,EAAUhB,UAAU8B,EAAOd,GAAYhB,GAClEwoB,GAAc,SAAdA,YAAe1mB,EAAQd,EAAUhB,UAAU8B,EAAOd,GAAUhB,IAC5DuoB,GAAuB,SAAvBA,qBAAwBzmB,EAAQd,EAAUhB,EAAOmb,UAASrZ,EAAOd,GAAUma,EAAKgO,GAAInpB,IAEpFwS,GAAa,SAAbA,WAAc1Q,EAAQd,UAAaf,EAAY6B,EAAOd,IAAawnB,GAAcroB,EAAa2B,EAAOd,KAAcc,EAAOwqB,aAAeD,GAAmB5D,IAC5JY,GAAe,SAAfA,aAAgB1I,EAAOxF,UAASA,EAAKkF,IAAIlF,EAAKrU,EAAGqU,EAAK7V,EAAGrC,KAAKC,MAAkC,KAA3BiY,EAAKxK,EAAIwK,EAAK7G,EAAIqM,IAAoB,IAASxF,IACpHiO,GAAiB,SAAjBA,eAAkBzI,EAAOxF,UAASA,EAAKkF,IAAIlF,EAAKrU,EAAGqU,EAAK7V,KAAM6V,EAAKxK,EAAIwK,EAAK7G,EAAIqM,GAAQxF,IACxF6N,GAAuB,SAAvBA,qBAAgCrI,EAAOxF,OAClCmM,EAAKnM,EAAKvQ,IACb+F,EAAI,OACAgQ,GAASxF,EAAKhM,EAClBwB,EAAIwK,EAAKhM,OACH,GAAc,IAAVwR,GAAexF,EAAK4M,EAC9BpX,EAAIwK,EAAK4M,MACH,MACCT,GACN3W,EAAI2W,EAAGhiB,GAAKgiB,EAAG4B,EAAI5B,EAAG4B,EAAE5B,EAAG3W,EAAI2W,EAAGhT,EAAIqM,GAAU1d,KAAKC,MAA8B,KAAvBokB,EAAG3W,EAAI2W,EAAGhT,EAAIqM,IAAkB,KAAUhQ,EACtG2W,EAAKA,EAAGrgB,MAET0J,GAAKwK,EAAK7G,EAEX6G,EAAKkF,IAAIlF,EAAKrU,EAAGqU,EAAK7V,EAAGqL,EAAGwK,IAE7BpJ,GAAoB,SAApBA,kBAA6B4O,EAAOxF,WAC/BmM,EAAKnM,EAAKvQ,IACP0c,GACNA,EAAG3T,EAAEgN,EAAO2G,EAAG/Y,GACf+Y,EAAKA,EAAGrgB,OAGVkL,GAAqB,SAArBA,mBAA8BD,EAAUjO,EAAOnC,EAAQd,WAErDmG,EADGmgB,EAAKlH,KAAKxV,IAEP0c,GACNngB,EAAOmgB,EAAGrgB,MACVqgB,EAAGhiB,IAAMtE,GAAYsmB,EAAGpV,SAASA,EAAUjO,EAAOnC,GAClDwlB,EAAKngB,GAGP8K,GAAoB,SAApBA,kBAA6BjR,WAE3B0rB,EAA0BvlB,EADvBmgB,EAAKlH,KAAKxV,IAEP0c,GACNngB,EAAOmgB,EAAGrgB,MACLqgB,EAAGhiB,IAAMtE,IAAasmB,EAAGqF,IAAOrF,EAAGqF,KAAO3rB,EAC9CkG,GAAsBkZ,KAAMkH,EAAI,OACrBA,EAAGsF,MACdF,EAA2B,GAE5BpF,EAAKngB,SAEEulB,GAKTvC,GAA4B,SAA5BA,0BAA4BlkB,WAE1BkB,EAAM0lB,EAAKC,EAAOC,EADfzF,EAAKrhB,EAAO2E,IAGT0c,GAAI,KACVngB,EAAOmgB,EAAGrgB,MACV4lB,EAAMC,EACCD,GAAOA,EAAIG,GAAK1F,EAAG0F,IACzBH,EAAMA,EAAI5lB,OAENqgB,EAAGtgB,MAAQ6lB,EAAMA,EAAI7lB,MAAQ+lB,GACjCzF,EAAGtgB,MAAMC,MAAQqgB,EAEjBwF,EAAQxF,GAEJA,EAAGrgB,MAAQ4lB,GACfA,EAAI7lB,MAAQsgB,EAEZyF,EAAOzF,EAERA,EAAKngB,EAENlB,EAAO2E,IAAMkiB,GAIF7Z,wBAiBZf,SAAA,kBAASrP,EAAMoB,EAAOnC,QAChB0qB,KAAOpM,KAAKoM,MAAQpM,KAAKC,SACzBA,IAAMkM,QACNrD,EAAIrmB,OACJ4pB,GAAK3qB,OACLmC,MAAQA,iCApBFkD,EAAMrF,EAAQgR,EAAMzP,EAAO4pB,EAAQC,EAAU/R,EAAMmN,EAAQX,QACjE7gB,EAAIhF,OACJ6O,EAAItN,OACJiR,EAAI2Y,OACJ3nB,EAAIwN,OACJa,EAAIuZ,GAAY7D,QAChB9a,EAAI4M,GAAQiF,UACZC,IAAMiI,GAAUG,QAChBuE,GAAKrF,GAAY,QACjB1gB,MAAQE,KAEZA,EAAKH,MAAQoZ,MAgBhBzd,GAAaqY,GAAiB,sOAAuO,SAAAzZ,UAAQwR,GAAexR,GAAQ,IACpSV,GAASssB,SAAWtsB,GAASusB,UAAYlhB,GACzCrL,GAASwsB,aAAexsB,GAASysB,YAAc7hB,GAC/CvF,EAAkB,IAAIuF,GAAS,CAACmX,cAAc,EAAOpd,SAAUmU,EAAWrS,oBAAoB,EAAM6Y,GAAG,OAAQ/W,mBAAmB,IAClIiQ,EAAQ4O,aAAe/S,GAoBV,SAAZqY,GAAY5hB,UAASwN,GAAWxN,IAAS6hB,IAAarZ,IAAI,SAAA0K,UAAKA,MAC9C,SAAjB4O,SACK/oB,EAAOsZ,KAAKC,MACfyP,EAAU,GACiB,EAAxBhpB,EAAOipB,KACVJ,GAAU,kBACVK,GAAO7qB,QAAQ,SAAAuR,OAGbpP,EAAOI,EAAGuoB,EAAUC,EAFjBC,EAAUzZ,EAAEyZ,QACfC,EAAa1Z,EAAE0Z,eAEX1oB,KAAKyoB,GACT7oB,EAAQwH,EAAKuhB,WAAWF,EAAQzoB,IAAIooB,WAC1BG,EAAW,GACjB3oB,IAAU8oB,EAAW1oB,KACxB0oB,EAAW1oB,GAAKJ,EAChB4oB,EAAU,GAGRA,IACHxZ,EAAEtM,SACF6lB,GAAYH,EAAQziB,KAAKqJ,MAG3BiZ,GAAU,oBACVG,EAAQ3qB,QAAQ,SAAAuR,UAAKA,EAAE4Z,QAAQ5Z,KAC/BqZ,GAAiBjpB,EACjB6oB,GAAU,eA/Bb,OAAIK,GAAS,GACZzU,GAAa,GACbqU,GAAc,GACdG,GAAiB,EACjBQ,GAAa,EA+BRC,2BASL5kB,IAAA,aAAIjI,EAAMsB,EAAMnC,GAYV,SAAJme,SAGEjK,EAFG7N,EAAOkR,EACVoW,EAAe/L,EAAK3V,gBAErB5F,GAAQA,IAASub,GAAQvb,EAAKoU,KAAKlQ,KAAKqX,GACxC5hB,IAAU4hB,EAAK3V,SAAWA,GAASjM,IACnCuX,EAAWqK,EACX1N,EAAS/R,EAAK4S,MAAM6M,EAAM9B,WAC1BvgB,EAAY2U,IAAW0N,EAAKgM,GAAGrjB,KAAK2J,GACpCqD,EAAWlR,EACXub,EAAK3V,SAAW0hB,EAChB/L,EAAKiM,YAAa,EACX3Z,EAlBL3U,EAAYsB,KACfb,EAAQmC,EACRA,EAAOtB,EACPA,EAAOtB,OAEJqiB,EAAOlC,YAeXkC,EAAKyK,KAAOlO,GACLtd,IAAStB,EAAc4e,GAAEyD,GAAQ/gB,EAAQ+gB,EAAK/gB,GAAQsd,GAAKA,OAEnE2P,OAAA,gBAAO3rB,OACFkE,EAAOkR,EACXA,EAAW,KACXpV,EAAKud,MACLnI,EAAWlR,MAEZ0nB,UAAA,yBACKtqB,EAAI,eACHgX,KAAKpY,QAAQ,SAAAglB,UAAMA,aAAaqG,QAAWjqB,EAAE8G,WAAF9G,EAAU4jB,EAAE0G,aAAgB1G,aAAa7b,MAAY6b,EAAE9hB,QAA4B,WAAlB8hB,EAAE9hB,OAAOkV,OAAsBhX,EAAE8G,KAAK8c,KAChJ5jB,MAER8iB,MAAA,sBACMqH,GAAGpsB,OAASke,KAAKjF,KAAKjZ,OAAS,MAErCmP,KAAA,cAAKrJ,EAAQimB,iBACRjmB,EAAQ,KACPgd,EAAS5E,KAAKqO,iBACbtT,KAAKpY,QAAQ,SAAA+D,GACF,WAAXA,EAAEqU,OACLrU,EAAEkB,SACFlB,EAAEie,aAAY,GAAM,GAAM,GAAOhiB,QAAQ,SAAAkB,UAAS+gB,EAAO3iB,OAAO2iB,EAAOjhB,QAAQE,GAAQ,QAIzF+gB,EAAO7Q,IAAI,SAAArN,SAAc,CAAC8M,EAAG9M,EAAEqa,WAAW,GAAIra,EAAAA,KAAKqG,KAAK,SAAChJ,EAAGgL,UAAMA,EAAEyE,EAAIzP,EAAEyP,IAAK,EAAA,IAAW7Q,QAAQ,SAAA2rB,UAAKA,EAAE5nB,EAAEkB,OAAOA,UAC7GmT,KAAKpY,QAAQ,SAAAglB,WAAOA,aAAa7b,KAAU6b,EAAE/f,QAAU+f,EAAE/f,OAAOA,UAChEsmB,GAAGvrB,QAAQ,SAAA8b,UAAKA,EAAE7W,EAAQ2mB,UAC1BJ,YAAa,YAEbpT,KAAKpY,QAAQ,SAAAglB,UAAKA,EAAE1W,MAAQ0W,EAAE1W,iBAE/B4V,QACDgH,UACCpsB,EAAI+rB,GAAO1rB,OACRL,KACN+rB,GAAO/rB,GAAGse,KAAOC,KAAKD,IAAMyN,GAAOvrB,OAAOR,EAAG,OAIhDmG,OAAA,gBAAOyJ,QACDJ,KAAKI,GAAU,+BA7ET5O,EAAMnC,QACZiM,SAAWjM,GAASiM,GAASjM,QAC7Bya,KAAO,QACPmT,GAAK,QACLC,YAAa,OACbpO,GAAKgO,KACVtrB,GAAQud,KAAK5W,IAAI3G,UA8Eb+rB,8BAKLplB,IAAA,aAAIwkB,EAAYnrB,EAAMnC,GACrBN,EAAU4tB,KAAgBA,EAAa,CAACN,QAASM,QAGhDa,EAAIvpB,EAAGwpB,EAFJnS,EAAU,IAAIyR,GAAQ,EAAG1tB,GAAS0f,KAAK1f,OAC1CquB,EAAOpS,EAAQqR,WAAa,OAMxB1oB,KAJL2S,IAAa0E,EAAQhQ,WAAagQ,EAAQhQ,SAAWsL,EAAStL,eACzDqiB,SAAS/jB,KAAK0R,GACnB9Z,EAAO8Z,EAAQnT,IAAI,UAAW3G,GAC9B8Z,EAAQoR,QAAUC,EAEP,QAAN1oB,EACHwpB,EAAS,GAETD,EAAKniB,EAAKuhB,WAAWD,EAAW1oB,OAE/BsoB,GAAO7pB,QAAQ4Y,GAAW,GAAKiR,GAAO3iB,KAAK0R,IAC1CoS,EAAKzpB,GAAKupB,EAAGnB,WAAaoB,EAAS,GACpCD,EAAGI,YAAcJ,EAAGI,YAAYxB,IAAkBoB,EAAGK,iBAAiB,SAAUzB,YAInFqB,GAAUjsB,EAAK8Z,GACRyD,SAWRpY,OAAA,gBAAOyJ,QACDJ,KAAKI,GAAU,QAErBJ,KAAA,cAAKrJ,QACCgnB,SAASjsB,QAAQ,SAAAuR,UAAKA,EAAEjD,KAAKrJ,GAAQ,sCAzC/BtH,QACNsuB,SAAW,QACXtuB,MAAQA,EAkDf,IAAMqB,GAAQ,CACbotB,oEAAkBC,2BAAAA,kBACjBA,EAAKrsB,QAAQ,SAAA0O,UAAUD,GAAcC,MAEtClI,2BAASvD,UACD,IAAIyF,GAASzF,IAErB2f,iCAAYhkB,EAAS+jB,UACbxf,EAAgByf,YAAYhkB,EAAS+jB,IAE7C2J,iCAAYvtB,EAAQd,EAAUsuB,EAAMC,GACnCxvB,EAAU+B,KAAYA,EAASS,GAAQT,GAAQ,QAC3C0tB,EAASltB,GAAUR,GAAU,IAAIwQ,IACpCmd,EAASH,EAAOjqB,GAAeL,SACvB,WAATsqB,IAAsBA,EAAO,IACrBxtB,EAAmBd,EAA8IyuB,GAAS7c,GAAS5R,IAAa4R,GAAS5R,GAAUsR,KAAQkd,GAAQ1tB,EAAQd,EAAUsuB,EAAMC,IAA7N,SAACvuB,EAAUsuB,EAAMC,UAAYE,GAAS7c,GAAS5R,IAAa4R,GAAS5R,GAAUsR,KAAQkd,GAAQ1tB,EAAQd,EAAUsuB,EAAMC,KAA5IztB,GAElB4tB,iCAAY5tB,EAAQd,EAAUsuB,MAET,GADpBxtB,EAASS,GAAQT,IACNI,OAAY,KAClBytB,EAAU7tB,EAAOqS,IAAI,SAAArN,UAAKhG,GAAK4uB,YAAY5oB,EAAG9F,EAAUsuB,KAC3DxrB,EAAI6rB,EAAQztB,cACN,SAAAlC,WACF6B,EAAIiC,EACFjC,KACL8tB,EAAQ9tB,GAAG7B,IAId8B,EAASA,EAAO,IAAM,OAClB6P,EAASiB,GAAS5R,GACrB0M,EAAQpL,GAAUR,GAClBwD,EAAKoI,EAAM1L,UAAY0L,EAAM1L,QAAQyQ,SAAW,IAAIzR,IAAcA,EAClEsnB,EAAS3W,EAAS,SAAA3R,OACbsF,EAAI,IAAIqM,EACZyG,EAAYxN,IAAM,EAClBtF,EAAEsM,KAAK9P,EAAQwtB,EAAOtvB,EAAQsvB,EAAOtvB,EAAOoY,EAAa,EAAG,CAACtW,IAC7DwD,EAAEf,OAAO,EAAGe,GACZ8S,EAAYxN,KAAOmH,GAAkB,EAAGqG,IACrC1K,EAAM2S,IAAIve,EAAQwD,UAChBqM,EAAS2W,EAAS,SAAAtoB,UAASsoB,EAAOxmB,EAAQwD,EAAGgqB,EAAOtvB,EAAQsvB,EAAOtvB,EAAO0N,EAAO,KAEzFkiB,yBAAQ9tB,EAAQd,EAAUgF,GAEjB,SAAPnD,GAAQ7C,EAAOqD,EAAO8nB,UAAoBlnB,EAAMinB,QAAQlqB,EAAUhB,EAAOqD,EAAO8nB,SAD7ElnB,EAAQnD,GAAKgiB,GAAGhhB,EAAQlB,WAASI,GAAW,UAAS+f,QAAQ,KAAO/a,GAAQ,YAEhFnD,GAAKoB,MAAQA,EACNpB,IAERgtB,+BAAWluB,UACiD,EAApDuE,EAAgByf,YAAYhkB,GAAS,GAAMO,QAEnDsD,2BAASxF,UACRA,GAASA,EAAMuN,OAASvN,EAAMuN,KAAOC,GAAWxN,EAAMuN,KAAMoM,EAAUpM,OAC/D9H,GAAWkU,EAAW3Z,GAAS,KAEvCyR,uBAAOzR,UACCyF,GAAW4T,EAASrZ,GAAS,KAErC8vB,8CAAgBvuB,IAAAA,KAAMwuB,IAAAA,OAAQC,IAAAA,QAASxqB,IAAAA,SAAUyqB,IAAAA,gBAC/CD,GAAW,IAAIltB,MAAM,KAAKC,QAAQ,SAAAmtB,UAAcA,IAAetd,GAASsd,KAAgBrvB,GAASqvB,IAAe/uB,EAAMI,EAAO,oBAAsB2uB,EAAa,cACjKpV,GAASvZ,GAAQ,SAACI,EAASqE,EAAMmgB,UAAO4J,EAAOxtB,GAAQZ,GAAU4D,GAAaS,GAAQ,GAAIR,GAAW2gB,IACjG8J,IACHxkB,GAASoH,UAAUtR,GAAQ,SAASI,EAASqE,EAAM6D,UAC3CuW,KAAK5W,IAAIsR,GAASvZ,GAAMI,EAASvB,EAAU4F,GAAQA,GAAQ6D,EAAW7D,IAAS,GAAIoa,MAAOvW,MAIpGsmB,mCAAa5uB,EAAMgM,GAClBiI,GAASjU,GAAQiM,GAAWD,IAE7B6iB,6BAAU7iB,EAAMgS,UACRiB,UAAUte,OAASsL,GAAWD,EAAMgS,GAAe/J,IAE3D2P,yBAAQhF,UACAja,EAAgBif,QAAQhF,IAEhCkQ,+BAAWrqB,EAAWsqB,YAAXtqB,IAAAA,EAAO,QAEhBU,EAAOS,EADJgf,EAAK,IAAI1a,GAASzF,OAEtBmgB,EAAG/c,kBAAoB/I,EAAY2F,EAAKoD,mBACxClD,EAAgBqB,OAAO4e,GACvBA,EAAG3f,IAAM,EACT2f,EAAG9c,MAAQ8c,EAAG5d,OAASrC,EAAgBmD,MACvC3C,EAAQR,EAAgBuQ,OACjB/P,GACNS,EAAOT,EAAMO,OACTqpB,IAA0B5pB,EAAMiB,MAAQjB,aAAiBwF,IAASxF,EAAMV,KAAKud,aAAe7c,EAAMsf,SAAS,IAC9Gpc,GAAeuc,EAAIzf,EAAOA,EAAMkB,OAASlB,EAAMsD,QAEhDtD,EAAQS,SAETyC,GAAe1D,EAAiBigB,EAAI,GAC7BA,GAERxJ,QAAS,iBAAC9Z,EAAMnC,UAAUmC,EAAO,IAAIurB,GAAQvrB,EAAMnC,GAASuX,GAC5DgW,WAAY,oBAAAvtB,UAAS,IAAIkuB,GAAWluB,IACpC6vB,kBAAmB,oCAAM3C,GAAO7qB,QAAQ,SAAAuR,OAEtCkc,EAAOlrB,EADJypB,EAAOza,EAAE0Z,eAER1oB,KAAKypB,EACLA,EAAKzpB,KACRypB,EAAKzpB,IAAK,EACVkrB,EAAQ,GAGVA,GAASlc,EAAEtM,YACNylB,MACNyB,2CAAiBvjB,EAAM8Q,OAClBtY,EAAIgV,GAAWxN,KAAUwN,GAAWxN,GAAQ,KAC/CxH,EAAEJ,QAAQ0Y,IAAatY,EAAE8G,KAAKwR,IAEhCgU,iDAAoB9kB,EAAM8Q,OACrBtY,EAAIgV,GAAWxN,GAClB9J,EAAIsC,GAAKA,EAAEJ,QAAQ0Y,GACf,GAAL5a,GAAUsC,EAAE9B,OAAOR,EAAG,IAEvB6uB,MAAO,CAAEC,KA7gFF,SAAPA,KAAgBjiB,EAAKD,EAAKzO,OACrB4wB,EAAQniB,EAAMC,SACXpI,EAASoI,GAAO4B,GAAW5B,EAAKiiB,KAAK,EAAGjiB,EAAIxM,QAASuM,GAAOtC,GAAmBnM,EAAO,SAAAA,UAAW4wB,GAAS5wB,EAAQ0O,GAAOkiB,GAASA,EAASliB,KA2gFpImiB,SAzgFJ,SAAXA,SAAYniB,EAAKD,EAAKzO,OACjB4wB,EAAQniB,EAAMC,EACjBoiB,EAAgB,EAARF,SACFtqB,EAASoI,GAAO4B,GAAW5B,EAAKmiB,SAAS,EAAGniB,EAAIxM,OAAS,GAAIuM,GAAOtC,GAAmBnM,EAAO,SAAAA,UAE7F0O,GAAgBkiB,GADvB5wB,GAAS8wB,GAAS9wB,EAAQ0O,GAAOoiB,GAASA,GAAS,GAClBA,EAAQ9wB,EAASA,MAogF3BqN,WAAAA,GAAYD,OAAAA,GAAQqC,KAAAA,GAAMshB,UA/gFvC,SAAZA,UAAariB,EAAKD,EAAKzO,UAAUic,GAASvN,EAAKD,EAAK,EAAG,EAAGzO,IA+gFIoM,QAAAA,GAAS4kB,MAroF/D,SAARA,MAAStiB,EAAKD,EAAKzO,UAAUmM,GAAmBnM,EAAO,SAAAyC,UAAKiH,GAAOgF,EAAKD,EAAKhM,MAqoFC+Q,WAAAA,GAAYjR,QAAAA,GAASoK,SAAAA,GAAUsP,SAAAA,GAAUgV,KAjhFhH,SAAPA,kCAAWC,2BAAAA,yBAAc,SAAAlxB,UAASkxB,EAAUC,OAAO,SAAC1uB,EAAGoc,UAAMA,EAAEpc,IAAIzC,KAihF0DoxB,QAhhFnH,SAAVA,QAAWvuB,EAAMysB,UAAS,SAAAtvB,UAAS6C,EAAKY,WAAWzD,KAAWsvB,GAAQljB,GAAQpM,MAghFwDqxB,YA/+ExH,SAAdA,YAAehuB,EAAOG,EAAK8N,EAAUggB,OAChCzuB,EAAOgL,MAAMxK,EAAQG,GAAO,EAAI,SAAA8B,UAAM,EAAIA,GAAKjC,EAAQiC,EAAI9B,OAC1DX,EAAM,KAGTyC,EAAGzD,EAAG0vB,EAAeztB,EAAG0tB,EAFrBC,EAAW1xB,EAAUsD,GACxBquB,EAAS,OAEG,IAAbpgB,IAAsBggB,EAAS,KAAOhgB,EAAW,MAC7CmgB,EACHpuB,EAAQ,CAACiC,EAAGjC,GACZG,EAAM,CAAC8B,EAAG9B,QAEJ,GAAI8C,EAASjD,KAAWiD,EAAS9C,GAAM,KAC7C+tB,EAAgB,GAChBztB,EAAIT,EAAMnB,OACVsvB,EAAK1tB,EAAI,EACJjC,EAAI,EAAGA,EAAIiC,EAAGjC,IAClB0vB,EAActmB,KAAKomB,YAAYhuB,EAAMxB,EAAE,GAAIwB,EAAMxB,KAElDiC,IACAjB,EAAO,cAAAyC,GACNA,GAAKxB,MACDjC,EAAIoB,KAAKyL,IAAI8iB,IAAMlsB,UAChBisB,EAAc1vB,GAAGyD,EAAIzD,IAE7ByP,EAAW9N,OACA8tB,IACXjuB,EAAQzC,GAAO0F,EAASjD,GAAS,GAAK,GAAIA,QAEtCkuB,EAAe,KACdjsB,KAAK9B,EACTwO,GAAcgK,KAAK0V,EAAQruB,EAAOiC,EAAG,MAAO9B,EAAI8B,IAEjDzC,EAAO,cAAAyC,UAAKyM,GAAkBzM,EAAGosB,KAAYD,EAAWpuB,EAAMiC,EAAIjC,YAG7D8I,GAAmBmF,EAAUzO,IA48E8GqK,QAAAA,IACnJykB,QAASlxB,EACTmxB,QAAS9W,GACT+W,OAAQ9mB,GACRoc,WAAY1b,GAAS0b,WACrB6I,QAASpd,GACTkf,eAAgB5rB,EAChB6rB,KAAM,CAAC9e,UAAAA,GAAW+e,QAAS1wB,EAAY4K,MAAAA,GAAOT,SAAAA,GAAU6U,UAAAA,GAAW2R,SAAU3vB,GAAW4E,sBAAAA,GAAuBgrB,UAAW,4BAAMrtB,GAAY8X,QAAS,iBAAAwV,UAAcA,GAASla,IAAYA,EAASkD,KAAKlQ,KAAKknB,GAAQA,EAAMvV,KAAO3E,GAAiBA,GAAama,mBAAoB,4BAAApyB,UAASgY,EAAsBhY,KAGlT2C,GAAa,8CAA+C,SAAApB,UAAQQ,GAAMR,GAAQ2K,GAAM3K,KACxFwJ,GAAQvB,IAAIiC,GAAS0b,YACrB/O,EAAcrW,GAAM+gB,GAAG,GAAI,CAACta,SAAS,IAQX,SAAtB6pB,GAAuBhL,EAAQvU,WAC7BwU,EAAKD,EAAOzc,IACT0c,GAAMA,EAAGhiB,IAAMwN,GAAQwU,EAAGqF,KAAO7Z,GAAQwU,EAAG6B,KAAOrW,GACzDwU,EAAKA,EAAGrgB,aAEFqgB,EAkBe,SAAvBgL,GAAwB/wB,EAAM2Q,SACtB,CACN3Q,KAAMA,EACN6Q,QAAS,EACTR,mBAAK9P,EAAQkE,EAAM/B,GAClBA,EAAMmmB,QAAU,SAAAnmB,OACXsuB,EAAMjtB,KACNvF,EAAUiG,KACbusB,EAAO,GACP5vB,GAAaqD,EAAM,SAAAzE,UAAQgxB,EAAKhxB,GAAQ,IACxCyE,EAAOusB,GAEJrgB,EAAU,KAER5M,KADLitB,EAAO,GACGvsB,EACTusB,EAAKjtB,GAAK4M,EAASlM,EAAKV,IAEzBU,EAAOusB,GAjCI,SAAhBC,cAAiBvuB,EAAOwuB,OAErBntB,EAAGzD,EAAGylB,EADH3lB,EAAUsC,EAAM+hB,aAEf1gB,KAAKmtB,MACT5wB,EAAIF,EAAQO,OACLL,MAEKylB,GADXA,EAAKrjB,EAAMwc,UAAU5e,GAAGyD,KACRgiB,EAAG/Y,KACd+Y,EAAG1c,MACN0c,EAAK+K,GAAoB/K,EAAIhiB,IAE9BgiB,GAAMA,EAAGpV,UAAYoV,EAAGpV,SAASugB,EAAUntB,GAAIrB,EAAOtC,EAAQE,GAAIyD,IAwBnEktB,CAAcvuB,EAAO+B,MA1C1B,IAiDalF,GAAOiB,GAAMotB,eAAe,CACvC5tB,KAAK,OACLqQ,mBAAK9P,EAAQkE,EAAM/B,EAAOuM,EAAO7O,OAC5B2D,EAAGgiB,EAAI7kB,MAEN6C,UADArB,MAAQA,EACH+B,EACTvD,EAAIX,EAAOY,aAAa4C,IAAM,IAC9BgiB,EAAKlH,KAAK5W,IAAI1H,EAAQ,gBAAiBW,GAAK,GAAK,GAAIuD,EAAKV,GAAIkL,EAAO7O,EAAS,EAAG,EAAG2D,IACjFqnB,GAAKrnB,EACRgiB,EAAGnY,EAAI1M,OACFoP,OAAO5G,KAAK3F,IAGnBf,uBAAOoc,EAAOxF,WACTmM,EAAKnM,EAAKvQ,IACP0c,GACNziB,EAAayiB,EAAGjH,IAAIiH,EAAGxgB,EAAGwgB,EAAGhiB,EAAGgiB,EAAGnY,EAAGmY,GAAMA,EAAG3T,EAAEgN,EAAO2G,EAAG/Y,GAC3D+Y,EAAKA,EAAGrgB,QAGR,CACF1F,KAAK,WACLqQ,mBAAK9P,EAAQ9B,WACR6B,EAAI7B,EAAMkC,OACPL,UACD2H,IAAI1H,EAAQD,EAAGC,EAAOD,IAAM,EAAG7B,EAAM6B,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,KAIhEywB,GAAqB,aAAchjB,IACnCgjB,GAAqB,aACrBA,GAAqB,OAAQ7iB,MACzB1N,GAELmK,GAAMuS,QAAUhT,GAASgT,QAAU3d,GAAK2d,QAAU,SAClDtG,EAAa,EACb7X,KAAmBqS,KCtoGD,SAAjB+f,GAAkB/R,EAAOxF,UAASA,EAAKkF,IAAIlF,EAAKrU,EAAGqU,EAAK7V,EAAIrC,KAAKC,MAAkC,KAA3BiY,EAAKxK,EAAIwK,EAAK7G,EAAIqM,IAAkB,IAASxF,EAAK/L,EAAG+L,GACxG,SAArBwX,GAAsBhS,EAAOxF,UAASA,EAAKkF,IAAIlF,EAAKrU,EAAGqU,EAAK7V,EAAa,IAAVqb,EAAcxF,EAAK4M,EAAK9kB,KAAKC,MAAkC,KAA3BiY,EAAKxK,EAAIwK,EAAK7G,EAAIqM,IAAkB,IAASxF,EAAK/L,EAAG+L,GAC1H,SAA9ByX,GAA+BjS,EAAOxF,UAASA,EAAKkF,IAAIlF,EAAKrU,EAAGqU,EAAK7V,EAAGqb,EAAS1d,KAAKC,MAAkC,KAA3BiY,EAAKxK,EAAIwK,EAAK7G,EAAIqM,IAAkB,IAASxF,EAAK/L,EAAI+L,EAAKhM,EAAGgM,GACnI,SAAxB0X,GAAyBlS,EAAOxF,OAC3Bnb,EAAQmb,EAAKxK,EAAIwK,EAAK7G,EAAIqM,EAC9BxF,EAAKkF,IAAIlF,EAAKrU,EAAGqU,EAAK7V,KAAMtF,GAASA,EAAQ,GAAK,GAAK,KAAOmb,EAAK/L,EAAG+L,GAE7C,SAA1B2X,GAA2BnS,EAAOxF,UAASA,EAAKkF,IAAIlF,EAAKrU,EAAGqU,EAAK7V,EAAGqb,EAAQxF,EAAK4M,EAAI5M,EAAKhM,EAAGgM,GAC1D,SAAnC4X,GAAoCpS,EAAOxF,UAASA,EAAKkF,IAAIlF,EAAKrU,EAAGqU,EAAK7V,EAAa,IAAVqb,EAAcxF,EAAKhM,EAAIgM,EAAK4M,EAAG5M,GAC1F,SAAlB6X,GAAmBlxB,EAAQd,EAAUhB,UAAU8B,EAAO4lB,MAAM1mB,GAAYhB,EACvD,SAAjBizB,GAAkBnxB,EAAQd,EAAUhB,UAAU8B,EAAO4lB,MAAMwL,YAAYlyB,EAAUhB,GAC9D,SAAnBmzB,GAAoBrxB,EAAQd,EAAUhB,UAAU8B,EAAOC,MAAMf,GAAYhB,EAC1D,SAAfozB,GAAgBtxB,EAAQd,EAAUhB,UAAU8B,EAAOC,MAAMsxB,OAASvxB,EAAOC,MAAMuxB,OAAStzB,EAC/D,SAAzBuzB,GAA0BzxB,EAAQd,EAAUhB,EAAOmb,EAAMwF,OACpDjT,EAAQ5L,EAAOC,MACnB2L,EAAM2lB,OAAS3lB,EAAM4lB,OAAStzB,EAC9B0N,EAAM8lB,gBAAgB7S,EAAOjT,GAED,SAA7B+lB,GAA8B3xB,EAAQd,EAAUhB,EAAOmb,EAAMwF,OACxDjT,EAAQ5L,EAAOC,MACnB2L,EAAM1M,GAAYhB,EAClB0N,EAAM8lB,gBAAgB7S,EAAOjT,GAIjB,SAAbgmB,GAAsB1yB,EAAU2yB,cAC3B7xB,EAASse,KAAKte,OACjB4lB,EAAQ5lB,EAAO4lB,SACX1mB,KAAY4yB,IAAoBlM,EAAO,SACtCmM,IAAMzT,KAAKyT,KAAO,GACN,cAAb7yB,SAII8yB,GAAiBC,UAAUjxB,MAAM,KAAKC,QAAQ,SAAAuC,UAAKouB,GAAW1X,KAAK6G,EAAMvd,EAAGquB,UAHnF3yB,EAAW8yB,GAAiB9yB,IAAaA,GAC/B+C,QAAQ,KAAO/C,EAAS8B,MAAM,KAAKC,QAAQ,SAAAoB,UAAK0e,EAAKgR,IAAI1vB,GAAK6vB,GAAKlyB,EAAQqC,KAAOic,KAAKyT,IAAI7yB,GAAYc,EAAOC,MAAMsM,EAAIvM,EAAOC,MAAMf,GAAYgzB,GAAKlyB,EAAQd,GAI/H,GAAtCof,KAAKqF,MAAM1hB,QAAQkwB,WACnBnyB,EAAOC,MAAMmyB,WACXC,KAAOryB,EAAOY,aAAa,wBAC3B+iB,MAAMxa,KAAKmpB,GAAsBT,EAAU,KAEjD3yB,EAAWizB,IAEXvM,GAASiM,IAAavT,KAAKqF,MAAMxa,KAAKjK,EAAU2yB,EAAUjM,EAAM1mB,IAEnC,SAA/BqzB,GAA+B3M,GAC1BA,EAAM4M,YACT5M,EAAM6M,eAAe,aACrB7M,EAAM6M,eAAe,SACrB7M,EAAM6M,eAAe,WAGR,SAAfC,SAKE3yB,EAAGyD,EAJAmgB,EAAQrF,KAAKqF,MAChB3jB,EAASse,KAAKte,OACd4lB,EAAQ5lB,EAAO4lB,MACfha,EAAQ5L,EAAOC,UAEXF,EAAI,EAAGA,EAAI4jB,EAAMvjB,OAAQL,GAAG,EAChC4jB,EAAM5jB,EAAE,GAAKC,EAAO2jB,EAAM5jB,IAAM4jB,EAAM5jB,EAAE,GAAK4jB,EAAM5jB,EAAE,GAAM6lB,EAAMjC,EAAM5jB,IAAM4jB,EAAM5jB,EAAE,GAAM6lB,EAAM6M,eAAwC,OAAzB9O,EAAM5jB,GAAG6B,OAAO,EAAE,GAAc+hB,EAAM5jB,GAAK4jB,EAAM5jB,GAAGmT,QAAQyf,GAAU,OAAOtd,kBAE1LiJ,KAAKyT,IAAK,KACRvuB,KAAK8a,KAAKyT,IACdnmB,EAAMpI,GAAK8a,KAAKyT,IAAIvuB,GAEjBoI,EAAMwmB,MACTxmB,EAAM8lB,kBACN1xB,EAAOwqB,aAAa,kBAAmBlM,KAAK+T,MAAQ,MAErDtyB,EAAIgD,OACQhD,EAAE+Y,SAAa8M,EAAMuM,MAChCI,GAA6B3M,GAC7Bha,EAAM6hB,QAAU,IAIF,SAAjBmF,GAAkB5yB,EAAQ6yB,OACrBC,EAAQ,CACX9yB,OAAAA,EACA2jB,MAAO,GACPzd,OAAQwsB,GACRK,KAAMnB,WAEP5xB,EAAOC,OAASjB,GAAKixB,KAAKE,SAASnwB,GACnC6yB,GAAcA,EAAW7xB,MAAM,KAAKC,QAAQ,SAAAuC,UAAKsvB,EAAMC,KAAKvvB,KACrDsvB,EAGS,SAAjBE,GAAkBnpB,EAAMopB,OACnBhN,EAAI/a,GAAKgoB,gBAAkBhoB,GAAKgoB,iBAAiBD,GAAM,gCAAgC/f,QAAQ,SAAU,QAASrJ,GAAQqB,GAAKC,cAActB,UAC1Ioc,EAAEL,MAAQK,EAAI/a,GAAKC,cAActB,GAElB,SAAvBspB,GAAwBnzB,EAAQd,EAAUk0B,OACrCC,EAAKC,iBAAiBtzB,UACnBqzB,EAAGn0B,IAAam0B,EAAGE,iBAAiBr0B,EAASgU,QAAQyf,GAAU,OAAOtd,gBAAkBge,EAAGE,iBAAiBr0B,KAAek0B,GAAsBD,GAAqBnzB,EAAQwzB,GAAiBt0B,IAAaA,EAAU,IAAO,GAczN,SAAZu0B,MAtHgB,SAAhBj1B,sBAAyC,oBAAZC,QAuHxBD,IAAmBC,OAAOge,WAC7B7R,GAAOnM,OACPyM,GAAON,GAAK6R,SACZiX,GAAcxoB,GAAKyoB,gBACnBC,GAAWZ,GAAe,QAAU,CAACpN,MAAM,IAC1BoN,GAAe,OAChCb,GAAiBqB,GAAiBrB,IAClCG,GAAuBH,GAAiB,SACxCyB,GAAShO,MAAMiO,QAAU,2DACzBC,KAAgBN,GAAiB,eACjCzwB,GAAa/D,GAAKixB,KAAKG,UACvB2D,GAAiB,GAGJ,SAAfC,GAAwBC,OAKtBC,EAJG9B,EAAMY,GAAe,MAAQ1U,KAAK6V,iBAAmB7V,KAAK6V,gBAAgBvzB,aAAa,UAAa,8BACvGwzB,EAAY9V,KAAK+V,WACjBC,EAAahW,KAAKiW,YAClBC,EAASlW,KAAKsH,MAAMiO,WAErBH,GAAYe,YAAYrC,GACxBA,EAAIqC,YAAYnW,WACXsH,MAAM8O,QAAU,QACjBT,MAEFC,EAAO5V,KAAKqW,eACPC,UAAYtW,KAAKqW,aACjBA,QAAUX,GACd,MAAO/N,SACC3H,KAAKsW,YACfV,EAAO5V,KAAKsW,oBAETR,IACCE,EACHF,EAAUS,aAAavW,KAAMgW,GAE7BF,EAAUK,YAAYnW,OAGxBoV,GAAYoB,YAAY1C,QACnBxM,MAAMiO,QAAUW,EACdN,EAEiB,SAAzBa,GAA0B/0B,EAAQg1B,WAC7Bj1B,EAAIi1B,EAAgB50B,OACjBL,QACFC,EAAOi1B,aAAaD,EAAgBj1B,WAChCC,EAAOY,aAAao0B,EAAgBj1B,IAInC,SAAXm1B,GAAWl1B,OACNm1B,MAEHA,EAASn1B,EAAO20B,UACf,MAAOS,GACRD,EAASnB,GAAa9Z,KAAKla,GAAQ,UAEnCm1B,IAAWA,EAAOE,OAASF,EAAOG,SAAYt1B,EAAO20B,UAAYX,KAAiBmB,EAASnB,GAAa9Z,KAAKla,GAAQ,KAE9Gm1B,GAAWA,EAAOE,OAAUF,EAAO5oB,GAAM4oB,EAAO3oB,EAA8I2oB,EAAzI,CAAC5oB,GAAIwoB,GAAuB/0B,EAAQ,CAAC,IAAI,KAAK,QAAU,EAAGwM,GAAGuoB,GAAuB/0B,EAAQ,CAAC,IAAI,KAAK,QAAU,EAAGq1B,MAAM,EAAGC,OAAO,GAEzL,SAATC,GAAStP,YAAQA,EAAEuP,QAAYvP,EAAEoO,aAAcpO,EAAEkO,kBAAoBe,GAASjP,IAC5D,SAAlBwP,GAAmBz1B,EAAQd,MACtBA,EAAU,KACT0mB,EAAQ5lB,EAAO4lB,MACf1mB,KAAY4yB,IAAmB5yB,IAAaozB,KAC/CpzB,EAAWizB,IAERvM,EAAM6M,gBACoB,OAAzBvzB,EAAS0C,OAAO,EAAE,IAAwC,WAAzB1C,EAAS0C,OAAO,EAAE,KACtD1C,EAAW,IAAMA,GAElB0mB,EAAM6M,eAAevzB,EAASgU,QAAQyf,GAAU,OAAOtd,gBAEvDuQ,EAAM8P,gBAAgBx2B,IAIL,SAApBy2B,GAAqBpQ,EAAQvlB,EAAQd,EAAU02B,EAAWl0B,EAAKm0B,OAC1DrQ,EAAK,IAAIrU,GAAUoU,EAAOzc,IAAK9I,EAAQd,EAAU,EAAG,EAAG22B,EAAe5E,GAAmCD,WAC7GzL,EAAOzc,IAAM0c,GACVnY,EAAIuoB,EACPpQ,EAAGS,EAAIvkB,EACP6jB,EAAOxV,OAAO5G,KAAKjK,GACZsmB,EAKS,SAAjBsQ,GAAkB91B,EAAQd,EAAUhB,EAAOsvB,OAUzCuI,EAAI5xB,EAAQyH,EAAOoqB,EAThBC,EAAWt0B,WAAWzD,IAAU,EACnCg4B,GAAWh4B,EAAQ,IAAIoF,OAAO1B,QAAQq0B,EAAW,IAAI71B,SAAW,KAChEwlB,EAAQgO,GAAShO,MACjBuQ,EAAaC,GAAe7iB,KAAKrU,GACjCm3B,EAA6C,QAAjCr2B,EAAOs2B,QAAQjhB,cAC3BkhB,GAAmBF,EAAY,SAAW,WAAaF,EAAa,QAAU,UAE9EK,EAAoB,OAAThJ,EACXiJ,EAAqB,MAATjJ,SAETA,IAAS0I,IAAYD,GAAYS,GAAqBlJ,IAASkJ,GAAqBR,GAChFD,GAEK,OAAZC,GAAqBM,IAAcP,EAAWH,GAAe91B,EAAQd,EAAUhB,EAAO,OACvF83B,EAAQh2B,EAAOw1B,QAAUD,GAAOv1B,IAC3By2B,GAAyB,MAAZP,IAAqBpE,GAAgB5yB,MAAcA,EAAS+C,QAAQ,UAItF2jB,EAAMuQ,EAAa,QAAU,UAbnB,KAayCK,EAAWN,EAAU1I,GACxErpB,GAAWjF,EAAS+C,QAAQ,UAAsB,OAATurB,GAAiBxtB,EAAOy0B,cAAgB4B,EAAcr2B,EAASA,EAAOq0B,WAC3G2B,IACH7xB,GAAUnE,EAAOm0B,iBAAmB,IAAIE,YAEpClwB,GAAUA,IAAW+G,IAAS/G,EAAOswB,cACzCtwB,EAAS+G,GAAKyrB,OAEf/qB,EAAQzH,EAAOlE,QACFw2B,GAAa7qB,EAAMypB,OAASc,GAAcvqB,EAAMhJ,OAASqG,GAAQrG,OAASgJ,EAAM6hB,QACrFvsB,GAAO+0B,EAAWrqB,EAAMypB,MAvBtB,OAyBRoB,GAAyB,MAAZP,GAAqBU,GAAoBzD,GAAqBhvB,EAAQ,cAAgByhB,EAAM7d,SAAWorB,GAAqBnzB,EAAQ,aACjJmE,IAAWnE,IAAY4lB,EAAM7d,SAAW,UACzC5D,EAAOswB,YAAYb,IACnBmC,EAAKnC,GAAS2C,GACdpyB,EAAO2wB,YAAYlB,IACnBhO,EAAM7d,SAAW,WACbouB,GAAcM,KACjB7qB,EAAQpL,GAAU2D,IACZvB,KAAOqG,GAAQrG,KACrBgJ,EAAMypB,MAAQlxB,EAAOoyB,IAGhBr1B,GAAOs1B,EAAWT,EAAKE,EArCpB,IAqCwCF,GAAME,EArC9C,IAqCkEF,EAAKE,EAAW,MA3B3FF,EAAKC,EAAQh2B,EAAO20B,UAAUwB,EAAa,QAAU,UAAYn2B,EAAOu2B,GACjEr1B,GAAOu1B,EAAYR,EAAWF,EAX5B,IAW0CE,EAAW,IAAMF,KAiD7C,SAAzBc,GAAkC72B,EAAQgR,EAAMzP,EAAOG,OACjDH,GAAmB,SAAVA,EAAkB,KAC3BiC,EAAIgwB,GAAiBxiB,EAAMhR,EAAQ,GACtC6O,EAAIrL,GAAK2vB,GAAqBnzB,EAAQwD,EAAG,GACtCqL,GAAKA,IAAMtN,GACdyP,EAAOxN,EACPjC,EAAQsN,GACW,gBAATmC,IACVzP,EAAQ4xB,GAAqBnzB,EAAQ,uBAMtCqC,EAAGyQ,EAAQgkB,EAAa9P,EAAUhU,EAAO+jB,EAAYC,EAAUlQ,EAAQC,EAAOkQ,EAASC,EAHpF1R,EAAK,IAAIrU,GAAUmN,KAAKxV,IAAK9I,EAAO4lB,MAAO5U,EAAM,EAAG,EAAGkW,IAC1DxY,EAAQ,EACRyY,EAAa,KAEd3B,EAAGnY,EAAI9L,EACPikB,EAAGS,EAAIvkB,EACPH,GAAS,GAEG,UADZG,GAAO,MAEN1B,EAAO4lB,MAAM5U,GAAQtP,EACrBA,EAAMyxB,GAAqBnzB,EAAQgR,IAAStP,EAC5C1B,EAAO4lB,MAAM5U,GAAQzP,GAGtB6R,GADA/Q,EAAI,CAACd,EAAOG,IAGZA,EAAMW,EAAE,GACRy0B,GAFAv1B,EAAQc,EAAE,IAEUe,MAAMsP,KAAoB,IAClChR,EAAI0B,MAAMsP,KAAoB,IAC5BtS,OAAQ,MACb0S,EAASJ,GAAgBlI,KAAK9I,IACrCs1B,EAAWlkB,EAAO,GAClBiU,EAAQrlB,EAAI4S,UAAU5F,EAAOoE,EAAOpE,OAChCsE,EACHA,GAASA,EAAQ,GAAK,EACS,UAArB+T,EAAMnlB,QAAQ,IAAuC,UAArBmlB,EAAMnlB,QAAQ,KACxDoR,EAAQ,GAELgkB,KAAcD,EAAaD,EAAY3P,MAAiB,MAC3DH,EAAWrlB,WAAWo1B,IAAe,EACrCG,EAAYH,EAAWn1B,QAAQolB,EAAW,IAAI5mB,QACtB,MAAvB42B,EAASv1B,OAAO,KAAgBu1B,EAAW11B,GAAe0lB,EAAUgQ,GAAYE,GACjFpQ,EAASnlB,WAAWq1B,GACpBC,EAAUD,EAASp1B,QAAQklB,EAAS,IAAI1mB,QACxCsO,EAAQgE,GAAgBY,UAAY2jB,EAAQ72B,OACvC62B,IACJA,EAAUA,GAAW1f,EAAQI,MAAM3G,IAASkmB,EACxCxoB,IAAUhN,EAAItB,SACjBsB,GAAOu1B,EACPzR,EAAGS,GAAKgR,IAGNC,IAAcD,IACjBjQ,EAAW8O,GAAe91B,EAAQgR,EAAM+lB,EAAYE,IAAY,GAGjEzR,EAAG1c,IAAM,CACR3D,MAAOqgB,EAAG1c,IACVtF,EAAIujB,GAAyB,IAAfI,EAAqBJ,EAAQ,IAC3ClY,EAAGmY,EACHxU,EAAGsU,EAASE,EACZI,EAAIpU,GAASA,EAAQ,GAAe,WAAThC,EAAoB7P,KAAKC,MAAQ,IAI/DokB,EAAGhT,EAAK9D,EAAQhN,EAAItB,OAAUsB,EAAI4S,UAAU5F,EAAOhN,EAAItB,QAAU,QAEjEolB,EAAG3T,EAAa,YAATb,GAA8B,SAARtP,EAAiBuvB,GAAmCD,UAElFpY,GAAQrF,KAAK7R,KAAS8jB,EAAGS,EAAI,QACxBnd,IAAM0c,EAIoB,SAAhC2R,GAAgCj5B,OAC3B8C,EAAQ9C,EAAM8C,MAAM,KACvBuL,EAAIvL,EAAM,GACVwL,EAAIxL,EAAM,IAAM,YACP,QAANuL,GAAqB,WAANA,GAAwB,SAANC,GAAsB,UAANA,IACpDtO,EAAQqO,EACRA,EAAIC,EACJA,EAAItO,GAEL8C,EAAM,GAAKo2B,GAAkB7qB,IAAMA,EACnCvL,EAAM,GAAKo2B,GAAkB5qB,IAAMA,EAC5BxL,EAAMiS,KAAK,KAEC,SAApBokB,GAAqBxY,EAAOxF,MACvBA,EAAKlX,OAASkX,EAAKlX,MAAMoF,QAAU8R,EAAKlX,MAAM0D,KAAM,KAKtDmL,EAAMsmB,EAAiBv3B,EAJpBC,EAASqZ,EAAKrU,EACjB4gB,EAAQ5lB,EAAO4lB,MACfjC,EAAQtK,EAAK/L,EACb1B,EAAQ5L,EAAOC,SAEF,QAAV0jB,IAA6B,IAAVA,EACtBiC,EAAMiO,QAAU,GAChByD,EAAkB,WAGlBv3B,GADA4jB,EAAQA,EAAM3iB,MAAM,MACVZ,QACI,IAALL,GACRiR,EAAO2S,EAAM5jB,GACT+xB,GAAgB9gB,KACnBsmB,EAAkB,EAClBtmB,EAAiB,oBAATA,EAA8BshB,GAAuBH,IAE9DsD,GAAgBz1B,EAAQgR,GAGtBsmB,IACH7B,GAAgBz1B,EAAQmyB,IACpBvmB,IACHA,EAAMwmB,KAAOpyB,EAAO01B,gBAAgB,aACpC6B,GAAgBv3B,EAAQ,GACxB4L,EAAM6hB,QAAU,EAChB8E,GAA6B3M,MA6Fd,SAAnB4R,GAAmBt5B,SAAoB,6BAAVA,GAAkD,SAAVA,IAAqBA,EACrD,SAArCu5B,GAAqCz3B,OAChC03B,EAAevE,GAAqBnzB,EAAQmyB,WACzCqF,GAAiBE,GAAgBC,GAAoBD,EAAa91B,OAAO,GAAGwB,MAAM+O,IAASE,IAAInR,IAE1F,SAAb02B,GAAc53B,EAAQ63B,OAIpB1zB,EAAQowB,EAAa9D,EAAMqH,EAHxBlsB,EAAQ5L,EAAOC,OAASO,GAAUR,GACrC4lB,EAAQ5lB,EAAO4lB,MACfmS,EAASN,GAAmCz3B,UAEzC4L,EAAMwmB,KAAOpyB,EAAOY,aAAa,aAGP,iBAD7Bm3B,EAAS,EADTtH,EAAOzwB,EAAOiyB,UAAU+F,QAAQC,cAAcF,QAC/B11B,EAAGouB,EAAKpjB,EAAGojB,EAAKje,EAAGie,EAAKhkB,EAAGgkB,EAAKxK,EAAGwK,EAAK1T,IACxC9J,KAAK,KAA0B0kB,GAAoBI,GACxDA,IAAWJ,IAAsB33B,EAAOk4B,cAAgBl4B,IAAW0zB,IAAgB9nB,EAAMwmB,MAEnG3B,EAAO7K,EAAM8O,QACb9O,EAAM8O,QAAU,SAChBvwB,EAASnE,EAAOq0B,aACAr0B,EAAOk4B,eACtBJ,EAAa,EACbvD,EAAcv0B,EAAOm4B,mBACrBzE,GAAYe,YAAYz0B,IAEzB+3B,EAASN,GAAmCz3B,GAC5CywB,EAAQ7K,EAAM8O,QAAUjE,EAAQgF,GAAgBz1B,EAAQ,WACpD83B,IACHvD,EAAcpwB,EAAO0wB,aAAa70B,EAAQu0B,GAAepwB,EAASA,EAAOswB,YAAYz0B,GAAU0zB,GAAYoB,YAAY90B,KAGjH63B,GAA2B,EAAhBE,EAAO33B,OAAc,CAAC23B,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAAIA,EAAO,IAAKA,EAAO,KAAOA,GAE9F,SAAlBK,GAAmBp4B,EAAQq4B,EAAQC,EAAkBC,EAAQC,EAAaC,OAgBxEtD,EAAQuD,EAAgBlsB,EAfrBZ,EAAQ5L,EAAOC,MAClB83B,EAASS,GAAeZ,GAAW53B,GAAQ,GAC3C24B,EAAa/sB,EAAMgtB,SAAW,EAC9BC,EAAajtB,EAAMktB,SAAW,EAC9BC,EAAantB,EAAMotB,SAAW,EAC9BC,EAAartB,EAAMstB,SAAW,EAC9B72B,EAAI01B,EAAO,GACX1qB,EAAI0qB,EAAO,GACXvlB,EAAIulB,EAAO,GACXtrB,EAAIsrB,EAAO,GACXoB,EAAKpB,EAAO,GACZqB,EAAKrB,EAAO,GACZsB,EAAchB,EAAOr3B,MAAM,KAC3B43B,EAAUj3B,WAAW03B,EAAY,KAAO,EACxCP,EAAUn3B,WAAW03B,EAAY,KAAO,EAEpCf,EAIMP,IAAWJ,KAAsBe,EAAer2B,EAAIoK,EAAIY,EAAImF,KAEtEhG,EAAIosB,IAAYvrB,EAAIqrB,GAAeI,GAAWz2B,EAAIq2B,IAAiBr2B,EAAI+2B,EAAK/rB,EAAI8rB,GAAMT,EACtFE,EAFIA,GAAWnsB,EAAIisB,GAAeI,IAAYtmB,EAAIkmB,IAAiBlmB,EAAI4mB,EAAK3sB,EAAI0sB,GAAMT,EAGtFI,EAAUtsB,IANVosB,GADAzD,EAASD,GAASl1B,IACDuM,IAAM8sB,EAAY,GAAGp3B,QAAQ,KAAO22B,EAAU,IAAMzD,EAAOE,MAAQuD,GACpFE,EAAU3D,EAAO3oB,KAAQ6sB,EAAY,IAAMA,EAAY,IAAIp3B,QAAQ,KAAQ62B,EAAU,IAAM3D,EAAOG,OAASwD,IAOxGP,IAAsB,IAAXA,GAAoB3sB,EAAM2sB,QACxCY,EAAKP,EAAUD,EACfS,EAAKN,EAAUD,EACfjtB,EAAMotB,QAAUD,GAAcI,EAAK92B,EAAI+2B,EAAK5mB,GAAK2mB,EACjDvtB,EAAMstB,QAAUD,GAAcE,EAAK9rB,EAAI+rB,EAAK3sB,GAAK2sB,GAEjDxtB,EAAMotB,QAAUptB,EAAMstB,QAAU,EAEjCttB,EAAMgtB,QAAUA,EAChBhtB,EAAMktB,QAAUA,EAChBltB,EAAM2sB,SAAWA,EACjB3sB,EAAMysB,OAASA,EACfzsB,EAAM0sB,mBAAqBA,EAC3Bt4B,EAAO4lB,MAAM0M,IAAwB,UACjCmG,IACH9C,GAAkB8C,EAAyB7sB,EAAO,UAAW+sB,EAAYC,GACzEjD,GAAkB8C,EAAyB7sB,EAAO,UAAWitB,EAAYC,GACzEnD,GAAkB8C,EAAyB7sB,EAAO,UAAWmtB,EAAYntB,EAAMotB,SAC/ErD,GAAkB8C,EAAyB7sB,EAAO,UAAWqtB,EAAYrtB,EAAMstB,UAEhFl5B,EAAOwqB,aAAa,kBAAmBoO,EAAU,IAAME,GAsKtC,SAAlBQ,GAAmBt5B,EAAQuB,EAAOrD,OAC7BsvB,EAAOljB,GAAQ/I,UACZL,GAAOS,WAAWJ,GAASI,WAAWm0B,GAAe91B,EAAQ,IAAK9B,EAAQ,KAAMsvB,KAAUA,EAmHxE,SAA1B+L,GAAmChU,EAAQvlB,EAAQd,EAAU8nB,EAAUgQ,OAMrEwC,EAAWhU,EALRiU,EAAM,IACT9J,EAAW1xB,EAAU+4B,GAErB7L,EADSxpB,WAAWq1B,IAAcrH,IAAaqH,EAAS/0B,QAAQ,OAAUy3B,GAAW,GACnE1S,EAClB2S,EAAc3S,EAAWmE,EAAU,aAEhCwE,IAEe,WADlB6J,EAAYxC,EAASh2B,MAAM,KAAK,MAE/BmqB,GAAUsO,KACKtO,QACdA,GAAWA,EAAS,EAAKsO,GAAOA,GAGhB,OAAdD,GAAsBrO,EAAS,EAClCA,GAAWA,EAASsO,MAAiBA,KAAUtO,EAASsO,GAAOA,EACvC,QAAdD,GAAgC,EAATrO,IACjCA,GAAWA,EAASsO,MAAiBA,KAAUtO,EAASsO,GAAOA,IAGjElU,EAAOzc,IAAM0c,EAAK,IAAIrU,GAAUoU,EAAOzc,IAAK9I,EAAQd,EAAU8nB,EAAUmE,EAAQ0F,IAChFrL,EAAGS,EAAI0T,EACPnU,EAAGlY,EAAI,MACPiY,EAAOxV,OAAO5G,KAAKjK,GACZsmB,EAEE,SAAVoU,GAAW55B,EAAQ65B,OACb,IAAIr2B,KAAKq2B,EACb75B,EAAOwD,GAAKq2B,EAAOr2B,UAEbxD,EAEc,SAAtB85B,GAAuBvU,EAAQwU,EAAY/5B,OAIzCg6B,EAAUx2B,EAAGuzB,EAAYC,EAAUhQ,EAAUF,EAAmBmQ,EAH7DgD,EAAaL,GAAQ,GAAI55B,EAAOC,OAEnC2lB,EAAQ5lB,EAAO4lB,UAeXpiB,KAbDy2B,EAAW7H,KACd2E,EAAa/2B,EAAOY,aAAa,aACjCZ,EAAOwqB,aAAa,YAAa,IACjC5E,EAAMuM,IAAkB4H,EACxBC,EAAWzC,GAAgBv3B,EAAQ,GACnCy1B,GAAgBz1B,EAAQmyB,IACxBnyB,EAAOwqB,aAAa,YAAauM,KAEjCA,EAAazD,iBAAiBtzB,GAAQmyB,IACtCvM,EAAMuM,IAAkB4H,EACxBC,EAAWzC,GAAgBv3B,EAAQ,GACnC4lB,EAAMuM,IAAkB4E,GAEfjF,IACTiF,EAAakD,EAAWz2B,OACxBwzB,EAAWgD,EAASx2B,KAlBV,gDAmB6BvB,QAAQuB,GAAK,IAGnDwjB,EAFY1c,GAAQysB,MACpBE,EAAU3sB,GAAQ0sB,IACmBlB,GAAe91B,EAAQwD,EAAGuzB,EAAYE,GAAWt1B,WAAWo1B,GACjGjQ,EAASnlB,WAAWq1B,GACpBzR,EAAOzc,IAAM,IAAIqI,GAAUoU,EAAOzc,IAAKkxB,EAAUx2B,EAAGwjB,EAAUF,EAASE,EAAU4J,IACjFrL,EAAOzc,IAAIwE,EAAI2pB,GAAW,EAC1B1R,EAAOxV,OAAO5G,KAAK3F,IAGrBo2B,GAAQI,EAAUC,OAh5BhBrvB,GAAMM,GAAMwoB,GAAaK,GAAgBH,GAA0BsG,GAAqBn3B,GAkG3F+wB,GDijGcqG,GAA4IzmB,GAA5IymB,OAAQC,GAAoI1mB,GAApI0mB,OAAQC,GAA4H3mB,GAA5H2mB,OAAQC,GAAoH5mB,GAApH4mB,OAAQC,GAA4G7mB,GAA5G6mB,OAAQxc,GAAoGrK,GAApGqK,OAAQyc,GAA4F9mB,GAA5F8mB,KAAMC,GAAsF/mB,GAAtF+mB,MAAOC,GAA+EhnB,GAA/EgnB,MAAOC,GAAwEjnB,GAAxEinB,MAAOC,GAAiElnB,GAAjEknB,OAAQC,GAAyDnnB,GAAzDmnB,QAASC,GAAgDpnB,GAAhDonB,KAAM5c,GAA0CxK,GAA1CwK,YAAa6c,GAA6BrnB,GAA7BqnB,OAAQC,GAAqBtnB,GAArBsnB,KAAMC,GAAevnB,GAAfunB,KAAMC,GAASxnB,GAATwnB,KCjpGjJpJ,GAAkB,GAClB4H,GAAW,IAAMv4B,KAAK6W,GACtBmjB,GAAWh6B,KAAK6W,GAAK,IACrBojB,GAASj6B,KAAKk6B,MAEd1I,GAAW,WACXyD,GAAiB,uCACjBkF,GAAc,YACdtJ,GAAmB,CAACuJ,UAAU,qBAAsBC,MAAM,gBAAiBC,MAAM,WAwBjFtJ,GAAiB,YACjBG,GAAuBH,GAAiB,SAwExCuJ,GAAY,qBAAqB16B,MAAM,KACvCwyB,GAAmB,SAAnBA,iBAAoBt0B,EAAUy8B,EAASC,OAErC/sB,GADO8sB,GAAW/H,IACZhO,MACN7lB,EAAI,KACDb,KAAY2P,IAAM+sB,SACd18B,MAERA,EAAWA,EAASuC,OAAO,GAAGyP,cAAgBhS,EAAS0C,OAAO,GACvD7B,OAAU27B,GAAU37B,GAAGb,KAAa2P,YACnC9O,EAAI,EAAK,MAAe,IAANA,EAAW,KAAa,GAALA,EAAU27B,GAAU37B,GAAK,IAAMb,GA0F7Ew3B,GAAuB,CAACmF,IAAI,EAAGC,IAAI,EAAGC,KAAK,GAC3CnF,GAAsB,CAAC7pB,KAAK,EAAGivB,KAAK,GAgDpC9J,GAAO,SAAPA,KAAQlyB,EAAQd,EAAUsuB,EAAMC,OAC3BvvB,SACJ61B,IAAkBN,KACbv0B,KAAY8yB,IAAkC,cAAb9yB,KACrCA,EAAW8yB,GAAiB9yB,IACd+C,QAAQ,OACrB/C,EAAWA,EAAS8B,MAAM,KAAK,IAG7B8wB,GAAgB5yB,IAA0B,cAAbA,GAChChB,EAAQq5B,GAAgBv3B,EAAQytB,GAChCvvB,EAAsB,oBAAbgB,EAAkChB,EAAMgB,GAAYhB,EAAMk0B,IAAMl0B,EAAMm6B,OAAS4D,GAAc9I,GAAqBnzB,EAAQsyB,KAAyB,IAAMp0B,EAAMg+B,QAAU,OAElLh+B,EAAQ8B,EAAO4lB,MAAM1mB,KACG,SAAVhB,IAAoBuvB,MAAavvB,EAAQ,IAAI+D,QAAQ,WAClE/D,EAASi+B,GAAcj9B,IAAai9B,GAAcj9B,GAAUc,EAAQd,EAAUsuB,IAAU2F,GAAqBnzB,EAAQd,IAAawB,GAAaV,EAAQd,KAA2B,YAAbA,EAAyB,EAAI,IAG7LsuB,MAAWtvB,EAAQ,IAAIoF,OAAOrB,QAAQ,KAAO6zB,GAAe91B,EAAQd,EAAUhB,EAAOsvB,GAAQA,EAAOtvB,GA6E5Gk5B,GAAoB,CAACgF,IAAI,KAAMC,OAAO,OAAQnvB,KAAK,KAAMovB,MAAM,OAAQnwB,OAAO,OAgD9EgwB,GAAgB,CACfI,+BAAWhX,EAAQvlB,EAAQd,EAAU83B,EAAU70B,MAC3B,gBAAfA,EAAMkX,KAAwB,KAC7BmM,EAAKD,EAAOzc,IAAM,IAAIqI,GAAUoU,EAAOzc,IAAK9I,EAAQd,EAAU,EAAG,EAAGm4B,WACxE7R,EAAGlY,EAAI0pB,EACPxR,EAAG0F,IAAM,GACT1F,EAAGrjB,MAAQA,EACXojB,EAAOxV,OAAO5G,KAAKjK,GACZ,KA6EVy4B,GAAoB,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAC/B6E,GAAwB,GAkFxBjF,GAAkB,SAAlBA,gBAAmBv3B,EAAQytB,OACtB7hB,EAAQ5L,EAAOC,OAAS,IAAIK,GAAQN,MACpC,MAAO4L,IAAU6hB,IAAY7hB,EAAM6hB,eAC/B7hB,MAQPW,EAAGC,EAAGiwB,EAAGlL,EAAQC,EAAQkL,EAAUC,EAAWC,EAAWC,EAAOC,EAAOC,EAAanE,EAASE,EAC7Ff,EAAQiF,EAAO3kB,EAAKC,EAAKjW,EAAGgL,EAAGmF,EAAG/F,EAAGwwB,EAAKC,EAAKC,EAAIC,EAAIC,EAAIC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAPjF/X,EAAQ5lB,EAAO4lB,MAClBgY,EAAiBhyB,EAAM2lB,OAAS,EAEhCsK,EAAM,MACNxI,EAAKC,iBAAiBtzB,GACtBq4B,EAASlF,GAAqBnzB,EAAQsyB,KAAyB,WAGhE/lB,EAAIC,EAAIiwB,EAAIC,EAAWC,EAAYC,EAAYC,EAAQC,EAAQC,EAAc,EAC7ExL,EAASC,EAAS,EAClB5lB,EAAMwmB,OAASpyB,EAAOw1B,SAAUD,GAAOv1B,IAEnCqzB,EAAGb,YACe,SAAjBa,EAAGb,WAAqC,SAAba,EAAGmI,OAAkC,SAAdnI,EAAGwK,SACxDjY,EAAMuM,KAAoC,SAAjBkB,EAAGb,UAAuB,gBAAkBa,EAAGb,UAAY,QAAQxxB,MAAM,KAAKsB,MAAM,EAAG,GAAG2Q,KAAK,MAAQ,KAAO,KAAqB,SAAdogB,EAAGwK,OAAoB,UAAYxK,EAAGwK,OAAS,KAAO,KAAoB,SAAbxK,EAAGmI,MAAmB,SAAWnI,EAAGmI,MAAMx6B,MAAM,KAAKiS,KAAK,KAAO,KAAO,KAA8B,SAAvBogB,EAAGlB,IAA6BkB,EAAGlB,IAAkB,KAEhVvM,EAAM4V,MAAQ5V,EAAMiY,OAASjY,EAAM4M,UAAY,QAGhDuF,EAASH,GAAW53B,EAAQ4L,EAAMwmB,KAC9BxmB,EAAMwmB,MAIR+K,EAHGvxB,EAAM6hB,SACT2P,EAAKp9B,EAAO20B,UACZ0D,EAAUzsB,EAAMgtB,QAAUwE,EAAG7wB,EAAK,OAASX,EAAMktB,QAAUsE,EAAG5wB,GAAK,KAC9D,KAECihB,GAAWztB,EAAOY,aAAa,mBAEtCw3B,GAAgBp4B,EAAQm9B,GAAM9E,IAAU8E,GAAMvxB,EAAM0sB,kBAAmC,IAAjB1sB,EAAM2sB,OAAkBR,IAE/Fa,EAAUhtB,EAAMgtB,SAAW,EAC3BE,EAAUltB,EAAMktB,SAAW,EACvBf,IAAWJ,KACdt1B,EAAI01B,EAAO,GACX1qB,EAAI0qB,EAAO,GACXvlB,EAAIulB,EAAO,GACXtrB,EAAIsrB,EAAO,GACXxrB,EAAI0wB,EAAMlF,EAAO,GACjBvrB,EAAI0wB,EAAMnF,EAAO,GAGK,IAAlBA,EAAO33B,QACVmxB,EAASpwB,KAAKgX,KAAK9V,EAAIA,EAAIgL,EAAIA,GAC/BmkB,EAASrwB,KAAKgX,KAAK1L,EAAIA,EAAI+F,EAAIA,GAC/BkqB,EAAYr6B,GAAKgL,EAAK+tB,GAAO/tB,EAAGhL,GAAKq3B,GAAW,GAChDmD,EAASrqB,GAAK/F,EAAK2uB,GAAO5oB,EAAG/F,GAAKitB,GAAWgD,EAAW,KAC9ClL,GAAUrwB,KAAK+F,IAAI/F,KAAKkX,IAAIwkB,EAAQ1B,MAC1CvvB,EAAMwmB,MACT7lB,GAAKqsB,GAAWA,EAAUv2B,EAAIy2B,EAAUtmB,GACxChG,GAAKssB,GAAWF,EAAUvrB,EAAIyrB,EAAUrsB,MAKzCkxB,EAAM5F,EAAO,GACb0F,EAAM1F,EAAO,GACbuF,EAAMvF,EAAO,GACbwF,EAAMxF,EAAO,GACbyF,EAAMzF,EAAO,IACb2F,EAAM3F,EAAO,IACbxrB,EAAIwrB,EAAO,IACXvrB,EAAIurB,EAAO,IACX0E,EAAI1E,EAAO,IAGX4E,GADAK,EAAQ5B,GAAOuC,EAAKH,IACA9D,GAEhBsD,IAGHG,EAAKF,GAFL5kB,EAAMlX,KAAKkX,KAAK2kB,IAEHM,GADbhlB,EAAMnX,KAAKmX,KAAK0kB,IAEhBI,EAAKF,EAAI7kB,EAAIklB,EAAIjlB,EACjB+kB,EAAKM,EAAItlB,EAAImlB,EAAIllB,EACjBglB,EAAML,GAAK3kB,EAAIglB,EAAIjlB,EACnBklB,EAAML,GAAK5kB,EAAIilB,EAAIllB,EACnBmlB,EAAMG,GAAKrlB,EAAIklB,EAAInlB,EACnBqlB,EAAMD,GAAKnlB,EAAIolB,EAAIrlB,EACnB4kB,EAAME,EACND,EAAME,EACNO,EAAMN,GAIPT,GADAI,EAAQ5B,IAAQ5oB,EAAGgrB,IACC9D,GAChBsD,IACH3kB,EAAMlX,KAAKkX,KAAK2kB,GAKhBU,EAAMjxB,GAJN6L,EAAMnX,KAAKmX,KAAK0kB,IAIJU,EAAIrlB,EAChBhW,EAJA86B,EAAK96B,EAAEgW,EAAIilB,EAAIhlB,EAKfjL,EAJA+vB,EAAK/vB,EAAEgL,EAAIklB,EAAIjlB,EAKf9F,EAJA6qB,EAAK7qB,EAAE6F,EAAImlB,EAAIllB,GAQhBokB,GADAM,EAAQ5B,GAAO/tB,EAAGhL,IACCq3B,GACfsD,IAGHG,EAAK96B,GAFLgW,EAAMlX,KAAKkX,IAAI2kB,IAEJ3vB,GADXiL,EAAMnX,KAAKmX,IAAI0kB,IAEfI,EAAKH,EAAI5kB,EAAI6kB,EAAI5kB,EACjBjL,EAAIA,EAAEgL,EAAIhW,EAAEiW,EACZ4kB,EAAMA,EAAI7kB,EAAI4kB,EAAI3kB,EAClBjW,EAAI86B,EACJF,EAAMG,GAGHT,GAAwD,MAA3Cx7B,KAAK+F,IAAIy1B,GAAax7B,KAAK+F,IAAIw1B,KAC/CC,EAAYD,EAAW,EACvBE,EAAY,IAAMA,GAEnBrL,EAASrwB,GAAOC,KAAKgX,KAAK9V,EAAIA,EAAIgL,EAAIA,EAAImF,EAAIA,IAC9Cgf,EAAStwB,GAAOC,KAAKgX,KAAK+kB,EAAMA,EAAMS,EAAMA,IAC5CX,EAAQ5B,GAAO6B,EAAKC,GACpBL,EAA2B,KAAlB17B,KAAK+F,IAAI81B,GAAmBA,EAAQtD,GAAW,EACxDqD,EAAcW,EAAM,GAAMA,EAAM,GAAMA,EAAMA,GAAO,GAGhD9xB,EAAMwmB,MACT+K,EAAKn9B,EAAOY,aAAa,aACzBgL,EAAMkyB,SAAW99B,EAAOwqB,aAAa,YAAa,MAASgN,GAAiBrE,GAAqBnzB,EAAQmyB,KACzGgL,GAAMn9B,EAAOwqB,aAAa,YAAa2S,KAInB,GAAlBh8B,KAAK+F,IAAI21B,IAAe17B,KAAK+F,IAAI21B,GAAS,MACzCe,GACHrM,IAAW,EACXsL,GAAUH,GAAY,EAAK,KAAO,IAClCA,GAAaA,GAAY,EAAK,KAAO,MAErClL,IAAW,EACXqL,GAAUA,GAAS,EAAK,KAAO,MAGjCpP,EAAUA,GAAW7hB,EAAM6hB,QAC3B7hB,EAAMW,EAAIA,IAAMX,EAAMmyB,SAAWxxB,KAAQkhB,GAAW7hB,EAAMmyB,WAAc58B,KAAKC,MAAMpB,EAAOg+B,YAAc,KAAO78B,KAAKC,OAAOmL,IAAM,GAAK,KAAOvM,EAAOg+B,YAAcpyB,EAAMmyB,SAAW,IAAM,GAxInL,KAyINnyB,EAAMY,EAAIA,IAAMZ,EAAMqyB,SAAWzxB,KAAQihB,GAAW7hB,EAAMqyB,WAAc98B,KAAKC,MAAMpB,EAAOk+B,aAAe,KAAO/8B,KAAKC,OAAOoL,IAAM,GAAK,KAAOxM,EAAOk+B,aAAetyB,EAAMqyB,SAAW,IAAM,GAzIrL,KA0INryB,EAAM6wB,EAAIA,EA1IJ,KA2IN7wB,EAAM2lB,OAASrwB,GAAOqwB,GACtB3lB,EAAM4lB,OAAStwB,GAAOswB,GACtB5lB,EAAM8wB,SAAWx7B,GAAOw7B,GAAYb,EACpCjwB,EAAM+wB,UAAYz7B,GAAOy7B,GAAad,EACtCjwB,EAAMgxB,UAAY17B,GAAO07B,GAAaf,EACtCjwB,EAAMixB,MAAQA,EAAQhB,EACtBjwB,EAAMkxB,MAAQA,EAAQjB,EACtBjwB,EAAMuyB,qBAAuBpB,EAlJvB,MAmJDnxB,EAAMswB,QAAUv6B,WAAW02B,EAAOr3B,MAAM,KAAK,KAAO,KACxD4kB,EAAM0M,IAAwB2J,GAAc5D,IAE7CzsB,EAAMotB,QAAUptB,EAAMstB,QAAU,EAChCttB,EAAM6L,QAAUF,EAAQE,QACxB7L,EAAM8lB,gBAAkB9lB,EAAMwmB,IAAMgM,GAAuBtK,GAAcuK,GAAuBC,GAChG1yB,EAAM6hB,QAAU,EACT7hB,GAERqwB,GAAgB,SAAhBA,cAAgB/9B,UAAUA,EAAQA,EAAM8C,MAAM,MAAM,GAAK,IAAM9C,EAAM,IAKrEogC,GAAyB,SAAzBA,uBAA0Bzf,EAAOjT,GAChCA,EAAM6wB,EAAI,MACV7wB,EAAMgxB,UAAYhxB,EAAM+wB,UAAY,OACpC/wB,EAAM6L,QAAU,EAChB4mB,GAAqBxf,EAAOjT,IAE7B2yB,GAAW,OACXC,GAAU,MACVC,GAAkB,KAClBJ,GAAuB,SAAvBA,qBAAgCxf,EAAOjT,SAC4GA,GAAS0S,KAAtJyf,IAAAA,SAAUE,IAAAA,SAAU1xB,IAAAA,EAAGC,IAAAA,EAAGiwB,IAAAA,EAAGC,IAAAA,SAAUE,IAAAA,UAAWD,IAAAA,UAAWE,IAAAA,MAAOC,IAAAA,MAAOvL,IAAAA,OAAQC,IAAAA,OAAQ2M,IAAAA,qBAAsB1mB,IAAAA,QAASzX,IAAAA,OAAQk8B,IAAAA,QACtInC,EAAa,GACb2E,EAAqB,SAAZjnB,GAAsBoH,GAAmB,IAAVA,IAA4B,IAAZpH,KAGrDykB,IAAYS,IAAc4B,IAAY3B,IAAc2B,IAAW,KAIjElmB,EAHG2kB,EAAQr7B,WAAWi7B,GAAazB,GACnCmC,EAAMn8B,KAAKmX,IAAI0kB,GACfQ,EAAMr8B,KAAKkX,IAAI2kB,GAEhBA,EAAQr7B,WAAWg7B,GAAaxB,GAChC9iB,EAAMlX,KAAKkX,IAAI2kB,GACfzwB,EAAI+sB,GAAgBt5B,EAAQuM,EAAG+wB,EAAMjlB,GAAO6jB,GAC5C1vB,EAAI8sB,GAAgBt5B,EAAQwM,GAAIrL,KAAKmX,IAAI0kB,IAAUd,GACnDO,EAAInD,GAAgBt5B,EAAQy8B,EAAGe,EAAMnlB,GAAO6jB,EAAUA,GAGnDiC,IAAyBK,KAC5BzE,GAAc,eAAiBoE,EAAuBM,KAEnDV,GAAYE,KACflE,GAAc,aAAegE,EAAW,MAAQE,EAAW,QAExDS,GAASnyB,IAAMiyB,IAAWhyB,IAAMgyB,IAAW/B,IAAM+B,KACpDzE,GAAe0C,IAAM+B,IAAWE,EAAS,eAAiBnyB,EAAI,KAAOC,EAAI,KAAOiwB,EAAI,KAAO,aAAelwB,EAAI,KAAOC,EAAIiyB,IAEtH/B,IAAa6B,KAChBxE,GAAc,UAAY2C,EAAW+B,IAElC7B,IAAc2B,KACjBxE,GAAc,WAAa6C,EAAY6B,IAEpC9B,IAAc4B,KACjBxE,GAAc,WAAa4C,EAAY8B,IAEpC5B,IAAU0B,IAAYzB,IAAUyB,KACnCxE,GAAc,QAAU8C,EAAQ,KAAOC,EAAQ2B,IAEjC,IAAXlN,GAA2B,IAAXC,IACnBuI,GAAc,SAAWxI,EAAS,KAAOC,EAASiN,IAEnDz+B,EAAO4lB,MAAMuM,IAAkB4H,GAAc,mBAE9CqE,GAAuB,SAAvBA,qBAAgCvf,EAAOjT,OAIrC+yB,EAAKC,EAAK3B,EAAKC,EAAKzM,IAH0G7kB,GAAS0S,KAAnIyf,IAAAA,SAAUE,IAAAA,SAAU1xB,IAAAA,EAAGC,IAAAA,EAAGkwB,IAAAA,SAAUG,IAAAA,MAAOC,IAAAA,MAAOvL,IAAAA,OAAQC,IAAAA,OAAQxxB,IAAAA,OAAQ44B,IAAAA,QAASE,IAAAA,QAASE,IAAAA,QAASE,IAAAA,QAAS4E,IAAAA,SAClH3E,EAAKx3B,WAAW4K,GAChB6sB,EAAKz3B,WAAW6K,GAEjBkwB,EAAW/6B,WAAW+6B,GACtBG,EAAQl7B,WAAWk7B,IACnBC,EAAQn7B,WAAWm7B,MAGlBD,GADAC,EAAQn7B,WAAWm7B,GAEnBJ,GAAYI,GAETJ,GAAYG,GACfH,GAAYvB,GACZ0B,GAAS1B,GACTwD,EAAMx9B,KAAKkX,IAAIqkB,GAAYnL,EAC3BqN,EAAMz9B,KAAKmX,IAAIokB,GAAYnL,EAC3B0L,EAAM97B,KAAKmX,IAAIokB,EAAWG,IAAUrL,EACpC0L,EAAM/7B,KAAKkX,IAAIqkB,EAAWG,GAASrL,EAC/BqL,IACHC,GAAS3B,GACT1K,EAAOtvB,KAAK09B,IAAIhC,EAAQC,GAExBG,GADAxM,EAAOtvB,KAAKgX,KAAK,EAAIsY,EAAOA,GAE5ByM,GAAOzM,EACHqM,IACHrM,EAAOtvB,KAAK09B,IAAI/B,GAEhB6B,GADAlO,EAAOtvB,KAAKgX,KAAK,EAAIsY,EAAOA,GAE5BmO,GAAOnO,IAGTkO,EAAMz9B,GAAOy9B,GACbC,EAAM19B,GAAO09B,GACb3B,EAAM/7B,GAAO+7B,GACbC,EAAMh8B,GAAOg8B,KAEbyB,EAAMpN,EACN2L,EAAM1L,EACNoN,EAAM3B,EAAM,IAER9D,MAAS5sB,EAAI,IAAItK,QAAQ,OAAWm3B,MAAS5sB,EAAI,IAAIvK,QAAQ,SACjEk3B,EAAKrD,GAAe91B,EAAQ,IAAKuM,EAAG,MACpC6sB,EAAKtD,GAAe91B,EAAQ,IAAKwM,EAAG,QAEjCosB,GAAWE,GAAWE,GAAWE,KACpCC,EAAKj4B,GAAOi4B,EAAKP,GAAWA,EAAU+F,EAAM7F,EAAUmE,GAAOjE,GAC7DI,EAAKl4B,GAAOk4B,EAAKN,GAAWF,EAAUgG,EAAM9F,EAAUoE,GAAOhE,KAE1D6E,GAAYE,KAEfxN,EAAOzwB,EAAO20B,UACdwE,EAAKj4B,GAAOi4B,EAAK4E,EAAW,IAAMtN,EAAK4E,OACvC+D,EAAKl4B,GAAOk4B,EAAK6E,EAAW,IAAMxN,EAAK6E,SAExC7E,EAAO,UAAYkO,EAAM,IAAMC,EAAM,IAAM3B,EAAM,IAAMC,EAAM,IAAM/D,EAAK,IAAMC,EAAK,IACnFp5B,EAAOwqB,aAAa,YAAaiG,GACjCqN,IAAa99B,EAAO4lB,MAAMuM,IAAkB1B,IAsE9C5vB,GAAa,8BAA+B,SAACpB,EAAMiP,OAEjDmD,EAAI,QACJxE,EAAI,SACJrL,EAAI,OACJ2hB,GAASjV,EAAQ,EAAI,CAJd,MAIiBmD,EAAExE,EAAErL,GAAK,CAJ1B,MAI6BA,EAJ7B,MAIkC6P,EAAGxE,EAAEwE,EAAGxE,EAAErL,IAAIqQ,IAAI,SAAAysB,UAAQpwB,EAAQ,EAAIjP,EAAOq/B,EAAO,SAAWA,EAAOr/B,IAChH08B,GAAuB,EAARztB,EAAY,SAAWjP,EAAOA,GAAS,SAAS8lB,EAAQvlB,EAAQd,EAAU83B,EAAU70B,OAC9FE,EAAG6B,KACHwa,UAAUte,OAAS,SACtBiC,EAAIshB,EAAMtR,IAAI,SAAArB,UAAQkhB,GAAK3M,EAAQvU,EAAM9R,KAEN,KADnCgF,EAAO7B,EAAE4Q,KAAK,MACFjS,MAAMqB,EAAE,IAAIjC,OAAeiC,EAAE,GAAK6B,EAE/C7B,GAAK20B,EAAW,IAAIh2B,MAAM,KAC1BkD,EAAO,GACPyf,EAAM1iB,QAAQ,SAAC+P,EAAMjR,UAAMmE,EAAK8M,GAAQ3O,EAAEtC,GAAKsC,EAAEtC,IAAMsC,GAAKtC,EAAI,GAAK,EAAK,KAC1EwlB,EAAOzV,KAAK9P,EAAQkE,EAAM/B,UAoLlB48B,GAAkBrC,GACvBsC,GAhLQC,GAAY,CACxBx/B,KAAM,MACNmR,SAAU6iB,GACVpzB,+BAAWL,UACHA,EAAO4lB,OAAS5lB,EAAO2K,UAE/BmF,mBAAK9P,EAAQkE,EAAM/B,EAAOuM,EAAO7O,OAI/Bk3B,EAAYC,EAAUlQ,EAAQE,EAAUnd,EAAMq1B,EAAa17B,EAAG0zB,EAAWD,EAASkI,EAAUC,EAAoBC,EAAoBzzB,EAAO2sB,EAAQ9Q,EAAa6X,EAH7J3b,EAAQrF,KAAKvO,OAChB6V,EAAQ5lB,EAAO4lB,MACfzb,EAAUhI,EAAM+B,KAAKiG,YAOjB3G,KALLuwB,IAAkBN,UAEb8L,OAASjhB,KAAKihB,QAAU3M,GAAe5yB,GAC5Cs/B,EAAchhB,KAAKihB,OAAO5b,WACrBxhB,MAAQA,EACH+B,KACC,cAANV,IAGJwzB,EAAW9yB,EAAKV,IACZsN,GAAStN,KAAM8hB,GAAa9hB,EAAGU,EAAM/B,EAAOuM,EAAO1O,EAAQH,OAG/DgK,SAAcmtB,EACdkI,EAAc/C,GAAc34B,GACf,aAATqG,IAEHA,SADAmtB,EAAWA,EAAS9c,KAAK/X,EAAOuM,EAAO1O,EAAQH,KAGnC,WAATgK,IAAsBmtB,EAAS/0B,QAAQ,aAC1C+0B,EAAWroB,GAAeqoB,IAEvBkI,EACHA,EAAY5gB,KAAMte,EAAQwD,EAAGwzB,EAAU70B,KAAWslB,EAAc,QAC1D,GAAsB,OAAlBjkB,EAAE5B,OAAO,EAAE,GACrBm1B,GAAczD,iBAAiBtzB,GAAQuzB,iBAAiB/vB,GAAK,IAAIF,OACjE0zB,GAAY,GACZvkB,GAAUa,UAAY,EACjBb,GAAUc,KAAKwjB,KACnBG,EAAY5sB,GAAQysB,GACpBE,EAAU3sB,GAAQ0sB,IAEnBC,EAAUC,IAAcD,IAAYF,EAAajB,GAAe91B,EAAQwD,EAAGuzB,EAAYE,GAAWA,GAAWC,IAAcF,GAAYE,QAClIxvB,IAAIke,EAAO,cAAemR,EAAYC,EAAUtoB,EAAO7O,EAAS,EAAG,EAAG2D,GAC3EmgB,EAAMxa,KAAK3F,GACX87B,EAAYn2B,KAAK3F,EAAG,EAAGoiB,EAAMpiB,SACvB,GAAa,cAATqG,EAAsB,IAC5BM,GAAW3G,KAAK2G,GACnB4sB,EAAoC,mBAAhB5sB,EAAQ3G,GAAqB2G,EAAQ3G,GAAG0W,KAAK/X,EAAOuM,EAAO1O,EAAQH,GAAWsK,EAAQ3G,GAC1GvF,EAAU84B,KAAgBA,EAAW90B,QAAQ,aAAe80B,EAAapoB,GAAeooB,IACxFzsB,GAAQysB,EAAa,MAAQA,GAAcxf,EAAQI,MAAMnU,IAAM8G,GAAQ4nB,GAAKlyB,EAAQwD,KAAO,IAC3D,OAA/BuzB,EAAa,IAAIt1B,OAAO,KAAes1B,EAAa7E,GAAKlyB,EAAQwD,KAElEuzB,EAAa7E,GAAKlyB,EAAQwD,GAE3BwjB,EAAWrlB,WAAWo1B,IACtBoI,EAAqB,WAATt1B,GAA4C,MAAvBmtB,EAASv1B,OAAO,IAAeu1B,EAASp1B,OAAO,EAAG,MACtEo1B,EAAWA,EAASp1B,OAAO,IACxCklB,EAASnlB,WAAWq1B,GAChBxzB,KAAKwuB,KACE,cAANxuB,IACc,IAAbwjB,GAAiD,WAA/BkL,GAAKlyB,EAAQ,eAA8B8mB,IAChEE,EAAW,GAEZsY,EAAYn2B,KAAK,aAAc,EAAGyc,EAAM4Z,YACxC7J,GAAkBrX,KAAMsH,EAAO,aAAcoB,EAAW,UAAY,SAAUF,EAAS,UAAY,UAAWA,IAErG,UAANtjB,GAAuB,cAANA,KACpBA,EAAIwuB,GAAiBxuB,IAClBvB,QAAQ,OAASuB,EAAIA,EAAExC,MAAM,KAAK,KAIvCo+B,EAAsB57B,KAAKsuB,WAIrByN,OAAOxM,KAAKvvB,GACZ67B,KACJzzB,EAAQ5L,EAAOC,OACRyxB,kBAAoBxtB,EAAKu7B,gBAAmBlI,GAAgBv3B,EAAQkE,EAAKu7B,gBAChFlH,GAAgC,IAAtBr0B,EAAKw7B,cAA0B9zB,EAAM2sB,QAC/C8G,EAAqB/gB,KAAKxV,IAAM,IAAIqI,GAAUmN,KAAKxV,IAAK8c,EAAOuM,GAAgB,EAAG,EAAGvmB,EAAM8lB,gBAAiB9lB,EAAO,GAAI,IACpGkf,IAAM,GAEhB,UAANtnB,OACEsF,IAAM,IAAIqI,GAAUmN,KAAKxV,IAAK8C,EAAO,SAAUA,EAAM4lB,QAAU2N,EAAW79B,GAAesK,EAAM4lB,OAAQ2N,EAAWrY,GAAUA,GAAUlb,EAAM4lB,QAAW,EAAGZ,SAC1J9nB,IAAIwE,EAAI,EACbqW,EAAMxa,KAAK,SAAU3F,GACrBA,GAAK,QACC,CAAA,GAAU,oBAANA,EAAyB,CACnC87B,EAAYn2B,KAAKmpB,GAAsB,EAAG1M,EAAM0M,KAChD0E,EAAWG,GAA8BH,GACrCprB,EAAMwmB,IACTgG,GAAgBp4B,EAAQg3B,EAAU,EAAGuB,EAAQ,EAAGja,QAEhD2Y,EAAUt1B,WAAWq1B,EAASh2B,MAAM,KAAK,KAAO,KACpC4K,EAAMswB,SAAWvG,GAAkBrX,KAAM1S,EAAO,UAAWA,EAAMswB,QAASjF,GACtFtB,GAAkBrX,KAAMsH,EAAOpiB,EAAGy4B,GAAclF,GAAakF,GAAcjF,cAGtE,GAAU,cAANxzB,EAAmB,CAC7B40B,GAAgBp4B,EAAQg3B,EAAU,EAAGuB,EAAQ,EAAGja,eAE1C,GAAI9a,KAAKg5B,GAAuB,CACtCjD,GAAwBjb,KAAM1S,EAAOpI,EAAGwjB,EAAUmY,EAAW79B,GAAe0lB,EAAUmY,EAAWnI,GAAYA,YAGvG,GAAU,iBAANxzB,EAAsB,CAChCmyB,GAAkBrX,KAAM1S,EAAO,SAAUA,EAAM2sB,OAAQvB,YAEjD,GAAU,YAANxzB,EAAiB,CAC3BoI,EAAMpI,GAAKwzB,WAEL,GAAU,cAANxzB,EAAmB,CAC7Bs2B,GAAoBxb,KAAM0Y,EAAUh3B,kBAGzBwD,KAAKoiB,IACjBpiB,EAAIgwB,GAAiBhwB,IAAMA,MAGxB47B,IAAwBtY,GAAqB,IAAXA,KAAkBE,GAAyB,IAAbA,KAAoBsU,GAAY/nB,KAAKyjB,IAAcxzB,KAAKoiB,EAEhHkB,EAAXA,GAAoB,GADpBoQ,GAAaH,EAAa,IAAIn1B,QAAQolB,EAAW,IAAI5mB,YAErD62B,EAAU3sB,GAAQ0sB,KAAexzB,KAAK+T,EAAQI,MAASJ,EAAQI,MAAMnU,GAAK0zB,MAChDlQ,EAAW8O,GAAe91B,EAAQwD,EAAGuzB,EAAYE,SACtEnuB,IAAM,IAAIqI,GAAUmN,KAAKxV,IAAKs2B,EAAqBxzB,EAAQga,EAAOpiB,EAAGwjB,GAAWmY,EAAW79B,GAAe0lB,EAAUmY,EAAWrY,GAAUA,GAAUE,EAAYoY,GAAmC,OAAZnI,GAA0B,WAANzzB,IAAsC,IAAnBU,EAAKy7B,UAA+C/O,GAAxBG,SACzPjoB,IAAIwE,EAAI2pB,GAAW,EACpBC,IAAcD,GAAuB,MAAZA,SACvBnuB,IAAIuE,EAAI0pB,OACRjuB,IAAI+I,EAAIif,SAER,GAAMttB,KAAKoiB,EAQjBiR,GAAuB3c,KAAKoE,KAAMte,EAAQwD,EAAGuzB,EAAYoI,EAAWA,EAAWnI,EAAWA,WAPtFxzB,KAAKxD,OACH0H,IAAI1H,EAAQwD,EAAGuzB,GAAc/2B,EAAOwD,GAAI27B,EAAWA,EAAWnI,EAAWA,EAAUtoB,EAAO7O,QACzF,GAAU,mBAAN2D,EAAwB,CAClCvE,EAAeuE,EAAGwzB,YAMpBoI,IAAuB57B,KAAKoiB,EAAQ0Z,EAAYn2B,KAAK3F,EAAG,EAAGoiB,EAAMpiB,IAAM87B,EAAYn2B,KAAK3F,EAAG,EAAGuzB,GAAc/2B,EAAOwD,KACnHmgB,EAAMxa,KAAK3F,GAGbikB,GAAeY,GAA0B/J,OAG1C7b,uBAAOoc,EAAOxF,MACTA,EAAKlX,MAAMoF,QAAUxE,aACpByiB,EAAKnM,EAAKvQ,IACP0c,GACNA,EAAG3T,EAAEgN,EAAO2G,EAAG/Y,GACf+Y,EAAKA,EAAGrgB,WAGTkU,EAAKkmB,OAAOr5B,UAGdsK,IAAK0hB,GACLvhB,QAASqhB,GACTvhB,6BAAUzQ,EAAQd,EAAUqmB,OACvB/hB,EAAIwuB,GAAiB9yB,UACxBsE,GAAKA,EAAEvB,QAAQ,KAAO,IAAO/C,EAAWsE,GACjCtE,KAAY4yB,IAAmB5yB,IAAaozB,KAAyBtyB,EAAOC,MAAMsM,GAAK2lB,GAAKlyB,EAAQ,MAAUulB,GAAU2U,KAAwB3U,EAAuB,UAAbrmB,EAAuBoyB,GAAeD,IAAqB6I,GAAsB3U,GAAU,MAAqB,UAAbrmB,EAAuBuyB,GAAyBE,IAA+B3xB,EAAO4lB,QAAUvnB,EAAa2B,EAAO4lB,MAAM1mB,IAAagyB,IAAmBhyB,EAAS+C,QAAQ,KAAOkvB,GAAiBzgB,GAAW1Q,EAAQd,IAE5d+wB,KAAM,CAAEwF,gBAAAA,GAAiBmC,WAAAA,KAI1B54B,GAAK4vB,MAAMgR,YAAcpM,GACzBx0B,GAAKixB,KAAK4P,cAAgBjN,GAErBoM,GAAMn+B,IADDk+B,GAQP,+CAPwC,KADfrC,GAQsB,4CAPU,iFAAc,SAAAj9B,GAASqyB,GAAgBryB,GAAQ,IAC1GoB,GAAa67B,GAAU,SAAAj9B,GAAS8X,EAAQI,MAAMlY,GAAQ,MAAO+8B,GAAsB/8B,GAAQ,IAC3FuyB,GAAiBgN,GAAI,KAAOD,GAAmB,IAAMrC,GACrD77B,GAI8K,6FAJxJ,SAAApB,OACjBuB,EAAQvB,EAAKuB,MAAM,KACvBgxB,GAAiBhxB,EAAM,IAAMg+B,GAAIh+B,EAAM,MAGzCH,GAAa,+EAAgF,SAAApB,GAAS8X,EAAQI,MAAMlY,GAAQ,OAE5HT,GAAKquB,eAAe4R,QC/mCda,GAAc9gC,GAAKquB,eAAe4R,KAAcjgC,GACrD+gC,GAAkBD,GAAY7P,KAAK7lB"}