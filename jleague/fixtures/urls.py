from django.urls import path, reverse
from django.views.generic.base import RedirectView
from utils.helpers import get_display_data_year
from wagtail.contrib.sitemaps.views import sitemap
from . import views
from .sitemaps import sitemaps


def _fixtures_stage_url(*args, **kwargs):
    params = {
        "competition_slug": kwargs["competition_slug"],
        "year": get_display_data_year(),
    }
    if kwargs.get("occasion_no"):
        pattern_name = "fixtures_stage_tournament"
        params["occasion_no"] = kwargs["occasion_no"]
    elif kwargs.get("stage") and kwargs.get("stage_round"):
        pattern_name = "fixtures_stage_cup"
        params["stage"] = kwargs["stage"]
        params["stage_round"] = kwargs["stage_round"]
    return reverse(pattern_name, kwargs=params)


def _redirect_search_url(*args, **kwargs):
    return reverse("fixtures")


urlpatterns = [
    # sitemap
    path("fixtures/sitemap.xml", sitemap, {"sitemaps": sitemaps}),
    # ajax
    path(
        "fixtures/data/<slug:competition_slug>/<int:year>/<slug:month>/<slug:club_slug>/",
        views.fixtures_data,
    ),
    path(
        "fixtures/data/stage/<slug:competition_slug>/<int:year>/<str:stage>/<int:stage_round>/<slug:club_slug>/",
        views.fixtures_stage_data,
    ),
    path(
        "fixtures/data/stage/<slug:competition_slug>/<int:year>/<int:occasion_no>/<slug:club_slug>/",
        views.fixtures_stage_data,
    ),
    path(
        "tickets/data/<slug:competition_slug>/",
        views.tickets_data,
    ),
    # redirect
    path(
        "fixtures/section/<slug:competition_slug>/<str:stage>/<int:stage_round>/",
        RedirectView.as_view(get_redirect_url=_fixtures_stage_url, permanent=True),
    ),
    path(
        "fixtures/section/<slug:competition_slug>/<int:occasion_no>/",
        RedirectView.as_view(get_redirect_url=_fixtures_stage_url, permanent=True),
    ),
    path(
        "fixtures/search/<str:slug>/<str:latest>/",
        RedirectView.as_view(get_redirect_url=_redirect_search_url, permanent=True),
    ),
    path(
        "fixtures/search/<str:all>/<int:id>/<str:name>/",
        RedirectView.as_view(get_redirect_url=_redirect_search_url, permanent=True),
    ),
    # pages
    path("fixtures/", views.fixtures, name="fixtures"),
    path(
        "fixtures/stage/<slug:competition_slug>/<int:year>/<str:stage>/<int:stage_round>/",
        views.fixtures_stage,
        name="fixtures_stage_cup",
    ),
    path(
        "fixtures/stage/<slug:competition_slug>/<int:year>/<str:stage>/<int:stage_round>/<slug:club_slug>/",
        views.fixtures_stage,
        name="fixtures_stage_cup_team",
    ),
    path(
        "fixtures/stage/<slug:competition_slug>/<int:year>/<int:occasion_no>/",
        views.fixtures_stage,
        name="fixtures_stage_tournament",
    ),
    path(
        "fixtures/stage/<slug:competition_slug>/<int:year>/<int:occasion_no>/<slug:club_slug>/",
        views.fixtures_stage,
        name="fixtures_stage_tournament_team",
    ),
    path(
        "fixtures/<slug:competition_slug>/<int:year>/<slug:month>/",
        views.fixtures,
        name="fixtures_search",
    ),
    path(
        "fixtures/<slug:competition_slug>/<int:year>/<slug:month>/<slug:club_slug>/",
        views.fixtures,
        name="fixtures_search_team",
    ),
    path(
        "tickets/",
        views.tickets,
        name="tickets",
    ),
    path(
        "tickets/<slug:competition_slug>/",
        views.tickets,
        name="tickets_search",
    ),
]
