@import 'setup/variable';

.cookie-settings {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: block;
    background-color: transparentize($color: $color-black, $amount: 0.5);
    z-index: 1;
  }

  &.cookie-settings--closed {
    display: none;
  }

  .cookie-settings__content {
    background-color: $color-white;
    z-index: 2;
    flex: 0 0 522px;
    max-height: 80%;
    overflow: hidden;

    .setting-header {
      padding: 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        font-family: $font-overpass;
        font-size: 28px;
        line-height: 32px;
        font-weight: 900;
      }

      .close {
        background: transparent;
        border: none;
        opacity: 1;

        &:hover {
          cursor: pointer;
          opacity: 0.6;
        }
      }
    }

    .setting-body {
      font-family: $font-overpass;
      font-style: normal;
      font-weight: 400;
      font-size: 16px;
      line-height: 20px;
      max-height: 600px;
      padding: 0 24px 24px;
      overflow-y: auto;
      @include scrollbars();
    }

    .setting-footer {
      padding: 24px;
      border: 1px solid $color-earth-2;
    }

    .cookie-group {
      display: flex;
      flex-direction: column;
      gap: 20px;
      margin-top: 20px;

      .accordion {
        .accordion-header {
          display: flex;
          justify-content: space-between;
          padding: 14px;
          background: $color-earth-1;
          border: solid 1px $color-earth-3;
          cursor: pointer;

          .icon,
          .title {
            pointer-events: none;
          }

          .title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-style: normal;
            font-weight: 700;
            font-size: 20px;
            line-height: 1;
          }

          .icon {
            display: flex;
            align-items: center;
            width: 24px;
            height: 24px;

            .icon__closed,
            .icon__open {
              width: 100%;
              height: auto;
            }

            .icon__closed {
              display: block;
            }
            .icon__open {
              display: none;
            }
          }
        }

        .accordion-body {
          padding: 20px;
          border: solid 1px $color-earth-3;
          border-top: none;
          display: none;
        }

        &.accordion--open {
          .accordion-header {
            .icon {
              .icon__closed {
                display: none;
              }
              .icon__open {
                display: block;
              }
            }
          }

          .accordion-body {
            display: block;
          }
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 19px;
      justify-content: flex-end;

      .jl-button {
        height: 40px;
        border-radius: 20px;
        padding: 10px 24px;

        .jl-button__label {
          font-size: 16px;
          line-height: 20px;
        }
      }
    }
  }

  @media screen and (max-width: 600px) {
    .cookie-settings__content {
      width: 100%;
      flex: auto;
      max-height: none;

      .setting-body {
        max-height: none;
        height: calc(100vh - 80px - 90px);
      }

      .action-buttons {
        justify-content: center;
      }
    }
  }

  @media screen and (max-height: 900px) {
    .cookie-settings__content {
      max-height: none;

      .setting-body {
        max-height: none;
        height: calc(90vh - 80px - 90px);
      }
    }
  }
}

body.lang-th {
  .cookie-settings .cookie-settings__content {
    .setting-header .title,
    .setting-body,
    .cookie-group .accordion .accordion-header .title .cookie-group .accordion .accordion-body,
    .jl-button .jl-button__label {
      @include font-thai();
    }

    .setting-header .title {
      font-weight: 700;
    }
  }
}
