{% extends 'base.html' %}

{% load pipeline static core_tags %}

{% block extra_styles %}
  {% stylesheet 'club-profile-page' %}
{% endblock %}

{% block schemaorg %}
<script type="application/ld+json">
  {
    "@context": "http://schema.org",
    "@type": "SportsTeam",
    "name": "{{ club.name }}",
    "url": "https://www.jleague.co/clubs/{{ club.slug }}",
    "logo": "{{ club.logo_url }}",
    "sport": "Soccer",
    "foundingDate": "{{ club.founding_date }}",
    "memberOf": {
      "@type": "SportsOrganization",
      "name": "J.League",
      "url": "https://www.jleague.co"
    },
    "location": {
      "@type": "Place",
      "name": "{{ club.stadium.name }}",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "{{ club.stadium.address.street }}",
        "addressLocality": "{{ club.stadium.address.city }}",
        "addressRegion": "{{ club.stadium.address.region }}",
        "postalCode": "{{ club.stadium.address.postal_code }}",
        "addressCountry": "{{ club.stadium.address.country }}"
      }
    }
  }
  </script>
{% endblock schemaorg %}


{% block extra_vendor_scripts %}
  {% javascript 'magnific' %}
{% endblock %}

{% block extra_scripts %}
  <script type="text/javascript">
    const selectedTeam = '{{ selected_team }}';
    const selectedClubId = {{ club.id }};
    let playersSelectedYear = {{ selected_year }};
    let statsSelectedYear = {{ selected_year }};
  </script>
  {% javascript 'club-profile-page' %}
{% endblock %}

{% block active_body_class %}body-club-profile{% endblock %}

{% block content %}
  <div class="club-profile">
    {% include 'clubs/components/club_profile_match_stats.html' %}

    <div class="goal-highlights-container">
      <div class="goal-highlights-content d-none">
        {% include 'components/goal-highlights/goal-highlights.html' with title_translation_key='HOME_PAGE_LATEST_GOAL_HIGHLIGHTS' hide_competition_selector=True %}
      </div>
    </div>

    {% if latest_video or video|length > 0  %}
      <div class="club-profile-videos">
        <div class="content-container">
          <div class="club-profile-videos__header">
            <h3 class="club-profile-videos__header__title">
              {% translation 'CLUB_PROFILE_PAGE_LATEST_VIDEOS' %}
            </h3>
          </div>

          {% include 'components/latest-videos.html' with highlight=latest_video videos=videos hide_header=True custom_class='m-t-0' %}
        </div>
      </div>
    {% endif %}

    <div class="club-profile-nav-sticky-checker"></div>
    <div class="club-profile-nav">
      <a
        class="btn-nav large active"
        href="{% url 'club_profile' club.name_slug %}#players"
        data-section="players"
        onclick="sendSelectContentEvent('club_players', '{{ club.slug }}');"
      >
        {% translation 'CLUB_PROFILE_PAGE_PLAYERS' %}
      </a>

      <a
        class="btn-nav large"
        href="{% url 'club_profile' club.name_slug %}#statistics"
        data-section="statistics"
        onclick="sendSelectContentEvent('club_stats', '{{ club.slug }}');"
      >
        {% translation 'CLUB_PROFILE_PAGE_STATS' %}
      </a>

      <a
        class="btn-nav large"
        href="{% url 'club_profile' club.name_slug %}#bio"
        data-section="bio"
        onclick="sendSelectContentEvent('club_bio', '{{ club.slug }}');"
      >
        {% translation 'CLUB_PROFILE_PAGE_BIO' %}
      </a>

      <a
        class="btn-nav large"
        href="{% url 'club_profile' club.name_slug %}#stadiums"
        data-section="stadiums"
        onclick="sendSelectContentEvent('club_stadiums', '{{ club.slug }}');"
      >
        {% translation 'CLUB_PROFILE_PAGE_STADIUMS' %}
      </a>

      <a
        class="btn-nav large"
        href="{% url 'club_profile' club.name_slug %}#mascots"
        data-section="mascots"
        onclick="sendSelectContentEvent('club_mascots', '{{ club.slug }}');"
      >
        {% translation 'CLUB_PROFILE_PAGE_MASCOT' %}
      </a>
    </div>

    {% include 'clubs/components/club_profile_players.html' %}
    {% include 'clubs/components/club_profile_statistics.html' %}
    {% include 'clubs/components/club_profile_bio.html' %}
    {% include 'clubs/components/club_profile_stadiums.html' %}
    {% include 'clubs/components/club_profile_mascots.html' %}
  </div>
{% endblock %}
