from core.constants import (
    ALLOW_COMPETITIONS,
    CUP_COMPETITIONS,
    LEAGUE_COMPETITIONS,
    PLAY_OFF_COMPETITIONS,
    PLAY_OFF_J2_COMPETITIONS,
    PLAY_OFF_J3_COMPETITIONS,
    ACL_COMPETITIONS,
)
from core.enums import GameKindEnum
from core.helpers import (
    get_translation_value,
    get_club_name,
    get_competition_name,
)
from django.core.cache import cache
from django.http import Http404
from django.shortcuts import redirect, render
from django.urls import reverse
from django.views.decorators.cache import never_cache
from utils.helpers import (
    get_current_month,
    get_display_data_year,
    get_month_selector_choices,
    get_year_selector_choices,
)
from utils.helpers.cache_util import get_fixture_cache_timeout
from utils.helpers.etc import (
    get_clubs_selector_choices,
    get_competition_selector_choices,
    map_competition_name,
)
from utils.helpers.seo import update_seo_data_django_view
from utils.helpers.view_context import simple_page_context

from .helper import (
    get_fixture_competitions,
    get_fixture_stage_selector_choices,
    get_fixtures,
    get_ticket_fixtures,
    humanize_stage_name,
)


def _is_valid_competition(competition_slug: str):
    allow_competitions = ["all"] + ALLOW_COMPETITIONS
    allow_competitions += ["afs"]  # !AFS temporary
    if competition_slug in allow_competitions:
        return True
    return False


@never_cache
def fixtures(
    request, competition_slug="all", year=None, month="latest", club_slug="all"
):
    if not _is_valid_competition(competition_slug):
        return redirect(to=reverse("fixtures"), permanent=True)

    year = year or get_display_data_year()
    month = None if month == "latest" else month
    competition_name = map_competition_name(competition_slug, year)
    context = simple_page_context("Fixtures & Results")
    context["header"]["title_translate_key"] = "MATCH_FIXTURES_AND_RESULTS"
    context["header"]["hide_matchweek_games"] = True
    context["selected_competition"] = competition_slug
    context["selected_competition_name"] = competition_name
    context["selected_month"] = month or "latest"
    context["selected_team"] = club_slug
    context["selected_year"] = year
    context["clubs"] = get_clubs_selector_choices(competition_slug, year)
    context["months"] = get_month_selector_choices()
    context["years"] = get_year_selector_choices()
    context["fixture_competitions"] = get_fixture_competitions(
        year, context["selected_month"]
    )
    # just keep this to avoid unexpected issue
    context["is_fixtures_stage"] = False
    context["selected_stage"] = None

    # the seo part should be located at the bottom
    month_seo = ""
    if context["months"]:
        for m in context["months"]:
            if m["value"] == month:
                month_seo = f'{get_translation_value(m["translation_key"])} '

    competition_title = ""
    competition_description = get_translation_value(
        "SEO_FIXTURES_PAGE_ALL_COMPETITIONS_DESCRIPTION"
    )
    if competition_slug == "all":
        all_competition_title = get_translation_value(
            "SEO_FIXTURES_PAGE_ALL_COMPETITIONS_TITLE"
        )
        competition_title = f"{all_competition_title} | "
    else:
        competitions = get_competition_selector_choices()
        if competitions:
            for gk in competitions:
                if gk["value"] == competition_slug:
                    competition = get_competition_name(gk)
                    competition_title = f"{competition} | "
                    competition_description = f"{competition}"
                    break

    club_title = ""
    club_description = ""
    if club_slug == "all":
        club_title = get_translation_value("SEO_FIXTURES_PAGE_ALL_CLUBS_TITLE")
        all_club_description = get_translation_value(
            "SEO_FIXTURES_PAGE_ALL_CLUBS_DESCRIPTION"
        )
        club_description = f" {all_club_description} "
    elif context["clubs"] and len(context["clubs"]):
        club_found = False
        for c in context["clubs"]:
            for child in c["children"]:
                if child["value"] == club_slug:
                    club = get_club_name(child)
                    club_title = f"{club}"
                    club_description = f" {club} "
                    club_found = True
                    break
            if club_found:
                break

    title = get_translation_value(
        "SEO_FIXTURES_PAGE_TITLE",
        competition=competition_title,
        club=club_title,
        year=str(year),
        month=month_seo,
    )
    description = get_translation_value(
        "SEO_FIXTURES_PAGE_DESCRIPTION",
        competition=competition_description,
        club=club_description,
        year=str(year),
        month=month_seo,
    )
    context = update_seo_data_django_view(
        context,
        {
            "title": title,
            "description": description,
        },
    )

    return render(request, "fixtures/fixtures_page.html", context)


@never_cache
def fixtures_stage(
    request,
    competition_slug="all",
    year=None,
    occasion_no=None,
    stage=None,
    stage_round=None,
    club_slug="all",
):
    if not _is_valid_competition(competition_slug):
        return redirect(to=reverse("fixtures"), permanent=True)

    year = year or get_display_data_year()
    month = get_current_month()
    context = simple_page_context("FIXTURES & RESULTS")
    context["header"]["title_translate_key"] = "MATCH_FIXTURES_AND_RESULTS"
    context["header"]["hide_matchweek_games"] = True
    context["selected_competition"] = competition_slug
    context["selected_month"] = month
    context["selected_team"] = club_slug
    context["selected_year"] = year
    context["clubs"] = get_clubs_selector_choices(competition_slug, year)
    context["months"] = get_month_selector_choices(True)
    context["stages"] = get_fixture_stage_selector_choices(competition_slug, year)
    context["years"] = get_year_selector_choices()
    context["fixture_competitions"] = get_fixture_competitions(
        year, context["selected_month"]
    )
    context["is_fixtures_stage"] = True

    if occasion_no is not None:
        context["selected_stage"] = occasion_no
    elif stage is not None and stage_round is not None:
        context["selected_stage"] = f"{stage}|{stage_round}"

    stage_name_lines = []
    if occasion_no is not None and competition_slug in LEAGUE_COMPETITIONS:
        stage_name_lines = humanize_stage_name(
            competition_slug, None, occasion_no, False, year
        )
    elif (
        stage is not None
        and stage_round is not None
        and competition_slug in CUP_COMPETITIONS
    ):
        stage_name_lines = humanize_stage_name(
            competition_slug, stage, stage_round, False, year
        )
    context["stage_name"] = ", ".join(i for i in stage_name_lines if i)

    # the seo part should be located at the bottom
    title = f"{year} Stage Fixtures & Results | J.LEAGUE"
    description = f'Follow all J.LEAGUE {context["stage_name"]} fixtures & results of {year} season, including J1, J2, J3, and cup competitions on the official website of J.LEAGUE.'

    if competition_slug == "all":
        title = "All competitions | " + title
    else:
        competitions = get_competition_selector_choices()
        if competitions:
            for gk in competitions:
                if gk["value"] == competition_slug:
                    title = f'{gk["label"]} | ' + title
                    description = description.replace(
                        ", including J1, J2, J3, and cup competitions", ""
                    )
                    description = description.replace(
                        "results of", f'results of {gk["label"]}'
                    )
                    break

    if club_slug == "all":
        title = "All clubs | " + title
    elif context["clubs"] and len(context["clubs"]):
        club_found = False
        for c in context["clubs"]:
            for child in c["children"]:
                if child["value"] == club_slug:
                    title = f'{child["label"]} | ' + title
                    description = description.replace(
                        "Follow all J.LEAGUE", f'Follow all {child["label"]}'
                    )
                    club_found = True
                    break
            if club_found:
                break

    context = update_seo_data_django_view(
        context,
        {
            "title": title,
            "description": description,
        },
    )

    return render(request, "fixtures/fixtures_page.html", context)


@never_cache
def fixtures_data(
    request, competition_slug="all", year=None, month="latest", club_slug="all"
):
    if not _is_valid_competition(competition_slug):
        raise Http404()

    user_tz = request.TIMEZONE
    cache_key = f"fixtures_data_{competition_slug}_{year}_{month}_{club_slug}_{user_tz}_{request.LANGUAGE_CODE}"
    context = cache.get(cache_key)

    if not context:
        year = year or get_display_data_year()
        competition_name = map_competition_name(competition_slug, year)
        context = {
            "selected_competition": competition_slug,
            "selected_competition_name": competition_name,
            "selected_month": month or "latest",
            "selected_team": club_slug,
            "selected_year": year,
        }
        fixture_options = {
            "competition_slug": competition_slug,
            "year": year,
            "month": month,
            "club_slug": club_slug,
            "timezone": user_tz,
        }

        if competition_slug == "afs":  # !AFS Temporary
            fixture_options = {
                "ids": [2023072604],
                "competition_slug": "friendly-match",
            }
            context["selected_competition_name"] = (
                "Audi Football Summit powered by docomo"
            )
        elif competition_slug == "friendly-match":  # !AFS Temporary
            fixture_options["excl_ids"] = [2023072604]
        elif competition_slug in PLAY_OFF_COMPETITIONS:
            if year <= 2022:
                fixture_options["competition_slug"] = (
                    GameKindEnum.J1_ENTRY_PLAY_OFF.value
                )  # 2022
            elif year <= 2023:
                fixture_options["competition_slug"] = (
                    GameKindEnum.J1_PROMOTION_PLAY_OFF.value
                )  # 2023
        elif competition_slug in PLAY_OFF_J2_COMPETITIONS:
            if year == 2024:
                fixture_options["competition_slug"] = (
                    GameKindEnum.J2_PROMOTION_PLAY_OFF.value
                )
        elif competition_slug in PLAY_OFF_J3_COMPETITIONS:
            if year == 2024:
                fixture_options["competition_slug"] = (
                    GameKindEnum.J3_JFL_Replace.value
                )
        elif competition_slug in ACL_COMPETITIONS:
            if year <= 2023:
                fixture_options["competition_slug"] = (
                    GameKindEnum.AFC_CHAMPIONS_LEAGUE.value
                )  # 2022
            elif year >= 2024:
                if competition_slug == "acle":
                    fixture_options["competition_slug"] = GameKindEnum.AFC_CHAMPIONS_LEAGUE_ELITE.value  # 2024
                elif competition_slug == "acl2":
                    fixture_options["competition_slug"] = GameKindEnum.AFC_CHAMPIONS_LEAGUE_TWO.value  # 2024

        context["fixtures"] = get_fixtures(**fixture_options)

        cache_timeout = get_fixture_cache_timeout(cache_key, context["fixtures"])
        cache.set(cache_key, context, cache_timeout)

    return render(request, "fixtures/ajax/fixtures_data.html", context)


@never_cache
def fixtures_stage_data(
    request,
    competition_slug="all",
    year=None,
    occasion_no=None,
    stage=None,
    stage_round=None,
    club_slug="all",
):
    if not _is_valid_competition(competition_slug):
        raise Http404()

    user_tz = request.TIMEZONE
    cache_key = f"fixtures_stage_data_{competition_slug}_{year}_{occasion_no}_{stage}_{stage_round}_{club_slug}_{user_tz}_{request.LANGUAGE_CODE}"
    context = cache.get(cache_key)

    if not context:
        year = year or get_display_data_year()
        month = get_current_month()

        context = {
            "selected_competition": competition_slug,
            "selected_month": month,
            "selected_team": club_slug,
            "selected_year": year,
        }

        if occasion_no is not None:
            context["selected_stage"] = occasion_no
        elif stage is not None and stage_round is not None:
            context["selected_stage"] = f"{stage}|{stage_round}"
        else:
            context["selected_stage"] = ""

        context["fixtures"] = get_fixtures(
            competition_slug=competition_slug,
            year=year,
            occasion_no=occasion_no,
            stage=stage,
            stage_round=stage_round,
            club_slug=club_slug,
            timezone=user_tz,
        )

        if occasion_no is not None and competition_slug in [
            *LEAGUE_COMPETITIONS,
            *PLAY_OFF_COMPETITIONS,
        ]:
            stage_name_lines = humanize_stage_name(
                competition_slug, None, occasion_no, False, year
            )
        elif (
            stage is not None
            and stage_round is not None
            and competition_slug in CUP_COMPETITIONS
        ):
            stage_name_lines = humanize_stage_name(
                competition_slug, stage, stage_round, False, year
            )
        context["stage_name"] = ", ".join(i for i in stage_name_lines if i)

        cache_timeout = get_fixture_cache_timeout(cache_key, context["fixtures"])
        cache.set(cache_key, context, cache_timeout)

    return render(request, "fixtures/ajax/fixtures_data.html", context)


@never_cache
def tickets(request, competition_slug="all"):
    if not _is_valid_competition(competition_slug):
        return redirect(to=reverse("tickets"), permanent=True)

    context = simple_page_context("Tickets")
    context["header"]["hide_matchweek_games"] = True
    context["selected_competition"] = competition_slug
    context["fixture_competitions"] = get_fixture_competitions(is_ticket=True)

    # the seo part should be located at the bottom
    seo_data = {
        "title": get_translation_value("SEO_TICKETS_PAGE_TITLE"),
        "description": get_translation_value("SEO_TICKETS_PAGE_DESCRIPTION"),
    }
    context = update_seo_data_django_view(context, seo_data)

    return render(request, "fixtures/tickets_page.html", context)


@never_cache
def tickets_data(request, competition_slug="all"):
    if not _is_valid_competition(competition_slug):
        raise Http404()

    user_tz = request.TIMEZONE
    cache_key = f"tickets_data_{competition_slug}_{user_tz}_{request.LANGUAGE_CODE}"
    context = cache.get(cache_key)

    if not context:
        context = {
            "selected_competition": competition_slug,
        }
        fixture_options = {
            "competition": competition_slug,
            "timezone": user_tz,
        }
        if competition_slug == "afs":  # !AFS Temporary
            fixture_options = {
                "ids": [2023072604],
                "competition": "friendly-match",
            }
            context["selected_competition_name"] = (
                "Audi Football Summit powered by docomo"
            )
        elif competition_slug == "friendly-match":  # !AFS Temporary
            fixture_options["excl_ids"] = [2023072604]

        context["fixtures"] = get_ticket_fixtures(**fixture_options)

        cache_timeout = get_fixture_cache_timeout(cache_key, context["fixtures"])
        cache.set(cache_key, context, cache_timeout)

    return render(request, "fixtures/ajax/tickets_data.html", context)
