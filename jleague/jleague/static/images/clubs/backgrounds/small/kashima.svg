<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 600 700" style="enable-background:new 0 0 600 700;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#8D202D;}
	.st1{fill:#1E2030;}
	.st2{fill:#A3A5A9;}
	.st3{fill:#C32F26;}
	.st4{fill:#D12E36;}
	.st5{fill:#D71920;}
	.st6{fill:#154E99;}
	.st7{clip-path:url(#SVGID_2_);}
	.st8{fill:#DE9E3B;}
	.st9{fill:#E7C069;}
	.st10{fill:#D68537;}
	.st11{fill:#CE672C;}
	.st12{fill:#9380B5;}
	.st13{clip-path:url(#SVGID_4_);}
	.st14{fill:#543787;}
	.st15{fill:#6953A0;}
	.st16{clip-path:url(#SVGID_6_);}
	.st17{fill:#D83843;}
	.st18{fill:#221F1F;}
	.st19{fill:none;}
	.st20{fill:url(#SVGID_7_);}
	.st21{fill:#FFEE3A;}
	.st22{fill:#2A3486;}
	.st23{clip-path:url(#SVGID_9_);fill:#D12D40;}
	.st24{clip-path:url(#SVGID_9_);}
	.st25{fill:#23408F;}
	.st26{fill:#FFFFFF;}
	.st27{fill:#D12D40;}
	.st28{fill:#799DD0;}
	.st29{clip-path:url(#SVGID_11_);}
	.st30{fill:#E59731;}
	.st31{fill:#B72825;}
	.st32{clip-path:url(#SVGID_13_);fill:#84BD56;}
	.st33{clip-path:url(#SVGID_13_);}
	.st34{fill:url(#SVGID_14_);}
	.st35{fill:url(#SVGID_15_);}
	.st36{fill:url(#SVGID_16_);}
	.st37{fill:url(#SVGID_17_);}
	.st38{fill:url(#SVGID_18_);}
	.st39{fill:url(#SVGID_19_);}
	.st40{fill:url(#SVGID_20_);}
	.st41{fill:url(#SVGID_21_);}
	.st42{fill:url(#SVGID_22_);}
	.st43{fill:url(#SVGID_23_);}
	.st44{fill:url(#SVGID_24_);}
	.st45{fill:url(#SVGID_25_);}
	.st46{fill:url(#SVGID_26_);}
	.st47{fill:url(#SVGID_27_);}
	.st48{clip-path:url(#SVGID_13_);fill:#A6D856;}
	.st49{fill:#009AC7;}
	.st50{fill:#010024;}
	.st51{fill:#008FAA;}
	.st52{clip-path:url(#SVGID_29_);fill:#F73C89;}
	.st53{clip-path:url(#SVGID_29_);fill:#C6285C;}
	.st54{clip-path:url(#SVGID_29_);fill:#D32F6A;}
	.st55{clip-path:url(#SVGID_29_);fill:#E5357D;}
	.st56{clip-path:url(#SVGID_31_);}
	.st57{fill:#721027;}
	.st58{fill:#510C25;}
	.st59{clip-path:url(#SVGID_31_);fill:#510C25;}
	.st60{clip-path:url(#SVGID_33_);}
	.st61{fill:url(#SVGID_34_);}
	.st62{fill:url(#SVGID_35_);}
	.st63{fill:url(#SVGID_36_);}
	.st64{fill:url(#SVGID_37_);}
	.st65{fill:url(#SVGID_38_);}
	.st66{fill:url(#SVGID_39_);}
	.st67{fill:url(#SVGID_40_);}
	.st68{fill:url(#SVGID_41_);}
	.st69{fill:url(#SVGID_42_);}
	.st70{fill:url(#SVGID_43_);}
	.st71{fill:url(#SVGID_44_);}
	.st72{fill:url(#SVGID_45_);}
	.st73{fill:url(#SVGID_46_);}
	.st74{fill:url(#SVGID_47_);}
	.st75{fill:url(#SVGID_48_);}
	.st76{fill:url(#SVGID_49_);}
	.st77{fill:url(#SVGID_50_);}
	.st78{fill:url(#SVGID_51_);}
	.st79{fill:url(#SVGID_52_);}
	.st80{fill:url(#SVGID_53_);}
	.st81{fill:url(#SVGID_54_);}
	.st82{fill:url(#SVGID_55_);}
	.st83{fill:url(#SVGID_56_);}
	.st84{fill:url(#SVGID_57_);}
	.st85{fill:url(#SVGID_58_);}
	.st86{fill:url(#SVGID_59_);}
	.st87{fill:url(#SVGID_60_);}
	.st88{fill:url(#SVGID_61_);}
	.st89{fill:url(#SVGID_62_);}
	.st90{fill:url(#SVGID_63_);}
	.st91{fill:url(#SVGID_64_);}
	.st92{fill:url(#SVGID_65_);}
	.st93{fill:url(#SVGID_66_);}
	.st94{fill:url(#SVGID_67_);}
	.st95{fill:url(#SVGID_68_);}
	.st96{fill:url(#SVGID_69_);}
	.st97{fill:url(#SVGID_70_);}
	.st98{fill:url(#SVGID_71_);}
	.st99{fill:url(#SVGID_72_);}
	.st100{fill:url(#SVGID_73_);}
	.st101{fill:url(#SVGID_74_);}
	.st102{fill:url(#SVGID_75_);}
	.st103{fill:url(#SVGID_76_);}
	.st104{fill:url(#SVGID_77_);}
	.st105{fill:url(#SVGID_78_);}
	.st106{fill:url(#SVGID_79_);}
	.st107{fill:url(#SVGID_80_);}
	.st108{fill:url(#SVGID_81_);}
	.st109{fill:url(#SVGID_82_);}
	.st110{fill:url(#SVGID_83_);}
	.st111{fill:url(#SVGID_84_);}
	.st112{fill:url(#SVGID_85_);}
	.st113{fill:url(#SVGID_86_);}
	.st114{fill:url(#SVGID_87_);}
	.st115{fill:url(#SVGID_88_);}
	.st116{fill:url(#SVGID_89_);}
	.st117{fill:url(#SVGID_90_);}
	.st118{fill:url(#SVGID_91_);}
	.st119{fill:url(#SVGID_92_);}
	.st120{fill:url(#SVGID_93_);}
	.st121{fill:url(#SVGID_94_);}
	.st122{fill:url(#SVGID_95_);}
	.st123{fill:url(#SVGID_96_);}
	.st124{fill:url(#SVGID_97_);}
	.st125{fill:url(#SVGID_98_);}
	.st126{fill:url(#SVGID_99_);}
	.st127{fill:url(#SVGID_100_);}
	.st128{fill:url(#SVGID_101_);}
	.st129{fill:url(#SVGID_102_);}
	.st130{fill:url(#SVGID_103_);}
	.st131{fill:url(#SVGID_104_);}
	.st132{fill:url(#SVGID_105_);}
	.st133{fill:url(#SVGID_106_);}
	.st134{fill:url(#SVGID_107_);}
	.st135{fill:url(#SVGID_108_);}
	.st136{fill:url(#SVGID_109_);}
	.st137{fill:url(#SVGID_110_);}
	.st138{fill:url(#SVGID_111_);}
	.st139{fill:url(#SVGID_112_);}
	.st140{fill:url(#SVGID_113_);}
	.st141{fill:url(#SVGID_114_);}
	.st142{fill:url(#SVGID_115_);}
	.st143{fill:url(#SVGID_116_);}
	.st144{fill:url(#SVGID_117_);}
	.st145{fill:url(#SVGID_118_);}
	.st146{fill:url(#SVGID_119_);}
	.st147{fill:url(#SVGID_120_);}
	.st148{fill:url(#SVGID_121_);}
	.st149{fill:url(#SVGID_122_);}
	.st150{fill:url(#SVGID_123_);}
	.st151{fill:url(#SVGID_124_);}
	.st152{fill:url(#SVGID_125_);}
	.st153{fill:url(#SVGID_126_);}
	.st154{fill:url(#SVGID_127_);}
	.st155{fill:url(#SVGID_128_);}
	.st156{fill:url(#SVGID_129_);}
	.st157{fill:url(#SVGID_130_);}
	.st158{fill:url(#SVGID_131_);}
	.st159{fill:url(#SVGID_132_);}
	.st160{fill:url(#SVGID_133_);}
	.st161{clip-path:url(#SVGID_135_);fill:#5BA1D7;}
	.st162{clip-path:url(#SVGID_135_);}
	.st163{fill:url(#SVGID_136_);}
	.st164{fill:url(#SVGID_137_);}
	.st165{fill:url(#SVGID_138_);}
	.st166{fill:url(#SVGID_139_);}
	.st167{fill:url(#SVGID_140_);}
	.st168{fill:url(#SVGID_141_);}
	.st169{fill:url(#SVGID_142_);}
	.st170{fill:url(#SVGID_143_);}
	.st171{fill:url(#SVGID_144_);}
	.st172{fill:url(#SVGID_145_);}
	.st173{fill:url(#SVGID_146_);}
	.st174{fill:url(#SVGID_147_);}
	.st175{fill:url(#SVGID_148_);}
	.st176{fill:url(#SVGID_149_);}
	.st177{fill:url(#SVGID_150_);}
	.st178{fill:url(#SVGID_151_);}
	.st179{fill:url(#SVGID_152_);}
	.st180{fill:url(#SVGID_153_);}
	.st181{fill:url(#SVGID_154_);}
	.st182{fill:url(#SVGID_155_);}
	.st183{fill:url(#SVGID_156_);}
	.st184{fill:url(#SVGID_157_);}
	.st185{fill:url(#SVGID_158_);}
	.st186{fill:url(#SVGID_159_);}
	.st187{fill:url(#SVGID_160_);}
	.st188{fill:url(#SVGID_161_);}
	.st189{fill:url(#SVGID_162_);}
	.st190{fill:url(#SVGID_163_);}
	.st191{fill:url(#SVGID_164_);}
	.st192{fill:url(#SVGID_165_);}
	.st193{fill:url(#SVGID_166_);}
	.st194{fill:url(#SVGID_167_);}
	.st195{fill:url(#SVGID_168_);}
	.st196{fill:url(#SVGID_169_);}
	.st197{fill:url(#SVGID_170_);}
	.st198{fill:url(#SVGID_171_);}
	.st199{fill:url(#SVGID_172_);}
	.st200{fill:url(#SVGID_173_);}
	.st201{fill:url(#SVGID_174_);}
	.st202{fill:url(#SVGID_175_);}
	.st203{fill:url(#SVGID_176_);}
	.st204{fill:url(#SVGID_177_);}
	.st205{fill:url(#SVGID_178_);}
	.st206{fill:url(#SVGID_179_);}
	.st207{fill:url(#SVGID_180_);}
	.st208{fill:url(#SVGID_181_);}
	.st209{fill:url(#SVGID_182_);}
	.st210{fill:url(#SVGID_183_);}
	.st211{fill:url(#SVGID_184_);}
	.st212{fill:url(#SVGID_185_);}
	.st213{fill:url(#SVGID_186_);}
	.st214{fill:url(#SVGID_187_);}
	.st215{fill:url(#SVGID_188_);}
	.st216{fill:url(#SVGID_189_);}
	.st217{fill:url(#SVGID_190_);}
	.st218{fill:url(#SVGID_191_);}
	.st219{fill:url(#SVGID_192_);}
	.st220{fill:url(#SVGID_193_);}
	.st221{fill:url(#SVGID_194_);}
	.st222{fill:url(#SVGID_195_);}
	.st223{fill:url(#SVGID_196_);}
	.st224{fill:url(#SVGID_197_);}
	.st225{fill:url(#SVGID_198_);}
	.st226{fill:url(#SVGID_199_);}
	.st227{fill:url(#SVGID_200_);}
	.st228{fill:url(#SVGID_201_);}
	.st229{fill:url(#SVGID_202_);}
	.st230{fill:url(#SVGID_203_);}
	.st231{fill:url(#SVGID_204_);}
	.st232{fill:url(#SVGID_205_);}
	.st233{fill:url(#SVGID_206_);}
	.st234{fill:url(#SVGID_207_);}
	.st235{fill:url(#SVGID_208_);}
	.st236{fill:url(#SVGID_209_);}
	.st237{fill:url(#SVGID_210_);}
	.st238{fill:url(#SVGID_211_);}
	.st239{fill:url(#SVGID_212_);}
	.st240{fill:url(#SVGID_213_);}
	.st241{fill:url(#SVGID_214_);}
	.st242{fill:url(#SVGID_215_);}
	.st243{fill:url(#SVGID_216_);}
	.st244{fill:url(#SVGID_217_);}
	.st245{fill:url(#SVGID_218_);}
	.st246{fill:url(#SVGID_219_);}
	.st247{fill:url(#SVGID_220_);}
	.st248{fill:url(#SVGID_221_);}
	.st249{fill:url(#SVGID_222_);}
	.st250{fill:url(#SVGID_223_);}
	.st251{fill:url(#SVGID_224_);}
	.st252{fill:url(#SVGID_225_);}
	.st253{fill:url(#SVGID_226_);}
	.st254{fill:url(#SVGID_227_);}
	.st255{fill:url(#SVGID_228_);}
	.st256{fill:url(#SVGID_229_);}
	.st257{fill:url(#SVGID_230_);}
	.st258{fill:url(#SVGID_231_);}
	.st259{fill:url(#SVGID_232_);}
	.st260{fill:url(#SVGID_233_);}
	.st261{fill:url(#SVGID_234_);}
	.st262{fill:url(#SVGID_235_);}
	.st263{fill:url(#SVGID_236_);}
	.st264{fill:url(#SVGID_237_);}
	.st265{fill:url(#SVGID_238_);}
	.st266{fill:url(#SVGID_239_);}
	.st267{fill:url(#SVGID_240_);}
	.st268{fill:url(#SVGID_241_);}
	.st269{fill:url(#SVGID_242_);}
	.st270{fill:url(#SVGID_243_);}
	.st271{fill:url(#SVGID_244_);}
	.st272{fill:url(#SVGID_245_);}
	.st273{fill:url(#SVGID_246_);}
	.st274{fill:url(#SVGID_247_);}
	.st275{fill:url(#SVGID_248_);}
	.st276{fill:url(#SVGID_249_);}
	.st277{fill:url(#SVGID_250_);}
	.st278{fill:url(#SVGID_251_);}
	.st279{fill:url(#SVGID_252_);}
	.st280{fill:url(#SVGID_253_);}
	.st281{fill:url(#SVGID_254_);}
	.st282{fill:url(#SVGID_255_);}
	.st283{fill:url(#SVGID_256_);}
	.st284{fill:url(#SVGID_257_);}
	.st285{fill:url(#SVGID_258_);}
	.st286{fill:url(#SVGID_259_);}
	.st287{fill:url(#SVGID_260_);}
	.st288{fill:url(#SVGID_261_);}
	.st289{fill:url(#SVGID_262_);}
	.st290{fill:url(#SVGID_263_);}
	.st291{fill:url(#SVGID_264_);}
	.st292{fill:url(#SVGID_265_);}
	.st293{fill:url(#SVGID_266_);}
	.st294{fill:url(#SVGID_267_);}
	.st295{fill:url(#SVGID_268_);}
	.st296{fill:url(#SVGID_269_);}
	.st297{fill:url(#SVGID_270_);}
	.st298{fill:url(#SVGID_271_);}
	.st299{fill:url(#SVGID_272_);}
	.st300{fill:url(#SVGID_273_);}
	.st301{fill:url(#SVGID_274_);}
	.st302{fill:url(#SVGID_275_);}
	.st303{fill:url(#SVGID_276_);}
	.st304{fill:url(#SVGID_277_);}
	.st305{fill:url(#SVGID_278_);}
	.st306{fill:url(#SVGID_279_);}
	.st307{fill:url(#SVGID_280_);}
	.st308{fill:url(#SVGID_281_);}
	.st309{fill:url(#SVGID_282_);}
	.st310{fill:url(#SVGID_283_);}
	.st311{fill:url(#SVGID_284_);}
	.st312{fill:url(#SVGID_285_);}
	.st313{fill:url(#SVGID_286_);}
	.st314{fill:url(#SVGID_287_);}
	.st315{fill:url(#SVGID_288_);}
	.st316{fill:url(#SVGID_289_);}
	.st317{fill:url(#SVGID_290_);}
	.st318{fill:url(#SVGID_291_);}
	.st319{fill:url(#SVGID_292_);}
	.st320{fill:url(#SVGID_293_);}
	.st321{fill:url(#SVGID_294_);}
	.st322{fill:url(#SVGID_295_);}
	.st323{fill:url(#SVGID_296_);}
	.st324{fill:url(#SVGID_297_);}
	.st325{fill:url(#SVGID_298_);}
	.st326{fill:url(#SVGID_299_);}
	.st327{fill:url(#SVGID_300_);}
	.st328{fill:url(#SVGID_301_);}
	.st329{fill:url(#SVGID_302_);}
	.st330{fill:url(#SVGID_303_);}
	.st331{fill:url(#SVGID_304_);}
	.st332{fill:url(#SVGID_305_);}
	.st333{fill:url(#SVGID_306_);}
	.st334{fill:url(#SVGID_307_);}
	.st335{fill:url(#SVGID_308_);}
	.st336{fill:url(#SVGID_309_);}
	.st337{fill:url(#SVGID_310_);}
	.st338{fill:url(#SVGID_311_);}
	.st339{fill:url(#SVGID_312_);}
	.st340{fill:url(#SVGID_313_);}
	.st341{fill:url(#SVGID_314_);}
	.st342{fill:url(#SVGID_315_);}
	.st343{fill:url(#SVGID_316_);}
	.st344{fill:url(#SVGID_317_);}
	.st345{fill:#89287D;}
</style>
<rect x="0" class="st0" width="600" height="700"/>
<rect x="73.3" class="st1" width="29.3" height="700"/>
<rect class="st2" width="73.3" height="700"/>
<rect x="497.4" y="0" class="st1" width="29.3" height="700"/>
<rect x="526.7" y="0" class="st2" width="73.3" height="700"/>
</svg>
