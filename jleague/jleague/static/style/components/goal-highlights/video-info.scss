.video-info {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-bottom: 8px;

  .video-info__left {
    display: flex;
    justify-content: center;
    align-items: center;

    .player-photo {
      height: 64px;
      width: 64px;
      border-radius: 50%;

      img {
        height: 100%;
        width: 100%;
      }
    }

    .player-photo-link {
      &:hover {
        opacity: 1;
      }
    }
  }

  .video-info__right {
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: center;
    padding-left: 12px;

    .event-detail {
      display: flex;
      align-items: center;
      margin-bottom: 4px;

      .event-detail__icon {
        width: 24px;
        height: 24px;
        margin-left: 0;
        margin-right: 4px;
      }

      .event-detail__time {
        color: var(--color-earth-4);
        font-family: var(--font-jleague-kick);
        font-style: normal;
        font-weight: 700;
        font-size: 24px;
        line-height: 24px;
      }
    }

    .player-name {
      color: var(--color-black);
      font-family: var(--font-overpass);
      font-style: normal;
      font-weight: 900;
      font-size: 16px;
      line-height: 20px;
      @include text_ellipsis();

      &:hover {
        opacity: 1;

        &[href] {
          text-decoration: underline;
        }
      }
    }
  }

  &.video-info--white-text {
    .video-info__right {
      .event-detail {
        .event-detail__time {
          color: var(--color-white);
        }
      }

      .player-name {
        color: var(--color-white);
      }
    }
  }

  &.video-info--highlight-video {
    margin-bottom: 0;

    .video-info__right {
      .event-detail {
        .event-detail__time {
          color: var(--color-earth-4);
        }
      }
    }
  }
}

body {
  &.lang-th {
    .video-info .video-info__right .player-name {
      font-weight: 700;
    }
  }

  &.lang-vi {
    .video-info .video-info__right .player-name {
      font-weight: 600;
    }
  }
}
