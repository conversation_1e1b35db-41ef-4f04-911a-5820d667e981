# Use an official Python runtime based on Debian 10 "buster" as a parent image.
FROM python:3.8.16-slim-buster

# Set environment variables.
ENV PYTHONUNBUFFERED 1
ENV PYTHONDONTWRITEBYTECODE 1

# Use /app folder as a directory where the source code is stored.
WORKDIR /app

# Install system packages required by Wagtail, Django and Memcached.
RUN apt-get update -qq
RUN apt-get install -qq -y --no-install-recommends \
  build-essential \
  libpq-dev \
  gettext \
  libjpeg62-turbo-dev \
  zlib1g-dev \
  libwebp-dev \
  curl \
  memcached
RUN apt-get purge -qq -y --auto-remove -o APT::AutoRemove::RecommendsImportant=false
RUN rm -rf /var/lib/apt/lists/*

# Install Node.js
RUN apt-get update && apt-get install --yes --quiet ca-certificates gnupg
RUN mkdir -p /etc/apt/keyrings
RUN curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg
ENV NODE_MAJOR=20
RUN echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_$NODE_MAJOR.x nodistro main" | tee /etc/apt/sources.list.d/nodesource.list
RUN apt-get update && apt-get install nodejs --yes --quiet

# Install yuglify for compress CSS & JS in django-pipeline
RUN npm install -g yuglify && npm cache clean --force

# Upgrade pip to the latest version
RUN pip install -U pip

# Install the application server.
RUN pip install -q gunicorn==20.1.0

# Install the project requirements.
COPY requirements.txt ./requirements.txt
RUN pip install -q -r ./requirements.txt

# Copy the source code of the project into the container.
COPY jleague .

COPY ./compose/entrypoint /entrypoint
RUN sed -i 's/\r$//g' /entrypoint
RUN chmod +x /entrypoint

COPY ./compose/start /start
RUN sed -i 's/\r$//g' /start
RUN chmod +x /start

# Create logs directory
RUN mkdir -p /app/logs

# Forward environment variable to build process.
ARG DJANGO_SETTINGS_MODULE
ARG DATABASE_URL
ARG REDIS_URL
ARG STORAGE_DEFAULT_FILE_STORAGE

# Collect django/wagtail static files
RUN python /app/manage.py collectstatic --noinput

ENTRYPOINT ["/entrypoint"]
