<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 2800 1400" style="enable-background:new 0 0 2800 1400;" xml:space="preserve">
<style type="text/css">
	.st0{clip-path:url(#SVGID_2_);fill:#385779;}
	.st1{clip-path:url(#SVGID_4_);fill:url(#SVGID_5_);}
	.st2{clip-path:url(#SVGID_7_);fill:url(#SVGID_8_);}
	.st3{clip-path:url(#SVGID_12_);}
	.st4{fill:#1E1B2F;}
	.st5{clip-path:url(#SVGID_14_);}
	.st6{clip-path:url(#SVGID_16_);fill:#FBDA39;}
	.st7{clip-path:url(#SVGID_18_);fill:none;}
</style>
<g>
	<g>
		<defs>
			<rect id="SVGID_1_" width="2800" height="1400"/>
		</defs>
		<clipPath id="SVGID_2_">
			<use xlink:href="#SVGID_1_"  style="overflow:visible;"/>
		</clipPath>
		<rect class="st0" width="2800" height="1400"/>
	</g>
	<g>
		<defs>
			<rect id="SVGID_3_" width="2800" height="1400"/>
		</defs>
		<clipPath id="SVGID_4_">
			<use xlink:href="#SVGID_3_"  style="overflow:visible;"/>
		</clipPath>
		
			<linearGradient id="SVGID_5_" gradientUnits="userSpaceOnUse" x1="837.7" y1="-1.4489" x2="837.7" y2="1383.875" gradientTransform="matrix(1 0 0 -1 0 1398.5511)">
			<stop  offset="0" style="stop-color:#1E1B2F"/>
			<stop  offset="0.4777" style="stop-color:#003D5D"/>
			<stop  offset="1" style="stop-color:#000000"/>
		</linearGradient>
		<rect x="557.7" class="st1" width="560" height="1400"/>
	</g>
	<g>
		<defs>
			<rect id="SVGID_6_" width="2800" height="1400"/>
		</defs>
		<clipPath id="SVGID_7_">
			<use xlink:href="#SVGID_6_"  style="overflow:visible;"/>
		</clipPath>
		
			<linearGradient id="SVGID_8_" gradientUnits="userSpaceOnUse" x1="1957.7" y1="15.1514" x2="1957.7" y2="1365.1356" gradientTransform="matrix(1 0 0 -1 0 1398.5511)">
			<stop  offset="0" style="stop-color:#1E1B2F"/>
			<stop  offset="0.4777" style="stop-color:#003D5D"/>
			<stop  offset="1" style="stop-color:#000000"/>
		</linearGradient>
		<rect x="1677.7" class="st2" width="560" height="1400"/>
	</g>
	<g>
		<defs>
			<rect id="SVGID_9_" width="2800" height="1400"/>
		</defs>
		<clipPath id="SVGID_10_">
			<use xlink:href="#SVGID_9_"  style="overflow:visible;"/>
		</clipPath>
	</g>
	<g>
		<defs>
			<rect id="SVGID_11_" width="2800" height="1400"/>
		</defs>
		<clipPath id="SVGID_12_">
			<use xlink:href="#SVGID_11_"  style="overflow:visible;"/>
		</clipPath>
		<g class="st3">
			<g>
				<polygon class="st4" points="3100,-115.4 3100,444.6 2918.6,617.7 2918.6,260.2 2913.6,265 2538.9,622.5 2538.9,260.2 
					2159.2,622.5 2159.2,260.2 1779.5,622.5 1779.5,260.2 1399.8,622.5 1399.8,260.2 1020,622.5 1020,260.2 640.3,622.5 
					640.3,260.2 260.6,622.5 260.6,260.2 -119.1,622.5 -119.1,410.7 62.3,246.9 442,-115.4 442,246.9 821.7,-115.4 821.7,246.9 
					1201.5,-115.4 1201.5,246.9 1581.2,-115.4 1581.2,246.9 1960.9,-115.4 1960.9,246.9 2340.6,-115.4 2340.6,246.9 2720.3,-115.4 
					2720.3,246.9 3095,-110.6 				"/>
			</g>
		</g>
	</g>
	<g>
		<defs>
			<rect id="SVGID_13_" width="2800" height="1400"/>
		</defs>
		<clipPath id="SVGID_14_">
			<use xlink:href="#SVGID_13_"  style="overflow:visible;"/>
		</clipPath>
		<g class="st5">
			<g>
				<polygon class="st4" points="3100,820.3 3100,1380.3 2918.6,1553.4 2918.6,1195.9 2913.6,1200.7 2538.9,1558.3 2538.9,1195.9 
					2159.2,1558.3 2159.2,1195.9 1779.5,1558.3 1779.5,1195.9 1399.8,1558.3 1399.8,1195.9 1020,1558.3 1020,1195.9 640.3,1558.3 
					640.3,1195.9 260.6,1558.3 260.6,1195.9 -119.1,1558.3 -119.1,1346.5 62.3,1182.7 442,820.3 442,1182.7 821.7,820.3 
					821.7,1182.7 1201.5,820.3 1201.5,1182.7 1581.2,820.3 1581.2,1182.7 1960.9,820.3 1960.9,1182.7 2340.6,820.3 2340.6,1182.7 
					2720.3,820.3 2720.3,1182.7 3095,825.2 				"/>
			</g>
		</g>
	</g>
	<g>
		<defs>
			<rect id="SVGID_15_" width="2800" height="1400"/>
		</defs>
		<clipPath id="SVGID_16_">
			<use xlink:href="#SVGID_15_"  style="overflow:visible;"/>
		</clipPath>
		<rect class="st6" width="2800" height="55.9"/>
	</g>
	<g>
		<defs>
			<rect id="SVGID_17_" width="2800" height="1400"/>
		</defs>
		<clipPath id="SVGID_18_">
			<use xlink:href="#SVGID_17_"  style="overflow:visible;"/>
		</clipPath>
		<rect class="st7" width="1200" height="1400"/>
	</g>
</g>
</svg>
