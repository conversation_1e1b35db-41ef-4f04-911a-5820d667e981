import aioftp
import logging
import xml.etree.ElementTree as ET
from ..helpers import async_find_first, async_upsert, process_path_if_needed, read_file_to_str, xml_value
from match.models import Game, GameTeamFormation

logger = logging.getLogger(__name__)


def _parse_player_postions(node):
    """Convert LiveFormation PlayerInfo into Dict"""
    if not node:
        return None

    player_postions = {'positions': []}

    for i in range(1, 12):
        pos = {
            'player_id': xml_value(node, f'P{i}ID', int),
            'position': xml_value(node, f'P{i}Pos'),
            # 'uniform_no': xml_value(node, f'P{i}Uni', int),
            # 'name_short': xml_value(node, f'P{i}NameES', int),
            'x': xml_value(node, f'P{i}X', int),
            'y': xml_value(node, f'P{i}Y', int),
        }
        player_postions['positions'].append(pos)

    return player_postions


async def _process_formation(game, team_nodes):
    # get team player positions
    home_team = None
    home_formation_id = None
    home_player_positions = None
    away_team = None
    away_formation_id = None
    away_player_positions = None

    for team_node in team_nodes:
        player_node = team_node.find('PlayerInfo')

        if team_node.attrib['HV'] == '1':
            home_team = game.home_team_id
            home_formation_id = xml_value(team_node, 'FormationID', int)
            home_player_positions = _parse_player_postions(player_node)
        elif team_node.attrib['HV'] == '2':
            away_team = game.away_team_id
            away_formation_id = xml_value(team_node, 'FormationID', int)
            away_player_positions = _parse_player_postions(player_node)

    # create or update GameTeamFormation
    required_data = [
        home_team,
        home_formation_id,
        home_player_positions,
        away_team,
        away_formation_id,
        away_player_positions,
    ]
    if None not in required_data:
        await async_upsert(
            GameTeamFormation,
            game=game,
            defaults={
                'home_team_id': home_team,
                'home_team_formation_id': home_formation_id,
                'home_player_positions': home_player_positions,
                'away_team_id': away_team,
                'away_team_formation_id': away_formation_id,
                'away_player_positions': away_player_positions,
            },
        )


async def import_live_formation(ftp_client: aioftp.Client, file_path: str, file_stat: dict):
    """Import live team formations of game from DS
    source file: {MATCH_ID}/live_formation.xml
    doc file: https://docs.google.com/spreadsheets/d/1GmF9jSIa31o1lCcp4W2DQ_lpzdNwieZj/edit?rtpof=true#gid=3025245
    """
    async with process_path_if_needed(file_path, file_stat) as should_process:
        if not should_process:
            return

        # read XML file
        file_content = await read_file_to_str(ftp_client, file_path)
        root = ET.fromstring(file_content)  # Football node
        report_node = root.find('FormationReport')
        game_id = xml_value(report_node, 'GameID', int)

        # check game exists
        game: Game = await async_find_first(Game, id=game_id)
        if not game:
            logger.warn('Game not found (id=%s)', game_id)
            return

        change_nodes = report_node.findall('ChangeInfo')
        team_nodes = None

        # get clubs
        if change_nodes and len(change_nodes):
            team_nodes = change_nodes[0].findall('TeamInfo')
        else:
            team_nodes = report_node.findall('TeamInfo')

        if team_nodes and len(team_nodes):
            await _process_formation(game, team_nodes)

        logger.info('Processed game team formations (game_id=%s)', game_id)
