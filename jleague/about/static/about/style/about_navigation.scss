.about-navigation {
  overflow-x: auto;

  .about-navigation-list {
    display: flex;
    flex-direction: row;

    .about-navigation-list__item {
      flex: 1;

      .about-navigation-list__item__link {
        font-family: var(--font-overpass);
        font-size: 20px;
        font-style: normal;
        font-weight: 700;
        line-height: 32px;
        letter-spacing: 0px;
        text-align: center;
        background: var(--color-white);
        color: var(--color-black);
        border: none;
        border-bottom: solid 8px var(--color-earth-2);
        padding: 8px 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
        width: 100%;
      }

      &:hover:not(.active) {
        .about-navigation-list__item__link {
          background: var(--color-earth-2);
          border-color: var(--color-red);
          color: var(--color-black);
          cursor: pointer;
          opacity: 1;
        }
      }

      &.active {
        &:hover {
          .about-navigation-list__item__link {
            opacity: 1;
            cursor: default;
          }
        }

        .about-navigation-list__item__link {
          border-color: var(--color-red);
          color: var(--color-red);
        }
      }
    }

    @media screen and (max-width: 767px) {
      .about-navigation-list__item {
        .about-navigation-list__item__link {
          font-size: 20px;
          line-height: 24px;
        }
      }
    }

    @media screen and (max-width: 576px) {
      .about-navigation-list__item {
        .about-navigation-list__item__link {
          font-size: 16px;
          line-height: 20px;
        }
      }
    }
  }
}
