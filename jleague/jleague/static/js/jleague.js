(function ($) {
  $.event.special.touchstart = {
    setup: function (_, ns, handle) {
      this.addEventListener('touchstart', handle, {
        passive: !ns.includes('noPreventDefault'),
      });
    },
  };

  $.event.special.touchmove = {
    setup: function (_, ns, handle) {
      this.addEventListener('touchmove', handle, {
        passive: !ns.includes('noPreventDefault'),
      });
    },
  };

  $.event.special.wheel = {
    setup: function (_, ns, handle) {
      this.addEventListener('wheel', handle, { passive: true });
    },
  };

  $.event.special.mousewheel = {
    setup: function (_, ns, handle) {
      this.addEventListener('mousewheel', handle, { passive: true });
    },
  };

  // checkToken();
  checkTimezone();

  // toggle floating buttons
  let scrollTimeout;
  const floatingButtons = $('.floating-buttons');
  const backTopButton = floatingButtons.find('.floating-button--backtotop');
  const copyLinkButton = floatingButtons.find('.floating-button--copylink');

  window.addEventListener('scroll', (event) => {
    clearTimeout(scrollTimeout);
    scrollTimeout = setTimeout(() => {
      if (this.scrollY >= 250) {
        floatingButtons.css({ opacity: 1, visibility: 'visible' });
      } else {
        floatingButtons.css({ opacity: 0, visibility: 'hidden' });
      }
    }, 250);
  });

  backTopButton.click(function () {
    scrollToTop();
  });

  copyLinkButton.click(function () {
    copyLink();
    copyLinkButton.addClass('floating-button--tooltip');
    setTimeout(function () {
      copyLinkButton.removeClass('floating-button--tooltip');
    }, 2000);
  });

  // enable transition and animation on elements
  document.addEventListener('DOMContentLoaded', function () {
    document.querySelectorAll('.no-transition').forEach((elm) => elm.classList.remove('no-transition'));
    document.querySelectorAll('.no-animation').forEach((elm) => elm.classList.remove('no-animation'));
  });

  // if (isth && isposter) {
  //   const poster = $('.poster-popup');
  //   const posterClose = poster.find('.poster-popup__close');
  //   const isPosterPopup = getCookie('jl_poster-popup');

  //   if (typeof isPosterPopup === 'undefined' || isPosterPopup !== '1') {
  //     posterClose.click(() => {
  //       poster.addClass('poster-popup--closed');
  //       setCookie('jl_poster-popup', 1);
  //     });
  //     setTimeout(() => poster.removeClass('poster-popup--closed'), 3500);
  //   }
  // }
})(jQuery);

// Register the service worker
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js');
  });
} else {
  console.error('Browser does not support service workers.');
}

// Initialize deferredPrompt for use later to show browser install prompt.
let deferredPrompt;
const installButton = $('.floating-button--install').css('display', 'none');

window.addEventListener('beforeinstallprompt', (e) => {
  // Prevent the mini-infobar from appearing on mobile
  e.preventDefault();
  // Stash the event so it can be triggered later.
  deferredPrompt = e;
  // Update UI notify the user they can install the PWA
  installButton.css('display', 'flex');
  // send analytics event that PWA install promo was shown.
  sendAppInstallDisplayEvent();
});

window.addEventListener('appinstalled', () => {
  // // Hide the app-provided install promotion
  // hideInstallPromotion();
  // hide our user interface that shows our install button
  installButton.css('display', 'none');
  // Clear the deferredPrompt so it can be garbage collected
  deferredPrompt = null;
  // send analytics event that PWA install succeeded.
  sendAppInstallSuccessEvent();
});

installButton.click((e) => {
  // Show the prompt
  deferredPrompt.prompt();
  // Wait for the user to respond to the prompt
  deferredPrompt.userChoice.then((choiceResult) => {
    if (choiceResult.outcome === 'accepted') {
      // send analytics event that User has accepted PWA.
      sendAppInstallAcceptedEvent();
    } else {
      // send analytics event that User has rejected PWA.
      sendAppInstallRejectedEvent();
    }
    deferredPrompt = null;
  });
});

// // Convert a base64 string to Uint8Array.
// // Must do this so the server can understand the VAPID_PUBLIC_KEY.
// function urlB64ToUint8Array(base64String) {
//   const padding = '='.repeat((4 - base64String.length % 4) % 4);
//   const base64 = (base64String + padding)
//     .replace(/\-/g, '+')
//     .replace(/_/g, '/');
//   const rawData = window.atob(base64);
//   const outputArray = new Uint8Array(rawData.length);
//   for (let i = 0; i < rawData.length; ++i) {
//     outputArray[i] = rawData.charCodeAt(i);
//   }
//   return outputArray;
// }

// // TEST PURPOSE ONLY
// // subscribe notification
// $('.subscribe-button').click(async function () {
//   // the permission prompt by pressing Allow, Block, or just closing it,
//   // we'll be given the result as a string: 'granted', 'default' or 'denied'.
//   const result = await Notification.requestPermission();
//   // console.log(result)
//   if (result === 'denied') {
//     console.error('The user explicitly denied the permission request.');
//     return;
//   }
//   // if (result === 'granted') {
//   //   console.info('The user accepted the permission request.');
//   // }
//   const registration = await navigator.serviceWorker.getRegistration();
//   const subscribed = await registration.pushManager.getSubscription();
//   if (subscribed) {
//     // console.info('User is already subscribed.');
//     return;
//   }
//   // generated from https://web-push-codelab.glitch.me/
//   const VAPID_PUBLIC_KEY = 'TM321...';
//   const subscription = await registration.pushManager.subscribe({
//     userVisibleOnly: true,
//     applicationServerKey: urlB64ToUint8Array(VAPID_PUBLIC_KEY)
//   });
//   // console.log('subscribeButtonHandler::subscription:', subscription)
//   ajaxRequest('/add_subscription/', { 'subscription': JSON.stringify(subscription) }, 'POST');
// });

// // unsubscribe notification
// $('.unsubscribe-button').click(async function () {
//   const registration = await navigator.serviceWorker.getRegistration();
//   const subscription = await registration.pushManager.getSubscription();
//   ajaxRequest('/remove_subscription/', { endpoint: subscription.endpoint }, 'POST');
//   const unsubscribed = await subscription.unsubscribe();
//   // if (unsubscribed) {
//   //   console.info('Successfully unsubscribed from push notifications.');
//   // }
// });

// // send notification
// $('.notifyme-button').click(async function () {
//   const registration = await navigator.serviceWorker.getRegistration();
//   const subscription = await registration.pushManager.getSubscription();
//   ajaxRequest('/notify_me/', { endpoint: subscription.endpoint }, 'POST');
// });
