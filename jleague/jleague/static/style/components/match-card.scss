@import 'setup/variable';
@import 'setup/mixin';

.match-card {
  width: 100%;
  height: 56px;
  border-bottom: 1px solid var(--color-earth-2);
  display: flex;
  align-items: center;
  overflow: hidden;

  .match-card__situation,
  .match-card__button {
    display: inline-block;
  }

  .match-card__situation {
    font-family: var(--font-barlow-condensed);
    font-style: normal;
    font-weight: bold;
    font-size: 14px;
    line-height: 17px;
    letter-spacing: 0.933333px;
    text-transform: uppercase;
    color: var(--color-earth-4);
    width: 48px;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .match-detail-situation {
      color: var(--color-drawn);
    }

    .ytlive-icon {
      display: flex;
      height: 24px;
      width: 24px;
    }

    &.match-card__situation--live {
      .match-detail-situation {
        color: var(--color-lost);
        font-size: 18px;
        line-height: 20px;
      }
    }
  }

  .match-card__details {
    display: flex;
    flex: 1;
    align-items: center;
    padding: 0 16px;
    overflow: hidden;
    position: relative;

    .club-name {
      color: var(--color-black);
      font-family: var(--font-jleague-kick);
      font-style: normal;
      font-weight: 700;
      font-size: 20px;
      line-height: 24px;
      @include text_ellipsis();
    }

    .club-emblem {
      display: inline-block;
      flex: 0 0 auto;
      width: 40px;
      height: 40px;
    }

    .home-detail,
    .away-detail {
      display: flex;
      align-items: center;
      // min-width: 100px;
      flex: 1;
    }

    .home-detail {
      justify-content: flex-end;
      padding-right: 30px;

      .club-name {
        text-align: right;
      }
    }

    .away-detail {
      justify-content: flex-start;
      padding-left: 30px;

      .club-name {
        text-align: left;
      }
    }

    .match-detail {
      width: 54px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--color-black);
      border-radius: 16px;
      font-family: var(--font-jleague-kick);
      font-style: normal;
      font-weight: bold;
      font-size: 24px;
      line-height: 24px;
      text-align: right;
      color: var(--color-white);
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);

      &.postponed {
        background: var(--color-earth-3);
        font-family: var(--font-barlow-condensed);
        font-style: normal;
        font-weight: bold;
        font-size: 10px;
        line-height: 12px;
        text-transform: uppercase;
        color: var(--color-black);
      }

      &.match-detail--date {
        background: var(--color-earth-3);
        color: var(--color-black);
        font-size: 20px;
      }
    }
  }

  .match-card__button {
    color: var(--color-earth-4);
    font-size: 24px;
    margin-right: 16px;
    display: flex;
    justify-content: center;
    column-gap: 4px;
    min-width: 24px;

    i {
      display: inline-block;
      transition: all 0.3s linear;
    }

    .match-logo {
      max-width: 48px;
      height: 24px;
      overflow: hidden;

      img {
        height: 100%;
        width: auto;
      }
    }
  }

  &:hover {
    opacity: 1;

    .match-card__button {
      i {
        transform: translateX(6px);
      }
    }
  }

  @media screen and (max-width: 576px) {
    .match-card {
      .match-card__details {
        .club-name {
          font-size: 16px;
          line-height: 20px;
        }

        .match-detail {
          font-size: 20px;
          width: 48px;
        }

        .club-emblem {
          width: 32px;
          height: 32px;
          margin: 0 2px;
        }
      }
    }
  }

  @media screen and (max-width: 375px) {
    .match-card {
      .match-card__details {
        .home-detail,
        .away-detail {
          // min-width: 70px;
          // overflow: hidden;
          // white-space: nowrap;
          // text-overflow: ellipsis;
        }
      }
    }
  }
}

// body.lang-th {
//   .match-card .match-card__details .match-detail {
//     &.postponed {
//       @include font-thai();
//     }
//   }
// }

body.lang-id,
body.lang-vi,
body.lang-th {
  .match-card .match-card__details .match-detail {
    &.postponed {
      font-size: 14px;
      line-height: 14px;
    }

    &.match-detail--date {
      font-size: 18px;
      padding-top: 2px;
    }
  }
}
