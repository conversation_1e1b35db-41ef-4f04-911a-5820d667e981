# Generated by Django 4.1.4 on 2023-08-02 14:57

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("wagtailimages", "0024_index_image_file_hash"),
        ("core", "0025_alter_country_two_letter_code"),
    ]

    operations = [
        migrations.CreateModel(
            name="Banner",
            fields=[
                (
                    "id",
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.Char<PERSON>ield(max_length=255)),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "position",
                    models.CharField(
                        choices=[
                            ("top", "Top"),
                            ("bottom", "Bottom"),
                            ("middle", "Middle"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "language",
                    models.CharField(
                        choices=[
                            ("en", "English"),
                            ("id", "Bahasa"),
                            ("th", "Thai"),
                            ("vi", "Vietnamese"),
                        ],
                        max_length=20,
                    ),
                ),
                ("target_url", models.URLField(blank=True, null=True)),
                ("is_external_link", models.BooleanField(default=True)),
                ("is_active", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "start_at",
                    models.DateTimeField(
                        blank=True,
                        null=True,
                        help_text="This picker uses Asia/Tokyo timezone",
                    ),
                ),
                (
                    "ended_at",
                    models.DateTimeField(
                        blank=True,
                        null=True,
                        help_text="This picker uses Asia/Tokyo timezone",
                    ),
                ),
                (
                    "lg_image",
                    models.ForeignKey(
                        blank=True,
                        help_text="Banner size 1200x200px",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to="wagtailimages.image",
                    ),
                ),
                (
                    "md_image",
                    models.ForeignKey(
                        blank=True,
                        help_text="Banner size 688x200px",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to="wagtailimages.image",
                    ),
                ),
                (
                    "sm_image",
                    models.ForeignKey(
                        blank=True,
                        help_text="Banner size 420x200px",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to="wagtailimages.image",
                    ),
                ),
            ],
        ),
    ]
