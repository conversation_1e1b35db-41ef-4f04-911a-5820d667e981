@import 'setup/variable';
@import 'setup/mixin';

.body-club-profile {
  .club-profile {
    .club-profile-stats {
      margin-top: 80px;

      .club-profile-stats__filters {
        display: flex;
        justify-content: center;
        margin-top: 40px;
      }

      .club-profile-stats__content {
        &.loading {
          min-height: 1800px;
        }
      }

      .club-profile-stats__club-stats {
        .overall-stats {
          background: $color-blue;

          .league-name {
            color: $color-white;
            font-family: $font-jleague-kick;
            font-style: normal;
            font-weight: 700;
            font-size: 83px;
            line-height: 140px;
            text-transform: uppercase;
          }

          .stats-list {
            .stats-item {
              display: flex;
              flex-wrap: wrap;
            }

            .stats {
              flex: 1;
              // width: 25%;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              align-items: flex-start;
              padding: 24px 0;
              height: 214px;

              .stats__label {
                max-width: 100%;
                text-transform: uppercase;
                color: $color-white;
                font-family: $font-overpass;
                font-style: normal;
                font-weight: 400;
                font-size: 17px;
                line-height: 26px;
                @include text_ellipsis();
              }

              .stats__value {
                color: $color-white;
                font-family: $font-jleague-kick;
                font-style: normal;
                font-weight: 700;
                font-size: 150px;
                line-height: 140px;
                text-overflow: ellipsis;
                overflow: hidden;
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
              }
            }
          }
        }

        .wins-stats {
          display: flex;
          flex-wrap: wrap;

          .home-wins,
          .away-wins {
            flex: 1;
            padding: 37px 36px;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;

            &::after {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
            }

            .wins-stat-label {
              position: relative;
              text-transform: uppercase;
              white-space: nowrap;
              color: $color-white;
              z-index: 2;
              font-family: $font-jleague-kick;
              font-style: normal;
              font-weight: 700;
              font-size: 70px;
              line-height: 84px;
            }

            .wins-stat-value {
              position: relative;
              color: $color-white;
              z-index: 2;
              font-family: $font-jleague-kick;
              font-style: normal;
              font-weight: 700;
              font-size: 52px;
              line-height: 52px;

              & > span {
                font-family: $font-jleague-kick;
                font-style: normal;
                font-weight: 700;
                font-size: 150px;
                line-height: 95%;
              }
            }
          }

          .home-wins {
            background: $color-blue;

            &::after {
              background-color: rgba(#000000, 0.2);
            }
          }

          .away-wins {
            background: $color-blue;

            &::after {
              background-color: rgba($color-white, 0.2);
            }
          }
        }
      }

      .club-profile-stats__player-stats {
        display: flex;
        flex-wrap: wrap;
        column-gap: 32px;
        row-gap: 40px;
        margin-top: 40px;

        &.loading {
          position: relative;
          margin-top: 40px;
          min-height: 80px;

          .player-rank-board {
            max-height: 0;
            overflow: hidden;
            opacity: 0;
          }

          &::after {
            position: absolute;
            content: url('/static/images/loader.gif');
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }

      @media screen and (min-width: 1200px) {
        .club-profile-stats__club-stats {
          display: flex;
          margin-top: 40px;

          .overall-stats {
            flex: 1;
            padding: 24px 40px;
          }

          .wins-stats {
            flex: 0 0 auto;
            flex-direction: column;
            max-width: 400px;
          }
        }
      }

      @media screen and (max-width: 1199px) {
        margin-top: 40px;

        .club-profile-stats__club-stats {
          margin-top: 32px;

          .overall-stats {
            padding: 24px 20px;

            .league-name {
              font-size: 50px;
              line-height: 50px;
            }

            .stats-list {
              margin: 14px -20px 0;

              .stats {
                height: 150px;
                padding: 20px;

                .stats__label {
                  font-size: 13px;
                  line-height: 20px;
                }

                .stats__value {
                  font-size: 100px;
                  line-height: 90px;
                }
              }
            }
          }

          .wins-stats {
            .home-wins,
            .away-wins {
              padding: 34px 0;

              .wins-stat-label {
                font-size: 30px;
                line-height: 36px;
              }

              .wins-stat-value {
                & > span {
                  font-size: 120px;
                  line-height: 120px;
                }
              }
            }
          }
        }

        .club-profile-stats__player-stats {
          .player-rank-board {
            // width: calc((100% - 32px) / 2);

            .ranking-item {
              height: 72px;

              .player-photo {
                width: 40px;
              }

              .info {
                .player {
                  .player-name {
                    font-size: 16px;
                    line-height: 20px;
                  }
                }

                .team {
                  .club-emblem {
                    display: none;
                  }
                }
              }

              .value {
                font-size: 32px;
                line-height: 32px;
              }
            }
          }
        }
      }

      @media screen and (max-width: 767px) {
        margin-top: 32px;

        .club-profile-stats__content {
          &.loading {
            min-height: 1100px;
          }
        }

        .club-profile-stats__club-stats {
          margin-top: 16px;

          .overall-stats {
            .league-name {
              font-size: 40px;
              line-height: 40px;
            }

            .stats-list {
              .stats {
                .stats__label {
                  font-size: 11px;
                  line-height: 10px;
                }

                .stats__value {
                  font-size: 80px;
                  line-height: 80px;
                }
              }
            }
          }

          .wins-stats {
            .home-wins,
            .away-wins {
              padding: 30px 0;

              .wins-stat-value {
                font-size: 30px;
                line-height: 30px;

                & > span {
                  font-size: 90px;
                  line-height: 90px;
                }
              }
            }
          }
        }

        .club-profile-stats__player-stats {
          overflow: hidden;

          .player-rank-board {
            width: 324px;

            & > div:nth-child(1) {
              font-size: 20px;
              line-height: 24px;
            }

            & + .player-rank-board {
              margin-left: 24px;
            }
          }
        }
      }

      @media screen and (max-width: 576px) {
        .club-profile-stats__club-stats {
          .overall-stats {
            .stats-list {
              .stats {
                flex-basis: 50%;
              }
            }
          }
        }
      }

      @media screen and (max-width: 425px) {

        .club-profile-stats__player-stats {

          .player-rank-board {

            & + .player-rank-board {
              margin-left: 0px;
            }
          }
        }
      }
    }
  }
}

body.lang-th {
  .club-profile .club-profile-stats .club-profile-stats__club-stats {
    .overall-stats {
      .league-name,
      .stats-list .stats .stats__label,
      .stats-list .stats .stats__value {
        @include font-thai();
      }

      .stats-list .stats .stats__value {
        line-height: 1;
      }

      @media screen and (min-width: 1200px) {
        .stats-list .stats .stats__value {
          font-size: 130px;
        }
      }
    }

    .wins-stats {
      .home-wins,
      .away-wins {
        .wins-stat-label,
        .wins-stat-value {
          @include font-thai();
        }

        .wins-stat-value {
          line-height: 1;
        }
      }
    }
  }
}

body.lang-vi {
  .club-profile .club-profile-stats .club-profile-stats__club-stats {
    .overall-stats {
      .league-name,
      .stats-list .stats .stats__label,
      .stats-list .stats .stats__value {
        @include font-vietnamese();
      }

      .stats-list .stats .stats__value {
        line-height: 1;
      }
    }

    .wins-stats {
      .home-wins,
      .away-wins {
        .wins-stat-label,
        .wins-stat-value {
          @include font-vietnamese();
        }

        .wins-stat-value {
          line-height: 1;
        }
      }
    }
  }
}

body.lang-id,
body.lang-vi {
  .club-profile .club-profile-stats .club-profile-stats__club-stats {
    @media screen and (min-width: 1200px) {
      .overall-stats {
        .league-name {
          font-size: 60px;
          line-height: 1.33;
        }

        .stats-list .stats .stats__label {
          font-size: 20px;
        }
      }

      .wins-stats {
        .home-wins,
        .away-wins {
          .wins-stat-label {
            font-size: 42px;
            line-height: 1.33;
          }
        }
      }
    }

    @media screen and (max-width: 899px) {
      .wins-stats {
        flex-direction: column;
      }
    }
  }
}
