@import 'setup/variable';

.team-text {
  color: $color-black;

  @each $name, $color in $club-text-colors {
    &.team-text--#{$name} {
      color: $color !important;
    }
  }

  @each $name, $color in $club-secondary-colors {
    &.team-text--#{$name}.team-text--secondary {
      color: $color !important;
    }
  }
}

.team-svg-color {
  path {
    fill: $color-black;
  }

  @each $name, $color in $club-text-colors {
    &.team-svg-color--#{$name} {
      path {
        fill: $color !important;
      }
    }
  }

  @each $name, $color in $club-secondary-colors {
    &.team-svg-color--#{$name}.team-svg-color--secondary {
      path {
        fill: $color !important;
      }
    }
  }
}
