.section-header {
  margin-bottom: 36px;
}

.broadcast-map {
  position: relative;
  border: 1px solid var(--color-earth-2);
  border-radius: 3px;

  .broadcast-map__map {
    aspect-ratio: 16/9;
    height: auto;
    width: 100%;
  }

  .broadcast-map__legends {
    background: var(--color-white);
    position: absolute;
    bottom: 20px;
    left: 20px;

    .broadcast-map__legends__item {
      .broadcast-map__legends__item__color {
        display: inline-block;
        width: 15px;
        height: 15px;
        border-radius: 50%;
        margin-right: 5px;
      }

      .broadcast-map__legends__item__label {
        color: var(--color-black);
        font-family: var(--font-overpass);
        font-style: normal;
        font-weight: 400;
        font-size: 20px;
        line-height: 32px;
      }
    }
  }
}

.table {
  width: 100%;
  table-layout: fixed;
  border: solid 1px var(--color-black);

  th {
    background-color: var(--color-earth-2);
  }

  th,
  td {
    border: solid 1px var(--color-black);
    text-align: left;
    vertical-align: middle;
    padding: 20px;
    height: auto;
    color: var(--color-black);
    font-family: var(--font-overpass);
    font-style: normal;
    font-weight: 400;
    font-size: 20px;
    line-height: 32px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  a {
    color: var(--color-red);
  }
}

.content-container {
  .content-container__body {
    .body-text {
      font-family: var(--font-overpass);
      font-style: normal;
      font-weight: 400;
      font-size: 20px;
      line-height: 32px;

      &.body-text--large {
        font-size: 26px;
      }

      &.body-text--small {
        font-size: 14px;
        line-height: 14px;
      }

      &.body-text--strong {
        font-weight: 700;
      }
    }
  }
}

/* JQV MAP */
.jqvmap-zoomin,
.jqvmap-zoomout {
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: var(--font-barlow-condensed);
  font-size: 20px;
  font-weight: 900;
  line-height: normal;
  margin: 0;
  padding: 0;
  height: 20px;
  width: 20px;
  left: 20px;
  background-color: var(--color-black);
  cursor: pointer;
  position: absolute;
  color: var(--color-white);
}

.jqvmap-zoomin {
  top: 20px;
}

.jqvmap-zoomout {
  top: 42px;
}

.jqvmap-region {
  cursor: pointer;
}

.jqvmap-label {
  position: absolute;
  display: none;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
  background: var(--color-black);
  color: var(--color-white);
  font-family: var(--font-barlow-condensed);
  padding: 3px 6px 5px;
  pointer-events: none;
}

.jqvmap-pin {
  pointer-events: none;
}

.jqvmap-ajax_response {
  width: 100%;
  height: 500px;
}

@media screen and (max-width: 767px) {
  .table {
    th,
    td {
      font-size: 18px;
      line-height: 28px;
    }
  }

  .content-container {
    .content-container__body {
      .body-text {
        font-size: 18px;
        line-height: 28px;
      }
    }
  }

  .broadcast-map {
    .broadcast-map__legends {
      .broadcast-map__legends__item {
        .broadcast-map__legends__item__color {
          width: 10px;
          height: 10px;
          margin-right: 0px;
        }

        .broadcast-map__legends__item__label {
          font-size: 12px;
          line-height: 12px;
        }
      }
    }
  }
}

@media screen and (max-width: 576px) {
  .table {
    th,
    td {
      font-size: 16px;
      line-height: 20px;
    }
  }

  .content-container {
    .content-container__body {
      .body-text {
        font-size: 16px;
        line-height: 120%;
      }
    }
  }
}
