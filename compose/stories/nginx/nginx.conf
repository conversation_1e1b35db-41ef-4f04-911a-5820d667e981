user nginx;
worker_processes 1;

error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;


events {
  worker_connections 1024;
}


http {
  include /etc/nginx/mime.types;
  default_type application/octet-stream;

  log_format main '$remote_addr - $remote_user [$time_local] "$request" '
  '$status $body_bytes_sent "$http_referer" '
  '"$http_user_agent" "$http_x_forwarded_for" "$upstream_cache_status"';

  access_log /var/log/nginx/access.log main;
  client_max_body_size 50m;
  gzip on;
  keepalive_timeout 60;
  sendfile on;

  proxy_cache_path /nginx-stories-cache levels=1:2 keys_zone=my-cache:8m max_size=10000m inactive=600m;
  proxy_temp_path /nginx-stories-cache/tmp;

  server {
    listen 127.0.0.1:8999;
    listen [::]:8999;

    location /stub_status {
      # https://nginx.org/en/docs/http/ngx_http_stub_status_module.html#stub_status
      stub_status;
      access_log off;
      allow 127.0.0.1;
      deny all;
    }
  }

  server {
    # https://nginx.org/en/docs/http/ngx_http_core_module.html#listen
    listen 80 default_server;
    listen [::]:80 default_server;
    listen 443 ssl;
    # https://nginx.org/en/docs/http/ngx_http_core_module.html#server_name
    server_name stories.jleague.co;
    # https://nginx.org/en/docs/http/ngx_http_ssl_module.html#ssl_certificate
    ssl_certificate /etc/letsencrypt/live/jleague.co/fullchain.pem;
    # https://nginx.org/en/docs/http/ngx_http_ssl_module.html#ssl_certificate_key
    ssl_certificate_key /etc/letsencrypt/live/jleague.co/privkey.pem;
    # https://nginx.org/en/docs/http/ngx_http_ssl_module.html#ssl_ciphers
    ssl_ciphers "ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384";
    # https://nginx.org/en/docs/http/ngx_http_ssl_module.html#ssl_protocols
    ssl_protocols TLSv1.2 TLSv1.3;
    # https://nginx.org/en/docs/http/ngx_http_ssl_module.html#ssl_session_cache
    ssl_session_cache shared:le_nginx_SSL:10m;
    # https://nginx.org/en/docs/http/ngx_http_ssl_module.html#ssl_session_tickets
    ssl_session_tickets off;
    # https://nginx.org/en/docs/http/ngx_http_ssl_module.html#ssl_session_timeout
    ssl_session_timeout 1440m;

    location / {
      # https://nginx.org/en/docs/http/ngx_http_proxy_module.html#proxy_pass
      proxy_pass https://wscstoriesjleague.blob.core.windows.net;
      # https://nginx.org/en/docs/http/ngx_http_proxy_module.html#proxy_set_header
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-Proto $scheme;

      proxy_cache my-cache;
      proxy_cache_valid 200 302 60m;
      proxy_cache_valid 404 1m;
      proxy_cache_valid any 1m;

      add_header Access-Control-Allow-Credentials true;
      add_header Access-Control-Allow-Origin "https://stories-jleague-co.cdn.ampproject.org";
      add_header AMP-Same-Origin true;
      add_header X-Cache-Status $upstream_cache_status;
    }
  }
}
