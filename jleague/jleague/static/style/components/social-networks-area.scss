@import 'setup/mixin';

.social-networks-area {
  background: var(--color-black);
  padding: 80px 120px;
  margin-top: 80px;

  .social-networks-area__header,
  .social-networks-area__body {
    max-width: 1440px;
    margin: 0 auto;
  }

  .social-networks-area__header {
    .social-networks-area__header__title {
      color: var(--color-white);
      font-family: var(--font-jleague-kick);
      font-style: normal;
      font-weight: 700;
      font-size: 42px;
      line-height: 48px;
      letter-spacing: 2px;
      text-align: center;
      text-transform: uppercase;
    }
  }

  .social-networks-area__body {
    padding-top: 80px;
  }

  .social-networks-list {
    display: flex;
    row-gap: 80px;
    flex-wrap: wrap;
    overflow: hidden;
    justify-content: center;

    .social-networks-list__item {
      display: flex;
      justify-content: center;
      align-items: center;
      flex: 0 0 220px;
    }
  }

  .social-network-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: none;

    .social-network-item__icon {
      height: 96px;
      width: 96px;

      .social-network-item__icon__normal,
      .social-network-item__icon__hovered {
        width: 100%;
        height: 100%;
      }

      .social-network-item__icon__hovered {
        display: none;
      }
    }

    .social-network-item__title {
      color: var(--color-white);
      font-family: var(--font-overpass);
      font-style: normal;
      font-weight: 900;
      font-size: 18px;
      line-height: 28px;
      margin-top: 16px;
      text-align: center;
      @include text_ellipsis();
    }

    &:hover {
      opacity: 1;

      .social-network-item__icon {
        .social-network-item__icon__normal {
          display: none;
        }

        .social-network-item__icon__hovered {
          display: block;
        }
      }
    }
  }

  @media screen and (max-width: 767px) {
    padding: 80px 48px;

    .social-networks-area__header {
      .social-networks-area__header__title {
        font-size: 32px;
        line-height: 32px;
      }
    }

    .social-networks-list {
      row-gap: 40px;
    }

    .social-network-item {
      .social-network-item__icon {
        height: 64px;
        width: 64px;
      }
    }
  }

  @media screen and (max-width: 499px) {
    .social-networks-list {
      flex-direction: column;

      .social-networks-list__item {
        min-width: unset;
        flex: 0 0 auto;
      }
    }
  }
}

body.lang-vi {
  .social-networks-area .social-network-item .social-network-item__title {
    font-weight: 600;
  }
}
