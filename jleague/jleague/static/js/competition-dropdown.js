const competitionDropdown = (function () {
  const dropdownItemSelector = '.competition-dropdown__items';
  const dropdownValueSelector = '.competition-dropdown__value';
  const dropdownVisibleClass = 'competition-dropdown__items--visible';
  const dropdownChoiceItemSelector = '.dropdown-item';
  const dropdownChoiceItemSelectedClass = 'dropdown-item--selected';
  const dropdownValueLogoSelector = '.competition-logo';
  const dropdownValueLogoSelectedClass = 'competition-logo--selected';
  const dropdownElements = {};
  let dropdownSelectedChoice;
  let dropdownSelectedElement;
  let isDropdownOpen = false;

  function _closeDropdownChoice() {
    if (typeof dropdownSelectedChoice === 'undefined') return;
    dropdownSelectedChoice.css('opacity', 0);
    dropdownSelectedChoice.removeClass(dropdownVisibleClass);
  }

  function _openDropdownChoice() {
    if (typeof dropdownSelectedChoice === 'undefined') return;
    dropdownSelectedChoice.css('opacity', 1);
    dropdownSelectedChoice.addClass(dropdownVisibleClass);
  }

  function _onCompetitionDropdownValueClick(event) {
    event.stopPropagation();
    dropdownSelectedElement = $(this);
    dropdownSelectedChoice = dropdownSelectedElement.parent().find(dropdownItemSelector);
    if (!dropdownSelectedChoice || dropdownSelectedChoice.length === 0) {
      return;
    }

    sendElementClickEvent('competition-dropdown', '', dropdownValueSelector);

    isDropdownOpen = !isDropdownOpen;
    if (isDropdownOpen) {
      _openDropdownChoice();
    } else {
      _closeDropdownChoice();
    }
  }

  function _onCompetitionDropdownItemClick(element, selector, callback) {
    const itemElm = $(element);
    const competition_slug = itemElm.data('value');

    sendElementClickEvent('competition-dropdown', competition_slug, dropdownItemSelector);

    // update selected item
    itemElm
      .parent()
      .find(dropdownChoiceItemSelector)
      .each(function () {
        $(this).removeClass(dropdownChoiceItemSelectedClass);
      });
    itemElm.addClass(dropdownChoiceItemSelectedClass);

    // update selected value
    dropdownElements[selector].find(dropdownValueLogoSelector).each(function () {
      const elm = $(this);
      const dropdownValueLogoClass = dropdownValueLogoSelector.replace('.', '');
      if (elm.hasClass(dropdownValueLogoSelectedClass)) {
        elm.removeClass(dropdownValueLogoSelectedClass);
      }
      if (elm.hasClass(`${dropdownValueLogoClass}--${competition_slug}`)) {
        elm.addClass(dropdownValueLogoSelectedClass);
      }
    });

    if (typeof callback === 'function') {
      callback(competition_slug, selector);
    }
  }

  function init(selector, callback) {
    const parent = $(selector);
    if (!parent || parent.length === 0) {
      return;
    }

    const element = parent.find(dropdownValueSelector);
    if (!element || element.length === 0) {
      return;
    }

    dropdownElements[selector] = element;

    // bind click event to dropdown value
    element.click(_onCompetitionDropdownValueClick);

    // bind click event to choice items
    parent
      .find(dropdownItemSelector)
      .find(dropdownChoiceItemSelector)
      .each(function () {
        $(this).click(function (e) {
          e.stopPropagation();
          e.preventDefault();
          _onCompetitionDropdownItemClick(this, selector, callback);
        });
      });

    // close dropdown choice when click outside
    $(document).on('mouseup', function (e) {
      if (!dropdownSelectedElement) {
        return;
      }
      if (!dropdownSelectedElement.is(e.target) && dropdownSelectedElement.has(e.target).length === 0) {
        _closeDropdownChoice();
      }
    });

    $(document).on('scroll', function (e) {
      _closeDropdownChoice();
    });
  }

  return { init };
})();
