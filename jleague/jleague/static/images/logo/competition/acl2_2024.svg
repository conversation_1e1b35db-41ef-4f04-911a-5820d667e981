<?xml version="1.0" encoding="UTF-8"?>
<svg width="260" height="80" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 260 80">
  <defs>
    <style>
      .cls-1 {
        fill: url(#radial-gradient-7);
      }

      .cls-2 {
        fill: url(#radial-gradient-6);
      }

      .cls-3 {
        fill: url(#radial-gradient-5);
      }

      .cls-4 {
        fill: url(#radial-gradient-4);
      }

      .cls-5 {
        fill: url(#radial-gradient-9);
      }

      .cls-6 {
        fill: url(#radial-gradient-8);
      }

      .cls-7 {
        fill: url(#radial-gradient-3);
      }

      .cls-8 {
        fill: url(#radial-gradient-2);
      }

      .cls-9 {
        fill: url(#linear-gradient-8);
      }

      .cls-10 {
        fill: url(#linear-gradient-7);
      }

      .cls-11 {
        fill: url(#linear-gradient-5);
      }

      .cls-12 {
        fill: url(#linear-gradient-6);
      }

      .cls-13 {
        fill: url(#linear-gradient-9);
      }

      .cls-14 {
        fill: url(#linear-gradient-4);
      }

      .cls-15 {
        fill: url(#linear-gradient-3);
      }

      .cls-16 {
        fill: url(#linear-gradient-2);
      }

      .cls-17 {
        fill: #060c33;
      }

      .cls-18 {
        fill: url(#radial-gradient);
      }

      .cls-19 {
        fill: url(#linear-gradient);
      }

      .cls-20 {
        fill: #00a8e1;
      }

      .cls-21 {
        fill: url(#radial-gradient-12);
      }

      .cls-22 {
        fill: url(#radial-gradient-14);
      }

      .cls-23 {
        fill: url(#radial-gradient-13);
      }

      .cls-24 {
        fill: url(#radial-gradient-10);
      }

      .cls-25 {
        fill: url(#radial-gradient-11);
      }

      .cls-26 {
        fill: url(#linear-gradient-34);
      }

      .cls-26, .cls-27, .cls-28, .cls-29, .cls-30, .cls-31, .cls-32, .cls-33 {
        opacity: .9;
      }

      .cls-27 {
        fill: url(#linear-gradient-35);
      }

      .cls-28 {
        fill: url(#linear-gradient-33);
      }

      .cls-29 {
        fill: url(#linear-gradient-17);
      }

      .cls-30 {
        fill: url(#linear-gradient-32);
      }

      .cls-31 {
        fill: url(#linear-gradient-22);
      }

      .cls-32 {
        fill: url(#linear-gradient-40);
      }

      .cls-33 {
        fill: url(#linear-gradient-41);
      }

      .cls-34 {
        fill: url(#linear-gradient-21);
      }

      .cls-34, .cls-35 {
        opacity: .8;
      }

      .cls-35 {
        fill: url(#linear-gradient-23);
      }

      .cls-36 {
        fill: url(#linear-gradient-36);
      }

      .cls-36, .cls-37 {
        opacity: .4;
      }

      .cls-37 {
        fill: url(#linear-gradient-37);
      }

      .cls-38 {
        fill: url(#linear-gradient-42);
      }

      .cls-39 {
        fill: url(#linear-gradient-10);
      }

      .cls-40 {
        fill: url(#linear-gradient-11);
      }

      .cls-41 {
        fill: url(#linear-gradient-12);
      }

      .cls-42 {
        fill: url(#linear-gradient-13);
      }

      .cls-43 {
        fill: url(#linear-gradient-19);
      }

      .cls-44 {
        fill: url(#linear-gradient-14);
      }

      .cls-45 {
        fill: url(#linear-gradient-20);
      }

      .cls-46 {
        fill: url(#linear-gradient-28);
      }

      .cls-47 {
        fill: url(#linear-gradient-25);
      }

      .cls-48 {
        fill: url(#linear-gradient-31);
      }

      .cls-49 {
        fill: url(#linear-gradient-30);
      }

      .cls-50 {
        fill: url(#linear-gradient-15);
      }

      .cls-51 {
        fill: url(#linear-gradient-16);
      }

      .cls-52 {
        fill: url(#linear-gradient-18);
      }

      .cls-53 {
        fill: url(#linear-gradient-38);
      }

      .cls-54 {
        fill: url(#linear-gradient-27);
      }

      .cls-55 {
        fill: url(#linear-gradient-24);
      }

      .cls-56 {
        fill: url(#linear-gradient-26);
      }

      .cls-57 {
        fill: url(#linear-gradient-29);
      }

      .cls-58 {
        fill: url(#linear-gradient-39);
      }
    </style>
    <radialGradient id="radial-gradient" cx="-10963.86" cy="15.84" fx="-10963.86" fy="15.84" r="42.2" gradientTransform="translate(-10603.93) rotate(-180) scale(.97 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".32" stop-color="#cdd5ec"/>
      <stop offset=".33" stop-color="#cad3eb"/>
      <stop offset=".43" stop-color="#7facd8"/>
      <stop offset=".5" stop-color="#5093cd"/>
      <stop offset=".53" stop-color="#3f8ac9"/>
      <stop offset=".54" stop-color="#3e84c4"/>
      <stop offset=".64" stop-color="#395aa7"/>
      <stop offset=".84" stop-color="#05054c"/>
    </radialGradient>
    <radialGradient id="radial-gradient-2" cx="-10964.56" cy="14.91" fx="-10964.56" fy="14.91" r="47.26" gradientTransform="translate(-10603.93) rotate(-180) scale(.97 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".41" stop-color="#cdd5ec"/>
      <stop offset=".41" stop-color="#cad3eb"/>
      <stop offset=".47" stop-color="#7facd8"/>
      <stop offset=".51" stop-color="#5093cd"/>
      <stop offset=".53" stop-color="#3f8ac9"/>
      <stop offset=".54" stop-color="#3e84c4"/>
      <stop offset=".64" stop-color="#395aa7"/>
      <stop offset=".84" stop-color="#05054c"/>
    </radialGradient>
    <radialGradient id="radial-gradient-3" cx="-10984.03" cy="7.31" fx="-10984.03" fy="7.31" r="26.05" gradientTransform="translate(-10603.93) rotate(-180) scale(.97 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".32" stop-color="#cdd5ec"/>
      <stop offset=".33" stop-color="#cad3eb"/>
      <stop offset=".44" stop-color="#7facd8"/>
      <stop offset=".51" stop-color="#5093cd"/>
      <stop offset=".55" stop-color="#3f8ac9"/>
      <stop offset=".56" stop-color="#3e86c6"/>
      <stop offset=".68" stop-color="#365ca9"/>
      <stop offset=".84" stop-color="#06044c"/>
    </radialGradient>
    <radialGradient id="radial-gradient-4" cx="-10974.97" cy="8.93" fx="-10974.97" fy="8.93" r="35.89" gradientTransform="translate(-10603.93) rotate(-180) scale(.97 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".44" stop-color="#cdd5ec"/>
      <stop offset=".44" stop-color="#cad3eb"/>
      <stop offset=".5" stop-color="#7facd8"/>
      <stop offset=".54" stop-color="#5093cd"/>
      <stop offset=".56" stop-color="#3f8ac9"/>
      <stop offset=".57" stop-color="#3e84c4"/>
      <stop offset=".64" stop-color="#395aa7"/>
      <stop offset=".84" stop-color="#05054c"/>
    </radialGradient>
    <linearGradient id="linear-gradient" x1="-10828.91" y1="8.04" x2="-10833.66" y2="12.74" gradientTransform="translate(-10785.14) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".17" stop-color="#cdd5ec"/>
      <stop offset=".18" stop-color="#cad3eb"/>
      <stop offset=".35" stop-color="#7facd8"/>
      <stop offset=".48" stop-color="#5093cd"/>
      <stop offset=".54" stop-color="#3f8ac9"/>
      <stop offset=".57" stop-color="#3e84c4"/>
      <stop offset=".81" stop-color="#395aa7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-2" x1="-10825.43" y1="28.8" x2="-10825.18" y2="-1.51" gradientTransform="translate(-10785.14) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".21" stop-color="#cdd5ec"/>
      <stop offset=".22" stop-color="#cad3eb"/>
      <stop offset=".34" stop-color="#7facd8"/>
      <stop offset=".42" stop-color="#5093cd"/>
      <stop offset=".46" stop-color="#3f8ac9"/>
      <stop offset=".5" stop-color="#3e84c4"/>
      <stop offset=".81" stop-color="#395aa7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="-10825.07" y1="37.44" x2="-10814.05" y2="-5.47" gradientTransform="translate(-10785.14) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".26" stop-color="#cdd5ec"/>
      <stop offset=".26" stop-color="#cad3eb"/>
      <stop offset=".38" stop-color="#7facd8"/>
      <stop offset=".46" stop-color="#5093cd"/>
      <stop offset=".51" stop-color="#3f8ac9"/>
      <stop offset=".53" stop-color="#3e84c4"/>
      <stop offset=".74" stop-color="#395aa7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="-10823.38" y1="50.18" x2="-10813" y2="-7.72" gradientTransform="translate(-10785.14) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".24" stop-color="#cdd5ec"/>
      <stop offset=".25" stop-color="#cad3eb"/>
      <stop offset=".37" stop-color="#7facd8"/>
      <stop offset=".46" stop-color="#5093cd"/>
      <stop offset=".51" stop-color="#3f8ac9"/>
      <stop offset=".53" stop-color="#3e84c4"/>
      <stop offset=".7" stop-color="#395aa7"/>
      <stop offset=".84" stop-color="#05054c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-5" x1="38.7" y1="73.03" x2="11.24" y2="73.03" gradientUnits="userSpaceOnUse">
      <stop offset=".12" stop-color="#395aa7"/>
      <stop offset=".13" stop-color="#3961ac"/>
      <stop offset=".18" stop-color="#3c77bc"/>
      <stop offset=".23" stop-color="#3e85c5"/>
      <stop offset=".27" stop-color="#3f8ac9"/>
      <stop offset=".39" stop-color="#3f8ac9"/>
      <stop offset=".6" stop-color="#3f8ac9"/>
      <stop offset=".74" stop-color="#3f8ac9"/>
      <stop offset=".76" stop-color="#3e84c4"/>
      <stop offset=".9" stop-color="#395aa7"/>
    </linearGradient>
    <radialGradient id="radial-gradient-5" cx="224.2" cy="15.84" fx="224.2" fy="15.84" r="42.2" gradientTransform="translate(-181.21) scale(.97 1)" xlink:href="#radial-gradient"/>
    <radialGradient id="radial-gradient-6" cx="223.49" cy="14.91" fx="223.49" fy="14.91" r="47.26" gradientTransform="translate(-181.21) scale(.97 1)" xlink:href="#radial-gradient-2"/>
    <radialGradient id="radial-gradient-7" cx="204.03" cy="7.31" fx="204.03" fy="7.31" r="26.05" gradientTransform="translate(-181.21) scale(.97 1)" gradientUnits="userSpaceOnUse">
      <stop offset=".4" stop-color="#cdd5ec"/>
      <stop offset=".4" stop-color="#cad3eb"/>
      <stop offset=".47" stop-color="#7facd8"/>
      <stop offset=".52" stop-color="#5093cd"/>
      <stop offset=".55" stop-color="#3f8ac9"/>
      <stop offset=".56" stop-color="#3e86c6"/>
      <stop offset=".67" stop-color="#355caa"/>
      <stop offset=".84" stop-color="#06044c"/>
    </radialGradient>
    <radialGradient id="radial-gradient-8" cx="213.09" cy="8.93" fx="213.09" fy="8.93" r="35.89" gradientTransform="translate(-181.21) scale(.97 1)" xlink:href="#radial-gradient-4"/>
    <radialGradient id="radial-gradient-9" cx="180.49" cy="11.82" fx="180.49" fy="11.82" r="52.58" gradientTransform="translate(-181.21) scale(.97 1)" gradientUnits="userSpaceOnUse">
      <stop offset=".16" stop-color="#d0d3d3"/>
      <stop offset=".16" stop-color="#cdd0d2"/>
      <stop offset=".26" stop-color="#7d91bb"/>
      <stop offset=".32" stop-color="#4c69ac"/>
      <stop offset=".36" stop-color="#395aa7"/>
      <stop offset=".36" stop-color="#395aa7"/>
      <stop offset=".4" stop-color="#3b6fb6"/>
      <stop offset=".45" stop-color="#3d7ec0"/>
      <stop offset=".5" stop-color="#3e87c7"/>
      <stop offset=".58" stop-color="#3f8ac9"/>
      <stop offset=".76" stop-color="#172f73"/>
      <stop offset=".86" stop-color="#05054c"/>
    </radialGradient>
    <linearGradient id="linear-gradient-6" x1="20.3" y1="53.08" x2="29.57" y2="53.08" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#1c2472"/>
      <stop offset=".17" stop-color="#395aa7"/>
      <stop offset=".19" stop-color="#395ca8"/>
      <stop offset=".37" stop-color="#3a6db4"/>
      <stop offset=".5" stop-color="#3b74b9"/>
      <stop offset=".81" stop-color="#395aa7"/>
      <stop offset="1" stop-color="#1c2472"/>
    </linearGradient>
    <linearGradient id="linear-gradient-7" x1="-3.02" y1="22.52" x2="24.96" y2="53.74" gradientUnits="userSpaceOnUse">
      <stop offset=".35" stop-color="#d0d3d3"/>
      <stop offset=".44" stop-color="#83accd"/>
      <stop offset=".51" stop-color="#5293ca"/>
      <stop offset=".54" stop-color="#3f8ac9"/>
      <stop offset=".74" stop-color="#172f73"/>
      <stop offset=".85" stop-color="#05054c"/>
    </linearGradient>
    <radialGradient id="radial-gradient-10" cx="194.25" cy="26.54" fx="194.25" fy="26.54" r="33.84" gradientTransform="translate(-181.21) scale(.97 1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#d0d3d3"/>
      <stop offset=".04" stop-color="#bbc2cd"/>
      <stop offset=".19" stop-color="#758ab8"/>
      <stop offset=".3" stop-color="#4967ab"/>
      <stop offset=".35" stop-color="#395aa7"/>
      <stop offset=".55" stop-color="#3f8ac9"/>
      <stop offset=".74" stop-color="#172f73"/>
      <stop offset=".84" stop-color="#05054c"/>
    </radialGradient>
    <linearGradient id="linear-gradient-8" x1="6.17" y1="8.04" x2="1.42" y2="12.74" gradientUnits="userSpaceOnUse">
      <stop offset=".17" stop-color="#cdd5ec"/>
      <stop offset=".17" stop-color="#cad3eb"/>
      <stop offset=".33" stop-color="#7facd8"/>
      <stop offset=".44" stop-color="#5093cd"/>
      <stop offset=".5" stop-color="#3f8ac9"/>
      <stop offset=".53" stop-color="#3e84c4"/>
      <stop offset=".81" stop-color="#395aa7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-9" x1="-1.21" y1="8.58" x2="9.89" y2="22.55" gradientUnits="userSpaceOnUse">
      <stop offset=".01" stop-color="#3767b0"/>
      <stop offset=".55" stop-color="#395aa7"/>
      <stop offset=".9" stop-color="#05054c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-10" x1="9.64" y1="28.8" x2="9.89" y2="-1.51" gradientTransform="matrix(1,0,0,1,0,0)" xlink:href="#linear-gradient-2"/>
    <linearGradient id="linear-gradient-11" x1="10.01" y1="37.44" x2="21.03" y2="-5.47" gradientTransform="matrix(1,0,0,1,0,0)" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-12" x1="11.7" y1="50.18" x2="22.08" y2="-7.72" gradientTransform="matrix(1,0,0,1,0,0)" xlink:href="#linear-gradient-4"/>
    <linearGradient id="linear-gradient-13" x1="24.91" y1="5.41" x2="13.85" y2="61.31" gradientUnits="userSpaceOnUse">
      <stop offset=".09" stop-color="#cdd5ec"/>
      <stop offset=".11" stop-color="#c1cfe9"/>
      <stop offset=".22" stop-color="#7baad7"/>
      <stop offset=".3" stop-color="#4f92cd"/>
      <stop offset=".34" stop-color="#3f8ac9"/>
      <stop offset=".73" stop-color="#172f74"/>
      <stop offset=".93" stop-color="#05054c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-14" x1="21.5" y1="15.97" x2="21.5" y2="64.5" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#3f8ac9"/>
      <stop offset=".02" stop-color="#3e84c4"/>
      <stop offset=".15" stop-color="#395aa7"/>
      <stop offset=".53" stop-color="#1d2c76"/>
      <stop offset=".9" stop-color="#05054c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-15" x1="16.46" y1="35.64" x2="24.81" y2="57.16" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#d0d3d3"/>
      <stop offset=".14" stop-color="#9ab5ca"/>
      <stop offset=".35" stop-color="#548ebe"/>
      <stop offset=".5" stop-color="#2876b7"/>
      <stop offset=".57" stop-color="#186db5"/>
      <stop offset=".93" stop-color="#05054c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-16" x1="-10797.84" y1="-50.54" x2="-10773.59" y2="-50.54" gradientTransform="translate(-10583.18 109.65) rotate(-180) scale(.98 -1.02)" gradientUnits="userSpaceOnUse">
      <stop offset=".16" stop-color="#395aa7"/>
      <stop offset=".18" stop-color="#3961ac"/>
      <stop offset=".28" stop-color="#3c77bc"/>
      <stop offset=".37" stop-color="#3e85c5"/>
      <stop offset=".45" stop-color="#3f8ac9"/>
      <stop offset=".56" stop-color="#3f8ac9"/>
      <stop offset=".59" stop-color="#3e84c4"/>
      <stop offset=".86" stop-color="#395aa7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-17" x1="-10804.03" y1="52.6" x2="-10808.6" y2="57.69" gradientTransform="translate(-10785.05) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".21" stop-color="#cdd5ec"/>
      <stop offset=".27" stop-color="#cdd5ec" stop-opacity=".86"/>
      <stop offset=".39" stop-color="#cdd5ec" stop-opacity=".6"/>
      <stop offset=".5" stop-color="#cdd5ec" stop-opacity=".38"/>
      <stop offset=".61" stop-color="#cdd5ec" stop-opacity=".22"/>
      <stop offset=".7" stop-color="#cdd5ec" stop-opacity=".1"/>
      <stop offset=".79" stop-color="#cdd5ec" stop-opacity=".03"/>
      <stop offset=".85" stop-color="#cdd5ec" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-18" x1="35.91" y1="56.02" x2="13.79" y2="56.02" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#cdd5ec"/>
      <stop offset="0" stop-color="#c5cee8"/>
      <stop offset=".04" stop-color="#93a5d1"/>
      <stop offset=".08" stop-color="#6c84be"/>
      <stop offset=".11" stop-color="#506db1"/>
      <stop offset=".14" stop-color="#3f5fa9"/>
      <stop offset=".16" stop-color="#395aa7"/>
      <stop offset=".24" stop-color="#395aa7"/>
      <stop offset=".27" stop-color="#3961ac"/>
      <stop offset=".35" stop-color="#3c77bc"/>
      <stop offset=".43" stop-color="#3e85c5"/>
      <stop offset=".5" stop-color="#3f8ac9"/>
      <stop offset=".53" stop-color="#3e84c4"/>
      <stop offset=".76" stop-color="#395aa7"/>
      <stop offset=".86" stop-color="#395aa7"/>
      <stop offset="1" stop-color="#cdd5ec"/>
    </linearGradient>
    <linearGradient id="linear-gradient-19" x1="37.75" y1="65.87" x2="12.19" y2="65.74" gradientUnits="userSpaceOnUse">
      <stop offset=".16" stop-color="#395aa7"/>
      <stop offset=".19" stop-color="#3961ac"/>
      <stop offset=".3" stop-color="#3c77bc"/>
      <stop offset=".41" stop-color="#3e85c5"/>
      <stop offset=".5" stop-color="#3f8ac9"/>
      <stop offset=".56" stop-color="#3f8ac9"/>
      <stop offset=".59" stop-color="#3e84c4"/>
      <stop offset=".86" stop-color="#395aa7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-20" x1="37.73" y1="71.06" x2="12.2" y2="70.94" gradientUnits="userSpaceOnUse">
      <stop offset=".16" stop-color="#395aa7"/>
      <stop offset=".18" stop-color="#3961ac"/>
      <stop offset=".25" stop-color="#3c77bc"/>
      <stop offset=".33" stop-color="#3e85c5"/>
      <stop offset=".39" stop-color="#3f8ac9"/>
      <stop offset=".61" stop-color="#3f8ac9"/>
      <stop offset=".64" stop-color="#3e84c4"/>
      <stop offset=".86" stop-color="#395aa7"/>
    </linearGradient>
    <linearGradient id="linear-gradient-21" x1="34.09" y1="71" x2="27.67" y2="71" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#395aa7"/>
      <stop offset="0" stop-color="#395aa7"/>
      <stop offset=".65" stop-color="#3d7cbf"/>
      <stop offset="1" stop-color="#3f8ac9"/>
    </linearGradient>
    <linearGradient id="linear-gradient-22" x1="12.32" y1="71" x2="17.47" y2="71" gradientUnits="userSpaceOnUse">
      <stop offset=".28" stop-color="#cdd5ec" stop-opacity=".8"/>
      <stop offset=".4" stop-color="#cdd5ec" stop-opacity=".6"/>
      <stop offset=".55" stop-color="#cdd5ec" stop-opacity=".38"/>
      <stop offset=".69" stop-color="#cdd5ec" stop-opacity=".22"/>
      <stop offset=".81" stop-color="#cdd5ec" stop-opacity=".1"/>
      <stop offset=".92" stop-color="#cdd5ec" stop-opacity=".03"/>
      <stop offset="1" stop-color="#cdd5ec" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-23" x1="-10801.03" y1="71" x2="-10807.45" y2="71" gradientTransform="translate(-10785.18) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#395aa7"/>
      <stop offset="0" stop-color="#395aa7"/>
      <stop offset=".64" stop-color="#3d7cbf"/>
      <stop offset=".99" stop-color="#3f8ac9"/>
    </linearGradient>
    <radialGradient id="radial-gradient-11" cx="-11007.61" cy="11.82" fx="-11007.61" fy="11.82" r="52.58" gradientTransform="translate(-10603.97) rotate(-180) scale(.97 -1)" xlink:href="#radial-gradient-9"/>
    <linearGradient id="linear-gradient-24" x1="-10838.14" y1="22.52" x2="-10810.17" y2="53.74" gradientTransform="translate(-10785.18) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-7"/>
    <radialGradient id="radial-gradient-12" cx="-10993.85" cy="26.54" fx="-10993.85" fy="26.54" r="33.84" gradientTransform="translate(-10603.97) rotate(-180) scale(.97 -1)" xlink:href="#radial-gradient-10"/>
    <linearGradient id="linear-gradient-25" x1="-10810.21" y1="5.41" x2="-10821.27" y2="61.31" gradientTransform="translate(-10785.18) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".09" stop-color="#cdd5ec"/>
      <stop offset=".11" stop-color="#c1cfe9"/>
      <stop offset=".22" stop-color="#7baad7"/>
      <stop offset=".3" stop-color="#4f92cd"/>
      <stop offset=".34" stop-color="#3f8ac9"/>
      <stop offset=".73" stop-color="#182e73"/>
      <stop offset=".93" stop-color="#06044c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-26" x1="-10813.62" y1="15.97" x2="-10813.62" y2="64.5" gradientTransform="translate(-10785.18) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-14"/>
    <linearGradient id="linear-gradient-27" x1="-10818.66" y1="35.64" x2="-10810.31" y2="57.16" gradientTransform="translate(-10785.18) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#d0d3d3"/>
      <stop offset=".14" stop-color="#9ab5ca"/>
      <stop offset=".35" stop-color="#548ebe"/>
      <stop offset=".5" stop-color="#2876b7"/>
      <stop offset=".57" stop-color="#186db5"/>
      <stop offset=".93" stop-color="#000041"/>
    </linearGradient>
    <linearGradient id="linear-gradient-28" x1="24.97" y1="12.74" x2="24.97" y2="57.71" gradientUnits="userSpaceOnUse">
      <stop offset=".06" stop-color="#cdd5ec"/>
      <stop offset=".34" stop-color="#3f8ac9"/>
      <stop offset=".49" stop-color="#2960ac"/>
      <stop offset=".75" stop-color="#3f8ac9"/>
      <stop offset=".75" stop-color="#3d86c6"/>
      <stop offset=".84" stop-color="#1f4084"/>
      <stop offset=".9" stop-color="#0d145b"/>
      <stop offset=".93" stop-color="#06044c"/>
    </linearGradient>
    <radialGradient id="radial-gradient-13" cx="24.74" cy="13.78" fx="24.74" fy="13.78" r="44.37" gradientTransform="translate(31.23 -11.04) rotate(89.3) scale(1 .49)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#cdd5ec" stop-opacity="0"/>
      <stop offset=".11" stop-color="#b5c4e2" stop-opacity=".11"/>
      <stop offset=".33" stop-color="#7898ca" stop-opacity=".39"/>
      <stop offset=".61" stop-color="#1f59a7" stop-opacity=".8"/>
      <stop offset=".66" stop-color="#285faa" stop-opacity=".76"/>
      <stop offset=".74" stop-color="#4372b5" stop-opacity=".63"/>
      <stop offset=".84" stop-color="#6e91c6" stop-opacity=".43"/>
      <stop offset=".94" stop-color="#aabcde" stop-opacity=".16"/>
      <stop offset="1" stop-color="#cdd5ec" stop-opacity="0"/>
    </radialGradient>
    <radialGradient id="radial-gradient-14" cx="24.66" cy="39.01" fx="24.66" fy="39.01" r="17.5" gradientTransform="translate(55.63 14.35) rotate(90) scale(1 .79)" gradientUnits="userSpaceOnUse">
      <stop offset=".16" stop-color="#cdd5ec"/>
      <stop offset=".31" stop-color="#7d93c7"/>
      <stop offset=".42" stop-color="#4c69af"/>
      <stop offset=".48" stop-color="#395aa7"/>
      <stop offset=".49" stop-color="#3962ad"/>
      <stop offset=".51" stop-color="#3b71b7"/>
      <stop offset=".54" stop-color="#3b7abe"/>
      <stop offset=".59" stop-color="#3c7dc0"/>
      <stop offset=".75" stop-color="#182f75"/>
      <stop offset=".85" stop-color="#05054c"/>
    </radialGradient>
    <linearGradient id="linear-gradient-29" x1="31.65" y1="54.32" x2="18.3" y2="54.32" gradientUnits="userSpaceOnUse">
      <stop offset=".07" stop-color="#a9b1d9"/>
      <stop offset=".16" stop-color="#395aa7"/>
      <stop offset=".18" stop-color="#3961ac"/>
      <stop offset=".3" stop-color="#3c77bc"/>
      <stop offset=".4" stop-color="#3e85c5"/>
      <stop offset=".5" stop-color="#3f8ac9"/>
      <stop offset=".54" stop-color="#3e84c4"/>
      <stop offset=".86" stop-color="#395aa7"/>
      <stop offset=".93" stop-color="#a7afd8"/>
    </linearGradient>
    <linearGradient id="linear-gradient-30" x1="16.53" y1="65.8" x2="23.49" y2="65.8" gradientUnits="userSpaceOnUse">
      <stop offset=".17" stop-color="#395aa7"/>
      <stop offset=".17" stop-color="#395aa7"/>
      <stop offset=".7" stop-color="#3d7cbf"/>
      <stop offset=".99" stop-color="#3f8ac9"/>
    </linearGradient>
    <linearGradient id="linear-gradient-31" x1="-10818.6" y1="65.8" x2="-10812.31" y2="65.8" gradientTransform="translate(10846.42 131.61) scale(1 -1)" xlink:href="#linear-gradient-30"/>
    <linearGradient id="linear-gradient-32" x1="-10803.26" y1="50.47" x2="-10810.2" y2="57.12" gradientTransform="translate(-10785.24) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset=".29" stop-color="#cdd5ec"/>
      <stop offset=".32" stop-color="#cdd5ec" stop-opacity=".86"/>
      <stop offset=".38" stop-color="#cdd5ec" stop-opacity=".6"/>
      <stop offset=".44" stop-color="#cdd5ec" stop-opacity=".38"/>
      <stop offset=".49" stop-color="#cdd5ec" stop-opacity=".22"/>
      <stop offset=".54" stop-color="#cdd5ec" stop-opacity=".1"/>
      <stop offset=".58" stop-color="#cdd5ec" stop-opacity=".03"/>
      <stop offset=".61" stop-color="#cdd5ec" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-33" x1="-2463.93" y1="52.6" x2="-2468.5" y2="57.69" gradientTransform="translate(2494.89)" xlink:href="#linear-gradient-17"/>
    <linearGradient id="linear-gradient-34" x1="-2463.16" y1="50.47" x2="-2470.1" y2="57.12" gradientTransform="translate(2495.08)" xlink:href="#linear-gradient-32"/>
    <linearGradient id="linear-gradient-35" x1="11.56" y1="73.03" x2="16.62" y2="73.03" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#cdd5ec" stop-opacity=".8"/>
      <stop offset=".17" stop-color="#cdd5ec" stop-opacity=".6"/>
      <stop offset=".37" stop-color="#cdd5ec" stop-opacity=".38"/>
      <stop offset=".56" stop-color="#cdd5ec" stop-opacity=".22"/>
      <stop offset=".74" stop-color="#cdd5ec" stop-opacity=".1"/>
      <stop offset=".89" stop-color="#cdd5ec" stop-opacity=".03"/>
      <stop offset="1" stop-color="#cdd5ec" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-36" x1="15.38" y1="73.03" x2="20.07" y2="73.03" xlink:href="#linear-gradient-21"/>
    <linearGradient id="linear-gradient-37" x1="-10819.75" y1="73.03" x2="-10815.05" y2="73.03" gradientTransform="translate(10851.33 146.07) scale(1 -1)" xlink:href="#linear-gradient-21"/>
    <linearGradient id="linear-gradient-38" x1="-10836.29" y1="8.58" x2="-10825.18" y2="22.55" gradientTransform="translate(-10785.14) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#3767b0"/>
      <stop offset=".55" stop-color="#395aa7"/>
      <stop offset=".9" stop-color="#05054c"/>
    </linearGradient>
    <linearGradient id="linear-gradient-39" x1="12.32" y1="65.8" x2="20.83" y2="65.8" gradientUnits="userSpaceOnUse">
      <stop offset=".19" stop-color="#cdd5ec" stop-opacity=".8"/>
      <stop offset=".33" stop-color="#cdd5ec" stop-opacity=".6"/>
      <stop offset=".49" stop-color="#cdd5ec" stop-opacity=".38"/>
      <stop offset=".65" stop-color="#cdd5ec" stop-opacity=".22"/>
      <stop offset=".79" stop-color="#cdd5ec" stop-opacity=".1"/>
      <stop offset=".91" stop-color="#cdd5ec" stop-opacity=".03"/>
      <stop offset="1" stop-color="#cdd5ec" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-40" x1="-8327.77" y1="71" x2="-8322.63" y2="71" gradientTransform="translate(-8290.16) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-22"/>
    <linearGradient id="linear-gradient-41" x1="-8328.54" y1="73.03" x2="-8323.47" y2="73.03" gradientTransform="translate(-8290.16) rotate(-180) scale(1 -1)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#cdd5ec" stop-opacity=".8"/>
      <stop offset=".17" stop-color="#cdd5ec" stop-opacity=".6"/>
      <stop offset=".37" stop-color="#cdd5ec" stop-opacity=".38"/>
      <stop offset=".56" stop-color="#cdd5ec" stop-opacity=".22"/>
      <stop offset=".74" stop-color="#cdd5ec" stop-opacity=".1"/>
      <stop offset=".89" stop-color="#cdd5ec" stop-opacity=".03"/>
      <stop offset="1" stop-color="#cdd5ec" stop-opacity="0"/>
    </linearGradient>
    <linearGradient id="linear-gradient-42" x1="-8327.77" y1="65.8" x2="-8319.27" y2="65.8" gradientTransform="translate(-8290.16) rotate(-180) scale(1 -1)" xlink:href="#linear-gradient-39"/>
  </defs>
  <g>
    <g>
      <g>
        <polygon class="cls-17" points="79.41 20.94 79.41 18.77 72.14 18.77 72.14 28.83 74.73 28.83 74.73 25.64 74.44 24.81 78.15 24.81 78.15 22.77 74.73 22.77 74.73 20.94 79.41 20.94"/>
        <path class="cls-17" d="M82.59,28.33c-.86-.41-1.52-1.01-1.99-1.79-.47-.78-.7-1.69-.7-2.74,0-1.05.23-1.96.7-2.74.47-.78,1.13-1.38,1.98-1.8.86-.42,1.85-.63,2.98-.63.74,0,1.41.07,1.99.21.58.14,1.13.36,1.62.66v2.38c-.44-.33-.93-.57-1.48-.72-.55-.15-1.18-.23-1.9-.23-1.02,0-1.81.25-2.36.76-.55.5-.83,1.2-.83,2.1,0,.9.28,1.6.84,2.11.56.51,1.35.76,2.36.76.71,0,1.35-.08,1.92-.24.56-.16,1.09-.42,1.58-.76v2.36c-.94.61-2.18.92-3.73.92-1.13,0-2.12-.21-2.98-.62"/>
        <path class="cls-17" d="M64.41,24.81l1.3-3.38,1.29,3.38h-2.59ZM71.26,28.83l-4.23-10.06h-3.51l.35,1.24-3.71,8.82h2.75l.5-1.24-.29-.83h4.53s.85,2.06.85,2.06h2.74Z"/>
      </g>
      <g>
        <path class="cls-17" d="M86.12,45.92h-3.85v-5.64h-4.44v5.64h-3.85v-13.84h3.85v5.14h4.44v-5.14h3.85v13.84Z"/>
        <path class="cls-17" d="M63,45.25c-1.18-.57-2.1-1.4-2.74-2.47-.64-1.07-.97-2.33-.97-3.78,0-1.44.32-2.7.96-3.78.64-1.07,1.56-1.9,2.74-2.48,1.18-.58,2.56-.87,4.12-.87,1.03,0,1.94.1,2.75.29.81.2,1.55.5,2.24.92v3.28c-.6-.46-1.28-.79-2.04-.99-.76-.21-1.63-.31-2.62-.31-1.41,0-2.5.35-3.26,1.05-.77.7-1.15,1.66-1.15,2.9,0,1.24.39,2.21,1.16,2.91.77.7,1.86,1.05,3.25,1.05.99,0,1.87-.11,2.65-.33.78-.22,1.51-.57,2.18-1.06v3.26c-1.3.85-3.02,1.27-5.15,1.27-1.56,0-2.93-.29-4.12-.86"/>
        <path class="cls-17" d="M93.03,40.39l1.79-4.66,1.77,4.65h-3.56ZM102.45,45.92l-5.82-13.84h-4.83l.48,1.7-5.1,12.14h3.79l.69-1.7-.39-1.14h6.24s1.17,2.84,1.17,2.84h3.77Z"/>
        <path class="cls-17" d="M126.21,38.23c-.39.27-.94.41-1.64.41h-2.02v-3.61h2.02c.72,0,1.27.13,1.65.39.38.26.57.73.57,1.42s-.19,1.13-.58,1.4M130.09,39.47c.47-.71.68-1.61.68-2.64,0-1.53-.46-2.71-1.39-3.53-.93-.82-2.21-1.23-3.86-1.23h-6.29v13.84h3.65v-3.18l-.39-1.15h3.13c.8,0,1.79-.14,2.55-.5.98-.46,1.65-1.21,1.92-1.63"/>
        <polygon class="cls-17" points="136.38 32.08 131.75 32.08 132.23 33.78 132.23 44.22 131.76 45.92 136.37 45.92 135.89 44.22 135.9 33.78 136.38 32.08"/>
        <path class="cls-17" d="M137.35,38.92c0-3.88,3.36-7.03,7.61-7.03s7.63,3.15,7.63,7.03-3.34,7.03-7.63,7.03-7.61-3.13-7.61-7.03ZM149.02,38.92c0-2.1-1.75-3.82-4.06-3.82s-4.02,1.72-4.02,3.82,1.67,3.8,4.02,3.8,4.06-1.72,4.06-3.8Z"/>
        <path class="cls-17" d="M102.45,32.08h3.43l4.07,5.18,4.19-5.18h3.28v13.84h-3.51v-8.78l-3.98,4.91-3.94-4.89v8.76h-3.53v-13.84Z"/>
        <polygon class="cls-17" points="166.28 45.82 166.27 32.08 162.62 32.08 162.62 40.25 157.3 32.08 153.68 32.08 153.69 45.82 157.35 45.82 157.35 37.62 162.68 45.82 166.28 45.82"/>
        <path class="cls-17" d="M168.52,42.99l-1.34,2.83h6.05c1.85,0,3.25-.35,4.18-1.04,1-.73,1.54-1.98,1.29-3.19-.59-2.83-4.05-3.34-6.34-4.69-.38-.23-.58-.52-.58-.89,0-.37.16-.65.53-.84.38-.2.92-.29,1.55-.29h3.31l1.02-2.8h-4.99c-1.03,0-1.95.11-2.74.44-.79.33-1.41.79-1.86,1.38-.44.59-.67,1.27-.66,2.02,0,.92.29,1.7.88,2.33.62.67,1.56,1.07,2.39,1.43,1.08.47,2.33.84,3.3,1.53.22.16.42.42.43.69,0,.31-.08.52-.29.68-.67.51-1.58.42-2.39.42h-3.75Z"/>
      </g>
      <g>
        <polygon class="cls-17" points="188.72 42.66 188.72 31.69 185 31.69 185 45.82 185.14 45.82 188.72 45.82 195.25 45.82 195.25 42.66 188.72 42.66"/>
        <polygon class="cls-17" points="196.51 45.82 206.72 45.82 206.72 42.8 200.15 42.8 200.15 40.15 204.95 40.15 204.95 37.34 200.15 37.34 200.15 34.69 206.72 34.69 206.72 31.69 196.51 31.69 196.51 45.82"/>
        <path class="cls-17" d="M212.68,40.17l1.83-4.75,1.81,4.75h-3.63ZM222.29,45.82l-5.94-14.13h-4.93l.49,1.73-5.21,12.39h3.87l.71-1.73-.4-1.16h6.37s1.19,2.89,1.19,2.89h3.85Z"/>
        <path class="cls-17" d="M241.89,46.03c1.77,0,3.76-.5,4.86-1.63,1.09-1.13,1.63-2.64,1.63-4.52v-8.19h-3.62v8.23c0,.94-.25,1.66-.76,2.14-.5.48-1.21.73-2.11.73s-1.6-.24-2.11-.73c-.5-.48-.76-1.2-.76-2.14v-8.23h-3.62v8.19c0,1.88.54,3.4,1.63,4.52,1.1,1.13,3.09,1.63,4.86,1.63"/>
        <polygon class="cls-17" points="249.79 45.82 260 45.82 260 42.8 253.43 42.8 253.43 40.15 258.23 40.15 258.23 37.34 253.43 37.34 253.43 34.69 260 34.69 260 31.69 249.79 31.69 249.79 45.82"/>
        <path class="cls-17" d="M234.46,39.13h-3.6v2.93l.83.49s-.33.13-.86.21c-.42.05-.86.08-1.33.08-1.41,0-2.51-.36-3.29-1.07-.78-.71-1.17-1.7-1.17-2.96,0-1.26.39-2.24,1.16-2.95.77-.71,1.87-1.06,3.3-1.06,1,0,1.88.11,2.65.32.77.21,1.45.55,2.06,1.01v-3.34c-.69-.42-1.45-.73-2.26-.93-.82-.2-1.74-.3-2.78-.3-1.58,0-2.96.3-4.16.89-1.2.59-2.12,1.43-2.77,2.52-.65,1.09-.97,2.37-.97,3.84,0,1.47.33,2.75.98,3.84.65,1.09,1.57,1.93,2.77,2.51,1.2.58,2.58.87,4.16.87,2.16,0,3.97-.45,5.28-1.31v-.03s0,0,0,0v-5.56Z"/>
      </g>
    </g>
    <g>
      <g>
        <path class="cls-20" d="M93.01,57c0-4,3.39-7.24,7.66-7.24s7.68,3.25,7.68,7.24-3.37,7.24-7.68,7.24-7.66-3.23-7.66-7.24ZM104.76,57c0-2.16-1.77-3.94-4.09-3.94s-4.05,1.78-4.05,3.94,1.68,3.92,4.05,3.92,4.09-1.78,4.09-3.92Z"/>
        <path class="cls-20" d="M72.11,50h3.92l2.34,8.62,2.05-6.9-.58-1.72h4.49l2.57,8.64,2.34-8.64h3.88l-4.16,13.99h-4.21l-2.13-7.71-2.09,7.71h-4.34l-4.09-13.99Z"/>
        <polygon class="cls-20" points="72.11 49.99 60.16 49.99 60.16 53.18 64.33 53.18 64.33 63.99 68.02 63.99 68.02 53.18 72.11 53.18 72.11 49.99"/>
      </g>
      <path class="cls-20" d="M110.9,52.29h-.4v-2.3h.35l.75,1.02.71-.97h.4v2.25h-.44v-1.55l-.62.84h-.09l-.66-.84v1.55ZM109.39,50.44v1.86h-.44v-1.86h-.62v-.44h1.72v.44h-.66Z"/>
    </g>
  </g>
  <g>
    <path class="cls-18" d="M30.17,12.87s.03.05.05.1c0,0,0,0,0,0,1.53,2.67,4.93,9.76,4.93,19.66,0,1-.13,2.37-.37,3.97.7-2.05,4.92-15.29.34-25.51-.09-.2-.16-.25-.43-.16-.17.05-4.24,1.51-4.41,1.57-.16.06-.19.21-.11.37Z"/>
    <path class="cls-8" d="M34.77,10.13c.06.2.15.47.24.81.04.03.07.09.1.16,3.44,7.67,1.92,17.04.65,22.11,1.36-3.37,7.18-18.38,4.61-23.95-.1-.22-.28-.27-.42-.25-.16.02-4.76.68-4.96.71-.23.04-.31.18-.23.43Z"/>
    <path class="cls-7" d="M41.22,21.17c1.86-2.61,4.7-6.2,8.23-9.49,0-.02.09-.56-.07-1.14-.3-1.08-1.42-2-3.27-2.01-.6,0-1.09.02-1.28.03-.14.01-.21.06-.22.2-.02.14-.1.87-.1.87,0,0-.02.08-.06.23.5-.02.86-.03.92-.03.2,0,.33.08.34.31,0,.19,0,.47,0,.87-.11,2.35-1.05,4.8-4.47,10.17Z"/>
    <path class="cls-4" d="M38.68,25.02l.15-.24c.23-.4,1.06-1.76,2.39-3.61,3.42-5.37,4.36-7.82,4.47-10.17.02-.4.02-.68,0-.87,0-.22-.14-.31-.34-.31s-3.91.13-4.28.15c-.22.01-.33.04-.39.13,1,3.57-.4,9.75-2,14.92Z"/>
    <path class="cls-19" d="M44.43,9.86c.5-.02.86-.03.92-.03h0c.03-.23.04-.45.06-.68.01-.25.02-.45.01-.62-.28,0-.49.02-.61.03-.14.01-.21.06-.22.2-.02.14-.1.87-.1.87,0,0-.02.08-.06.23Z"/>
    <path class="cls-16" d="M38.68,25.02c1.81-4.3,3.57-11.15,2.92-15.06-.26,0-.45.02-.52.02-.22.01-.33.04-.39.13,1,3.57-.4,9.75-2,14.92Z"/>
    <path class="cls-15" d="M34.77,10.13c.06.2.15.47.24.81.04.03.07.09.1.16,3.44,7.67,1.92,17.04.65,22.11.16-.39.37-.93.63-1.6,1.29-5.58,2.43-14.46-.8-21.9-.01-.03-.02-.05-.04-.07-.31.05-.52.08-.56.08-.23.04-.31.18-.23.43Z"/>
    <path class="cls-14" d="M30.17,12.87s.03.05.05.1c0,0,0,0,0,0,1.53,2.67,4.93,9.76,4.93,19.66,0,1-.13,2.37-.37,3.97.33-1.01.8-4.31.8-5.28,0-9.24-2.95-16.04-4.6-19.07-.39.14-.67.24-.71.26-.16.06-.19.21-.11.37Z"/>
    <path class="cls-11" d="M38.7,72.2v1.96H11.24v-1.96c0-.23.05-.3.31-.3h26.84c.26,0,.31.08.31.3Z"/>
    <path class="cls-3" d="M19.77,12.87s-.03.05-.05.1c0,0,0,0,0,0-1.53,2.67-4.93,9.76-4.93,19.66,0,1,.13,2.37.37,3.97-.7-2.05-4.92-15.29-.34-25.51.09-.2.16-.25.43-.16.17.05,4.24,1.51,4.41,1.57.16.06.19.21.11.37Z"/>
    <path class="cls-2" d="M15.17,10.13c-.06.2-.15.47-.24.81-.04.03-.07.09-.1.16-3.44,7.67-1.92,17.04-.65,22.11-1.36-3.37-7.18-18.38-4.61-23.95.1-.22.28-.27.42-.25.16.02,4.76.68,4.96.71.23.04.31.18.23.43Z"/>
    <path class="cls-1" d="M8.72,21.17c-1.86-2.61-4.7-6.2-8.23-9.49,0-.02-.09-.56.07-1.14.3-1.08,1.42-2,3.27-2.01.6,0,1.09.02,1.28.03.14.01.21.06.22.2.02.14.1.87.1.87,0,0,.02.08.06.23-.5-.02-.86-.03-.92-.03-.2,0-.33.08-.34.31,0,.19,0,.47,0,.87.11,2.35,1.05,4.8,4.47,10.17Z"/>
    <path class="cls-6" d="M11.26,25.02l-.15-.24c-.23-.4-1.06-1.76-2.39-3.61-3.42-5.37-4.36-7.82-4.47-10.17-.02-.4-.02-.68,0-.87,0-.22.14-.31.34-.31s3.91.13,4.28.15c.22.01.33.04.39.13-1,3.57.4,9.75,2,14.92Z"/>
    <path class="cls-5" d="M18,48.11c-.08-.05-5.47-3.69-10.24-9.95-2.03-2.58-3.97-5.7-5.4-9.33C.97,25.37.05,21.51.01,17.32c-.04-1.3.03-2.29.09-2.89,0,0,0,0,0,0,.11-1.33.38-2.75.39-2.75,6.04,5.63,10.05,12.15,10.61,13.1l.15.24c.06.21.13.42.19.62,0,0,0,0,0,0-.52-.89-3.37-3.75-5.24-5.54-1.87-1.78-3.6-3.16-3.6-3.16-.43,10.01,3.25,16.78,6.88,21.04,3.25,3.81,7.22,5.87,7.22,5.87,0,.03,1.29,4.25,1.29,4.25Z"/>
    <path class="cls-12" d="M29.59,52.43v1.31h-9.24v-1.31h9.24Z"/>
    <path class="cls-10" d="M18,48.11c-.08-.05-5.47-3.69-10.24-9.95-2.03-2.58-3.97-5.7-5.4-9.33-.78-1.93-1.41-3.98-1.82-6.14.91,3.24,1.33,4.69,2.31,6.79,1.61,3.55,3.71,6.56,5.87,9.02,3.27,3.85,6.74,6.61,8.82,8.1.25.82.46,1.51.46,1.51Z"/>
    <path class="cls-24" d="M16.71,43.85s-3.68-1.24-8.16-6.43c-3.83-4.44-7.47-11.63-6.92-22.12,0,0,1.8,1.47,3.74,3.36,1.94,1.89,5.66,5.96,6.08,6.98,0,0,0,0,0,0-.52-.89-3.37-3.75-5.24-5.54-1.87-1.78-3.6-3.16-3.6-3.16-.43,10.01,3.25,16.78,6.88,21.04,3.25,3.81,7.22,5.87,7.22,5.87Z"/>
    <path class="cls-9" d="M5.5,9.86c-.5-.02-.86-.03-.92-.03h0c-.03-.23-.04-.45-.06-.68-.01-.25-.02-.45-.01-.62.28,0,.49.02.61.03.14.01.21.06.22.2.02.14.1.87.1.87,0,0,.02.08.06.23Z"/>
    <path class="cls-13" d="M8.08,20.29c-1.84-2.5-4.44-5.68-7.59-8.61,0-.02-.09-.56.07-1.14.04-.16.1-.31.18-.45,1.06,1.84,5.67,7.37,7.34,10.21Z"/>
    <path class="cls-39" d="M11.26,25.02c-1.81-4.3-3.57-11.15-2.92-15.06.26,0,.45.02.52.02.22.01.33.04.39.13-1,3.57.4,9.75,2,14.92Z"/>
    <path class="cls-40" d="M15.17,10.13c-.06.2-.15.47-.24.81-.04.03-.07.09-.1.16-3.44,7.67-1.92,17.04-.65,22.11-.16-.39-.37-.93-.63-1.6-1.29-5.58-2.43-14.46.8-21.9.01-.03.02-.05.04-.07.31.05.52.08.56.08.23.04.31.18.23.43Z"/>
    <path class="cls-41" d="M19.77,12.87s-.03.05-.05.1c0,0,0,0,0,0-1.53,2.67-4.93,9.76-4.93,19.66,0,1,.13,2.37.37,3.97-.33-1.01-.8-4.31-.8-5.28,0-9.24,2.95-16.04,4.6-19.07.39.14.67.24.71.26.16.06.19.21.11.37Z"/>
    <path class="cls-42" d="M24.72,14.31h0s-.05-.08-.1-.11c-.17-.1-3.63-2.09-3.86-2.23-.23-.13-.38-.08-.51.12-1.03,1.57-5.46,9.22-5.46,20.53,0,3.61,1.64,12.07,4.33,18.42.01.03.04.07.07.12.01.02.03.04.05.06.01.01.02.02.03.03.02.02.04.04.06.05.02.01.03.03.05.04.12.1.42.35.99.15.74-.26,1.63-1.25,1.27-3.09-.11-.57-.9-3.64-1.41-6.37-1.52-8.07-1.08-15.72,4.25-27.05.05-.1.12-.19.18-.29.08-.14.11-.29.06-.4Z"/>
    <path class="cls-44" d="M20.24,42.04c-1.52-8.07-1.08-15.72,4.25-27.05-6.15,10.7-6.8,17.86-5.28,25.93.51,2.74,1.57,6.47,1.72,7.03.82,3.01-1.57,3.38-1.58,3.36.02.01.03.03.05.04.12.1.42.35.99.15.74-.26,1.63-1.25,1.27-3.09-.11-.57-.9-3.64-1.41-6.37Z"/>
    <path class="cls-50" d="M20.92,47.95c-.15-.56-1.2-4.29-1.72-7.03-.54-2.85-.8-5.59-.65-8.41-.15,2.2-.25,4.89.36,8.2.5,2.74,1.49,6.54,1.65,7.1.81,2.99-1.44,3.19-1.47,3.16,0,.03.02.05.03.08.01.03.04.07.07.12.01.02.03.04.05.06.01.01.02.02.03.03.02.02.04.04.06.05.02.01,2.4-.35,1.58-3.36Z"/>
    <path class="cls-51" d="M34.85,57.13c.8,0,1.46,2.3,2.03,2.3s-24.41,0-23.83,0,1.23-2.3,2.03-2.3,18.97,0,19.77,0Z"/>
    <path class="cls-29" d="M15.09,57.13c-.8,0-1.46,2.3-2.03,2.3s24.41,0,23.83,0-1.23-2.3-2.03-2.3-18.97,0-19.77,0Z"/>
    <path class="cls-52" d="M24.97,57.13h9.88c-2.06-.37-1.34-2.21-3.28-2.21s-6.61,0-6.61,0h-6.61c-1.94,0-1.21,1.84-3.28,2.21h9.89Z"/>
    <path class="cls-43" d="M37.73,61.77v8.32H12.21v-8.32c0-.21.04-.26.25-.26h25.02c.21,0,.25.06.25.26Z"/>
    <rect class="cls-45" x="12.21" y="70.1" width="25.52" height="1.8"/>
    <rect class="cls-34" x="28.54" y="70.1" width="5.59" height="1.8"/>
    <rect class="cls-31" x="12.21" y="70.1" width="3.61" height="1.8"/>
    <rect class="cls-35" x="15.81" y="70.1" width="5.59" height="1.8"/>
    <path class="cls-25" d="M31.94,48.11c.08-.05,5.47-3.69,10.24-9.95,2.03-2.58,3.97-5.7,5.4-9.33,1.4-3.46,2.32-7.32,2.36-11.51.04-1.3-.03-2.29-.09-2.89,0,0,0,0,0,0-.11-1.33-.38-2.75-.39-2.75-6.04,5.63-10.05,12.15-10.61,13.1l-.15.24c-.06.21-.13.42-.19.62,0,0,0,0,0,0,.52-.89,3.37-3.75,5.24-5.54,1.87-1.78,3.6-3.16,3.6-3.16.43,10.01-3.25,16.78-6.88,21.04-3.25,3.81-7.22,5.87-7.22,5.87,0,.03-1.29,4.25-1.29,4.25Z"/>
    <path class="cls-55" d="M31.94,48.11c.08-.05,5.47-3.69,10.24-9.95,2.03-2.58,3.97-5.7,5.4-9.33.78-1.93,1.41-3.98,1.82-6.14-.91,3.24-1.33,4.69-2.31,6.79-1.61,3.55-3.71,6.56-5.87,9.02-3.27,3.85-6.74,6.61-8.82,8.1-.25.82-.46,1.51-.46,1.51Z"/>
    <path class="cls-21" d="M33.22,43.85s3.68-1.24,8.16-6.43c3.83-4.44,7.47-11.63,6.92-22.12,0,0-1.8,1.47-3.74,3.36-1.94,1.89-5.66,5.96-6.08,6.98,0,0,0,0,0,0,.52-.89,3.37-3.75,5.24-5.54,1.87-1.78,3.6-3.16,3.6-3.16.43,10.01-3.25,16.78-6.88,21.04-3.25,3.81-7.22,5.87-7.22,5.87Z"/>
    <path class="cls-47" d="M25.27,14.71c.06.09.13.18.18.29,5.32,11.34,5.76,18.98,4.25,27.05-.51,2.74-1.3,5.8-1.41,6.37-.36,1.84.53,2.83,1.27,3.09.57.2.87-.05.99-.15.02-.01.04-.03.05-.04.02-.02.04-.04.06-.05.01-.01.02-.02.03-.03.02-.02.04-.04.05-.06.04-.05.06-.09.07-.12,2.69-6.35,4.33-14.81,4.33-18.42,0-11.32-4.43-18.96-5.46-20.53-.13-.2-.28-.26-.51-.12-.23.13-3.7,2.13-3.86,2.23-.05.03-.08.06-.1.11h0c-.05.11-.02.26.06.4Z"/>
    <path class="cls-56" d="M28.28,48.42c-.36,1.84.53,2.83,1.27,3.09.57.2.87-.05.99-.15.02-.01.04-.03.05-.04-.02.01-2.4-.35-1.58-3.36.15-.56,1.2-4.29,1.72-7.03,1.52-8.07.87-15.23-5.28-25.93,5.32,11.34,5.76,18.98,4.25,27.05-.51,2.74-1.3,5.8-1.41,6.37Z"/>
    <path class="cls-54" d="M30.6,51.31s.04-.04.06-.05c.01-.01.02-.02.03-.03.02-.02.04-.04.05-.06.04-.05.06-.09.07-.12.01-.02.02-.05.03-.08-.03.02-2.28-.17-1.47-3.16.15-.56,1.14-4.36,1.65-7.1.61-3.31.52-6,.36-8.2.15,2.82-.11,5.56-.65,8.41-.51,2.74-1.57,6.47-1.72,7.03-.82,3.01,1.57,3.38,1.58,3.36Z"/>
    <path class="cls-46" d="M30.06,51.57c.13.37-.23.85-.47.85h-.39c-.46,0-1.19-.57-1.43-1.05-.32-.63-1.04-1.25-2.8-1.25v-.02.02c-1.77,0-2.49.62-2.81,1.25-.24.48-.97,1.05-1.43,1.05h-.39c-.24,0-.6-.48-.47-.85.14.01.31,0,.51-.07.74-.26,1.63-1.25,1.27-3.09-.11-.57-.9-3.64-1.41-6.37-1.52-8.07-1.08-15.72,4.25-27.05.05-.1.12-.19.18-.29.08-.14.11-.29.06-.4h0s0,0,0,0c.07.03.16.05.24.05h0c.08,0,.17-.03.24-.05,0,0,0,0,0,0h0c-.05.11-.02.26.06.4.06.09.13.18.18.29,5.32,11.34,5.76,18.98,4.25,27.05-.51,2.74-1.3,5.8-1.41,6.37-.36,1.84.53,2.83,1.27,3.09.2.07.37.09.51.07Z"/>
    <path class="cls-23" d="M30.06,51.57c.13.37-.23.85-.47.85h-.39c-.46,0-1.19-.57-1.43-1.05-.32-.63-1.04-1.25-2.8-1.25v-.02.02c-1.77,0-2.49.62-2.81,1.25-.24.48-.97,1.05-1.43,1.05h-.39c-.24,0-.6-.48-.47-.85.14.01.31,0,.51-.07.74-.26,1.63-1.25,1.27-3.09-.11-.57-.9-3.64-1.41-6.37-1.52-8.07-1.08-15.72,4.25-27.05.05-.1.12-.19.18-.29.08-.14.11-.29.06-.4h0s0,0,0,0c.07.03.16.05.24.05h0c.08,0,.17-.03.24-.05,0,0,0,0,0,0h0c-.05.11-.02.26.06.4.06.09.13.18.18.29,5.32,11.34,5.76,18.98,4.25,27.05-.51,2.74-1.3,5.8-1.41,6.37-.36,1.84.53,2.83,1.27,3.09.2.07.37.09.51.07Z"/>
    <path class="cls-22" d="M29.2,52.43h-8.47c.46,0,1.19-.57,1.43-1.05.32-.63,1.04-1.25,2.8-1.25s2.49.62,2.81,1.25c.24.48.97,1.05,1.43,1.05Z"/>
    <path class="cls-57" d="M31.4,54.91h-12.87c.49,0,.55-1.18,1.37-1.18h10.12c.82,0,.89,1.18,1.37,1.18Z"/>
    <rect class="cls-49" x="16.53" y="61.51" width="5.59" height="8.58"/>
    <rect class="cls-48" x="27.83" y="61.51" width="5.59" height="8.58" transform="translate(61.24 131.61) rotate(-180)"/>
    <path class="cls-30" d="M24.96,57.13h-9.88c2.06-.37,1.34-2.21,3.28-2.21h6.61s4.67,0,6.61,0,1.21,1.84,3.28,2.21h-9.88s0,0,0,0Z"/>
    <path class="cls-28" d="M34.85,57.13c.8,0,1.46,2.3,2.03,2.3s-24.41,0-23.83,0,1.23-2.3,2.03-2.3,18.97,0,19.77,0Z"/>
    <path class="cls-26" d="M24.98,57.13h9.88c-2.06-.37-1.34-2.21-3.28-2.21s-6.61,0-6.61,0h-6.61c-1.94,0-1.21,1.84-3.28,2.21h9.89Z"/>
    <path class="cls-27" d="M15.08,74.17h-3.84v-1.96c0-.23.05-.3.31-.3h3.53v2.27Z"/>
    <rect class="cls-36" x="15.08" y="71.9" width="3.56" height="2.27"/>
    <rect class="cls-37" x="31.29" y="71.9" width="3.56" height="2.27" transform="translate(66.15 146.07) rotate(-180)"/>
    <path class="cls-53" d="M41.85,20.29c1.84-2.5,4.44-5.68,7.59-8.61,0-.02.09-.56-.07-1.14-.04-.16-.1-.31-.18-.45-1.06,1.84-5.67,7.37-7.34,10.21Z"/>
    <path class="cls-58" d="M19.09,70.1h-6.88v-8.32c0-.21.04-.26.25-.26h6.63v8.58Z"/>
    <rect class="cls-32" x="34.13" y="70.1" width="3.61" height="1.8"/>
    <path class="cls-33" d="M34.86,74.17h3.84s0-1.74,0-1.96c0-.23-.05-.3-.31-.3h-3.53s0,2.27,0,2.27Z"/>
    <path class="cls-38" d="M30.85,70.1h6.88s0-8.12,0-8.32-.04-.26-.25-.26h-6.63s0,8.58,0,8.58Z"/>
  </g>
</svg>
