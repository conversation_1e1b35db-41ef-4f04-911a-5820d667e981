@import 'setup/variable';
@import 'setup/mixin';

.template-tickets,
.template-fixtures {
  .tickets,
  .fixtures {
    .fixtures__header,
    .tickets__header {
      margin-top: 24px;
      padding: 0 16px;
    }

    .ticket-fixtures,
    .fixture-results-list {
      &.loading {
        min-height: 500px;
      }
    }

    .load-more-form {
      margin-top: 48px;
      display: flex;
      justify-content: center;
      align-items: center;

      button#load-more {
        text-transform: uppercase;
      }
    }

    .selector-form {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;

      .dropdown + .dropdown {
        margin-left: 8px;
      }

      @media screen and (min-width: 1200px) {
        margin-top: 40px;
      }

      @media screen and (max-width: 1199px) {
        margin-top: 32px;
      }

      @media screen and (max-width: 779px) {
        margin-top: 24px;

        .dropdown {
          margin-top: 8px;
        }
      }

      @media screen and (max-width: 619px) {
        flex-direction: column;

        .dropdown {
          margin-top: 0px;

          & + .dropdown {
            margin-left: 0px;
            margin-top: 8px;
          }
        }
      }
    }

    @media screen and (max-width: 768px) {
      padding: 0 32px;
    }
    @media screen and (max-width: 576px) {
      padding: 0 0px;
    }
  }
}

@import 'components/fixtures_results';
