<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 2800 1400" style="enable-background:new 0 0 2800 1400;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#27348B;}
	.st1{fill:#E4032E;}
	.st2{fill:#E90006;}
	.st3{clip-path:url(#SVGID_2_);fill:#E4003A;}
	.st4{clip-path:url(#SVGID_2_);}
	.st5{fill:#164194;}
	.st6{fill:#FFFFFF;}
	.st7{fill:#E4003A;}
	.st8{fill:#977FB9;}
	.st9{clip-path:url(#SVGID_4_);}
	.st10{fill:#59358C;}
	.st11{fill:#6E50A4;}
	.st12{fill:#990C29;}
	.st13{fill:#1D2031;}
	.st14{fill:#A3A5A9;}
	.st15{fill:#D51317;}
	.st16{clip-path:url(#SVGID_6_);}
	.st17{fill:#E99915;}
	.st18{fill:#EEBD59;}
	.st19{fill:#E3801B;}
	.st20{fill:#DC5F11;}
	.st21{clip-path:url(#SVGID_8_);}
	.st22{fill:#EA1C3C;}
	.st23{fill:#231F1F;}
	.st24{fill:none;}
	.st25{fill:url(#SVGID_9_);}
	.st26{fill:#FFED00;}
	.st27{fill:#6F9DD3;}
	.st28{clip-path:url(#SVGID_11_);fill:#72BE44;}
	.st29{clip-path:url(#SVGID_11_);}
	.st30{fill:url(#SVGID_12_);}
	.st31{fill:url(#SVGID_13_);}
	.st32{fill:url(#SVGID_14_);}
	.st33{fill:url(#SVGID_15_);}
	.st34{fill:url(#SVGID_16_);}
	.st35{fill:url(#SVGID_17_);}
	.st36{fill:url(#SVGID_18_);}
	.st37{fill:url(#SVGID_19_);}
	.st38{fill:url(#SVGID_20_);}
	.st39{fill:url(#SVGID_21_);}
	.st40{fill:url(#SVGID_22_);}
	.st41{fill:url(#SVGID_23_);}
	.st42{fill:url(#SVGID_24_);}
	.st43{fill:url(#SVGID_25_);}
	.st44{clip-path:url(#SVGID_11_);fill:#97D939;}
	.st45{fill:#010026;}
	.st46{fill:#0092AE;}
	.st47{fill:#004F9F;}
	.st48{clip-path:url(#SVGID_27_);fill:#FF1489;}
	.st49{clip-path:url(#SVGID_27_);fill:#D8005B;}
	.st50{clip-path:url(#SVGID_27_);fill:#E5066A;}
	.st51{clip-path:url(#SVGID_27_);fill:#F90C7E;}
	.st52{clip-path:url(#SVGID_29_);}
	.st53{fill:#7D0025;}
	.st54{fill:#590125;}
	.st55{clip-path:url(#SVGID_29_);fill:#590125;}
	.st56{fill:#009DCC;}
	.st57{clip-path:url(#SVGID_31_);}
	.st58{fill:url(#SVGID_32_);}
	.st59{fill:url(#SVGID_33_);}
	.st60{fill:url(#SVGID_34_);}
	.st61{fill:url(#SVGID_35_);}
	.st62{fill:url(#SVGID_36_);}
	.st63{fill:url(#SVGID_37_);}
	.st64{fill:url(#SVGID_38_);}
	.st65{fill:url(#SVGID_39_);}
	.st66{fill:url(#SVGID_40_);}
	.st67{fill:url(#SVGID_41_);}
	.st68{fill:url(#SVGID_42_);}
	.st69{fill:url(#SVGID_43_);}
	.st70{fill:url(#SVGID_44_);}
	.st71{fill:url(#SVGID_45_);}
	.st72{fill:url(#SVGID_46_);}
	.st73{fill:url(#SVGID_47_);}
	.st74{fill:url(#SVGID_48_);}
	.st75{fill:url(#SVGID_49_);}
	.st76{fill:url(#SVGID_50_);}
	.st77{fill:url(#SVGID_51_);}
	.st78{fill:url(#SVGID_52_);}
	.st79{fill:url(#SVGID_53_);}
	.st80{fill:url(#SVGID_54_);}
	.st81{fill:url(#SVGID_55_);}
	.st82{fill:url(#SVGID_56_);}
	.st83{fill:url(#SVGID_57_);}
	.st84{fill:url(#SVGID_58_);}
	.st85{fill:url(#SVGID_59_);}
	.st86{fill:url(#SVGID_60_);}
	.st87{fill:url(#SVGID_61_);}
	.st88{fill:url(#SVGID_62_);}
	.st89{fill:url(#SVGID_63_);}
	.st90{fill:url(#SVGID_64_);}
	.st91{fill:url(#SVGID_65_);}
	.st92{fill:url(#SVGID_66_);}
	.st93{fill:url(#SVGID_67_);}
	.st94{fill:url(#SVGID_68_);}
	.st95{fill:url(#SVGID_69_);}
	.st96{fill:url(#SVGID_70_);}
	.st97{fill:url(#SVGID_71_);}
	.st98{fill:url(#SVGID_72_);}
	.st99{fill:url(#SVGID_73_);}
	.st100{fill:url(#SVGID_74_);}
	.st101{fill:url(#SVGID_75_);}
	.st102{fill:url(#SVGID_76_);}
	.st103{fill:url(#SVGID_77_);}
	.st104{fill:url(#SVGID_78_);}
	.st105{fill:url(#SVGID_79_);}
	.st106{fill:url(#SVGID_80_);}
	.st107{fill:url(#SVGID_81_);}
	.st108{fill:url(#SVGID_82_);}
	.st109{fill:url(#SVGID_83_);}
	.st110{fill:url(#SVGID_84_);}
	.st111{fill:url(#SVGID_85_);}
	.st112{fill:url(#SVGID_86_);}
	.st113{fill:url(#SVGID_87_);}
	.st114{fill:url(#SVGID_88_);}
	.st115{fill:url(#SVGID_89_);}
	.st116{fill:url(#SVGID_90_);}
	.st117{fill:url(#SVGID_91_);}
	.st118{fill:url(#SVGID_92_);}
	.st119{fill:url(#SVGID_93_);}
	.st120{fill:url(#SVGID_94_);}
	.st121{fill:url(#SVGID_95_);}
	.st122{fill:url(#SVGID_96_);}
	.st123{fill:url(#SVGID_97_);}
	.st124{fill:url(#SVGID_98_);}
	.st125{fill:url(#SVGID_99_);}
	.st126{fill:url(#SVGID_100_);}
	.st127{fill:url(#SVGID_101_);}
	.st128{fill:url(#SVGID_102_);}
	.st129{fill:url(#SVGID_103_);}
	.st130{fill:url(#SVGID_104_);}
	.st131{fill:url(#SVGID_105_);}
	.st132{fill:url(#SVGID_106_);}
	.st133{fill:url(#SVGID_107_);}
	.st134{fill:url(#SVGID_108_);}
	.st135{fill:url(#SVGID_109_);}
	.st136{fill:url(#SVGID_110_);}
	.st137{fill:url(#SVGID_111_);}
	.st138{fill:url(#SVGID_112_);}
	.st139{fill:url(#SVGID_113_);}
	.st140{fill:url(#SVGID_114_);}
	.st141{fill:url(#SVGID_115_);}
	.st142{fill:url(#SVGID_116_);}
	.st143{fill:url(#SVGID_117_);}
	.st144{fill:url(#SVGID_118_);}
	.st145{fill:url(#SVGID_119_);}
	.st146{fill:url(#SVGID_120_);}
	.st147{fill:url(#SVGID_121_);}
	.st148{fill:url(#SVGID_122_);}
	.st149{fill:url(#SVGID_123_);}
	.st150{fill:url(#SVGID_124_);}
	.st151{fill:url(#SVGID_125_);}
	.st152{fill:url(#SVGID_126_);}
	.st153{fill:url(#SVGID_127_);}
	.st154{fill:url(#SVGID_128_);}
	.st155{fill:url(#SVGID_129_);}
	.st156{fill:url(#SVGID_130_);}
	.st157{fill:url(#SVGID_131_);}
	.st158{clip-path:url(#SVGID_133_);fill:#3EA3DC;}
	.st159{clip-path:url(#SVGID_133_);}
	.st160{fill:url(#SVGID_134_);}
	.st161{fill:url(#SVGID_135_);}
	.st162{fill:url(#SVGID_136_);}
	.st163{fill:url(#SVGID_137_);}
	.st164{fill:url(#SVGID_138_);}
	.st165{fill:url(#SVGID_139_);}
	.st166{fill:url(#SVGID_140_);}
	.st167{fill:url(#SVGID_141_);}
	.st168{fill:url(#SVGID_142_);}
	.st169{fill:url(#SVGID_143_);}
	.st170{fill:url(#SVGID_144_);}
	.st171{fill:url(#SVGID_145_);}
	.st172{fill:url(#SVGID_146_);}
	.st173{fill:url(#SVGID_147_);}
	.st174{fill:url(#SVGID_148_);}
	.st175{fill:url(#SVGID_149_);}
	.st176{fill:url(#SVGID_150_);}
	.st177{fill:url(#SVGID_151_);}
	.st178{fill:url(#SVGID_152_);}
	.st179{fill:url(#SVGID_153_);}
	.st180{fill:url(#SVGID_154_);}
	.st181{fill:url(#SVGID_155_);}
	.st182{fill:url(#SVGID_156_);}
	.st183{fill:url(#SVGID_157_);}
	.st184{fill:url(#SVGID_158_);}
	.st185{fill:url(#SVGID_159_);}
	.st186{fill:url(#SVGID_160_);}
	.st187{fill:url(#SVGID_161_);}
	.st188{fill:url(#SVGID_162_);}
	.st189{fill:url(#SVGID_163_);}
	.st190{fill:url(#SVGID_164_);}
	.st191{fill:url(#SVGID_165_);}
	.st192{fill:url(#SVGID_166_);}
	.st193{fill:url(#SVGID_167_);}
	.st194{fill:url(#SVGID_168_);}
	.st195{fill:url(#SVGID_169_);}
	.st196{fill:url(#SVGID_170_);}
	.st197{fill:url(#SVGID_171_);}
	.st198{fill:url(#SVGID_172_);}
	.st199{fill:url(#SVGID_173_);}
	.st200{fill:url(#SVGID_174_);}
	.st201{fill:url(#SVGID_175_);}
	.st202{fill:url(#SVGID_176_);}
	.st203{fill:url(#SVGID_177_);}
	.st204{fill:url(#SVGID_178_);}
	.st205{fill:url(#SVGID_179_);}
	.st206{fill:url(#SVGID_180_);}
	.st207{fill:url(#SVGID_181_);}
	.st208{fill:url(#SVGID_182_);}
	.st209{fill:url(#SVGID_183_);}
	.st210{fill:url(#SVGID_184_);}
	.st211{fill:url(#SVGID_185_);}
	.st212{fill:url(#SVGID_186_);}
	.st213{fill:url(#SVGID_187_);}
	.st214{fill:url(#SVGID_188_);}
	.st215{fill:url(#SVGID_189_);}
	.st216{fill:url(#SVGID_190_);}
	.st217{fill:url(#SVGID_191_);}
	.st218{fill:url(#SVGID_192_);}
	.st219{fill:url(#SVGID_193_);}
	.st220{fill:url(#SVGID_194_);}
	.st221{fill:url(#SVGID_195_);}
	.st222{fill:url(#SVGID_196_);}
	.st223{fill:url(#SVGID_197_);}
	.st224{fill:url(#SVGID_198_);}
	.st225{fill:url(#SVGID_199_);}
	.st226{fill:url(#SVGID_200_);}
	.st227{fill:url(#SVGID_201_);}
	.st228{fill:url(#SVGID_202_);}
	.st229{fill:url(#SVGID_203_);}
	.st230{fill:url(#SVGID_204_);}
	.st231{fill:url(#SVGID_205_);}
	.st232{fill:url(#SVGID_206_);}
	.st233{fill:url(#SVGID_207_);}
	.st234{fill:url(#SVGID_208_);}
	.st235{fill:url(#SVGID_209_);}
	.st236{fill:url(#SVGID_210_);}
	.st237{fill:url(#SVGID_211_);}
	.st238{fill:url(#SVGID_212_);}
	.st239{fill:url(#SVGID_213_);}
	.st240{fill:url(#SVGID_214_);}
	.st241{fill:url(#SVGID_215_);}
	.st242{fill:url(#SVGID_216_);}
	.st243{fill:url(#SVGID_217_);}
	.st244{fill:url(#SVGID_218_);}
	.st245{fill:url(#SVGID_219_);}
	.st246{fill:url(#SVGID_220_);}
	.st247{fill:url(#SVGID_221_);}
	.st248{fill:url(#SVGID_222_);}
	.st249{fill:url(#SVGID_223_);}
	.st250{fill:url(#SVGID_224_);}
	.st251{fill:url(#SVGID_225_);}
	.st252{fill:url(#SVGID_226_);}
	.st253{fill:url(#SVGID_227_);}
	.st254{fill:url(#SVGID_228_);}
	.st255{fill:url(#SVGID_229_);}
	.st256{fill:url(#SVGID_230_);}
	.st257{fill:url(#SVGID_231_);}
	.st258{fill:url(#SVGID_232_);}
	.st259{fill:url(#SVGID_233_);}
	.st260{fill:url(#SVGID_234_);}
	.st261{fill:url(#SVGID_235_);}
	.st262{fill:url(#SVGID_236_);}
	.st263{fill:url(#SVGID_237_);}
	.st264{fill:url(#SVGID_238_);}
	.st265{fill:url(#SVGID_239_);}
	.st266{fill:url(#SVGID_240_);}
	.st267{fill:url(#SVGID_241_);}
	.st268{fill:url(#SVGID_242_);}
	.st269{fill:url(#SVGID_243_);}
	.st270{fill:url(#SVGID_244_);}
	.st271{fill:url(#SVGID_245_);}
	.st272{fill:url(#SVGID_246_);}
	.st273{fill:url(#SVGID_247_);}
	.st274{fill:url(#SVGID_248_);}
	.st275{fill:url(#SVGID_249_);}
	.st276{fill:url(#SVGID_250_);}
	.st277{fill:url(#SVGID_251_);}
	.st278{fill:url(#SVGID_252_);}
	.st279{fill:url(#SVGID_253_);}
	.st280{fill:url(#SVGID_254_);}
	.st281{fill:url(#SVGID_255_);}
	.st282{fill:url(#SVGID_256_);}
	.st283{fill:url(#SVGID_257_);}
	.st284{fill:url(#SVGID_258_);}
	.st285{fill:url(#SVGID_259_);}
	.st286{fill:url(#SVGID_260_);}
	.st287{fill:url(#SVGID_261_);}
	.st288{fill:url(#SVGID_262_);}
	.st289{fill:url(#SVGID_263_);}
	.st290{fill:url(#SVGID_264_);}
	.st291{fill:url(#SVGID_265_);}
	.st292{fill:url(#SVGID_266_);}
	.st293{fill:url(#SVGID_267_);}
	.st294{fill:url(#SVGID_268_);}
	.st295{fill:url(#SVGID_269_);}
	.st296{fill:url(#SVGID_270_);}
	.st297{fill:url(#SVGID_271_);}
	.st298{fill:url(#SVGID_272_);}
	.st299{fill:url(#SVGID_273_);}
	.st300{fill:url(#SVGID_274_);}
	.st301{fill:url(#SVGID_275_);}
	.st302{fill:url(#SVGID_276_);}
	.st303{fill:url(#SVGID_277_);}
	.st304{fill:url(#SVGID_278_);}
	.st305{fill:url(#SVGID_279_);}
	.st306{fill:url(#SVGID_280_);}
	.st307{fill:url(#SVGID_281_);}
	.st308{fill:url(#SVGID_282_);}
	.st309{fill:url(#SVGID_283_);}
	.st310{fill:url(#SVGID_284_);}
	.st311{fill:url(#SVGID_285_);}
	.st312{fill:url(#SVGID_286_);}
	.st313{fill:url(#SVGID_287_);}
	.st314{fill:url(#SVGID_288_);}
	.st315{fill:url(#SVGID_289_);}
	.st316{fill:url(#SVGID_290_);}
	.st317{fill:url(#SVGID_291_);}
	.st318{fill:url(#SVGID_292_);}
	.st319{fill:url(#SVGID_293_);}
	.st320{fill:url(#SVGID_294_);}
	.st321{fill:url(#SVGID_295_);}
	.st322{fill:url(#SVGID_296_);}
	.st323{fill:url(#SVGID_297_);}
	.st324{fill:url(#SVGID_298_);}
	.st325{fill:url(#SVGID_299_);}
	.st326{fill:url(#SVGID_300_);}
	.st327{fill:url(#SVGID_301_);}
	.st328{fill:url(#SVGID_302_);}
	.st329{fill:url(#SVGID_303_);}
	.st330{fill:url(#SVGID_304_);}
	.st331{fill:url(#SVGID_305_);}
	.st332{fill:url(#SVGID_306_);}
	.st333{fill:url(#SVGID_307_);}
	.st334{fill:url(#SVGID_308_);}
	.st335{fill:url(#SVGID_309_);}
	.st336{fill:url(#SVGID_310_);}
	.st337{fill:url(#SVGID_311_);}
	.st338{fill:url(#SVGID_312_);}
	.st339{fill:url(#SVGID_313_);}
	.st340{fill:url(#SVGID_314_);}
	.st341{fill:url(#SVGID_315_);}
	.st342{clip-path:url(#SVGID_317_);}
	.st343{fill:url(#SVGID_318_);}
	.st344{fill:url(#SVGID_319_);}
	.st345{fill:url(#SVGID_320_);}
	.st346{fill:url(#SVGID_321_);}
	.st347{fill:url(#SVGID_322_);}
	.st348{fill:url(#SVGID_323_);}
	.st349{fill:url(#SVGID_324_);}
	.st350{fill:url(#SVGID_325_);}
	.st351{fill:url(#SVGID_326_);}
	.st352{fill:url(#SVGID_327_);}
	.st353{fill:url(#SVGID_328_);}
	.st354{fill:url(#SVGID_329_);}
	.st355{fill:url(#SVGID_330_);}
	.st356{fill:url(#SVGID_331_);}
	.st357{fill:url(#SVGID_332_);}
	.st358{fill:url(#SVGID_333_);}
	.st359{fill:url(#SVGID_334_);}
	.st360{fill:url(#SVGID_335_);}
	.st361{fill:url(#SVGID_336_);}
	.st362{fill:url(#SVGID_337_);}
	.st363{fill:url(#SVGID_338_);}
	.st364{fill:url(#SVGID_339_);}
	.st365{fill:url(#SVGID_340_);}
	.st366{fill:url(#SVGID_341_);}
	.st367{fill:url(#SVGID_342_);}
	.st368{fill:url(#SVGID_343_);}
	.st369{fill:url(#SVGID_344_);}
	.st370{fill:url(#SVGID_345_);}
	.st371{fill:url(#SVGID_346_);}
	.st372{fill:url(#SVGID_347_);}
	.st373{fill:url(#SVGID_348_);}
	.st374{fill:url(#SVGID_349_);}
	.st375{fill:url(#SVGID_350_);}
	.st376{fill:url(#SVGID_351_);}
	.st377{fill:url(#SVGID_352_);}
	.st378{fill:url(#SVGID_353_);}
	.st379{fill:url(#SVGID_354_);}
	.st380{fill:url(#SVGID_355_);}
	.st381{fill:url(#SVGID_356_);}
	.st382{fill:url(#SVGID_357_);}
	.st383{fill:url(#SVGID_358_);}
	.st384{fill:url(#SVGID_359_);}
	.st385{fill:url(#SVGID_360_);}
	.st386{fill:url(#SVGID_361_);}
	.st387{fill:url(#SVGID_362_);}
	.st388{fill:url(#SVGID_363_);}
	.st389{fill:url(#SVGID_364_);}
	.st390{fill:url(#SVGID_365_);}
	.st391{fill:url(#SVGID_366_);}
	.st392{fill:url(#SVGID_367_);}
	.st393{fill:url(#SVGID_368_);}
	.st394{fill:url(#SVGID_369_);}
	.st395{fill:url(#SVGID_370_);}
	.st396{fill:url(#SVGID_371_);}
	.st397{fill:url(#SVGID_372_);}
	.st398{fill:url(#SVGID_373_);}
	.st399{fill:url(#SVGID_374_);}
	.st400{fill:url(#SVGID_375_);}
	.st401{fill:url(#SVGID_376_);}
	.st402{fill:url(#SVGID_377_);}
	.st403{fill:url(#SVGID_378_);}
	.st404{fill:url(#SVGID_379_);}
	.st405{fill:url(#SVGID_380_);}
	.st406{fill:url(#SVGID_381_);}
	.st407{fill:url(#SVGID_382_);}
	.st408{fill:url(#SVGID_383_);}
	.st409{fill:url(#SVGID_384_);}
	.st410{fill:url(#SVGID_385_);}
	.st411{fill:url(#SVGID_386_);}
	.st412{fill:url(#SVGID_387_);}
	.st413{fill:url(#SVGID_388_);}
	.st414{fill:url(#SVGID_389_);}
	.st415{fill:url(#SVGID_390_);}
	.st416{fill:url(#SVGID_391_);}
	.st417{fill:url(#SVGID_392_);}
	.st418{fill:url(#SVGID_393_);}
	.st419{fill:url(#SVGID_394_);}
	.st420{fill:url(#SVGID_395_);}
	.st421{fill:url(#SVGID_396_);}
	.st422{fill:url(#SVGID_397_);}
	.st423{fill:url(#SVGID_398_);}
	.st424{fill:url(#SVGID_399_);}
	.st425{fill:url(#SVGID_400_);}
	.st426{fill:url(#SVGID_401_);}
	.st427{fill:url(#SVGID_402_);}
	.st428{fill:url(#SVGID_403_);}
	.st429{fill:url(#SVGID_404_);}
	.st430{fill:url(#SVGID_405_);}
	.st431{fill:url(#SVGID_406_);}
	.st432{fill:url(#SVGID_407_);}
	.st433{fill:url(#SVGID_408_);}
	.st434{fill:url(#SVGID_409_);}
	.st435{fill:url(#SVGID_410_);}
	.st436{fill:url(#SVGID_411_);}
	.st437{fill:url(#SVGID_412_);}
	.st438{fill:url(#SVGID_413_);}
	.st439{fill:url(#SVGID_414_);}
	.st440{fill:url(#SVGID_415_);}
	.st441{fill:url(#SVGID_416_);}
	.st442{fill:url(#SVGID_417_);}
	.st443{fill:url(#SVGID_418_);}
	.st444{fill:url(#SVGID_419_);}
	.st445{fill:url(#SVGID_420_);}
	.st446{fill:url(#SVGID_421_);}
	.st447{fill:url(#SVGID_422_);}
	.st448{fill:url(#SVGID_423_);}
	.st449{fill:url(#SVGID_424_);}
	.st450{fill:url(#SVGID_425_);}
	.st451{fill:url(#SVGID_426_);}
	.st452{fill:url(#SVGID_427_);}
	.st453{fill:url(#SVGID_428_);}
	.st454{fill:url(#SVGID_429_);}
	.st455{fill:url(#SVGID_430_);}
	.st456{fill:url(#SVGID_431_);}
	.st457{fill:url(#SVGID_432_);}
	.st458{fill:url(#SVGID_433_);}
	.st459{fill:url(#SVGID_434_);}
	.st460{fill:url(#SVGID_435_);}
	.st461{fill:url(#SVGID_436_);}
	.st462{fill:url(#SVGID_437_);}
	.st463{fill:url(#SVGID_438_);}
	.st464{fill:url(#SVGID_439_);}
	.st465{fill:url(#SVGID_440_);}
	.st466{fill:url(#SVGID_441_);}
	.st467{fill:url(#SVGID_442_);}
	.st468{fill:url(#SVGID_443_);}
	.st469{fill:url(#SVGID_444_);}
	.st470{fill:url(#SVGID_445_);}
	.st471{fill:url(#SVGID_446_);}
	.st472{fill:url(#SVGID_447_);}
	.st473{fill:url(#SVGID_448_);}
	.st474{fill:url(#SVGID_449_);}
	.st475{fill:url(#SVGID_450_);}
	.st476{fill:url(#SVGID_451_);}
	.st477{fill:url(#SVGID_452_);}
	.st478{fill:url(#SVGID_453_);}
	.st479{fill:url(#SVGID_454_);}
	.st480{fill:url(#SVGID_455_);}
	.st481{fill:url(#SVGID_456_);}
	.st482{fill:url(#SVGID_457_);}
	.st483{fill:url(#SVGID_458_);}
	.st484{fill:url(#SVGID_459_);}
	.st485{fill:url(#SVGID_460_);}
	.st486{fill:url(#SVGID_461_);}
	.st487{fill:url(#SVGID_462_);}
	.st488{fill:url(#SVGID_463_);}
	.st489{fill:url(#SVGID_464_);}
	.st490{fill:url(#SVGID_465_);}
	.st491{fill:url(#SVGID_466_);}
	.st492{fill:url(#SVGID_467_);}
	.st493{fill:url(#SVGID_468_);}
	.st494{fill:url(#SVGID_469_);}
	.st495{fill:url(#SVGID_470_);}
	.st496{fill:url(#SVGID_471_);}
	.st497{fill:url(#SVGID_472_);}
	.st498{fill:url(#SVGID_473_);}
	.st499{fill:url(#SVGID_474_);}
	.st500{fill:url(#SVGID_475_);}
	.st501{fill:url(#SVGID_476_);}
	.st502{fill:url(#SVGID_477_);}
	.st503{fill:url(#SVGID_478_);}
	.st504{fill:url(#SVGID_479_);}
	.st505{fill:url(#SVGID_480_);}
	.st506{fill:url(#SVGID_481_);}
	.st507{fill:url(#SVGID_482_);}
	.st508{fill:url(#SVGID_483_);}
	.st509{fill:url(#SVGID_484_);}
	.st510{fill:url(#SVGID_485_);}
	.st511{fill:url(#SVGID_486_);}
	.st512{fill:url(#SVGID_487_);}
	.st513{fill:url(#SVGID_488_);}
	.st514{fill:url(#SVGID_489_);}
	.st515{fill:url(#SVGID_490_);}
	.st516{fill:url(#SVGID_491_);}
	.st517{fill:url(#SVGID_492_);}
	.st518{fill:url(#SVGID_493_);}
	.st519{fill:url(#SVGID_494_);}
	.st520{fill:url(#SVGID_495_);}
	.st521{fill:url(#SVGID_496_);}
	.st522{fill:url(#SVGID_497_);}
	.st523{fill:url(#SVGID_498_);}
	.st524{fill:url(#SVGID_499_);}
	.st525{fill:#951B81;}
	.st526{clip-path:url(#SVGID_501_);}
	.st527{fill:#F39200;}
	.st528{fill:#C70619;}
</style>
<g>
	<rect class="st2" width="560" height="1400"/>
	<rect x="560" width="560" height="1400"/>
	<rect x="1120" class="st2" width="560" height="1400"/>
	<rect x="2240" class="st2" width="560" height="1400"/>
	<rect x="1680" width="560" height="1400"/>
</g>
</svg>
