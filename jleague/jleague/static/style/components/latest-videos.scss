@import 'setup/variable';
@import 'setup/mixin';

.latest-videos {
  margin-top: 80px;
  position: relative;

  .latest-videos__header {
    margin-bottom: 40px;
  }

  .latest-videos__highlight {
    aspect-ratio: 16/9;
    height: auto;
    width: 100%;
    margin-bottom: 40px;

    iframe {
      border-radius: 3px;
      height: 100%;
      width: 100%;
    }
  }

  .latest-videos__content {
    display: flex;
    overflow-x: auto;
    overflow-y: hidden;
    width: 100%;
    margin-bottom: 40px;
  }

  .latest-videos__footer {
    display: flex;
    justify-content: center;
  }

  .videos-list {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;

    .videos-list__item {
      height: auto;
      width: 352px;
      overflow: hidden;

      & + .videos-list__item {
        margin-left: 32px;
      }
    }
  }

  .popup-video {
    display: flex;
    flex-direction: column;

    .popup-video__thumbnail {
      aspect-ratio: 16/9;
      height: auto;
      width: 100%;

      img {
        height: 100%;
        width: 100%;
      }
    }

    .popup-video__info {
      margin-top: 14px;

      .popup-video__info__title {
        color: $color-black;
        font-family: $font-overpass;
        font-style: normal;
        font-weight: 900;
        font-size: 18px;
        line-height: 24px;
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .popup-video__info__date {
        color: $color-drawn;
        font-family: $font-barlow-condensed;
        font-style: normal;
        font-weight: 700;
        font-size: 14px;
        line-height: 16px;
        text-transform: uppercase;
        margin-top: 8px;
      }
    }
  }

  .explore-button {
    padding: 0 24px;
    text-transform: uppercase;

    &:not(:hover) {
      background: $color-white;
    }
  }

  @media screen and (max-width: 1199px) {
    .videos-list {
      .videos-list__item {
        width: 266px;
      }
    }

    .popup-video {
      .popup-video__info {
        margin-top: 12px;
      }
    }
  }

  @media screen and (max-width: 767px) {
    margin-top: 32px;

    .videos-list {
      .videos-list__item {
        width: 192px;
      }
    }

    .popup-video {
      .popup-video__info {
        margin-top: 8px;
      }
    }
  }

  @media screen and (max-width: 576px) {
    margin-top: 24px;
    padding: 24px 0;

    .latest-videos__header,
    .latest-videos__highlight,
    .latest-videos__content {
      margin-bottom: 24px;
    }

    .popup-video {
      .popup-video__info {
        .popup-video__info__title {
          font-size: 16px;
          line-height: 20px;
          -webkit-line-clamp: 3;
        }

        .popup-video__info__date {
          font-size: 12px;
          line-height: 14px;
        }
      }
    }
  }
}

body.lang-th {
  .latest-videos .explore-button {
    @include font-thai();
  }
}

body.lang-vi {
  .latest-videos .explore-button {
    @include font-vietnamese();
  }
}
