# Generated by Django 3.1.13 on 2022-05-31 08:19

from django.db import migrations


def _create_club(model, data):
    club = model.objects.update_or_create(
        ds_club_id=data.team_id,
        defaults={
            'name': data.name,
            'name_short': data.name_short,
            'slug': data.slug,
            'federation_id': data.federation_id,
            'stadium_id': data.home_stadium_id,
            'founded': data.founded,
            'website_url': data.website_url,
            'twitter_url': data.twitter_url,
            'facebook_url': data.facebook_url,
            'instagram_url': data.instagram_url,
            'j1_license': data.j1_license,
            'j2_license': data.j2_license,
            'j3_license': data.j3_license,
            'average_age': data.average_age,
            'average_height': data.average_height,
            'average_weight': data.average_weight,
            'hometowner_count': data.hometowner_count,
            'hometowner_rate': data.hometowner_rate,
            'domestic_league_count': data.domestic_league_count,
            'domestic_league_rate': data.domestic_league_rate,
            'country_id': data.country_id,
            'country_name': data.country_name,
            'prefecture': data.prefecture,
        },
    )
    return club[0]


def patch_clubs(apps, schema_editor):
    Team = apps.get_model('data_stadium', 'Team')
    Club = apps.get_model('clubs', 'Club')
    ClubCompetition = apps.get_model('clubs', 'ClubCompetition')
    ClubExtraInfo = apps.get_model('clubs', 'ClubExtraInfo')
    teams = Team.objects.all().order_by('year', 'team_id')

    for team in teams:
        # create club based on old club table (data_stadium_team)
        club = _create_club(Club, team)

        if club:
            # patch club competition (data_stadium_gamekindseasonteam)
            club_competitions = team.season_teams.all()
            for club_competition in club_competitions:
                ClubCompetition.objects.create(
                    club=club,
                    competition_id=club_competition.game_kind_id,
                    group_id=club_competition.group_id,
                    ordering=club_competition.ordering,
                    season_id=club_competition.season_id,
                    year=club_competition.year,
                )

            # patch club extra info (club field [ForeignKey])
            cinfo = ClubExtraInfo.objects.filter(ds_club_id=team.team_id)[:1]
            if cinfo and len(cinfo):
                cinfo[0].club = club
                cinfo[0].save()


class Migration(migrations.Migration):
    dependencies = [
        ('clubs', '0011_auto_20220531_1719'),
    ]

    operations = [
        migrations.RunPython(patch_clubs),
    ]
