(function () {
  let mutationObserver;

  function _observe(elements, handler) {
    let observer;

    if ('IntersectionObserver' in window) {
      observer = new IntersectionObserver((_entries, _observer) => {
        for (const entry of _entries) {
          if (entry.isIntersecting) {
            _observer.unobserve(entry.target);
            handler(entry.target);
          }
        }
      });
    }

    for (const element of elements) {
      if (typeof observer !== 'undefined') {
        observer.observe(element);
      } else {
        handler(element);
      }
    }
  }

  function _imageLazyLoader() {
    const elements = document.querySelectorAll('img.lazy');
    _observe(elements, (element) => {
      const clubEmblem = element.classList.contains('club-emblem__image');
      const playerPhoto = element.classList.contains('player-photo');
      // use `data-src` instead of `src` to prevent image loads
      // too early (before onerror is applied)
      let dataSrc = element.getAttribute('data-src');

      element.onerror = (e) => {
        console.error('Image Error:', e);
        if (clubEmblem) {
          element.src = '/static/images/club-emblem-placeholder.svg';
          element.classList.add('club-emblem__image--placeholder');
        } else if (playerPhoto) {
          element.src = '/static/images/player-avatar-placeholder.svg';
          element.classList.add('player-photo--placeholder');
        } else {
          element.classList.add('d-none');
        }
      };

      element.onload = () => {
        if (element.naturalWidth) {
          element.width = Math.round(element.naturalWidth);
        }
        if (element.naturalHeight) {
          element.height = Math.round(element.naturalHeight);
        }
      };

      if (dataSrc) element.src = dataSrc;
      if (element.src) element.classList.remove('lazy');
    });
  }

  function _backgroundLazyLoader() {
    const elements = [...document.querySelectorAll('div.lazy'), ...document.querySelectorAll('header.lazy')];
    _observe(elements, (element) => {
      let dataSrc = element.getAttribute('data-src');

      if (dataSrc) {
        dataSrc = dataSrc.includes('url') ? dataSrc : `url("${dataSrc}")`;

        if (element.style.backgroundImage) {
          element.style.backgroundImage = `${element.style.backgroundImage}, ${dataSrc}`;
        } else {
          element.style.backgroundImage = dataSrc;
        }

        element.removeAttribute('data-src');
        element.classList.remove('lazy');
      }
    });
  }

  function lazyLoad() {
    if (mutationObserver) {
      mutationObserver.disconnect();
      mutationObserver = null;
    }

    requestAnimationFrame(() => {
      _imageLazyLoader();
      _backgroundLazyLoader();
    });

    mutationObserver = new MutationObserver(lazyLoad);
    mutationObserver.observe(document.body, { childList: true, subtree: true });
  }

  lazyLoad();
})();
