#! /bin/sh

set -e

export PGPASSWORD=$POSTGRES_PASSWORD
export AWS_ACCESS_KEY_ID=$STORAGE_AWS_ACCESS_KEY_ID
export AWS_SECRET_ACCESS_KEY=$STORAGE_AWS_SECRET_ACCESS_KEY

# https://healthchecks.io/projects/49b6ccaa-421d-4821-9025-45e0894c5895/checks/
curl -X POST https://hc-ping.com/002448a9-1981-4d4a-8d75-2038662cc615/start
echo "Creating dump..."
pg_dump -w -c -h db -U $POSTGRES_USER $POSTGRES_DB | gzip > dump.sql.gz

echo "Uploading dump..."
s3cmd put dump.sql.gz s3://jleague-co/backup/pg_dump_$(date +"%Y-%m-%dT%H:%M:%SZ").sql.gz -c /root/.s3cfg

rm dump.sql.gz

echo "Dump uploaded"
curl -X POST https://hc-ping.com/002448a9-1981-4d4a-8d75-2038662cc615/0
