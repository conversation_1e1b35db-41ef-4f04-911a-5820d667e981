# Generated by Django 3.1.7 on 2021-04-20 10:01

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ('data_stadium', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='GameKindTeamPlayerStats',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField()),
                ('goals_count', models.IntegerField()),
                ('games_count', models.IntegerField()),
                ('time_spent', models.IntegerField()),
                ('shots_count', models.IntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                (
                    'game_kind',
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_stadium.gamekind'),
                ),
                ('player', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_stadium.player')),
                ('team', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_stadium.team')),
            ],
        ),
        migrations.CreateModel(
            name='GameKindTeamPlayerGameStats',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField()),
                ('distance', models.DecimalField(decimal_places=3, max_digits=7, null=True)),
                ('sprint_count', models.IntegerField(null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('game', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_stadium.game')),
                (
                    'game_kind',
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_stadium.gamekind'),
                ),
                ('player', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_stadium.player')),
                ('team', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_stadium.team')),
            ],
        ),
        migrations.CreateModel(
            name='GameKindTeamGameStats',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField()),
                ('distance', models.DecimalField(decimal_places=3, max_digits=7, null=True)),
                ('sprint_count', models.IntegerField(null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('game', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_stadium.game')),
                (
                    'game_kind',
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_stadium.gamekind'),
                ),
                ('team', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_stadium.team')),
            ],
        ),
        migrations.CreateModel(
            name='GameKindTeamAvgStats',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField()),
                ('distance', models.DecimalField(decimal_places=3, max_digits=7, null=True)),
                ('sprint_count', models.IntegerField(null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                (
                    'game_kind',
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_stadium.gamekind'),
                ),
                ('team', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_stadium.team')),
            ],
        ),
        migrations.AddIndex(
            model_name='gamekindteamplayerstats',
            index=models.Index(fields=['year', 'game_kind_id', 'goals_count'], name='data_stadiu_year_8cfbbf_idx'),
        ),
        migrations.AddConstraint(
            model_name='gamekindteamplayerstats',
            constraint=models.UniqueConstraint(
                fields=('year', 'game_kind_id', 'team_id', 'player_id'), name='gktps_uniq_primary_key'
            ),
        ),
        migrations.AddIndex(
            model_name='gamekindteamplayergamestats',
            index=models.Index(fields=['year', 'game_kind_id', 'distance'], name='data_stadiu_year_255ebc_idx'),
        ),
        migrations.AddIndex(
            model_name='gamekindteamplayergamestats',
            index=models.Index(fields=['year', 'game_kind_id', 'sprint_count'], name='data_stadiu_year_0644d0_idx'),
        ),
        migrations.AddConstraint(
            model_name='gamekindteamplayergamestats',
            constraint=models.UniqueConstraint(
                fields=('year', 'game_kind_id', 'team_id', 'player_id', 'game_id'), name='gktpgs_uniq_primary_key'
            ),
        ),
        migrations.AddIndex(
            model_name='gamekindteamgamestats',
            index=models.Index(fields=['year', 'game_kind_id', 'distance'], name='data_stadiu_year_d12fe3_idx'),
        ),
        migrations.AddIndex(
            model_name='gamekindteamgamestats',
            index=models.Index(fields=['year', 'game_kind_id', 'sprint_count'], name='data_stadiu_year_722182_idx'),
        ),
        migrations.AddConstraint(
            model_name='gamekindteamgamestats',
            constraint=models.UniqueConstraint(
                fields=('year', 'game_kind_id', 'team_id', 'game_id'), name='gktgs_uniq_primary_key'
            ),
        ),
        migrations.AddIndex(
            model_name='gamekindteamavgstats',
            index=models.Index(fields=['year', 'game_kind_id', 'distance'], name='data_stadiu_year_726105_idx'),
        ),
        migrations.AddIndex(
            model_name='gamekindteamavgstats',
            index=models.Index(fields=['year', 'game_kind_id', 'sprint_count'], name='data_stadiu_year_7e6ce5_idx'),
        ),
        migrations.AddConstraint(
            model_name='gamekindteamavgstats',
            constraint=models.UniqueConstraint(
                fields=('year', 'game_kind_id', 'team_id'), name='gktas_uniq_primary_key'
            ),
        ),
    ]
