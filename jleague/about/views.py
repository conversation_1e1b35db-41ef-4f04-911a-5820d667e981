from django.shortcuts import render
from django.views.decorators.cache import cache_page
from wagtail.models import Locale

from core.helpers import get_translation_value
from news.models import NewsCategory
from utils.helpers.seo import update_seo_data_django_view
from utils.helpers.view_context import simple_page_context

from .helper import (
    get_broadcast_areas,
)


@cache_page(60 * 60 * 24)
def broadcast(request):
    context = simple_page_context("Broadcast")

    # get broadcast news
    news_category = NewsCategory.objects.filter(slug="broadcast-news").first()
    news_articles = (
        news_category.news_articles.all()
        .filter(locale=Locale.get_active())
        .order_by("-date")
    )
    context["latest_news"] = news_articles

    # get broadcast areas
    broadcast_map, broadcast_links, broadcast_details = get_broadcast_areas()
    context["broadcasters"] = broadcast_details
    context["broadcaster_map"] = broadcast_map
    context["broadcaster_country_links"] = broadcast_links

    # the seo part should be located at the bottom
    title = get_translation_value("SEO_BROADCAST_PAGE_TITLE")
    description = get_translation_value("SEO_BROADCAST_PAGE_DESCRIPTION")
    context = update_seo_data_django_view(
        context, {"title": title, "description": description}
    )

    return render(request, "about/broadcast_page.html", context)
