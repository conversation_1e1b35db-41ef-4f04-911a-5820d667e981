{% load wagtailcore_tags wagtailimages_tags core_tags %}

<div class="home__latest-news-container">
  <div class="home__latest-news-content">
    {% image latest_news.image width-800 format-webp-lossless as latest_news_image %}
    <div
      class="home__latest-news-content__header lazy"
      data-src="url('{{ latest_news_image.url }}')"
      role="img"
      aria-label="J.League latest news article cover image"
    ></div>

    <div class="home__latest-news-content__body">
      <article class="home__latest-news-article">
        <div class="home__latest-news-article__category">
          {% spaceless %}
            {% with latest_news.categories.all as categories %}
              {% for category in categories %}
                {% if forloop.last %}
                  {% translation_obj category.local_names %}
                {% else %}
                  {% translation_obj category.local_names %}, &nbsp;
                {% endif %}
              {% endfor %}
            {% endwith %}
          {% endspaceless %}
        </div>

        <a
          class="home__latest-news-article__title"
          href="{% pageurl latest_news %}"
          onclick="sendSelectContentEvent('news', '{{ latest_news.id }}')"
        >
          {{ latest_news.title }}
        </a>

        <div class="home__latest-news-article__content">
          {% with latest_news.body|first as block %}
            {% if block.block_type == 'richtext' %}
              {{ block.value|safe|striptags|truncatechars:360 }}
            {% endif %}
          {% endwith %}
        </div>

        <a
          class="home__latest-news-article__read-more-button"
          href="{% pageurl latest_news %}"
          onclick="sendSelectContentEvent('news', '{{ latest_news.id }}');"
        >
          {% if read_more_button_label %}
            {{ read_more_button_label }}
          {% else %}
            {% translation 'HOME_PAGE_READ_ARTICLE' %}
          {% endif %}
        </a>
      </article>
    </div>
  </div>
</div>
