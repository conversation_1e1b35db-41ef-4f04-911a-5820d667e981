{% extends 'base.html' %}

{% load pipeline static core_tags %}

{% block extra_styles %}
  {% stylesheet 'fixtures-page' %}
{% endblock %}

{% block extra_scripts %}
  <script type="text/javascript">
    let selectedCompetition = '{{ selected_competition }}';
  </script>

  {% javascript 'tickets-page' %}
{% endblock extra_scripts %}

{% block active_body_class %}template-tickets{% endblock %}

{% block content %}
  <main class="content-container tickets">
    <div class="tickets__header">
      {% include 'components/competitions-list.html' with data=fixture_competitions selected=selected_competition custom_class="competition-selector" %}
    </div>

    <div class="tickets__body">
      <section class="ticket-fixtures"></section>
    </div>

    <div class="tickets__footer">
    </div>

    {% include 'components/tickets-modal.html' %}
  </main>
{% endblock content %}
