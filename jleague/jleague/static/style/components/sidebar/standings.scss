@import 'setup/variable';
@import 'setup/mixin';

.sidebar-table {
  .sidebar__standing_table {
    width: 100%;

    .standing-table__row--header {
      display: flex;
      gap: 4px;
      padding: 4px 8px 4px 8px;

      background: $color-earth-1;
      border-radius: 6px;
      height: 24px;

      .standing-table__col {
        font-family: $font-barlow-condensed;
        font-style: normal;
        font-weight: bold;
        font-size: 12px;
        line-height: 14px;
        text-align: center;
        letter-spacing: 1px;
        text-transform: uppercase;
        color: $color-earth-4;
        vertical-align: middle;
        width: 18px;
      }

      .standing-table__col--position {
        width: 30px;
        text-align: left;
      }

      .standing-table__col--club {
        width: 120px;
      }
    }

    .standing-table__row {
      display: flex;
      gap: 4px;
      padding: 0px 8px 0px 0px;
      margin-top: 2px;
      border-radius: 4px;
      overflow: hidden;

      &:hover {
        background: $color-white;
        border-radius: 4px;
      }

      .standing-table__col {
        display: flex;
        justify-content: center;
        align-items: center;

        position: relative;
        height: 32px;
        width: 30px;
        font-family: $font-jleague-kick;
        font-style: normal;
        font-weight: 500;
        font-size: 16px;
        line-height: 16px;
        color: $color-earth-4;
        text-align: center;
        vertical-align: middle;
      }

      .standing-table__col--position {
        margin-right: 8px;

        span {
          vertical-align: middle;
          font-size: 14px;
          margin-left: 15px;
        }

        img {
          margin-left: 6px;
          vertical-align: middle;
          width: 16px;
          height: 16px;
        }
      }

      .standing-table__col--club {
        display: block;
        width: 120px;
        text-align: left;

        a {
          display: flex;
          align-items: center;
          color: $color-black;

          .club-emblem {
            flex: 0 0 auto;
            margin-right: 8px;
            width: 32px;
            height: 32px;
          }

          .club-name {
            padding: 2px 0;
            font-family: $font-barlow-condensed;

            @include text_ellipsis();
          }

          &:hover {
            opacity: 1;

            .club-name {
              text-decoration: underline;
            }
          }
        }
      }

      .standing-table__col--pl,
      .standing-table__col--pts {
        width: 18px;
        font-size: 14px;
      }

      .standing-table__col--pts {
        color: $color-black;
      }

      &:not(.standing-table__row--header) {
        .standing-table__col {
          &.standing-table__col--position {
            &::before {
              content: '';
              position: absolute;
              left: 0;
              top: 0;
              height: 100%;
              width: 2px;
              border-radius: 90px 0px 0px 90px;
              display: block;
            }

            &.standing-table__col--promoted::before {
              background-color: $color-blue;
            }

            &.standing-table__col--relegated::before {
              background-color: $color-orange;
            }

            &.standing-table__col--playoff::before {
              background-color: $color-black;
            }
          }
        }
      }
    }
  }

  .view-more-button {
    margin: 16px auto 0;
    padding: 8px 16px;
    height: 32px;
    font-size: 14px;
    font-weight: 700;
    line-height: 16px;
    letter-spacing: 1.667px;
    border-radius: 16px;
  }

  @media screen and (max-width: 576px) {
    .sidebar__standing_table {
      .standing-table__row,
      .standing-table__row--header {
        .standing-table__col--club {
          width: 100%;
        }
      }
    }
  }
}

body.lang-th {
  .sidebar-table .sidebar__standing_table {
    .standing-table__row--header .standing-table__col {
      @include font-thai();
    }

    .standing-table__col--club .club-name {
      @include font-thai();
    }
  }
}
