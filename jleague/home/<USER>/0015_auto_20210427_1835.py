# Generated by Django 3.1.7 on 2021-04-27 18:35

from django.db import migrations
from datetime import datetime
from django.utils.timezone import make_aware
import pytz
from wagtail.embeds.embeds import get_embed

japan_tz = pytz.timezone('Asia/Tokyo')


def create_videos(apps, schema_editor):
    VideoItem = apps.get_model('home', 'VideoItem')
    for url, title, date_naive in [
        (
            'https://youtu.be/hEMEE-c4WeU?list=PLWUTUoTjis0DE9ZNjgqC84v-70wKBhBmt',
            'Matchweek 31 Highlights | 2020 | J1 League',
            datetime(year=2020, month=12, day=6),
        ),
        (
            'https://youtu.be/VdQidiPHxQ4?list=PLWUTUoTjis0DSMb1Q1weB7VXzCo6KRMLA',
            'Matchweek 32 Highlights | 2020 | J1 League',
            datetime(year=2020, month=12, day=13),
        ),
        (
            'https://youtu.be/rb9HtzmwLtQ?list=PLWUTUoTjis0CED8WRtX7zazG1DX0dI-kK',
            'Matchweek 33 Highlights | 2020 | J1 League',
            datetime(year=2020, month=12, day=17),
        ),
        (
            'https://youtu.be/m2S2cGicOVc?list=PLWUTUoTjis0B-Xs0MRbkIzeClgCQtpb6u',
            'Matchweek 34 Highlights | 2020 | J1 League',
            datetime(year=2020, month=12, day=19),
        ),
        (
            'https://youtu.be/dQ__bKqY59o?list=PLWUTUoTjis0C-v8b1JDlFAp28onhioMwZ',
            'Matchweek 1 Highlights | 2021 | J1 League',
            datetime(year=2021, month=2, day=27),
        ),
        (
            'https://youtu.be/j64Y7kl41Y8?list=PLWUTUoTjis0DAJZm5ek0xKUxoTonR3G1h',
            'Matchweek 2 Highlights | 2021 | J1 League',
            datetime(year=2021, month=3, day=6),
        ),
        (
            'https://youtu.be/caG79Ws53jc?list=PLWUTUoTjis0DC4UNaB8ENSCZN_CEErXy6',
            'Matchweek 3 Highlights | 2021 | J1 League',
            datetime(year=2021, month=3, day=10),
        ),
        (
            'https://youtu.be/Ii8F3p1AcLc?list=PLWUTUoTjis0C8meA6DzyznQiBETQxkW2p',
            'Matchweek 4 Highlights | 2021 | J1 League',
            datetime(year=2021, month=3, day=14),
        ),
        (
            'https://youtu.be/qbtvVOQOodU?list=PLWUTUoTjis0AdcXP8gkeWJ9AyP_RMkZap',
            'Matchweek 5 Highlights | 2021 | J1 League',
            datetime(year=2021, month=3, day=18),
        ),
        (
            'https://youtu.be/yGCjaBjoXsc?list=PLWUTUoTjis0BL1M_L2EP-ekUvhFryjtLx',
            'Matchweek 6 Highlights | 2021 | J1 League',
            datetime(year=2021, month=3, day=21),
        ),
    ]:
        date = make_aware(date_naive, japan_tz)
        vi = VideoItem(video_url=url, date=date, title=title)
        emb = get_embed(url)
        vi.thumbnail_url = emb.thumbnail_url
        vi.html = emb.html
        html = emb.html
        html = html[html.index('src=\"') + len('src=\"') :]
        embed_url = html[: html.index('"')]
        vi.embed_url = embed_url
        vi.author_name = emb.author_name
        vi.provider_name = emb.provider_name
        vi.save()


def reverse_code(*args, **kwargs):
    pass


class Migration(migrations.Migration):
    dependencies = [
        ('home', '0014_videoitem'),
        ('wagtailembeds', '0008_allow_long_urls'),
    ]

    operations = [
        migrations.RunPython(create_videos, reverse_code=reverse_code),
    ]
