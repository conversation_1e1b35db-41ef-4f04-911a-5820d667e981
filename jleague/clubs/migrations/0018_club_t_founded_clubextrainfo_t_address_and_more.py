# Generated by Django 4.1.4 on 2023-01-24 15:14

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("clubs", "0017_clubextrainfo_local_names"),
    ]

    operations = [
        migrations.Add<PERSON>ield(
            model_name="club",
            name="t_founded",
            field=models.JSO<PERSON>ield(blank=True, default=dict, null=True),
        ),
        migrations.AddField(
            model_name="clubextrainfo",
            name="t_address",
            field=models.J<PERSON><PERSON>ield(blank=True, default=dict, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name="clubextrainfo",
            name="t_phone_number",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AddField(
            model_name="clubextrainfo",
            name="t_practice_ground",
            field=models.JSONField(blank=True, default=dict, null=True),
        ),
        migrations.AddField(
            model_name="clubextrainfo",
            name="t_president",
            field=models.J<PERSON><PERSON>ield(blank=True, default=dict, null=True),
        ),
    ]
