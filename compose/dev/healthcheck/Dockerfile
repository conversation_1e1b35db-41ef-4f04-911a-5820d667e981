FROM python:3.10-slim-buster

# install cron
RUN apt-get update && apt-get install -y -qq cron iputils-ping
# install slack sdk
RUN pip install slack-sdk requests

# copy files
COPY compose/dev/healthcheck/health_check.py health_check.py

# register cron job
RUN echo "*/1 * * * * root python /health_check.py > /proc/1/fd/1 2>/proc/1/fd/2" >> /etc/crontab

# start cron in foreground (don't fork)
ENTRYPOINT [ "cron", "-f" ]
