# Generated by Django 3.1.13 on 2022-03-02 06:50

from django.db import migrations, models
import django.db.models.deletion
import modelcluster.fields
import wagtail.search.index


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ('media', '0003_auto_20220225_1618'),
        ('data_stadium', '0040_auto_20220225_1400'),
        ('wagtailimages', '0023_add_choose_permissions'),
    ]

    operations = [
        migrations.CreateModel(
            name='ClubExtraInfo',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.TextField(null=True)),
                ('team_id', models.IntegerField(unique=True)),
                (
                    'fk_team',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='+',
                        to='data_stadium.team',
                    ),
                ),
                (
                    'horizontal_bg',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='+',
                        to='wagtailimages.image',
                    ),
                ),
                (
                    'vertical_bg',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='+',
                        to='wagtailimages.image',
                    ),
                ),
            ],
            options={
                'abstract': False,
            },
            bases=(models.Model, wagtail.search.index.Indexed),
        ),
        migrations.CreateModel(
            name='ClubVideo',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sort_order', models.IntegerField(blank=True, editable=False, null=True)),
                (
                    'team_extra_info',
                    modelcluster.fields.ParentalKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='team_videos',
                        to='clubs.clubextrainfo',
                    ),
                ),
                (
                    'video',
                    models.ForeignKey(
                        null=True, on_delete=django.db.models.deletion.CASCADE, related_name='+', to='media.video'
                    ),
                ),
            ],
            options={
                'ordering': ['sort_order'],
                'abstract': False,
            },
        ),
    ]
