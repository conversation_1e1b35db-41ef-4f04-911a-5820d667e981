from django.urls import path, reverse
from django.views.generic.base import RedirectView
from .helper import get_league_clubs
from utils.helpers import get_display_data_year
from wagtail.contrib.sitemaps.views import sitemap
from . import views
from .sitemaps import sitemaps

year = get_display_data_year()


def _club_profile_url(*args, **kwargs):
    if kwargs.get('team_slug'):
        return reverse('club_profile', args=[kwargs['team_slug']])
    return reverse('clubs')


urlpatterns = [
    # sitemap
    path('sitemap.xml', sitemap, {'sitemaps': sitemaps}),
    # ajax
    path('<slug:team_slug>/stats/<int:year>/', views.club_stats),
    path('<slug:team_slug>/players/stats/<int:year>/', views.club_player_stats),
    path('<slug:team_slug>/players/<int:year>/', views.club_players),
    path('<slug:team_slug>/<slug:anything>/', RedirectView.as_view(get_redirect_url=_club_profile_url, permanent=True)),
    # pages
    path('', views.clubs, name='clubs'),
    path('<slug:team_name>/', views.club_profile, name='club_profile'),
]
