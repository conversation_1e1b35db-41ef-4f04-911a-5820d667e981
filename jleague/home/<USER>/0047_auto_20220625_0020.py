# Generated by Django 3.1.13 on 2022-06-24 15:20

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ('wagtailforms', '0004_add_verbose_name_plural'),
        ('wagtailredirects', '0006_redirect_increase_max_length'),
        ('wagtailcore', '0066_collection_management_permissions'),
        ('home', '0046_auto_20220429_2102'),
        ('news', '0006_auto_20220624_2337'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='newsarticle',
            name='categories',
        ),
        migrations.RemoveField(
            model_name='newsarticle',
            name='clubs',
        ),
        migrations.RemoveField(
            model_name='newsarticle',
            name='image',
        ),
        migrations.RemoveField(
            model_name='newsarticle',
            name='page_ptr',
        ),
        migrations.DeleteModel(
            name='Club',
        ),
        migrations.DeleteModel(
            name='NewsArticle',
        ),
        migrations.DeleteModel(
            name='NewsCategory',
        ),
    ]
