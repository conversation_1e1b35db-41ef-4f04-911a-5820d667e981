@import 'setup/mixin';

.body-club-list {
  header {
    padding-bottom: 78px;
  }

  .club-list {
    .nav-container {
      margin-top: 40px;
      display: flex;
      justify-content: center;
      overflow-x: auto;
      overflow-y: hidden;

      .btn-nav {
        .btn-nav__label {
          margin-left: 16px;
        }
      }
    }

    .clubs-container {
      margin-top: 40px;

      & + .clubs-container {
        margin-top: 80px;
      }

      .league-logo {
        display: flex;
        justify-content: center;
        align-items: center;

        & > img {
          width: auto;

          @media screen and (min-width: 1200px) {
            height: 80px;
          }

          @media screen and (max-width: 1199px) and (min-width: 768px) {
            height: 80px;
          }

          @media screen and (max-width: 767px) {
            height: 64px;
          }
        }
      }

      .section-header {
        margin-top: 32px;
      }

      .club-list {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 32px;
        margin: 24px 0;
      }

      .club-item {
        border-radius: 8px;
        overflow: hidden;
        display: block;

        .logo {
          position: relative;
          height: 224px;

          .club-emblem {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
          }
        }

        .name {
          display: flex;
          justify-content: center;
          align-items: center;
          height: 96px;
          padding: 16px;

          .team-text {
            text-align: center;
            font-family: var(--font-jleague-kick);
            font-style: normal;
            font-weight: 700;
            font-size: 28px;
            line-height: 32px;
          }
        }

        &:hover {
          opacity: 1;

          .team-text {
            text-decoration: underline;
          }
        }
      }
    }

    .club-item-buttons {
      display: flex;
      flex-direction: column;
      gap: 12px;
      margin-top: 24px;

      .club-item-button {
        width: 100%;
        font-size: 16px;
        padding: 13.5px;
        border-radius: 16px;
        border: 1px solid black;
        background-color: white;
        font-weight: bold;
        cursor: pointer;
        text-align: center;
        color: black;

        &:hover {
          text-decoration: underline;
          opacity: 1;
        }

        &.buy-button {
          background-color: #e40827;
          border: 1px dashed transparent;
          color: white;
        }
      }
    }

    .club-tickets-list {
      .league-logo {
        justify-content: flex-start;

        @media screen and (max-width: 767px) {
          justify-content: center;
        }
      }

      .club-list {
        gap: 52px 32px;
      }
    }
  }


  @media screen and (max-width: 1199px) {
    header {
      padding-bottom: 54px;
    }

    .club-list {
      .clubs-container {
        .club-list {
          grid-template-columns: repeat(3, 1fr);

          .club-item {
            .logo {
              height: 200px;
            }
          }
        }
      }
    }
  }

  @media screen and (max-width: 767px) {
    header {
      padding-bottom: 54px;
    }

    .club-list {
      .nav-container {
        .btn-nav {
          flex: 1;
        }
      }

      .clubs-container {
        .club-list {
          grid-template-columns: repeat(2, 1fr);

          .club-item {
            .logo {
              height: 120px;

              .club-emblem {
                width: 120px;
                height: 120px;
              }
            }

            .name {
              height: 80px;

              .team-text {
                font-size: 20px;
                line-height: 24px;
              }
            }
          }
        }
      }
    }
  }

  @media screen and (max-width: 425px) {
    .club-list {
      .clubs-container {
        margin-top: 0;
      }
    }
  }
}

body.lang-th {
  .club-list .clubs-container .club-item .name .team-text {
    @include text_ellipsis();
    line-height: normal;
  }
}

.banner-image {
  width: 100%;
  height: auto;
  display: block;
}

.clubtickets-lp-container {
  .jleague-map {
    max-width: 100%;
    position: relative;

    img {
      width: 100%;
      height: auto;
    }

    @media screen and (max-width: 767px) {
      img, svg {
        display: none;
      }
    }
  }

  .clubtickets-lp--title {
    font-size: 70px;
    margin: 75px auto;
    text-align: center;

    @media screen and (max-width: 1199px) {
      font-size: 40px;
    }

    @media screen and (max-width: 767px) {
      display: none;
    }
  }

  .clubtickets-lp--title-mb {
    display: none;
    @media screen and (max-width: 767px) {
      display: block;
      text-align: center;
      font-weight: bold;
      font-size: 16px;
      margin-bottom: 16px;
      margin-top: 8px;
    }
  }

  .clubtickets-lp--description {
    display: block;
    padding: 60px 0;
    font-weight: bold;
    font-size: 23px;
  }

  .clubtickets-lp-logo.team-bg-img {
    width: 50px;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    cursor: pointer;

    top: var(--top, 0);
    right: var(--right, 0);

    .club-emblem {
      width: 85%;
      height: auto;
    }
  }

  .logo-clubs-items {
    @media screen and (max-width: 767px) {
      display: none;
    }
  }
}

.clubtickets-lp--sub-banner {
  display: flex;
  justify-content: center;
  gap: 75px;
  flex-wrap: wrap;
  padding: 75px 0;

  .banner {
    width: 350px;
    display: block;
    position: relative;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.2);
    }

    .play-icon {
      display: block;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 104px;
      height: 104px;
      border-radius: 50%;
      border-style: solid;
      border-width: 6px;
      border-color: rgba(255, 255, 255, 0.8);
      box-sizing: border-box;

      &::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-36%, -50%);
        width: 54px;
        height: 54px;
        border-style: solid;
        border-width: 32px 0px 32px 54px;
        border-color: transparent transparent transparent rgba(255, 255, 255, 0.8);
        box-sizing: border-box;
      }
    }
  }
}

.jleague-map-mb {
  display: none;
  width: 100%;
  height: 100%;

  @media screen and (max-width: 767px) {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 32px;
  }

  .jleague-league {
    display: grid;
    grid-template-columns: repeat(2, 116px);
    gap: 1rem;
  }

  .jleague-club {
    text-align: center;
    padding: 8px;
    width: 100%;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
    border: 1px solid #7A7A7A;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 36px;
  }
}

.clubtickets-lp-clubs-select-container {
  position: relative;

  #mapImage {
    border: 1px solid black;
    position: absolute;
    top: 0;
    left: 0;
  }

  @media screen and (min-width: 768px) {
    padding-bottom: 32px;
  }

  @media screen and (max-width: 500px) {
    .nav-container {
      margin-left: -16px;
      margin-right: -16px;

      .btn-nav.large {
        padding: 0;

        .btn-nav__label {
          margin-left: 8px;
        }
      }
    }
  }

  .clubtickets-lp-select-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    justify-content: center;
    align-items: center;
    background-color: rgba($color: #D9D9D9, $alpha: .7);

    @media screen and (min-width: 768px) {
      display: none !important;
    }

    &.active {
      display: flex;
    }

    .clubtickets-lp-select-clubs {
      width: 300px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;
      background-color: white;
      padding: 18px 0;

      .clubtickets-lp-select-clubs-list {
        display: flex;
        flex-direction: column;
        gap: 16px;
        align-items: center;
      }
    }

    .club-item-mb {
      padding: 0 8px;
      width: 100%;
      border-radius: 4px;
      font-size: 14px;
      font-weight: bold;
      border: 1px solid black;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 36px;
      gap: 4px;
      min-width: 192px;

      .club-logo {
        width: 36px;
        height: 36px;
        display: flex;
        justify-content: center;
        align-items: center;

        .club-emblem {
          width: 100%;
          height: 100%;
        }
      }

      .club-name {
        flex: 1;
        text-align: center;
      }
    }
  }
}
