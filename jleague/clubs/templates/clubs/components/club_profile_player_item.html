{% load core_tags %}

{% spaceless %}
  <a
    class="player-item"
    href="{% url 'player_name_profile' data.id data.name_slug %}"
    onclick="sendSelectContentEvent('player', '{{ data.id }};{{ data.name }}');"
  >
    <span class="uniform-no">
      {{ data.jersey_no }}
    </span>

    <span class="position">
      {{ data.position }}
    </span>

    {% include 'components/player-photo.html' with player=player photo=data.avatar_image %}

    <div class="info">
      <div class="player-name">{% player_name data %}</div>

      <div class="nationality">
        <span class="flag">
          <img
            class="lazy"
            src="https://hatscripts.github.io/circle-flags/flags/{{ data.nationality_flag }}.svg"
            alt="{{ data.nationality_name }}"
            loading="lazy"
          />
        </span>

        <span class="name">
          {% translation_obj data.country_local_names %}
        </span>
      </div>
    </div>
  </a>
{% endspaceless %}