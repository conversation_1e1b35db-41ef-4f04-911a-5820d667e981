# Generated by Django 4.1.4 on 2024-03-13 05:19

from django.db import migrations, models
import django.db.models.deletion
import django_jsonform.models.fields


class Migration(migrations.Migration):
    dependencies = [
        ("core", "0031_alter_banner_language"),
    ]

    operations = [
        migrations.CreateModel(
            name="CompetitionSystem",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=255)),
                ("year", models.IntegerField()),
                ("stages", django_jsonform.models.fields.JSONField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "competition",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="core.competition",
                    ),
                ),
            ],
        ),
    ]
