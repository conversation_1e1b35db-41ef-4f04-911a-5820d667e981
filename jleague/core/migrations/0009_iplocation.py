# Generated by Django 3.1.13 on 2022-06-16 09:38

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ('core', '0008_auto_20220601_2159'),
    ]

    operations = [
        migrations.CreateModel(
            name='IPLocation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ip_address', models.GenericIPAddressField(db_index=True, unique=True)),
                ('ip_type', models.CharField(choices=[('IPv4', 'IPv4'), ('IPv6', 'IPv6')], max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                (
                    'country',
                    models.ForeignKey(
                        blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.country'
                    ),
                ),
            ],
        ),
    ]
