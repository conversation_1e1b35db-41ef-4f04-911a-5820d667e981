@import 'setup/variable';

.poster-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: block;
    background-color: transparentize($color: $color-black, $amount: 0.5);
    z-index: 1;
  }

  &.poster-popup--closed {
    display: none;
  }

  .poster-popup__close {
    position: absolute;
    top: -10px;
    right: -12px;
    z-index: 2;
    background-color: #fff;
    border-radius: 50%;
    height: 30px;
    width: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid;

    &:hover {
      cursor: pointer;
    }
  }

  .poster-popup__content {
    position: relative;
    z-index: 2;
    flex: auto;
    max-width: 80vw;

    .poster-header,
    .poster-body,
    .poster-footer {
      position: relative;
    }

    .poster-link {
      width: 100%;
      height: auto;
      margin: auto;

      &:hover {
        opacity: 1;
        cursor: pointer;
      }
    }

    .tall-version,
    .wide-version {
      display: none;
      width: 100%;
      height: auto;
    }
  }

  @media screen and (min-width: 769px) {
    .poster-popup__content {
      .wide-version {
        display: block;
      }
      .tall-version {
        display: none;
      }
    }
  }

  @media screen and (max-width: 768px) {
    .poster-popup__content {
      width: 95vw;

      .wide-version {
        display: none;
      }
      .tall-version {
        display: block;
      }
    }
  }

  @media screen and (max-width: 600px) {
    .poster-popup__content {
      width: 100%;
      flex: auto;
      max-height: none;

      .setting-body {
        max-height: none;
        // height: calc(100vh - 80px - 90px);
      }

      .action-buttons {
        justify-content: center;
      }

      .tall-version {
        display: block;
      }
    }
  }
}
