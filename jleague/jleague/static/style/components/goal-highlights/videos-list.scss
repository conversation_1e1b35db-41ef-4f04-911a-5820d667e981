.videos-list {
  &.loading {
    min-height: 275px;
  }

  .video-item {
    width: 100%;
    max-height: 275px;
    max-width: 240px;
    border-radius: 8px;
    background-color: var(--color-earth-1);
    overflow: hidden;
    position: relative;

    .video-item__header,
    .video-item__body {
      position: relative;
      pointer-events: none;
    }

    .video-item__header {
      line-height: 0;

      .video-thumbnail {
        aspect-ratio: 16/9;
        height: auto;
        width: 100%;

        img {
          height: 100%;
          width: 100%;
          object-fit: cover;
        }
      }

      &::after {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background: var(--color-black);
        opacity: 0.6;
        transition: opacity 300ms ease-in-out;
      }
    }

    .video-item__body {
      padding: 16px;

      .team-bg {
        position: absolute;
        height: 4px;
        width: 100%;
        top: -2px;
        left: 0;
        right: 0;
      }
    }

    &:hover {
      cursor: pointer;

      .video-item__header {
        &::after {
          opacity: 0;
        }
      }
    }
  }
}
