import json
from .models import BroadcastArea
from core.helpers import (
    get_translation_obj_value,
    get_translation_value,
    get_competition_name,
)
from core.constants import COMPETITION_TIER
from utils.helpers.etc import get_user_country


def get_broadcast_areas():
    broadcast_map = {}
    broadcast_links = {}
    broadcast_details = []
    areas = (
        BroadcastArea.objects.prefetch_related(
            "countries", "competitions", "broadcasters"
        )
        .filter(is_active=True)
        .order_by("sort_order")
    )
    for area in areas:
        countries = area.countries.all()
        broadcasters = area.broadcasters.all()
        competitions = area.competitions.all()
        sorted_competitions = []
        tier_competitions = {"1": [], "2": [], "3": []}

        for c in competitions:
            competition_name = get_competition_name(c.format())
            tier = COMPETITION_TIER.get(c.slug)
            if tier:
                if tier not in tier_competitions:
                    tier_competitions[tier] = []
                tier_competitions[tier].append(competition_name)

        for tier, comp in tier_competitions.items():
            sorted_competitions.extend(sorted(comp))

        details = {
            "country_note": area.country_note,
            "competition_note": area.competition_note,
            "competitions": sorted_competitions,
            "networks": [],
        }

        if broadcasters and len(broadcasters):
            for broadcaster in broadcasters:
                details["networks"].append(
                    {
                        "name": broadcaster.name,
                        "link": broadcaster.website,
                    }
                )

        if countries and len(countries):
            details["countries"] = []
            details["country_codes"] = []

            for country in countries:
                country_code = country.two_letter_code.lower()

                details["countries"].append(
                    get_translation_obj_value(country.local_names)
                )
                details["country_codes"].append(country_code)

                broadcast_map[country_code] = 0

                if broadcasters and len(broadcasters):
                    broadcast_links[country_code] = broadcasters[0].website

        else:
            details["countries"] = [get_translation_value(area.name)]

        broadcast_details.append(details)

    broadcast_map = json.dumps(broadcast_map)
    broadcast_links = json.dumps(broadcast_links)
    return broadcast_map, broadcast_links, broadcast_details


def get_local_broadcaster(request):
    request_country = get_user_country(request)

    area = (
        BroadcastArea.objects.prefetch_related("competitions", "broadcasters")
        .filter(
            is_active=True,
            countries__two_letter_code=request_country,
        )
        .first()
    )

    broadcasters = []
    competitions = []

    if area:
        broadcasters = area.broadcasters.filter(is_active=True)
        competitions = area.competitions.all()

    return broadcasters, competitions
