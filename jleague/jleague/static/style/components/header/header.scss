@import 'setup/variable';
@import 'setup/mixin';

header {
  position: relative;
  background-attachment: fixed;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  padding-bottom: 64px;

  section {
    margin: 0 auto;
    padding: 0;
    max-width: 1440px;
    padding: 0 120px;
  }

  .logo-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 24px;

    .logo-link {
      img {
        &.lazy {
          min-height: 40px;
        }
      }
    }
  }

  .header-title {
    position: relative;
    font-family: var(--font-druk-wide);
    font-style: normal;
    font-weight: 900;
    font-size: 44px;
    line-height: 44px;
    letter-spacing: 4px;
    text-align: center;
    text-transform: uppercase;
    color: var(--color-white);
    margin-top: 44px;

    &::after {
      content: '';
      width: 200px;
      height: 6px;
      position: absolute;
      top: calc(100% - -8px);
      left: 50%;
      transform: translate(-50%, 0%);
      background: linear-gradient(90deg, transparentize($color-black, 0.9999) 0%, var(--color-red) 99.69%);
      border-radius: 1px;
    }
  }

  .today-games ~ .header-title,
  .match-week-games ~ .header-title {
    margin-top: 64px;
  }

  @media screen and (max-width: 1439px) and (min-width: 1121px) {
    section {
      margin: 0 auto;
      padding: 0;
      max-width: 1120px;
      padding: 0 24px;
    }
  }

  @media screen and (max-width: 1199px) {
    .header-title {
      margin-top: 40px;
      letter-spacing: 2px;
    }
  }

  @media screen and (max-width: 1199px) and (min-width: 768px) {
    .header-title {
      font-size: 40px;
      line-height: 40px;
    }
  }

  @media screen and (min-width: 1121px) {
    .logo-container {
      .menu-trigger,
      .search-modal-trigger {
        display: none;
      }
    }
  }

  @media screen and (max-width: 1120px) {
    padding-bottom: 48px;

    section {
      margin: 0 48px;
      padding: 0;
    }

    .logo-container {
      height: 88px;
      display: flex;
      justify-content: space-between;

      .search-modal-trigger {
        display: block;
      }
    }
  }

  @media screen and (max-width: 768px) {
    padding-bottom: 24px;

    section {
      margin: 0 24px;
      width: calc(100% - 48px);
    }

    .logo-container {
      height: 64px;
      padding: 0 16px;

      .logo-link {
        img {
          width: auto;
          height: 32px;
        }
      }
    }

    .header-title {
      font-size: 32px;
      line-height: 32px;
    }

    .today-games ~ .header-title,
    .match-week-games ~ .header-title {
      margin-top: 40px;
    }
  }

  @media screen and (max-width: 576px) {
    .header-title {
      font-size: 28px;
      line-height: 28px;
    }
  }

  @media screen and (max-width: 425px) {
    margin-bottom: 0;
  }
}

@import 'nav-container';
