# Generated by Django 3.1.7 on 2021-04-20 15:29

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ('data_stadium', '0002_auto_20210420_1001'),
    ]

    operations = [
        migrations.CreateModel(
            name='GameKindTeamStats',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField()),
                ('games_count', models.IntegerField()),
                ('won_games_count', models.IntegerField()),
                ('lost_games_count', models.IntegerField()),
                ('draw_games_count', models.IntegerField()),
                ('goals_scored', models.IntegerField()),
                ('goals_missed', models.IntegerField()),
                ('points', models.IntegerField()),
                ('shots_count', models.IntegerField()),
                ('fkd_value', models.IntegerField()),
                ('fki_value', models.IntegerField()),
                ('ck_value', models.IntegerField()),
                ('pk_value', models.IntegerField()),
                ('yellow_cards', models.IntegerField()),
                ('red_cards', models.IntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                (
                    'game_kind',
                    models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_stadium.gamekind'),
                ),
                ('team', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='data_stadium.team')),
            ],
        ),
        migrations.AddIndex(
            model_name='gamekindteamstats',
            index=models.Index(fields=['year', 'game_kind_id', 'team_id'], name='data_stadiu_year_9f05c9_idx'),
        ),
        migrations.AddIndex(
            model_name='gamekindteamstats',
            index=models.Index(fields=['year', 'team_id'], name='data_stadiu_year_7dc2c5_idx'),
        ),
        migrations.AddConstraint(
            model_name='gamekindteamstats',
            constraint=models.UniqueConstraint(
                fields=('year', 'game_kind_id', 'team_id'), name='gkts_uniq_primary_key'
            ),
        ),
    ]
