@import 'setup/mixin';

.section-header {
  background: transparent;
  display: flex;
  align-items: center;
  height: auto;
  min-height: 56px;
  position: relative;
  padding-bottom: 4px;

  .section-header__title {
    @include section-header-title-text();
    color: var(--color-black);
  }

  &::after {
    content: '';
    position: absolute;
    bottom: 0px;
    left: 0;
    width: 100%;
    border: solid 2px var(--color-black);
    box-sizing: border-box;
  }

  &.section-header--with-logo {
    justify-content: space-between;
  }

  @media screen and (max-width: 991px) {
    min-height: 40px;

    .section-header__league-logo {
      .competition-logo {
        height: 32px;
        width: auto;
      }
    }
  }

  @media screen and (max-width: 767px) {
    height: auto;
    padding: 0;
  }

  @media screen and (max-width: 576px) {
    .section-header__league-logo {
      .competition-logo {
        height: 26px;
      }
    }
  }

  @media screen and (max-width: 425px) {
    #fixture.section-header__title {
      font-size: 24px;
      line-height: 24px;
      text-align: center;
      width: 100%;
      margin-bottom: 16px;
    }
  }
}

body.lang-vi {
  .section-header.small .section-header__title {
    font-size: 28px;
    line-height: 28px;
  }
}
