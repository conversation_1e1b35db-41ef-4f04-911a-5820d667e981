from django.contrib import admin
from wagtail.contrib.modeladmin.options import (
    ModelAdmin,
    ModelAdminGroup,
    modeladmin_register,
)
from .models import BroadcastArea, Broadcaster


class BroadcasterAdmin(ModelAdmin):
    """Menu: Broadcasts -> Broadcasters"""

    model = Broadcaster
    menu_label = "Broadcasters"
    menu_icon = "radio-empty"
    menu_order = 999
    list_display = ("name", "is_active")
    list_filter = ("is_active",)
    search_fields = ("name",)


class BroadcastAreaAdmin(ModelAdmin):
    """Menu: Broadcasts -> Areas"""

    model = BroadcastArea
    menu_label = "Areas"
    menu_icon = "site"
    menu_order = 999
    list_display = ("name", "is_active")
    list_filter = ("is_active",)
    search_fields = ("name",)


class BroadcastAdminGroup(ModelAdminGroup):
    """Menu: Broadcasts"""

    menu_label = "Broadcasts"
    menu_icon = "radio-full"
    menu_order = 999
    items = (BroadcasterAdmin, BroadcastAreaAdmin)


# wagtail register
modeladmin_register(BroadcastAdminGroup)

# django register
admin.site.register(BroadcastArea)
admin.site.register(Broadcaster)
