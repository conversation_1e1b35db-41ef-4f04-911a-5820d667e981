from django.contrib import admin
from wagtail.contrib.modeladmin.options import (
    ModelAdmin,
    ModelAdminGroup,
    modeladmin_register,
)
from wagtail import hooks
from .models import (
    Partner,
    PartnerGroup,
    NavigationLink,
    NavigationGroup,
    Translation,
    Banner,
)


class CustomHookModelAdmin(ModelAdmin):
    hook = "register_admin_menu_item"

    def register_with_wagtail(self):
        @hooks.register("register_permissions")
        def register_permissions():
            return self.get_permissions_for_registration()

        @hooks.register("register_admin_urls")
        def register_admin_urls():
            return self.get_admin_urls_for_registration()

        menu_hook = (
            "register_settings_menu_item" if self.add_to_settings_menu else self.hook
        )

        @hooks.register(menu_hook)
        def register_admin_menu_item():
            return self.get_menu_item()

        # Overriding the explorer page queryset is a somewhat 'niche' / experimental
        # operation, so only attach that hook if we specifically opt into it
        # by returning True from will_modify_explorer_page_queryset
        if self.will_modify_explorer_page_queryset():

            @hooks.register("construct_explorer_page_queryset")
            def construct_explorer_page_queryset(parent_page, queryset, request):
                return self.modify_explorer_page_queryset(
                    parent_page, queryset, request
                )

        self.register_admin_url_finders()


# Partners
class PartnerAdmin(ModelAdmin):
    """Menu: Partners -> List"""

    model = Partner
    menu_label = "Partners"
    menu_icon = "list-ul"
    menu_order = 999
    list_display = ("name", "is_active")
    list_filter = ("is_active",)
    search_fields = ("name",)


class PartnerGroupAdmin(ModelAdmin):
    """Menu: Partners -> Groups"""

    model = PartnerGroup
    menu_label = "Groups"
    menu_icon = "list-ul"
    menu_order = 999
    list_display = ("name", "is_active")
    list_filter = ("is_active",)
    search_fields = ("name",)


class PartnerAdminGroup(ModelAdminGroup):
    """Menu: Partners"""

    menu_label = "Partners"
    menu_icon = "link"
    menu_order = 999
    items = (PartnerAdmin, PartnerGroupAdmin)


# Navigation
class NavigationLinkAdmin(ModelAdmin):
    """Menu: Navigation -> Links"""

    model = NavigationLink
    menu_label = "Links"
    menu_icon = "radio-full"
    menu_order = 999
    list_display = ("label", "link_page", "link_external")
    search_fields = ("label", "link_page", "link_external")


class NavigationGroupAdmin(ModelAdmin):
    """Menu: Navigation -> Groups"""

    model = NavigationGroup
    menu_label = "Groups"
    menu_icon = "radio-full"
    menu_order = 999
    list_display = ("title",)
    search_fields = ("title",)


class NavigationAdminGroup(ModelAdminGroup):
    """Menu: Navigation"""

    menu_label = "Navigation"
    menu_icon = "radio-full"
    menu_order = 999
    items = (NavigationLinkAdmin, NavigationGroupAdmin)


class BannerAdmin(ModelAdmin):
    """Menu: Banner"""

    model = Banner
    menu_label = "Banners"
    menu_icon = "thumbtack"
    menu_order = 999
    list_display = ("title", "description", "target_url")
    search_fields = ("title", "description", "target_url")


# Translation
class TranslationAdmin(CustomHookModelAdmin):
    """Menu: Translation -> List"""

    model = Translation
    menu_label = "List"
    menu_icon = "site"
    menu_order = 1
    list_display = ("key",)
    list_filter = ("is_active",)
    search_fields = ("key",)
    hook = "register_core_translation_menu_item"


# wagtail register
modeladmin_register(PartnerAdminGroup)
modeladmin_register(NavigationAdminGroup)
modeladmin_register(TranslationAdmin)
modeladmin_register(BannerAdmin)


# django register
admin.site.register(Partner)
admin.site.register(PartnerGroup)
admin.site.register(NavigationLink)
admin.site.register(NavigationGroup)
admin.site.register(Translation)
admin.site.register(Banner)
