# Generated by Django 3.1.13 on 2022-04-25 05:14

from django.db import migrations


def patch_team_manager(apps, schema_editor):
    Team = apps.get_model('data_stadium', 'Team')
    ClubExtraInfo = apps.get_model('clubs', 'ClubExtraInfo')
    ClubStaff = apps.get_model('clubs', 'ClubStaff')

    team_ids = []
    staff_ids = []
    clubs = Team.objects.all().distinct('team_id').order_by('team_id', '-year').values('team_id', 'name', 'staff')
    extra_infos = ClubExtraInfo.objects.all()
    extra_info_mapping = {}

    for extra in extra_infos:
        extra_info_mapping[extra.team_id] = extra

    for club in clubs:
        if club['team_id'] in team_ids:
            # skipped already exists club
            continue

        extra_info = extra_info_mapping.get(club['team_id'])
        staffs = list(club['staff']) if club['staff'] else []

        if not extra_info:
            # create club extra info of non-exists
            extra_info = ClubExtraInfo.objects.create(
                team_id=club['team_id'],
                title=club['name'],
            )

        if extra_info and len(staffs):
            staff = staffs[0]
            if staff['id'] in staff_ids:
                # skipped already exists staff
                continue

            # create new staff
            ClubStaff.objects.create(
                sort_order=0,
                staff_id=staff['id'],
                name=staff['name'],
                name_short=staff['name_short'],
                position=staff['post'],
                nationality_name=staff['nationality_name'],
                club_extra_info=extra_info,
            )

            # update counter
            staff_ids.append(staff['id'])
            team_ids.append(club['team_id'])


class Migration(migrations.Migration):
    dependencies = [
        ('clubs', '0005_clubstaff'),
    ]

    operations = [migrations.RunPython(patch_team_manager)]
