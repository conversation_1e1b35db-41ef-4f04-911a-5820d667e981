@import 'setup/variable';

header .nav-container .navbar {
  .more-menu {
    position: relative;

    .more-menu-trigger {
      background: none;
      border: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      padding: 1px 7px 3px;

      .more-menu-trigger__label {
        font-family: $font-barlow-condensed;
        font-style: normal;
        font-weight: 500;
        font-size: 20px;
        line-height: 24px;
        letter-spacing: 2px;
        color: $color-white;
        text-transform: uppercase;
      }

      .more-menu-trigger__arrow {
        margin-left: 10px;
        pointer-events: none;
      }

      &:hover {
        opacity: 1;
        background: $color-white;
        border-radius: 1px;

        .more-menu-trigger__label {
          color: $color-black;
        }

        .more-menu-trigger__arrow {
          filter: invert(100%);
        }
      }
    }

    .more-menu-modal {
      position: absolute;
      top: calc(100% - -12px);
      right: 0;
      width: 230px;
      z-index: 9999;
      background: $color-black;
      max-height: 0;
      opacity: 0;
      transition: all 0.3s linear;
      overflow: hidden;

      &.more-menu-modal--opened {
        max-height: 600px;
        opacity: 1;
      }

      .more-menu-nav {
        padding: 33px 0 35px;
        max-height: calc(100vh - 64px - 8px);
        display: flex;
        flex-direction: column;
        align-items: center;
        // overflow: auto;

        .more-menu-nav__item {
          display: flex;
          align-items: center;
          padding: 0 32px;
          font-family: $font-barlow-condensed;
          font-style: normal;
          font-weight: 500;
          font-size: 20px;
          line-height: 24px;
          letter-spacing: 2px;
          color: $color-white;
          text-transform: uppercase;

          &.more-menu-nav__item {
            margin-top: 28px;
          }

          &.more-menu-nav__item--language {
            cursor: pointer;

            .current-language {
              margin-left: 4px;
              margin-right: 10px;
            }

            .arrow,
            .icon {
              pointer-events: none;
            }

            .arrow {
              transition: all 0.3s linear;

              &.arrow--invert {
                transform: rotate(-180deg);
              }
            }
          }

          &.active {
            background-color: $color-white;
            color: $color-black;
            opacity: 1;
          }
        }
      }

      .current-language {
        white-space: nowrap;
      }

      .language-choices {
        margin-top: 14px;
        background-color: transparentize($color-white, 0.9);
        position: relative;
        width: auto;
      }

      .moving-dot--bottom {
        width: 120px;
        margin: 0 auto 35px;

        &::before {
          width: 100%;
        }
      }
    }
  }

  @media screen and (min-width: 1440px) {
    .more-menu {
      display: none;
    }
  }
}

body.lang-th {
  header .nav-container .more-menu {
    .more-menu-trigger .more-menu-trigger__label,
    .more-menu-modal .more-menu-nav .more-menu-nav__item {
      letter-spacing: normal !important;
      font-family: $font-noto-sans-thai !important;
    }
  }
}
