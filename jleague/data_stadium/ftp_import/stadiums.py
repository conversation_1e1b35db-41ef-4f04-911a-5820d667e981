import aioftp
import logging
import xml.etree.ElementTree as ET
from .helpers import (
    async_upsert,
    process_path_if_needed,
    read_file_to_str,
    xml_value,
)
from stadiums.models import Stadium, StadiumExtraInfo

logger = logging.getLogger(__name__)


async def import_stadiums(ftp_client: aioftp.Client, file_path: str, file_stat: dict):
    """Import stadiums from DS
    source file: master/mst_stadium.xml
    doc file:
    """
    async with process_path_if_needed(file_path, file_stat) as should_process:
        if not should_process:
            return

        # read XML file
        file_content = await read_file_to_str(ftp_client, file_path)
        root = ET.fromstring(file_content)

        # process stadiums
        for stadium_node in root.findall('StadiumMST/StadiumInfo'):
            id = xml_value(stadium_node, 'StadiumID', int)
            name = xml_value(stadium_node, 'StadiumName')
            name_short = xml_value(stadium_node, 'StadiumNameS')
            capacity = xml_value(stadium_node, 'Capacity', int)
            nationality = xml_value(stadium_node, 'NationalityName')
            lat = xml_value(stadium_node, 'Latitude', float)
            lon = xml_value(stadium_node, 'Longitude', float)

            # create or update stadium
            await async_upsert(
                Stadium,
                id=id,
                defaults={
                    'name': name,
                    'name_short': name_short,
                    'capacity': capacity,
                    'nationality': nationality,
                    'lat': lat,
                    'lon': lon,
                },
            )

            # create or update stadium extra info
            await async_upsert(StadiumExtraInfo, stadium_id=id, defaults={'title': name})

        logger.info('Processed stadiums')
