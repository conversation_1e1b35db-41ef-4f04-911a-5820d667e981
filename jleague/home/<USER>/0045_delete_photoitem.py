# Generated by Django 3.1.13 on 2022-04-28 09:02

from django.db import migrations


def clean_photoitem_pages(apps, schema_editor):
    Page = apps.get_model('wagtailcore', 'Page')
    PageRevision = apps.get_model('wagtailcore', 'PageRevision')

    # fetch child pages (/home/<USER>/*)
    photos_child_pages = Page.objects.filter(content_type_id=8)
    # delete child pages revision
    PageRevision.objects.filter(page__in=photos_child_pages).delete()
    # delete child pages
    photos_child_pages.delete()


def clean_photos_page(apps, schema_editor):
    Page = apps.get_model('wagtailcore', 'Page')
    PageRevision = apps.get_model('wagtailcore', 'PageRevision')

    # fetch parent page (/home/<USER>/)
    photos_page = Page.objects.filter(id=5)
    # delete parent page revision
    PageRevision.objects.filter(page=photos_page[0]).delete()
    # delete parent page
    photos_page.delete()


class Migration(migrations.Migration):
    dependencies = [
        ('wagtailredirects', '0006_redirect_increase_max_length'),
        ('wagtailforms', '0004_add_verbose_name_plural'),
        ('wagtailcore', '0066_collection_management_permissions'),
        ('home', '0044_remove_teaminfo_videos'),
        ('media', '0008_remove_video_date'),
    ]

    operations = [
        migrations.DeleteModel(
            name='PhotoItem',
        ),
        migrations.RunPython(clean_photoitem_pages),
        migrations.RunPython(clean_photos_page),
    ]
