@import 'setup/variable';

header .nav-container {
  .mobile-menu {
    .mobile-menu-trigger {
      padding: 8px;
      background: none;
      border: none;
      cursor: pointer;

      & > img {
        width: auto;
        height: 16px;

        &.lazy {
          min-height: 16px;
        }
      }
    }

    .mobile-menu-modal {
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background: $color-black;
      z-index: 9999;
      display: none;
      overflow: auto;

      @media screen and (max-width: 1120px) {
        display: block;
      }

      .logo-container {
        #mobile-menu-modal-close {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          padding: 8px;
          background: none;
          border: none;
          cursor: pointer;

          @media screen and (max-width: 1120px) and (min-width: 769px) {
            right: 24px;
          }

          @media screen and (max-width: 768px) {
            right: 16px;
          }
        }
      }

      .mobile-menu-nav {
        padding: 33px 0 35px;
        max-height: calc(100vh - 64px - 8px);
        overflow-x: hidden;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        align-items: center;

        .mobile-menu-nav__item {
          display: flex;
          font-family: $font-barlow-condensed;
          font-style: normal;
          font-weight: 500;
          font-size: 20px;
          line-height: 24px;
          letter-spacing: 2px;
          color: $color-white;
          text-align: center;
          text-transform: uppercase;

          & + .mobile-menu-nav__item {
            margin-top: 24px;
          }

          &.mobile-menu-nav__item--language {
            cursor: pointer;

            .current-language {
              margin-left: 4px;
              margin-right: 10px;
            }

            .arrow,
            .icon {
              pointer-events: none;
            }

            .arrow {
              transition: all 0.3s linear;

              &.arrow--invert {
                transform: rotate(-180deg);
              }
            }
          }

          &.active {
            background-color: $color-white;
            color: $color-black;
            opacity: 1;
          }
        }

        @media screen and (max-width: 1120px) {
          max-height: unset;
        }
      }

      .language-choices {
        margin-top: 14px;
        background-color: transparentize($color-white, 0.9);
        position: relative;
        width: 200px;
        overflow: hidden;
        transition: all 300ms ease-in-out;
      }

      .moving-dot--bottom {
        width: 120px;
        margin: 0 auto;

        &::before {
          width: 100%;
        }

        @media screen and (max-width: 1120px) {
          margin-bottom: 80px;
        }
      }
    }
  }
}

body.body--mobile-menu {
  .sidebar {
    display: none;
  }
}

body.lang-th {
  header .nav-container .mobile-menu .mobile-menu-modal .mobile-menu-nav .mobile-menu-nav__item {
    letter-spacing: normal !important;
    font-family: $font-noto-sans-thai !important;
  }
}
