class GoalHighlights {
  constructor(selector, swiperOptions = {}) {
    this.selector = selector;
    this.swiperOptions = swiperOptions;
    this.highlightVideoModalSelector = '.video-modal';
    this.highlightVideoModalBackdropSelector = '.video-modal__backdrop';
    this.highlightVideoModalCloseSelector = '.video-modal__close-button';
    this.highlightVideoModalAreaSelector = '.video-modal__content';
    this.highlightVideoPlayerWrapperSelector = '.highlight-video-wrapper';
    this.highlightVideoPlayerSelector = '.highlight-video-player';
    this.highlightVideoNavSelector = '.highlight-video-nav';
    this.goalHighlightWrapperSelector = '.goal-highlights'; // wrapper element
    this.goalHighlightVideosWrapperSelector = `${this.selector} .videos-list`; // videos container
    this.goalHighlightLimit = 6;
    this.goalHighlightReachEndGap = 3;
    this.goalHighlightCompetition = undefined;
    this.goalHighlightClub = undefined;
    this.goalHighlightPlayer = undefined;
    this.goalHighlightMatch = undefined;
    this.goalHighlightOffset = 0;
    this.goalHighlightIsFirstLoading = true;
    this.goalHighlightLastVideoReach = false;
    this.goalHighlightIsVideoModalOpened = false;
    this.goalHighlightVideos = {};
    this.goalHighlightVideoIds = [];
    this.goalHighlightCurrentVideoId = undefined;
    this.goalHighlightSwiper = undefined;
    this.goalHighlightVideoModal = undefined;
    this.goalHighlightVideoModalArea = undefined;
    this.goalHighlightVideoModalBackdrop = undefined;
    this.goalHighlightVideoModalClose = undefined;
    this.goalHighlightVideoPlayerWrapper = undefined;
    this.goalHighlightVideoPlayer = undefined;
    this.goalHighlightNextButton = undefined;
    this.goalHighlightPrevButton = undefined;
    this.goalHighlightVideoProgressInterval = undefined;
    this.goalHighlightVideoProgressCheckpoints = undefined;
    this.goalHighlightFetchVideosTimeout = undefined;
    this.goalHighlightIsBlocked = false;
    this.goalHighlightPreSelectVideoId = undefined;
    this.wrapperElement = undefined;
    this.mode = 'standalone';

    this.goalHighlightLikeButton = undefined;
    this.goalHighlightShareButton = undefined;
    this.highlightVideoLikeSelector = '.action-button--like';
    this.highlightVideoShareSelector = '.action-button--share';

    this.goalHighlightModalVideoNav = undefined;
    this.goalHighlightModalOffsetStart = 0;
    this.goalHighlightModalOffsetEnd = 0;
    this.goalHighlightModalVideoNavReachOldest = false;
    this.goalHighlightModalVideoNavReachLatest = false;

    this.goalHighlightUrlPath = '';

    this.initialize();
  }

  _goalHighightModalClose() {
    this.goalHighlightIsVideoModalOpened = false;
    this._destroyGoalHighightVideoPlayer();
    this.goalHighlightVideoModal.css('opacity', '0');
    this.goalHighlightVideoModal.css('visibility', 'hidden');
    $(document).off('keyup');
    enableBodyScroll();

    this._destroyModalVideoNav();
  }

  _goalHighightModalOpen() {
    this.goalHighlightIsVideoModalOpened = true;
    disableBodyScroll();
    $(document).on('keyup', (event) => {
      if (event.which === 27 && this.goalHighlightIsVideoModalOpened) {
        // on ESC pressed and modal opened
        this._goalHighightModalClose();
      }
    });
    this.goalHighlightVideoModal.css('visibility', 'visible');
    this.goalHighlightVideoModal.css('opacity', '1');
  }

  _initGoalHighightVideoProgressInterval(eventData) {
    this._clearGoalHighightVideoProgressInterval();
    this.goalHighlightVideoProgressInterval = setInterval(() => {
      const currentTime = this.goalHighlightVideoPlayer.currentTime();
      const duration = this.goalHighlightVideoPlayer.duration();
      eventData.duration = duration;
      eventData.currentTime = currentTime;
      // fire ga4 video progress event
      const progress = (currentTime / duration) * 100;
      if (progress >= 90 && !this.goalHighlightVideoProgressCheckpoints['90']) {
        this.goalHighlightVideoProgressCheckpoints['90'] = true;
        eventData.percent = 90;
        sendVideoProgressEvent(eventData);
      } else if (progress >= 75 && !this.goalHighlightVideoProgressCheckpoints['75']) {
        this.goalHighlightVideoProgressCheckpoints['75'] = true;
        eventData.percent = 75;
        sendVideoProgressEvent(eventData);
      } else if (progress >= 50 && !this.goalHighlightVideoProgressCheckpoints['50']) {
        this.goalHighlightVideoProgressCheckpoints['50'] = true;
        eventData.percent = 50;
        sendVideoProgressEvent(eventData);
      } else if (progress >= 25 && !this.goalHighlightVideoProgressCheckpoints['25']) {
        this.goalHighlightVideoProgressCheckpoints['25'] = true;
        eventData.percent = 25;
        sendVideoProgressEvent(eventData);
      } else if (progress >= 10 && !this.goalHighlightVideoProgressCheckpoints['10']) {
        this.goalHighlightVideoProgressCheckpoints['10'] = true;
        eventData.percent = 10;
        sendVideoProgressEvent(eventData);
      }
    }, 1000);
  }

  _clearGoalHighightVideoProgressInterval() {
    if (this.goalHighlightVideoProgressInterval) {
      clearInterval(this.goalHighlightVideoProgressInterval);
      this.goalHighlightVideoProgressInterval = null;
    }
  }

  _initGoalHighightVideoPlayer(video) {
    this.goalHighlightVideoProgressCheckpoints = {
      10: false,
      25: false,
      50: false,
      75: false,
      90: false,
    };
    let firstStart = true;
    let firstEnd = true;
    let playCount = 0;
    const eventData = {
      currentTime: null,
      duration: null,
      percent: null,
      provider: 'WSC',
      title: video.title,
      url: video.video_file,
    };

    const videoPlayer = this.goalHighlightVideoPlayerWrapper.find(this.highlightVideoPlayerSelector)[0];
    this.goalHighlightVideoPlayer = videojs(videoPlayer, {
      controls: true,
      autoplay: true,
      loop: false,
      muted: true,
      preload: 'auto',
      aspectRatio: '16:9',
      controlBar: { pictureInPictureToggle: false },
    });

    this.goalHighlightVideoPlayer.on('play', () => {
      if (firstStart) {
        firstStart = false;
        // fire ga4 video start event
        sendVideoStartEvent({
          ...eventData,
          currentTime: 0,
          duration: this.goalHighlightVideoPlayer.duration(),
          percent: 0,
        });
      }

      this._initGoalHighightVideoProgressInterval(eventData);
    });

    this.goalHighlightVideoPlayer.on('pause', () => {
      this._clearGoalHighightVideoProgressInterval();
    });

    this.goalHighlightVideoPlayer.on('ended', () => {
      playCount++;

      if (firstEnd) {
        firstEnd = false;
        // fire ga4 video complete event
        sendVideoCompleteEvent({
          ...eventData,
          currentTime: this.goalHighlightVideoPlayer.duration(),
          duration: this.goalHighlightVideoPlayer.duration(),
          percent: 100,
        });
      }

      if (playCount < 3) {
        this.goalHighlightVideoPlayer.play();
      }

      this._clearGoalHighightVideoProgressInterval();
    });
  }

  _destroyGoalHighightVideoPlayer() {
    if (typeof this.goalHighlightVideoPlayer !== 'undefined') {
      this.goalHighlightVideoPlayer.dispose();
      this.goalHighlightVideoPlayer = undefined;
    }
    this._clearGoalHighightVideoProgressInterval();
  }

  loadVideos(offset, cleanBeforeInsert, showLoadingIndicator = true) {
    const that = this;
    if (that.goalHighlightFetchVideosTimeout) {
      clearTimeout(that.goalHighlightFetchVideosTimeout);
    }

    that.goalHighlightFetchVideosTimeout = setTimeout(() => {
      if (!that.goalHighlightSwiper) {
        return;
      }

      if (cleanBeforeInsert) {
        // cleanup videos data
        that.goalHighlightVideos = {};
        that.goalHighlightVideoIds = [];
        that.goalHighlightOffset = 0;
        // reset checker flag
        that.goalHighlightIsFirstLoading = true;
        that.goalHighlightLastVideoReach = false;
      }

      const container = $(that.goalHighlightVideosWrapperSelector);
      let isFirst = false;
      if (that.goalHighlightIsFirstLoading) {
        isFirst = true;
        that.goalHighlightIsFirstLoading = false;

        if (showLoadingIndicator) {
          container.addClass('loading');
        }
      }

      let url = `/widgets/goal_highlights/`;
      const urlParams = urlSearchQuery({
        offset: offset,
        competition: that.goalHighlightCompetition,
        club: that.goalHighlightClub,
        player: that.goalHighlightPlayer,
        match: that.goalHighlightMatch,
        ts: new Date().getTime(),
      });
      url += urlParams;

      return ajaxRequest(url)
        .then((result) => {
          if (cleanBeforeInsert) {
            // cleanup swiper slides
            that.goalHighlightSwiper.removeAllSlides();
          }

          if (result.blocked) {
            that.goalHighlightIsBlocked = true;
            return;
          }

          const slides = result.html_events;
          const events = result.events;
          if (slides && slides.length) {
            that.goalHighlightSwiper.appendSlide(slides);
          }

          if (events && events.length) {
            for (const e of events) {
              if (e === 'null') {
                continue;
              }
              const event = JSON.parse(e);
              that.goalHighlightVideos[event.id] = event;
              that.goalHighlightVideoIds.push(event.id);
            }
          }

          if (!isFirst && events.length === 0) {
            // reach last video
            that.goalHighlightLastVideoReach = true;
          }

          if (isFirst) {
            if (showLoadingIndicator) {
              container.removeClass('loading');
            }
            that._toggleGoalHighlights();

            // check if preselect video exist
            if (events.length) {
              that._onGoalHighightVideoPreSelected();
            }
          }
        })
        .catch((error) => {
          console.error(error);
        });
    }, 500);
  }

  _loadVideo(id) {
    const that = this;

    if (that.goalHighlightIsBlocked || typeof id === 'undefined') return;

    that.goalHighlightVideoPlayerWrapper.addClass('loading');
    if (that.goalHighlightVideoModalClose) {
      that.goalHighlightVideoModalClose.css('opacity', '0');
    }

    // find offset current
    let index = that.goalHighlightVideoIds.indexOf(parseInt(id));
    let offset = Math.floor(index / that.goalHighlightLimit) * that.goalHighlightLimit;
    that.goalHighlightModalOffsetStart = offset;
    that.goalHighlightModalOffsetEnd = offset;

    // find index for set list selected
    index = offset === 0 ? index : index - offset;

    let url = `/widgets/goal_highlights/`;
    const urlParams = urlSearchQuery({
      offset: offset,
      competition: that.goalHighlightCompetition,
      club: that.goalHighlightClub,
      player: that.goalHighlightPlayer,
      match: that.goalHighlightMatch,
      event_id: id,
      ts: new Date().getTime(),
    });
    url += urlParams;

    return ajaxRequest(url)
      .then((result) => {
        if (result.blocked) return;

        if (that.goalHighlightVideoPlayerWrapper) {
          that.goalHighlightVideoPlayerWrapper.empty();
          that.goalHighlightVideoPlayerWrapper.append(result.html_events);

          const event = JSON.parse(result.event);
          that._initGoalHighightVideoPlayer(event);
          // fire ga4 select content event
          sendSelectContentEvent('goal_highlight_video', event.title);

          // init button
          that._initLikeButton(result);
          that._initShareButton(result);
          that._initModalVideoNav(index, result.html_item_list);

          // get element close-button
          that.goalHighlightVideoModalClose = that.goalHighlightVideoPlayerWrapper.find(
            that.highlightVideoModalCloseSelector,
          );
          that.goalHighlightVideoModalClose.click(that._goalHighightModalClose.bind(that));
        }

        that.goalHighlightVideoPlayerWrapper.removeClass('loading');
      })
      .catch((error) => {
        console.error(error);
      });
  }

  _loadVideoModal(id, elementSelected) {
    const url = `/widgets/goal_highlights/${id}/`;
    return ajaxRequest(url)
      .then((result) => {
        // remove old element and add new element
        const highlightVideo = this.goalHighlightVideoPlayerWrapper.find('.highlight-video');
        const highlightContainer = document.querySelector('.highlight-video__container');
        highlightContainer.remove();
        highlightVideo.append(result.html_event);

        // remove active class on the previous nav item
        elementSelected.parent().find('.video-item--active').removeClass('video-item--active');
        // add active class to the current nav item
        elementSelected.addClass('video-item--active');

        // init button
        this._initLikeButton(result);
        this._initShareButton(result);

        const eventPlayer = JSON.parse(result.event);
        this._initGoalHighightVideoPlayer(eventPlayer);
      })
      .catch((error) => {
        console.error(error);
      });
  }

  _onGoalHighightReachEnd() {
    if (this.goalHighlightIsFirstLoading || this.goalHighlightLastVideoReach) {
      return;
    }
    this.goalHighlightOffset = this.goalHighlightOffset + this.goalHighlightLimit;
    this.loadVideos(this.goalHighlightOffset);
  }

  _onGoalHighightVideoSelected(swiper, event) {
    event.stopPropagation();

    // Check if the click event is within the video item container
    const clickedElement = $(event.target);
    const isWithinVideoItem = clickedElement.closest('.video-item').length > 0;

    if (isWithinVideoItem) {
      // prepare share data for share button
      const slide = clickedElement.closest('.video-item').data();
      this.goalHighlightCurrentVideoId = slide.vid;
      this.goalHighlightUrlPath = slide.url;

      // display selected video modal
      this._goalHighightModalOpen();
      this._loadVideo(this.goalHighlightCurrentVideoId);

      // fire ga4 element click event
      sendElementClickEvent(`match_video_event_${this.goalHighlightCurrentVideoId}`, slide.title, 'video-item');
    }
  }

  _onGoalHighightVideoPreSelected() {
    if (!this.goalHighlightPreSelectVideoId) return;

    // prepare share data for share button
    const slide = $(`.video-events .video-item[data-vid=${this.goalHighlightPreSelectVideoId}]`).data();
    this.goalHighlightCurrentVideoId = slide.vid;
    this.goalHighlightUrlPath = slide.url;

    // display pre-selected video modal
    this._goalHighightModalOpen();
    this._loadVideo(this.goalHighlightPreSelectVideoId);

    // fire ga4 element click event
    console.log(
      `match_video_event_${this.goalHighlightPreSelectVideoId}`,
      slide.title,
      'video-item',
      this.goalHighlightUrlPath,
    );
    sendViewShareElementEvent(
      `match_video_event_${this.goalHighlightPreSelectVideoId}`,
      slide.title,
      'video-item',
      this.goalHighlightUrlPath,
    );
  }

  _toggleGoalHighlights() {
    const element = this.mode === 'sidebar' ? this.wrapperElement : this.wrapperElement.parent();
    if (this.goalHighlightVideoIds.length === 0 || this.goalHighlightIsBlocked) {
      element.addClass('d-none');
    } else {
      element.removeClass('d-none');
    }
  }

  _getElements() {
    // elements
    this.wrapperElement = $(this.goalHighlightWrapperSelector);
    this.goalHighlightVideoModal = $(this.highlightVideoModalSelector);
    this.goalHighlightVideoModalBackdrop = this.goalHighlightVideoModal.find(this.highlightVideoModalBackdropSelector);
    this.goalHighlightVideoModalArea = this.goalHighlightVideoModal.find(this.highlightVideoModalAreaSelector);
    this.goalHighlightVideoPlayerWrapper = this.goalHighlightVideoModalArea.find(
      this.highlightVideoPlayerWrapperSelector,
    );

    // click handler
    this.goalHighlightVideoModalBackdrop.click(this._goalHighightModalClose.bind(this));
  }

  _onLikeButtonClick() {
    sendElementClickEvent(
      'goal-highlight-like-button',
      this.goalHighlightCurrentVideoId,
      this.highlightVideoLikeSelector,
    );

    const likeButton = this.goalHighlightLikeButton;
    const likeStatus = likeButton.hasClass('action-button--highlight') ? 'unlike' : 'like';
    const textElm = likeButton.find('.action-button__text');
    const payload = { status_like: likeStatus };
    const url = `/widgets/goal_highlights/${this.goalHighlightCurrentVideoId}/like/`;

    return ajaxRequest(url, payload, 'POST')
      .then(function (response) {
        if (likeStatus === 'like') {
          likeButton.addClass('action-button--highlight');
        } else {
          likeButton.removeClass('action-button--highlight');
        }

        if (typeof response.like_count === 'number' && response.like_count > 0) {
          textElm.html(response.like_count);
        } else {
          textElm.html(0);
        }
      })
      .catch((error) => {
        console.error(error);
      });
  }

  _initLikeButton(data) {
    this.goalHighlightLikeButton = this.goalHighlightVideoPlayerWrapper.find(this.highlightVideoLikeSelector);

    // update like icon
    if (data.like_status) {
      this.goalHighlightLikeButton.addClass('action-button--highlight');
    }

    // update total likes
    if (typeof data.like_count === 'number' && data.like_count > 0) {
      const totalLike = this.goalHighlightLikeButton.find('.action-button__text');
      if (totalLike) {
        totalLike.html(data.like_count);
      }
    }

    // bind click event
    this.goalHighlightLikeButton.click(() => this._onLikeButtonClick());
  }

  _onShareButtonClick() {
    sendElementClickEvent(
      'goal-highlight-share-button',
      this.goalHighlightCurrentVideoId,
      this.highlightVideoShareSelector,
    );

    if (this.goalHighlightUrlPath === '') return;

    const url = new URL(this.goalHighlightUrlPath, window.location.origin);
    url.searchParams.append('vid', this.goalHighlightCurrentVideoId);

    const copiedElement = this.goalHighlightShareButton.find('.action-button__overlay-text');

    navigator.clipboard
      .writeText(url.href)
      .then(function () {
        copiedElement.css('display', 'block');

        setTimeout(() => {
          copiedElement.css('display', 'none');
        }, 1000);
      })
      .catch((error) => {
        console.error(error);
      });
  }

  _initShareButton(data) {
    this.goalHighlightShareButton = this.goalHighlightVideoPlayerWrapper.find(this.highlightVideoShareSelector);
    this.goalHighlightShareButton.click(() => this._onShareButtonClick());
  }

  _onVideoNavReachBeginning(swiper) {
    if (!this.goalHighlightModalVideoNavReachLatest) {
      this.goalHighlightModalOffsetStart =
        Math.floor(this.goalHighlightModalOffsetStart / this.goalHighlightLimit - 1) * this.goalHighlightLimit;
      // check if the latest video was reached
      if (this.goalHighlightModalOffsetStart < 0) {
        this.goalHighlightModalOffsetStart = 0;
        this.goalHighlightModalVideoNavReachLatest = true;
        return;
      }
      this._loadNavVideos(swiper, this.goalHighlightModalOffsetStart, true);
    }
  }

  _onVideoNavReachEnd(swiper) {
    if (!this.goalHighlightModalVideoNavReachOldest) {
      this.goalHighlightModalOffsetEnd =
        Math.floor(this.goalHighlightModalOffsetEnd / this.goalHighlightLimit + 1) * this.goalHighlightLimit;
      this._loadNavVideos(swiper, this.goalHighlightModalOffsetEnd);
    }
  }

  _onVideoNavSelected(swiper, event) {
    event.stopPropagation();
    if (typeof swiper.clickedSlide === 'undefined') return;

    // display selected video
    const slide = $(swiper.clickedSlide);
    const slideData = slide.data();

    if (this.goalHighlightCurrentVideoId !== slideData.vid) {
      this.goalHighlightCurrentVideoId = slideData.vid;
      this.goalHighlightUrlPath = slideData.url;
      swiper.slideTo(swiper.clickedIndex);

      this._loadVideoModal(this.goalHighlightCurrentVideoId, slide);

      // fire ga4 element click event
      sendElementClickEvent(`match_video_event_${this.goalHighlightCurrentVideoId}`, slideData.title, 'video-item');
    }
  }

  _loadNavVideos(swiper, offset, isLatest = false) {
    if (!swiper) return;

    setTimeout(() => {
      let url = `/widgets/goal_highlights/`;
      const urlParams = urlSearchQuery({
        offset: offset,
        competition: this.goalHighlightCompetition,
        club: this.goalHighlightClub,
        player: this.goalHighlightPlayer,
        match: this.goalHighlightMatch,
        reach: true,
        ts: new Date().getTime(),
      });
      url += urlParams;

      return ajaxRequest(url)
        .then((result) => {
          if (result.blocked) return;

          const slides = result.html_item_list;
          if (slides && slides.length) {
            if (isLatest) {
              this.goalHighlightModalVideoNavReachLatest = false;
              swiper.prependSlide(slides.reverse());
            } else {
              this.goalHighlightModalVideoNavReachOldest = false;
              swiper.appendSlide(slides);
            }
          } else {
            if (isLatest) {
              this.goalHighlightModalVideoNavReachLatest = true;
            } else {
              this.goalHighlightModalVideoNavReachOldest = true;
            }
          }
        })
        .catch((error) => {
          console.error(error);
        });
    }, 500);
  }

  _initModalVideoNav(index, slides) {
    if (slides && slides.length) {
      this.goalHighlightModalVideoNav = new Swiper(this.highlightVideoNavSelector, {
        slidesPerView: 'auto',
        spaceBetween: 16,
        freeMode: true,
        mousewheel: true,
        centeredSlides: true,
        centeredSlidesBounds: true,
        on: {
          reachBeginning: this._onVideoNavReachBeginning.bind(this),
          reachEnd: this._onVideoNavReachEnd.bind(this),
          tap: this._onVideoNavSelected.bind(this),
        },
      });

      this.goalHighlightModalVideoNav.appendSlide(slides);

      if (index > 0) {
        this.goalHighlightModalVideoNav.slideTo(index);
      }
    }
  }

  _destroyModalVideoNav() {
    if (this.goalHighlightModalVideoNav) {
      this.goalHighlightModalVideoNav.destroy();
      this.goalHighlightModalVideoNavReachOldest = false;
      this.goalHighlightModalVideoNavReachLatest = false;
    }
  }

  initialize() {
    this.goalHighlightSwiper = new Swiper(this.selector, {
      slidesPerView: 'auto',
      spaceBetween: 30,
      freeMode: true,
      mousewheel: true,
      ...this.swiperOptions,
      on: {
        reachEnd: this._onGoalHighightReachEnd.bind(this),
        tap: this._onGoalHighightVideoSelected.bind(this),
      },
    });

    this._getElements();
  }

  destroy() {
    if (this.goalHighlightSwiper) {
      this.goalHighlightSwiper.destroy();
    }
  }

  setMode(mode) {
    this.mode = mode;
  }

  setClub(id) {
    this.goalHighlightClub = id;
  }

  setPlayer(id) {
    this.goalHighlightPlayer = id;
  }

  setMatch(id) {
    this.goalHighlightMatch = id;
  }

  setCompetition(slug) {
    this.goalHighlightCompetition = slug;
  }

  setPreSelectVideoId(videoId) {
    this.goalHighlightPreSelectVideoId = videoId;
  }

  setWrapperSelector(selector) {
    this.goalHighlightWrapperSelector = selector;
    this._getElements();
  }

  setVideoWrapperSelector(selector) {
    this.goalHighlightVideosWrapperSelector = selector;
  }
}
