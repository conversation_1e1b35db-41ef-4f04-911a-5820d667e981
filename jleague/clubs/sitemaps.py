from django.contrib.sitemaps import Sitemap
from django.urls import reverse
from .helper import get_competition_clubs


class ClubsViewSitemap(Sitemap):
    changefreq = 'monthly'
    priority = 0.6
    protocol = 'https'

    def items(self):
        return ['clubs']

    def location(self, item):
        return reverse(item)


class ClubProfileViewSitemap(Sitemap):
    changefreq = 'monthly'
    priority = 0.7
    protocol = 'https'

    def items(self):
        j1_teams = get_competition_clubs('j1')
        j2_teams = get_competition_clubs('j2')
        j3_teams = get_competition_clubs('j3')

        teams = []
        for team in j1_teams + j2_teams + j3_teams:
            teams.append(team.get_name_slug())
        return teams

    def location(self, item):
        return reverse('club_profile', args=[item])


sitemaps = {
    'clubs': ClubsViewSitemap,
    'club_profile': ClubProfileViewSitemap,
}
