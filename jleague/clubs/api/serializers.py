from rest_framework import serializers
from clubs.models import Club, ClubCompetition, ClubExtraInfo, ClubPlayer


class ClubSerializer(serializers.ModelSerializer):
    is_jleague = serializers.BooleanField()

    class Meta:
        model = Club
        fields = "__all__"


class ClubCompetitionSerializer(serializers.ModelSerializer):
    class Meta:
        model = ClubCompetition
        fields = "__all__"


class ClubExtraInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = ClubExtraInfo
        fields = "__all__"


class ClubPlayerSerializer(serializers.ModelSerializer):
    class Meta:
        model = ClubPlayer
        fields = "__all__"
