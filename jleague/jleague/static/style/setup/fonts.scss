/*
  Avenir Next Condensed
*/
@font-face {
  font-family: 'AvenirNextCondensed';
  font-display: swap;
  font-weight: 200;
  src: url('/static/fonts/AvenirNextCondensed-UltraLight.ttf') format('truetype');
}

@font-face {
  font-family: 'AvenirNextCondensed';
  font-display: swap;
  font-weight: 400;
  src: url('/static/fonts/AvenirNextCondensed-Regular.ttf') format('truetype');
}

@font-face {
  font-family: 'AvenirNextCondensed';
  font-display: swap;
  font-weight: 500;
  src: url('/static/fonts/AvenirNextCondensed-Medium.ttf') format('truetype');
}

@font-face {
  font-family: 'AvenirNextCondensed';
  font-display: swap;
  font-weight: 600;
  src: url('/static/fonts/AvenirNextCondensed-DemiBold.ttf') format('truetype');
}

@font-face {
  font-family: 'AvenirNextCondensed';
  font-display: swap;
  font-weight: 700;
  src: url('/static/fonts/AvenirNextCondensed-Bold.ttf') format('truetype');
}

@font-face {
  font-family: 'AvenirNextCondensed';
  font-display: swap;
  font-weight: 800;
  src: url('/static/fonts/AvenirNextCondensed-Heavy.ttf') format('truetype');
}

/*
  Barlow Condensed
*/
@font-face {
  font-family: 'Barlow Condensed';
  src: url('/static/fonts/BarlowCondensed-Thin.ttf') format('truetype');
  font-weight: 100;
  font-display: swap;
}

@font-face {
  font-family: 'Barlow Condensed';
  src: url('/static/fonts/BarlowCondensed-ExtraLight.ttf') format('truetype');
  font-weight: 200;
  font-display: swap;
}

@font-face {
  font-family: 'Barlow Condensed';
  src: url('/static/fonts/BarlowCondensed-Light.ttf') format('truetype');
  font-weight: 300;
  font-display: swap;
}

@font-face {
  font-family: 'Barlow Condensed';
  src: url('/static/fonts/BarlowCondensed-Regular.ttf') format('truetype');
  font-weight: 400;
  font-display: swap;
}

@font-face {
  font-family: 'Barlow Condensed';
  src: url('/static/fonts/BarlowCondensed-Medium.ttf') format('truetype');
  font-weight: 500;
  font-display: swap;
}

@font-face {
  font-family: 'Barlow Condensed';
  src: url('/static/fonts/BarlowCondensed-SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-display: swap;
}

@font-face {
  font-family: 'Barlow Condensed';
  src: url('/static/fonts/BarlowCondensed-Bold.ttf') format('truetype');
  font-weight: 700;
  font-display: swap;
}

@font-face {
  font-family: 'Barlow Condensed';
  src: url('/static/fonts/BarlowCondensed-ExtraBold.ttf') format('truetype');
  font-weight: 800;
  font-display: swap;
}

@font-face {
  font-family: 'Barlow Condensed';
  src: url('/static/fonts/BarlowCondensed-Black.ttf') format('truetype');
  font-weight: 900;
  font-display: swap;
}

/*
  Druk Wide
*/
@font-face {
  font-family: 'Druk Wide';
  font-display: swap;
  font-style: normal;
  font-weight: 900;
  src: url('/static/fonts/DrukWide-Heavy.otf') format('opentype');
}

@font-face {
  font-family: 'Druk Wide';
  font-display: swap;
  font-style: normal;
  font-weight: 700;
  src: url('/static/fonts/DrukWide-Bold.otf') format('opentype');
}

@font-face {
  font-family: 'Druk Wide';
  font-display: swap;
  font-style: normal;
  font-weight: 500;
  src: url('/static/fonts/DrukWide-Medium.otf') format('opentype');
}

/*
  JLEAGUE KICK
*/
@font-face {
  font-family: 'JLEAGUE KICK';
  font-display: swap;
  src: url('/static/fonts/JLEAGUEKICK-BoldCondensed.otf') format('opentype');
}

/*
  Overpass
*/
@font-face {
  font-family: 'Overpass';
  src: url('/static/fonts/overpass-regular.ttf') format('truetype');
  font-weight: 400;
  font-display: swap;
}

@font-face {
  font-family: 'Overpass';
  src: url('/static/fonts/overpass-bold.ttf') format('truetype');
  font-weight: 700;
  font-display: swap;
}

@font-face {
  font-family: 'Overpass';
  src: url('/static/fonts/overpass-extraBold.ttf') format('truetype');
  font-weight: 800;
  font-display: swap;
}

@font-face {
  font-family: 'Overpass';
  src: url('/static/fonts/overpass-heavy.ttf') format('truetype');
  font-weight: 900;
  font-display: swap;
}

@font-face {
  font-family: 'Notosansthai';
  src: url('/static/fonts/NotoSansThai-Black.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Notosansthai';
  src: url('/static/fonts/NotoSansThai-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Notosansthai';
  src: url('/static/fonts/NotoSansThai-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
