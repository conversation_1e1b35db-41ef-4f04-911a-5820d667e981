# Generated by Django 3.1.13 on 2022-07-27 09:25

from django.db import migrations

_partner_rows = [
    {
        'name': 'J.League Title Partner',
        'row': 1,
        'is_active': True,
        'headers': [{'slug': 'jleague-title', 'sort_order': 0}],
        'partners': [
            {
                'name': 'MEIJI YASUDA',
                'slug': 'meiji-yasuda',
                'website': 'https://www.meijiyasuda.co.jp',
                'is_active': True,
                'sort_order': 0,
            }
        ],
    },
    {
        'name': 'J.League Official Broadcasting Partner',
        'row': 2,
        'is_active': True,
        'headers': [{'slug': 'jleague-official', 'sort_order': 0}, {'slug': 'jleague-broadcast', 'sort_order': 1}],
        'partners': [
            {'name': 'DAZN', 'slug': 'dazn', 'website': 'https://www.dazn.com', 'is_active': True, 'sort_order': 0}
        ],
    },
    {
        'name': 'J.League Top Partners',
        'row': 3,
        'is_active': True,
        'headers': [{'slug': 'jleague-top', 'sort_order': 0}],
        'partners': [
            {
                'name': 'Aidem',
                'slug': 'aidem',
                'website': 'https://www.aidem.co.jp/',
                'is_active': True,
                'sort_order': 0,
            },
            {
                'name': 'Route Inn',
                'slug': 'route-inn',
                'website': 'https://www.route-inn.co.jp/',
                'is_active': True,
                'sort_order': 1,
            },
            {
                'name': 'Aeon',
                'slug': 'aeon',
                'website': 'https://www.aeonretail.jp/',
                'is_active': True,
                'sort_order': 2,
            },
            {
                'name': 'Docomo',
                'slug': 'docomo',
                'website': 'https://www.nttdocomo.co.jp/english/',
                'is_active': True,
                'sort_order': 3,
            },
            {
                'name': 'Konami',
                'slug': 'konami',
                'website': 'https://www.konami.com/games/us/en/',
                'is_active': True,
                'sort_order': 4,
            },
            {
                'name': 'Ichigo',
                'slug': 'ichigo',
                'website': 'https://www.ichigo.gr.jp/en',
                'is_active': True,
                'sort_order': 5,
            },
        ],
    },
    {
        'name': 'J.League 100 Year Vision Partner',
        'row': 4,
        'is_active': True,
        'headers': [
            {'slug': 'jleague-100-year-vision', 'sort_order': 0},
        ],
        'partners': [
            {
                'name': 'Asahi Shimbun',
                'slug': 'asahi-shimbun',
                'website': 'https://www.asahi.com/corporate/english/',
                'is_active': True,
                'sort_order': 0,
            }
        ],
    },
    {
        'name': 'J.League Cup Partner',
        'row': 4,
        'is_active': True,
        'headers': [
            {'slug': 'league-cup', 'sort_order': 0},
        ],
        'partners': [
            {
                'name': 'Yamazaki',
                'slug': 'yamazaki',
                'website': 'https://www.yamazaki-biscuits.co.jp/',
                'is_active': True,
                'sort_order': 0,
            }
        ],
    },
    {
        'name': 'Super Cup Partner',
        'row': 4,
        'is_active': True,
        'headers': [
            {'slug': 'super-cup', 'sort_order': 0},
        ],
        'partners': [
            {
                'name': 'Fuji Film',
                'slug': 'fuji-film',
                'website': 'https://www.fujifilm.com/fbglobal/eng',
                'is_active': True,
                'sort_order': 0,
            }
        ],
    },
    {
        'name': 'J.League Cup Partner',
        'row': 4,
        'is_active': True,
        'headers': [
            {'slug': 'jleague-equipment', 'sort_order': 0},
        ],
        'partners': [
            {
                'name': 'Adidas',
                'slug': 'adidas',
                'website': 'https://shop.adidas.jp/football/',
                'is_active': True,
                'sort_order': 0,
            }
        ],
    },
    {
        'name': 'Sports Promotion Partner',
        'row': 4,
        'is_active': True,
        'headers': [
            {'slug': 'sports-promotion', 'sort_order': 0},
        ],
        'partners': [
            {
                'name': 'Japan Sports Council',
                'slug': 'japan-sports-council',
                'website': None,
                'is_active': True,
                'sort_order': 0,
            }
        ],
    },
    {
        'name': 'J.League Official Ticketing Partner',
        'row': 4,
        'is_active': True,
        'headers': [
            {'slug': 'jleague-ticketing', 'sort_order': 0},
        ],
        'partners': [
            {'name': 'PIA', 'slug': 'pia', 'website': 'https://corporate.pia.jp/', 'is_active': True, 'sort_order': 0}
        ],
    },
    {
        'name': 'J.League Official Technology Partner',
        'row': 4,
        'is_active': True,
        'headers': [
            {'slug': 'jleague-technology', 'sort_order': 0},
        ],
        'partners': [
            {
                'name': 'Rakuten',
                'slug': 'rakuten',
                'website': 'https://global.rakuten.com/corp/',
                'is_active': True,
                'sort_order': 0,
            }
        ],
    },
    {
        'name': 'J.League Official EC Platform Partner',
        'row': 4,
        'is_active': True,
        'headers': [
            {'slug': 'jleague-ec-platform', 'sort_order': 0},
        ],
        'partners': [
            {
                'name': 'NTT Group',
                'slug': 'ntt-group',
                'website': 'https://group.ntt',
                'is_active': True,
                'sort_order': 0,
            }
        ],
    },
    {
        'name': 'J.League Supporting Companies',
        'row': 5,
        'is_active': True,
        'headers': [
            {'slug': 'jleague-supporting-companies', 'sort_order': 0},
        ],
        'partners': [
            {
                'name': 'Yahoo',
                'slug': 'yahoo',
                'website': 'https://www.yahoo.co.jp/',
                'is_active': True,
                'sort_order': 0,
            },
            {
                'name': 'Deloitte',
                'slug': 'deloitte',
                'website': 'https://www2.deloitte.com',
                'is_active': True,
                'sort_order': 1,
            },
            {
                'name': 'Data Stadium',
                'slug': 'data-stadium',
                'website': 'https://www.datastadium.co.jp',
                'is_active': True,
                'sort_order': 2,
            },
            {
                'name': 'Imagica Group',
                'slug': 'imagica-group',
                'website': 'https://www.imagicagroup.co.jp',
                'is_active': True,
                'sort_order': 3,
            },
            {'name': 'LINE', 'slug': 'line', 'website': 'https://linecorp.com', 'is_active': True, 'sort_order': 4},
            {
                'name': 'Suntory Wellness',
                'slug': 'suntory-wellness',
                'website': 'https://www.suntory-kenko.com/company/',
                'is_active': True,
                'sort_order': 5,
            },
            {
                'name': 'Tiktok',
                'slug': 'tiktok',
                'website': 'https://www.tiktok.com/@jleagueintl',
                'is_active': True,
                'sort_order': 6,
            },
        ],
    },
]


def _create_group(model, data):
    return model.objects.create(
        name=data['name'],
        is_active=data['is_active'],
        row=data['row'],
    )


def _create_group_header(model, data, group):
    return model.objects.create(
        partner_group_id=group.id,
        slug=data['slug'],
        sort_order=data['sort_order'],
    )


def _create_partner(model, data, group):
    return model.objects.create(
        partner_group_id=group.id,
        name=data['name'],
        slug=data['slug'],
        website=data['website'],
        is_active=data['is_active'],
        sort_order=data['sort_order'],
    )


def insert_partners(apps, schema_editor):
    Partner = apps.get_model('core', 'Partner')
    PartnerGroup = apps.get_model('core', 'PartnerGroup')
    PartnerGroupHeader = apps.get_model('core', 'PartnerGroupHeader')

    for row in _partner_rows:
        group = _create_group(PartnerGroup, row)

        for header in row['headers']:
            _create_group_header(PartnerGroupHeader, header, group)

        for partner in row['partners']:
            _create_partner(Partner, partner, group)


class Migration(migrations.Migration):
    dependencies = [
        ('core', '0010_partner_partnergroup_partnergroupheader'),
    ]

    operations = [
        migrations.RunPython(insert_partners),
    ]
