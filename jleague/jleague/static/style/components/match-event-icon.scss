.match-event-icon {
  position: relative;
  flex: 0 0 auto;
  display: inline-block;
  width: 24px;
  height: 24px;
  margin-right: 8px;
  margin-left: 8px;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100%;
  }

  &.match-event-icon--goal::after {
    background-image: url('/static/images/match-details/goal.svg');
  }

  &.match-event-icon--penalty-goal::after {
    background-image: url('/static/images/match-details/penalty-goal.svg');
  }

  &.match-event-icon--penalty-goal-failed::after {
    background-image: url('/static/images/match-details/penalty-goal-failed.svg');
  }

  &.match-event-icon--own-goal::after {
    background-image: url('/static/images/match-details/own-goal.svg');
  }

  &.match-event-icon--in-out::after {
    background-image: url('/static/images/match-details/in-out.svg');
  }

  &.match-event-icon--red::after {
    background-image: url('/static/images/match-details/card-red.svg');
  }

  &.match-event-icon--yellow::after {
    background-image: url('/static/images/match-details/card-yellow.svg');
  }

  &.match-event-icon--yellow-red::after {
    background-image: url('/static/images/match-details/card-yellow-red.svg');
  }

  &.match-event-icon--white {
    &.match-event-icon--goal::after {
      background-image: url('/static/images/match-details/goal-white.svg');
    }

    &.match-event-icon--penalty-goal::after {
      background-image: url('/static/images/match-details/penalty-goal-white.svg');
    }

    &.match-event-icon--penalty-goal-failed::after {
      background-image: url('/static/images/match-details/penalty-goal-failed-white.svg');
    }

    &.match-event-icon--own-goal::after {
      background-image: url('/static/images/match-details/own-goal-white.svg');
    }
  }

  @media screen and (max-width: 1199px) {
    width: 16px;
    height: 16px;
  }
}
