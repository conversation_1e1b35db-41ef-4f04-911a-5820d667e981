{% load core_tags %}

<div class="club-selector club-selector--closed">
  <div class="club-selector__current">
    <div class="team-bg team-bg--{{ club.slug }}"></div>

    <h1 class="team-text team-text--{{ club.slug }}">
      {% club_name club %}
    </h1>

    <div class="arrow-down">
      <svg
        class="team-svg-color team-svg-color--{{ club.slug }}"
        width="16"
        height="10"
        viewBox="0 0 16 10"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M14.4527 0.773002L8 7.42771L1.54726 0.773002C1.19295 0.408999 0.619358 0.408999 0.265051 0.773002C-0.0883502 1.14634 -0.0883502 1.73434 0.265051 2.09834L7.20439 9.24772C7.42097 9.47172 7.71819 9.53705 8 9.48105C8.28181 9.53705 8.57903 9.47172 8.79651 9.24772L15.7349 2.09834C16.0884 1.73434 16.0884 1.14634 15.7349 0.773002C15.3806 0.408999 14.807 0.408999 14.4527 0.773002Z"
          fill="#1A1919"
        />
      </svg>
    </div>
  </div>

  <div class="club-selector__dropdown">
    {% for league in club_leagues %}
      <div class="dropdown-group">
        <div class="dropdown-group__header">
          {% include 'components/competition-logo.html' with slug=league.competition.slug size='xsmall' %}
          <span class="league-name">{% translation_obj league.competition.t_name %}</span>
        </div>

        <div class="dropdown-group__body">
          {% for league_club in league.clubs %}
            <a
              class="dropdown-item{% if league_club.slug == club.slug %} dropdown-item--active{% endif %}"
              href="{% url 'club_profile' league_club.name_slug %}"
              onclick="sendSelectContentEvent('club', '{{ league_club.slug }}');"
            >
              <div class="team-bg team-bg--{{ league_club.slug }}"></div>

              {% if league_club.slug == "tokushima" %}
              {% include 'components/club-emblem.html' with slug=league_club.slug use_alt=False custom_class="club-emblem club-emblem--tokushima" %}
              {% include 'components/club-emblem.html' with slug=league_club.slug use_alt=True custom_class="club-emblem club-emblem--tokushima-alt" %}
              {% elif league_club.slug == "tokyov" %}
              {% include 'components/club-emblem.html' with slug=league_club.slug use_alt=False custom_class="club-emblem club-emblem--tokyov" %}
              {% include 'components/club-emblem.html' with slug=league_club.slug use_alt=True custom_class="club-emblem club-emblem--tokyov-alt" %}
            {% else %}
              {% include 'components/club-emblem.html' with slug=league_club.slug use_alt=True custom_class="club-emblem" %}
            {% endif %}

              <span class="team-name team-text team-text--{{ league_club.slug }}">
                {% club_name league_club %}
              </span>
            </a>
          {% endfor %}
        </div>
      </div>
    {% endfor %}
  </div>
</div>
