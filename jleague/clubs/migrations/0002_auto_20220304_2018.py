# Generated by Django 3.1.13 on 2022-03-04 11:18

from django.db import migrations, models
import django.db.models.deletion
import modelcluster.fields


class Migration(migrations.Migration):
    dependencies = [
        ('media', '0005_auto_20220302_1712'),
        ('clubs', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='clubextrainfo',
            name='fk_team',
        ),
        migrations.RemoveField(
            model_name='clubvideo',
            name='team_extra_info',
        ),
        migrations.AddField(
            model_name='clubvideo',
            name='club_extra_info',
            field=modelcluster.fields.ParentalKey(
                default=None,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='club_videos',
                to='clubs.clubextrainfo',
            ),
        ),
        migrations.AddField(
            model_name='clubvideo',
            name='description',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AlterField(
            model_name='clubvideo',
            name='video',
            field=models.ForeignKey(
                default=None, on_delete=django.db.models.deletion.CASCADE, related_name='+', to='media.video'
            ),
        ),
    ]
