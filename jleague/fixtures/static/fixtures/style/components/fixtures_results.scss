@import 'setup/variable';
@import 'setup/mixin';

.fixture-results {
  margin-top: 40px;

  &+.fixture-results {
    margin-top: 80px;
  }

  .match-list-container {
    &>.divider {
      height: 4px;
      border-radius: 1px;
    }

    .match-list-header {
      height: 88px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .league-container {
        display: flex;
        align-items: center;

        .competition-name {
          color: var(--color-black);
          font-family: var(--font-overpass);
          font-style: normal;
          font-weight: 900;
          font-size: 28px;
          line-height: 32px;
          margin-left: 16px;
        }
      }

      .nav-container {
        display: flex;
        align-items: center;

        .nav-matchweek {
          font-family: var(--font-barlow-condensed);
          font-style: normal;
          font-weight: 700;
          font-size: 16px;
          line-height: 20px;

          &.nav-matchweek--previous,
          &.nav-matchweek--next {
            height: 40px;
            padding: 0 16px;
            letter-spacing: 1.67px;
            text-transform: uppercase;
            color: var(--color-black);
            white-space: nowrap;
            display: flex;
            align-items: center;

            &:hover {
              background: var(--color-red);
              border-radius: 40px;
              color: var(--color-white);
              opacity: 1;
            }
          }

          &.nav-matchweek--previous {
            &::before {
              content: '';
              width: 12px;
              height: 12px;
              margin-right: 14px;
              background-image: url('/static/images/icons/arrow-right-1A1919.svg');
              background-repeat: no-repeat;
              background-size: contain;
              background-position: center;
              transform: rotate(180deg);

              &:hover {
                background-image: url('/static/images/icons/arrow-right-FFFFFF.svg');
              }
            }
          }

          &.nav-matchweek--next {
            &::after {
              content: '';
              width: 12px;
              height: 12px;
              margin-left: 14px;
              background-image: url('/static/images/icons/arrow-right-1A1919.svg');
              background-repeat: no-repeat;
              background-size: contain;
              background-position: center;

              &:hover {
                background-image: url('/static/images/icons/arrow-right-FFFFFF.svg');
              }
            }
          }

          &.nav-matchweek--current {
            color: var(--color-earth-4);
            white-space: nowrap;
            letter-spacing: 1.66667px;
            text-transform: uppercase;
            padding: 0 16px;
          }
        }
      }

      .league-container+.nav-container {
        margin-top: 8px;
      }
    }

    &+.match-list-container {
      margin-top: 48px;
    }
  }

  @media screen and (max-width: 1199px) {
    &+.fixture-results {
      margin-top: 64px;
    }

    .match-list-container {
      .match-list-header {
        height: 124px;
        flex-direction: column;
        justify-content: center;

        .league-container {
          .competition-name {
            margin-left: 16px;
            font-size: 24px;
            line-height: 28px;
          }
        }
      }
    }
  }

  @media screen and (min-width: 768px) {
    .match-list-container {
      margin-top: 40px;
    }

    .fixture-item {
      display: flex;
      flex-direction: column;
      background-color: var(--color-white);

      &+.fixture-item {
        margin-top: 8px;
      }

    }
  }

  @media screen and (max-width: 768px) {
    .match-list-container {
      margin-top: 0px;

      .match-list-header {
        height: 120px;

        .league-container {
          .competition-name {
            font-size: 20px;
            line-height: 24px;
          }
        }

        .nav-container {
          .nav-matchweek {
            &.nav-matchweek--current {
              flex: auto;
              padding: 0 8px;
              @include text_ellipsis();
            }
          }
        }
      }
    }

    .fixture-item .fixture-item__body .match-info .match-details .club-info .club-info__body .club-name {
      font-size: 24px;
      line-height: 24px;
    }
  }

  @media screen and (max-width: 576px) {
    .match-list-container {
      .match-list-header {
        .league-container {
          .competition-name {
            font-size: 18px;
            line-height: 20px;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 425px) {
  .match-list-container {
    .match-list-header {
      .league-container {
        .competition-name {
          font-size: 16px;
          line-height: 20px;
        }
      }
    }
  }
}

.fixture-item {
  display: flex;
  flex-direction: column;
  background-color: var(--color-white);

  &+.fixture-item {
    margin-top: 0px;
  }

  .fixture-item__body {
    position: relative;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--color-earth-1);
    border-radius: 3px;

    .match-logos {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      margin-top: 14px;
      // padding-left: 56px;
      // padding-right: 32px;

      .match-logo {
        height: 32px;
        max-width: 200px;
        overflow: hidden;

        img {
          height: 100%;
          width: auto;
        }
      }
    }

    .match-info {
      position: relative;
      display: flex;
      align-items: center;
      height: 80px;
      padding: 0 24px;

      .match-situation {
        color: var(--color-earth-4);
        font-family: var(--font-barlow-condensed);
        font-style: normal;
        font-weight: 700;
        font-size: 18px;
        line-height: 24px;
        letter-spacing: 0.933333px;
        text-transform: uppercase;
        width: 48px;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;

        .match-detail-situation {
          color: var(--color-drawn);
        }

        .ytlive-icon {
          display: flex;
          height: 40px;
          width: 40px;
        }

        &.match-situation--live {
          .match-detail-situation {
            color: var(--color-lost);
            font-family: var(--font-jleague-kick);
            font-size: 20px;
            line-height: 20px;
          }
        }
      }

      .match-details {
        flex: 1;
        display: flex;
        align-items: center;
        position: relative;

        .club-info {
          flex: 1;
          pointer-events: none;
          overflow: hidden;

          .club-info__body {
            position: relative;
            pointer-events: all;
            display: flex;
            align-items: center;
            font-family: var(--font-jleague-kick);
            font-style: normal;
            font-weight: 700;
            font-size: 30px;
            line-height: 32px;

            .club-emblem {
              flex: 0 0 auto;
              width: 64px;
              height: 64px;
            }

            .club-name {
              flex: auto;
              color: var(--color-black);
              font-size: 25px;
              @include text_ellipsis();

              &.club-name-tablet {
                display: none;
              }
            }

            &:hover {
              opacity: 1;

              .club-name {
                text-decoration: underline;
              }
            }
          }

          &.club-info--home {
            text-align: right;
            padding-right: 45px;

            .club-info__body {
              flex-direction: row-reverse;

              .club-name {
                margin-left: 8px;
                text-align: right;
              }
            }
          }

          &.club-info--away {
            padding-left: 45px;

            .club-info__body {
              .club-name {
                margin-right: 8px;
              }
            }
          }
        }

        .score-info {
          background: var(--color-black);
          border-radius: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 84px;
          height: 40px;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);

          .score-info__body {
            color: var(--color-white);
            font-family: var(--font-jleague-kick);
            font-style: normal;
            font-weight: 700;
            font-size: 30px;
            line-height: 30px;
            text-align: center;
            display: flex;
            width: 100%;
            height: 100%;
            align-items: center;
            justify-content: center;
          }

          &.score-info--upcoming {
            background: var(--color-earth-3);

            .score-info__body {
              color: var(--color-black);
            }
          }

          &.score-info--postponed {
            background: var(--color-earth-3);

            .score-info__body {
              color: var(--color-black);
              font-family: var(--font-barlow-condensed);
              font-size: 16px;
              line-height: 16px;
              text-transform: uppercase;
            }
          }
        }
      }

      .match-link {
        display: flex;
        justify-content: center;
        align-items: center;
        transition: all 0.3 linear;
        width: 48px;

        .match-link__icon {
          display: block;
          color: var(--color-earth-4);
          font-size: 32px;
        }
      }
    }

    &:hover:not(.disabled) {
      background: var(--color-earth-1);

      .match-link {
        transform: translateX(8px);
      }
    }
  }

  .fixture-item__footer {
    background-color: var(--color-earth-1);
    border-bottom-left-radius: 24px;
    border-bottom-right-radius: 24px;
    display: flex;
    justify-content: center;
    gap: 16px;
    padding: 16px;

    .jl-button {
      padding: 8px 24px;
      height: 40px;
    }

    &:empty {
      padding: 0;
    }
  }

  @media screen and (max-width: 1199px) {
    .fixture-item__body {
      .match-info {
        .match-details {
          .club-info {
            .club-info__body {
              .club-name {
                font-size: 24px;
                line-height: 24px;
              }

              .club-emblem {
                width: 40px;
                height: 40px;
              }
            }
          }
        }
      }
    }
  }

  @media screen and (max-width: 767px) {
    .fixture-item__body {
      .match-logos {
        margin-top: 10px;

        .match-logo {
          height: 24px;
        }
      }

      .match-info {
        height: 56px;
        padding: 8px;

        .match-situation {
          .ytlive-icon {
            height: 24px;
            width: 24px;
          }
        }

        .match-details {
          .club-info {
            .club-info__body {
              .club-name {

                &.club-name-tablet {
                  display: none;
                }
              }
            }

            &.club-info--home {
              padding-right: 30px;
            }

            &.club-info--away {
              padding-left: 30px;
            }
          }

          .score-info {
            width: 54px;
            height: 32px;

            .score-info__body {
              font-size: 24px;
              line-height: 24px;
            }

            &.score-info--upcoming {
              .score-info__body {
                font-size: 20px;
                line-height: 20px;
              }
            }

            &.score-info--postponed {
              .score-info__body {
                font-size: 10px;
                line-height: 10px;
              }
            }
          }
        }

        .match-link {
          .match-link__icon {
            font-size: 24px;
          }
        }
      }
    }
  }

  @media screen and (max-width: 576px) {
    .fixture-item__body {
      .match-info {
        .match-details {
          .club-info {
            .club-info__body {
              font-size: 16px;
              line-height: 20px;

              .club-emblem {
                height: 32px;
                width: 32px;
                margin: 0 2px;
              }

              .club-name {
                font-size: 16px;

                &.club-name-desktop {
                  display: none;
                }

                &.club-name-tablet {
                  display: block;
                }
              }
            }

            &.club-info--home {
              padding-right: 27px;
            }

            &.club-info--away {
              padding-left: 27px;
            }
          }

          .score-info {
            width: 48px;

            .score-info__body {
              font-size: 20px;
            }
          }
        }
      }
    }


    .fixture-item__footer {
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      gap: 8px;
      padding: 8px;

      .jl-button {
        height: 32px;
        border-radius: 16px;

        .jl-button__label {
          font-size: 14px;
          line-height: 16px;
        }

        &::before {
          width: 16px;
          height: 16px;
        }
      }
    }
  }

  @media screen and (max-width: 425px) {
    .fixture-item__body {
      .match-info {
        .match-situation{
          &.match-situation--full-time {
            .match-detail-situation {
              font-size: 14px;
            }
          }
        }
        .match-details {
          .match-detail-situation {
            font-size: 14px;
          }
          .club-info {
            .club-info__body {
              font-size: 14px;

              .club-name {
                &.club-name-desktop {
                  display: none;
                }

                &.club-name-tablet {
                  display: block;
                  font-size: clamp(16px, 2vw, 20px);
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }
            }
          }
        }
      }
    }
  }
}

body.lang-th {
  .fixture-item .fixture-item__body .match-details .score-info.score-info--postponed .score-info__body {
    font-size: 20px;
    line-height: 20px;

    @media screen and (max-width: 1199px) {
      font-size: 18px;
      line-height: 18px;
    }

    @media screen and (max-width: 767px) {
      font-size: 14px;
      line-height: 14px;
    }

    @media screen and (max-width: 576px) {
      font-size: 12px;
      line-height: 12px;
    }
  }

  .match-list-container {
    .match-list-header {
      .league-container {
        .competition-name {
          font-size: 16px;
          line-height: 20px;
        }
      }
    }
  }
}


body.lang-vi {
  .fixture-results .match-list-container .match-list-header .league-container .competition-name {
    font-weight: 600;
  }
}

:root {
  body.lang-th {
    .fixture-item .fixture-item__body .match-details .score-info .score-info__body {
      --font-jleague-kick: #{$font-jleague-kick};
    }
  }
}
