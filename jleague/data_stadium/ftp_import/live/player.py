import aioftp
import logging
import xml.etree.ElementTree as ET
from core.enums import TeamMemberTypeEnum
from match.models import Game
from ..helpers import (
    async_find_first,
    async_save_item,
    process_path_if_needed,
    read_file_to_str,
    xml_value,
    get_team_side,
)

logger = logging.getLogger(__name__)


async def import_live_player(ftp_client: aioftp.Client, file_path: str, file_stat: dict):  # noqa
    """Import live players of game from DS
    source file: {MATCH_ID}/live_player.xml
    doc file:
    """
    async with process_path_if_needed(file_path, file_stat) as should_process:
        if not should_process:
            return

        # read XML file
        file_content = await read_file_to_str(ftp_client, file_path)
        root = ET.fromstring(file_content)  # Football node
        report_node = root.find('PlayerReport')
        game_id = xml_value(report_node, 'GameID', int)

        # check game exists
        game: Game = await async_find_first(Game, id=game_id)
        if not game:
            logger.warn('Game not found (id=%s)', game_id)
            return

        if not game.details:
            game.details = {}

        subs_from_to = {}  # out-player-id -> (time, in-player-id, in-uniform-no)
        subs_to_from = {}  # in-player-id -> (time, out-player-id, out-uniform-no)

        # substitutions
        key = 'substitutions'
        game.details[key] = []

        for change_info_node in report_node.findall('ChangeInfo'):
            team = get_team_side(change_info_node.attrib['HV'])
            if team is None:
                continue

            for player_info_node in change_info_node.findall('PlayerInfo'):
                state_id = xml_value(player_info_node, 'StateID', int)
                time = xml_value(player_info_node, 'Time')
                in_id = xml_value(player_info_node, 'InID', int)
                in_uniform_no = xml_value(player_info_node, 'InUni', int)
                out_id = xml_value(player_info_node, 'OutID', int)
                out_uniform_no = xml_value(player_info_node, 'OutUni', int)
                subs_from_to[out_id] = (time, in_id, in_uniform_no)
                subs_to_from[in_id] = (time, out_id, out_uniform_no)

                game.details[key].append(
                    {
                        'state_id': state_id,
                        'time': time,
                        'team': team,
                        'in_player_id': in_id,
                        'in_uniform_no': in_uniform_no,
                        'out_player_id': out_id,
                        'out_uniform_no': out_uniform_no,
                    }
                )

        # starting players
        for starting_info_node in report_node.findall('StartingInfo'):
            team = get_team_side(starting_info_node.attrib['HV'])
            if team is None:
                continue

            key = f'{team}_team_starting_players'
            game.details[key] = []

            for player_info_node in starting_info_node.findall('PlayerInfo'):
                player_id = xml_value(player_info_node, 'ID', int)
                position = xml_value(player_info_node, 'Pos')
                uniform_no = xml_value(player_info_node, 'Uni', int)
                sub = subs_from_to.get(player_id)
                game.details[key].append(
                    {
                        'player_id': player_id,
                        'position': position,
                        'uniform_no': uniform_no,
                        'team': team,
                        'out_time': sub[0] if sub else None,
                        'in_player_id': sub[1] if sub else None,
                        'in_uniform_no': sub[2] if sub else None,
                    }
                )

        # bench players
        for bench_info_node in report_node.findall('BenchInfo'):
            team = get_team_side(bench_info_node.attrib['HV'])
            if team is None:
                continue

            key = f'{team}_team_bench_players'
            game.details[key] = []

            for player_info_node in bench_info_node.findall('PlayerInfo'):
                player_id = xml_value(player_info_node, 'ID', int)
                position = xml_value(player_info_node, 'Pos')
                uniform_no = xml_value(player_info_node, 'Uni', int)
                sub = subs_to_from.get(player_id)
                game.details[key].append(
                    {
                        'player_id': player_id,
                        'position': position,
                        'uniform_no': uniform_no,
                        'team': team,
                        'in_time': sub[0] if sub else None,
                        'out_player_id': sub[1] if sub else None,
                        'out_uniform_no': sub[2] if sub else None,
                    }
                )

        # staffs
        for staff_info_node in report_node.findall('TeamStaffInfo'):
            team = get_team_side(bench_info_node.attrib['HV'])
            if team is None:
                continue

            key = f'{team}_team_staffs'
            game.details[key] = []

            for staff_node in staff_info_node.findall('StaffInfo'):
                staff_id = xml_value(staff_node, 'StaffID', int)
                position = xml_value(staff_node, 'PostName')

                # only care the manager for now
                if '監督' == position:  # director
                    game.details[key].append(
                        {
                            'staff_id': staff_id,
                            'team': team,
                            'position': TeamMemberTypeEnum.MANAGER.value,
                        }
                    )
                    break

        await async_save_item(game)

        logger.info('Processed live game players for game (id=%s)', game_id)
