import logging
from clubs.models import Club
from standings.models import ClubRanking
from .helpers import (
    async_find_first,
    async_upsert,
    read_xml_file,
    xml_value,
)

logger = logging.getLogger(__name__)


async def import_team_rankings(file_path: str):
    """Import club rank of the season from DS
    The file which outputs club ranking of the season.
    source file: real/rank_team-{competition_id}.xml
    """
    # read XML file
    root = await read_xml_file(file_path)
    report_node = root.find('RankReport')
    year = int(xml_value(report_node, 'GameDate')[:4])
    game_kind_id = xml_value(report_node, 'GameKindID', int)
    season_id = xml_value(report_node, 'SeasonID', int)
    group_id = None

    # process rankings
    for i, rank_info_node in enumerate(report_node.findall('RankInfo')):
        # check club exists
        club_id = xml_value(rank_info_node, 'TeamID', int)
        club = await async_find_first(Club, ds_club_id=club_id)
        if not club:
            logger.warn('Club not found (ds_club_id=%s)', club_id)
            continue

        prev_group_id, group_id = group_id, xml_value(rank_info_node, 'GroupID')
        if group_id:
            position = position + 1 if prev_group_id == group_id else 1
        else:
            position = i + 1

        await async_upsert(
            ClubRanking,
            year=year,
            competition_id=game_kind_id,
            club=club,
            defaults={
                'season_id': season_id,
                'group_id': group_id,
                'position': position,
                'ranking': int(rank_info_node.attrib['Ranking']),
                'replace': xml_value(rank_info_node, 'Replace', int),
                'points': xml_value(rank_info_node, 'Point', int),
                'games_played': xml_value(rank_info_node, 'Game', int),
                'games_won': xml_value(rank_info_node, 'Win', int),
                'games_drawn': xml_value(rank_info_node, 'Draw', int),
                'games_lost': xml_value(rank_info_node, 'Lose', int),
                'goals_for': xml_value(rank_info_node, 'Score', int),
                'goals_against': xml_value(rank_info_node, 'Lost', int),
            },
        )

    logger.info('Processed club rankings')
