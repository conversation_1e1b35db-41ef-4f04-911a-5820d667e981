@import 'setup/variable';
@import 'setup/mixin';

.body-club-profile {
  .club-profile-bio {
    background-attachment: fixed;
    position: relative;

    .bio-wrapper {
      background: $color-white;
      border-radius: 2px;

      .bio-title {
        font-family: $font-overpass;
        font-style: normal;
        font-weight: 900;
        font-size: 28px;
        line-height: 32px;
        color: $color-black;
        margin-bottom: 24px;
      }

      .bio-item {
        .title {
          font-family: $font-avenir-next-condensed;
          font-style: normal;
          font-weight: 600;
          font-size: 16px;
          line-height: 22px;
          text-transform: uppercase;
          color: $color-earth-4;
        }

        .value {
          margin-top: 8px;
          font-family: $font-overpass;
          font-style: normal;
          font-weight: normal;
          font-size: 20px;
          line-height: 32px;
          color: $color-black;

          & > p + p {
            margin-top: 20px;
          }
        }

        & + .bio-item,
        & + .bio-group {
          margin-top: 24px;
        }
      }

      .bio-group {
        display: flex;

        .bio-item {
          flex: 0 0 auto;
          width: 50%;

          & + .bio-item {
            margin-top: 0;
          }
        }

        & + .bio-item,
        & + .bio-group {
          margin-top: 24px;
        }
      }
    }

    @media screen and (min-width: 1200px) {
      margin-top: 80px;
      padding: 80px 0;

      .bio-wrapper {
        margin: 0 auto;
        width: 664px;
        padding: 48px;
      }
    }

    @media screen and (max-width: 1199px) and (min-width: 768px) {
      margin-top: 80px;
      padding: 48px 0;

      .bio-wrapper {
        margin: 0 48px;
        padding: 52px;
      }
    }

    @media screen and (max-width: 767px) {
      margin-top: 28px;
      padding: 25px 0;

      .bio-wrapper {
        margin: 0 24px;
        padding: 52px 50px;
      }
    }
  }
}

body.lang-th {
  .club-profile-bio .bio-wrapper {
    .bio-title,
    .bio-item .title,
    .bio-item .value {
      @include font-thai();
    }
  }
}

body.lang-vi {
  .club-profile-bio .bio-wrapper {
    .bio-title,
    .bio-item .title,
    .bio-item .value {
      @include font-vietnamese();
      font-weight: 600;
    }
  }
}
