from django.db.models import <PERSON><PERSON>anField, ExpressionWrapper, Q
from django_filters import rest_framework as filters
from rest_framework import viewsets
from clubs.api.serializers import (
    ClubSerializer,
    ClubCompetitionSerializer,
    ClubExtraInfoSerializer,
    ClubPlayerSerializer,
)
from clubs.models import Club, ClubCompetition, ClubExtraInfo, ClubPlayer


class ClubFilter(filters.FilterSet):
    is_jleague = filters.BooleanFilter(
        method="is_jleague_filter",
        label="J.League Club",
    )

    class Meta:
        model = Club
        fields = {
            "id": ("exact", "in"),
            "ds_club_id": ("exact", "in"),
            "j1_license": ("exact",),
            "j2_license": ("exact",),
            "j3_license": ("exact",),
            "name": ("iexact",),
            "name_short": ("iexact",),
            "slug": ("exact", "in"),
            "country_id": ("iexact", "in"),
        }

    def is_jleague_filter(self, queryset, name, value):
        value = None if value is not True else value
        return queryset.filter(is_jleague=value)


class ClubExtraInfoFilter(filters.FilterSet):
    class Meta:
        model = ClubExtraInfo
        fields = {
            "id": ("exact", "in"),
            "club_id": ("exact", "in"),
            "four_letters_name": ("iexact", "in"),
        }


class ClubCompetitionFilter(filters.FilterSet):
    class Meta:
        model = ClubCompetition
        fields = {
            "id": ("exact", "in"),
            "club_id": ("exact", "in"),
            "competition_id": ("exact", "in"),
            "year": ("exact",),
        }


# generics.ListAPIView
class ClubViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows clubs to be viewed.
    """

    is_jleague_aggr = ExpressionWrapper(
        Q(j1_license=True) | Q(j2_license=True) | Q(j3_license=True),
        output_field=BooleanField(),  # NOTE: BooleanField returns None instead of False when default is not set
    )

    queryset = Club.objects.annotate(is_jleague=is_jleague_aggr)
    serializer_class = ClubSerializer
    filterset_class = ClubFilter
    # filterset_fields = "__all__"


class ClubCompetitionViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows club competitions to be viewed.
    """

    queryset = ClubCompetition.objects.all()
    serializer_class = ClubCompetitionSerializer
    filterset_class = ClubCompetitionFilter
    # filterset_fields = ("id", "year", "club", "competition")


class ClubExtraInfoViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows club extra infos to be viewed.
    """

    queryset = ClubExtraInfo.objects.all()
    serializer_class = ClubExtraInfoSerializer
    filterset_class = ClubExtraInfoFilter
    # filterset_fields = ("id", "title", "four_letters_name", "club")


class ClubPlayerViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows club players to be viewed.
    """

    queryset = ClubPlayer.objects.all()
    serializer_class = ClubPlayerSerializer
    filterset_fields = "__all__"
