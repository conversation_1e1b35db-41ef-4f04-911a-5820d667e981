# J-League Docker Architecture

```mermaid
graph TB
    A[Nginx Container] --> B[Django App Container]
    B --> C[PostgreSQL Container]
    B --> D[Redis Container]
    E[Backup Container] --> C
    F[Restore Container] --> C
    
    A --> A1[Port 80/443]
    A --> A2[SSL Termination]
    A --> A3[Static Files]
    
    B --> B1[Django + Wagtail]
    B --> B2[WebSocket Support]
    B --> B3[Background Tasks]
    
    C --> C1[Primary Database]
    C --> C2[Port 5432]
    
    D --> D1[Cache Layer]
    D --> D2[Session Storage]
    D --> D3[WebSocket Backend]
    
    E --> E1[Automated Backups]
    E --> E2[S3 Storage]
    
    F --> F1[Database Restore]
    F --> F2[Recovery Mode]
```
