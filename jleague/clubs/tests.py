import datetime
from django.test import TestCase
from clubs.models import ClubCompetition
from .parsers import ClubParser
from .helper import (
    get_club_competition,
    get_competition_clubs,
    get_league_clubs,
    get_club_name_by_name_slug,
    get_ordered_clubs,
)

years = [2022, 2021, 2020, 2019, 2018]
com = [2, 6, 68]  # j1, j2, j3
datas = {}


def get_data_club_competition():
    for year in years:
        datas[year] = list(
            ClubCompetition.objects.filter(
                year=year,
                competition_id__in=com,
            )
            .order_by("competition_id")
            .values(
                "club",
                "club__name",
                "club__slug",
                "competition__name",
                "competition__slug",
                "year",
            )
        )
    return datas


class ClubsTestCase(TestCase):
    get_data_club_competition()

    def test_jleague_club_2022(self):
        """There must be 58 clubs played in J.League on 2022"""
        league_clubs = get_league_clubs(year=2022)
        jleague_competitions = ("j1", "j2", "j3")
        total_clubs = 0

        for league_club in league_clubs:
            competition = league_club["competition"]
            clubs = league_club["clubs"]

            self.assertTrue(
                competition.slug in jleague_competitions, "Not a J.League competition"
            )

            if competition.slug == "j1":
                self.assertGreaterEqual(
                    len(clubs), 18, "The total of J1 clubs is less than 18"
                )
            elif competition.slug == "j2":
                self.assertGreaterEqual(
                    len(clubs), 22, "The total of J2 clubs is less than 22"
                )
            elif competition.slug == "j3":
                self.assertGreaterEqual(
                    len(clubs), 18, "The total of J3 clubs is less than 18"
                )

            total_clubs += len(clubs)

        self.assertEqual(
            total_clubs, 58, "The total of J.League clubs is not equal to 58"
        )

    def test_get_club_name_by_name_slug(self):
        """Convert club name slug back into normal club name
        (replace dash (-) to space ( ) or any special character e.g. middle dot(･), dot(.)
        """
        slugs = {
            "Kawasaki Frontale": get_club_name_by_name_slug("Kawasaki-Frontale"),
            "V-Varen Nagasaki": get_club_name_by_name_slug("V-Varen-Nagasaki"),
            "Y.S.C.C. Yokohama": get_club_name_by_name_slug("YSCC-Yokohama"),
            "Yokohama F･Marinos": get_club_name_by_name_slug("Yokohama-F-Marinos"),
            "Test Club Name": get_club_name_by_name_slug("Test-Club-Name"),
        }

        for name, slug in slugs.items():
            self.assertEqual(slug, name, "The slug is not equal " + str(name))

    def test_get_club_competition(self):
        """Main club's competition must be correct on each year"""

        for years in datas.values():
            for clubs_year in years:
                # มีปัญหาปี 2020, 2019, 2018 มีทีมที่มี competition_slug มากกว่า 1
                if clubs_year["year"] == 2022 or clubs_year["year"] == 2021:
                    competition = get_club_competition(
                        clubs_year["club__slug"], clubs_year["year"]
                    )

                    self.assertEqual(
                        competition["slug"],
                        clubs_year["competition__slug"],
                        "The competition slug is not equal to "
                        + clubs_year["competition__slug"],
                    )

    def test_get_competition_clubs(self):
        """J1 League
        There must be 20 clubs played in J.League 1 on 2021
        There must be 18 clubs played in J.League 1 on 2022

        J.league 2
        There must be 22 clubs played in J.League 2 on 2021 and 2022

        J.league 3
        There must be 15 clubs played in J.League 3 on 2021
        There must be 18 clubs played in J.League 3 on 2022

        The club.slug is contain club__slug
        """

        years_competition = {2022: ["j1", "j2", "j3"], 2021: ["j1", "j2", "j3"]}

        for year, coms in years_competition.items():
            for com in coms:
                context = get_competition_clubs(com, year)

                count = 0
                for club in context:
                    for club_data in datas[year]:
                        if com == club_data["competition__slug"]:
                            if club.id == club_data["club"]:
                                self.assertEqual(
                                    club.slug,
                                    club_data["club__slug"],
                                    "The slug is contain " + str(club.slug),
                                )
                                count += 1

                self.assertEqual(
                    len(context),
                    count,
                    "The total of " + com + " clubs is not equal to " + str(count),
                )

    def test_get_ordered_clubs(self):
        """There must be 57 clubs played in J.League on 2021
        There must be 58 clubs played in J.League on 2022

        There must be club__slug fields in J.League on 2021 and 2022
        """

        clubs_year = {}
        clubs_year = {"2021": get_ordered_clubs(2021), "2022": get_ordered_clubs(2022)}

        self.assertEqual(
            len(clubs_year["2021"]),
            57,
            "The total of J.League clubs on 2021 is not equal to 57",
        )
        for club in datas[2021]:
            self.assertTrue(
                club["club__slug"] in clubs_year["2021"],
                club["club__slug"] + " does not exist J.League on 2021",
            )

        self.assertEqual(
            len(clubs_year["2022"]),
            58,
            "The total of J.League clubs on 2022 is not equal to 58",
        )
        for club in datas[2022]:
            self.assertTrue(
                club["club__slug"] in clubs_year["2022"],
                club["club__slug"] + " does not exist J.League on 2022",
            )

    def test_parsers_player_stats(self):
        """There must be less equal 5 stats in clubs on 2021 and 2022

        The field club name must equal to club__name
        The field club slugs must equal to club__slug
        The field label must equal to label stats

        There must be name and value fields in player stats
        """

        for club_year in datas[2022]:
            club = ClubParser(id=club_year["club"])

            player_stats = {
                "player_stats_2021": club.player_stats(2021),
                "player_stats_2022": club.player_stats(2022),
            }

            label = ["Goals", "Assists", "Tackles", "Saves", "Distance", "Sprints"]
            for stats in player_stats.values():
                count = 0
                for stat in stats:
                    self.assertEqual(
                        stat["label"],
                        label[count],
                        "The label is not equal to " + label[count],
                    )
                    self.assertEqual(
                        stat["club_slugs"],
                        [club_year["club__slug"]],
                        "The club name is not equal to " + club_year["club__slug"],
                    )

                    for result in stat["result"]:
                        self.assertEqual(
                            result["club"]["name"],
                            club_year["club__name"],
                            "The club name is not equal to " + club_year["club__name"],
                        )
                        self.assertTrue(
                            "name" in result["player"],
                            '"name" field does not exist in the player',
                        )
                        self.assertTrue(
                            "value" in result,
                            '"value" field does not exist in the player',
                        )

                    count += 1
                    self.assertLessEqual(
                        len(stat["result"]),
                        5,
                        "The total of player stats is greater than 5",
                    )

    def test_parsers_format(self):
        """club.format is infomation club
        There must be stadiums, mascots, club name and founded in club info
        """

        for club_data in datas[2022]:
            club = ClubParser(id=club_data["club"])
            club_info = club.format()

            self.assertEqual(type(club_info), dict, "Data type is not equal dict")

            if "stadiums" in club_info:
                self.assertTrue(
                    "title" in club_info["stadiums"][0],
                    "`title` does not exist club info stadiums field",
                )
                self.assertTrue(
                    "capacity" in club_info["stadiums"][0],
                    "`capacity` does not exist club info stadiums field",
                )
                self.assertTrue(
                    "stadium_id" in club_info["stadiums"][0],
                    "`stadium_id` does not exist club info stadiums field",
                )
            else:
                print(club_info["club"]["name"], "--not stadiums--")

            if "mascots" in club_info:
                self.assertTrue(
                    "mascots" in club_info,
                    "`mascots` does not exist club info mascots field",
                )
            else:
                print(club_info["club"]["name"], "--not mascots--")

            self.assertTrue(
                "name" in club_info["club"], "`name` does not exist club info field"
            )
            self.assertTrue(
                "founded" in club_info["club"],
                "`founded` does not exist club info field",
            )

    def test_parsers_statistics(self):
        """Test club statistics
        - There must be games played, games won, games drawn, games lost, goals, goals conceded and goal difference fields in club statistics
        - The field type of games played, games won, games drawn, games lost, goals, goals conceded and goal difference must equal int
        - The field games played, games won, games drawn, games lost, goals, goals conceded, goal difference must greater or equal to 0
        - The sum of games won, games drawn and games lost must equal to games played
        - The sum of goals and goals conceded must equal to goal difference
        """

        for year, data in datas.items():
            for club_data in data:
                club = ClubParser(id=club_data["club"])

                club_statistics = club.statistics(year)

                self.assertTrue(
                    "games_played" in club_statistics,
                    "`games_played` does not exist club statistics field",
                )
                self.assertEqual(
                    type(club_statistics["games_played"]),
                    int,
                    "`games_played` field type is not equal int",
                )
                self.assertGreaterEqual(
                    club_statistics["games_played"],
                    0,
                    "The total of `games_played` is less than 0",
                )

                self.assertTrue(
                    "games_won" in club_statistics,
                    "`games_won` does not exist club statistics",
                )
                self.assertEqual(
                    type(club_statistics["games_won"]),
                    int,
                    "`games_won` field type is not equal int",
                )
                self.assertGreaterEqual(
                    club_statistics["games_won"],
                    0,
                    "The total of `games_won` is less than 0",
                )

                self.assertTrue(
                    "games_drawn" in club_statistics,
                    "`games_drawn` does not exist club statistics",
                )
                self.assertEqual(
                    type(club_statistics["games_drawn"]),
                    int,
                    "`games_drawn` field type is not equal int",
                )
                self.assertGreaterEqual(
                    club_statistics["games_drawn"],
                    0,
                    "The total of `games_drawn` is less than 0",
                )

                self.assertTrue(
                    "games_lost" in club_statistics,
                    "`games_lost` does not exist club statistics",
                )
                self.assertEqual(
                    type(club_statistics["games_lost"]),
                    int,
                    "`games_lost` field type is not equal int",
                )
                self.assertGreaterEqual(
                    club_statistics["games_lost"],
                    0,
                    "The total of `games_lost` is less than 0",
                )

                self.assertTrue(
                    "goals" in club_statistics, "`goals` does not exist club statistics"
                )
                self.assertEqual(
                    type(club_statistics["goals"]),
                    int,
                    "`goals` field type is not equal int",
                )
                self.assertGreaterEqual(
                    club_statistics["goals"], 0, "The total of `goals` is less than 0"
                )

                self.assertTrue(
                    "goals_conceded" in club_statistics,
                    "`goals_conceded` does not exist club statistics",
                )
                self.assertEqual(
                    type(club_statistics["goals_conceded"]),
                    int,
                    "`goals_conceded` field type is not equal int",
                )
                self.assertGreaterEqual(
                    club_statistics["goals_conceded"],
                    0,
                    "The total of `goals_conceded` is less than 0",
                )

                self.assertTrue(
                    "goal_difference" in club_statistics,
                    "`goal_difference` does not exist club statistics",
                )
                self.assertEqual(
                    type(club_statistics["goal_difference"]),
                    int,
                    "`goal_difference` field type is not equal int",
                )

                games_total = (
                    club_statistics["games_won"]
                    + club_statistics["games_drawn"]
                    + club_statistics["games_lost"]
                )
                games_played = club_statistics["games_played"]
                self.assertEqual(
                    games_total,
                    games_played,
                    "The sum of won, drawn and lost is not equal to games played",
                )

                goal_total = (
                    club_statistics["goals"] - club_statistics["goals_conceded"]
                )
                goal_difference = club_statistics["goal_difference"]
                self.assertEqual(
                    goal_total,
                    goal_difference,
                    "The sum of goals and goals againt is not equal to goals difference",
                )

    def test_parsers_standings(self):
        """Test standing of club profile page
        - Current year club's standing must return 5 rows
        - Next year club's standing must return 0 rows
        - The sum of win point and draw point is not equal to points
        - The sum of won, drawn and lost is not equal to games played
        - The sum of goals and goals againt is not equal to goals difference
        """

        for club_data in datas[2022]:
            club = ClubParser(id=club_data["club"])

            standings = club.standings()
            standings_2025 = club.standings(year=2025)

            self.assertEqual(len(standings), 5, "The standing(club) is not equal to 5")

            games_total = (
                standings[0]["games_won"]
                + standings[0]["games_drawn"]
                + standings[0]["games_lost"]
            )
            games_count = standings[0]["games_played"]
            self.assertEqual(
                games_total,
                games_count,
                "The sum of won, drawn and lost is not equal to games played",
            )

            goal_total = standings[0]["goals_for"] - standings[0]["goals_against"]
            goals_difference = standings[0]["goals_difference"]
            self.assertEqual(
                goal_total,
                goals_difference,
                "The sum of goals and goals againt is not equal to goals difference",
            )

            points = (standings[0]["games_won"] * 3) + (standings[0]["games_drawn"])
            points_sum = standings[0]["points"]
            self.assertEqual(
                points,
                points_sum,
                "The sum of win point and draw point is not equal to points",
            )

            self.assertEqual(
                len(standings_2025), 0, "The standing 2023(club) is not equal to 0"
            )

    def test_parsers_last_matches(self):
        """Test club latest matches
        - The total of latest matches must be less or equal to 5
        - There must be a match short result of the selected club
        - Home team must not empty
        - Away team must not empty
        - Selected club must be exist on home or away team
        """

        # TODO: e.g. promoted club, relegated club
        for club_data in datas[2022]:
            club = ClubParser(id=club_data["club"])

            last_matches = club.last_matches()

            self.assertLessEqual(
                len(last_matches), 5, "The total of last matches is greater than 5"
            )

            if len(last_matches):
                for club in last_matches:
                    self.assertTrue(
                        club.get("short_result"),
                        '"Short result" does not exist last matches field',
                    )

                    self.assertTrue(
                        club.get("home"), '"home" does not exist last matches field'
                    )
                    self.assertTrue(
                        club.get("away"), '"away" does not exist last matches field'
                    )

                    if club["home_team_id"] == club_data["club"]:
                        self.assertEqual(
                            club["home_team_id"],
                            club_data["club"],
                            "The id home team is not equal to "
                            + str(club_data["club"]),
                        )
                    else:
                        self.assertEqual(
                            club["away_team_id"],
                            club_data["club"],
                            "The id away team is not equal to "
                            + str(club_data["club"]),
                        )

    def test_parsers_get_next_match(self):
        """Test club next match
        - There must not be a match when off season #TODO
        - The match date and time must be in the future
        - There must not be a match short result
        - Home team must not empty
        - Away team must not empty
        - Selected club must be exist on home or away team
        """

        for club_data in datas[2022]:
            club = ClubParser(id=club_data["club"])

            next_match = club.next_match()

            if next_match is not None:
                self.assertTrue(
                    "game_date" in next_match,
                    '"Date match" does not exist next match field',
                )
                self.assertTrue(
                    "game_time_raw" in next_match,
                    '"Time match" does not exist next match field',
                )
                self.assertTrue(
                    "stage" in next_match, '"Matchweek" does not exist next match field'
                )

                self.assertTrue(
                    "home" in next_match, '"home" does not exist next match field'
                )
                self.assertTrue(
                    "name" in next_match["home"],
                    '"Home club" does not exist next match field',
                )

                self.assertTrue(
                    "away" in next_match, '"away" does not exist next match field'
                )
                self.assertTrue(
                    "name" in next_match["away"],
                    '"Away club" does not exist next match field',
                )

                self.assertLessEqual(
                    datetime.date.today(),
                    next_match["game_date"],
                    "Datetime now is greater than game date",
                )

                if next_match["home_team_id"] == club_data["club"]:
                    self.assertEqual(
                        next_match["home_team_id"],
                        club_data["club"],
                        "The id home team is not equal to " + str(club_data["club"]),
                    )
                else:
                    self.assertEqual(
                        next_match["away_team_id"],
                        club_data["club"],
                        "The id away team is not equal to " + str(club_data["club"]),
                    )
