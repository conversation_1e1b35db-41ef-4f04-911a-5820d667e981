from django import forms
from django.db import models
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from wagtail import blocks
from wagtail.admin.panels import FieldPanel
from wagtail.contrib.table_block.blocks import TableBlock
from wagtail.embeds.blocks import EmbedBlock
from wagtail.fields import StreamField
from wagtail.images.blocks import ImageChooserBlock
from wagtail.models import Page
from wagtail.search import index
from core.models import Country, Competition, NavigationGroup, NavigationLink
from core.blocks import SectionHeaderBlock
from core.helpers import get_sidebar_banner
from utils.helpers import get_competitions_list
from utils.helpers.etc import get_partners, get_footer_navigations, get_main_navigations
from utils.helpers.seo import update_seo_data_wagtail_view
from news.helper import get_latest_featured
from newsletter.forms import SubscribeForm


class Broadcaster(models.Model, index.Indexed):
    name = models.CharField(max_length=255)
    website = models.URLField(blank=True, null=True)
    is_active = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    search_fields = [
        index.SearchField("name", partial_match=True),
    ]

    panels = [
        FieldPanel("name"),
        FieldPanel("website"),
        FieldPanel("is_active"),
    ]

    class Meta:
        ordering = ["name"]

    def __str__(self):
        return self.name


class BroadcastArea(models.Model, index.Indexed):
    name = models.CharField(max_length=255)
    countries = models.ManyToManyField(Country, blank=True)
    competitions = models.ManyToManyField(Competition)
    broadcasters = models.ManyToManyField(Broadcaster)
    country_note = models.TextField(blank=True, null=True)
    competition_note = models.TextField(blank=True, null=True)
    sort_order = models.IntegerField(blank=True, null=True, default=0)
    is_active = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    search_fields = [
        index.SearchField("name", partial_match=True),
    ]

    panels = [
        FieldPanel("name"),
        FieldPanel("countries", widget=forms.CheckboxSelectMultiple),
        FieldPanel("broadcasters", widget=forms.CheckboxSelectMultiple),
        FieldPanel("competitions", widget=forms.CheckboxSelectMultiple),
        FieldPanel("country_note"),
        FieldPanel("competition_note"),
        FieldPanel("sort_order"),
        FieldPanel("is_active"),
    ]

    class Meta:
        ordering = ["name"]

    def __str__(self):
        return self.name


@method_decorator(cache_page(60), name="serve")
class AboutPage(Page):
    table_options = {
        "contextMenu": [
            "row_above",
            "row_below",
            "---------",
            "col_left",
            "col_right",
            "---------",
            "remove_row",
            "remove_col",
            "---------",
            "undo",
            "redo",
            "---------",
            "copy",
            "cut",
            "---------",
            "alignment",
        ]
    }

    subject = models.CharField(max_length=255, null=False, blank=False)
    body = StreamField(
        [
            ("richtext", blocks.RichTextBlock()),
            ("embed", EmbedBlock()),
            ("image", ImageChooserBlock()),
            ("table", TableBlock(table_options=table_options)),
            ("sectionheader", SectionHeaderBlock()),
            ("html", blocks.RawHTMLBlock()),
        ],
        use_json_field=True,
    )
    navigation = models.ForeignKey(
        NavigationGroup,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="+",
    )

    content_panels = Page.content_panels + [
        FieldPanel("subject"),
        FieldPanel("body"),
        FieldPanel("navigation"),
    ]

    template = "about/about_page.html"

    # Parent page / subpage type rules
    parent_page_types = ["home.HomePage", "about.AboutPage"]
    subpage_types = ["about.AboutPage"]
    # max_count = 1

    def get_context(self, request):
        context = super().get_context(request)
        context["competitions"] = get_competitions_list()
        context["header"] = {
            "template": "components/header/general.html",
            "background": get_latest_featured(),
            "title": self.subject,
            "navigations": get_main_navigations(),
        }
        context["sidebar"] = {"banner": get_sidebar_banner()}
        context["footer"] = {
            "partners": get_partners(),
            "navigations": get_footer_navigations(),
        }

        context["subscribe_form"] = SubscribeForm(initial={"source": "jleague-footer"})

        if self.navigation:
            context["nav_items"] = list(
                NavigationLink.objects.filter(parent=self.navigation).select_related(
                    "link_page"
                )
            )

        # the seo part should be located at the bottom
        context = update_seo_data_wagtail_view(context)
        context["seo_title"] = f"{context['seo_title']} | About | J.LEAGUE"

        if self.title.lower() == "about":
            context["hide_breadcrumb"] = True
            context["seo_title"] = "About | J.LEAGUE"

        return context

    def get_sitemap_urls(self, request):
        sitemap = super().get_sitemap_urls(request)
        sitemap[0]["priority"] = 0.6
        return sitemap
