(function ($) {
  // fixtures filter
  function onReady() {
    const yearSelector = $('.fixture-year-selector');
    const monthSelector = $('.fixture-month-selector');
    const clubSelector = $('.fixture-club-selector');
    const stageSelector = $('.fixture-stage-selector');
    const fixturesContainer = $('.fixture-results-list');

    function _getFixturesURL(isData = false) {
      const year = yearSelector.val() || selectedYear;
      const month = monthSelector.val() || selectedMonth;
      const club = clubSelector.val() || selectedClub;
      let stage = stageSelector.val() || selectedStage;
      let urlPrefix = '/fixtures' + (isData ? '/data' : '');
      let url = `${urlPrefix}/${selectedCompetition}/${year}/${month}/${club}`;

      if (isFixturesStage) {
        stage = stage.split('|');

        if (stage.length === 1) {
          url = `${urlPrefix}/stage/${selectedCompetition}/${year}/${stage[0]}/`;
        } else if (stage.length === 2) {
          url = `${urlPrefix}/stage/${selectedCompetition}/${year}/${stage[0]}/${stage[1]}/`;
        }

        url += `${club}/`;
      }

      return url;
    }

    function _toggleInputs(isEnabled = false) {
      if (isEnabled) {
        yearSelector.removeAttr('disabled');
        monthSelector.removeAttr('disabled');
        clubSelector.removeAttr('disabled');
        stageSelector.removeAttr('disabled');
      } else {
        yearSelector.attr('disabled', true);
        monthSelector.attr('disabled', true);
        clubSelector.attr('disabled', true);
        stageSelector.attr('disabled', true);
      }
    }

    function _afterFetchFixtures() {
      ticketsModal.init('.tickets-modal-trigger');
    }

    function _fetchFixtures() {
      _toggleInputs(false);
      fixturesContainer.addClass('loading');

      const url = _getFixturesURL(true);
      return ajaxRequest(url)
        .then((result) => {
          fixturesContainer.empty();

          if (result.trim() !== '') {
            fixturesContainer.html(result);
          }

          _afterFetchFixtures();

          fixturesContainer.removeClass('loading');
          _toggleInputs(true);
        })
        .catch((error) => {
          console.error(error);
        });
    }

    function _handleChange() {
      _toggleInputs();
      const url = _getFixturesURL();
      const term = url.replace('/fixtures/', '').replaceAll('/', ':');
      sendSearchEvent('fixture', term);

      window.location.href = getCurrentPath(url);
    }

    _fetchFixtures();
    yearSelector.change(_handleChange);
    monthSelector.change(_handleChange);
    clubSelector.change(_handleChange);
    stageSelector.change(_handleChange);
  }

  $(document).ready(onReady);
})(jQuery);
