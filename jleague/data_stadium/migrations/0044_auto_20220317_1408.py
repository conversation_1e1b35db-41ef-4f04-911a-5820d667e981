# Generated by Django 3.1.13 on 2022-03-17 05:08

from django.db import migrations


def insert_formations(apps, schema_editor):
    TeamFormation = apps.get_model('data_stadium', 'TeamFormation')
    formations = (
        (1, '4-4-2 (<PERSON>)'),
        (2, '4-4-2 (Flat)'),
        (3, '4-1-3-2'),
        (4, '4-3-1-2'),
        (5, '4-2-2-2'),
        (6, '4-3-2-1'),
        (7, '4-1-4-1'),
        (8, '4-2-3-1'),
        (9, '4-1-2-3'),
        (10, '4-2-1-3'),
        (11, 'Missing'),
        (12, '3-4-1-2'),
        (13, '3-1-4-2'),
        (14, '3-3-2-2'),
        (15, 'Missing'),
        (16, '3-4-2-1'),
        (17, '3-3-3-1'),
        (18, '3-1-3-3'),
        (19, '3-4-3'),
        (20, '3-1-5-1'),
        (21, '5-1-2-2'),
        (22, '5-2-1-2'),
        (23, 'Missing'),
        (24, '5-3-1-1'),
        (25, '5-4-1'),
        (26, '5-1-3-1'),
        (27, '5-3-1-1'),
        (28, '5-2-2-1'),
        (29, '3-3-1-3'),
    )

    for f in formations:
        TeamFormation.objects.create(formation_id=f[0], name=f[1])


class Migration(migrations.Migration):
    dependencies = [
        ('data_stadium', '0043_teamformation'),
    ]

    operations = [migrations.RunPython(insert_formations)]
