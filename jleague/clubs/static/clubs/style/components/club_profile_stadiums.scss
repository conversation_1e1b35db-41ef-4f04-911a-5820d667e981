@import 'setup/variable';
@import 'setup/mixin';

.body-club-profile {
  .club-profile-stadiums {
    position: relative;

    .club-profile-stadiums__content {
      &.loading {
        min-height: 800px;
      }
    }

    .stadium-item {
      display: flex;
      flex-direction: column;
    }

    .stadium-info,
    .stadium-map,
    .stadium-photo {
      position: relative;
      flex: 0 0 auto;
      width: 100%;
    }

    .stadium-photo {
      .stadium-photo-slider {
        .stadium-image {
          width: 100%;
          height: auto;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }

    .stadium-map {
      height: 500px;

      iframe {
        border: none;
        height: 100%;
        width: 100%;
      }
    }

    .stadium-info {
      background-position: center;
      padding: 80px 0;

      .stadium-info__bg-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0.9;
        z-index: 1;
      }

      .stadium-info__content {
        position: relative;
        z-index: 2;
      }

      .info-grid {
        display: grid;
        column-gap: 20px;
        row-gap: 20px;
        align-items: center;
        grid-template-areas:
          'name name skeleton capacity'
          'city city address address'
          '. . station station';
        grid-auto-rows: auto;
      }

      .stadium-name {
        font-family: $font-overpass;
        font-style: normal;
        font-weight: 900;
        font-size: 64px;
        line-height: 80px;
        grid-area: name;
        display: flex;
        flex-wrap: wrap;
        column-gap: 32px;
      }

      .stadium-city {
        display: flex;
        align-items: center;
        grid-area: city;

        .icon {
          margin-right: 10px;
          width: auto;
          height: 40px;
        }

        .name {
          margin-top: 8px;
          font-family: $font-overpass;
          font-style: normal;
          font-weight: 600;
          font-size: 40px;
          line-height: 62px;
        }
      }

      .stadium-skeleton {
        display: flex;
        align-items: center;
        grid-area: skeleton;

        .image {
          width: auto;
          height: 144px;
        }
      }

      .stadium-capacity {
        grid-area: capacity;

        .label {
          font-family: $font-avenir-next-condensed;
          font-style: normal;
          font-weight: 600;
          font-size: 16px;
          line-height: 22px;
          text-transform: uppercase;
        }

        .value {
          font-family: $font-jleague-kick;
          font-style: normal;
          font-weight: 700;
          font-size: 72px;
          line-height: 72px;
        }
      }

      .stadium-address {
        grid-area: address;

        .label,
        .value {
          font-family: $font-avenir-next-condensed;
          font-style: normal;
        }

        .label {
          font-weight: 600;
          font-size: 16px;
          line-height: 22px;
          letter-spacing: 1px;
          text-transform: uppercase;
        }

        .value {
          font-weight: 700;
          font-size: 18px;
          line-height: 25px;
          margin-top: 2px;
        }
      }

      .stadium-station {
        grid-area: station;

        .label,
        .value {
          font-family: $font-avenir-next-condensed;
          font-style: normal;
        }

        .label {
          font-weight: 600;
          font-size: 16px;
          line-height: 22px;
          letter-spacing: 1px;
          text-transform: uppercase;
        }

        .value {
          font-weight: 400;
          font-size: 18px;
          line-height: 25px;
          margin-top: 2px;

          ul {
            list-style: inside;
          }
        }
      }
    }

    @media screen and (max-width: 1199px) {
      .stadium-info {
        .info-grid {
          grid-template-areas:
            'name name'
            'city city'
            'skeleton capacity'
            'address address'
            'station station';
        }
      }
    }

    @media screen and (max-width: 767px) {
      .stadium-info {
        .stadium-name {
          font-size: 48px;
          line-height: 64px;
        }

        .stadium-city {
          .icon {
            height: 32px;
          }

          .name {
            font-size: 32px;
            line-height: 50px;
          }
        }
      }
    }

    @media screen and (max-width: 576px) {
      .stadium-info {
        .info-grid {
          grid-template-areas:
            'name name'
            'city city'
            'skeleton skeleton'
            'capacity capacity'
            'address address'
            'station station';
        }
      }
    }
  }
}

body.lang-th {
  .club-profile-stadiums .stadium-info {
    .stadium-name,
    .stadium-city .name,
    .stadium-capacity .label,
    .stadium-capacity .value,
    .stadium-introduction,
    .stadium-address .label,
    .stadium-address .value,
    .stadium-station .label,
    .stadium-station .value {
      @include font-thai();
    }

    .stadium-name {
      font-weight: 700;
    }
  }
}

body.lang-vi {
  .club-profile-stadiums .stadium-info {
    .stadium-name,
    .stadium-city .name,
    .stadium-capacity .label,
    .stadium-capacity .value,
    .stadium-introduction,
    .stadium-address .label,
    .stadium-address .value,
    .stadium-station .label,
    .stadium-station .value {
      @include font-vietnamese();
    }

    .stadium-name,
    .stadium-capacity .capacity__value,
    .stadium-address .address__value,
    .stadium-station .station__value {
      font-weight: 600;
    }

    .stadium-address .address__label,
    .stadium-capacity .capacity__label,
    .stadium-station .station__label {
      font-weight: 400;
    }
  }
}
