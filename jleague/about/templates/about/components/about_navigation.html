{% load wagtailcore_tags core_tags %}

<div class="about-navigation">
  {% if items|length > 1 %}
  <ul class="about-navigation-list">
      {% for item in items %}
        {% if item.link_page_id and item.link_page %}
          <li class="about-navigation-list__item{% if request|active_route:item %} active{% endif %}">
            <a class="about-navigation-list__item__link" href="{% pageurl item.link_page.localized %}">
              {{ item.label }}
            </a>
          </li>
        {% elif item.link_external %}
          <li class="about-navigation-list__item">
            <a class="about-navigation-list__item__link" href="{{ item.link_external }}">
              {{ item.label }}
            </a>
          </li>
        {% endif %}
      {% endfor %}
  {% else %}
  </ul>
  {% endif %}
</div>
