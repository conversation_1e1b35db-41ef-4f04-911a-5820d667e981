import logging

from django.core.cache import cache
from django.db.models.expressions import OuterRef, Subquery
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from utils.helpers import get_display_data_year
from core.constants import LEAGUE_COMPETITIONS
from .models import ClubCompetition, ClubExtraInfo
from utils.helpers.slack import send_message
from core.models import AlertLog
from core.enums import (
    GameKindEnum,
)

_club_name_mappings = {
    "FCTokyo": "F.C.Tokyo",
    "Kyoto-Sanga-FC": "Kyoto Sanga F.C.",
    "Matsumoto-Yamaga-FC": "Matsumoto Yamaga F.C.",
    "Osaka-FC": "Osaka F.C.",
    "SC-Sagamihara": "S.C. Sagamihara",
    "Shimizu-S-Pulse": "Shimizu S-Pulse",
    "V-Varen-Nagasaki": "V-Varen Nagasaki",
    "YSCC-Yokohama": "Y.S.C.C. Yokohama",
    "Yokohama-F-Marinos": "Yokohama F･Marinos",
}

_ordered_club_slugs = {}

# List of objects representing clubs that sell tickets
# Predefined list of clubs that sell tickets with additional fields
_club_sell_ticket = [
    {
        "slug": "urawa",
        "name": "Urawa",
        "top": 679,
        "right": 648,
        "buy_tickets": "https://quick.pia.jp/urawareds_en/",
        "official_website_english": "https://www.urawa-reds.co.jp/en/"
    },
    {
        "slug": "ftokyo",
        "name": "FC TOKYO",
        "top": 733,
        "right": 648,
        "buy_tickets": "https://quick.pia.jp/fctokyo_en/",
        "official_website_english": "https://www.fctokyo.co.jp/en/"
    },
    {
        "slug": "tokyov",
        "name": "Tokyo Verdy",
        "top": 733,
        "right": 594,
        "buy_tickets": "https://quick.pia.jp/tokyoverdy_en/",
        "official_website_english": "https://www.verdy.co.jp/en/content/clubstadium/"
    },
    {
        "slug": "machida",
        "name": "FC Machida Zelvia",
        "top": 733,
        "right": 539,
        "buy_tickets": "https://quick.pia.jp/zelvia_en/",
        "official_website_english": "https://www.zelvia.co.jp/"
    },
    {
        "slug": "kawasakif",
        "name": "Kawasaki Frontale",
        "top": 0,
        "right": 0,
        "buy_tickets": "https://www.jleague.co/tickets/",
        "official_website_english": "https://www.frontale.co.jp/"
    },
    {
        "slug": "yokohamafm",
        "name": "Yokohama F･Marinos",
        "top": 787,
        "right": 648,
        "buy_tickets": "https://www.f-marinos.com/en/tickets",
        "official_website_english": "https://www.f-marinos.com/en/"
    },
    {
        "slug": "nagoya",
        "name": "Nagoya Grampus",
        "top": 0,
        "right": 0,
        "buy_tickets": "https://www.jleague.co/tickets/",
        "official_website_english": "https://nagoya-grampus.jp/"
    },
    {
        "slug": "shimizu",
        "name": "Shimizu S-Pulse",
        "top": 698,
        "right": 821,
        "buy_tickets": "https://quick.pia.jp/s-pulse_en/",
        "official_website_english": "https://www.s-pulse.co.jp/english"
    },
    {
        "slug": "kyoto",
        "name": "Kyoto Sanga F.C.",
        "top": 698,
        "right": 821,
        "buy_tickets": "https://quick.pia.jp/kyotosanga_en/",
        "official_website_english": "https://www.sanga-fc.jp/ticket/english_ticket_page"
    },
    {
        "slug": "gosaka",
        "name": "Gamba Osaka",
        "top": 761,
        "right": 875,
        "buy_tickets": "https://quick.pia.jp/gamba_en/",
        "official_website_english": "https://www.gamba-osaka.net/english/"
    },
    {
        "slug": "cosaka",
        "name": "Cerezo Osaka",
        "top": 761,
        "right": 933,
        "buy_tickets": "https://quick.pia.jp/cerezo_en/",
        "official_website_english": "https://www.cerezo.jp/en/"
    },
    {
        "slug": "niigata",
        "name": "Albirex Niigata",
        "top": 760,
        "right": 900,
        "buy_tickets": "https://quick.pia.jp/albirex_en/",
        "official_website_english": "https://www.albirex.co.jp/en/"
    },
    {
        "slug": "kobe",
        "name": "Vissel-Kobe",
        "top": 0,
        "right": 0,
        "buy_tickets": "https://quick.pia.jp/vissel_en/",
        "official_website_english": "https://www.vissel-kobe.co.jp/ensp/"
    },
    {
        "slug": "fukuoka",
        "name": "Avispa Fukuoka",
        "top": 0,
        "right": 0,
        "buy_tickets": "https://quick.pia.jp/avispa_en/",
        "official_website_english": "https://www.avispa.co.jp/"
    },
    # j2
    {
        "slug": "sapporo",
        "name": "Hokkaido Consadole Sapporo",
        "top": 0,
        "right": 0,
        "buy_tickets": "https://www.jleague.co/tickets/",
        "official_website_english": "https://www.consadole-sapporo.jp/"
    },
    {
        "slug": "sendai",
        "name": "Vegalta Sendai",
        "top": 0,
        "right": 0,
        "buy_tickets": "https://www.jleague.co/tickets/",
        "official_website_english": "https://www.vegalta.co.jp/"
    },
    {
        "slug": "oita",
        "name": "Oita Trinita",
        "top": 803,
        "right": 1029,
        "buy_tickets": "https://quick.pia.jp/trinita_en/",
        "official_website_english": "https://www.oita-trinita.co.jp/"
    },
    {
        "slug": "yamagata",
        "name": "Montedio Yamagata",
        "top": 0,
        "right": 0,
        "buy_tickets": "https://quick.pia.jp/montedio_en/",
        "official_website_english": "https://www.montedioyamagata.jp/"
    },
    # j3
    {
        "slug": "kanazawa",
        "name": "Zweigen Kanazawa",
        "top": 591,
        "right": 753,
        "buy_tickets": "https://quick.pia.jp/zweigen_en/",
        "official_website_english": "https://www.zweigen-kanazawa.jp/"
    },
    {
        "slug": "ryukyu",
        "name": "FC RYUKYU",
        "top": 303,
        "right": 1100,
        "buy_tickets": "https://quick.pia.jp/fcryukyu_en/",
        "official_website_english": "https://fcryukyu.com/"
    },
    {
        "slug": "chiba",
        "name": "JEF United Chiba",
        "top": 303,
        "right": 1100,
        "buy_tickets": "https://www.jleague.co/tickets/",
        "official_website_english": "https://jefunited.co.jp/en/"
    },
    {
        "slug": "yokohamafc",
        "name": "Yokohama FC",
        "top": 0,
        "right": 0,
        "buy_tickets": "https://quick.pia.jp/yokohamafc_en/",
        "official_website_english": "https://www.yokohamafc.com"
    },
    {
        "slug": "nara",
        "name": "nara Club",
        "top": 0,
        "right": 0,
        "buy_tickets": "https://quick.pia.jp/naraclub_en/?top=date",
        "official_website_english": "https://naraclub.jp/"
    },
    {
        "slug": "fosaka",
        "name": "FC Osaka",
        "top": 0,
        "right": 0,
        "buy_tickets": "https://quick.pia.jp/fcosaka_en/?top=date",
        "official_website_english": "https://www.fc-osaka.com/"
    }
]

logger = logging.getLogger(__name__)


def get_club_sell_ticket(competitions: list, year=None):
    """
    Filters clubs that sell tickets and returns their details with additional fields 'top' and 'right'.

    Args:
        clubs (list): List of club dictionaries with a 'slug' field.

    Returns:
        list: List of dictionaries with details of clubs that sell tickets.
    """
    year = year or get_display_data_year()
    cache_key = f"club_sell_ticket_{year}"
    result = cache.get(cache_key)
    if not result:
        for competition in competitions:
            sell_ticket_clubs = []
            for club in competition['clubs']:

                # Get the slug from the input club
                slug = club.get("slug")

                if not slug:
                    continue

                # Find the club in the `_club_sell_ticket` list
                sell_ticket_club = next((c for c in _club_sell_ticket if c["slug"] == slug), None)
                if sell_ticket_club:
                    # Add the matching club along with top and right fields without resetting
                    club["top"] = sell_ticket_club["top"]
                    club["right"] = sell_ticket_club["right"]
                    club["buy_tickets"] = sell_ticket_club["buy_tickets"]
                    club["official_website_english"] = sell_ticket_club["official_website_english"]
                    sell_ticket_clubs.append(club)
            competition['clubs'] = sell_ticket_clubs
        cache.set(cache_key, result, 60 * 60 * 24)
    return competitions


def get_club_name_by_name_slug(name_slug: str):
    name = _club_name_mappings.get(name_slug)
    if not name:
        name = name_slug.replace("-", " ")
    return name


def get_club_competition(club_slug: str, year: int):
    cache_key = f"club_competition_{club_slug}_{year}"
    result = cache.get(cache_key)
    if not result:
        club_competition = (
            ClubCompetition.objects.select_related("competition", "club")
            .filter(
                club__slug=club_slug,
                competition__slug__in=LEAGUE_COMPETITIONS,
                year=year,
            )
            .first()
        )
        result = {}
        if club_competition:
            result = club_competition.competition.format()
        cache.set(cache_key, result, 60 * 60 * 24)
    return result


def get_league_clubs(competition=None, year=None):
    competitions = [c for c in LEAGUE_COMPETITIONS if c != GameKindEnum.LEVAIN_CUP.value]
    year = year or get_display_data_year()

    if competition and competition in competitions:
        competitions = [competition]

    cache_key = f'league_clubs_{"-".join(competitions)}_{year}'
    results = cache.get(cache_key)
    if not results:
        # ClubExtraInfo get t_name field
        clubextrainfo_t_name_sq = ClubExtraInfo.objects.filter(
            club=OuterRef("club")
        ).values("t_name")[:1]

        club_competitions = list(
            ClubCompetition.objects.select_related("competition", "club")
            .annotate(t_name=Subquery(clubextrainfo_t_name_sq))
            .filter(competition__slug__in=competitions, year=year)
            .order_by("competition", "ordering")
        )

        results = {}

        for club_competition in club_competitions:
            c_slug = club_competition.competition.slug
            if results.get(c_slug) is None:
                results[c_slug] = {
                    "competition": club_competition.competition,
                    "clubs": [],
                }
            club = club_competition.club.format()
            club["t_name"] = club_competition.t_name
            results[c_slug]["clubs"].append(club)

        results = [v for k, v in results.items()]
        cache.set(cache_key, results, 60 * 60 * 24)
    return results


def get_competition_clubs(competition, year=None, id_only=False, slug_only=False):
    year = year or get_display_data_year()
    cache_key = f"competition_clubs_{competition}_{year}_{id_only}_{slug_only}"
    clubs = cache.get(cache_key)

    if not clubs:
        clubs = []
        clubextrainfo_t_name_sq = ClubExtraInfo.objects.filter(
            club=OuterRef("club")
        ).values("t_name")[:1]

        club_competitions = list(
            ClubCompetition.objects.select_related("club", "competition")
            .annotate(t_name=Subquery(clubextrainfo_t_name_sq))
            .filter(competition__slug=competition, year=year)
            .order_by("ordering")
        )

        for club_competition in club_competitions:
            if id_only:
                clubs.append(club_competition.club.id)
            elif slug_only:
                clubs.append(club_competition.club.slug)
            else:
                club_competition.club.t_name = club_competition.t_name
                clubs.append(club_competition.club)

        cache.set(cache_key, clubs)

    return clubs


def get_ordered_clubs(year: int = None):
    year = year or get_display_data_year()
    if _ordered_club_slugs.get(year) is None:
        _ordered_club_slugs[year] = []
        for competition in LEAGUE_COMPETITIONS:
            _ordered_club_slugs[year] += get_competition_clubs(
                competition, year, slug_only=True
            )
    return _ordered_club_slugs[year]


def send_alert_and_create_log(club_info):
    """
    Sends an alert message and creates a log entry if the club information does not match the expected data.

    Args:
        club_info (dict): A dictionary containing the club information.

    Returns:
        None
    """
    if isinstance(club_info, dict) and club_info.get("club") is None:
        return

    club = club_info.get("club")

    conditions = {
        "name": club.get("name") != club.get("t_name", {}).get("en", ""),
        "president": club.get("president") != club.get("t_president", {}).get("en", ""),
        "address": club.get("address") != club.get("t_address", {}).get("en", ""),
        "practice_ground": club.get("practice_ground")
                           != club.get("t_practice_ground", {}).get("en", ""),
    }

    related_content = f'club_info_{club.get("name_slug")}'

    last_alert = (
        AlertLog.objects.filter(related_content=related_content)
        .order_by("-created_at")
        .first()
    )

    if last_alert is not None and last_alert.created_at - timezone.now() < timedelta(
            minutes=240
    ):
        return

    fields = []
    for field, condition in conditions.items():
        if condition:
            fields.append(field)

    if not fields:
        return

    link = reverse("club_profile", args=[club.get("name_slug")])

    if "jleague.co" not in link:
        link = f"https://jleague.co{link}"

    blocks = [
        {
            "type": "section",
            "text": {
                "type": "mrkdwn",
                "text": f"Data unmatch detected, Please check the following fields: {', '.join(f'`{field}`' for field in fields)} from <{link}|here>",
            },
        },
    ]

    res = send_message(blocks=blocks)
    if res is not None and res["ok"]:
        AlertLog.objects.create(
            message=res["message"]["text"],
            level="warning",
            related_content=related_content,
            alert_channel="slack",
        )
