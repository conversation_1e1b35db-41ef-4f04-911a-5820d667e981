@import 'setup/variable';
@import 'setup/mixin';

.view-more-button {
  display: inline-block;
  margin: 40px auto 80px auto;
  padding: 10px 24px;
  border: 1px solid $color-black;
  border-radius: 20px;
  font-family: $font-barlow-condensed;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 20px;
  text-align: center;
  letter-spacing: 1.92px;
  text-transform: uppercase;
  color: $color-black;
  transition: all 0.3s linear;

  &:hover {
    opacity: 1;
    border-color: $color-red;
    background: $color-red;
    color: $color-white;
  }

  @media screen and (max-width: 425px) {
    margin: 24px 0 0;
  }
}

body.lang-id {
  .view-more-button {
    font-size: 15px;
    letter-spacing: normal;
    border-radius: 25px;
  }
}

body.lang-th {
  .view-more-button {
    @include font-thai();
    border-radius: 25px;
  }
}

body.lang-vi {
  .view-more-button {
    @include font-vietnamese();
    font-size: 14px;
    border-radius: 25px;
  }
}
