import aioftp
import logging
import xml.etree.ElementTree as ET
from clubs.models import Club
from match.models import Game
from stats.models import ClubStats
from ..helpers import async_find_first, async_upsert, process_path_if_needed, read_file_to_str, xml_value

logger = logging.getLogger(__name__)


async def import_live_team_details(ftp_client: aioftp.Client, file_path: str, file_stat: dict):  # noqa
    async with process_path_if_needed(file_path, file_stat) as should_process:
        if not should_process:
            return
        logger.info('Importing live_team_details.xml, but not implemented yet.')

        # # read XML file
        # file_content = await read_file_to_str(ftp_client, file_path)
        # root = ET.fromstring(file_content)  # Football node
        # report_node = root.find('StatsReport')
        # game_id = xml_value(report_node, 'GameID', int)
        # game: Game = await async_find_first(Game, id=game_id, select_related=('competition',))

        # if not game:
        #     logger.warn('Game not found (id=%s)', game_id)
        #     return

        # competition = game.competition

        # year = int(str(game_id)[:4])

        # for team_info_node in report_node.findall('TeamInfo'):
        #     team_id = xml_value(team_info_node, 'ID', int)
        #     club = await async_find_first(Club, ds_club_id=team_id)
        #     if not club:
        #         logger.warn('Team not found year=%s team_id=%s', year, team_id)
        #         continue

        #     team_stats = {
        #         'clean_sheets': xml_value(team_info_node, 'CleanSheet', int)
        #     }

        #     if team_stats['clean_sheets'] is not None:
        #         await async_upsert(
        #             ClubStats,
        #             year=year,
        #             club=club,
        #             competition=competition,
        #             defaults=team_stats
        #         )

        #    logger.info('Processed Club details record %d', team_id)
