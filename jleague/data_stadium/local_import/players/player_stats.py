import logging
from decimal import Decimal
from clubs.models import Club, ClubPlayer, ClubCompetition
from core.models import Competition
from players.models import Player
from stats.models import PlayerStats
from ..helpers import (
    # async_delete,
    async_find_first,
    async_upsert,
    read_xml_file,
    xml_value,
)

logger = logging.getLogger(__name__)

# player_Stats_details-
_ds_mapping_player_stats = {
    'aerial_duel_win_rate': 'AirBattleWinRate',
    'aerial_duel_win_rate_rank_league': 'AirBattleWinRateL',
    'aerial_duels_won': 'AirBattleWinCount',
    'aerial_duels_won_rank_league': 'AirBattleWinCountRankL',
    'aerial_duels_won_rank_team': 'AirBattleWinCountRank',
    'assists': 'Assist',
    'assists_rank_league': 'AssistRankL',
    'assists_rank_team': 'AssistRank',
    'attacking_third_sprints': 'AtSprint',
    'attacking_third_sprints_rank_league': 'AtSprintCountRankL',
    'attacking_third_sprints_rank_team': 'AtSprintCountRank',
    'big_chances': 'BigChance',
    'big_chances_rank_league': 'BigChanceRankL',
    'big_chances_rank_team': 'BigChanceRank',
    'blocks': 'BlockCount',
    'blocks_rank_league': 'BlockCountRankL',
    'blocks_rank_team': 'BlockCountRank',
    'chances_created': 'ChanceCreate',
    'chances_created_pg': 'ChanceCreatePG',
    'chances_created_pg_rank_league': 'ChanceCreatePGRankL',
    'chances_created_rank_league': 'ChanceCreateRankL',
    'chances_created_rank_team': 'ChanceCreateRank',
    'clean_sheets': 'CleanSheet',
    'clean_sheets_rank_league': 'CleanSheetRankL',
    'clearances': 'ClearCount',
    'clearances_rank_league': 'ClearCountRankL',
    'clearances_rank_team': 'ClearCountRank',
    'corner_kicks': 'CK',
    'corner_kicks_rank_league': 'CKRankL',
    'corner_kicks_rank_team': 'CKRank',
    'cross_catch_rate': 'CrossCatchRate',
    'cross_catch_rate_rank_league': 'CrossCatchRateRankL',
    'cross_punch_rate': 'CrossPunchRate',
    'cross_punch_rate_rank_league': 'CrossPunchRateRankL',
    'cross_rate': 'CrossRate',
    'crosses': 'CrossCount',
    'crosses_pg': 'CrossCountPG',
    'crosses_pg_rank_league': 'CrossCountPGRankL',
    'crosses_rank_league': 'CrossCountRankL',
    'crosses_rank_team': 'CrossCountRank',
    'defensive_third_sprints': 'DtSprint',
    'defensive_third_sprints_rank_league': 'DtSprintCountRankL',
    'defensive_third_sprints_rank_team': 'DtSprintCountRank',
    'distance': 'Distance',
    'distance_rank_league': 'DistanceRankL',
    'distance_rank_team': 'DistanceRank',
    'dribble_rate': 'DribbleRate',
    'dribbles': 'DribbleCount',
    'dribbles_rank_league': 'DribbleCountRankL',
    'dribbles_rank_team': 'DribbleCountRank',
    'duels_won': 'DuelsWon',
    'duels_won_rank_league': 'DuelsWonRankL',
    'duels_won_rank_team': 'DuelsWonRank',
    'expected_goals': 'ExpectedGoals',
    'expected_goals_diff': 'ExpectedGoalsDiff',
    'expected_goals_diff_rank_league': 'ExpectedGoalsDiffRankL',
    'expected_goals_diff_rank_team': 'ExpectedGoalsDiffRank',
    'expected_goals_excl_pk': 'ExpectedGoalsExclPK',
    'expected_goals_excl_pk_rank_league': 'ExpectedGoalsExclPKRankL',
    'expected_goals_excl_pk_rank_team': 'ExpectedGoalsExclPKRank',
    'expected_goals_rank_league': 'ExpectedGoalsRankL',
    'expected_goals_rank_team': 'ExpectedGoalsRank',
    'fouls': 'FoulCount',
    'fouls_rank_league': 'FoulCountRankL',
    'fouls_rank_team': 'FoulCountRank',
    'free_kick_goals': 'FKScore',
    'free_kick_goals_rank_league': 'FKScoreRankL',
    'free_kick_goals_rank_team': 'FKScoreRank',
    'free_kicks': 'FK',
    'free_kicks_rank_league': 'FKRankL',
    'free_kicks_rank_team': 'FKRank',
    'games_played': 'Game',
    'goals': 'Score',
    'goals_conceded': 'Lost',
    'goals_conceded_rank_league': 'LostRankL',
    # 'goals_pg':'', # doesn't exist on the file from DS
    'goals_rank_league': 'ScoreRankL',
    'goals_rank_team': 'ScoreRank',
    'head_goals': 'HeadScore',
    'head_goals_rank_league': 'HeadScoreRankL',
    'head_goals_rank_team': 'HeadScoreRank',
    'interceptions': 'InterceptCount',
    'interceptions_pg': 'InterceptCountPG',
    'interceptions_pg_rank_league': 'InterceptCountPGRankL',
    'interceptions_rank_league': 'InterceptCountRankL',
    'interceptions_rank_team': 'InterceptCountRank',
    'left_foot_goals': 'LeftFootScore',
    'left_foot_goals_rank_league': 'LeftFootScoreRankL',
    'left_foot_goals_rank_team': 'LeftFootScoreRank',
    'long_pass_rate': 'LongPassRate',
    'long_pass_rate_rank_league': 'LongPassRateRankL',
    'long_passes': 'LongPassCount',
    'long_passes_pg': 'LongPassCountPG',
    'long_passes_pg_rank_league': 'LongPassCountPGRankL',
    'long_passes_rank_league': 'LongPassCountRankL',
    'long_passes_rank_team': 'LongPassCountRank',
    'middle_third_sprints': 'MtSprint',
    'middle_third_sprints_rank_league': 'MtSprintCountRankL',
    'middle_third_sprints_rank_team': 'MtSprintCountRank',
    'minutes_played': 'Time',
    'one_on_one': 'OneonOne',
    'one_on_one_rank_league': 'OneonOneRankL',
    'one_on_one_rank_team': 'OneonOneRank',
    'opponent_area_pass_rate': 'OpponentAreaPassRate',
    'opponent_area_passes': 'OpponentAreaPassCount',
    'opponent_area_passes_pg': 'OpponentAreaPassCountPG',
    'opponent_area_passes_pg_rank_league': 'OpponentAreaPassCountPGRankL',
    'opponent_area_passes_rank_league': 'OpponentAreaPassCountRankL',
    'opponent_area_passes_rank_team': 'OpponentAreaPassCountRank',
    'other_type_goals': 'OtherTypeScore',
    'other_type_goals_rank_league': 'OtherTypeScoreRankL',
    'other_type_goals_rank_team': 'OtherTypeScoreRank',
    'own_area_pass_rate': 'OwnAreaPassRate',
    'own_area_passes': 'OwnAreaPassCount',
    'own_area_passes_pg': 'OwnAreaPassCountPG',
    'own_area_passes_pg_rank_league': 'OwnAreaPassCountPGRankL',
    'own_area_passes_rank_league': 'OwnAreaPassCountRankL',
    'own_area_passes_rank_team': 'OwnAreaPassCountRank',
    'pass_rate': 'PassRate',
    'passes': 'PassCount',
    'passes_pg': 'PassCountPG',
    'passes_pg_rank_league': 'PassCountPGRankL',
    'passes_rank_league': 'PassCountRankL',
    'passes_rank_team': 'PassCountRank',
    'penalty_kick_goals': 'PKScore',
    'penalty_kick_goals_rank_league': 'PKScoreRankL',
    'penalty_kick_goals_rank_team': 'PKScoreRank',
    'plays': 'PlayCount',
    'plays_pg': 'PlayCountPG',
    'plays_pg_rank_league': 'PlayCountPGRankL',
    'plays_rank_league': 'PlayCountRankL',
    'plays_rank_team': 'PlayCountRank',
    'possession_distance': 'PossessionDistance',
    'possession_distance_rank_league': 'PossessionDistanceRankL',
    'possession_distance_rank_team': 'PossessionDistanceRank',
    'possession_sprints': 'PossessionSprint',
    'possession_sprints_rank_league': 'PossessionSprintCountRankL',
    'possession_sprints_rank_team': 'PossessionSprintCountRank',
    'recoveries': 'RecoveryCount',
    'recoveries_rank_league': 'RecoveryCountRankL',
    'recoveries_rank_team': 'RecoveryCountRank',
    'red_cards': 'RedCount',
    'red_cards_rank_league': 'RedCountRankL',
    'red_cards_rank_team': 'RedCountRank',
    'right_foot_goals': 'RightFootScore',
    'right_foot_goals_rank_league': 'RightFootScoreRankL',
    'right_foot_goals_rank_team': 'RightFootScoreRank',
    'save_catch_rate_in_pa': 'SaveCatchRateInPA',
    'save_catch_rate_in_pa_rank_league': 'SaveCatchRateInPARankL',
    'save_catch_rate_out_pa': 'SaveCatchRateOutPA',
    'save_catch_rate_out_pa_rank_league': 'SaveCatchRateOutPARankL',
    'save_punch_rate_in_pa': 'SavePunchRateInPA',
    'save_punch_rate_in_pa_rank_league': 'SavePunchRateInPARankL',
    'save_punch_rate_out_pa': 'SavePunchRateOutPA',
    'save_punch_rate_out_pa_rank_league': 'SavePunchRateOutPARankL',
    'save_rate': 'SaveRate',
    'save_rate_in_pa': 'SaveRateInPA',
    'save_rate_in_pa_rank_league': 'SaveRateInPARankL',
    'save_rate_out_pa': 'SaveRateOutPA',
    'save_rate_out_pa_rank_league': 'SaveRateOutPARankL',
    'saves': 'SaveCount',
    'saves_pg': 'SaveCountPG',
    'saves_pg_rank_league': 'SaveCountPGRankL',
    'saves_rank_league': 'SaveCountRankL',
    'shoot_rate': 'ShootRate',
    'shoot_rate_rank_league': 'ShootRateRankL',
    'shots': 'Shoot',
    'shots_on_target': 'ShootOnTarget',
    'shots_on_target_rank_league': 'ShootOnTargetRankL',
    'shots_on_target_rank_team': 'ShootOnTargetRank',
    'shots_pg': 'ShootPG',
    'shots_pg_rank_league': 'ShootPGRankL',
    'shots_rank_league': 'ShootRankL',
    'shots_rank_team': 'ShootRank',
    'sprints': 'Sprint',
    'sprints_rank_league': 'SprintCountRankL',
    'sprints_rank_team': 'SprintCountRank',
    'suffer_fouls': 'SufferFoulCount',
    'suffer_fouls_rank_league': 'SufferFoulCountRankL',
    'suffer_fouls_rank_team': 'SufferFoulCountRank',
    'suffer_shots': 'SufferShoot',
    'suffer_shots_on_target': 'SufferShootOnTarget',
    'suffer_shots_on_target_rank_league': 'SufferShootOnTargetRankL',
    'tackle_rate': 'TackleRate',
    'tackle_rate_rank_league': 'TackleRateRankL',
    'tackles': 'TackleCount',
    'tackles_pg': 'TackleCountPG',
    'tackles_pg_rank_league': 'TackleCountPGRankL',
    'tackles_rank_league': 'TackleCountRankL',
    'tackles_rank_team': 'TackleCountRank',
    'through_ball_rate': 'ThroughPassRate',
    'through_balls': 'ThroughPassCount',
    'through_balls_rank_league': 'ThroughPassCountRankL',
    'through_balls_rank_team': 'ThroughPassCountRank',
    'top_speed': 'TopSpeed',
    'top_speed_rank_league': 'TopSpeedRankL',
    'top_speed_rank_team': 'TopSpeedRank',
    'unpossession_distance': 'UnPossessionDistance',
    'unpossession_distance_rank_league': 'UnPossessionDistanceRankL',
    'unpossession_distance_rank_team': 'UnPossessionDistanceRank',
    'unpossession_sprints': 'UnPossessionSprint',
    'unpossession_sprints_rank_league': 'UnPossessionSprintCountRankL',
    'unpossession_sprints_rank_team': 'UnPossessionSprintCountRank',
    'yellow_cards': 'YellowCount',
    'yellow_cards_rank_league': 'YellowCountRankL',
    'yellow_cards_rank_team': 'YellowCountRank',
}

_decimal_value_stats = (
    'aerial_duel_win_rate',
    'chances_created_pg',
    'cross_catch_rate',
    'cross_punch_rate',
    'cross_rate',
    'crosses_pg',
    'distance',
    'dribble_rate',
    'expected_goals',
    'expected_goals_diff',
    'expected_goals_excl_pk',
    'interceptions_pg',
    'long_pass_rate',
    'long_passes_pg',
    'opponent_area_pass_rate',
    'opponent_area_passes_pg',
    'own_area_pass_rate',
    'own_area_passes_pg',
    'pass_rate',
    'passes_pg',
    'plays_pg',
    'possession_distance',
    'save_catch_rate_in_pa',
    'save_catch_rate_out_pa',
    'save_punch_rate_in_pa',
    'save_punch_rate_out_pa',
    'save_rate',
    'save_rate_in_pa',
    'save_rate_out_pa',
    'saves_pg',
    'shoot_rate',
    'shots_pg',
    'tackle_rate',
    'tackles_pg',
    'through_ball_rate',
    'top_speed',
    'unpossession_distance',
)

# custom stats (similar to {stat}_pg but manually calculate based on minutes_played)
_pg_stats = ('goals',)
_90min_stats = (
    'chances_created',
    'crosses',
    'goals',
    'interceptions',
    'long_passes',
    'opponent_area_passes',
    'own_area_passes',
    'passes',
    'plays',
    'saves',
    'shots',
    'tackles',
)


def _is_decimal_value_stat(stat: str):
    return True if stat in _decimal_value_stats else False


def _get_stat_value_type(stat: str):
    return Decimal if _is_decimal_value_stat(stat) else int


def _calculate_stat_per_game(player_stats):
    games_played = player_stats.get('games_played')
    if games_played and games_played > 0:
        for stat in _pg_stats:
            stat_pg = f'{stat}_pg'
            stat_val = player_stats.get(stat)
            if stat_val and stat_val > 0:
                player_stats[stat_pg] = round(stat_val / games_played, 1)
            else:
                player_stats[stat_pg] = 0
    return player_stats


def _calculate_stat_per_90min(player_stats):
    minutes_played = player_stats.get('minutes_played')
    if minutes_played and minutes_played >= 90:
        for stat in _90min_stats:
            stat_90min = f'{stat}_90min'
            stat_val = player_stats.get(stat)
            if stat_val and stat_val > 0:
                player_stats[stat_90min] = round(stat_val / (minutes_played / 90.0), 1)
            else:
                player_stats[stat_90min] = 0
    return player_stats


async def import_players_stats(file_path: str, data_year: int):
    """Import team player stats from DS
    The file which outputs detailed player stats of the season.
    source file: /total/player/player_Stats_details-{COMPETITION_ID}_{DS_CLUB_ID}.xml
    """
    # read XML file
    root = await read_xml_file(file_path)
    details_report_node = root.find('PlayerStatsDetailsReport')
    year = xml_value(details_report_node, 'Year', int)
    if year != data_year:
        return

    club_id = xml_value(details_report_node, 'TeamID', int)
    competition_id = xml_value(details_report_node, 'GameKindID', int)

    # check club exists
    club = await async_find_first(Club, ds_club_id=club_id)
    if not club:
        logger.warn('Club not found (ds_club_id=%s)', club_id)
        return

    # check competition exists
    competition = await async_find_first(Competition, id=competition_id)
    if not competition:
        logger.warn('Competition not found (id=%s)', competition_id)
        return

    # process player stats
    stat_keys = _ds_mapping_player_stats.keys()
    for player_node in details_report_node.findall('PlayerInfo'):
        # check player exists
        player_id = xml_value(player_node, 'PlayerID', int)
        player = await async_find_first(Player, ds_player_id=player_id)
        if not player:
            # player does not exist, skipped
            logger.warn('Player not found (ds_player_id=%s)', player_id)

            # # delete player stats if exists
            # await async_delete(
            #     PlayerStats,
            #     year=year,
            #     competition=competition,
            #     club=club,
            #     player__ds_player_id=player_id
            # )
            continue

        # dynamically collect player stats
        player_stats = {}
        for stat in stat_keys:
            ds_key = _ds_mapping_player_stats.get(stat)
            if ds_key is not None:
                value_type = _get_stat_value_type(stat)
                player_stats[stat] = xml_value(player_node, ds_key, value_type)

        # calculate per game stats
        player_stats = _calculate_stat_per_game(player_stats)

        # calculate 90min stats
        player_stats = _calculate_stat_per_90min(player_stats)

        # update player stats
        await async_upsert(
            PlayerStats, year=year, competition=competition, club=club, player=player, defaults=player_stats
        )
        # logger.info('Processed player stats (ds_player_id=%s)', player_id)
