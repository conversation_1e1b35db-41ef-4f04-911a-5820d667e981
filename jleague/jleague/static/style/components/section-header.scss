@import 'setup/variable';
@import 'setup/mixin';

.section-header {
  background: var(--color-black);
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80px;
  padding: 0 24px;
  position: relative;

  .section-header__title {
    @include section-header-title-text();
  }

  .section-header__league-logo {
    height: 40px;

    img {
      height: 100%;
      width: auto;
    }
  }

  &::before,
  &::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 8px;
    background-image: linear-gradient(180deg, var(--color-red) 0%, var(--color-black) 100%);
    background-repeat: no-repeat;
    background-size: cover;
  }

  &::before {
    left: 0;
  }

  &::after {
    right: 0;
  }

  &.small {
    height: 64px;

    .section-header__title {
      font-size: 32px;
      line-height: 32px;
    }

    .section-header__league-logo {
      height: 32px;
    }
  }

  &.section-header--with-logo {
    justify-content: space-between;
  }

  @media screen and (max-width: 991px) {
    .section-header__title {
      font-size: 32px;
      line-height: 32px;
    }

    .section-header__league-logo {
      height: 32px;
    }
  }

  @media screen and (max-width: 767px) {
    height: 64px;
  }

  @media screen and (max-width: 576px) {
    .section-header__title {
      font-size: 24px;
      line-height: 24px;
    }

    .section-header__league-logo {
      height: 26px;
    }

    &.small {
      .section-header__title {
        font-size: 24px;
        line-height: 24px;
      }

      .section-header__league-logo {
        height: 26px;
      }
    }
  }
}

// body.lang-th {
//   .section-header .section-header__title {
//     @include font-thai();
//   }
// }

body.lang-vi {
  .section-header.small .section-header__title {
    // @include font-vietnamese();
    font-size: 28px;
    line-height: 28px;
  }

  // .section-header .section-header__title {
  //   @include font-vietnamese();
  // }
}
