@import 'setup/variable';

.team-bg-img {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;

  @each $club in $clubs {
    &.team-bg-img--#{$club} {
      @if $club == gifu {
        &.team-bg-img--small {
          background-image: url('#{$club-sm-bg-path}#{$club}.svg');
        }

        &.team-bg-img--large {
          background-image: url('#{$club-lg-bg-path}#{$club}.png');
        }
      } @else {
        &.team-bg-img--small {
          background-image: url('#{$club-sm-bg-path}#{$club}.svg');
        }

        &.team-bg-img--large {
          background-image: url('#{$club-lg-bg-path}#{$club}.svg');
        }
      }
    }
  }
}
