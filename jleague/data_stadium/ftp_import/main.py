import aioftp
import logging
import requests
from asgiref.sync import sync_to_async
from datetime import datetime, timedelta
from django.conf import settings
from .clubs.game_club_stats import (
    import_game_club_stat_distance,
    import_game_club_stat_sprints,
)
from .clubs.club_stats import (
    import_club_avg_stats,
    import_club_stats,
    import_club_stat_details,
)
from .clubs.clubs import import_clubs, import_club_competitions
from .game_kinds import import_game_kinds
from .helpers import parse_stat_datetime, process_path_if_needed, remote_path
from .live.game import import_live_game
from .live.player import import_live_player
from .live.stats import import_live_stats

# from .live.team_details import import_live_team_details
from .live.formation import import_live_formation
from .players.game_player_stats import (
    import_game_player_stats,
    import_game_player_stats_live,
    import_game_player_stat_distance,
    import_game_player_stat_sprints,
)
from .players.player_stats import (
    import_players_stats,
    import_player_goals_stat,
)
from .players.players import (
    import_players,
    import_players_master_info,
    import_players_additional_info,
)
from .ranking import import_team_rankings
from .schedule import (
    import_schedule,
    import_last_5_games,
    import_game_records,
)
from .stadiums import import_stadiums
from ..models import PathSyncLog

logger = logging.getLogger(__name__)


def remote_files_to_last_modification_mapping(remote_files):
    return {str(x[0]): parse_stat_datetime(x[1]["modify"]) for x in remote_files}


@sync_to_async
def fetch_synced_files_last_modified_mapping(paths):
    result = {}
    chunk_size = 50
    chunks = [paths[i : i + chunk_size] for i in range(0, len(paths), chunk_size)]
    """
    chunks = [
        [
            '/2021111414/live_formation.xml',
            '/2021111414/live_game.xml',
            '/2021111414/live_player.xml',
            '/2021111414/live_stats.xml',
            '/2021111414/live_stats_details.xml',
            '/2021111414/live_team_details.xml'
        ]
    ]
    """
    # loop 50 paths per round
    for chunk in chunks:
        path_sync_logs = (
            PathSyncLog.objects.filter(path__in=chunk)
            .values("path", "last_modified_at")
            .all()
        )
        """
        path_sync_logs = [
            {'path': '/2021111414/live_game.xml', 'last_modified_at': datetime.datetime(2021, 11, 13, 21, 29, tzinfo=<UTC>)},
            {'path': '/2021111414/live_player.xml', 'last_modified_at': datetime.datetime(2021, 11, 13, 21, 29, tzinfo=<UTC>)},
            {'path': '/2021111414/live_stats.xml', 'last_modified_at': datetime.datetime(2021, 11, 13, 21, 29, tzinfo=<UTC>)},
            {'path': '/2021111414/live_stats_details.xml', 'last_modified_at': datetime.datetime(2021, 11, 16, 0, 16, tzinfo=<UTC>)},
            {'path': '/2021111414/live_team_details.xml', 'last_modified_at': datetime.datetime(2021, 11, 16, 0, 16, tzinfo=<UTC>)}
        ]
        """
        for path_sync_log in path_sync_logs:
            result[path_sync_log["path"]] = path_sync_log["last_modified_at"]
        """
        result = {
            '/2021111414/live_game.xml': datetime.datetime(2021, 11, 13, 21, 29, tzinfo=<UTC>),
            '/2021111414/live_player.xml': datetime.datetime(2021, 11, 13, 21, 29, tzinfo=<UTC>),
            '/2021111414/live_stats.xml': datetime.datetime(2021, 11, 13, 21, 29, tzinfo=<UTC>),
            '/2021111414/live_stats_details.xml': datetime.datetime(2021, 11, 16, 0, 16, tzinfo=<UTC>),
            '/2021111414/live_team_details.xml': datetime.datetime(2021, 11, 16, 0, 16, tzinfo=<UTC>)
        }
        """
    return result


def select_files_to_resync(remote_file_last_modified_at, synced_file_last_modified_at):
    """Gets out of date file paths

    Compares last_modified_at between two dicts: PathSyncLog (local) and DataStadium (remote)

    Args:
        remote_file_last_modified_at: A dict of path and last_modified_at
        synced_file_last_modified_at: A dict of path and last_modified_at

    Returns:
        A list of paths
    """
    files_to_resync = []
    for remote_file_path, last_modified_at in remote_file_last_modified_at.items():
        local_last_modified_at = synced_file_last_modified_at.get(remote_file_path)
        if local_last_modified_at is None or local_last_modified_at < last_modified_at:
            files_to_resync.append(remote_file_path)
    return files_to_resync


def file_execution_plan(path: str):  # noqa
    """Maps the XML file to the execution function

    Set priortiy and execution function based on file name

    Args:
        path: A path to the data stadium xml file

    Returns:
        A tuple of priority, path, execution function
    """
    file_name: str = path.split("/")[-1]
    priority = 999
    executor = None
    if file_name.startswith("mst_gamekind.xml"):
        priority = 1
        executor = import_game_kinds
    elif file_name.startswith("mst_stadium.xml"):
        priority = 2
        executor = import_stadiums
    elif file_name.startswith("team_directory-"):
        priority = 3
        executor = import_clubs
    elif file_name.startswith("mst_team-"):
        priority = 4
        executor = import_club_competitions
    elif file_name.startswith("mst_player_"):
        priority = 5
        executor = import_players_master_info
    elif file_name.startswith("directory-"):
        priority = 6
        executor = import_players
    elif file_name.startswith("directory_add-"):
        priority = 7
        executor = import_players_additional_info
    elif file_name.startswith("player_Stats_details-"):
        priority = 8
        executor = import_players_stats
    elif file_name.startswith("schedule-"):
        priority = 9
        executor = import_schedule
    elif file_name.startswith("team_5thGame-"):
        priority = 10
        executor = import_last_5_games
    elif file_name.startswith("match_record-"):
        priority = 11
        executor = import_game_records
    elif file_name.startswith("rank_goal-"):
        priority = 12
        executor = import_player_goals_stat
    elif file_name.startswith("rank_tracking_distance-"):
        priority = 13
        executor = import_game_player_stat_distance
    elif file_name.startswith("rank_tracking_sprint-"):
        priority = 14
        executor = import_game_player_stat_sprints
    elif file_name.startswith("rank_tracking_distance_team-"):
        priority = 15
        executor = import_game_club_stat_distance
    elif file_name.startswith("rank_tracking_sprint_team-"):
        priority = 16
        executor = import_game_club_stat_sprints
    elif file_name.startswith("tracking_team_average-"):
        priority = 17
        executor = import_club_avg_stats
    elif file_name.startswith("team_Stats-"):
        priority = 18
        executor = import_club_stats
    elif file_name == "live_game.xml":  # * run_live_import::import_games
        priority = 19
        executor = import_live_game
    elif file_name == "live_player.xml":  # * run_live_import::import_games
        priority = 20
        executor = import_live_player
    elif file_name.startswith("rank_team-"):
        priority = 21
        executor = import_team_rankings
    elif file_name.startswith("Participate_Stats-"):
        priority = 22
        executor = import_game_player_stats
    elif file_name == "live_stats_details.xml":  # * run_live_import::import_games
        priority = 23
        executor = import_game_player_stats_live
    elif file_name == "live_stats.xml":  # * run_live_import::import_games
        priority = 24
        executor = import_live_stats
    # NOTE: no need right now
    # elif file_name == 'live_team_details.xml':  # * run_live_import::import_games
    #     priority = 25
    #     executor = import_live_team_details
    elif file_name.startswith("team_Stats_details-"):
        priority = 25
        executor = import_club_stat_details
    elif file_name == "live_formation.xml":  # * run_live_import::import_games
        priority = 26
        executor = import_live_formation

    return (priority, path, executor)


async def process_folder(
    ftp_client: aioftp.Client,
    folder_path: str,
    folder_stat: dict,
    always_check_children=False,
):
    """Handle the data stadium folder

    Check the data stadium xml files in a folder and manage the files

    Args:
        ftp_client: A dict of path and last_modified_at
        folder_path: A path of the data stadium directory
        folder_stat: A dict of the data stadium directory stat
        always_check_children: Like recursive

    Returns:
        None
    """
    # * check if it already up to date
    async with process_path_if_needed(
        folder_path, folder_stat, force=always_check_children
    ) as should_process:
        if not should_process:
            # ! already up to date, skipped
            return
        # * [1] list of items under the folder_path
        remote_files = await ftp_client.list(folder_path)
        # * [2] dict of path:stat
        path_stat = {str(x[0]): x[1] for x in remote_files}  # noqa
        # * [3] dict of path:last_modified_at
        remote_file_last_modified_at = remote_files_to_last_modification_mapping(
            remote_files
        )
        # * [4] list of path from [3]
        remote_file_paths = list(remote_file_last_modified_at.keys())
        # * [5] dict of path:last_modified_at from PathSyncLog
        synced_file_last_modified_at = await fetch_synced_files_last_modified_mapping(
            remote_file_paths
        )
        # * [6] compare and get list of out of date path
        files_to_resync = select_files_to_resync(
            remote_file_last_modified_at, synced_file_last_modified_at
        )
        # * [7] loop list of files and select what to do...and sorted by priority
        execution_plans = sorted(
            [file_execution_plan(path) for path in files_to_resync],
            key=lambda x: x[0],  # sort by sync priority
        )
        # * [8] loop the list from [7] and excute them
        for _, path, executor in execution_plans:
            if executor is None:
                logger.warn("Executor for path %s is not implemented yet", path)
            else:
                await executor(ftp_client, path, path_stat[path])


async def import_sub_files(
    ftp_client: aioftp.Client, parent_folder_path, always_check_children
):
    remote_parent_folder_path = remote_path(parent_folder_path)
    parent_stat = await ftp_client.stat(remote_parent_folder_path)
    await process_folder(
        ftp_client, remote_parent_folder_path, parent_stat, always_check_children
    )


def is_general_folder_path(x: str):
    return (
        x.endswith("/master")
        or x.endswith("/total")  # noqa
        or x.endswith("/ranking")  # noqa
        or x.endswith("/real")  # noqa
        or x.endswith("/test")  # noqa
    )


async def import_games(
    ftp_client: aioftp.Client, always_check_children, live_only=False
):
    now = datetime.now()
    live_dates_to_import = {
        datetime.strftime(d, "%Y%m%d")
        for d in (
            now - timedelta(hours=24),
            now,
            now + timedelta(hours=24),
        )
    }
    remote_folder_path = remote_path("/")
    all_remote_files = await ftp_client.list(remote_folder_path)
    """
    live_dates_to_import = {'20211114', '20211116', '20211115'}
    needed_remote_paths = ['/2021041001']
    """
    needed_remote_paths = [
        x for x in all_remote_files if not is_general_folder_path(str(x[0]))
    ]
    for path, stat in needed_remote_paths:
        if live_only is False or str(path)[1:9] in live_dates_to_import:
            await process_folder(ftp_client, str(path), stat, always_check_children)


async def bootstrap_ftp_client():
    # TODO: Inherit aioftp.Client to skip MLSD/MLST commands as they are not supported by the server
    if settings.DS_INTEGRATION_USE_SOCKS_PROXY:
        logger.debug(
            "DS Integration: using socks proxy %s:%d V%d",
            settings.DS_INTEGRATION_SOCKS_PROXY_HOST,
            settings.DS_INTEGRATION_SOCKS_PROXY_PORT,
            settings.DS_INTEGRATION_SOCKS_PROXY_VERSION,
        )
        ftp_client = aioftp.Client(
            socks_host=settings.DS_INTEGRATION_SOCKS_PROXY_HOST,
            socks_port=settings.DS_INTEGRATION_SOCKS_PROXY_PORT,
            socks_version=settings.DS_INTEGRATION_SOCKS_PROXY_VERSION,
        )
    else:
        ftp_client = aioftp.Client()
    try:
        await ftp_client.connect(settings.DS_INTEGRATION_FTP_HOST)
        await ftp_client.login(
            settings.DS_INTEGRATION_FTP_USER, settings.DS_INTEGRATION_FTP_PASSWORD
        )
        return ftp_client
    except Exception as e:
        logger.critical(e, exc_info=True)
        raise e


async def run_import(always_check_children=False):
    logger.info(
        "Starting general data import: always_check_children=%s", always_check_children
    )
    ftp_client = await bootstrap_ftp_client()
    try:
        await import_sub_files(ftp_client, "/master", always_check_children)
        await import_sub_files(ftp_client, "/total/team", always_check_children)
        await import_sub_files(ftp_client, "/total/player", always_check_children)
        await import_sub_files(ftp_client, "/ranking", always_check_children)
        await import_games(
            ftp_client, always_check_children=always_check_children, live_only=False
        )
        await import_sub_files(ftp_client, "/real", always_check_children)
        if settings.DS_INTEGRATION_HEALTHCHECK_URL:
            requests.post(settings.DS_INTEGRATION_HEALTHCHECK_URL)
        logger.info("Import finished")
    except Exception as e:
        logger.critical(e, exc_info=True)
        if settings.DS_INTEGRATION_HEALTHCHECK_URL:
            requests.post(settings.DS_INTEGRATION_HEALTHCHECK_URL + "/fail")
    finally:
        ftp_client.close()


async def run_live_import(always_check_children=False):
    logger.info("Starting live data import")
    ftp_client = await bootstrap_ftp_client()
    try:
        await import_games(
            ftp_client, always_check_children=always_check_children, live_only=True
        )
        if settings.DS_INTEGRATION_HEALTHCHECK_URL:
            requests.post(settings.DS_INTEGRATION_HEALTHCHECK_URL)
        logger.info("Import finished")
    except Exception as e:
        logger.critical(e, exc_info=True)
        if settings.DS_INTEGRATION_HEALTHCHECK_URL:
            requests.post(settings.DS_INTEGRATION_HEALTHCHECK_URL + "/fail")
    finally:
        ftp_client.close()
