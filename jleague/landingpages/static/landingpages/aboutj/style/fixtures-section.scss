@import 'setup/mixin';

.section.section--fixtures {
  .section__body {
    max-width: 1500px;
  }

  .section-header .section-header__title {
    line-height: 100%;
    letter-spacing: 1.28px;
    font-feature-settings: 'clig' off, 'liga' off;
  }

  .fixtures-wrapper {
    display: flex;
    flex-wrap: wrap;
    row-gap: 64px;
    column-gap: 40px;
    justify-content: center;
  }

  .fixture-results {
    flex-basis: 500px;
    flex-grow: 1;
    flex-shrink: 0;
    max-width: 600px;
    margin: 0;

    & + .fixture-results {
      margin: 0;
    }

    .match-list-container {
      margin: 0;

      .match-list-header {
        align-items: center;

        .league-container + .nav-container {
          margin-top: 0;
        }
      }
    }

    .jl-button-fixture {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 12px 24px;
      height: 48px;
      background-color: var(--color-red);
      border: 1px solid var(--color-red);
      border-radius: 25px;
      transition: all 0.3s linear;
      cursor: pointer;

      .jl-button__label-fixture {
        color: var(--color-white);
        font-family: var(--font-barlow-condensed);
        font-style: normal;
        font-weight: 700;
        font-size: 20px;
        line-height: 30px;
        text-align: center;
        font-feature-settings: 'clig' off, 'liga' off;
        letter-spacing: 1.667px;
        text-transform: uppercase;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        word-break: break-all;
        transition: all 0.3s;
      }

      &:hover:not(:disabled) {
        opacity: 1;
        background: var(--color-white);
        border-color: var(--color-black);
        cursor: pointer;

        .jl-button__label-fixture {
          color: var(--color-black);
        }
      }

    }
  }

  @media (max-width: 1199px) {
    .fixture-results .match-list-container .match-list-header {
      flex-direction: row;
    }
  }

  @media (max-width: 1140px) {
    .fixtures-wrapper {
      .fixture-results {
        flex-basis: 100%;
        max-width: none;
      }
    }
  }

  @media (max-width: 768px) {
    .section__body {
      max-width: none;
    }
  }

  @media screen and (max-width: 768px) {
    .fixtures-wrapper{
      row-gap: 32px;
      column-gap: 16px;
    }
    .fixture-results {
      .match-list-container{
        .match-list-header {
          height: 80px;
        }
      }
      .fixture-item__footer .jl-button-fixture {
        border-radius: 25px;
    }
  }
}

@media screen and (max-width: 425px) {
    .fixture-item__footer .jl-button-fixture {
      height: 32px;
      border-radius: 16px;
      .jl-button__label-fixture {
        font-size: 14px;
        line-height: 16px;
      }
  }
}

@media screen and (max-width: 325px) {
  .fixture-results {
    .section-header{
      padding-left: 30px;
    }
  }
}


body.lang-th {
  .fixture-item__footer .jl-button-fixture {
    .jl-button__label-fixture {
      @include font-thai();
    }
    }
}
}



