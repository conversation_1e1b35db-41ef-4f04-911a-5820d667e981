const sidebar = (function () {
  // css classes
  const CLS_BODY_SIDEBAR_OPENED = 'body--sidebar-opened';
  const CLS_SIDEBAR_OPENED = 'sidebar--opened';
  const CLS_TAB_ACTIVE = 'sidebar-tabs__item--active';
  const CLS_MATCH_CARD_SELECTED = 'sidebar-match-card--selected';
  const CLS_COMPETITION_VALUE_SELECTED = 'competition-logo--selected';
  const CLS_COMPETITION_DROPDOWN_SELECTED = 'dropdown-item--selected';

  // css selectors
  const SLT_COMPETITION_DROPDOWN = '.sidebar-competition-dropdown';
  const SLT_COMPETITION_DROPDOWN_VALUE = `${SLT_COMPETITION_DROPDOWN} .competition-dropdown__value`;
  const SLT_COMPETITION_VALUE_LOGO = `${SLT_COMPETITION_DROPDOWN_VALUE} .competition-logo`;
  const SLT_COMPETITION_DROPDOWN_ITEMS = `${SLT_COMPETITION_DROPDOWN} .competition-dropdown__items`;
  const SLT_COMPETITION_DROPDOWN_ITEM = `${SLT_COMPETITION_DROPDOWN_ITEMS} .dropdown-item`;
  const SLT_MATCHWEEK_NAV = '.matchweek-nav';
  const SLT_MATCHWEEK_NAV_PREV = '.matchweek-nav__item--prev';
  const SLT_MATCHWEEK_NAV_NEXT = '.matchweek-nav__item--next';
  const SLT_MATCHWEEK_NAV_LATEST = '.matchweek-nav__item--latest';
  const SLT_SIDEBAR = '.sidebar';
  const SLT_SIDEBAR_BODY = '.sidebar__body';
  const SLT_SIDEBAR_TRIGGER = '.sidebar__trigger';
  const SLT_SIDEBAR_TOGGLE = '.sidebar-toggle';
  const SLT_SIDEBAR_CTA = '.sidebar-cta';
  const SLT_TAB = '.sidebar-tabs__item';
  const SLT_TABS = '.sidebar-tabs';
  const SLT_MATCH_CARD = '.sidebar-match-card';
  const SLT_MATCH_INFO_MODAL = '#match-info-modal';
  const SLT_MATCH_INFO_SUMMARY = '.match-summary';
  const SLT_MATCH_INFO_LIVE = '.match-live';
  const SLT_MATCH_INFO_HIGHLIGHTS = '.match-highlights';
  const SLT_MATCH_HIGHLIGHT_SWIPER = '.highlight-swiper';
  const SLT_MATCH_HIGHLIGHT_VIDEOS = `${SLT_MATCH_HIGHLIGHT_SWIPER} .videos-list`;
  const SLT_STATS_DROPDOWN = '.sidebar-stat-dropdown';

  const TAB_MATCHES = 'matches';
  const TAB_TABLE = 'table';
  const TAB_STATS = 'stats';

  let body;
  let sidebar;
  let sidebarBody;
  let sidebarTrigger;
  let sidebarToggle;
  let sidebarCta;
  let tabItems = [];
  let tabs;
  let selectedCompetition = 'j1';
  let selectedTab = TAB_MATCHES;
  let selectedMatch;
  let selectedMatchweek;
  let selectedStat = 'goals';
  let isExpanded = true;
  let isFirstOpen = true;
  let isLoadingMatchInfo = false;
  let matchCards;
  let matchCardSelected;
  let matchInfoModal;
  let matchInfoSummary;
  let matchInfoLive;
  // let matchInfoHighlights;
  let popperInstance;
  let tooltip;
  let sidebarGoalHighlights;
  let matchHoverTimeout;
  let statSelector;

  function _onMwNavClick(that, event, sourceClass) {
    const stage = $(that).data('stage');
    if (stage === 'latest') {
      selectedMatchweek = undefined;
      localStorage.removeItem('sidebar.matchweek');
    } else {
      selectedMatchweek = stage;
      localStorage.setItem('sidebar.matchweek', selectedMatchweek);
    }
    localStorage.setItem('sidebar.matchweek-at', new Date().getTime());
    sendElementClickEvent('sidebar-matchweek', 'sidebar-matchweek', sourceClass);
    _retrieveData();
  }

  function _showMatchInfo(element) {
    // init tooltip
    tooltip = matchInfoModal.get(0);
    popperInstance = Popper.createPopper(element.get(0), tooltip, {
      placement: 'right-start',
      modifiers: [
        {
          name: 'offset',
          options: {
            offset: [0, 24],
          },
        },
        {
          name: 'preventOverflow',
          options: {
            tether: false,
          },
        },
      ],
    });

    // make the tooltip visible
    tooltip.setAttribute('data-show', '');

    // update its position
    popperInstance.update();

    // on mouse leave modal
    matchInfoModal.on('mouseleave', _hideMatchInfo);
  }

  function _hideMatchInfo() {
    matchInfoModal.off('mouseleave');

    // hide the tooltip
    if (tooltip) {
      tooltip.removeAttribute('data-show');
      tooltip = null;
    }

    if (popperInstance) {
      popperInstance.destroy();
      popperInstance = null;
    }

    // destroy goal highlight videos
    if (sidebarGoalHighlights) {
      sidebarGoalHighlights.destroy();
    }

    $(SLT_MATCH_HIGHLIGHT_VIDEOS).empty();
    // clear selected match
    selectedMatch = null;
  }

  function _initMatchHighlights(matchId) {
    // create goal highlight videos
    const options = { spaceBetween: 16 };
    sidebarGoalHighlights = new GoalHighlights(SLT_MATCH_HIGHLIGHT_SWIPER, options);
    sidebarGoalHighlights.setWrapperSelector(SLT_MATCH_INFO_HIGHLIGHTS);
    sidebarGoalHighlights.setMode('sidebar');

    // fetch goal highlights
    sidebarGoalHighlights.setMatch(matchId);
    sidebarGoalHighlights.loadVideos(0, true);
  }

  function retrieveMatchInfo(mid) {
    if (isLoadingMatchInfo) return;
    isLoadingMatchInfo = true;

    let liveUrl = `/match/data/${mid}/live/?sidebar=1`;
    let infoUrl = `/widgets/sidebar/match/info/`;
    params = { mid };
    infoUrl += urlSearchQuery(params);

    matchInfoModal.addClass('loading');

    Promise.all([ajaxRequest(infoUrl), ajaxRequest(liveUrl)])
      .then((res) => {
        // update summary
        matchInfoSummary.empty();
        matchInfoSummary.append(res[0]);

        // update live
        matchInfoLive.empty();
        matchInfoLive.append(res[1]);

        isLoadingMatchInfo = false;
        matchInfoModal.removeClass('loading');

        // update its position
        if (popperInstance) popperInstance.update();
      })
      .catch((err) => console.error(err));
  }

  function _onMatchCardHovered(e) {
    clearTimeout(matchHoverTimeout);
    matchHoverTimeout = setTimeout(() => {
      if (isMobile()) return;

      const card = $(this);
      const mid = card.data('mid');
      const homeScore = card.data('home-score');
      const awaySocre = card.data('away-socre');

      // hover the same match, ignores
      if (selectedMatch === mid) return;
      selectedMatch = mid;
      _hideMatchInfo();

      if (homeScore || awaySocre) {
        _showMatchInfo(card);
        retrieveMatchInfo(mid);
        _initMatchHighlights(mid);
      }
    }, 500);
  }

  function _onMatchCardSelected(e) {
    const card = $(this);
    if (matchCards && matchCards.length) {
      for (const matchCard of matchCards) {
        $(matchCard).removeClass(CLS_MATCH_CARD_SELECTED);
      }
    }
    card.addClass(CLS_MATCH_CARD_SELECTED);

    const matchId = card.data('mid');
    sendElementClickEvent('sidebar-match-card', matchId, '.sidebar-match-card');
  }

  function _setupMatchesTab() {
    // handler matchweek navigation click
    $(SLT_MATCHWEEK_NAV_PREV).click(function (event) {
      _onMwNavClick(this, event, SLT_MATCHWEEK_NAV_PREV);
    });
    $(SLT_MATCHWEEK_NAV_NEXT).click(function (event) {
      _onMwNavClick(this, event, SLT_MATCHWEEK_NAV_NEXT);
    });
    $(SLT_MATCHWEEK_NAV_LATEST).click(function (event) {
      _onMwNavClick(this, event, SLT_MATCHWEEK_NAV_LATEST);
    });

    // handle match card click
    matchInfoModal = sidebarBody.find(SLT_MATCH_INFO_MODAL);
    matchInfoSummary = matchInfoModal.find(SLT_MATCH_INFO_SUMMARY);
    matchInfoLive = matchInfoModal.find(SLT_MATCH_INFO_LIVE);
    // matchInfoHighlights = matchInfoModal.find(SLT_MATCH_INFO_HIGHLIGHTS);

    matchCards = sidebarBody.find(SLT_MATCH_CARD);
    for (const card of matchCards) {
      $(card).click(_onMatchCardSelected);
      $(card).on('mouseenter', _onMatchCardHovered);
      // .on('mouseleave', _hideMatchInfo);

      // scroll to the selected match
      if (card.classList.contains(CLS_MATCH_CARD_SELECTED)) {
        matchCardSelected = card;
        _scrollToSelectedMatch();
      }
    }
  }

  function _onStatsSelectorChange() {
    selectedStat = statSelector.val();
    localStorage.setItem('sidebar.stat', selectedStat);
    sendElementClickEvent('sidebar-stats-dropdown', selectedStat, SLT_STATS_DROPDOWN);
    _retrieveData();
  }

  function _setupStatsTab() {
    statSelector = $(SLT_STATS_DROPDOWN);
    statSelector.change(_onStatsSelectorChange);
  }

  function _afterRetrieveData() {
    // cleanup
    matchCardSelected = null;

    switch (selectedTab) {
      case TAB_MATCHES:
        _setupMatchesTab();
        break;

      case TAB_TABLE:
        break;

      case TAB_STATS:
        _setupStatsTab();
        break;

      default:
        break;
    }
  }

  function _retrieveData() {
    if (selectedTab === TAB_MATCHES) $(SLT_MATCHWEEK_NAV).addClass('disabled');

    let url = `/widgets/sidebar/`;
    const params = {
      source: selectedTab,
      competition: selectedCompetition,
    };

    if (selectedCompetition === 'levain') {
      $('#sidebar-table').addClass('display-none');
    } else {
      $('#sidebar-table').removeClass('display-none');
    }

    if (typeof selectedMatchweek !== 'undefined') params.stage = selectedMatchweek;
    if (typeof selectedStat !== 'undefined') params.stat = selectedStat;
    // from match detail page
    if (typeof matchId !== 'undefined') params.match_page = matchId;

    url += urlSearchQuery(params);

    ajaxRequest(url)
      .then((r) => {
        sidebarBody.empty();
        sidebarBody.append(r);
        _afterRetrieveData();
      })
      .catch((e) => console.error(e));
  }

  function _updateTab() {
    for (const item of tabItems) {
      const tabElm = $(item);
      const tab = tabElm.data('tab');
      if (tab !== selectedTab) {
        tabElm.removeClass(CLS_TAB_ACTIVE);
      } else {
        tabElm.addClass(CLS_TAB_ACTIVE);
      }
    }
  }

  function _onTabClick(e) {
    e.stopPropagation();
    selectedTab = $(this).data('tab');
    localStorage.setItem('sidebar.tab', selectedTab);
    sendElementClickEvent('sidebar-tab', selectedTab, '.sidebar-tabs__item');
    _updateTab();
    _retrieveData();
  }

  function _setupTabs() {
    for (const item of tabItems) {
      $(item).click(_onTabClick);
    }
  }

  function _scrollToSelectedMatch() {
    // scroll to the selected match
    if (isExpanded && matchCardSelected) {
      setTimeout(() => {
        matchCardSelected.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'end' });
      }, 500);
    }
  }

  function _toggleSidebar() {
    if (isExpanded) {
      body.addClass(CLS_BODY_SIDEBAR_OPENED);
      sidebar.addClass(CLS_SIDEBAR_OPENED);
      sidebarCta.addClass('sidebar-cta--show');
    } else {
      body.removeClass(CLS_BODY_SIDEBAR_OPENED);
      sidebar.removeClass(CLS_SIDEBAR_OPENED);
      sidebarCta.removeClass('sidebar-cta--show');
    }

    if (isExpanded && isFirstOpen) {
      isFirstOpen = false;
      _retrieveData();
    }

    _scrollToSelectedMatch();
  }

  function _onSidebarToggleClick(event, expanded) {
    if (event) event.stopPropagation();
    isExpanded = typeof expanded !== 'undefined' ? expanded : !isExpanded;
    localStorage.setItem('sidebar.expanded', isExpanded);
    _toggleSidebar();
  }

  function _setupToggle() {
    if (isMobile()) {
      _onSidebarToggleClick(null, false);
    }

    // sidebar click handler
    sidebarToggle.click((e) => {
      _onSidebarToggleClick(e);
      sendElementClickEvent('sidebar-toggle', 'sidebar-toggle', SLT_SIDEBAR_TOGGLE);
    });
    sidebarTrigger.click((e) => {
      _onSidebarToggleClick(e);
      sendElementClickEvent('sidebar-trigger', 'sidebar-trigger', SLT_SIDEBAR_TRIGGER);
    });
  }

  function _updateCompetitionDropdown() {
    $(SLT_COMPETITION_VALUE_LOGO).each(function () {
      const elm = $(this);
      if (elm.hasClass(`competition-logo--${selectedCompetition}`)) {
        elm.addClass(CLS_COMPETITION_VALUE_SELECTED);
      } else {
        elm.removeClass(CLS_COMPETITION_VALUE_SELECTED);
      }
    });

    $(SLT_COMPETITION_DROPDOWN_ITEM).each(function () {
      const elm = $(this);
      const competition = elm.data('value');
      if (competition === selectedCompetition) {
        elm.addClass(CLS_COMPETITION_DROPDOWN_SELECTED);
      } else {
        elm.removeClass(CLS_COMPETITION_DROPDOWN_SELECTED);
      }
    });
  }

  function _onCompetitionChange(competitionSlug) {
    selectedCompetition = competitionSlug;
    localStorage.setItem('sidebar.competition', selectedCompetition);
    selectedMatchweek = undefined;
    localStorage.removeItem('sidebar.matchweek');
    _retrieveData();
  }

  function _restoreState(reload = false) {
    const sbExpanded = localStorage.getItem('sidebar.expanded');
    if (sbExpanded) isExpanded = sbExpanded === 'true';

    const sbCompetition = localStorage.getItem('sidebar.competition');
    if (sbCompetition) selectedCompetition = sbCompetition;

    const sbTab = localStorage.getItem('sidebar.tab');
    if (sbTab) selectedTab = sbTab;

    const sbMwAt = localStorage.getItem('sidebar.matchweek-at');
    const now = new Date().getTime();
    const timeDiff = now - parseInt(sbMwAt, 10);
    const latestTimeout = 3 * 24 * 60 * 60 * 1000; // 3 days

    const sbMw = localStorage.getItem('sidebar.matchweek');
    if (sbMw === null || sbMw === 'undefined' || timeDiff > latestTimeout) {
      selectedMatchweek = undefined;
      localStorage.removeItem('sidebar.matchweek');
    } else {
      selectedMatchweek = sbMw;
    }

    const sbStat = localStorage.getItem('sidebar.stat');
    if (sbStat) selectedStat = sbStat;

    _toggleSidebar();
    _updateCompetitionDropdown();
    _updateTab();
    if (reload === true) _retrieveData();
  }

  function adjustSidebarHeight() {
    const sidebarBody = document.querySelector('.sidebar__body');
    const sidebarBanner = document.querySelector('.sidebar_cta');

    if (sidebarBody) {
      if (sidebarBanner) {
        sidebarBody.style.maxHeight = 'calc(100vh - 225px)';
      } else {
        sidebarBody.style.maxHeight = 'calc(100vh - 125px)';
      }
    } else {
      console.error('Sidebar body not found!');
    }
  }

  function init() {
    body = $('body');
    sidebar = $(SLT_SIDEBAR);
    sidebarBody = $(SLT_SIDEBAR_BODY);
    sidebarToggle = $(SLT_SIDEBAR_TOGGLE);
    sidebarTrigger = $(SLT_SIDEBAR_TRIGGER);
    sidebarCta = $(SLT_SIDEBAR).find(SLT_SIDEBAR_CTA);
    tabs = $(SLT_TABS);
    tabItems = tabs.find(SLT_TAB);
    adjustSidebarHeight();
    _restoreState();
    _setupToggle();
    _setupTabs();
    competitionDropdown.init(SLT_COMPETITION_DROPDOWN, _onCompetitionChange);
  }

  return { init, refresh: _restoreState };
})();

sidebar.init();
