# Generated by Django 3.1.13 on 2021-08-27 01:38

from django.db import migrations
import modelcluster.fields


class Migration(migrations.Migration):
    dependencies = [
        ('home', '0019_auto_20210819_1214'),
    ]

    operations = [
        migrations.AddField(
            model_name='teaminfo',
            name='stadiums',
            field=modelcluster.fields.ParentalManyToManyField(
                blank=True, related_name='stadiums', to='home.StadiumInfo'
            ),
        ),
        migrations.AddField(
            model_name='teaminfo',
            name='videos',
            field=modelcluster.fields.ParentalManyToManyField(blank=True, related_name='videos', to='home.VideoItem'),
        ),
    ]
