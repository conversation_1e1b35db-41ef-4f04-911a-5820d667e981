{% extends 'wagtailadmin/base.html' %}

{% load i18n wagtailimages_tags %}

{% block titletag %}
  {% trans 'Translations Import' %}
{% endblock %}

{% block content %}
  {% trans 'Translations Import' as translations_title %}

  {% include 'wagtailadmin/shared/header.html' with title=translations_title icon='image' %}

  <div class="nice-padding">
    <section>
      <form
        action="{% url 'translation_import' %}"
        method="POST"
        enctype="multipart/form-data"
      >
        {% csrf_token %}

        {% for field in form.visible_fields %}
          {% comment %} {% if field.widget_type == 'checkbox' %}
            # render one way
          {% else %}
            # render another way
          {% endif %} {% endcomment %}

          <div class="w-field__wrapper">
            <label class="w-field__label" for="{{ field.id_for_label }}">
              {{ field.label }}
              {% if field.field.required %}<span class="w-required-mark">*</span>{% endif %}
            </label>

            <div class="w-field">
              <div class="w-field__errors">
                {{ field.errors }}
              </div>

              <div class="w-field__input">
                {{ field }}
              </div>

              {% if field.help_text %}
                <div>
                  <p class="help">{{ field.help_text|safe }}</p>
                </div>
              {% endif %}
            </div>
          </div>
        {% endfor %}

        <button type="submit" class="button">Import</button>
      </form>

    </section>
  </div>
{% endblock %}
