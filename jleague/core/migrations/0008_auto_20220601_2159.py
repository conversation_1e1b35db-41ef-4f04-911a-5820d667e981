# Generated by Django 3.1.13 on 2022-06-01 12:59

from django.db import migrations


def patch_formations(apps, schema_editor):
    TeamFormation = apps.get_model('data_stadium', 'TeamFormation')
    Formation = apps.get_model('core', 'Formation')

    team_formations = TeamFormation.objects.all()

    for team_formation in team_formations:
        Formation.objects.create(
            id=team_formation.id,
            name=team_formation.name,
            created_at=team_formation.created_at,
            updated_at=team_formation.updated_at,
        )


class Migration(migrations.Migration):
    dependencies = [
        ('core', '0007_formation'),
    ]

    operations = [
        migrations.RunPython(patch_formations),
    ]
