import logging
from decimal import Decimal
from ..helpers import (
    async_find_first,
    async_upsert,
    read_xml_file,
    xml_value,
)
from clubs.models import Club
from core.models import Competition
from stats.models import ClubStats

logger = logging.getLogger(__name__)

# team_Stats-
_ds_mapping_team_stats = {
    'corner_kicks': 'CK',
    'direct_free_kicks': 'FKD',
    'games_drawn': 'Draw',
    'games_lost': 'Lose',
    'games_played': 'Game',
    'games_won': 'Win',
    'indirect_free_kicks': 'FKI',
    'offsides': 'Offside',
    'penalty_goals': 'PKScore',
    'penalty_kicks': 'PK',
    'points': 'Point',
}

# team_Stats_details-
_ds_mapping_team_stat_details = {
    'aerial_duel_win_rate': 'AirBattleWinRate',
    'aerial_duels_won': 'AirBattleWinCount',
    'aerial_duels_won_pg': 'AirBattleWinCountPG',
    'aerial_duels_won_pg_rank': 'AirBattleWinCountPGRank',
    'attacking_third_sprints': 'AtSprint',
    'attacking_third_sprints_rank': 'AtSprintCountRank',
    'ball_possessions': 'BallRate',
    'ball_possessions_rank': 'BallRateRank',
    'big_chances': 'BigChance',
    'big_chances_pg': 'BigChancePG',
    'big_chances_pg_rank': 'BigChancePGRank',
    'blocks': 'BlockCount',
    'blocks_pg': 'BlockCountPG',
    'blocks_pg_rank': 'BlockCountPGRank',
    'chances_created': 'ChanceCreate',
    'chances_created_pg': 'ChanceCreatePG',
    'chances_created_pg_rank': 'ChanceCreatePGRank',
    'clean_sheets': 'CleanSheet',
    'clean_sheets_rank': 'CleanSheetRank',
    'clearances': 'ClearCount',
    'clearances_pg': 'ClearCountPG',
    'clearances_pg_rank': 'ClearCountPGRank',
    'cross_rate': 'CrossRate',
    'cross_rate_rank': 'CrossRateRank',
    'crosses': 'CrossCount',
    'crosses_pg': 'CrossCountPG',
    'crosses_pg_rank': 'CrossCountPGRank',
    'defensive_third_sprints': 'DtSprint',
    'defensive_third_sprints_rank': 'DtSprintCountRank',
    'distance': 'Distance',
    'distance_rank': 'DistanceRank',
    'dribble_rate': 'DribbleRate',
    'dribble_rate_rank': 'DribbleRateRank',
    'dribbles': 'DribbleCount',
    'dribbles_pg': 'DribbleCountPG',
    'dribbles_pg_rank': 'DribbleCountPGRank',
    'expected_goals': 'ExpectedGoals',
    'expected_goals_against': 'ExpectedGoalsAgainst',
    'expected_goals_against_excl_pk': 'ExpectedGoalsAgainstExclPK',
    'expected_goals_against_excl_pk_pg': 'ExpectedGoalsAgainstExclPKPG',
    'expected_goals_against_excl_pk_pg_rank': 'ExpectedGoalsAgainstExclPKPGRank',
    'expected_goals_against_pg': 'ExpectedGoalsAgainstPG',
    'expected_goals_against_pg_rank': 'ExpectedGoalsAgainstPGRank',
    'expected_goals_excl_pk': 'ExpectedGoalsExclPK',
    'expected_goals_excl_pk_pg': 'ExpectedGoalsExclPKPG',
    'expected_goals_excl_pk_pg_rank': 'ExpectedGoalsExclPKPGRank',
    'expected_goals_pg': 'ExpectedGoalsPG',
    'expected_goals_pg_rank': 'ExpectedGoalsPGRank',
    'fouls': 'FoulCount',
    'fouls_pg': 'FoulCountPG',
    'fouls_pg_rank': 'FoulCountPGRank',
    'games_played': 'Game',
    'goals': 'Score',
    'goals_conceded': 'Lost',
    'goals_conceded_pg': 'LostPG',
    'goals_conceded_pg_rank': 'LostPGRank',
    'goals_pg': 'ScorePG',
    'goals_pg_rank': 'ScorePGRank',
    'interceptions': 'InterceptCount',
    'interceptions_pg': 'InterceptCountPG',
    'interceptions_pg_rank': 'InterceptCountPGRank',
    'middle_third_sprints': 'MtSprint',
    'middle_third_sprints_rank': 'MtSprintCountRank',
    'one_on_one': 'OneonOne',
    'one_on_one_pg': 'OneonOnePG',
    'one_on_one_pg_rank': 'OneonOnePGRank',
    'pass_rate': 'PassRate',
    'pass_rate_rank': 'PassRateRank',
    'passes': 'PassCount',
    'passes_pg': 'PassCountPG',
    'passes_pg_rank': 'PassCountPGRank',
    'possession_distance': 'PossessionDistance',
    'possession_distance_rank': 'PossessionDistanceRank',
    'possession_sprints': 'PossessionSprint',
    'possession_sprints_rank': 'PossessionSprintCountRank',
    'recoveries': 'RecoveryCount',
    'recoveries_pg': 'RecoveryCountPG',
    'recoveries_pg_rank': 'RecoveryCountPGRank',
    'red_cards': 'RedCount',
    'red_cards_rank': 'RedCountRank',
    'shoot_rate': 'ShootRate',
    'shots': 'Shoot',
    'shots_on_target': 'ShootOnTarget',
    'shots_on_target_pg': 'ShootOnTargetPG',
    'shots_on_target_pg_rank': 'ShootOnTargetPGRank',
    'shots_pg': 'ShootPG',
    'shots_pg_rank': 'ShootPGRank',
    'sprints': 'Sprint',
    'sprints_rank': 'SprintCountRank',
    'suffer_shots': 'SufferShoot',
    'suffer_shots_on_target': 'SufferShootOnTarget',
    'suffer_shots_on_target_pg': 'SufferShootOnTargetPG',
    'suffer_shots_on_target_pg_rank': 'SufferShootOnTargetPGRank',
    'suffer_shots_pg': 'SufferShootPG',
    'suffer_shots_pg_rank': 'SufferShootPGRank',
    'tackle_rate': 'TackleRate',
    'tackles': 'TackleCount',
    'tackles_pg': 'TackleCountPG',
    'tackles_pg_rank': 'TackleCountPGRank',
    'through_ball_rate': 'ThroughPassRate',
    'through_ball_rate_rank': 'ThroughPassRateRank',
    'through_balls': 'ThroughPassCount',
    'through_balls_pg': 'ThroughPassCountPG',
    'through_balls_pg_rank': 'ThroughPassCountPGRank',
    'unpossession_distance': 'UnPossessionDistance',
    'unpossession_distance_rank': 'UnPossessionDistanceRank',
    'unpossession_sprints': 'UnPossessionSprint',
    'unpossession_sprints_rank': 'UnPossessionSprintCountRank',
    'yellow_cards': 'YellowCount',
    'yellow_cards_pg': 'YellowCountPG',
    'yellow_cards_rank': 'YellowCountRank',
}

_decimal_value_stats = (
    'aerial_duel_win_rate',
    'aerial_duels_won_pg',
    'attacking_third_sprints',
    'ball_possessions',
    'big_chances_pg',
    'blocks_pg',
    'chances_created_pg',
    'clearances_pg',
    'cross_rate',
    'crosses_pg',
    'defensive_third_sprints',
    'distance',
    'dribble_rate',
    'dribbles_pg',
    'expected_goals',
    'expected_goals_against',
    'expected_goals_against_excl_pk',
    'expected_goals_against_excl_pk_pg',
    'expected_goals_against_pg',
    'expected_goals_excl_pk',
    'expected_goals_excl_pk_pg',
    'expected_goals_pg',
    'fouls_pg',
    'goals_conceded_pg',
    'goals_pg',
    'interceptions_pg',
    'middle_third_sprints',
    'one_on_one_pg',
    'pass_rate',
    'passes_pg',
    'possession_distance',
    'possession_sprints',
    'recoveries_pg',
    'shoot_rate',
    'shots_on_target_pg',
    'shots_pg',
    'sprints',
    'suffer_shots_on_target_pg',
    'suffer_shots_pg',
    'tackle_rate',
    'tackles_pg',
    'through_ball_rate',
    'through_balls_pg',
    'unpossession_distance',
    'unpossession_sprints',
    'yellow_cards_pg',
)


def _is_decimal_value_stat(stat: str):
    return True if stat in _decimal_value_stats else False


def _get_stat_value_type(stat: str):
    return Decimal if _is_decimal_value_stat(stat) else int


async def import_club_stats(file_path: str, data_year: int):
    """Import team stat details from DS
    The file which outputs team stats summary of the season.
    source file: /total/team/team_Stats-{COMPETITION_ID}_{DS_CLUB_ID}.xml
    """
    # read XML file
    root = await read_xml_file(file_path)
    report_node = root.find('TeamStatsReport')
    year = xml_value(report_node, 'Year', int)
    if year != data_year:
        return

    competition_id = xml_value(report_node, 'GameKindID', int)
    club_id = xml_value(report_node, 'TeamID', int)

    # check competition
    competition = await async_find_first(Competition, id=competition_id)
    if not competition:
        logger.warn('Competition not found (id=%s)', competition_id)
        return

    # check club
    club = await async_find_first(Club, ds_club_id=club_id)
    if not club:
        logger.warn('Club not found (ds_club_id=%s)', club_id)
        return

    # get club stats info
    team_info = report_node.find('TeamInfo')
    if team_info is None:
        logger.warn('TeamInfo node not found, skipped.')
        return

    # dynamically collect club stats
    stats = {}
    for stat in _ds_mapping_team_stats.keys():
        ds_key = _ds_mapping_team_stats.get(stat)
        if ds_key is not None:
            stats[stat] = xml_value(team_info, ds_key, _get_stat_value_type(stat))

    # upsert club stats
    await async_upsert(ClubStats, year=year, competition=competition, club=club, defaults=stats)
    logger.info('Processed club stats (year=%s, ds_club_id=%s)', year, club_id)


async def import_club_stat_details(file_path: str, data_year: int):
    """Import team stat details from DS
    The file which outputs team stat details of the season.
    source file: /total/team/team_Stats_details-{COMPETITION_ID}_{DS_CLUB_ID}.xml
    """
    # read XML file
    root = await read_xml_file(file_path)
    report_node = root.find('TeamStatsDetailsReport')
    year = xml_value(report_node, 'Year', int)
    if year != data_year:
        return

    competition_id = xml_value(report_node, 'GameKindID', int)
    club_id = xml_value(report_node, 'TeamID', int)

    # check competition
    competition = await async_find_first(Competition, id=competition_id)
    if not competition:
        logger.warn('Competition not found (id=%s)', competition_id)
        return

    # check club
    club = await async_find_first(Club, ds_club_id=club_id)
    if not club:
        logger.warn('Club not found (ds_club_id=%d)', club_id)
        return

    # get club stats info
    team_info = report_node.find('TeamInfo')
    if team_info is None:
        logger.warn('TeamInfo node not found, skipped.')
        return

    # dynamically collect club stats
    stats = {}
    for stat in _ds_mapping_team_stat_details.keys():
        ds_key = _ds_mapping_team_stat_details.get(stat)
        if ds_key is not None:
            stats[stat] = xml_value(team_info, ds_key, _get_stat_value_type(stat))

    # upsert club stats
    await async_upsert(ClubStats, year=year, competition=competition, club=club, defaults=stats)
    logger.info('Processed club stat details (year=%s, ds_club_id=%s)', year, club_id)
