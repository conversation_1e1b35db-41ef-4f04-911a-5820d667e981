# Generated by Django 3.1.13 on 2022-10-11 10:41

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('core', '0012_auto_20220804_1648'),
    ]

    operations = [
        migrations.CreateModel(
            name='AlertLog',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('message', models.TextField()),
                (
                    'level',
                    models.CharField(
                        choices=[('error', 'ERROR'), ('warning', 'WARNING'), ('info', 'INFO'), ('debug', 'DEBUG')],
                        max_length=30,
                    ),
                ),
                (
                    'related_content',
                    models.CharField(
                        choices=[('club', 'Club'), ('player', 'Player'), ('game', 'Game'), ('stat', 'Stat')],
                        max_length=255,
                    ),
                ),
                ('alert_channel', models.CharField(choices=[('slack', 'Slack'), ('email', 'Email')], max_length=30)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
