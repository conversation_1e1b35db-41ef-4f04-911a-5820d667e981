# Generated by Django 3.1.13 on 2022-07-07 07:44

from django.db import migrations


class Migration(migrations.Migration):
    dependencies = [
        ('data_stadium', '0050_remove_player_team_member_type'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='game',
            name='away_previous_game',
        ),
        migrations.RemoveField(
            model_name='game',
            name='away_team',
        ),
        migrations.RemoveField(
            model_name='game',
            name='game_kind',
        ),
        migrations.RemoveField(
            model_name='game',
            name='home_previous_game',
        ),
        migrations.RemoveField(
            model_name='game',
            name='home_team',
        ),
        migrations.RemoveField(
            model_name='game',
            name='referee',
        ),
        migrations.RemoveField(
            model_name='game',
            name='stadium',
        ),
        migrations.RemoveField(
            model_name='gamekindseasonteam',
            name='game_kind',
        ),
        migrations.RemoveField(
            model_name='gamekindseasonteam',
            name='team',
        ),
        migrations.RemoveField(
            model_name='gamekindteamavgstats',
            name='game_kind',
        ),
        migrations.RemoveField(
            model_name='gamekindteamavgstats',
            name='team',
        ),
        migrations.RemoveField(
            model_name='gamekindteamgamestats',
            name='game',
        ),
        migrations.RemoveField(
            model_name='gamekindteamgamestats',
            name='game_kind',
        ),
        migrations.RemoveField(
            model_name='gamekindteamgamestats',
            name='team',
        ),
        migrations.RemoveField(
            model_name='gamekindteamplayergamestats',
            name='game',
        ),
        migrations.RemoveField(
            model_name='gamekindteamplayergamestats',
            name='game_kind',
        ),
        migrations.RemoveField(
            model_name='gamekindteamplayergamestats',
            name='player',
        ),
        migrations.RemoveField(
            model_name='gamekindteamplayergamestats',
            name='team',
        ),
        migrations.RemoveField(
            model_name='gamekindteamplayerstats',
            name='game_kind',
        ),
        migrations.RemoveField(
            model_name='gamekindteamplayerstats',
            name='player',
        ),
        migrations.RemoveField(
            model_name='gamekindteamplayerstats',
            name='team',
        ),
        migrations.RemoveField(
            model_name='gamekindteamstats',
            name='game_kind',
        ),
        migrations.RemoveField(
            model_name='gamekindteamstats',
            name='team',
        ),
        migrations.RemoveField(
            model_name='gameteamformation',
            name='away_team',
        ),
        migrations.RemoveField(
            model_name='gameteamformation',
            name='away_team_formation',
        ),
        migrations.RemoveField(
            model_name='gameteamformation',
            name='game',
        ),
        migrations.RemoveField(
            model_name='gameteamformation',
            name='home_team',
        ),
        migrations.RemoveField(
            model_name='gameteamformation',
            name='home_team_formation',
        ),
        migrations.RemoveField(
            model_name='player',
            name='prev_team',
        ),
        migrations.RemoveField(
            model_name='player',
            name='team',
        ),
        migrations.RemoveField(
            model_name='playergameparticipation',
            name='game',
        ),
        migrations.RemoveField(
            model_name='playergameparticipation',
            name='player',
        ),
        migrations.RemoveField(
            model_name='team',
            name='home_stadium',
        ),
        migrations.DeleteModel(
            name='TeamFormation',
        ),
        migrations.RemoveField(
            model_name='teamranking',
            name='game_kind',
        ),
        migrations.RemoveField(
            model_name='teamranking',
            name='team',
        ),
        migrations.DeleteModel(
            name='Game',
        ),
        migrations.DeleteModel(
            name='GameKind',
        ),
        migrations.DeleteModel(
            name='GameKindSeasonTeam',
        ),
        migrations.DeleteModel(
            name='GameKindTeamAvgStats',
        ),
        migrations.DeleteModel(
            name='GameKindTeamGameStats',
        ),
        migrations.DeleteModel(
            name='GameKindTeamPlayerGameStats',
        ),
        migrations.DeleteModel(
            name='GameKindTeamPlayerStats',
        ),
        migrations.DeleteModel(
            name='GameKindTeamStats',
        ),
        migrations.DeleteModel(
            name='GameTeamFormation',
        ),
        migrations.DeleteModel(
            name='Player',
        ),
        migrations.DeleteModel(
            name='PlayerGameParticipation',
        ),
        migrations.DeleteModel(
            name='Referee',
        ),
        migrations.DeleteModel(
            name='Stadium',
        ),
        migrations.DeleteModel(
            name='Team',
        ),
        migrations.DeleteModel(
            name='TeamRanking',
        ),
    ]
