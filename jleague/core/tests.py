from django.test import TestCase

import datetime
from core.helpers import (
    get_club_name,
    get_competition_name,
    get_local_time,
    get_player_name,
    get_translation_obj_value,
    get_translation_value,
)
from .models import Competition


class CoreTestCase(TestCase):
    def test_competitions(self):
        """Main competitions must be in Competitions List"""
        competitions = Competition.objects.all().values_list("slug", flat=True)
        main_competitions = (
            "j1",
            "j2",
            "j3",
            "levain",
            "ffsc",
            "acl",
            "j1-entry-play-off",
            "asia-challenge",
            "jwc",
            "friendly-match",
        )
        count = 0

        self.assertGreaterEqual(
            len(competitions),
            len(main_competitions),
            "Competitions less than the main competitions",
        )

        for c in competitions:
            if c in main_competitions:
                count += 1

        self.assertEqual(
            count,
            len(main_competitions),
            "At least one or more main competitions missing",
        )

    def test_get_translation_value(self):
        """
        คีย์ มีคำแปล จะต้องได้คำแปลกลับมา
        คีย์ ไม่มีคำแปล จะต้องได้คีย์กลับมา

        prefix - คีย์ มีคำแปล จะต้องได้คำแปลกลับมา
        prefix - คีย์ ไม่มีคำแปล จะต้องได้คีย์กลับมา

        prefix - คีย์ มีคำแปล จะต้องได้คำแปลกลับมา
        prefix - คีย์ ไม่มีคำแปล จะต้องได้คีย์กลับมา

        uppercase - คีย์ มีคำแปล จะต้องได้คำแปลกลับมาและตัวอักษรใหญ่ทั้งหมด
        uppercase - คีย์ ไม่มีคำแปล จะต้องได้คีย์กลับมาและตัวอักษรใหญ่ทั้งหมด

        lowercase - คีย์ มีคำแปล จะต้องได้คำแปลกลับมาและตัวอักษรเล็กทั้งหมด
        lowercase - คีย์ ไม่มีคำแปล จะต้องได้คีย์กลับมาและตัวอักษรเล็กทั้งหมด

        titlecase - คีย์ มีคำแปล จะต้องได้คำแปลกลับมาและตัวอักษรตัวแรกของคำเป็นตัวใหญ่
        titlecase - คีย์ ไม่มีคำแปล จะต้องได้คีย์กลับมาและตัวอักษรตัวแรกของคำเป็นตัวใหญ่
        """

        # คีย์มีคำแปล
        is_translation = get_translation_value("SEO_CLUB_LIST_PAGE_TITLE")
        self.assertEqual(
            is_translation,
            "Clubs | J.LEAGUE",
            "'SEO_CLUB_LIST_PAGE_TITLE' translation is not equal to 'Clubs | J.LEAGUE'",
        )

        # คีย์ไม่มีคำแปล
        not_translation = get_translation_value("SEO_CLUB_LIST_PAGE_TITLE_01")
        self.assertEqual(
            not_translation,
            "SEO_CLUB_LIST_PAGE_TITLE_01",
            "'SEO_CLUB_LIST_PAGE_TITLE_01' translation is not equal to 'SEO_CLUB_LIST_PAGE_TITLE_01'",
        )

        # คีย์มีคำแปลและใช้ prefix
        prefix_is_translation = get_translation_value(
            "CLUB_LIST_PAGE_TITLE", prefix="SEO_"
        )
        self.assertEqual(
            prefix_is_translation,
            "Clubs | J.LEAGUE",
            "'SEO_CLUB_LIST_PAGE_TITLE' translation is not equal to 'Clubs | J.LEAGUE'",
        )

        # คีย์ไม่มีคำแปลและใช้ prefix
        prefix_not_translation = get_translation_value(
            "CLUB_LIST_PAGE_TITLE_01", prefix="SEO_"
        )
        self.assertEqual(
            prefix_not_translation,
            "CLUB_LIST_PAGE_TITLE_01",
            "'CLUB_LIST_PAGE_TITLE_01' translation is not equal to 'CLUB_LIST_PAGE_TITLE_01'",
        )

        # คีย์มีคำแปลและใช้ uppercase
        option_is_uppercase = get_translation_value(
            "SEO_CLUB_LIST_PAGE_TITLE", uppercase=True
        )
        self.assertEqual(
            option_is_uppercase,
            "CLUBS | J.LEAGUE",
            "'SEO_CLUB_LIST_PAGE_TITLE' translation is not equal to 'CLUBS | J.LEAGUE'",
        )

        # คีย์ไม่มีคำแปลและใช้ uppercase
        option_not_uppercase = get_translation_value(
            "SEO_CLUB_LIST_PAGE_TITLE_01", uppercase=True
        )
        self.assertEqual(
            option_not_uppercase,
            "SEO_CLUB_LIST_PAGE_TITLE_01",
            "'SEO_CLUB_LIST_PAGE_TITLE_01' translation is not equal to 'SEO_CLUB_LIST_PAGE_TITLE_01'",
        )

        # คีย์มีคำแปลและใช้ lowercase
        option_is_lowercase = get_translation_value(
            "SEO_CLUB_LIST_PAGE_TITLE", lowercase=True
        )
        self.assertEqual(
            option_is_lowercase,
            "clubs | j.league",
            "'SEO_CLUB_LIST_PAGE_TITLE' translation is not equal to 'clubs | j.league'",
        )

        # คีย์ไม่มีคำแปลและใช้ lowercase
        option_not_lowercase = get_translation_value(
            "SEO_CLUB_LIST_PAGE_TITLE_01", lowercase=True
        )
        self.assertEqual(
            option_not_lowercase,
            "seo_club_list_page_title_01",
            "'seo_club_list_page_title_01' translation is not equal to 'seo_club_list_page_title_01'",
        )

        # คีย์มีคำแปลและใช้ titlecase
        option_is_titlecase = get_translation_value(
            "SEO_CLUB_LIST_PAGE_TITLE", titlecase=True
        )
        self.assertEqual(
            option_is_titlecase,
            "Clubs | J.League",
            "'SEO_CLUB_LIST_PAGE_TITLE' translation is not equal to 'Clubs | J.League'",
        )

        # คีย์ไม่มีคำแปลและใช้ titlecase
        option_not_titlecase = get_translation_value(
            "SEO_CLUB_LIST_PAGE_TITLE_01", titlecase=True
        )
        self.assertEqual(
            option_not_titlecase,
            "Seo_Club_List_Page_Title_01",
            "'Seo_Club_List_Page_Title_01' translation is not equal to 'Seo_Club_List_Page_Title_01'",
        )

    def test_get_translation_obj_value(self):
        """
        dict มีคำแปล จะต้องได้คำแปลกลับมา
        dict ไม่มีคำแปล จะต้องได้ค่าว่างกลับมา

        is default มีคำแปล จะต้องได้คำแปลกลับมา
        is default ไม่มีคำแปล จะต้องได้ค่า default กลับมา

        uppercase - dict มีคำแปล จะต้องได้คำแปลกลับมาและตัวอักษรใหญ่ทั้งหมด
        uppercase - dict ไม่มีคำแปล จะต้องได้ค่าว่างกลับมา

        lowercase - dict มีคำแปล จะต้องได้คำแปลกลับมาและตัวอักษรเล็กทั้งหมด
        lowercase - dict ไม่มีคำแปล จะต้องได้ค่าว่างกลับมา

        titlecase - dict มีคำแปล จะต้องได้คำแปลกลับมาและตัวอักษรตัวแรกของคำเป็นตัวใหญ่
        titlecase - dict ไม่มีคำแปล จะต้องได้ค่าว่างกลับมา

        not dict - จะต้องได้ค่าว่างกลับมา
        """

        # is translation
        is_data = {
            "en": "J1 LEAGUE",
            "id": "Liga J1",
            "th": "J1 ลีก",
            "vi": "J1 LEAGUE",
        }
        is_translation_en = get_translation_obj_value(is_data)
        self.assertEqual(
            is_translation_en,
            "J1 LEAGUE",
            "'is data[en]' translation is not equal to 'J1 LEAGUE'",
        )

        is_translation_th = get_translation_obj_value(is_data, lang_code="th")
        self.assertEqual(
            is_translation_th,
            "J1 ลีก",
            "'is data[th]' translation is not equal to 'J1 ลีก'",
        )

        # not translation
        is_not_data = {
            "en": "",
            "id": "Liga J1",
            "th": "J1 ลีก",
        }
        is_translation_en = get_translation_obj_value(is_not_data)
        self.assertEqual(
            is_translation_en,
            "",
            "'is not data[en]' translation is not equal to ''",
        )

        is_translation_vi = get_translation_obj_value(is_not_data, lang_code="vi")
        self.assertEqual(
            is_translation_vi,
            "",
            "'is not data[vi]' translation is not equal to ''",
        )

        # not dict
        not_data = []
        not_translation = get_translation_obj_value(not_data)
        self.assertEqual(
            not_translation,
            "",
            "'is not dict' translation is not equal to ''",
        )

        # is default, is not translation
        default_data = {
            "en": "",
            "id": "Liga J1",
            "th": "J1 ลีก",
            "vi": "J1 LEAGUE",
        }
        default_translation = get_translation_obj_value(default_data, default="J1")
        self.assertEqual(
            default_translation,
            "J1",
            "'default data[en]' translation is not equal to 'J1'",
        )

        # is default, is translation
        default_data = {
            "en": "J1 LEAGUE",
            "id": "Liga J1",
            "th": "J1 ลีก",
            "vi": "J1 LEAGUE",
        }
        default_translation_id = get_translation_obj_value(
            default_data, default="J1", lang_code="id"
        )
        self.assertEqual(
            default_translation_id,
            "Liga J1",
            "'default data[id]' translation is not equal to 'Liga J1'",
        )

        # is option, `is translation
        is_data_option = {
            "en": "j1 lEague",
            "id": "Liga J1",
            "th": "J1 ลีก",
            "vi": "J1 LEAGUE",
        }

        # uppercase
        is_translation_uppercase = get_translation_obj_value(
            is_data_option, uppercase=True
        )
        self.assertEqual(
            is_translation_uppercase,
            "J1 LEAGUE",
            "'is data[en], uppercase' translation is not equal to 'J1 LEAGUE'",
        )

        # lowercase
        is_translation_lowercase = get_translation_obj_value(
            is_data_option, lowercase=True
        )
        self.assertEqual(
            is_translation_lowercase,
            "j1 league",
            "'is data[en], lowercase' translation is not equal to 'j1 league'",
        )

        # titlecase
        is_translation_titlecase = get_translation_obj_value(
            is_data_option, titlecase=True
        )
        self.assertEqual(
            is_translation_titlecase,
            "J1 League",
            "'is data[en], titlecase' translation is not equal to 'J1 League'",
        )

        # is option, not translation
        is_data_option = {
            "en": "",
            "id": "Liga J1",
            "th": "J1 ลีก",
            "vi": "J1 LEAGUE",
        }

        # uppercase
        is_translation_uppercase = get_translation_obj_value(
            is_data_option, uppercase=True
        )
        self.assertEqual(
            is_translation_uppercase,
            "",
            "'is not data[en], uppercase' translation is not equal to ''",
        )

    def test_get_competition_name(self):
        """
        is translation, use_short - มีคำแปล จะต้องได้คำแปลกลับมา
        is not translation, use_short - ไม่มีคำแปล จะต้องได้ชื่อปกติกลับมา
        not dict - จะต้องได้ค่าว่างกลับมา
        """

        # is translation, use_short
        is_competition = {
            "id": 2,
            "name": "MEIJI YASUDA J1 LEAGUE",
            "name_short": "MEIJI YASUDA J1 LEAGUE",
            "slug": "j1",
            "category_slug": "j1",
            "t_name": {
                "en": "MEIJI YASUDA J1 LEAGUE",
                "id": "Liga J1 Meiji Yasuda",
                "th": "เมจิ ยาสึดะ J1 ลีก",
                "vi": "MEIJI YASUDA J1 LEAGUE",
            },
            "t_name_short": {
                "en": "J1 LEAGUE",
                "id": "LIGA J1",
                "th": "J1 ลีก",
                "vi": "J1 LEAGUE",
            },
        }

        # is translation
        is_translation_en = get_competition_name(is_competition)
        self.assertEqual(
            is_translation_en,
            "MEIJI YASUDA J1 LEAGUE",
            "'Competition is data[en]' translation is not equal to 'MEIJI YASUDA J1 LEAGUE'",
        )

        is_translation_th = get_competition_name(is_competition, lang_code="th")
        self.assertEqual(
            is_translation_th,
            "เมจิ ยาสึดะ J1 ลีก",
            "'Competition is data[th]' translation is not equal to 'เมจิ ยาสึดะ J1 ลีก'",
        )

        # use_short
        is_translation_use_short = get_competition_name(is_competition, use_short=True)
        self.assertEqual(
            is_translation_use_short,
            "J1 LEAGUE",
            "'is data[en], use_short' translation is not equal to 'J1 LEAGUE'",
        )

        # is not translation
        is_not_competition = {
            "id": 2,
            "name": "MEIJI YASUDA J1 LEAGUE",
            "name_short": "MEIJI YASUDA J1 LEAGUE",
            "slug": "j1",
            "category_slug": "j1",
            "t_name": {
                "id": "Liga J1 Meiji Yasuda",
                "th": "เมจิ ยาสึดะ J1 ลีก",
                "vi": "MEIJI YASUDA J1 LEAGUE",
            },
            "t_name_short": {
                "id": "LIGA J1",
                "th": "J1 ลีก",
                "vi": "J1 LEAGUE",
            },
        }

        # is not translation
        is_not_translation = get_competition_name(is_not_competition)
        self.assertEqual(
            is_not_translation,
            "MEIJI YASUDA J1 LEAGUE",
            "'Competition is not data[en]' translation is not equal to 'MEIJI YASUDA J1 LEAGUE'",
        )

        # use_short
        is_not_translation_use_short = get_competition_name(
            is_not_competition, use_short=True
        )
        self.assertEqual(
            is_not_translation_use_short,
            "MEIJI YASUDA J1 LEAGUE",
            "'Competition is not data[en]' translation is not equal to 'MEIJI YASUDA J1 LEAGUE'",
        )

        # not dict
        not_data = []
        not_translation = get_competition_name(not_data)
        self.assertEqual(
            not_translation,
            "",
            "'Competition is not dict' translation is not equal to ''",
        )

    def test_get_club_name(self):
        """
        is translation, use_short, use_four_letters - มีคำแปล จะต้องได้คำแปลกลับมา
        is not translation, use_short, use_four_letters - ไม่มีคำแปล จะต้องได้ชื่อปกติกลับมา
        not dict - จะต้องได้ค่าว่างกลับมา
        """

        # is translation, use_short, use_four_letters
        is_club = {
            "id": 46,
            "name": "Yokohama F･Marinos",
            "name_slug": "Yokohama-F-Marinos",
            "slug": "yokohamafm",
            "allow_view_profile": True,
            "t_name": {
                "en": "Yokohama F･Marinos",
                "id": "Yokohama F･Marinos",
                "th": "โยโกฮาม่า เอฟ･มารินอส",
                "vi": "Yokohama F･Marinos",
            },
            "t_name_short": {
                "en": "Yokohama F･M",
                "id": "Yokohama F･M",
                "th": "มารินอส",
                "vi": "Yokohama F･M",
            },
            "four_letters_name": "Y-FM",
        }

        # is translation
        is_translation_en = get_club_name(is_club)
        self.assertEqual(
            is_translation_en,
            "Yokohama F･Marinos",
            "'is data[en]' translation is not equal to 'Yokohama F･Marinos'",
        )

        is_translation_th = get_club_name(is_club, lang_code="th")
        self.assertEqual(
            is_translation_th,
            "โยโกฮาม่า เอฟ･มารินอส",
            "'is data[th]' translation is not equal to 'โยโกฮาม่า เอฟ･มารินอส'",
        )

        # is translation - use_short
        is_translation_use_short = get_club_name(is_club, use_short=True)
        self.assertEqual(
            is_translation_use_short,
            "Yokohama F･M",
            "'is data[en], use_short' translation is not equal to 'Yokohama F･M'",
        )

        # is translation - use_four_letters
        is_translation_use_four_letters = get_club_name(is_club, use_four_letters=True)
        self.assertEqual(
            is_translation_use_four_letters,
            "Y-FM",
            "'is data[en], use_four_letters' translation is not equal to 'Y-FM'",
        )

        # not translation, use_short, use_four_letters
        is_not_data = {
            "id": 46,
            "name": "Yokohama F･Marinos",
            "name_slug": "Yokohama-F-Marinos",
            "slug": "yokohamafm",
            "allow_view_profile": True,
            "t_name": {
                "en": "",
                "id": "Yokohama F･Marinos",
                "th": "โยโกฮาม่า เอฟ･มารินอส",
                "vi": "Yokohama F･Marinos",
            },
            "t_name_short": {
                "en": "",
                "id": "Yokohama F･M",
                "th": "มารินอส",
                "vi": "Yokohama F･M",
            },
            "four_letters_name": "",
        }

        # not translation
        is_not_translation_en = get_club_name(is_not_data)
        self.assertEqual(
            is_not_translation_en,
            "Yokohama F･Marinos",
            "'is not data[en]' translation is not equal to 'Yokohama F･Marinos'",
        )

        is_not_translation_id = get_club_name(is_not_data)
        self.assertEqual(
            is_not_translation_id,
            "Yokohama F･Marinos",
            "'is not data[id]' translation is not equal to 'Yokohama F･Marinos'",
        )

        # not translation - use_short
        is_not_translation_use_short = get_club_name(is_not_data, use_short=True)
        self.assertEqual(
            is_not_translation_use_short,
            None,
            "'is not data[en], use_short' translation is not equal to 'None'",
        )

        # not translation - use_four_letters
        is_not_translation_use_four_letters = get_club_name(
            is_not_data, use_four_letters=True
        )
        self.assertEqual(
            is_not_translation_use_four_letters,
            "Yokohama F･Marinos",
            "'is data[en], use_four_letters' translation is not equal to 'Yokohama F･Marinos'",
        )

        # not dict
        not_data = []
        not_translation = get_club_name(not_data)
        self.assertEqual(
            not_translation,
            "",
            "'is not dict' translation is not equal to ''",
        )

    def test_get_player_name(self):
        """
        is translation, use_short - มีคำแปล จะต้องได้คำแปลกลับมา
        is not translation, use_short - ไม่มีคำแปล จะต้องได้ชื่อปกติกลับมา
        not dict - จะต้องได้ค่าว่างกลับมา
        """

        # is translation, use_short
        is_player = {
            "id": 5091,
            "name": "NASSIM BEN KHALIFA",
            "name_short": "N. BEN KHALIFA",
            "name_slug": "NASSIM-BEN-KHALIFA",
            "jersey_no": 13,
            "position": "FW",
            "allow_view_profile": True,
            "t_name": {
                "en": "NASSIM BEN KHALIFA",
                "id": "NASSIM BEN KHALIFA",
                "th": "นาสซิม เบน คาลิฟา",
                "vi": "NASSIM BEN KHALIFA",
            },
            "t_name_short": {
                "en": "N. BEN KHALIFA",
                "id": "N. BEN KHALIFA",
                "th": "เบ็น คาลิฟา",
                "vi": "N. BEN KHALIFA",
            },
        }

        # is translation
        is_translation_en = get_player_name(is_player)
        self.assertEqual(
            is_translation_en,
            "NASSIM BEN KHALIFA",
            "'is data[en]' translation is not equal to 'NASSIM BEN KHALIFA'",
        )

        is_translation_vi = get_player_name(is_player, lang_code="vi")
        self.assertEqual(
            is_translation_vi,
            "NASSIM BEN KHALIFA",
            "'is data[vi]' translation is not equal to 'NASSIM BEN KHALIFA'",
        )

        # is translation - use_short
        is_translation_use_short = get_player_name(is_player, use_short=True)
        self.assertEqual(
            is_translation_use_short,
            "N. BEN KHALIFA",
            "'is data[en]' translation is not equal to 'N. BEN KHALIFA'",
        )

        # is not translation, use_short
        is_not_player = {
            "id": 5091,
            "name": "NASSIM BEN KHALIFA",
            "name_short": "N. BEN KHALIFA",
            "name_slug": "NASSIM-BEN-KHALIFA",
            "jersey_no": 13,
            "position": "FW",
            "allow_view_profile": True,
            "t_name": {
                "en": "",
                "id": "NASSIM BEN KHALIFA",
                "vi": "NASSIM BEN KHALIFA",
            },
            "t_name_short": {
                "en": "",
                "id": "N. BEN KHALIFA",
                "th": "เบ็น คาลิฟา",
                "vi": "N. BEN KHALIFA",
            },
        }

        # is not translation
        is_not_translation_en = get_player_name(is_not_player)
        self.assertEqual(
            is_not_translation_en,
            "NASSIM BEN KHALIFA",
            "'is not data[en]' translation is not equal to 'NASSIM BEN KHALIFA'",
        )

        is_not_translation_th = get_player_name(is_not_player, lang_code="th")
        self.assertEqual(
            is_not_translation_th,
            "NASSIM BEN KHALIFA",
            "'is not data[th]' translation is not equal to 'NASSIM BEN KHALIFA'",
        )

        # is not translation - use_short
        is_not_translation_use_short = get_player_name(is_not_player, use_short=True)
        self.assertEqual(
            is_not_translation_use_short,
            "N. BEN KHALIFA",
            "'is not data[en], use_short' translation is not equal to 'N. BEN KHALIFA'",
        )

        # not dict
        is_not_player = []
        not_translation = get_player_name(is_not_player)
        self.assertEqual(
            not_translation,
            "",
            "'not dict' translation is not equal to ''",
        )

    def test_get_local_time(self):
        """
        ไม่ส่งค่า format
        ไม่ส่งค่า format, time zone
        format - FIXTURE_DATE_FORMAT, DATETIME_FORMAT, MONTH_DAY_FORMAT
        ส่ง format ผิด
        ค่าว่าง datetime
        """

        full_date = datetime.datetime(2023, 8, 16, 15, 30, 0)
        user_tz = "Asia/Bangkok"

        # ไม่ส่งค่า format
        time_format = get_local_time(time=full_date, user_tz=user_tz)
        self.assertEqual(
            time_format,
            "15:30",
            "time is not equal to '15:30'",
        )

        # ไม่ส่งค่า format, time zone
        time_format = get_local_time(full_date)
        self.assertEqual(
            time_format,
            "15:30",
            "time is not equal to '15:30'",
        )

        # format - FIXTURE_DATE_FORMAT
        date_format = get_local_time(full_date, "FIXTURE_DATE_FORMAT", user_tz)
        self.assertEqual(
            date_format,
            "Wednesday, 16 Aug 2023",
            "date is not equal to 'Wednesday, 16 Aug 2023'",
        )

        # format - DATETIME_FORMAT
        datetime_format = get_local_time(full_date, "DATETIME_FORMAT", user_tz)
        self.assertEqual(
            datetime_format,
            "Wednesday, 16 August 2023 · 13:30",
            "datetime is not equal to 'Wednesday, 16 August 2023 · 13:30'",
        )

        # format - MONTH_DAY_FORMAT
        date_format = get_local_time(full_date, "MONTH_DAY_FORMAT", user_tz)
        self.assertEqual(
            date_format,
            "August 16",
            "date is not equal to 'August 16'",
        )

        # ส่ง format ผิด
        date_format = get_local_time(full_date, "XXXX_XXXX", user_tz)
        self.assertEqual(
            date_format,
            "XXXX_XXXX",
            "date is not equal to 'XXXX_XXXX'",
        )

        # ค่าว่าง datetime
        full_date = ""
        _format = get_local_time(full_date, "DATE_FORMAT", user_tz)
        self.assertEqual(
            _format,
            "",
            "time is not equal to ''",
        )
