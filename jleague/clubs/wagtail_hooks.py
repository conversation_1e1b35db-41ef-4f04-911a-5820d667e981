from django.urls import path, reverse
from wagtail.admin.menu import AdminOnlyMenuItem, Menu, SubmenuMenuItem
from wagtail import hooks
from .admin_views import club_staff_avatar, club_staff_diecut


@hooks.register('register_admin_urls')
def register_club_url():
    return [
        path('clubs/staffs/avatars/', club_staff_avatar, name='club_staff_avatar_upload'),
        path('clubs/staffs/diecuts/', club_staff_diecut, name='club_staff_diecut_upload'),
    ]


@hooks.register('register_clubs_menu_item')
def register_club_staff_avatar_menu_item():
    return AdminOnlyMenuItem('Staff Avatars', reverse('club_staff_avatar_upload'), icon_name='image', order=1)


@hooks.register('register_clubs_menu_item')
def register_club_staff_diecut_menu_item():
    return AdminOnlyMenuItem('Staff Diecuts', reverse('club_staff_diecut_upload'), icon_name='image', order=2)


@hooks.register('register_admin_menu_item')
def register_clubs_menu():
    clubs_menu = Menu(register_hook_name='register_clubs_menu_item', construct_hook_name='construct_clubs_menu')
    return SubmenuMenuItem('Clubs', clubs_menu, icon_name='home', order=101)
