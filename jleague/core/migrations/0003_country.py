# Generated by Django 3.1.13 on 2022-05-10 03:38

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('core', '0002_auto_20220121_1814'),
    ]

    operations = [
        migrations.CreateModel(
            name='Country',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('two_letter_code', models.Char<PERSON>ield(db_index=True, max_length=2, unique=True)),
                ('three_letter_code', models.Char<PERSON>ield(db_index=True, max_length=3, unique=True)),
                ('created_at', models.DateTime<PERSON>ield(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
    ]
