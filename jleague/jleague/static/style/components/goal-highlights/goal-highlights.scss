@import 'setup/variable';
@import 'setup/mixin';

.goal-highlights {
  padding: 80px 120px;
  max-width: 1440px;
  margin: auto;

  .goal-highlights__header {
    max-width: 1440px;
    display: flex;
    align-items: center;
    gap: 20px;

    .title {
      @include section-header-title-text();
    }
  }

  .goal-highlights__body {
    position: relative;
    margin-top: 40px;
    min-height: 270px;
  }
}

@media screen and (max-width: 1296px) {
  .goal-highlights {
    padding: 80px 48px;
  }
}

@media screen and (max-width: 768px) {
  .goal-highlights {
    padding: 80px 24px;
  }
}

@media screen and (max-width: 576px) {
  .goal-highlights {
    padding: 40px 16px;
  }
}

@import 'videos-list';
@import 'video-info';
@import 'video-modal';
