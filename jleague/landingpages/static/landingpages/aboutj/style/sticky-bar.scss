@import 'setup/variable';

@keyframes pulseAnimation {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}


.jl-button-cta {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  height: 48px;
  background-color: $color-red;
  border: 1px solid $color-red;
  border-radius: 25px;
  animation: pulseAnimation 2s infinite ease-in-out;
  transition: all 0.3s linear;
  cursor: pointer;

  .jl-button__label-cta  {
    color: $color-white;
    font-family: $font-barlow-condensed;
    font-style: normal;
    font-weight: 700;
    font-size: 20px;
    line-height: 24px;
    text-align: center;
    font-feature-settings: 'clig' off, 'liga' off;
    letter-spacing: 1.667px;
    text-transform: uppercase;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    word-break: break-all;
    transition: all 0.3s;
  }

  &:hover:not(:disabled) {
    opacity: 1;
    background: $color-white;
    border-color: $color-black;
    cursor: pointer;

    .jl-button__label-cta {
      color: $color-black !important;
    }
  }

}

.sticky-bar {
  position: fixed;
  width: 100%;
  height: 80px;
  bottom: 0;
  left: 0;
  overflow: hidden;
  background: var(--color-white);
  box-shadow: 0 3px 5px 0 var(--color-black);
  z-index: 0;
  transition: all 0.3s ease-in-out;
  opacity: 1;


  .sticky-bar__body {
    max-width: 2560px;
    margin: 0 auto;
    height: 100%;
    position: relative;
    padding: 15px;
    gap: 15px;

    .sticky-bar__body-wrapper{
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 20px;
    }
  }


  .sponsor-logo {
    display: block;

    .sponsor-logo__image {
      display: block;
      width: auto;
      height: 50px;

      &.compact-size {
        display: none;
      }

      &.full-size {
        display: block;
      }
    }
  }

  &.sticky-bar--closed {
    height: 0;
    opacity: 0;
  }

  @media screen and (max-width: 768px) {
    .sticky-bar__body {
      gap: 8px;
      padding: 20px;
      .sponsor-logo {
        .sponsor-logo__image {
          height: 28px;
        }
      }
    .jl-button-cta {
        padding: 8px 16px;
        height: 40px;

        .jl-button__label-cta {
          font-size: 16px;
          line-height: 20px;
        }
      }
    }
  }
}
