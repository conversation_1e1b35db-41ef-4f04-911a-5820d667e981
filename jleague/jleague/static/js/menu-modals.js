(function () {
  const MENU_LANGUAGE = 'header nav .language-chooser';
  const MENU_LANGUAGE_OPEN_CLASS = 'language-choices--opened';
  let languageMenuChoices;
  let languageMenuChoicesOpened = false;

  const MENU_MORE = 'header nav .more-menu';
  const MENU_MORE_OPEN_CLASS = 'more-menu-modal--opened';
  let moreModal;
  let moreModalOpened = false;

  function _onSelectLanguage(event, form) {
    // checkToken().then((token) => {
    const language = event.target.getAttribute('data-value');
    if (language) {
      form['language'].value = language;
      // form['csrfmiddlewaretoken'].value = token;
      form.submit();
    }
    // });
  }

  function _bindClickToLanguageChoice(choicesElement) {
    const choices = choicesElement.querySelectorAll('.language-choice');
    const form = choicesElement.querySelector('form');
    for (const choice of choices) {
      choice.onclick = (event) => _onSelectLanguage(event, form);
    }
  }

  function initLanguagesModal() {
    const languageMenu = document.querySelector(MENU_LANGUAGE);
    if (!languageMenu) {
      return;
    }

    const languageMenuTrigger = languageMenu.querySelector('.language-button');
    languageMenuChoices = languageMenu.querySelector('.language-choices');

    if (languageMenuTrigger && languageMenuChoices) {
      languageMenuTrigger.onclick = () => {
        languageMenuChoices.classList.toggle(MENU_LANGUAGE_OPEN_CLASS);
        languageMenuChoicesOpened = !languageMenuChoicesOpened;
      };

      _bindClickToLanguageChoice(languageMenuChoices);
    }
  }

  function initMoreMenuModal() {
    const moreMenu = document.querySelector(MENU_MORE);
    if (!moreMenu) {
      return;
    }

    const moreModalTrigger = moreMenu.querySelector('.more-menu-trigger');
    moreModal = moreMenu.querySelector('.more-menu-modal');

    if (moreModalTrigger && moreModal) {
      moreModalTrigger.onclick = () => {
        moreModal.classList.toggle(MENU_MORE_OPEN_CLASS);
        moreModalOpened = !moreModalOpened;
      };

      // language chooser
      const moreModalLanguageButton = moreModal.querySelector('.more-menu-nav .more-menu-nav__item--language');
      const moreModalLanguageChoices = moreModal.querySelector('.more-menu-nav .language-choices');
      const moreModalLanguageButtonArrow = moreModalLanguageButton.querySelector('.arrow');

      if (moreModalLanguageButton && moreModalLanguageChoices) {
        moreModalLanguageButton.onclick = () => {
          moreModalLanguageChoices.classList.toggle(MENU_LANGUAGE_OPEN_CLASS);
          moreModalLanguageButtonArrow.classList.toggle('arrow--invert');
        };

        _bindClickToLanguageChoice(moreModalLanguageChoices);
      }
    }
  }

  function initMobileMenuModal() {
    const mobileModalTrigger = document.querySelector('header .nav-container .mobile-menu-trigger');
    const mobileModal = document.querySelector('header .nav-container .mobile-menu-modal');
    const mobileModalCloseButton = mobileModal.querySelector('#mobile-menu-modal-close');
    const body = document.querySelector('body');

    if (mobileModal) {
      if (mobileModalTrigger) {
        mobileModalTrigger.onclick = () => {
          mobileModal.classList.toggle('d-none');
          body.classList.toggle('noscroll');
          body.classList.toggle('body--mobile-menu');
        };
      }

      if (mobileModalCloseButton) {
        mobileModalCloseButton.onclick = () => {
          mobileModal.classList.add('d-none');
          body.classList.remove('noscroll');
          body.classList.remove('body--mobile-menu');
        };
      }

      // language chooser
      const mobileModalLanguageButton = mobileModal.querySelector('.mobile-menu-nav .mobile-menu-nav__item--language');
      const mobileModalLanguageChoices = mobileModal.querySelector('.mobile-menu-nav .language-choices');
      const mobileModalLanguageButtonArrow = mobileModalLanguageButton.querySelector('.arrow');

      if (mobileModalLanguageButton && mobileModalLanguageChoices) {
        mobileModalLanguageButton.onclick = () => {
          mobileModalLanguageChoices.classList.toggle(MENU_LANGUAGE_OPEN_CLASS);
          mobileModalLanguageButtonArrow.classList.toggle('arrow--invert');
        };

        _bindClickToLanguageChoice(mobileModalLanguageChoices);
      }
    }
  }

  document.addEventListener('DOMContentLoaded', function (event) {
    initLanguagesModal();
    initMoreMenuModal();
    initMobileMenuModal();

    // close the modal when click outside it
    window.addEventListener(
      'click',
      (event) => {
        // language modal
        if (languageMenuChoicesOpened && languageMenuChoices && !event.target.closest(MENU_LANGUAGE)) {
          languageMenuChoices.classList.remove(MENU_LANGUAGE_OPEN_CLASS);
          languageMenuChoicesOpened = false;
        }

        // more modal
        if (moreModalOpened && moreModal && !event.target.closest(MENU_MORE)) {
          moreModal.classList.remove(MENU_MORE_OPEN_CLASS);
          moreModalOpened = false;
        }
      },
      false
    );
  });
})();
