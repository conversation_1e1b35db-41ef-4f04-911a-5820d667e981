$(function () {
  // create a map
  if (map) {
    const onRegionClickHandler = (event, region) => {
      const link = map_links && map_links[region];
      if (link) {
        window.open(link, '_blank');
      } else {
        window.open('https://www.youtube.com/@JLEAGUEInternational', '_blank');
      }
    };

    $('.broadcast-map__map').vectorMap({
      map: 'world_en', // display area: world_en, usa_en, europe_en, germany_en
      backgroundColor: null,
      color: '#ede2da', // region color
      hoverOpacity: 0.7,
      selectedColor: false,
      enableZoom: true,
      showTooltip: true,
      values: map,
      scaleColors: ['#ec1d24', '#87cefa'],
      normalizeFunction: 'polynomial',
      onRegionClick: onRegionClickHandler,
    });
  }
});
