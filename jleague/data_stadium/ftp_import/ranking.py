import aioftp
import logging
import xml.etree.ElementTree as ET
from clubs.models import Club
from standings.models import ClubRanking, ClubRankLog
from .helpers import (
    async_find_first,
    async_create,
    async_upsert,
    process_path_if_needed,
    read_file_to_str,
    xml_value,
)

logger = logging.getLogger(__name__)


async def import_team_rankings(ftp_client: aioftp.Client, file_path: str, file_stat: dict):
    """Import club rank of the season from DS
    The file which outputs club ranking of the season.
    source file: real/rank_team-{competition_id}.xml
    """
    async with process_path_if_needed(file_path, file_stat) as should_process:
        if not should_process:
            return

        # read XML file
        file_content = await read_file_to_str(ftp_client, file_path)
        root = ET.fromstring(file_content)  # Football node
        report_node = root.find('RankReport')
        year = int(xml_value(report_node, 'GameDate')[:4])
        competition_id = xml_value(report_node, 'GameKindID', int)
        season_id = xml_value(report_node, 'SeasonID', int)
        prev_group_id = None
        position = 0

        # process rankings
        for i, rank_info_node in enumerate(report_node.findall('RankInfo')):
            # check club exists
            club_id = xml_value(rank_info_node, 'TeamID', int)
            club = await async_find_first(Club, ds_club_id=club_id)
            if not club:
                logger.warn('Club not found (ds_club_id=%s)', club_id)
                continue

            # check ranking exists
            ranking = rank_info_node.attrib['Ranking']
            if not ranking:
                continue

            data = {
                'ranking': int(ranking),
                'replace': xml_value(rank_info_node, 'Replace', int),
                'points': xml_value(rank_info_node, 'Point', int),
                'games_played': xml_value(rank_info_node, 'Game', int),
                'games_won': xml_value(rank_info_node, 'Win', int),
                'games_drawn': xml_value(rank_info_node, 'Draw', int),
                'games_lost': xml_value(rank_info_node, 'Lose', int),
                'goals_for': xml_value(rank_info_node, 'Score', int),
                'goals_against': xml_value(rank_info_node, 'Lost', int),
                'goals_difference': xml_value(rank_info_node, 'Differ', int),
            }

            group_id = xml_value(rank_info_node, 'GroupID')

            if group_id:
                position = position + 1 if prev_group_id == group_id else 1
            else:
                position = i + 1

            await async_upsert(
                ClubRanking,
                year=year,
                competition_id=competition_id,
                club=club,
                defaults={
                    **data,
                    'season_id': season_id,
                    'group_id': group_id,
                    'position': position,

                    # !NOTE these tags were removed since 2023-11-06
                    'aclspot_f': xml_value(rank_info_node, 'ACLSpotF', int),
                    'demotion_f': xml_value(rank_info_node, 'DemotionF', int),
                    'promotion_f': xml_value(rank_info_node, 'PromotionF', int),
                    'playoff_f': xml_value(rank_info_node, 'PlayoffF', int),
                    'demotion_f2': xml_value(rank_info_node, 'DemotionF2', int),
                    'promotion_f2': xml_value(rank_info_node, 'PromotionF2', int),
                    'j1_playoff_f2': xml_value(rank_info_node, 'J1PlayoffF2', int),
                    'ylc_playoff_f': xml_value(rank_info_node, 'YLCPlayoffF', int),

                    # !NOTE: these tags were added on 2023-01-16
                    'ylc_promotion_f': xml_value(rank_info_node, "YLCPromotionF", int),
                    'demotion_f3': xml_value(rank_info_node, "DemotionF3", int),
                    'jfl_playoff_f': xml_value(rank_info_node, "JFLPlayoff", int),

                    # !NOTE: these tag was added on 2023-11-06
                    'step_type': xml_value(rank_info_node, "StepType", int),
                },
            )

            await async_create(
                ClubRankLog,
                year=year,
                competition_id=competition_id,
                club=club,
                **data,
            )

            prev_group_id = group_id

        logger.info('Processed club rankings')
