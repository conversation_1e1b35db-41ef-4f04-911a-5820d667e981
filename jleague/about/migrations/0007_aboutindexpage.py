# Generated by Django 4.1.4 on 2022-12-24 07:59

from django.db import migrations, models
import django.db.models.deletion
import wagtail.blocks
import wagtail.contrib.table_block.blocks
import wagtail.fields
import wagtail.images.blocks


class Migration(migrations.Migration):
    dependencies = [
        ("wagtailcore", "0078_referenceindex"),
        ("about", "0006_auto_20220804_2236"),
    ]

    operations = [
        migrations.CreateModel(
            name="AboutIndexPage",
            fields=[
                (
                    "page_ptr",
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to="wagtailcore.page",
                    ),
                ),
                ("label", models.CharField(max_length=255)),
                (
                    "body",
                    wagtail.fields.StreamField(
                        [
                            ("richtext", wagtail.blocks.RichTextBlock()),
                            ("image", wagtail.images.blocks.ImageChooserBlock()),
                            ("table", wagtail.contrib.table_block.blocks.TableBlock()),
                            (
                                "sectionheader",
                                wagtail.blocks.StructBlock([("title", wagtail.blocks.CharBlock())]),
                            ),
                        ],
                        use_json_field=True,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
            bases=("wagtailcore.page",),
        ),
    ]
