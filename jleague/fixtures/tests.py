import datetime
from django.test import TestCase
from utils.helpers.etc import get_clubs_selector_choices
from .helper import get_fixture_competitions, get_fixtures


class FixturesTestCase(TestCase):
    def test_get_fixture_competitions(self):
        """competition
        - There must be 12 list (All, j1, levain, afc, j1 play-off, j2, j3, fujifilm super cup, asia challenge, jwc, afs, club friendly)
        - There must be name, slug in competition field
        """

        competition = get_fixture_competitions(2023, "latest")

        self.assertEqual(
            len(competition), 12, "The total of competition is not equal 12"
        )

        for com in competition:
            self.assertTrue(
                "name" in com, '"name" field does not exist in the competition'
            )
            self.assertTrue(
                "slug" in com, '"slug" field does not exist in the competition'
            )

    def test_get_clubs_selector_choices(self):
        """competition
        - All
            - 2021 -J1,J2,J3 -57 clubs
            - 2022 -J1,J2,J3 -58 clubs
        - J1
            - 2021 -J1 -20 clubs
            - 2022 -J1 -18 clubs
        - J2
            - 2022 -J2 -22 clubs
        - J3
            - 2021 -J3 -15 clubs
            - 2022 -J3 -18 clubs
        """

        clubs_year = {
            "all_2021": get_clubs_selector_choices("all", 2021),
            "all_2022": get_clubs_selector_choices("all", 2022),
            "j1_2021": get_clubs_selector_choices("j1", 2021),
            "j1_2022": get_clubs_selector_choices("j1", 2022),
            "j2_2022": get_clubs_selector_choices("j2", 2022),
            "j3_2021": get_clubs_selector_choices("j3", 2021),
            "j3_2022": get_clubs_selector_choices("j3", 2022),
        }

        for key, clubs in clubs_year.items():
            count = 0
            for club in clubs:
                count += len(club["children"])

            if key == "all_2021":
                self.assertEqual(
                    count, 57, "The total of all clubs in 2021 is not equal 57"
                )
            elif key == "all_2022":
                self.assertEqual(
                    count, 58, "The total of all clubs in 2022 is not equal 58"
                )

            elif key == "j1_2021":
                self.assertEqual(
                    count, 20, "The total of clubs J1 in 2021 is not equal 20"
                )
            elif key == "j1_2022":
                self.assertEqual(
                    count, 18, "The total of clubs J1 in 2022 is not equal 18"
                )

            elif key == "j2_2022":
                self.assertEqual(
                    count, 22, "The total of clubs J2 in 2022 is not equal 22"
                )

            elif key == "j3_2021":
                self.assertEqual(
                    count, 15, "The total of clubs J3 in 2021 is not equal 15"
                )
            elif key == "j3_2022":
                self.assertEqual(
                    count, 18, "The total of clubs J3 in 2022 is not equal 18"
                )

    def test_get_fixtures(self):
        """- There must be home club, away club, name, logo and stadium in fixtures

        - The matchweek competition is equal to selected
        - The total of game in matchweek
           - J1
               - 2022 = 9 match
           - J2
               - 2022 = 11 match
           - J3
               - 2021 = 7 match

        - Latest Fixtures of all clubs
        - Latest Fixtures of selected competition and all clubs
        - Latest Fixtures of selected competition and selected club
        - All Fixtures of all clubs
        - All Fixtures of selected competition and all clubs
           - แสดง หน้า 7 หลัง 7 ณ วันที่ปัจจุบัน
           - The total of club in match day is equal to 1
           - The total of home score, away score is greater than or equal 0

        - Selected month Fixtures of all clubs
        - Selected month Fixtures of selected competition and all clubs
        - Selected month Fixtures of selected competition and selected club
           - แสดงการแข่งขันในเดือนที่เลือก
           - There must be home_team_id, away_team_id, stadium in games data
           - There must be name, slug in home games data
           - There must be name, slug in away games data

        - All Fixtures of selected club
        - All Fixtures of selected competition and selected club
           - There must be of club selected  in all fixtures
        """

        # Selected month
        fixtures_selected_month_10 = {
            "nagoya": get_fixtures(
                competition_slug="j1",
                year=2022,
                month="10",
                club_slug="nagoya",
            ),
            "selected_month_competition_2022": get_fixtures(
                competition_slug="j1",
                year=2022,
                month="10",
                club_slug="all",
            ),
            "selected_month_2022": get_fixtures(
                competition_slug="all",
                year=2022,
                month="10",
                club_slug="all",
            ),
            "selected_month_2021": get_fixtures(
                competition_slug="all",
                year=2021,
                month="10",
                club_slug="all",
            ),
        }

        for slug, fixture in fixtures_selected_month_10.items():
            for day, data in fixture.items():
                for games in data.values():
                    if games:
                        self.assertTrue(
                            (
                                games["games"][0]["game_date"]
                                >= datetime.date(
                                    games["games"][0]["game_date"].year, 10, 1
                                )
                                and games["games"][0]["game_date"]
                                <= datetime.date(
                                    games["games"][0]["game_date"].year, 10, 31
                                )
                            ),
                            "Date does not exist in month selected",
                        )

                        self.assertTrue(
                            "home_team_id" in games["games"][0],
                            '"home_team_id" field does not exist in the games',
                        )
                        self.assertTrue(
                            "away_team_id" in games["games"][0],
                            '"away_team_id" field does not exist in the games',
                        )

                        self.assertTrue(
                            "name" in games["games"][0]["home"],
                            '"name" field does not exist in the games-home',
                        )
                        self.assertTrue(
                            "name" in games["games"][0]["away"],
                            '"name" field does not exist in the games-away',
                        )

                        self.assertTrue(
                            "slug" in games["games"][0]["home"],
                            '"slug" field does not exist in the games-home',
                        )
                        self.assertTrue(
                            "slug" in games["games"][0]["away"],
                            '"slug" field does not exist in the games-away',
                        )

                        self.assertTrue(
                            "stadium" in games["games"][0],
                            '"stadium" field does not exist in the games',
                        )

                        if slug == "nagoya":
                            if games["games"][0]["home"]["slug"] == slug:
                                continue
                            elif games["games"][0]["away"]["slug"] == slug:
                                continue
                            else:
                                self.assertTrue(
                                    "" == slug, "slug does not exist in the games"
                                )

        # Selected club
        fixtures_selected_club = {
            "tochigi": get_fixtures(
                competition_slug="all",
                year=2022,
                month="all",
                team_slug="tochigi",
            ),
            "imabari": get_fixtures(
                competition_slug="j3",
                year=2021,
                month="all",
                team_slug="imabari",
            ),
        }

        for slug, fixture in fixtures_selected_club.items():
            for data in fixture.values():
                for games in data.values():
                    for game in games["games"]:
                        if game["home"]["slug"] == slug:
                            continue
                        elif game["away"]["slug"] == slug:
                            continue
                        else:
                            self.assertTrue(
                                "" == slug, "slug does not exist in the games"
                            )

        # All - Latest
        fixtures_all_latest = {
            "all_2022": get_fixtures(
                competition_slug="all",
                year=2022,
                month="all",
                team_slug="all",
            ),
            "all_2021": get_fixtures(
                competition_slug="all",
                year=2021,
                month="all",
                team_slug="all",
            ),
            "all_2022_selected_competition": get_fixtures(
                competition_slug="j2",
                year=2022,
                month="all",
                team_slug="all",
            ),
            "latest_all_2022": get_fixtures(
                competition_slug="all",
                year=2022,
                month="latest",
                team_slug="all",
            ),
            "latest_all_2021": get_fixtures(
                competition_slug="all",
                year=2021,
                month="latest",
                team_slug="all",
            ),
            "latest_2022_selected_competition": get_fixtures(
                competition_slug="j3",
                year=2022,
                month="latest",
                team_slug="all",
            ),
            "latest_2022_selected_competition_club": get_fixtures(
                competition_slug="j1",
                year=2022,
                month="latest",
                team_slug="kobe",
            ),
        }

        date_before = datetime.date.today() + datetime.timedelta(days=-7)
        date_after = datetime.date.today() + datetime.timedelta(days=7)

        for fixture in fixtures_all_latest.values():
            for day, data in fixture.items():
                self.assertTrue(
                    (day >= date_before and day <= date_after),
                    "วันที่ไม่ได้อยู่ในช่วงเวลา (+- 7 วัน)",
                )

                clubs = {}
                for games in data.values():
                    for game in games["games"]:
                        # การแข่งขันจบแล้ว
                        if game["details"]:
                            self.assertGreaterEqual(
                                game["home_score"],
                                0,
                                "The total of home score is less than 0",
                            )
                            self.assertGreaterEqual(
                                game["away_score"],
                                0,
                                "The total of away score is less than 0",
                            )
                            if game["home_score"] > 0 or game["away_score"] > 0:
                                self.assertTrue(
                                    game["details"]["goals"],
                                    "The details goals data is None",
                                )

                        # เช็กต้องไม่มีทีมซ้ำใน 1 วัน
                        if clubs.get(game["home_team_id"]) is None:
                            clubs[game["home_team_id"]] = 0
                        clubs[game["home_team_id"]] += 1

                        if clubs.get(game["away_team_id"]) is None:
                            clubs[game["away_team_id"]] = 0
                        clubs[game["away_team_id"]] += 1

                for i in clubs.values():
                    self.assertEqual(
                        i, 1, "The total of club in match day is not equal to 1"
                    )

        # Matchweek
        fixtures_matchweek = {
            "9": get_fixtures(
                competition_slug="j1",
                year=2022,
                occasion_no=31,
                stage=None,
                stage_round=None,
                team_slug="all",
            ),
            "11": get_fixtures(
                competition_slug="j2",
                year=2022,
                occasion_no=20,
                stage=None,
                stage_round=None,
                team_slug="all",
            ),
            "7": get_fixtures(
                competition_slug="j3",
                year=2021,
                occasion_no=10,
                stage=None,
                stage_round=None,
                team_slug="all",
            ),
        }

        for total_match, matchweek in fixtures_matchweek.items():
            count = 0
            competition_name = ""
            for data in matchweek.values():
                for games in data.values():
                    if games:
                        count += len(games["games"])
                        competition_name = games["competition"].name
                        for game in games["games"]:
                            self.assertEqual(
                                str(game["competition"]["name"]),
                                str(competition_name),
                                "The competition slug is not equal to "
                                + str(competition_name),
                            )

            self.assertEqual(
                count,
                int(total_match),
                "The total of matchweek is not equal to" + total_match,
            )
