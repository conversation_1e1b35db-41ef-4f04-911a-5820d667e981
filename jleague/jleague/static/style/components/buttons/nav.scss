@import 'setup/variable';
@import 'setup/mixin';

.btn-nav {
  background: $color-white;
  border: none;
  border-bottom-color: $color-earth-2;
  border-bottom-style: solid;
  color: $color-black;
  white-space: nowrap;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;

  * {
    pointer-events: none;
  }

  &:active {
    background: $color-earth-3;
  }

  &:hover {
    background: $color-earth-2;
    border-bottom-color: $color-red;
    opacity: 1;
  }

  &:disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }

  &.large {
    font-family: $font-overpass;
    font-style: normal;
    font-weight: 600;
    font-size: 24px;
    line-height: 32px;
    height: 72px;
    padding: 0 32px;
    border-bottom-width: 12px;

    @media screen and (max-width: 1199px) {
      height: 56px;
      padding: 0 24px;
      border-bottom-width: 8px;
      font-size: 20px;
    }

    @media screen and (max-width: 767px) {
      height: 40px;
      border-bottom-width: 4px;
      font-size: 16px;
      line-height: 20px;
    }

    & > img:nth-child(1) {
      margin-right: 10px;
    }
  }

  &.medium {
    min-width: 70px;
    font-family: $font-overpass;
    font-style: normal;
    font-weight: 600;
    font-size: 20px;
    line-height: 32px;
    height: 56px;
    padding: 0 24px;
    border-bottom-width: 8px;

    @media screen and (max-width: 767px) {
      height: 44px;
      padding: 0 16px;
      border-bottom-width: 4px;
      font-size: 16px;
      line-height: 20px;
    }
  }

  &.small {
    height: 40px;
    padding: 0 24px;
    border-bottom-width: 4px;
    font-family: $font-overpass;
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 20px;
  }

  &.active {
    border-bottom-color: $color-red;
    color: $color-red;
  }
}

body.lang-vi {
  .btn-nav {
    @include font-vietnamese();
    font-weight: 600;
  }
}

body.lang-th {
  .btn-nav {
    @include font-thai();
  }
}
