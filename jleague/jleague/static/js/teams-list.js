(function ($) {
  $(document).ready(function () {
    const teamsContainer = $('.header-teams-list__teams');
    const competitionSelector = $('.header-teams-list__competitions__selector');
    const _sendRequest = (gameKind = 'j1') => {
      return ajaxRequest(`/widgets/teams_list/${gameKind}/`).then((data) => {
        teamsContainer.empty();
        teamsContainer.append(data);
      });
    };

    competitionSelector.change(() => {
      const gameKind = competitionSelector.val();

      sendElementClickEvent('header-teams-list', gameKind, '.header-teams-list__competitions__selector');

      _sendRequest(gameKind);
    });

    _sendRequest();
  });
})(jQuery);
