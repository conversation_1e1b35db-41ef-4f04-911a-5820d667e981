<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 25.0.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 2800 1400" style="enable-background:new 0 0 2800 1400;" xml:space="preserve">
<style type="text/css">
	.st0{clip-path:url(#SVGID_2_);fill:#1C2761;}
	.st1{clip-path:url(#SVGID_4_);}
	.st2{fill:#00588D;}
	.st3{clip-path:url(#SVGID_6_);fill:url(#SVGID_7_);}
	.st4{clip-path:url(#SVGID_9_);fill:url(#SVGID_10_);}
</style>
<g>
	<g>
		<g>
			<defs>
				<rect id="SVGID_1_" width="2800" height="1400"/>
			</defs>
			<clipPath id="SVGID_2_">
				<use xlink:href="#SVGID_1_"  style="overflow:visible;"/>
			</clipPath>
			<rect class="st0" width="2800" height="1400"/>
		</g>
		<g>
			<defs>
				<rect id="SVGID_3_" width="2800" height="1400"/>
			</defs>
			<clipPath id="SVGID_4_">
				<use xlink:href="#SVGID_3_"  style="overflow:visible;"/>
			</clipPath>
			<g class="st1">
				<polygon class="st2" points="-71.2,1259.4 1397.7,1468.4 2800,1259.4 2800,1221.4 1392.1,1401.9 -71.2,1207.1 				"/>
				<polygon class="st2" points="-71.2,1105 1397.7,1314.1 2800,1105 2800,1077.3 1392.1,1257.9 -71.2,1063.1 				"/>
				<polygon class="st2" points="-71.2,964.2 1397.7,1173.2 2800,964.2 2800,951.9 1392.1,1132.5 -71.2,937.7 				"/>
				<polygon class="st2" points="-71.2,873.6 1397.7,1082.7 2800,873.6 2800,861.4 1392.1,1041.9 -71.2,847.1 				"/>
				<polygon class="st2" points="-71.2,788.1 1397.7,997.2 2800,788.1 2800,765.6 1392.1,946.1 -71.2,751.3 				"/>
				<polygon class="st2" points="-71.2,713.2 1397.7,922.2 2800,713.2 2800,690.6 1392.1,871.2 -71.2,676.4 				"/>
				<polygon class="st2" points="-71.2,638.1 1397.7,847.1 2800,638.1 2800,615.6 1392.1,796.1 -71.2,601.3 				"/>
				<polygon class="st2" points="-71.2,577.7 1397.7,786.8 2800,577.7 2800,555.2 1392.1,735.7 -71.2,541 				"/>
				<polygon class="st2" points="-71.2,513.6 1397.7,722.7 2800,513.6 2800,491.1 1392.1,671.6 -71.2,476.8 				"/>
				<polygon class="st2" points="-71.2,451.9 1397.7,660.9 2800,451.9 2800,444.8 1392.1,625.3 -71.2,430.5 				"/>
				<polygon class="st2" points="-71.2,398.8 1397.7,607.9 2800,398.8 2800,391.8 1392.1,572.3 -71.2,377.5 				"/>
				<polygon class="st2" points="-71.2,336.7 1397.7,545.7 2800,336.7 2800,329.6 1392.1,510.2 -71.2,315.4 				"/>
				<polygon class="st2" points="-71.2,281.3 1397.7,490.4 2800,281.3 2800,274.3 1392.1,454.8 -71.2,260 				"/>
				<polygon class="st2" points="-71.2,221.5 1397.7,430.5 2800,221.5 2800,214.4 1392.1,395 -71.2,200.2 				"/>
				<polygon class="st2" points="-71.2,166.2 1397.7,375.2 2800,166.2 2800,159.1 1392.1,339.6 -71.2,144.9 				"/>
				<polygon class="st2" points="-71.2,106.3 1397.7,315.4 2800,106.3 2800,99.3 1392.1,279.8 -71.2,85 				"/>
				<polygon class="st2" points="-71.2,51 1397.7,260 2800,51 2800,43.9 1392.1,224.5 -71.2,29.7 				"/>
				<polygon class="st2" points="-71.2,-8.9 1397.7,200.2 2800,-8.9 2800,-15.9 1392.1,164.6 -71.2,-30.2 				"/>
				<polygon class="st2" points="-71.2,-64.2 1397.7,144.9 2800,-64.2 2800,-71.2 1392.1,109.3 -71.2,-85.5 				"/>
				<polygon class="st2" points="-71.2,-119.5 1397.7,89.5 2800,-119.5 2800,-126.6 1392.1,54 -71.2,-140.8 				"/>
			</g>
		</g>
		<g>
			<defs>
				<rect id="SVGID_5_" width="2800" height="1400"/>
			</defs>
			<clipPath id="SVGID_6_">
				<use xlink:href="#SVGID_5_"  style="overflow:visible;"/>
			</clipPath>
			
				<linearGradient id="SVGID_7_" gradientUnits="userSpaceOnUse" x1="1400" y1="11.9915" x2="1400" y2="1095.8967" gradientTransform="matrix(1 0 0 -1 0 1398.5511)">
				<stop  offset="0" style="stop-color:#00598F"/>
				<stop  offset="1" style="stop-color:#00568D;stop-opacity:0"/>
			</linearGradient>
			<rect class="st3" width="2800" height="1400"/>
		</g>
		<g>
			<defs>
				<rect id="SVGID_8_" width="2800" height="1400"/>
			</defs>
			<clipPath id="SVGID_9_">
				<use xlink:href="#SVGID_8_"  style="overflow:visible;"/>
			</clipPath>
			
				<linearGradient id="SVGID_10_" gradientUnits="userSpaceOnUse" x1="1258" y1="-796.0085" x2="1258" y2="287.8967" gradientTransform="matrix(-1 0 0 1 2658 809.4489)">
				<stop  offset="0" style="stop-color:#1C2761"/>
				<stop  offset="1" style="stop-color:#1C2761;stop-opacity:0"/>
			</linearGradient>
			<polygon class="st4" points="0,0 2800,0 2800,1400 0,1400 			"/>
		</g>
	</g>
</g>
</svg>
