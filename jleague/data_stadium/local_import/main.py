import logging
import os
from .clubs.club_stats import (
    import_club_stats,
    import_club_stat_details,
)
from .players.player_stats import import_players_stats


logger = logging.getLogger(__name__)

PATH_BASE = f'tmp/old_stats_data'
PATH_TEAM = f'total/team'
PATH_PLAYER = f'total/player'
PATH_RANKING = f'ranking'
DATA_PATHS = [
    PATH_TEAM,
    PATH_PLAYER,
    PATH_RANKING,
]


def file_execution_plan(path: str):  # noqa
    """Maps the XML file to the execution function

    Set priortiy and execution function based on file name

    Args:
        path: A path to the data stadium xml file

    Returns:
        A tuple of priority, path, execution function
    """
    file_name: str = path.split('/')[-1]
    priority = 999
    executor = None
    if file_name.startswith('team_Stats-'):
        priority = 24
        executor = import_club_stats
    elif file_name.startswith('team_Stats_details-'):
        priority = 25
        executor = import_club_stat_details
    elif file_name.startswith('player_Stats_details-'):
        priority = 26
        executor = import_players_stats
    return (priority, path, executor)


async def process_folder(folder_path: str, year: int):
    # list of items under the folder_path
    files = os.listdir(folder_path)
    # create execution plans
    execution_plans = [file_execution_plan(path) for path in files]
    # sorted execution plans by priority
    execution_plans = sorted(execution_plans, key=lambda x: x[0])
    # run execution plans
    for _, path, executor in execution_plans:
        if executor is not None:
            full_path = f'{folder_path}/{path}'
            await executor(full_path, year)


async def import_old_stats(year: int = None):
    if year is None:
        return

    # loop paths
    for path in DATA_PATHS:
        current_path = f'{PATH_BASE}/{year}/{path}'
        # check if path exists
        if os.path.exists(current_path):
            await process_folder(current_path, year)
