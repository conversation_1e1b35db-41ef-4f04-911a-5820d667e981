const ticketsModal = (function () {
  const SLT_MODAL = '.tickets-modal';
  const SLT_BACKDROP = '.tickets-modal__backdrop';
  const SLT_CLOSE_BUTTON = '.tickets-modal__close';
  const SLT_TICKETS_LIST = '.tickets-list__body';
  const CLS_MODAL_CLOSED = 'tickets-modal--closed';
  const CLS_BODY_NOSCROLL = 'noscroll';

  let isInit = false;
  let body;
  let modal;
  let ticketsList;
  let backdrop;
  let closeButton;
  let trigger;

  function triggerBindEvent() {
    if (!trigger) return;

    if (typeof trigger.length === 'undefined') {
      // only one trigger
      trigger.onclick = openModal;
    } else {
      // many triggers
      for (const t of trigger) {
        t.onclick = () => {
          ticketsList.replaceChildren();
          try {
            const links = JSON.parse(t.dataset.links);

            Object.keys(links).forEach((key) => {
              const a = document.createElement('a');
              const span = document.createElement('span');
              const label = document.createTextNode(key);
              span.classList.add('jl-button__label');
              span.appendChild(label);
              a.appendChild(span);
              a.classList.add('jl-button', 'jl-button--inverse');
              a.href = links[key];
              a.target = '_blank';
              a.rel = 'noopener';

              ticketsList.appendChild(a);
            });
            openModal();
          } catch (error) {
            console.error(error);
          }
        };
      }
    }
  }

  const closeModal = () => {
    modal.classList.add(CLS_MODAL_CLOSED);
    body.classList.remove(CLS_BODY_NOSCROLL);
  };

  const openModal = () => {
    modal.classList.remove(CLS_MODAL_CLOSED);
    body.classList.add(CLS_BODY_NOSCROLL);
  };

  function _preinit() {
    if (isInit) return;
    isInit = true;

    body = document.querySelector('body');
    modal = document.querySelector(SLT_MODAL);
    ticketsList = document.querySelector(SLT_TICKETS_LIST);

    if (modal) {
      backdrop = modal.querySelector(SLT_BACKDROP);
      closeButton = modal.querySelector(SLT_CLOSE_BUTTON);

      if (backdrop) backdrop.onclick = closeModal;
      if (closeButton) closeButton.onclick = closeModal;
    }
  }

  function init(triggerSelector) {
    _preinit();

    if (triggerSelector.startsWith('#')) {
      trigger = document.querySelector(triggerSelector);
    } else {
      trigger = document.querySelectorAll(triggerSelector);
    }
    triggerBindEvent();
  }

  return { init };
})();
