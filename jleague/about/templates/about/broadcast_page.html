{% extends 'base.html' %}

{% load pipeline static core_tags %}

{% block extra_styles %}
  {% stylesheet 'broadcast-page' %}
{% endblock %}

{% block extra_scripts %}
  <script>
    const map = JSON.parse('{{ broadcaster_map|safe }}');
    const map_links = JSON.parse('{{ broadcaster_country_links|safe }}');
  </script>

  {% javascript 'map' %}
  {% javascript 'broadcast-page' %}
{% endblock extra_scripts %}

{% block active_body_class %}template-about{% endblock %}

{% block content %}
  <main class="content-container content-container--has-breadcrumb">
    <section class="content-container__header">
      {% include 'about/components/about_breadcrumb.html' with current_page='Broadcast' %}
    </section>

    <section class="content-container__body">
      <div class="m-b-l">
        <p class="body-text body-text--strong body-text--large m-b-m">
          {% translation 'BROADCAST_PAGE_THE_MEIJI_YASUDA_JLEAGUE' %}
        </p>

        <div class="broadcast-map">
          <div class="broadcast-map__map"></div>

          <ul class="broadcast-map__legends">
            <li class="broadcast-map__legends__item">
              <span class="broadcast-map__legends__item__color red-bg"></span>
              <span class="broadcast-map__legends__item__label">
                {% translation 'BROADCAST_PAGE_OVERSEAS_BROADCAST' %}
              </span>
            </li>

            <li class="broadcast-map__legends__item">
              <span class="broadcast-map__legends__item__color earth-2-bg"></span>
              <span class="broadcast-map__legends__item__label">
                {% translation 'BROADCAST_PAGE_WORLDWIDE_EXCL_JAPAN' %}
              </span>
            </li>
          </ul>
        </div>
      </div>

      <div class="m-b-l">
        {% include 'components/section-header.html' with title_translation_key='BROADCAST_PAGE_BROADCAST_AREA' %}

        <table class="table">
          <thead>
            <tr>
              <th>
                <p class="body-text body-text--strong">
                  {% translation 'BROADCAST_PAGE_COUNTRY_REGION' %}
                </p>
              </th>

              <th>
                <p class="body-text body-text--strong">
                  {% translation 'BROADCAST_PAGE_BROADCASTING_NETWORK' %}
                </p>
              </th>

              <th>
                <p class="body-text body-text--strong">
                  {% translation 'BROADCAST_PAGE_COMPETITION' %}
                </p>
              </th>
            </tr>
          </thead>

          <tbody>
            {% for broadcaster in broadcasters %}
              <tr>
                <td>
                  <p class="body-text">
                    {% for country in broadcaster.countries %}
                      {{ country }}{% if forloop.counter < broadcaster.countries|length %},&nbsp;{% endif %}
                    {% endfor %}
                  </p>

                  {% if broadcaster.countries|length > 3 %}
                    <p class="body-text body-text--small">
                      {% with broadcaster.countries|length as ct %}
                        {% translation 'BROADCAST_PAGE_COUNTRIES_IN_TOTAL' number=ct|stringformat:"i" %}
                      {% endwith %}
                    </p>
                  {% endif %}

                  {% if broadcaster.country_note %}
                    <p class="body-text body-text--small">
                      {% translation broadcaster.country_note %}
                    </p>
                  {% endif %}
                </td>

                <td>
                  {% for network in broadcaster.networks %}
                    {% if network.link %}
                      <a
                        href="{{ network.link }}"
                        target="_blank"
                        rel="noopener"
                      >{{ network.name }}</a>{% if forloop.counter < broadcaster.networks|length %},&nbsp;{% endif %}
                    {% else %}
                      {{ network.name }}{% if forloop.counter < broadcaster.networks|length %},&nbsp;{% endif %}
                    {% endif %}
                  {% endfor %}
                </td>

                <td>
                  {% for competition in broadcaster.competitions %}
                    <p class="body-text">
                      {{ competition|upper }}
                    </p>
                  {% endfor %}

                  {% if broadcaster.competition_note %}
                    <p class="body-text body-text--small">
                      {% translation broadcaster.competition_note %}
                    </p>
                  {% endif %}
                </td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>

      <div class="m-b-l">
        {% include 'components/section-header.html' with title_translation_key='HOME_PAGE_LATEST_NEWS' %}

        {% include 'components/news-articles-list.html' with news_articles=latest_news %}
      </div>
    </section>
  </main>
{% endblock %}
