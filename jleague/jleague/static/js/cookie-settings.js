const cookieSettings = (function () {
  const CONSENT_COOKIE_NAME = 'jl_consent';
  const CONSENT_VERSION = 2;
  const CONSENT_RECHECK_DAYS = 7;
  let cookieSettings;
  let cookieConsent;

  function _updateFacebookConsent(settings) {
    if (typeof fbq !== 'function') return;

    const consentSettings = settings.targeting ? 'grant' : 'revoke'
    fbq('consent', consentSettings);
  }

  function _updateGoogleConsent(settings) {
    if (typeof gtag !== 'function') return;

    const consentSettings = {
      // targeting
      'ad_storage': settings.targeting ? 'granted' : 'denied',
      'personalization_storage': settings.targeting ? 'granted' : 'denied',
      // performance
      'analytics_storage': settings.performance ? 'granted' : 'denied',
      // functional
      'functionality_storage': settings.functional ? 'granted' : 'denied',
      'security_storage': settings.functional ? 'granted' : 'denied',
    };
    gtag('consent', 'update', consentSettings);
  }

  function _updateCookieSettings(settings) {
    if (!settings) return;

    _updateFacebookConsent(settings);
    _updateGoogleConsent(settings);
  }

  function _onCookieConsentSave(acceptAll = false) {
    const settings = {
      version: CONSENT_VERSION,
      functional: true,
      performance: true,
      targeting: true,
    };

    if (!acceptAll && cookieSettings) {
      // update settings
      settings.functional = cookieSettings.querySelector('#functional').checked;
      settings.performance = cookieSettings.querySelector('#performance').checked;
      settings.targeting = cookieSettings.querySelector('#targeting').checked;
    }

    // store consent settings and will re-check every X days
    setCookie(
      CONSENT_COOKIE_NAME,
      window.btoa(JSON.stringify(settings)),
      CONSENT_RECHECK_DAYS
    );

    // update conset
    _updateCookieSettings(settings);

    // hide consent setting and banner
    if (cookieSettings) {
      cookieSettings.classList.add('cookie-settings--closed');
      enableBodyScroll();
    }

    if (cookieConsent) {
      cookieConsent.classList.remove('cookie-consent--show');
    }
  }

  function _getCookieConsent() {
    const consent = getCookie(CONSENT_COOKIE_NAME);
    if (consent) {
      return JSON.parse(window.atob(consent));
    }
    return null;
  }

  function _checkCookieConsent() {
    // get consent settings
    const settings = _getCookieConsent();
    if (settings) {
      // check consent version
      if (settings.version === CONSENT_VERSION) {
        // update settings
        _updateCookieSettings(settings);
        return true;
      }
    }
    return false;
  }

  function initCookieSettings(force = false, customSettingSelector) {
    const isValidSettings = _checkCookieConsent();
    if (isValidSettings && !force) return;

    cookieConsent = document.querySelector('.cookie-consent');
    const cookieConsentSettings = cookieConsent.querySelector('#cookie-settings');
    const cookieConsentAccept = cookieConsent.querySelector('#cookie-accept-all');
    cookieSettings = document.querySelector('.cookie-settings');
    const cookieSettingsClose = cookieSettings.querySelector('.close');
    const cookieSettingsSave = cookieSettings.querySelector('#cookie-save');
    const cookieSettingsAccecpt = cookieSettings.querySelector('#cookie-accept-all');
    const accordionItems = cookieSettings.querySelectorAll('.cookie-item.accordion .accordion-header');

    // show cookie consent banner
    if (!isValidSettings) {
      cookieConsent.classList.add('cookie-consent--show');
    }

    // open settings
    cookieConsentSettings.onclick = () => {
      cookieSettings.classList.remove('cookie-settings--closed');
      disableBodyScroll();
    };

    // close settings
    cookieSettingsClose.onclick = () => {
      cookieSettings.classList.add('cookie-settings--closed');
      enableBodyScroll();
    };

    // accordion
    for (const accItem of accordionItems) {
      accItem.onclick = (event) => {
        if (!event.target.closest('.checkbox')) {
          const parent = event.target.parentNode;
          parent.classList.toggle('accordion--open');
        }
      }
    }

    // action button click
    cookieConsentAccept.onclick = () => _onCookieConsentSave(true);
    cookieSettingsAccecpt.onclick = () => _onCookieConsentSave(true);
    cookieSettingsSave.onclick = () => _onCookieConsentSave();

    if (customSettingSelector) {
      _initCustomCookieSettings(customSettingSelector);
    }
  }

  function _initCustomCookieSettings(selector) {
    const settings = document.querySelector(selector);

    // open settings
    settings.onclick = () => {
      _patchCookieSettings();
      cookieSettings.classList.remove('cookie-settings--closed');
      disableBodyScroll();
    };
  }

  function _patchCookieSettings() {
    const settings = _getCookieConsent();
    if (settings) {
      cookieSettings.querySelector('#functional').checked = settings.functional;
      cookieSettings.querySelector('#performance').checked = settings.performance;
      cookieSettings.querySelector('#targeting').checked = settings.targeting;
    }
  }

  return { initCookieSettings }
})();

document.addEventListener('DOMContentLoaded', function (event) {
  cookieSettings.initCookieSettings();
});
