import aioftp
import logging
import pytz
import typing as t
from asgiref.sync import sync_to_async
from contextlib import asynccontextmanager
from datetime import datetime
from django.utils import timezone
from ..models import PathSyncLog

japan_tiimezone = pytz.timezone('Asia/Tokyo')
logger = logging.getLogger(__name__)


def remote_path(s) -> str:
    return s
    # /test is a prefix that is probably going to be removed
    return f'/test{s}'


async def read_file_to_str(ftp_client: aioftp.Client, file_path: str) -> str:
    result = b''
    async with ftp_client.download_stream(file_path) as stream:
        async for block in stream.iter_by_block():
            result += block
    return result.decode('utf-8')


def xml_value(current_node, lookup_field, coerce_fn=str, default=None):
    node = current_node.find(lookup_field)
    if node is None or not node.text or not node.text.strip():
        return default
    return coerce_fn(node.text.replace('　', ' ').strip())


def parse_stat_datetime(s) -> datetime:
    # This is the format which aioftp returns file stats with
    default_tz = timezone.get_default_timezone()
    naive_dt = datetime.strptime(s, '%Y%m%d%H%M00')
    return timezone.make_aware(naive_dt, default_tz)


def parse_date(s) -> datetime:
    # return datetime.strptime(s, '%Y%m%d')

    # Let's drop date fields instead

    naive_dt = datetime.strptime(s, '%Y%m%d')
    return timezone.make_aware(naive_dt, japan_tiimezone)


# def make_aware_datetime(dt):
#     return timezone.make_aware(dt, japan_tiimezone)


@sync_to_async
def _fetch_path_sync_log(path) -> t.Optional[PathSyncLog]:
    return PathSyncLog.objects.filter(path=path).first()


@sync_to_async
def _update_or_create_path_sync_log(path, modified_at):
    return PathSyncLog.objects.update_or_create(
        path=path,
        defaults={
            'last_modified_at': modified_at,
        },
    )


@asynccontextmanager
async def process_path_if_needed(path: str, file_stat: dict, force=False):
    # commit key is only for faster re-running durring development
    modified_at = parse_stat_datetime(file_stat['modify'])
    """
    file_stat: 20211116091600
    default_tz: Asia/Tokyo
    naive_dt: 2021-11-16 09:16:00
    modified_at: 2021-11-16 09:16:00+09:00
    """
    file_sync_log: t.Optional[PathSyncLog] = await _fetch_path_sync_log(path)
    if force or file_sync_log is None or file_sync_log.last_modified_at < modified_at:
        if file_sync_log:
            logger.info('Path %s is outdated, processing', path)
        else:
            logger.info('Path %s is syncing for the first time', path)
        yield True
        await _update_or_create_path_sync_log(path, modified_at)
    else:
        logger.info('Path %s is already synced to its latest state', path)
        yield False


@sync_to_async
def async_create(model_klass, **kwargs) -> t.Any:
    return model_klass.objects.create(
        **kwargs,
    )


@sync_to_async
def async_upsert(model_klass, **kwargs) -> t.Tuple[t.Any, bool]:
    defaults = kwargs.pop('defaults')
    return model_klass.objects.update_or_create(
        defaults=defaults,
        **kwargs,
    )


@sync_to_async
def async_find_all(model_klass, **kwargs):
    return list(model_klass.objects.filter(**kwargs))


@sync_to_async
def async_find_first(model_klass, **kwargs):
    obj = model_klass.objects
    if 'select_related' in kwargs:
        select_related = kwargs.pop('select_related')
        if select_related:
            obj = obj.select_related(*select_related)
    return obj.filter(**kwargs).first()


def parse_datetime(date_str, time_str):
    if not time_str:
        time_str = '0000'
    time_str = time_str.replace('#', '0')
    if len(time_str) <= 3:
        time_str = "0" + time_str
    naive_dt = datetime.strptime(f'{date_str}{time_str}', '%Y%m%d%H%M')
    return timezone.make_aware(naive_dt, japan_tiimezone)


@sync_to_async
def async_save_item(model_instance):
    return model_instance.save()


@sync_to_async
def async_delete_item(model_instance):
    return model_instance.delete()


@sync_to_async
def async_select_ids(model_klass, **kwargs):
    return list(model_klass.objects.filter(**kwargs).values_list('id', flat=True))


@sync_to_async
def async_delete(model_klass, **kwargs):
    return model_klass.objects.filter(**kwargs).delete()


# @sync_to_async
# def async_clear_duplicated(model_klass, **kwargs):
#     instances = list(model_klass.objects.filter(**kwargs))
#     for index, instance in enumerate(instances):
#         if index == 0:
#             continue
#         model_klass.objects.filter(id=instance.id).delete()


def get_team_side(key):
    if key == '1':
        return 'home'
    elif key == '2':
        return 'away'
    return None
