# Generated by Django 3.1.13 on 2022-03-17 08:12

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ('data_stadium', '0044_auto_20220317_1408'),
    ]

    operations = [
        migrations.CreateModel(
            name='GameTeamFormation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('home_player_positions', models.JSONField(default=dict)),
                ('away_player_positions', models.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                (
                    'away_team',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='+',
                        to='data_stadium.team',
                    ),
                ),
                (
                    'away_team_formation',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='+',
                        to='data_stadium.teamformation',
                    ),
                ),
                (
                    'game',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='teams_formation',
                        to='data_stadium.game',
                    ),
                ),
                (
                    'home_team',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='+',
                        to='data_stadium.team',
                    ),
                ),
                (
                    'home_team_formation',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='+',
                        to='data_stadium.teamformation',
                    ),
                ),
            ],
        ),
    ]
