# Generated by Django 3.1.13 on 2022-06-01 12:14

from django.db import migrations
from django.utils.text import slugify


def patch_competitions(apps, schema_editor):
    GameKind = apps.get_model('data_stadium', 'GameKind')
    Competition = apps.get_model('core', 'Competition')

    game_kinds = GameKind.objects.all()

    for game_kind in game_kinds:
        """Handle duplicate game kind slug
        # 13	"International Friendly Match"	"IFM"	"japan"	"2021-04-27 06:14:03.873929+00"	"2022-05-17 04:45:06.124935+00"
        # 18	"FIFA World Cup"	"FIFAWC"	"japan"	"2021-04-27 06:14:03.8826+00"	"2022-05-17 04:45:06.134382+00"
        # 23	"Olympic Games"	"Olympic"	"u-23"	"2021-04-27 06:14:03.889255+00"	"2022-05-17 04:45:06.140315+00"
        # 31	"U-21 International Friendly Match"	"U-21 Friendly Match"	"u-23"	"2021-04-27 06:14:03.897599+00"	"2022-05-17 04:45:06.149817+00"
        # 58	"Copa America"	"Copa America"	"japan"	"2021-04-27 06:14:03.932334+00"	"2022-05-17 04:45:06.185581+00"
        # 225	"FIFA World Cup Asian Qualifiers"	"WC Qualifiers AFC"	"japan"	"2021-04-27 06:14:04.01991+00"	"2022-05-17 04:45:06.270774+00"
        """
        if game_kind.slug == 'japan' or game_kind.slug == 'u-23':
            slug = slugify(game_kind.name_short)
        else:
            slug = game_kind.slug

        Competition.objects.create(
            id=game_kind.id,
            name=game_kind.name,
            name_short=game_kind.name_short,
            slug=slug,
            created_at=game_kind.created_at,
            updated_at=game_kind.updated_at,
        )


class Migration(migrations.Migration):
    dependencies = [
        ('core', '0005_competition'),
    ]

    operations = [
        migrations.RunPython(patch_competitions),
    ]
