log:
  level: DEBUG

entryPoints:
  web:
    # http
    address: ':80'
    http:
      # Redirect all HTTP traffic to HTTPS
      redirections:
        entryPoint:
          to: web-secure

  web-secure:
    # https
    address: ':443'

certificatesResolvers:
  cloudflare:
    # https://go-acme.github.io/lego/dns/cloudflare/
    acme:
      email: '<EMAIL>'
      storage: /etc/traefik/acme/acme.json
      # https://doc.traefik.io/traefik/master/https/acme/#dnschallenge
      dnsChallenge:
        provider: cloudflare
        resolvers:
          - '*******:53'
          - '*******:53'

  # letsencrypt:
  #   acme:
  #     email: <EMAIL>
  #     storage: /etc/traefik/acme/acme.json
  #     caServer: https://acme-staging-v02.api.letsencrypt.org/directory
  #     httpChallenge:
  #       entryPoint: web

http:
  routers:
    www-router:
      rule: 'Host(`www.jleague.co`)'
      entryPoints:
        - web-secure
      middlewares:
        - csrf
      service: jleagueco
      tls:
        certResolver: cloudflare

    non-www-router:
      rule: 'Host(`jleague.co`)'
      entryPoints:
        - web-secure
      middlewares:
        - redirect-to-www

  middlewares:
    csrf:
      # CSRF protection
      headers:
        hostsProxyHeaders: ['X-CSRFToken']

    redirect-to-www:
      # Redirect jleague.co to www.jleague.co
      redirectRegex:
        regex: 'https://jleague.co(.*)'
        replacement: 'https://www.jleague.co$1'
        permanent: true

  services:
    jleagueco:
      loadBalancer:
        servers:
          - url: http://jleague:8000

providers:
  # https://docs.traefik.io/master/providers/file/
  file:
    filename: /etc/traefik/traefik.yml
    watch: true
