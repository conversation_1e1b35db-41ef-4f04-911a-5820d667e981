# Generated by Django 4.1.4 on 2022-12-28 09:08

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ("core", "0016_auto_20221228_1807"),
        ("about", "0009_navigationgroup_navigationlink_aboutpage_navigation"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="navigationlink",
            name="link_page",
        ),
        migrations.RemoveField(
            model_name="navigationlink",
            name="parent",
        ),
        migrations.AlterField(
            model_name="aboutpage",
            name="navigation",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="+",
                to="core.navigationgroup",
            ),
        ),
        migrations.DeleteModel(
            name="NavigationGroup",
        ),
        migrations.DeleteModel(
            name="NavigationLink",
        ),
    ]
