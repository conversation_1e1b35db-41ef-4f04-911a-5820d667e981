{% extends 'base.html' %}

{% load pipeline static core_tags %}

{% block extra_styles %}
  {% stylesheet 'clubs-page' %}
{% endblock %}

{% block schemaorg %}
<script type="application/ld+json">
{
  "@context": "http://schema.org",
  "@type": "BreadcrumbList",
  "itemListElement": [
    {
      "@type": "ListItem",
      "position": 1,
      "name": "Home",
      "item": "https://www.jleague.co/"
    },
    {
      "@type": "ListItem",
      "position": 2,
      "name": "Clubs",
      "item": "https://www.jleague.co/clubs/"
    },
    {
      "@type": "ListItem",
      "position": 3,
      "name": "{{ league.competition.t_name }}",
      "item": "https://www.jleague.co/clubs/{{ club.slug }}"
    }
  ]
}
</script>
{% endblock %}

{% block extra_scripts %}
  {% javascript 'clubs-page' %}
{% endblock %}

{% block active_body_class %}body-club-list{% endblock %}

{% block content %}
  <main class="content-container club-list">
    <section id="nav-container" class="content-container__header nav-container">
      <a
        class="btn-nav large active"
        href="#j1"
        data-slug="j1"
        onclick="sendSearchEvent('club', 'j1');"
      >
        {% include 'components/competition-logo.html' with slug='j1' size='small' compact=True %}
        <span class="btn-nav__label">{% translation 'GLOBAL_COMPETITION_J1_LEAGUE' %}</span>
      </a>

      <a
        class="btn-nav large"
        href="#j2"
        data-slug="j2"
        onclick="sendSearchEvent('club', 'j2');"
      >
        {% include 'components/competition-logo.html' with slug='j2' size='small' compact=True %}
        <span class="btn-nav__label">{% translation 'GLOBAL_COMPETITION_J2_LEAGUE' %}</span>
      </a>

      <a
        class="btn-nav large"
        href="#j3"
        data-slug="j3"
        onclick="sendSearchEvent('club', 'j3');"
      >
        {% include 'components/competition-logo.html' with slug='j3' size='small' compact=True %}
        <span class="btn-nav__label">{% translation 'GLOBAL_COMPETITION_J3_LEAGUE' %}</span>
      </a>
    </section>

    <section class="content-container__body">
      {% for league in club_leagues %}
        <div id="{{ league.competition.slug }}" class="clubs-container">
          <div class="league-logo">
            {% include 'components/competition-logo.html' with slug=league.competition.slug size='xlarge' %}
          </div>

          {% include 'components/section-header.html' with title_translation_obj=league.competition.t_name selected_competition=None %}

          <div class="club-list">
            {% for club in league.clubs %}
              {% include 'clubs/components/club_list_item.html' with club=club %}
            {% endfor %}
          </div>
        </div>
      {% endfor %}
    </section>
  </main>
{% endblock %}
