{% load static core_tags %}

<div class="stat-item next-match">
  <div class="stat-item__body info-container">
    <div class="league-container">
      <span>{% translation 'CLUB_PROFILE_PAGE_NEXT_MATCH' %}</span>

      {% if next_match %}
        {% if next_match.competition.slug == 'ffsc' %}
          {% include 'components/competition-logo.html' with slug=next_match.competition.slug year=next_match.year size='xsmall' %}
        {% else %}
          {% include 'components/competition-logo.html' with slug=next_match.competition.slug year=next_match.year size='small' %}
        {% endif %}
      {% endif %}
    </div>

    {% if next_match %}
      <a
        href="{% url 'match_overview' next_match.competition.slug next_match.id %}"
        onclick="sendSelectContentEvent('match', '{{ next_match.competition.slug }}_{{ next_match.id }}');"
      >
        <div class="occasion-container">
          {% if next_match.competition.slug != 'ffsc' %}
            <div class="occasion-no">{% translation 'CLUB_PROFILE_PAGE_MATCHWEEK' %} {{ next_match.occasion_no }}</div>
          {% endif %}
          <div class="date">{% local_time next_match.date 'DATE_FORMAT' %}</div>
        </div>

        <div class="match-info">
          <div class="team">
            {% include 'components/club-emblem.html' with slug=next_match.home.slug %}

            <span class="team-name">
              {% club_name next_match.home %}
            </span>
          </div>

          <div class="time-panel">{% local_time next_match.date %}</div>

          <div class="team">
            {% include 'components/club-emblem.html' with slug=next_match.away.slug %}

            <span class="team-name">
              {% club_name next_match.away %}
            </span>
          </div>
        </div>

        <div class="stadium">
          <img
            class="lazy"
            src="{% static "images/icons/field-football-6B5B52.svg" %}"
            alt="field-football"
            loading="lazy"
          />
          <span>{% translation_obj next_match.stadium_t_name default=next_match.stadium %}</span>
        </div>
      </a>
    {% else %}
      {% include 'components/data-empty-state.html' %}
    {% endif %}
  </div>

  <div class="stat-item__footer">
    {% if next_match and next_match.tickets %}
      {% for ticket in next_match.tickets %}
        <a
          class="jl-button jl-button--inverse"
          href="{{ ticket.link }}"
          rel="noopener"
          target="_blank"
          onclick="sendSelectContentEvent('page', 'match-ticket-website:{{ next_match.id }}');"
        >
          <span class="jl-button__label">
            {% if ticket.label %}
              {{ ticket.label }}
            {% else %}
              {% translation 'GLOBAL_BUY_TICKETS' %}
            {% endif %}
          </span>
        </a>
      {% endfor %}
    {% endif %}

    {% if next_match and club %}
      <a
        class="jl-button"
        href="{% url 'fixtures_search_team' next_match.competition.slug selected_year 'latest' club.slug %}"
        onclick="sendSelectContentEvent('page', 'fixtures');"
      >
        <span class="jl-button__label">
          {% translation 'CLUB_PROFILE_PAGE_VIEW_ALL_MATCHES' %}
        </span>
      </a>
    {% endif %}
  </div>
</div>
