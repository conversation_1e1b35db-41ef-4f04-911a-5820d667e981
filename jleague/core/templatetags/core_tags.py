import datetime
import re
from django import template
from django.urls import reverse
from django.utils.safestring import mark_safe
from utils.helpers import get_display_data_year
from ..helpers import (
    get_translation_value,
    get_translation_obj_value,
    get_competition_name,
    get_club_name,
    get_player_name,
    get_local_time,
)


register = template.Library()


@register.filter
def dict_val(data, key):
    """Get a dict value by key"""
    if data and isinstance(data, dict):
        return data.get(key, key)
    elif data and isinstance(data, object):
        return data.__dict__.get(key, key)
    return key


@register.filter
def active_route(request, nav_item):
    """Get active route"""
    is_active = False

    if request and request.resolver_match:
        url_name = request.resolver_match.url_name
        req_path = request.path

        if url_name == "wagtail_serve":
            # wagtail routes
            if nav_item.link_page and nav_item.link_page.slug:
                route = rf"^\/{nav_item.link_page.slug}\/"
                if (
                    nav_item.link_page.slug in request.__str__()
                    or re.search(route, req_path)  # noqa
                    or (nav_item.link_page.slug == "home" and req_path == "/")  # noqa
                ):
                    is_active = True
        else:
            # django routes
            route = rf"^\/{nav_item.label.lower()}\/"
            if url_name == nav_item.django_page or re.search(route, req_path):
                is_active = True

    return is_active


@register.simple_tag
def dynamic_url(name, *args, **kwargs):
    if kwargs.get("params") and kwargs["params"].keys():
        params = kwargs["params"]
        year = params.get("year")
        if year == "auto":
            params["year"] = get_display_data_year()
        return reverse(name, kwargs=params)
    return reverse(name)


def translation_key(key, *args, **kwargs):
    return get_translation_value(key, **kwargs)


register.filter("translation", translation_key)
register.simple_tag(translation_key, False, "translation")


def translation_obj(data, *args, **kwargs):
    return get_translation_obj_value(data, **kwargs)


register.filter("translation_obj", translation_obj)
register.simple_tag(translation_obj, False, "translation_obj")


def local_competition_name(competition, *args, **kwargs):
    use_short = False
    if kwargs.get("short"):
        use_short = kwargs["short"] is True
    return get_competition_name(competition, use_short, **kwargs)


register.filter("competition_name", local_competition_name)
register.simple_tag(local_competition_name, False, "competition_name")


def local_club_name(club, *args, **kwargs):
    use_short = False
    use_four_letters = False
    if kwargs.get("four_letters"):
        use_four_letters = kwargs["four_letters"] is True
    elif kwargs.get("short"):
        use_short = kwargs["short"] is True
    return get_club_name(club, use_short, use_four_letters, **kwargs)


register.filter("club_name", local_club_name)
register.simple_tag(local_club_name, False, "club_name")


def local_player_name(player, *args, **kwargs):
    use_short = False
    if kwargs.get("short"):
        use_short = kwargs["short"] is True
    return get_player_name(player, use_short, **kwargs)


register.filter("player_name", local_player_name)
register.simple_tag(local_player_name, False, "player_name")


def local_time(context, time, time_format="TIME_FORMAT", *args, **kwargs):
    if not isinstance(time, datetime.datetime):
        return ""

    timezone = context["request"].TIMEZONE
    lang_code = None
    if kwargs.get("timezone"):
        timezone = kwargs["timezone"]
    if kwargs.get("lang_code"):
        lang_code = kwargs["lang_code"]

    result = get_local_time(
        time,
        time_format,
        timezone=timezone,
        lang_code=lang_code,
    )

    if kwargs.get("prefix"):
        result = kwargs["prefix"] + result
    if kwargs.get("suffix"):
        result = result + kwargs["suffix"]
    return result


# register.filter('local_time', local_time) # it doesn't support context
register.simple_tag(local_time, True, "local_time")


@register.simple_tag
def club_emblem_file(slug, *args, **kwargs):
    if slug:
        use_alt = kwargs.get("alt")
        png_list = (
            "barcelona",
            "bayern",
            "celtic",
            "leon",
            "manchesterc",
            "muangthong",
            "paris-sg",
            "pathumunited",
            "tottenham",
            "stuttgart",
            "newcastle",
        )
        alt_list = ("tokyov", "tokushima")

        if use_alt and slug in alt_list:
            return f"{slug}-alt.svg"
        elif slug in png_list:
            return f"{slug}.png"
        return f"{slug}.svg"
    else:
        return "club-emblem-placeholder.svg"


def name_space_break(text, *args, **kwargs):
    splitted = text.split(" ")
    html = ""
    for word in splitted:
        html += f"<span>{word}</span>"
    return mark_safe(html)


register.filter("name_space_break", name_space_break)
