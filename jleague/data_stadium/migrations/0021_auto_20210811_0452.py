# Generated by Django 3.1.13 on 2021-08-11 08:52

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('data_stadium', '0020_game_referee'),
    ]

    operations = [
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='aerial_battles_won',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='appearances',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='blocks',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='chances_created',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='corners',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='crosses',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='dribbles',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='fouls',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='freekicks',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='goals_from_freekick',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='goals_from_head',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='goals_from_left',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='goals_from_penalty',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='goals_from_right',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='goals_per_90',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='interceptions',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='minutes_played',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='passes',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='passes_per_90',
            field=models.DecimalField(decimal_places=1, max_digits=100, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='recoveries',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='red_cards',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='shots_on_target',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='shots_per_90',
            field=models.DecimalField(decimal_places=1, max_digits=100, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='through_balls',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='top_speed',
            field=models.DecimalField(decimal_places=1, max_digits=100, null=True),
        ),
        migrations.AddField(
            model_name='gamekindteamplayerstats',
            name='yellow_cards',
            field=models.IntegerField(blank=True, null=True),
        ),
    ]
