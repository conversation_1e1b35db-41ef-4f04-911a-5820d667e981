# Generated by Django 3.1.13 on 2022-05-10 04:02

from django.db import migrations

BROADCASTS = [
    {
        'countries': ['China'],
        'country_codes': ['cn'],
        'competitions': ['J1', 'J2'],
        'networks': [
            {
                'name': 'K-BALL',
                'link': 'https://www.sportsmedia.com.cn/',
            },
        ],
    },
    {
        'countries': ['Hong Kong'],
        'country_codes': ['hk'],
        'competitions': ['J1', 'J2'],
        'networks': [
            {
                'name': 'i-Cable',
                'link': 'http://www.i-cable.com/',
            },
        ],
    },
    {
        'countries': ['Macau'],
        'country_codes': ['mo'],
        'competitions': ['J1'],
        'networks': [
            {
                'name': 'TDM',
                'link': 'https://portugues.tdm.com.mo/',
            },
        ],
    },
    {
        'countries': ['Malaysia', 'Brunei'],
        'country_codes': ['my', 'bn'],
        'competitions': ['J1'],
        'networks': [
            {
                'name': 'SPOTV',
                'link': 'https://www.spotvnow.com',
            },
            {
                'name': 'Astro',
                'link': 'https://www.stadiumastro.com/',
            },
            {
                'name': 'unifi TV',
                'link': 'https://unifi.com.my/tv/',
            },
        ],
    },
    {
        'countries': ['Indonesia'],
        'country_codes': ['id'],
        'competitions': ['J1'],
        'networks': [
            {
                'name': 'SPOTV Now Asia',
                'link': 'https://www.spotvnow.com',
            },
            {
                'name': 'MNC Vision',
                'link': 'https://www.spotvnow.com',
            },
            {
                'name': 'K-Vision',
                'link': 'https://www.spotvnow.com',
            },
            {
                'name': 'UseeTV',
                'link': 'https://www.useetv.com',
            },
        ],
    },
    {
        'countries': ['Singapore', 'Philippines'],
        'country_codes': ['sg', 'ph'],
        'competitions': ['J1'],
        'networks': [
            {
                'name': 'SPOTV Now Asia',
                'link': 'https://www.spotvnow.com',
            },
        ],
    },
    {
        'countries': ['Vietnam'],
        'country_codes': ['vn'],
        'competitions': ['J1'],
        'networks': [
            {
                'name': 'VieON',
                'link': 'https://vieon.vn/',
            },
        ],
    },
    {
        'countries': ['Australia'],
        'country_codes': ['au'],
        'competitions': ['J1'],
        'networks': [
            {
                'name': 'OPTUS',
                'link': 'https://sport.optus.com.au/',
            },
        ],
    },
    {
        'countries': ['England', 'Ireland'],
        'country_codes': ['gb', 'ie'],
        'competitions': ['J1'],
        'networks': [
            {
                'name': 'Premier Sports	',
                'link': 'https://www.premiersports.com/',
            },
        ],
        'is_disabled': True,
    },
    {
        'countries': ['Israel'],
        'country_codes': ['il'],
        'competitions': ['J1'],
        'networks': [
            {
                'name': 'The Sports Channel',
                'link': 'https://www.sport5.co.il/',
            },
        ],
    },
    {
        'countries': ['Thailand'],
        'country_codes': ['th'],
        'competitions': ['J1'],
        'networks': [
            {
                'name': 'SIAMSPORT',
                'link': 'https://www.siamsport.co.th',
            },
            {
                'name': 'PPTV HD 36',
                'link': 'https://www.pptvhd36.com/',
            },
        ],
    },
    {
        'countries': ['Germany', 'Switzerland', 'Austria'],
        'country_codes': ['de', 'ch', 'at'],
        'competitions': ['J1'],
        'networks': [
            {
                'name': 'Sportdigital',
                'link': 'https://start.sportdigital.de/',
            },
        ],
    },
    {
        'countries': ['Serbia', 'Montenegro', 'Slovenia', 'Croatia', 'Bosnia-Herzegovina', 'North Macedonia'],
        'country_codes': ['rs', 'me', 'si', 'hr', 'ba', 'mk'],
        'competitions': ['J1'],
        'networks': [
            {
                'name': 'SportKlub',
                'link': 'http://sportklub.com/',
            },
        ],
    },
    {
        'countries': [
            'Armenia',
            'Azerbaijan',
            'Belarus',
            'Georgia',
            'Kazakhstan',
            'Kyrgyzstan',
            'Moldova',
            'Russia',
            'Tajikistan',
            'Turkmenistan',
            'Uzbekistan',
        ],
        'country_codes': ['am', 'az', 'by', 'ge', 'kz', 'kg', 'md', 'ru', 'tj', 'tm', 'uz'],
        'competitions': ['J1'],
        'networks': [
            {
                'name': 'TSG',
                'link': 'https://tele-sport.ru/',
            },
        ],
    },
    {
        'countries': [
            'Algeria',
            'Bahrain',
            'Chad',
            'Comoros',
            'Djibouti',
            'Egypt',
            'Iran',
            'Iraq',
            'Jordan',
            'Kuwait',
            'Lebanon',
            'Libya',
            'Mauritania',
            'Morocco',
            'Oman',
            'Palestine',
            'Qatar',
            'Saudi Arabia',
            'Somalia',
            'Sudan',
            'Syria',
            'Tunisia',
            'United Arab Emirates',
            'Yemen',
        ],
        'country_codes': [
            'dz',
            'bh',
            'td',
            'km',
            'dj',
            'eg',
            'iq',
            'ir',
            'jo',
            'kw',
            'lb',
            'ly',
            'ma',
            'mr',
            'om',
            'ps',
            'qa',
            'sa',
            'sd',
            'so',
            'sy',
            'tn',
            'ae',
            'ye',
        ],
        'competitions': ['J1'],
        'networks': [
            {
                'name': 'Dubai Sports',
                'link': 'https://www.dubaisports.ae/content/dubaisports/home.html',
            },
        ],
    },
    {
        'countries': ['Italy', 'Brazil'],
        'country_codes': ['it', 'br'],
        'competitions': ['J1'],
        'networks': [
            {
                'name': 'OneFootball',
                'link': 'https://onefootball.com/en/home',
            },
        ],
    },
    {
        'countries': ['Worldwide excluding Japan'],
        'country_note': '(News rights only)',
        'competition_note': '* News distribution only',
        'competitions': ['J1'],
        'networks': [
            {
                'name': 'SNTV',
                'link': 'https://www.sntv.com/',
            },
        ],
    },
]


def insert_broadcast_networks(apps, schema_editor):
    Broadcaster = apps.get_model('about', 'Broadcaster')
    BroadcastArea = apps.get_model('about', 'BroadcastArea')
    Country = apps.get_model('core', 'Country')
    GameKind = apps.get_model('data_stadium', 'GameKind')

    countries = Country.objects.all()
    competitions = GameKind.objects.all()
    country_entities = {c.two_letter_code.lower(): c for c in countries}
    competition_entities = {c.slug.upper(): c for c in competitions}
    broadcaster_entities = {}
    created_networks = []

    for broadcast in BROADCASTS:
        # create area
        area = BroadcastArea.objects.create(
            name=', '.join(broadcast['countries']),
            country_note=broadcast.get('country_note'),
            competition_note=broadcast.get('competition_note'),
            is_active=False if broadcast.get('is_disabled') else True,
        )

        # add area's countries
        if broadcast.get('country_codes'):
            for c in broadcast['country_codes']:
                country = country_entities.get(c)
                if country:
                    area.countries.add(country)

        # add area's competitions
        if broadcast.get('competitions'):
            for c in broadcast['competitions']:
                competition = competition_entities.get(c)
                if competition:
                    area.competitions.add(competition)

        # create broadcasters
        if broadcast.get('networks'):
            for network in broadcast['networks']:
                if network['name'] in created_networks:
                    continue

                created_networks.append(network['name'])

                b = Broadcaster.objects.create(name=network['name'], website=network['link'], is_active=True)

                broadcaster_entities[network['name']] = b

            # add area's broadcasters
            for network in broadcast['networks']:
                broadcaster = broadcaster_entities.get(network['name'])
                if broadcaster:
                    area.broadcasters.add(broadcaster)


class Migration(migrations.Migration):
    dependencies = [
        ('about', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(insert_broadcast_networks),
    ]
