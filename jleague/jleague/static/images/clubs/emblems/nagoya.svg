<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="78417 28238 240 240" width="240" height="240" fill-rule="evenodd" stroke-width="28.222" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg">
  <g class="Page" transform="matrix(0.03811900317668915, 0, 0, 0.03811900317668915, 75480.3125, 27191.595703125)">
    <g class="com.sun.star.drawing.ClosedBezierShape">
      <g id="id3">
        <rect class="BoundingBox" stroke="none" fill="none" x="78534" y="28373" width="3307" height="4438"/>
        <path fill="rgb(249,167,13)" stroke="none" d="M 80755,32482 C 81072,32353 81325,32182 81508,31974 81729,31725 81840,31429 81840,31096 L 81840,29286 80187,28373 78535,29286 78535,31096 C 78535,31429 78647,31725 78867,31974 79051,32182 79304,32353 79620,32482 79771,32544 79946,32623 80093,32737 80108,32748 80145,32777 80145,32777 L 80187,32810 80229,32777 C 80229,32777 80267,32748 80282,32737 80429,32623 80603,32544 80755,32482 Z"/>
      </g>
    </g>
    <g class="com.sun.star.drawing.ClosedBezierShape">
      <g id="id4">
        <rect class="BoundingBox" stroke="none" fill="none" x="78417" y="28238" width="3542" height="4722"/>
        <path fill="rgb(171,157,82)" stroke="none" d="M 78417,29215 L 78417,31096 C 78417,31459 78539,31780 78779,32052 78974,32273 79242,32455 79575,32592 79720,32651 79886,32725 80022,32830 80033,32838 80116,32903 80116,32903 L 80188,32959 80261,32903 C 80261,32903 80340,32841 80355,32830 80491,32725 80656,32651 80800,32592 81133,32455 81401,32273 81597,32052 81837,31780 81958,31459 81958,31096 L 81958,29215 80188,28238 78417,29215 Z M 80188,28373 L 81840,29285 81840,31096 C 81840,31429 81729,31725 81508,31974 81325,32182 81072,32353 80756,32482 80604,32544 80430,32623 80283,32737 80268,32748 80230,32777 80230,32777 L 80188,32810 80146,32777 C 80146,32777 80109,32748 80094,32737 79947,32623 79772,32544 79620,32482 79304,32353 79051,32182 78867,31974 78647,31725 78535,31429 78535,31096 L 78535,29285 80188,28373 Z"/>
      </g>
    </g>
    <g class="com.sun.star.drawing.ClosedBezierShape">
      <g id="id5">
        <rect class="BoundingBox" stroke="none" fill="none" x="81181" y="28921" width="661" height="3337"/>
        <path fill="rgb(219,31,38)" stroke="none" d="M 81508,31974 C 81729,31725 81840,31428 81840,31095 L 81840,29285 81181,28921 81181,32257 C 81308,32172 81417,32078 81508,31974 Z"/>
      </g>
    </g>
    <g class="com.sun.star.drawing.ClosedBezierShape">
      <g id="id6">
        <rect class="BoundingBox" stroke="none" fill="none" x="78535" y="28921" width="661" height="3337"/>
        <path fill="rgb(219,31,38)" stroke="none" d="M 78535,29285 L 78535,31095 C 78535,31428 78648,31725 78868,31974 78960,32078 79069,32172 79195,32257 L 79195,28921 78535,29285 Z"/>
      </g>
    </g>
    <g class="com.sun.star.drawing.PolyPolygonShape">
      <g id="id7">
        <rect class="BoundingBox" stroke="none" fill="none" x="80487" y="29673" width="1" height="1"/>
        <path fill="rgb(219,31,38)" stroke="none" d="M 80487,29673 L 80487,29673 Z"/>
      </g>
    </g>
    <g class="com.sun.star.drawing.ClosedBezierShape">
      <g id="id8">
        <rect class="BoundingBox" stroke="none" fill="none" x="80026" y="32111" width="386" height="409"/>
        <path fill="rgb(219,31,38)" stroke="none" d="M 80273,32308 C 80226,32202 80145,32129 80026,32111 80026,32111 80124,32217 80134,32354 80145,32463 80176,32516 80262,32518 80338,32519 80411,32418 80411,32418 80411,32418 80325,32434 80273,32308 Z"/>
      </g>
    </g>
    <g class="com.sun.star.drawing.ClosedBezierShape">
      <g id="id9">
        <rect class="BoundingBox" stroke="none" fill="none" x="80334" y="30226" width="281" height="522"/>
        <path fill="rgb(219,31,38)" stroke="none" d="M 80496,30226 C 80446,30345 80312,30489 80339,30585 80366,30681 80465,30693 80614,30747 80614,30747 80495,30652 80462,30536 80430,30420 80485,30356 80496,30226 Z"/>
      </g>
    </g>
    <g class="com.sun.star.drawing.ClosedBezierShape">
      <g id="id10">
        <rect class="BoundingBox" stroke="none" fill="none" x="79923" y="31086" width="1186" height="782"/>
        <path fill="rgb(219,31,38)" stroke="none" d="M 80262,31867 C 80262,31867 80341,31773 80328,31553 80322,31442 80326,31356 80449,31278 80449,31278 80656,31153 80877,31236 80928,31256 81053,31299 81108,31424 81108,31424 81083,31182 80819,31106 80713,31078 80593,31070 80433,31168 80366,31209 80306,31262 80254,31322 80220,31362 80206,31384 80180,31411 80180,31411 80044,31556 79923,31629 79923,31629 80173,31620 80262,31867 Z"/>
      </g>
    </g>
    <g class="com.sun.star.drawing.ClosedBezierShape">
      <g id="id11">
        <rect class="BoundingBox" stroke="none" fill="none" x="80400" y="31308" width="707" height="1108"/>
        <path fill="rgb(219,31,38)" stroke="none" d="M 80848,31318 C 80561,31267 80400,31458 80400,31458 80675,31472 80736,31703 80706,31920 80675,32153 80416,32415 80416,32415 80817,32224 80955,31993 80956,31750 80957,31503 80743,31442 80743,31442 81141,31490 81011,31934 81011,31934 81239,31533 81002,31352 80848,31318 Z"/>
      </g>
    </g>
    <g class="com.sun.star.drawing.ClosedBezierShape">
      <g id="id12">
        <rect class="BoundingBox" stroke="none" fill="none" x="79974" y="28535" width="373" height="1221"/>
        <path fill="rgb(219,31,38)" stroke="none" d="M 79974,29755 C 80274,29556 80334,29388 80343,29280 80357,29140 80315,29024 80288,28931 80237,28740 80194,28535 80194,28535 80147,28742 80196,28983 80202,29067 80208,29174 80194,29237 80184,29295 80144,29523 79974,29755 79974,29755 Z"/>
      </g>
    </g>
    <g class="com.sun.star.drawing.ClosedBezierShape">
      <g id="id13">
        <rect class="BoundingBox" stroke="none" fill="none" x="79249" y="29242" width="1681" height="2500"/>
        <path fill="rgb(219,31,38)" stroke="none" d="M 80283,31182 C 80377,31101 80536,31020 80630,31019 80630,31019 80257,30847 79804,31234 79804,31234 80122,31101 80157,31144 80192,31187 79964,31358 79900,31400 79837,31441 79583,31621 79543,31549 79503,31477 79593,31306 79653,31225 79697,31164 79787,31078 79906,31010 79949,30984 79997,30962 80048,30943 80091,30928 80195,30906 80308,30901 80479,30893 80693,30923 80928,31078 80928,31078 80646,30825 80458,30772 80330,30736 80210,30624 80267,30454 80321,30293 80575,30091 80502,29629 80503,29642 80479,29777 80368,29964 80321,30044 80230,30142 80154,30249 80307,29926 80534,29563 80423,29242 80423,29242 80427,29370 80380,29465 80337,29551 80285,29610 80132,29732 79979,29854 79914,29877 79832,30067 79832,30067 79788,30145 79790,30281 79793,30415 79929,30619 79929,30619 79929,30619 79858,30422 79968,30167 80023,30039 80086,29972 80151,29899 79886,30537 80061,30713 80061,30713 80061,30713 80018,30719 79892,30767 79892,30767 79575,30887 79379,31177 79276,31337 79258,31476 79258,31476 79258,31476 79232,31633 79268,31686 79317,31762 79426,31751 79575,31701 79676,31662 79864,31547 79972,31458 80079,31369 80206,31255 80283,31182 Z"/>
      </g>
    </g>
  </g>
</svg>
