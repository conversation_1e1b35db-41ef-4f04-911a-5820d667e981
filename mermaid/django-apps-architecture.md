# J-League Django Apps Architecture

```mermaid
graph TB
    A[Core Module] --> B[Data Stadium Integration]
    A --> C[Clubs Management]
    A --> D[Players Management]
    A --> E[Match Management]
    A --> F[Statistics]
    A --> G[Standings]
    A --> H[News & Media]
    A --> I[Search]
    A --> J[Utils & Widgets]
    
    B --> B1[FTP Import System]
    B --> B2[Live Data Sync]
    B --> B3[SOCKS Proxy Support]
    
    C --> C1[Club Profiles]
    C --> C2[Club Statistics]
    C --> C3[Club Players]
    
    D --> D1[Player Profiles]
    D --> D2[Player Statistics]
    D --> D3[Player Game Logs]
    
    E --> E1[Match Details]
    E --> E2[Live Match Updates]
    E --> E3[WebSocket Consumers]
    E --> E4[Match Statistics]
    
    F --> F1[Player Stats]
    F --> F2[Club Stats]
    F --> F3[Game Stats]
    
    G --> G1[League Tables]
    G --> G2[Competition Rankings]
    
    H --> H1[News Articles]
    H --> H2[Videos]
    H --> H3[Photos]
    
    I --> I1[Global Search]
    I --> I2[Search API]
    
    J --> J1[Translation System]
    J --> J2[Helper Functions]
    J --> J3[UI Widgets]
```
