from .base import *  # noqa
from .base import env

# ------------------------------------------------------------------------------
# GENERAL
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#debug
DEBUG = env.bool("DJANGO_DEBUG", default=False)
# https://docs.djangoproject.com/en/dev/ref/settings/#secret-key
SECRET_KEY = env("DJANGO_SECRET_KEY", default="")
# https://docs.djangoproject.com/en/dev/ref/settings/#allowed-hosts
ALLOWED_HOSTS = env.list("DJANGO_ALLOWED_HOSTS", default=[])
# https://docs.djangoproject.com/en/dev/ref/settings/#csrf-trusted-origins
CSRF_TRUSTED_ORIGINS = env.list("DJANGO_CSRF_TRUSTED_ORIGINS", default=[])

# ------------------------------------------------------------------------------
# DATABASE
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#databases
DATABASES = {"default": env.db("DATABASE_URL")}
DATABASES["default"]["ATOMIC_REQUESTS"] = True
DATABASES["default"]["CONN_MAX_AGE"] = env.int("CONN_MAX_AGE", default=60)

# ------------------------------------------------------------------------------
# LOGGING
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/logging/
LOGGING["root"]["level"] = "WARNING"  # noqa: F405

# ------------------------------------------------------------------------------
# WAGTAIL
# ------------------------------------------------------------------------------
# https://docs.wagtail.org/en/latest/reference/settings.html#wagtailadmin-base-url
WAGTAILADMIN_BASE_URL = env("WAGTAIL_ADMIN_BASE_URL", default=None)

# ------------------------------------------------------------------------------
# FRONTEND CACHE INVALIDATOR
# ------------------------------------------------------------------------------
# https://docs.wagtail.org/en/stable/reference/contrib/frontendcache.html#frontend-cache-invalidator
INSTALLED_APPS = INSTALLED_APPS + ["wagtail.contrib.frontend_cache"]  # noqa: F405
WAGTAILFRONTENDCACHE = {
    "cloudflare": {
        "BACKEND": env("WAGTAIL_FRONTENDCACHE_BACKEND", default=None),
        "BEARER_TOKEN": env("WAGTAIL_FRONTENDCACHE_BEARER_TOKEN", default=None),
        "ZONEID": env("WAGTAIL_FRONTENDCACHE_ZONEID", default=None),
    },
}

# ------------------------------------------------------------------------------
# SECURITY
# ------------------------------------------------------------------------------
# https://docs.djangoproject.com/en/dev/ref/settings/#secure-proxy-ssl-header
SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")
# https://docs.djangoproject.com/en/dev/ref/settings/#secure-ssl-redirect
SECURE_SSL_REDIRECT = env.bool("DJANGO_SECURE_SSL_REDIRECT", default=True)
# https://docs.djangoproject.com/en/dev/ref/settings/#session-cookie-secure
SESSION_COOKIE_SECURE = True
# https://docs.djangoproject.com/en/dev/ref/settings/#csrf-cookie-secure
CSRF_COOKIE_SECURE = True
# https://docs.djangoproject.com/en/dev/topics/security/#ssl-https
# https://docs.djangoproject.com/en/dev/ref/settings/#secure-hsts-seconds
# TODO: set this to 60 seconds first and then to 518400 once you prove the former works
SECURE_HSTS_SECONDS = 60
# https://docs.djangoproject.com/en/dev/ref/settings/#secure-hsts-include-subdomains
SECURE_HSTS_INCLUDE_SUBDOMAINS = env.bool(
    "DJANGO_SECURE_HSTS_INCLUDE_SUBDOMAINS", default=True
)
# https://docs.djangoproject.com/en/dev/ref/settings/#secure-hsts-preload
SECURE_HSTS_PRELOAD = env.bool("DJANGO_SECURE_HSTS_PRELOAD", default=True)
# https://docs.djangoproject.com/en/dev/ref/middleware/#x-content-type-options-nosniff
SECURE_CONTENT_TYPE_NOSNIFF = env.bool(
    "DJANGO_SECURE_CONTENT_TYPE_NOSNIFF", default=True
)

# ------------------------------------------------------------------------------
# SEO
# ------------------------------------------------------------------------------
SEO_TRACKERS_ENABLED = True

# ------------------------------------------------------------------------------
# MAILER LITE
# ------------------------------------------------------------------------------
MAILER_LITE_ENABLED = True

# ------------------------------------------------------------------------------
# WSC
# ------------------------------------------------------------------------------
WSC_VIDEO_ALERT_ENABLED = True
