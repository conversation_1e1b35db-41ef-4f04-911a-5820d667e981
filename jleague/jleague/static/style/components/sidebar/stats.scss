@import 'setup/variable';
@import 'setup/mixin';

.sidebar-stats {
  .dropdown {
    height: 36px;
    width: 100%;
    background: transparent;

    .dropdown__selector {
      width: 100%;
      font-size: 16px;
      @include text_ellipsis();
    }
  }

  .sidebar-stats__stats--header {
    display: flex;
    gap: 4px;
    padding: 4px 8px 4px 8px;
    background: $color-earth-1;
    border-radius: 6px;
    height: 22px;
    margin-top: 10px;

    span {
      font-family: $font-barlow-condensed;
      font-style: normal;
      font-weight: bold;
      font-size: 12px;
      line-height: 14.4px;
      letter-spacing: 1px;
      text-transform: uppercase;
      color: $color-earth-4;
      vertical-align: middle;
      width: 16px;

      &.stat {
        text-align: right;
        width: 35px;
        @include text_ellipsis();
      }

      &.player {
        width: 100%;
      }
    }
  }

  .ranking-item {
    padding: 8px;
    border-bottom: 1px solid $color-earth-2;
    display: flex;
    gap: 8px;
    align-items: center;
    height: 48px;

    &:hover {
      background: $color-white;
      border-radius: 4px;
    }

    .rank {
      flex: 0 0 auto;
      width: 10px;
      text-align: right;
      color: $color-earth-4;
      font-family: $font-jleague-kick;
      font-style: normal;
      font-weight: 700;
      font-size: 14px;
      line-height: 16px;
    }

    .player-photo {
      flex: 0 0 auto;
      width: 32px;
      height: auto;
      border-radius: 32px;
    }

    .info {
      width: 90px;

      .player {
        .player-name {
          color: $color-black;
          font-family: $font-barlow-condensed;
          font-style: normal;
          font-weight: 500;
          font-size: 16px;
          line-height: 16px;
          @include text_ellipsis();

          &:hover {
            opacity: 1;

            &[href] {
              text-decoration: underline;
            }
          }
        }
      }

      .team {
        display: flex;
        align-items: center;

        .club-emblem {
          display: none;
        }

        .team-name {
          flex: auto;
          padding: 2px 0;
          color: $color-earth-4;
          font-family: $font-barlow-condensed;
          font-style: normal;
          font-weight: 400;
          font-size: 12px;
          line-height: 12px;
          @include text_ellipsis();

          &:hover {
            opacity: 1;

            &[href] {
              text-decoration: underline;
            }
          }
        }
      }
    }

    .value {
      width: 30px;
      color: $color-black;
      font-family: $font-jleague-kick;
      font-style: normal;
      font-weight: 700;
      font-size: 16px;
      line-height: 16px;
      text-align: right;
    }
  }

  .view-more-button {
    margin: 16px auto 0;
    padding: 8px 16px;
    height: 32px;
    font-size: 14px;
    font-weight: 700;
    line-height: 16px;
    letter-spacing: 1.667px;
    border-radius: 16px;
  }

  @media screen and (max-width: 576px) {
    .ranking-item .info {
      width: 100%;
    }
  }
}

body.lang-th {
  .sidebar-stats {
    .sidebar-stats__stats--header {
      .player,
      .stat {
        @include font-thai();
      }
    }

    .ranking-item .info {
      .player .player-name {
        @include font-thai();
      }

      .team .team-name {
        @include font-thai();
      }
    }
  }
}
