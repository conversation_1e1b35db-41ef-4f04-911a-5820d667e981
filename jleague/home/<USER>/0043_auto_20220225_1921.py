# Generated by Django 3.1.13 on 2022-02-25 10:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ('match', '0003_gameextrainfo_gamevideosorderable'),
        ('home', '0042_auto_20220225_1420'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='homepage',
            name='is_live_video_enabled',
        ),
        migrations.RemoveField(
            model_name='homepage',
            name='live_video',
        ),
        migrations.AddField(
            model_name='homepage',
            name='is_live_game_enabled',
            field=models.BooleanField(default=False, verbose_name='Enable'),
        ),
        migrations.AddField(
            model_name='homepage',
            name='live_game',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='+',
                to='match.gameextrainfo',
            ),
        ),
        migrations.Alter<PERSON>ield(
            model_name='homepage',
            name='is_popup_image_enabled',
            field=models.<PERSON>olean<PERSON>ield(default=False, verbose_name='Enable'),
        ),
    ]
