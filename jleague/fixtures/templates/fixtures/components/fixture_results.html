{% load core_tags %}

{% spaceless %}
{% for dt, competitions_stages in fixtures.items %}
  <div class="fixture-results">
    {% if competitions_stages.title %}
      {% include 'components/section-header--fixture--fixture.html' with title=competitions_stages.title %}
    {% else %}
      {% if competitions_stages.is_today %}
        {% include 'components/section-header--fixture.html' with title_translation_key='MATCH_PAGE_TODAY' suffix=' · '|add:dt selected_competition=None %}
      {% else %}
        {% include 'components/section-header--fixture.html' with title=dt selected_competition=None %}
      {% endif %}
    {% endif %}

    {% for stage_key, competition_stage in competitions_stages.items %}
      {% if competition_stage.competition.slug %}
        <div class="match-list-container">
          {% comment %} <div class="divider competition-bg--{{ competition_stage.competition.slug }}"></div> {% endcomment %}

          <div class="match-list-header">
            <div class="league-container">
              {% include 'components/competition-logo.html' with slug=competition_stage.competition.slug size='small' year=selected_year %}

              <span class="competition-name">
                {% if selected_competition_name %}
                  {{ selected_competition_name }}
                {% elif competitions_stages.competition_name %}
                  {% comment %} !use for AFS {% endcomment %}
                  {{ competitions_stages.competition_name }}
                {% else %}
                  {% translation_obj competition_stage.competition.t_name %}
                {% endif %}
              </span>
            </div>

            {% if not ticket_only %}
              <div class="nav-container">
                {% if competition_stage.nav.prev_stage_name %}
                  <a class="nav-matchweek nav-matchweek--previous" href="{{ competition_stage.nav.prev_stage_url }}">
                    <span>{{ competition_stage.nav.prev_stage_name }}</span>
                  </a>
                {% endif %}

                <span class="nav-matchweek nav-matchweek--current">
                  {{ competition_stage.nav.current_stage_name }}
                </span>

                {% if competition_stage.nav.next_stage_name %}
                  <a class="nav-matchweek nav-matchweek--next" href="{{ competition_stage.nav.next_stage_url }}">
                    <span>{{ competition_stage.nav.next_stage_name }}</span>
                  </a>
                {% endif %}
              </div>
            {% else %}
              <div class="nav-container">
                <span class="nav-matchweek nav-matchweek--current">
                  {{ competition_stage.nav.current_stage_fullname }}
                </span>
              </div>
            {% endif %}
          </div>

          <div class="match-list">
            {% for game in competition_stage.games %}
              {% include 'fixtures/components/fixture_item.html' with game=game %}
            {% endfor %}
          </div>
        </div>
      {% endif %}
    {% endfor %}
  </div>
{% endfor %}
{% endspaceless %}
