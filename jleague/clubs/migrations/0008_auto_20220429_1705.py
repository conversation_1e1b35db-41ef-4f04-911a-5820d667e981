# Generated by Django 3.1.13 on 2022-04-29 08:05

from django.db import migrations, models
import django.db.models.deletion
import modelcluster.fields


class Migration(migrations.Migration):
    dependencies = [
        ('clubs', '0007_auto_20220425_1518'),
        ('mascots', '0001_initial'),
        ('stadiums', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='clubextrainfo',
            options={'ordering': ['title']},
        ),
        migrations.AddField(
            model_name='clubextrainfo',
            name='address',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='clubextrainfo',
            name='introduction',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='clubextrainfo',
            name='phone_number',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='clubextrainfo',
            name='practice_ground',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='clubextrainfo',
            name='president',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='clubextrainfo',
            name='four_letters_name',
            field=models.CharField(blank=True, max_length=4, null=True),
        ),
        migrations.AlterField(
            model_name='clubextrainfo',
            name='title',
            field=models.CharField(default='', max_length=255),
        ),
        migrations.CreateModel(
            name='ClubStadium',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sort_order', models.IntegerField(blank=True, editable=False, null=True)),
                (
                    'club_extra_info',
                    modelcluster.fields.ParentalKey(
                        default=None,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='club_stadiums',
                        to='clubs.clubextrainfo',
                    ),
                ),
                (
                    'stadium',
                    models.ForeignKey(
                        default=None,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='+',
                        to='stadiums.stadiumextrainfo',
                    ),
                ),
            ],
            options={
                'ordering': ['sort_order'],
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='ClubMascot',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sort_order', models.IntegerField(blank=True, editable=False, null=True)),
                (
                    'club_extra_info',
                    modelcluster.fields.ParentalKey(
                        default=None,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='club_mascots',
                        to='clubs.clubextrainfo',
                    ),
                ),
                (
                    'mascot',
                    models.ForeignKey(
                        default=None, on_delete=django.db.models.deletion.CASCADE, related_name='+', to='mascots.mascot'
                    ),
                ),
            ],
            options={
                'ordering': ['sort_order'],
                'abstract': False,
            },
        ),
    ]
