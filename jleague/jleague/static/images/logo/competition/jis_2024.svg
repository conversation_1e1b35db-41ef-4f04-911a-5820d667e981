<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 400 128" xmlns="http://www.w3.org/2000/svg">
  <g transform="matrix(0.3344400227069855, 0, 0, 0.3344400227069855, -21.059673309326172, -160.78367614746094)">
    <path class="cls-2" d="M62.97,667.11v-7.99h120.72v-162.68h189.09v170.67c0,8.91.05,17.81-.22,26.71-.27,8.92-.94,17.75-2.44,26.57-3.01,17.69-9.14,34.46-18.12,49.98-27.7,47.87-78.84,77.21-134.12,77.08-55.28.13-106.42-29.21-134.12-77.08-8.98-15.52-15.11-32.29-18.12-49.98-1.5-8.82-2.17-17.65-2.44-26.57-.27-8.9-.22-17.81-.22-26.71Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <circle class="cls-3" cx="217.88" cy="692.33" r="47.66" style="fill: rgb(255, 255, 255); stroke-width: 0px;"/>
    <circle class="cls-1" cx="285.29" cy="759.74" r="47.66" style="fill: rgb(237, 28, 36); stroke-width: 0px;"/>
  </g>
  <g transform="matrix(0.3344400227069855, 0, 0, 0.3344400227069855, -21.059673309326172, -160.78367614746094)">
    <g>
      <g>
        <path class="cls-2" d="M698.31,532.67l-4.98,5.88c7.83,9.31,23.51,4.08,23.51-9.51v-23.35h5.46v-7.05h-18.62v7.05h5.49v23.08c.06,6.81-6.94,8.75-10.86,3.89Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
        <rect class="cls-2" x="721.8" y="533.49" width="7.36" height="7.07" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
        <polygon class="cls-2" points="789.38 521.55 789.38 533.49 805 533.49 805 526.26 812.27 526.26 812.27 540.56 776.66 540.56 776.66 533.49 782.12 533.49 782.12 505.7 776.66 505.7 776.66 498.65 812.27 498.65 812.27 511.22 805 511.22 805 505.7 789.38 505.7 789.38 514.79 801.37 514.79 801.37 521.55 789.38 521.55" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
        <path class="cls-2" d="M898.53,510.73c-3.03-3.3-6.31-5.61-11.07-5.61-8.35,0-14.91,5.91-14.91,14.42s6.37,14.55,14.72,14.55c6.27,0,11.03-3.56,12.95-8.98h-13.31v-7.33h21.82c.02.66.02,1,.02,1.35,0,13.82-10.2,22.54-21.62,22.54-13.29,0-21.94-10.05-21.94-22.54s9.99-21.71,21.94-21.71c4.42,0,8.13,1.15,11.4,3.52v-2.28h7.4v14.87h-7.4v-2.79Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
        <polygon class="cls-2" points="853.05 498.65 853.05 505.7 848.36 505.7 859.54 533.49 864.08 533.49 864.08 540.56 846.86 540.56 846.86 533.49 851.51 533.49 841.49 507.25 831.47 533.49 836.12 533.49 836.12 540.56 818.9 540.56 818.9 533.49 823.44 533.49 834.62 505.7 829.93 505.7 829.93 498.65 853.05 498.65" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
        <path class="cls-2" d="M936.11,541.83c-8.56,0-17.12-6.53-17.12-17.94v-18.2h-5.58v-7.05h18.23v7.05h-5.57v17.81c0,5.94,4.29,10.75,10.04,10.75s10.04-4.82,10.04-10.75v-17.81h-5.57v-7.05h18.23v7.05h-5.58v18.2c0,11.6-8.56,17.94-17.12,17.94Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
        <polygon class="cls-2" points="978.08 521.55 978.08 533.49 993.69 533.49 993.69 526.26 1000.96 526.26 1000.96 540.56 965.36 540.56 965.36 533.49 970.81 533.49 970.81 505.7 965.36 505.7 965.36 498.65 1000.96 498.65 1000.96 511.22 993.69 511.22 993.69 505.7 978.08 505.7 978.08 514.79 990.06 514.79 990.06 521.55 978.08 521.55" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
        <polygon class="cls-2" points="754.8 498.65 736.37 498.65 736.37 505.7 741.83 505.7 741.83 533.49 736.37 533.49 736.37 540.56 770.1 540.56 770.1 526.26 762.83 526.26 762.83 533.49 749.09 533.49 749.09 505.7 754.8 505.7 754.8 498.65" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      </g>
      <g>
        <path class="cls-2" d="M722.37,685.79v.92c-.14,12.56-8.22,18.84-24.26,18.84-4.71,0-8.94-.79-12.69-2.36-2.93-1.23-5.15-2.73-6.65-4.5v5.73h-10.54v-22.62h10.54c0,4.43,2.18,7.95,6.55,10.54,3.69,2.25,7.88,3.38,12.59,3.38,3.41,0,6.21-.61,8.4-1.84,2.8-1.5,4.2-3.82,4.2-6.96,0-2.39-1.07-4.28-3.21-5.68-2.14-1.4-5.62-2.57-10.44-3.53-9.3-1.91-15.51-3.62-18.64-5.12-6.59-3.28-9.92-8.36-9.98-15.25v-.51c-.07-4.5,1.53-8.26,4.79-11.26,3.94-3.75,9.81-5.63,17.62-5.63,3.4,0,6.69.48,9.88,1.43,4.01,1.16,6.86,2.83,8.55,5.02v-5.53h10.55v19.34h-10.55c-.2-2.11-1.57-4.23-4.1-6.35-3.34-2.66-7.71-3.99-13.1-3.99-7.85,0-11.77,2.49-11.77,7.47,0,2.45,1.47,4.33,4.4,5.63,2.32,1.09,6.79,2.29,13.41,3.58,8.26,1.57,14.23,3.62,17.92,6.14,4.43,3,6.62,7.37,6.55,13.1Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
        <path class="cls-2" d="M784.09,704.42h-55.79v-10.03h7.98v-43.51h-7.98v-10.03h54.97v18.32h-10.75v-8.29h-24.67v15.66h19.76v9.52h-19.76v18.32h25.49v-9.93h10.75v19.96Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
        <path class="cls-2" d="M858.1,704.42h-18.12v-13.62c0-4.71-.48-7.85-1.45-9.41-1.58-2.59-4.91-3.89-10-3.89h-15.27v16.89h8.29v10.03h-27.43v-10.03h7.57v-43.51h-7.57v-10.03h34.09c16.24,0,24.36,5.7,24.36,17.09,0,8.12-4.1,13-12.29,14.64,1.98,0,3.92.55,5.83,1.64,3.62,2.12,5.42,6.14,5.42,12.08v8.09h6.55v10.03ZM840.7,659.18c0-5.53-4.21-8.29-12.64-8.29h-14.8v16.89h15.21c8.15,0,12.23-2.87,12.23-8.6Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
        <path class="cls-2" d="M895.77,704.42h-27.43v-10.03h7.98v-43.61h-7.98v-9.93h27.43v9.93h-7.88v43.61h7.88v10.03Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
        <path class="cls-2" d="M961.59,704.42h-55.79v-10.03h7.98v-43.51h-7.98v-10.03h54.97v18.32h-10.75v-8.29h-24.67v15.66h19.76v9.52h-19.76v18.32h25.49v-9.93h10.75v19.96Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
        <path class="cls-2" d="M1026.07,685.79v.92c-.14,12.56-8.22,18.84-24.26,18.84-4.71,0-8.94-.79-12.69-2.36-2.93-1.23-5.15-2.73-6.65-4.5v5.73h-10.54v-22.62h10.54c0,4.43,2.18,7.95,6.55,10.54,3.69,2.25,7.88,3.38,12.59,3.38,3.41,0,6.21-.61,8.4-1.84,2.8-1.5,4.2-3.82,4.2-6.96,0-2.39-1.07-4.28-3.21-5.68-2.14-1.4-5.62-2.57-10.44-3.53-9.3-1.91-15.51-3.62-18.64-5.12-6.59-3.28-9.92-8.36-9.98-15.25v-.51c-.07-4.5,1.53-8.26,4.79-11.26,3.94-3.75,9.81-5.63,17.62-5.63,3.4,0,6.69.48,9.88,1.43,4.01,1.16,6.86,2.83,8.55,5.02v-5.53h10.55v19.34h-10.55c-.2-2.11-1.57-4.23-4.1-6.35-3.34-2.66-7.71-3.99-13.1-3.99-7.85,0-11.77,2.49-11.77,7.47,0,2.45,1.47,4.33,4.4,5.63,2.32,1.09,6.79,2.29,13.41,3.58,8.26,1.57,14.23,3.62,17.92,6.14,4.43,3,6.62,7.37,6.55,13.1Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      </g>
      <g>
        <g>
          <path class="cls-2" d="M462.72,624.3h-27.43v-10.03h7.98v-43.61h-7.98v-9.93h27.43v9.93h-7.88v43.61h7.88v10.03Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
          <path class="cls-2" d="M538.37,570.77h-7.57v53.54h-13.1l-28.05-43.81v33.78h7.57v10.03h-26.41v-10.03h7.78v-43.51h-7.78v-10.03h19.8l29.13,45.31v-35.28h-7.78v-10.03h26.41v10.03Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
          <path class="cls-2" d="M605.83,583.56h-11.26v-12.8h-12.29v43.51h9.93v10.03h-31.43v-10.03h9.93v-43.51h-12.18v12.8h-11.26v-22.83h58.55v22.83Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
          <path class="cls-2" d="M669.91,624.3h-55.79v-10.03h7.98v-43.51h-7.98v-10.03h54.97v18.32h-10.75v-8.29h-24.67v15.66h19.76v9.52h-19.76v18.32h25.49v-9.93h10.75v19.96Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
          <path class="cls-2" d="M741.87,624.3h-18.12v-13.62c0-4.71-.48-7.85-1.45-9.41-1.58-2.59-4.91-3.89-10-3.89h-15.27v16.89h8.29v10.03h-27.43v-10.03h7.57v-43.51h-7.57v-10.03h34.09c16.24,0,24.36,5.7,24.36,17.09,0,8.12-4.1,13-12.29,14.64,1.98,0,3.92.55,5.83,1.64,3.62,2.12,5.42,6.14,5.42,12.08v8.09h6.55v10.03ZM724.46,579.06c0-5.53-4.21-8.29-12.64-8.29h-14.8v16.89h15.21c8.15,0,12.23-2.87,12.23-8.6Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
          <path class="cls-2" d="M817.31,570.77h-7.57v53.54h-13.1l-28.05-43.81v33.78h7.57v10.03h-26.41v-10.03h7.78v-43.51h-7.78v-10.03h19.8l29.13,45.31v-35.28h-7.78v-10.03h26.41v10.03Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
          <path class="cls-2" d="M888.45,624.3h-25.39v-9.93h6.46l-3.69-10.75h-23.27l-3.67,10.75h5.94v9.93h-25.69v-9.93h7.78l15.76-43.61h-8.29v-10.03h38.8v10.03h-7.17l16.27,43.61h6.14v9.93ZM862.39,594.11l-8.33-23.49-8.06,23.49h16.38Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
          <path class="cls-2" d="M945.36,583.56h-11.26v-12.8h-12.29v43.51h9.93v10.03h-31.43v-10.03h9.93v-43.51h-12.18v12.8h-11.26v-22.83h58.55v22.83Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
          <path class="cls-2" d="M982.11,624.3h-27.43v-10.03h7.98v-43.61h-7.98v-9.93h27.43v9.93h-7.88v43.61h7.88v10.03Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
          <path class="cls-2" d="M1128.08,570.77h-7.57v53.54h-13.1l-28.05-43.81v33.78h7.57v10.03h-26.41v-10.03h7.78v-43.51h-7.78v-10.03h19.8l29.13,45.31v-35.28h-7.78v-10.03h26.41v10.03Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
          <path class="cls-2" d="M1199.22,624.3h-25.39v-9.93h6.46l-3.69-10.75h-23.27l-3.67,10.75h5.94v9.93h-25.69v-9.93h7.78l15.76-43.61h-8.29v-10.03h38.8v10.03h-7.17l16.27,43.61h6.14v9.93ZM1173.17,594.11l-8.33-23.49-8.06,23.49h16.38Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
          <path class="cls-2" d="M1259,624.3h-52.62v-10.03h7.98v-43.51h-7.98v-10.03h27.53v10.03h-7.98v43.51h22.21v-11.47h10.85v21.5Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
        </g>
        <path class="cls-1" d="M1056.16,592.71c0,19.41-15.74,35.15-35.15,35.15s-35.15-15.73-35.15-35.15,15.74-35.15,35.15-35.15,35.15,15.74,35.15,35.15" style="fill: rgb(237, 28, 36); stroke-width: 0px;"/>
      </g>
      <g>
        <path class="cls-2" d="M771.22,786.51h-52.04l-.74-12.24c14.21-8.87,23.5-15.1,27.87-18.68,6.68-5.49,10.03-10.52,10.03-15.09,0-7.53-4.29-11.3-12.88-11.3-3.38,0-6.37,1.2-8.97,3.59-3.24,3.03-5,7.32-5.28,12.88l-11.93-2.53c.84-7.32,3.78-13.18,8.81-17.57,5.03-4.4,11.28-6.6,18.74-6.6,6.97,0,12.7,1.94,17.21,5.8,4.5,3.87,6.75,8.94,6.75,15.2,0,5.35-1.58,10.1-4.75,14.25-2.53,3.38-6.65,7-12.35,10.87-5.91,3.73-11.79,7.42-17.63,11.08h25.65v-9.08h11.51v19.42Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
        <path class="cls-2" d="M840.47,753.9c0,10.77-2.64,19.19-7.91,25.28-5.28,6.09-12.18,9.13-20.69,9.13s-15.34-2.99-20.48-8.97c-5.42-6.19-8.13-14.67-8.13-25.44s2.68-18.75,8.02-25.02c5.35-6.26,12.21-9.39,20.58-9.39s15.32,3.13,20.64,9.39c5.31,6.26,7.97,14.6,7.97,25.02ZM828.55,753.9c0-7.25-1.48-13.18-4.45-17.79-2.97-4.61-7.06-6.92-12.28-6.92s-9.21,2.31-12.18,6.92c-2.96,4.61-4.44,10.54-4.44,17.79s1.46,13.42,4.39,17.89c2.93,4.47,7.01,6.7,12.23,6.7s9.31-2.23,12.28-6.7c2.97-4.47,4.45-10.43,4.45-17.89Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
        <path class="cls-2" d="M905.71,786.51h-52.04l-.74-12.24c14.21-8.87,23.5-15.1,27.87-18.68,6.68-5.49,10.03-10.52,10.03-15.09,0-7.53-4.29-11.3-12.88-11.3-3.38,0-6.37,1.2-8.97,3.59-3.24,3.03-5,7.32-5.28,12.88l-11.93-2.53c.84-7.32,3.78-13.18,8.81-17.57,5.03-4.4,11.28-6.6,18.74-6.6,6.97,0,12.7,1.94,17.21,5.8,4.5,3.87,6.75,8.94,6.75,15.2,0,5.35-1.58,10.1-4.75,14.25-2.53,3.38-6.65,7-12.35,10.87-5.91,3.73-11.79,7.42-17.63,11.08h25.65v-9.08h11.51v19.42Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
        <path class="cls-2" d="M977.01,769.31h-11.71v6.86h8.87v10.34h-29.66v-10.34h8.87v-6.86h-33.14l-3.07-10.77,32.2-37.58h15.94v38h11.71v10.34ZM953.37,758.97v-26.62l-23.09,26.62h23.09Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      </g>
    </g>
    <g>
      <path class="cls-2" d="M658.97,820.39h5.3v2.59h.08c1.33-2.16,3.53-3.14,6.03-3.14,6.35,0,9.21,5.14,9.21,10.9,0,5.41-2.98,10.47-8.9,10.47-2.43,0-4.75-1.06-6.08-3.06h-.08v9.64h-5.57v-27.41ZM674.02,830.59c0-3.21-1.29-6.55-4.86-6.55s-4.82,3.26-4.82,6.55,1.25,6.43,4.86,6.43,4.82-3.14,4.82-6.43Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-2" d="M692.91,819.85c6.39,0,10.51,4.23,10.51,10.7s-4.12,10.67-10.51,10.67-10.47-4.24-10.47-10.67,4.12-10.7,10.47-10.7ZM692.91,837.02c3.8,0,4.94-3.26,4.94-6.47s-1.14-6.51-4.94-6.51-4.9,3.26-4.9,6.51,1.14,6.47,4.9,6.47Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-2" d="M729.71,840.66h-5.72l-3.61-13.6h-.08l-3.45,13.6h-5.77l-6.43-20.27h5.88l3.72,13.76h.08l3.37-13.76h5.41l3.45,13.72h.08l3.72-13.72h5.72l-6.39,20.27Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-2" d="M742.73,831.88c.16,3.53,1.88,5.14,4.98,5.14,2.24,0,4.04-1.37,4.4-2.63h4.9c-1.57,4.78-4.9,6.82-9.49,6.82-6.39,0-10.35-4.39-10.35-10.67s4.2-10.7,10.35-10.7c6.9,0,10.23,5.8,9.84,12.04h-14.62ZM751.79,828.35c-.51-2.82-1.73-4.31-4.43-4.31-3.53,0-4.55,2.75-4.63,4.31h9.06Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-2" d="M760.83,820.39h5.3v3.76h.08c1.02-2.55,3.76-4.31,6.46-4.31.39,0,.86.08,1.22.19v5.18c-.51-.12-1.33-.2-2-.2-4.08,0-5.49,2.94-5.49,6.51v9.14h-5.57v-20.27Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-2" d="M780.96,831.88c.16,3.53,1.88,5.14,4.98,5.14,2.24,0,4.04-1.37,4.4-2.63h4.9c-1.57,4.78-4.9,6.82-9.49,6.82-6.39,0-10.35-4.39-10.35-10.67s4.2-10.7,10.35-10.7c6.9,0,10.23,5.8,9.84,12.04h-14.63ZM790.02,828.35c-.51-2.82-1.73-4.31-4.43-4.31-3.53,0-4.55,2.75-4.63,4.31h9.06Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-2" d="M813.53,838.08h-.08c-1.29,2.19-3.57,3.14-6.16,3.14-6.12,0-9.1-5.25-9.1-10.86s3.02-10.51,8.98-10.51c2.39,0,4.75,1.02,6,3.02h.07v-10.19h5.57v27.99h-5.29v-2.59ZM808.67,824.04c-3.65,0-4.9,3.14-4.9,6.47s1.45,6.51,4.9,6.51c3.68,0,4.78-3.22,4.78-6.55s-1.18-6.43-4.78-6.43Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-2" d="M831.93,812.67h5.57v10.19h.08c1.37-2.08,3.88-3.02,6.39-3.02,4.04,0,8.39,3.25,8.39,10.66s-4.35,10.71-8.39,10.71c-2.98,0-5.45-.9-6.66-3.14h-.08v2.59h-5.3v-27.99ZM842.04,824.04c-3.3,0-4.74,3.1-4.74,6.51s1.45,6.47,4.74,6.47,4.75-3.1,4.75-6.47-1.45-6.51-4.75-6.51Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-2" d="M865.53,843.21c-1.22,3.25-3.14,4.59-6.98,4.59-1.14,0-2.27-.08-3.41-.2v-4.59c1.06.08,2.16.24,3.26.2,1.92-.2,2.55-2.19,1.92-3.8l-7.13-19.02h5.96l4.58,13.88h.08l4.43-13.88h5.76l-8.46,22.82Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-2" d="M899.25,838.08h-.08c-1.29,2.19-3.57,3.14-6.16,3.14-6.12,0-9.09-5.25-9.09-10.86s3.02-10.51,8.97-10.51c2.39,0,4.75,1.02,6,3.02h.08v-10.19h5.56v27.99h-5.29v-2.59ZM894.39,824.04c-3.65,0-4.9,3.14-4.9,6.47s1.45,6.51,4.9,6.51c3.68,0,4.78-3.22,4.78-6.55s-1.18-6.43-4.78-6.43Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-2" d="M918.73,819.85c6.39,0,10.51,4.23,10.51,10.7s-4.12,10.67-10.51,10.67-10.47-4.24-10.47-10.67,4.12-10.7,10.47-10.7ZM918.73,837.02c3.8,0,4.94-3.26,4.94-6.47s-1.14-6.51-4.94-6.51-4.9,3.26-4.9,6.51,1.14,6.47,4.9,6.47Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-2" d="M946.58,827.53c-.36-2.27-1.81-3.49-4.12-3.49-3.57,0-4.75,3.61-4.75,6.59s1.14,6.39,4.63,6.39c2.59,0,4.08-1.65,4.43-4.12h5.37c-.7,5.37-4.43,8.31-9.76,8.31-6.12,0-10.24-4.31-10.24-10.39s3.77-10.98,10.35-10.98c4.78,0,9.18,2.51,9.53,7.68h-5.45Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-2" d="M965.21,819.85c6.39,0,10.51,4.23,10.51,10.7s-4.12,10.67-10.51,10.67-10.47-4.24-10.47-10.67,4.12-10.7,10.47-10.7ZM965.21,837.02c3.8,0,4.94-3.26,4.94-6.47s-1.14-6.51-4.94-6.51-4.9,3.26-4.9,6.51,1.14,6.47,4.9,6.47Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-2" d="M979.56,820.39h5.25v2.75h.08c1.45-2.08,3.57-3.29,6.2-3.29s4.82.94,5.92,3.37c1.17-1.77,3.29-3.37,6.11-3.37,4.31,0,7.41,2,7.41,7.25v13.57h-5.57v-11.49c0-2.71-.24-4.94-3.41-4.94s-3.72,2.59-3.72,5.14v11.29h-5.57v-11.37c0-2.35.16-5.06-3.37-5.06-1.09,0-3.76.71-3.76,4.66v11.76h-5.57v-20.27Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-2" d="M1024.81,819.85c6.39,0,10.51,4.23,10.51,10.7s-4.12,10.67-10.51,10.67-10.47-4.24-10.47-10.67,4.12-10.7,10.47-10.7ZM1024.81,837.02c3.8,0,4.94-3.26,4.94-6.47s-1.14-6.51-4.94-6.51-4.9,3.26-4.9,6.51,1.14,6.47,4.9,6.47Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    </g>
  </g>
</svg>