@import 'variable';

@mixin scrollbars(
  $size: 6px,
  $foreground-color: transparentize($color: $color-drawn, $amount: 0.8),
  $background-color: transparentize($color: $color-drawn, $amount: 0.95)
) {
  // For Chrome & Safari
  &::-webkit-scrollbar {
    width: $size;
    height: $size;
  }
  &::-webkit-scrollbar-thumb {
    background: $foreground-color;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-track {
    background: $background-color;
    border-radius: 3px;
  }

  // Standard version (Firefox only for now)
  scrollbar-color: $foreground-color $background-color;
}

@mixin text_ellipsis($max-lines: 1, $word-break: break-all) {
  text-overflow: ellipsis;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: $max-lines;
  -webkit-box-orient: vertical;
  word-break: $word-break;
}

@mixin section-header-title-text() {
  color: var(--color-white);
  font-family: var(--font-jleague-kick);
  font-style: normal;
  font-weight: 700;
  font-size: 42px;
  line-height: 48px;
  letter-spacing: 2px;
  text-align: left;
  text-transform: uppercase;

  @media screen and (max-width: 991px) {
    font-size: 32px;
    line-height: 32px;
  }

  @media screen and (max-width: 576px) {
    font-size: 24px;
    line-height: 24px;
  }
}

@mixin font-bahasa() {
  font-family: $font-barlow-condensed;
  letter-spacing: normal;
}

@mixin font-thai($is-body-text: false) {
  @if $is-body-text {
    font-family: $font-overpass;
  } @else {
    font-family: $font-noto-sans-thai;
  }
  letter-spacing: normal;
  line-height: 1.5;
}

@mixin font-vietnamese() {
  font-family: $font-barlow-condensed;
  letter-spacing: normal;
}
