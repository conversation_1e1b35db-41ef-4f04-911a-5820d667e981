const IS_DESKTOP = window.innerWidth >= 769;
const IS_TABLET = window.innerWidth <= 768 && window.innerWidth >= 577;
const IS_MOBILE = window.innerWidth < 576;
const IS_SAFARI = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
const SUPPORTED_LANGS = ['en', 'id', 'th', 'vi'];
const TOKEN_KEY = 'csrftoken';
const langPrefix = new RegExp(`^/(${SUPPORTED_LANGS.join('|')})/`, 'i');
let elementVisibleTimeout;

function isDesktop() {
  return window.innerWidth >= 769;
}

function isTablet() {
  return window.innerWidth <= 768 && window.innerWidth >= 577;
}

function isMobile() {
  return window.innerWidth < 576;
}

// Move your fullscreen check into its own function
function isFullScreen() {
  return Boolean(
    document.fullscreenElement ||
      document.webkitFullscreenElement ||
      document.mozFullScreenElement ||
      document.msFullscreenElement,
  );
}

// Make DoFullScreen() reusable by passing the element as a parameter
function doFullScreen(el) {
  // Use a guard clause to exit out of the function immediately
  if (isFullScreen()) return false;
  // Set a default value for your element parameter
  if (el === undefined) el = document.documentElement;
  // Test for the existence of document.fullscreenEnabled instead of requestFullscreen()
  if (document.fullscreenEnabled) {
    el.requestFullscreen();
  } else if (document.webkitFullscreenEnabled) {
    el.webkitRequestFullscreen();
  } else if (document.mozFullScreenEnabled) {
    el.mozRequestFullScreen();
  } else if (document.msFullscreenEnabled) {
    el.msRequestFullscreen();
  }
}

function setCookie(name, value, days = 180) {
  let expires = '';
  if (days) {
    const date = new Date();
    date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
    expires = '; expires=' + date.toUTCString();
  }
  document.cookie = name + '=' + (value || '') + expires + '; path=/';
}

function getCookie(name) {
  const nameEQ = name + '=';
  const cookies = document.cookie.split(';');
  for (let c of cookies) {
    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
  }
  return null;
}

function delCookie(name) {
  document.cookie = name + '=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
}

function checkTimezone() {
  let prevTz = getCookie('jl_timezone');
  let currTz;

  if (window.Intl) {
    currTz = window.Intl.DateTimeFormat().resolvedOptions().timeZone;
  }

  if (typeof currTz === 'undefined' || currTz === 'Etc/Unknown') {
    /**
     * The number of minutes returned by getTimezoneOffset() is:
     * - positive if the local time zone is behind UTC.
     * - negative if the local time zone is ahead of UTC.
     * For example, for UTC+10, -600 will be returned.
     */
    currTz = new Date().getTimezoneOffset(); // / 60 * -1;
  }

  if (currTz !== prevTz) {
    setCookie('jl_timezone', currTz === 0 ? '0' : currTz);
  }
}

// function checkToken() {
//   return new Promise((resolve, reject) => {
//     let token = getCookie(TOKEN_KEY);
//     if (token) {
//       resolve(token);
//     } else {
//       ajaxRequest('/t/', null, 'POST', true)
//         .then((token) => {
//           setCookie(TOKEN_KEY, token);
//           resolve(token);
//         })
//         .catch((e) => reject(e));
//     }
//   });
// }

function urlSearchQuery(params = {}) {
  let query = '';
  const keys = Object.keys(params);
  if (keys.length) {
    let count = 0;
    for (const key of keys) {
      if (typeof params[key] === 'undefined') {
        continue;
      }
      query += `${count === 0 ? '?' : '&'}${key}=${encodeURIComponent(params[key])}`;
      count++;
    }
  }
  return query;
}

function getQueryParam(name, url = window.location.href) {
  name = name.replace(/[\[\]]/g, '\\$&');
  const regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)');
  const results = regex.exec(url);
  if (!results) return;
  if (!results[2]) return '';
  return decodeURIComponent(results[2].replace(/\+/g, ' '));
}

function getCurrentPath(url) {
  let current_path = window.location.pathname;
  if (langPrefix.test(current_path)) {
    url = current_path.substring(0, 3) + url;
  }
  return url;
}

function ajaxRequest(url, payload = {}, type = 'GET', ignorePrefixAware = false) {
  return new Promise((resolve, reject) => {
    // main url language prefix aware
    url = !ignorePrefixAware ? getCurrentPath(url) : url;

    $.ajax({
      type: type,
      url: url,
      data: payload,
      beforeSend: function (xhr, settings) {
        // these HTTP methods do not require CSRF protection
        const csrfSafeMethod = /^(GET|HEAD|OPTIONS|TRACE)$/;
        if (!csrfSafeMethod.test(settings.type) && !this.crossDomain) {
          // let csrftoken = getCookie('csrftoken');
          // if (!csrftoken) {
          //   csrftoken = document.querySelector('[name=csrfmiddlewaretoken]').value;
          //   setCookie('csrftoken', csrftoken);
          // }

          const csrftoken = document.querySelector('[name=csrfmiddlewaretoken]').value;
          xhr.setRequestHeader('X-CSRFToken', csrftoken);
        }
      },
    })
      .done((data) => resolve(data))
      .fail((error) => reject(error));
  });
}

function isScrolledIntoView(selector, minVisible) {
  if (selector.length === 0) {
    return false;
  }

  const element = typeof selector === 'string' ? $(selector) : selector;
  const pageTop = $(window).scrollTop();
  const pageBottom = pageTop + $(window).height();
  const elementTop = element.offset().top;
  const elementBottom = elementTop + element.height();

  if (minVisible && minVisible > 0) {
    const botGap = ((elementBottom - pageTop) / element.height()) * 100;
    const topGap = ((pageBottom - elementTop) / element.height()) * 100;
    if (botGap >= minVisible && topGap >= minVisible) {
      return true;
    }
    return false;
  } else {
    // element top edge is above page bottom edge and element bottom is below page top edge
    return elementTop <= pageBottom && elementBottom >= pageTop;
  }
}

function isElementVisible(selector, callback, options = {}) {
  duration = options.duration || 2000;
  minVisible = options.minVisible || 80;
  clearTimeout(elementVisibleTimeout);
  elementVisibleTimeout = setTimeout(function () {
    if (isScrolledIntoView(selector, minVisible)) {
      sendElementVisibleEvent(selector);
      if (typeof callback === 'function') {
        return callback();
      }
    }
  }, duration);
}

function copyLink() {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(window.location.href);
  } else if (document.execCommand) {
    const currentUrl = window.location.href;
    const urlInput = document.createElement('input');
    urlInput.classList.add('visually-hidden');
    urlInput.value = currentUrl;
    document.body.appendChild(urlInput);
    urlInput.select();
    document.execCommand('copy');
    document.body.removeChild(urlInput);
  }
}

function scrollToTop() {
  $('html, body').animate({ scrollTop: 0 }, 'fast');
}

function disableBodyScroll() {
  // disable a body scrollbar when a modal opened
  $('body').addClass('noscroll');
}

function enableBodyScroll() {
  // enable a body scrollbar when a modal closed
  $('body').removeClass('noscroll');
}

function createPaginator(getLink, onclick = null, selectorName = '.paginator') {
  const paginatorContainer = $(selectorName);

  function _createChild(index, tagName, label, className = null) {
    if (!paginatorContainer) {
      return null;
    }

    const paginatorItem = document.createElement(tagName);
    paginatorItem.classList.add('paginator__item');

    if (className) {
      paginatorItem.classList.add(className);
    }

    paginatorItem.innerHTML = label;

    if (typeof getLink === 'function') {
      paginatorItem.href = getLink(index);
    } else {
      paginatorItem.href = getLink;
    }

    if (typeof onclick === 'function') {
      paginatorItem.onclick = onclick.bind(this, index);
    }

    paginatorContainer.append(paginatorItem);
    return paginatorItem;
  }

  // left arrows
  _createChild(1, 'a', '<<', pageIndex === 1 ? 'disabled' : '');
  _createChild(pageIndex - 1, 'a', '<', pageIndex === 1 ? 'disabled' : '');

  // setpup
  let pageOffset = 5;
  let pageLimit = 11; // DESKTOP
  if (IS_TABLET) {
    pageLimit = 10;
  } else if (IS_MOBILE) {
    pageLimit = 6;
  }
  let pageStart;
  let pageEnd;

  if (pageIndex - pageOffset < 1 && pageIndex + pageOffset > pageCount) {
    pageStart = 1;
    pageEnd = pageCount;
  } else if (pageIndex + pageOffset > pageCount) {
    pageStart = pageCount - pageLimit + 1;
    pageEnd = pageCount;
  } else if (pageIndex - pageOffset < 1) {
    pageStart = 1;
    pageEnd = pageLimit;
  } else {
    pageStart = pageIndex - pageOffset;
    pageEnd = pageIndex + pageOffset;
  }

  if (pageStart < 1) {
    pageStart = 1;
  }
  if (pageEnd > pageCount) {
    pageEnd = pageCount;
  }

  // page number
  for (let i = pageStart; i <= pageEnd; i++) {
    _createChild(i, 'a', i, i === pageIndex ? 'active' : '');
  }

  // tripple dot
  if (pageIndex + pageOffset < pageCount) {
    _createChild(pageIndex, 'div', '\u00B7\u00B7\u00B7', 'disabled');
  }

  // right arrows
  _createChild(pageIndex + 1, 'a', '>', pageIndex === pageCount ? 'disabled' : '');
  _createChild(pageCount, 'a', '>>', pageIndex === pageCount ? 'disabled' : '');
}

function initPopupVideo(element, options) {
  if (!(element instanceof jQuery)) {
    element = $(element);
  }

  element.magnificPopup({
    preloader: false,
    removalDelay: 200,
    ...options,
    type: 'iframe',
  });
}

function initPopupPhoto(element, options) {
  if (!(element instanceof jQuery)) {
    element = $(element);
  }
  element.magnificPopup({ ...options, type: 'image' });
}

/**
 * GA4 send event helper
 */
function _sendEvent(key, value) {
  if (typeof gtag !== 'function') {
    return;
  }
  return gtag('event', key, value);
}

function prepareVideoEventData(data) {
  return {
    video_current_time: data.currentTime,
    video_duration: data.duration,
    video_percent: data.percent,
    video_provider: data.provider,
    video_title: data.title,
    video_url: data.url,
  };
}

function sendVideoStartEvent(data) {
  _sendEvent('video_start', prepareVideoEventData(data));
}

function sendVideoCompleteEvent(data) {
  _sendEvent('video_complete', prepareVideoEventData(data));
}

function sendVideoProgressEvent(data) {
  _sendEvent('video_progress', prepareVideoEventData(data));
}

function sendSelectContentEvent(content_type, item_id) {
  item_id = `${content_type}:${item_id}`;
  _sendEvent('select_content', { content_type, item_id });
}

function sendSearchEvent(content_type, search_term) {
  search_term = `${content_type}:${search_term}`;
  _sendEvent('search', { search_term });
}

function sendSelectSearchResultEvent(content_type, content_key, search_term, link_url, link_domain) {
  const eventData = {
    content_type,
    content_key,
    search_term,
    link_url,
    link_domain,
  };
  _sendEvent('select_search_result', eventData);
}

function sendElementClickEvent(element_id, element_text, element_class_name) {
  _sendEvent('element_click', {
    element_id,
    element_text,
    element_class_name,
  });
}

function sendElementVisibleEvent(element_class_name) {
  _sendEvent('element_visible', {
    element_class_name,
  });
}

function sendViewShareElementEvent(element_id, element_text, element_class_name, link_url) {
  _sendEvent('view_share_element', {
    element_id,
    element_text,
    element_class_name,
    link_url,
  });
}

function sendParallaxEngagedEvent(element_class_name) {
  _sendEvent('parallax_engaged', { element_class_name });
}

function sendAppInstallAcceptedEvent() {
  _sendEvent('pwa_prompt', { value: 'accept' });
}

function sendAppInstallRejectedEvent() {
  _sendEvent('pwa_prompt', { value: 'reject' });
}

function sendAppInstallSuccessEvent() {
  _sendEvent('pwa_install', { value: 1 });
}

function sendAppInstallDisplayEvent() {
  _sendEvent('pwa_display', { value: 1 });
}
