{% load core_tags %}

<div class="stat-item current-standing">
  <div class="stat-item__body info-container">
    <div>{% translation 'CLUB_PROFILE_PAGE_CURRENT_STANDINGS' %}</div>

    <div class="standing-list">
      {% if not standings %}
        {% include 'components/data-empty-state.html' %}
      {% else %}
        {% for standing in standings %}
          <div class="standing-item {% if standing.highlight %}highlight{% endif %}">
            <span class="ranking">{{ standing.ranking }}</span>

            {% include 'components/standing-replace.html' with arrow=standing.arrow %}

            <a
              class="club-info__link"
              href="{% url 'club_profile' standing.club.name_slug %}"
              onclick="sendSelectContentEvent('club', '{{ standing.club.slug }}');"
            >
              {% include 'components/club-emblem.html' with slug=standing.club.slug %}

              <span class="team-name">{% club_name standing.club %}</span>
            </a>

            <span class="games-count"> {{ standing.games_played }} </span>

            <span class="goal-difference">
              {% if standing.goal_difference > 0 %}+{% endif %}{{ standing.goal_difference }}
            </span>

            <span class="points">{{ standing.points }}</span>
          </div>
        {% endfor %}
      {% endif %}
    </div>
  </div>

  <div class="stat-item__footer">
    {% if competition and competition.slug %}
      <a
        class="jl-button"
        href="{% url 'standings' competition.slug selected_year %}"
        onclick="sendSelectContentEvent('page', 'standings');"
      >
        <span class="jl-button__label">
          {% translation 'CLUB_PROFILE_PAGE_VIEW_FULL_STANDINGS' %}
        </span>
      </a>
    {% endif %}
  </div>
</div>