@import 'setup/variable';
@import 'setup/mixin';

.body-club-profile {
  .club-selector {
    margin-top: 64px;
    position: relative;
    display: flex;
    flex-direction: column;

    .club-selector__current,
    .club-selector__dropdown {
      display: flex;
      align-items: center;
      padding: 0 24px;
    }

    .club-selector__current {
      cursor: pointer;
      margin: 0 auto;
      max-width: 1120px;
      justify-content: center;
      position: relative;

      .team-text {
        position: relative;
        display: block;
        color: $color-white;
        font-family: var(--font-druk-wide);
        font-style: normal;
        font-weight: 900;
        font-size: 44px;
        line-height: 44px;
        text-align: center;
        text-transform: uppercase;
        z-index: 2;
      }

      .team-bg {
        position: absolute;
        top: -12px;
        bottom: -8px;
        left: 0;
        width: 100%;
        border-radius: 3px;
        opacity: 1;
        transition: opacity 300ms ease-in-out;
        z-index: 1;
      }

      .arrow-down {
        flex: 0 0 auto;
        display: flex;
        margin-left: 24px;
        width: 28px;
        transform: rotate(-180deg);
        transition: all 0.3s linear;
        z-index: 2;

        svg {
          width: 100%;
          height: auto;
        }
      }

      &::after {
        content: '';
        position: absolute;
        width: 200px;
        height: 6px;
        left: 50%;
        bottom: -15px;
        transform: translateX(-50%);
        background-image: linear-gradient(
          90deg,
          transparentize($color: $color-black, $amount: 0.9999) 0%,
          $color-red 99.69%
        );
        background-repeat: no-repeat;
        background-size: cover;
      }

      &:hover {
        .team-bg {
          opacity: 0.5;
        }
      }
    }

    .club-selector__dropdown {
      flex-direction: column;
      background: $color-white;
      border-bottom: 1px solid $color-black;
      position: absolute;
      top: 48px;
      left: 0;
      width: 100%;
      z-index: 20;
      padding: 40px 0;
      transition: all 0.3s ease-in-out;
      overflow: hidden;
    }

    .dropdown-group {
      display: flex;
      flex-direction: column;
      max-width: 1120px;
      width: 100%;
      margin: 0 auto;
      transition: all 0.3s ease-in-out;

      .dropdown-group__header {
        display: flex;
        align-items: center;
        padding: 0 32px;

        .league-name {
          font-family: $font-overpass;
          font-style: normal;
          font-weight: 900;
          font-size: 20px;
          line-height: normal;
          color: $color-black;
          margin-left: 16px;
        }
      }

      .dropdown-group__body {
        display: grid;
        grid-column-gap: 32px;
        grid-row-gap: 8px;
        grid-template-columns: repeat(4, 1fr);
        margin-top: 16px;
        padding: 0 32px;

        .dropdown-item {
          display: flex;
          align-items: center;
          position: relative;
          padding: 2px 0;
          width: 100%;
          background: $color-earth-1;

          .club-emblem,
          .team-name {
            display: inline-block;
            z-index: 2;
          }

          .club-emblem {
            flex: 0 0 auto;
            margin-right: 4px;
            width: 40px;
            height: 40px;
          }

          .club-emblem--tokushima-alt {
            display: none;
          }

          .club-emblem--tokyov-alt {
            display: none;
          }

          .team-name.team-text {
            color: $color-black;
            font-family: $font-jleague-kick;
            font-style: normal;
            font-weight: 700;
            font-size: 20px;
            line-height: 24px;
            text-align: left;
            flex: auto;
            @include text_ellipsis();
          }

          .team-bg {
            position: absolute;
            opacity: 0;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 3px;
            transition: opacity 300ms ease-in-out;
          }

          &:not(.dropdown-item--active, :hover) {
            .team-name.team-text {
              color: $color-black !important;
            }
          }

          &:hover,
          &.dropdown-item--active {
            opacity: 1;

            .team-bg {
              opacity: 1;
            }

            .team-name.team-text {
              color: initial;
            }

            .club-emblem.club-emblem--tokushima {
              display: none;
            }

            .club-emblem--tokushima-alt {
              display: block;
            }

            .club-emblem.club-emblem--tokyov {
              display: none;
            }

            .club-emblem--tokyov-alt {
              display: block;
            }
          }
        }
      }

      & + .dropdown-group {
        margin-top: 32px;
      }
    }

    &.club-selector--closed {
      .club-selector__current {
        .arrow-down {
          transform: rotate(0deg);
        }
      }

      .club-selector__dropdown {
        opacity: 0;
        height: 0;
      }
    }

    @media screen and (max-width: 1199px) {
      .club-selector__current {
        .team-text {
          font-size: 40px;
          line-height: 40px;
        }

        .arrow-down {
          width: 20px;
        }
      }

      .dropdown-group {
        .dropdown-group__body {
          grid-template-columns: repeat(3, 1fr);
        }
      }
    }

    @media screen and (max-width: 890px) {
      .club-selector__current {
        .team-text {
          font-size: 26px;
          line-height: 26px;
        }
      }

      .dropdown-group {
        .dropdown-group__body {
          grid-template-columns: repeat(2, 1fr);
        }
      }
    }

    @media screen and (max-width: 767px) {
      .club-selector__current {
        .team-text {
          font-size: 18px;
          line-height: 18px;
        }

        .arrow-down {
          margin-left: 20px;
        }
      }
    }

    @media screen and (max-width: 576px) {
      .club-selector__current {
        .team-text {
          font-size: 16px;
          line-height: 16px;
        }

        .arrow-down {
          margin-left: 16px;
          width: 16px;
        }
      }

      .dropdown-group {
        .dropdown-group__header {
          .league-name {
            font-size: 16px;
          }
        }

        .dropdown-group__body {
          grid-template-columns: 1fr;
          padding: 0 16px;
        }
      }
    }
  }
}

body.lang-th {
  .club-selector {
    .club-selector__current .team-text,
    .dropdown-group .dropdown-group__header .league-name,
    .dropdown-group .dropdown-group__body .dropdown-item .team-name.team-text {
      @include font-thai();
    }
  }
}

// body.body-club-profile.lang-th {
//   .club-selector {
//     .club-selector__current .team-text {
//       font-family: 'Barlow Condensed';
//     }
//     .dropdown-group .dropdown-group__body .dropdown-item .team-name.team-text {
//       white-space: nowrap;
//       width: 50px;
//       overflow: hidden;
//       text-overflow: ellipsis;
//       line-height: 31px;
//     }
//   }
// }
