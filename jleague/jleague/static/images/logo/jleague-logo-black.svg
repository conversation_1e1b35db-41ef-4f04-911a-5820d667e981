<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.2" width="43.72mm" height="9.21mm" viewBox="3332 18633 4372 921" preserveAspectRatio="xMidYMid" fill-rule="evenodd" stroke-width="28.222" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg" xmlns:ooo="http://xml.openoffice.org/svg/export" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:presentation="http://sun.com/xmlns/staroffice/presentation" xmlns:smil="http://www.w3.org/2001/SMIL20/" xmlns:anim="urn:oasis:names:tc:opendocument:xmlns:animation:1.0" xml:space="preserve">
 <defs class="EmbeddedBulletChars">
  <g id="bullet-char-template-57356" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M 580,1141 L 1163,571 580,0 -4,571 580,1141 Z"/>
  </g>
  <g id="bullet-char-template-57354" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M 8,1128 L 1137,1128 1137,0 8,0 8,1128 Z"/>
  </g>
  <g id="bullet-char-template-10146" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M 174,0 L 602,739 174,1481 1456,739 174,0 Z M 1358,739 L 309,1346 659,739 1358,739 Z"/>
  </g>
  <g id="bullet-char-template-10132" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M 2015,739 L 1276,0 717,0 1260,543 174,543 174,936 1260,936 717,1481 1274,1481 2015,739 Z"/>
  </g>
  <g id="bullet-char-template-10007" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M 0,-2 C -7,14 -16,27 -25,37 L 356,567 C 262,823 215,952 215,954 215,979 228,992 255,992 264,992 276,990 289,987 310,991 331,999 354,1012 L 381,999 492,748 772,1049 836,1024 860,1049 C 881,1039 901,1025 922,1006 886,937 835,863 770,784 769,783 710,716 594,584 L 774,223 C 774,196 753,168 711,139 L 727,119 C 717,90 699,76 672,76 641,76 570,178 457,381 L 164,-76 C 142,-110 111,-127 72,-127 30,-127 9,-110 8,-76 1,-67 -2,-52 -2,-32 -2,-23 -1,-13 0,-2 Z"/>
  </g>
  <g id="bullet-char-template-10004" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M 285,-33 C 182,-33 111,30 74,156 52,228 41,333 41,471 41,549 55,616 82,672 116,743 169,778 240,778 293,778 328,747 346,684 L 369,508 C 377,444 397,411 428,410 L 1163,1116 C 1174,1127 1196,1133 1229,1133 1271,1133 1292,1118 1292,1087 L 1292,965 C 1292,929 1282,901 1262,881 L 442,47 C 390,-6 338,-33 285,-33 Z"/>
  </g>
  <g id="bullet-char-template-9679" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M 813,0 C 632,0 489,54 383,161 276,268 223,411 223,592 223,773 276,916 383,1023 489,1130 632,1184 813,1184 992,1184 1136,1130 1245,1023 1353,916 1407,772 1407,592 1407,412 1353,268 1245,161 1136,54 992,0 813,0 Z"/>
  </g>
  <g id="bullet-char-template-8226" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M 346,457 C 273,457 209,483 155,535 101,586 74,649 74,723 74,796 101,859 155,911 209,963 273,989 346,989 419,989 480,963 531,910 582,859 608,796 608,723 608,648 583,586 532,535 482,483 420,457 346,457 Z"/>
  </g>
  <g id="bullet-char-template-8211" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M -4,459 L 1135,459 1135,606 -4,606 -4,459 Z"/>
  </g>
  <g id="bullet-char-template-61548" transform="scale(0.00048828125,-0.00048828125)">
   <path d="M 173,740 C 173,903 231,1043 346,1159 462,1274 601,1332 765,1332 928,1332 1067,1274 1183,1159 1299,1043 1357,903 1357,740 1357,577 1299,437 1183,322 1067,206 928,148 765,148 601,148 462,206 346,322 231,437 173,577 173,740 Z"/>
  </g>
 </defs>
 <g class="Page">
  <g class="com.sun.star.drawing.ClosedBezierShape">
   <g id="id3">
    <rect class="BoundingBox" stroke="none" fill="none" x="4226" y="18848" width="329" height="507"/>
    <path fill="rgb(35,31,32)" stroke="none" d="M 4282,19234 L 4226,19300 C 4314,19406 4492,19346 4492,19193 L 4492,18929 4554,18929 4554,18848 4342,18848 4342,18929 4405,18929 4405,19190 C 4405,19267 4326,19289 4282,19234 Z"/>
   </g>
  </g>
  <g class="com.sun.star.drawing.PolyPolygonShape">
   <g id="id4">
    <rect class="BoundingBox" stroke="none" fill="none" x="4547" y="19242" width="85" height="82"/>
    <path fill="rgb(35,31,32)" stroke="none" d="M 4547,19242 L 4631,19242 4631,19323 4547,19323 4547,19242 Z"/>
   </g>
  </g>
  <g class="com.sun.star.drawing.PolyPolygonShape">
   <g id="id5">
    <rect class="BoundingBox" stroke="none" fill="none" x="5167" y="18848" width="404" height="476"/>
    <path fill="rgb(35,31,32)" stroke="none" d="M 5311,19107 L 5311,19243 5488,19243 5488,19161 5570,19161 5570,19323 5167,19323 5167,19243 5229,19243 5229,18928 5167,18928 5167,18848 5570,18848 5570,18990 5488,18990 5488,18928 5311,18928 5311,19031 5447,19031 5447,19107 5311,19107 Z"/>
   </g>
  </g>
  <g class="com.sun.star.drawing.ClosedBezierShape">
   <g id="id6">
    <rect class="BoundingBox" stroke="none" fill="none" x="6168" y="18834" width="494" height="502"/>
    <path fill="rgb(35,31,32)" stroke="none" d="M 6544,18985 C 6510,18947 6473,18921 6419,18921 6325,18921 6251,18988 6251,19084 6251,19180 6323,19250 6417,19250 6488,19250 6542,19208 6563,19147 L 6413,19147 6413,19064 6660,19064 C 6661,19072 6661,19076 6661,19080 6661,19237 6544,19335 6415,19335 6265,19335 6168,19222 6168,19080 6168,18942 6280,18834 6415,18834 6465,18834 6507,18847 6544,18874 L 6544,18848 6628,18848 6628,19016 6544,19016 6544,18985 Z"/>
   </g>
  </g>
  <g class="com.sun.star.drawing.PolyPolygonShape">
   <g id="id7">
    <rect class="BoundingBox" stroke="none" fill="none" x="5644" y="18848" width="513" height="476"/>
    <path fill="rgb(35,31,32)" stroke="none" d="M 6031,18848 L 6031,18928 5978,18928 6105,19243 6156,19243 6156,19323 5961,19323 5961,19243 6014,19243 5901,18945 5786,19243 5839,19243 5839,19323 5644,19323 5644,19243 5696,19243 5822,18928 5769,18928 5769,18848 6031,18848 Z"/>
   </g>
  </g>
  <g class="com.sun.star.drawing.ClosedBezierShape">
   <g id="id8">
    <rect class="BoundingBox" stroke="none" fill="none" x="6712" y="18848" width="515" height="491"/>
    <path fill="rgb(35,31,32)" stroke="none" d="M 6970,19337 C 6872,19337 6775,19263 6775,19133 L 6775,18928 6712,18928 6712,18848 6918,18848 6918,18928 6855,18928 6855,19129 C 6855,19196 6904,19252 6970,19252 7035,19252 7083,19196 7083,19129 L 7083,18928 7020,18928 7020,18848 7226,18848 7226,18928 7163,18928 7163,19133 C 7163,19266 7067,19337 6970,19337 Z"/>
   </g>
  </g>
  <g class="com.sun.star.drawing.PolyPolygonShape">
   <g id="id9">
    <rect class="BoundingBox" stroke="none" fill="none" x="7299" y="18848" width="405" height="476"/>
    <path fill="rgb(35,31,32)" stroke="none" d="M 7444,19107 L 7444,19243 7621,19243 7621,19161 7703,19161 7703,19323 7299,19323 7299,19243 7361,19243 7361,18928 7299,18928 7299,18848 7703,18848 7703,18990 7621,18990 7621,18928 7444,18928 7444,19031 7580,19031 7580,19107 7444,19107 Z"/>
   </g>
  </g>
  <g class="com.sun.star.drawing.PolyPolygonShape">
   <g id="id10">
    <rect class="BoundingBox" stroke="none" fill="none" x="4712" y="18848" width="383" height="476"/>
    <path fill="rgb(35,31,32)" stroke="none" d="M 4921,18848 L 4712,18848 4712,18928 4775,18928 4775,19243 4712,19243 4712,19323 5094,19323 5094,19161 5012,19161 5012,19243 4857,19243 4857,18928 4921,18928 4921,18848 Z"/>
   </g>
  </g>
  <g class="com.sun.star.drawing.ClosedBezierShape">
   <g id="id11">
    <rect class="BoundingBox" stroke="none" fill="none" x="3332" y="18633" width="813" height="921"/>
    <path fill="rgb(35,31,32)" stroke="none" d="M 3332,19080 L 3332,19059 3649,19059 3649,18633 4144,18633 4144,19080 C 4144,19104 4144,19127 4143,19150 4143,19174 4141,19197 4137,19220 4129,19266 4113,19310 4089,19350 4017,19476 3883,19553 3738,19552 3593,19553 3459,19476 3387,19350 3363,19310 3347,19266 3339,19220 3335,19197 3333,19174 3333,19150 3332,19127 3332,19104 3332,19080 Z"/>
   </g>
  </g>
  <g class="com.sun.star.drawing.ClosedBezierShape">
   <g id="id12">
    <rect class="BoundingBox" stroke="none" fill="none" x="3613" y="19021" width="251" height="251"/>
    <path fill="rgb(255,255,255)" stroke="none" d="M 3737,19021 C 3669,19021 3613,19077 3613,19146 3613,19215 3669,19271 3737,19271 3806,19271 3863,19215 3863,19146 3863,19077 3806,19021 3737,19021 Z"/>
   </g>
  </g>
  <g class="com.sun.star.drawing.ClosedBezierShape">
   <g id="id13">
    <rect class="BoundingBox" stroke="none" fill="none" x="3789" y="19197" width="252" height="251"/>
    <path fill="rgb(237,28,36)" stroke="none" d="M 4040,19323 C 4040,19392 3984,19447 3915,19447 3845,19447 3789,19392 3789,19323 3789,19253 3845,19197 3915,19197 3984,19197 4040,19253 4040,19323 Z"/>
   </g>
  </g>
 </g>
</svg>