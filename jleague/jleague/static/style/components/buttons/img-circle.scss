.btn-img-circle {
  position: relative;
  background: #1a1919;
  display: block;

  & > img {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: auto;
    max-height: 60%;
  }

  &:not(:hover) {
    & > img:nth-child(1) {
    }

    & > img:nth-child(2) {
      display: none;
    }
  }

  &:hover {
    background: #ffffff;
    border: 1px solid #1a1919;
    opacity: 1;

    & > img:nth-child(1) {
      display: none;
    }

    & > img:nth-child(2) {
    }

    & > svg path {
      stroke: #1a1919;
    }
  }

  &.medium {
    width: 40px;
    height: 40px;
    border-radius: 20px;
  }
}
