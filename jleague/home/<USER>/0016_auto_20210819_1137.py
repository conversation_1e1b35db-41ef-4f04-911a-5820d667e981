# Generated by Django 3.1.13 on 2021-08-19 15:37

from django.db import migrations, models
import django.db.models.deletion
import modelcluster.fields
import wagtail.fields


class Migration(migrations.Migration):
    dependencies = [
        ('data_stadium', '0027_auto_20210819_1137'),
        ('wagtailcore', '0060_fix_workflow_unique_constraint'),
        ('wagtailimages', '0023_add_choose_permissions'),
        ('home', '0015_auto_20210427_1835'),
    ]

    operations = [
        migrations.AddField(
            model_name='photoitem',
            name='description',
            field=wagtail.fields.RichTextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='photoitem',
            name='date',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Date'),
        ),
        migrations.CreateModel(
            name='StadiumInfo',
            fields=[
                (
                    'page_ptr',
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to='wagtailcore.page',
                    ),
                ),
                ('city', models.CharField(max_length=255, null=True)),
                ('address', models.TextField(null=True)),
                ('introduction', wagtail.fields.RichTextField(null=True)),
                (
                    'featured_photo',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='featured_photo',
                        to='wagtailimages.image',
                    ),
                ),
                (
                    'photos',
                    modelcluster.fields.ParentalManyToManyField(
                        blank=True, related_name='photos', to='wagtailimages.Image'
                    ),
                ),
                (
                    'skeleton',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='skeleton',
                        to='wagtailimages.image',
                    ),
                ),
                (
                    'stadium',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='stadium',
                        to='data_stadium.stadium',
                    ),
                ),
            ],
            options={
                'abstract': False,
            },
            bases=('wagtailcore.page',),
        ),
    ]
