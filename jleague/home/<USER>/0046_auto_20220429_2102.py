# Generated by Django 3.1.13 on 2022-04-29 12:02

from django.db import migrations


def clean_child_pages(apps, schema_editor):
    Page = apps.get_model('wagtailcore', 'Page')
    PageRevision = apps.get_model('wagtailcore', 'PageRevision')

    # fetch child pages
    content_type_ids = (
        62,  # StadiumInfo
        63,  # TeamInfo
        64,  # MascotItem
    )
    pages = Page.objects.filter(content_type_id__in=content_type_ids)
    # delete child pages revision
    PageRevision.objects.filter(page__in=pages).delete()
    # delete child pages
    pages.delete()


def clean_parent_pages(apps, schema_editor):
    Page = apps.get_model('wagtailcore', 'Page')
    PageRevision = apps.get_model('wagtailcore', 'PageRevision')

    # fetch parent pages
    page_ids = (
        2901,  # /home/<USER>/
        2902,  # /home/<USER>/
        3054,  # /home/<USER>/
    )
    pages = Page.objects.filter(id__in=page_ids)
    # delete parent page revision
    PageRevision.objects.filter(page__in=pages).delete()
    # delete parent page
    pages.delete()


class Migration(migrations.Migration):
    dependencies = [
        ('wagtailforms', '0004_add_verbose_name_plural'),
        ('wagtailredirects', '0006_redirect_increase_max_length'),
        ('wagtailcore', '0066_collection_management_permissions'),
        ('home', '0045_delete_photoitem'),
        ('mascots', '0002_auto_20220429_1457'),
        ('stadiums', '0002_auto_20220429_1457'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='stadiuminfo',
            name='featured_photo',
        ),
        migrations.RemoveField(
            model_name='stadiuminfo',
            name='page_ptr',
        ),
        migrations.RemoveField(
            model_name='stadiuminfo',
            name='photos',
        ),
        migrations.RemoveField(
            model_name='stadiuminfo',
            name='skeleton',
        ),
        migrations.RemoveField(
            model_name='stadiuminfo',
            name='stadium',
        ),
        migrations.RemoveField(
            model_name='teaminfo',
            name='mascotes',
        ),
        migrations.RemoveField(
            model_name='teaminfo',
            name='page_ptr',
        ),
        migrations.RemoveField(
            model_name='teaminfo',
            name='stadiums',
        ),
        migrations.RemoveField(
            model_name='teaminfo',
            name='team',
        ),
        migrations.DeleteModel(
            name='MascotItem',
        ),
        migrations.DeleteModel(
            name='StadiumInfo',
        ),
        migrations.DeleteModel(
            name='TeamInfo',
        ),
        migrations.RunPython(clean_child_pages),
        migrations.RunPython(clean_parent_pages),
    ]
