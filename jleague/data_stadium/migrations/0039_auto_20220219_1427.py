# Generated by Django 3.1.13 on 2022-02-19 05:27

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ('data_stadium', '0038_auto_20220210_1754'),
    ]

    operations = [
        migrations.AlterField(
            model_name='gamekindteamstats',
            name='ck_value',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gamekindteamstats',
            name='draw_games_count',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gamekindteamstats',
            name='fkd_value',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gamekindteamstats',
            name='fki_value',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gamekindteamstats',
            name='games_count',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='gamekindteamstats',
            name='goals_missed',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gamekindteamstats',
            name='goals_scored',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gamekindteamstats',
            name='lost_games_count',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gamekindteamstats',
            name='pk_value',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gamekindteamstats',
            name='points',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gamekindteamstats',
            name='red_cards',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gamekindteamstats',
            name='shots_count',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gamekindteamstats',
            name='won_games_count',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='gamekindteamstats',
            name='yellow_cards',
            field=models.IntegerField(blank=True, null=True),
        ),
    ]
