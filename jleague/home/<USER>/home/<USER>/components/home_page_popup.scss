.template-homepage {
  .home-popup {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin: 3rem auto 0;
  }

  .home-popup__header {
    flex: 0.2;
    margin-bottom: 2rem;

    .home-popup__header__title {
      color: var(--color-white);
      font-family: var(--font-overpass);
      font-style: normal;
      font-weight: bold;
      font-size: 42px;
      line-height: 48px;
      text-align: center;
    }
  }

  .home-popup__content {
    flex: 0.8;
    overflow: hidden;
    animation: fadeIn 1s;
    -webkit-animation: fadeIn 1s;
    -moz-animation: fadeIn 1s;
    -o-animation: fadeIn 1s;
    -ms-animation: fadeIn 1s;

    .home-popup__content__image {
      display: block;
      height: auto;
      width: 100%;
      max-width: 100%;
      max-height: 100%;
    }

    .home-popup__content__divider {
      height: 1px;
      width: 100%;
      background-color: rgba(255, 255, 255, 0.3);
      margin: 3rem 0;
    }
  }

  @keyframes fadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  @-moz-keyframes fadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  @-webkit-keyframes fadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  @-o-keyframes fadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  @-ms-keyframes fadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  @media screen and (min-width: 1440px) {
    .home-popup {
      max-width: 1440px;
      padding: 0 120px;
    }
  }

  @media screen and (max-width: 1439px) and (min-width: 1121px) {
    .home-popup {
      max-width: 1120px;
      padding: 0 24px;
    }
  }

  @media screen and (max-width: 1120px) and (min-width: 769px) {
    .home-popup {
      margin: 0 48px;
    }
  }

  @media screen and (max-width: 768px) {
    .home-popup {
      padding: 0 24px;
    }

    .home-popup__header__title {
      font-size: 24px;
      line-height: 32px;
    }
  }
}
