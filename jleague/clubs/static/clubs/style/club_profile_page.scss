@import 'setup/variable';
@import 'setup/mixin';

.body-club-profile {
  header {
    overflow: unset;

    .team-bg-img {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-attachment: fixed;
    }

    .team-bg-gradient {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 252px;
    }

    @media screen and (min-width: 1200px) {
      padding-bottom: 174px;
    }

    @media screen and (max-width: 1199px) {
      padding-bottom: 133px;
    }

    @media screen and (max-width: 767px) {
      padding-bottom: 117px;
    }
  }

  .club-profile {
    position: relative;

    @media screen and (min-width: 1200px) {
      padding-top: 80px;
    }

    @media screen and (max-width: 1199px) {
      padding-top: 160px;
    }

    @media screen and (max-width: 576px) {
      padding-top: 130px;
    }

    .club-profile-nav-sticky-checker {
      height: 1px;
      width: 100%;
      margin-bottom: 80px;

      @media screen and (max-width: 1199px) {
        margin-bottom: 40px;
      }

      @media screen and (max-width: 767px) {
        margin-bottom: 32px;
      }

      &.active {
        margin-bottom: 122px;

        @media screen and (max-width: 1199px) {
          margin-bottom: 106px;
        }

        @media screen and (max-width: 767px) {
          margin-bottom: 82px;
        }
      }
    }

    .club-profile-nav {
      display: flex;
      justify-content: center;

      @media screen and (max-width: 576px) {
        justify-content: flex-start;
        overflow-x: auto;
        @include scrollbars();
      }

      .btn-nav {
        & > img {
          width: auto;
          height: 32px;

          @media screen and (max-width: 767px) {
            height: 24px;
          }
        }
      }

      @keyframes anim-sticky-nav {
        from {
          opacity: 0;
          left: 50%;
          width: 0%;
        }

        to {
          opacity: 1;
          left: 0;
          width: 100%;
        }
      }

      &.sticky {
        position: fixed;
        top: 0;
        margin: 0;
        width: 100%;
        max-width: unset;
        padding: 0;
        background: $color-white;
        z-index: 10;
        display: flex;
        justify-content: flex-start;
        animation-name: anim-sticky-nav;
        animation-duration: 0.5s;

        .btn-nav {
          flex: 1 1;
        }
      }
    }
  }

  .club-profile-popup-video {
    .mfp-content {
      aspect-ratio: 16/9;
      max-width: 85%;
    }
  }

  .social-networks-area {
    margin-top: 0;
  }
}

@import 'components/club_profile_match_stats';
@import 'components/club_profile_videos';
@import 'components/club_profile_players';
@import 'components/club_profile_statistics';
@import 'components/club_profile_bio';
@import 'components/club_profile_stadiums';
@import 'components/club_profile_mascots';
@import 'components/club_selector';
