@import 'setup/variable';

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.floating-buttons {
  position: fixed;
  bottom: 72px;
  right: 24px;
  z-index: 999;
  display: flex;
  flex-direction: column;
  gap: 24px;
  transition: visibility 0.1s ease-in-out, opacity 0.2s linear;
  opacity: 0;
  visibility: hidden;

  .floating-button {
    border-radius: 50%;
    height: 80px;
    width: 80px;
    background-color: $color-white;
    display: flex;
    align-items: center;
    justify-content: center;
    filter: drop-shadow(0px 12px 40px rgba(16, 24, 64, 0.24));
    border: none;
    position: relative;

    .floating-button__icon {
      height: 40px;
      width: 40px;

      &::before {
        content: '';
        transition: all 0.25s ease-in-out;
        display: block;
        width: 100%;
        height: 100%;
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
      }
    }

    .floating-button__tooltip,
    .floating-button__label {
      position: absolute;
      right: 80px;
      font-family: $font-barlow-condensed;
      font-style: normal;
      font-weight: 700;
      font-size: 14px;
      line-height: 16px;
      text-align: center;
      letter-spacing: 1.66667px;
      text-transform: uppercase;
      white-space: nowrap;
      color: $color-black;
      background: $color-white;
      padding: 7px 16px 8px;
      border-radius: 16px;
      opacity: 0;
      visibility: hidden;
      transition: all 0.25s ease-in;
    }

    .floating-button__tooltip {
      right: 96px;
    }

    &.floating-button--tooltip {
      .floating-button__tooltip {
        visibility: visible;
        opacity: 1;
      }
    }


    &.floating-button--tickets {
      scroll-behavior: smooth;


      .floating-button__icon {
        animation: pulse 2s infinite;
        &::before {
          background-image: url('/static/images/icons/ticket-1A1919.svg');
        }
      }
    }

    &.floating-button--install {
      .floating-button__icon {
        &::before {
          background-image: url('/static/images/icons/download-1A1919.svg');
        }
      }
    }

    &:hover:not(.floating-button--tooltip) {
      cursor: pointer;
      opacity: 1;

      .floating-button__label {
        visibility: visible;
        opacity: 1;
        right: 96px;
      }


      &.floating-button--tickets {
        .floating-button__icon {
          &::before {
            background-image: url('/static/images/icons/ticket-EC1D24.svg');
          }
        }
      }

    }
  }

  @media screen and (max-width: 767px) {
    .floating-button {
      height: 60px;
      width: 60px;

      .floating-button__icon {
        height: 30px;
        width: 30px;
      }

      &.floating-button--backtotop {
        .floating-button__icon {
          height: 36px;
          width: 36px;
        }
      }
    }
  }
}
