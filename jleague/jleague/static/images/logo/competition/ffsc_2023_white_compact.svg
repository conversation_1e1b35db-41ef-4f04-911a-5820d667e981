<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 26.3.1, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 84 80" style="enable-background:new 0 0 84 80;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#FFFFFF;}
	.st1{clip-path:url(#SVGID_00000060020874070842896550000009454307449818987708_);}
	.st2{fill:none;stroke:#BAA700;stroke-width:0.1416;stroke-miterlimit:10;}
	.st3{fill:none;stroke:#BAA701;stroke-width:0.1416;stroke-miterlimit:10;}
	.st4{fill:none;stroke:#BAA802;stroke-width:0.1416;stroke-miterlimit:10;}
	.st5{fill:none;stroke:#BBA803;stroke-width:0.1416;stroke-miterlimit:10;}
	.st6{fill:none;stroke:#BBA804;stroke-width:0.1416;stroke-miterlimit:10;}
	.st7{fill:none;stroke:#BBA805;stroke-width:0.1416;stroke-miterlimit:10;}
	.st8{fill:none;stroke:#BBA906;stroke-width:0.1416;stroke-miterlimit:10;}
	.st9{fill:none;stroke:#BCA907;stroke-width:0.1416;stroke-miterlimit:10;}
	.st10{fill:none;stroke:#BCA908;stroke-width:0.1416;stroke-miterlimit:10;}
	.st11{fill:none;stroke:#BCAA09;stroke-width:0.1416;stroke-miterlimit:10;}
	.st12{fill:none;stroke:#BCAA0A;stroke-width:0.1416;stroke-miterlimit:10;}
	.st13{fill:none;stroke:#BDAA0B;stroke-width:0.1416;stroke-miterlimit:10;}
	.st14{fill:none;stroke:#BDAB0C;stroke-width:0.1416;stroke-miterlimit:10;}
	.st15{fill:none;stroke:#BDAB0D;stroke-width:0.1416;stroke-miterlimit:10;}
	.st16{fill:none;stroke:#BDAB0E;stroke-width:0.1416;stroke-miterlimit:10;}
	.st17{fill:none;stroke:#BEAB0F;stroke-width:0.1416;stroke-miterlimit:10;}
	.st18{fill:none;stroke:#BEAC10;stroke-width:0.1416;stroke-miterlimit:10;}
	.st19{fill:none;stroke:#BEAC11;stroke-width:0.1416;stroke-miterlimit:10;}
	.st20{fill:none;stroke:#BEAC12;stroke-width:0.1416;stroke-miterlimit:10;}
	.st21{fill:none;stroke:#BFAD13;stroke-width:0.1416;stroke-miterlimit:10;}
	.st22{fill:none;stroke:#BFAD14;stroke-width:0.1416;stroke-miterlimit:10;}
	.st23{fill:none;stroke:#BFAD15;stroke-width:0.1416;stroke-miterlimit:10;}
	.st24{fill:none;stroke:#BFAE16;stroke-width:0.1416;stroke-miterlimit:10;}
	.st25{fill:none;stroke:#C0AE17;stroke-width:0.1416;stroke-miterlimit:10;}
	.st26{fill:none;stroke:#C0AE18;stroke-width:0.1416;stroke-miterlimit:10;}
	.st27{fill:none;stroke:#C0AE19;stroke-width:0.1416;stroke-miterlimit:10;}
	.st28{fill:none;stroke:#C0AF1A;stroke-width:0.1416;stroke-miterlimit:10;}
	.st29{fill:none;stroke:#C1AF1B;stroke-width:0.1416;stroke-miterlimit:10;}
	.st30{fill:none;stroke:#C1AF1C;stroke-width:0.1416;stroke-miterlimit:10;}
	.st31{fill:none;stroke:#C1B01D;stroke-width:0.1416;stroke-miterlimit:10;}
	.st32{fill:none;stroke:#C1B01E;stroke-width:0.1416;stroke-miterlimit:10;}
	.st33{fill:none;stroke:#C2B01F;stroke-width:0.1416;stroke-miterlimit:10;}
	.st34{fill:none;stroke:#C2B120;stroke-width:0.1416;stroke-miterlimit:10;}
	.st35{fill:none;stroke:#C2B121;stroke-width:0.1416;stroke-miterlimit:10;}
	.st36{fill:none;stroke:#C2B122;stroke-width:0.1416;stroke-miterlimit:10;}
	.st37{fill:none;stroke:#C3B123;stroke-width:0.1416;stroke-miterlimit:10;}
	.st38{fill:none;stroke:#C3B224;stroke-width:0.1416;stroke-miterlimit:10;}
	.st39{fill:none;stroke:#C3B225;stroke-width:0.1416;stroke-miterlimit:10;}
	.st40{fill:none;stroke:#C3B226;stroke-width:0.1416;stroke-miterlimit:10;}
	.st41{fill:none;stroke:#C4B327;stroke-width:0.1416;stroke-miterlimit:10;}
	.st42{fill:none;stroke:#C4B328;stroke-width:0.1416;stroke-miterlimit:10;}
	.st43{fill:none;stroke:#C4B329;stroke-width:0.1416;stroke-miterlimit:10;}
	.st44{fill:none;stroke:#C4B32A;stroke-width:0.1416;stroke-miterlimit:10;}
	.st45{fill:none;stroke:#C5B42B;stroke-width:0.1416;stroke-miterlimit:10;}
	.st46{fill:none;stroke:#C5B42C;stroke-width:0.1416;stroke-miterlimit:10;}
	.st47{fill:none;stroke:#C5B42D;stroke-width:0.1416;stroke-miterlimit:10;}
	.st48{fill:none;stroke:#C5B52E;stroke-width:0.1416;stroke-miterlimit:10;}
	.st49{fill:none;stroke:#C6B52F;stroke-width:0.1416;stroke-miterlimit:10;}
	.st50{fill:none;stroke:#C6B530;stroke-width:0.1416;stroke-miterlimit:10;}
	.st51{fill:none;stroke:#C6B631;stroke-width:0.1416;stroke-miterlimit:10;}
	.st52{fill:none;stroke:#C6B632;stroke-width:0.1416;stroke-miterlimit:10;}
	.st53{fill:none;stroke:#C7B633;stroke-width:0.1416;stroke-miterlimit:10;}
	.st54{fill:none;stroke:#C7B634;stroke-width:0.1416;stroke-miterlimit:10;}
	.st55{fill:none;stroke:#C7B735;stroke-width:0.1416;stroke-miterlimit:10;}
	.st56{fill:none;stroke:#C7B736;stroke-width:0.1416;stroke-miterlimit:10;}
	.st57{fill:none;stroke:#C8B737;stroke-width:0.1416;stroke-miterlimit:10;}
	.st58{fill:none;stroke:#C8B838;stroke-width:0.1416;stroke-miterlimit:10;}
	.st59{fill:none;stroke:#C8B839;stroke-width:0.1416;stroke-miterlimit:10;}
	.st60{fill:none;stroke:#C8B83A;stroke-width:0.1416;stroke-miterlimit:10;}
	.st61{fill:none;stroke:#C9B93B;stroke-width:0.1416;stroke-miterlimit:10;}
	.st62{fill:none;stroke:#C9B93C;stroke-width:0.1416;stroke-miterlimit:10;}
	.st63{fill:none;stroke:#C9B93D;stroke-width:0.1416;stroke-miterlimit:10;}
	.st64{fill:none;stroke:#C9B93E;stroke-width:0.1416;stroke-miterlimit:10;}
	.st65{fill:none;stroke:#CABA3F;stroke-width:0.1416;stroke-miterlimit:10;}
	.st66{fill:none;stroke:#CABA40;stroke-width:0.1416;stroke-miterlimit:10;}
	.st67{fill:none;stroke:#CABA41;stroke-width:0.1416;stroke-miterlimit:10;}
	.st68{fill:none;stroke:#CABB42;stroke-width:0.1416;stroke-miterlimit:10;}
	.st69{fill:none;stroke:#CBBB43;stroke-width:0.1416;stroke-miterlimit:10;}
	.st70{fill:none;stroke:#CBBB44;stroke-width:0.1416;stroke-miterlimit:10;}
	.st71{fill:none;stroke:#CBBC45;stroke-width:0.1416;stroke-miterlimit:10;}
	.st72{fill:none;stroke:#CBBC46;stroke-width:0.1416;stroke-miterlimit:10;}
	.st73{fill:none;stroke:#CBBC47;stroke-width:0.1416;stroke-miterlimit:10;}
	.st74{fill:none;stroke:#CCBC48;stroke-width:0.1416;stroke-miterlimit:10;}
	.st75{fill:none;stroke:#CCBD49;stroke-width:0.1416;stroke-miterlimit:10;}
	.st76{fill:none;stroke:#CCBD4A;stroke-width:0.1416;stroke-miterlimit:10;}
	.st77{fill:none;stroke:#CCBD4B;stroke-width:0.1416;stroke-miterlimit:10;}
	.st78{fill:none;stroke:#CDBE4C;stroke-width:0.1416;stroke-miterlimit:10;}
	.st79{fill:none;stroke:#CDBE4D;stroke-width:0.1416;stroke-miterlimit:10;}
	.st80{fill:none;stroke:#CDBE4E;stroke-width:0.1416;stroke-miterlimit:10;}
	.st81{fill:none;stroke:#CDBE4F;stroke-width:0.1416;stroke-miterlimit:10;}
	.st82{fill:none;stroke:#CEBF50;stroke-width:0.1416;stroke-miterlimit:10;}
	.st83{fill:none;stroke:#CEBF51;stroke-width:0.1416;stroke-miterlimit:10;}
	.st84{fill:none;stroke:#CEBF52;stroke-width:0.1416;stroke-miterlimit:10;}
	.st85{fill:none;stroke:#CEC053;stroke-width:0.1416;stroke-miterlimit:10;}
	.st86{fill:none;stroke:#CFC054;stroke-width:0.1416;stroke-miterlimit:10;}
	.st87{fill:none;stroke:#CFC055;stroke-width:0.1416;stroke-miterlimit:10;}
	.st88{fill:none;stroke:#CFC156;stroke-width:0.1416;stroke-miterlimit:10;}
	.st89{fill:none;stroke:#CFC157;stroke-width:0.1416;stroke-miterlimit:10;}
	.st90{fill:none;stroke:#D0C158;stroke-width:0.1416;stroke-miterlimit:10;}
	.st91{fill:none;stroke:#D0C159;stroke-width:0.1416;stroke-miterlimit:10;}
	.st92{fill:none;stroke:#D0C25A;stroke-width:0.1416;stroke-miterlimit:10;}
	.st93{fill:none;stroke:#D0C25B;stroke-width:0.1416;stroke-miterlimit:10;}
	.st94{fill:none;stroke:#D1C25C;stroke-width:0.1416;stroke-miterlimit:10;}
	.st95{fill:none;stroke:#D1C35D;stroke-width:0.1416;stroke-miterlimit:10;}
	.st96{fill:none;stroke:#D1C35E;stroke-width:0.1416;stroke-miterlimit:10;}
	.st97{fill:none;stroke:#D1C35F;stroke-width:0.1416;stroke-miterlimit:10;}
	.st98{fill:none;stroke:#D2C460;stroke-width:0.1416;stroke-miterlimit:10;}
	.st99{fill:none;stroke:#D2C461;stroke-width:0.1416;stroke-miterlimit:10;}
	.st100{fill:none;stroke:#D2C462;stroke-width:0.1416;stroke-miterlimit:10;}
	.st101{fill:none;stroke:#D2C463;stroke-width:0.1416;stroke-miterlimit:10;}
	.st102{fill:none;stroke:#D3C564;stroke-width:0.1416;stroke-miterlimit:10;}
	.st103{fill:none;stroke:#D3C565;stroke-width:0.1416;stroke-miterlimit:10;}
	.st104{fill:none;stroke:#D3C566;stroke-width:0.1416;stroke-miterlimit:10;}
	.st105{fill:none;stroke:#D3C667;stroke-width:0.1416;stroke-miterlimit:10;}
	.st106{fill:none;stroke:#D4C668;stroke-width:0.1416;stroke-miterlimit:10;}
	.st107{fill:none;stroke:#D4C669;stroke-width:0.1416;stroke-miterlimit:10;}
	.st108{fill:none;stroke:#D4C66A;stroke-width:0.1416;stroke-miterlimit:10;}
	.st109{fill:none;stroke:#D4C76B;stroke-width:0.1416;stroke-miterlimit:10;}
	.st110{fill:none;stroke:#D5C76C;stroke-width:0.1416;stroke-miterlimit:10;}
	.st111{fill:none;stroke:#D5C76D;stroke-width:0.1416;stroke-miterlimit:10;}
	.st112{fill:none;stroke:#D5C86E;stroke-width:0.1416;stroke-miterlimit:10;}
	.st113{fill:none;stroke:#D5C86F;stroke-width:0.1416;stroke-miterlimit:10;}
	.st114{fill:none;stroke:#D6C870;stroke-width:0.1416;stroke-miterlimit:10;}
	.st115{fill:none;stroke:#D6C971;stroke-width:0.1416;stroke-miterlimit:10;}
	.st116{fill:none;stroke:#D6C972;stroke-width:0.1416;stroke-miterlimit:10;}
	.st117{fill:none;stroke:#D6C973;stroke-width:0.1416;stroke-miterlimit:10;}
	.st118{fill:none;stroke:#D7C974;stroke-width:0.1416;stroke-miterlimit:10;}
	.st119{fill:none;stroke:#D7CA75;stroke-width:0.1416;stroke-miterlimit:10;}
	.st120{fill:none;stroke:#D7CA76;stroke-width:0.1416;stroke-miterlimit:10;}
	.st121{fill:none;stroke:#D7CA77;stroke-width:0.1416;stroke-miterlimit:10;}
	.st122{fill:none;stroke:#D8CB78;stroke-width:0.1416;stroke-miterlimit:10;}
	.st123{fill:none;stroke:#D8CB79;stroke-width:0.1416;stroke-miterlimit:10;}
	.st124{fill:none;stroke:#D8CB7A;stroke-width:0.1416;stroke-miterlimit:10;}
	.st125{fill:none;stroke:#D8CC7B;stroke-width:0.1416;stroke-miterlimit:10;}
	.st126{fill:none;stroke:#D9CC7C;stroke-width:0.1416;stroke-miterlimit:10;}
	.st127{fill:none;stroke:#D9CC7D;stroke-width:0.1416;stroke-miterlimit:10;}
	.st128{fill:none;stroke:#D9CC7E;stroke-width:0.1416;stroke-miterlimit:10;}
	.st129{fill:none;stroke:#D9CD7F;stroke-width:0.1416;stroke-miterlimit:10;}
	.st130{fill:none;stroke:#DACD80;stroke-width:0.1416;stroke-miterlimit:10;}
	.st131{fill:none;stroke:#DACD81;stroke-width:0.1416;stroke-miterlimit:10;}
	.st132{fill:none;stroke:#DACE82;stroke-width:0.1416;stroke-miterlimit:10;}
	.st133{fill:none;stroke:#DACE83;stroke-width:0.1416;stroke-miterlimit:10;}
	.st134{fill:none;stroke:#DBCE84;stroke-width:0.1416;stroke-miterlimit:10;}
	.st135{fill:none;stroke:#DBCF85;stroke-width:0.1416;stroke-miterlimit:10;}
	.st136{fill:none;stroke:#DBCF86;stroke-width:0.1416;stroke-miterlimit:10;}
	.st137{fill:none;stroke:#DBCF87;stroke-width:0.1416;stroke-miterlimit:10;}
	.st138{fill:none;stroke:#DCCF88;stroke-width:0.1416;stroke-miterlimit:10;}
	.st139{fill:none;stroke:#DCD089;stroke-width:0.1416;stroke-miterlimit:10;}
	.st140{fill:none;stroke:#DCD08A;stroke-width:0.1416;stroke-miterlimit:10;}
	.st141{fill:none;stroke:#CBBB45;stroke-width:0.1416;stroke-miterlimit:10;}
	.st142{clip-path:url(#SVGID_00000121264409516205865790000007810521617388902828_);}
	.st143{fill:#504100;}
	
		.st144{clip-path:url(#SVGID_00000150781427748027579540000014728177118393040513_);fill:url(#SVGID_00000183207235743290904070000010602853134485991311_);}
	
		.st145{clip-path:url(#SVGID_00000011749355988736327940000007297440352259504776_);fill:url(#SVGID_00000114072396848107818470000016784849054667051927_);}
	
		.st146{clip-path:url(#SVGID_00000051375880781352742860000018087121930727831719_);fill:url(#SVGID_00000069373916398729270150000004403149386517206677_);}
	
		.st147{clip-path:url(#SVGID_00000140698297745683693200000010214276969406735245_);fill:url(#SVGID_00000067939341113084592170000012379510552221927320_);}
	
		.st148{clip-path:url(#SVGID_00000081609317691676709930000012321849727639925950_);fill:url(#SVGID_00000042714114169291842750000000304524235683306932_);}
	
		.st149{clip-path:url(#SVGID_00000078042236828660593690000004288544800393884809_);fill:url(#SVGID_00000121973700186463860250000011062205200091428537_);}
	
		.st150{clip-path:url(#SVGID_00000053509902740827868200000010336617279448755903_);fill:url(#SVGID_00000052816971065633683650000009538357323109097631_);}
	
		.st151{clip-path:url(#SVGID_00000113315069748893185640000000219861358329673401_);fill:url(#SVGID_00000106106438126186432710000010699646976740192182_);}
	
		.st152{clip-path:url(#SVGID_00000147214119892592927070000005644024654378787997_);fill:url(#SVGID_00000085932723775785027250000006648722903379240869_);}
	
		.st153{clip-path:url(#SVGID_00000177484283788252787350000017818176549298439827_);fill:url(#SVGID_00000171707241316745807460000005198974226281624473_);}
	
		.st154{clip-path:url(#SVGID_00000101072494438816117690000008987188095130143904_);fill:url(#SVGID_00000114046306495838746780000017523696760224617651_);}
	
		.st155{clip-path:url(#SVGID_00000116193854678793964870000007935354124701115537_);fill:url(#SVGID_00000126300603777150053070000008354851677616195978_);}
	
		.st156{clip-path:url(#SVGID_00000060011088831994640890000010602135607463465139_);fill:url(#SVGID_00000144333254006658075290000009395953318076113024_);}
	
		.st157{clip-path:url(#SVGID_00000122002015650928117460000004063300240773051310_);fill:url(#SVGID_00000022531720257335517130000010028707209848831406_);}
	
		.st158{clip-path:url(#SVGID_00000052087662900766303190000006604505909161216412_);fill:url(#SVGID_00000069369222458723177880000014517295425694752387_);}
	
		.st159{clip-path:url(#SVGID_00000044891983656857112600000012674572715420622979_);fill:url(#SVGID_00000089567311806873389260000008618626620804125887_);}
	
		.st160{clip-path:url(#SVGID_00000160192667759849901870000016507913731428316597_);fill:url(#SVGID_00000178909228635974830040000008294306345148904590_);}
	
		.st161{clip-path:url(#SVGID_00000052803811065023929250000007746932902712008630_);fill:url(#SVGID_00000132051016485847332270000009159482584537482402_);}
	
		.st162{clip-path:url(#SVGID_00000033354034119047196090000003031640898044856505_);fill:url(#SVGID_00000122683842612597318980000013728466882377451454_);}
	
		.st163{clip-path:url(#SVGID_00000121256899331620501240000017386865134645584823_);fill:url(#SVGID_00000076583107881050976360000005110667126445477820_);}
	
		.st164{clip-path:url(#SVGID_00000028321981797873690810000010595958108151590832_);fill:url(#SVGID_00000128468639357764896120000013764296279086000042_);}
	
		.st165{clip-path:url(#SVGID_00000131363609646923404830000017198029476971342994_);fill:url(#SVGID_00000026145754954846868540000006078990412814014626_);}
	
		.st166{clip-path:url(#SVGID_00000074428852363634794900000008413186584415259814_);fill:url(#SVGID_00000024709394190735238750000012336518193261653915_);}
	
		.st167{clip-path:url(#SVGID_00000046320552686716442360000011916273399695779259_);fill:url(#SVGID_00000058557571457153452790000004181139121786354096_);}
	
		.st168{clip-path:url(#SVGID_00000004520978794187556590000002754375950609467032_);fill:url(#SVGID_00000035531945305262585330000001479772169789235882_);}
	
		.st169{clip-path:url(#SVGID_00000173122750089498441960000008057100191918809268_);fill:url(#SVGID_00000013168200759475639770000009501951241567200130_);}
	
		.st170{clip-path:url(#SVGID_00000057838699906807276990000001188877091912782510_);fill:url(#SVGID_00000129169225351488415960000017884893452027264938_);}
	
		.st171{clip-path:url(#SVGID_00000169558246556430786540000013135704515373365683_);fill:url(#SVGID_00000037685164068050660410000014406342230012411058_);}
	
		.st172{clip-path:url(#SVGID_00000150805449315293873960000017207917705635185560_);fill:url(#SVGID_00000001656598565981651090000006213936652785334198_);}
	
		.st173{clip-path:url(#SVGID_00000145050963094531341790000002030305375821096577_);fill:url(#SVGID_00000158024587071133563840000015556072853104010120_);}
	
		.st174{clip-path:url(#SVGID_00000038406598976530761980000007217207220832469920_);fill:url(#SVGID_00000108999062148022846730000013430416264490850207_);}
	
		.st175{clip-path:url(#SVGID_00000101786871364398137610000003337715933334339973_);fill:url(#SVGID_00000079476966854694786210000017204595577505917576_);}
	
		.st176{clip-path:url(#SVGID_00000090282213115185289060000013373696989360867229_);fill:url(#SVGID_00000168812806592533796620000001450446635882465666_);}
	
		.st177{clip-path:url(#SVGID_00000006676981237581918550000007179778839699504778_);fill:url(#SVGID_00000020387013631496100040000011767254796535684759_);}
	
		.st178{clip-path:url(#SVGID_00000139273296483994473540000017091137827510727349_);fill:url(#SVGID_00000085928759437507341280000002491357701600036738_);}
	
		.st179{clip-path:url(#SVGID_00000146479736167235833860000010633168619953638327_);fill:url(#SVGID_00000168088679699162935840000005273347098569837995_);}
	
		.st180{clip-path:url(#SVGID_00000170987179062501149450000018009124612615262607_);fill:url(#SVGID_00000031914240563165943130000002531044134427790988_);}
	
		.st181{clip-path:url(#SVGID_00000128444419094850647840000013014976650264870319_);fill:url(#SVGID_00000152254825725999296490000016963947003222804138_);}
	
		.st182{clip-path:url(#SVGID_00000057854296474931441930000010186939402386130085_);fill:url(#SVGID_00000119111222898740186080000009919712773297265574_);}
	
		.st183{clip-path:url(#SVGID_00000006667503576603816660000009038473067460645813_);fill:url(#SVGID_00000181806617582323226950000006223935017144009360_);}
	
		.st184{clip-path:url(#SVGID_00000158749517690922105480000013632865355919030698_);fill:url(#SVGID_00000019647972491895086790000012646580042559867276_);}
	
		.st185{clip-path:url(#SVGID_00000018217084042189111800000016520067010027485083_);fill:url(#SVGID_00000170270561015022948210000013156699573005067410_);}
	
		.st186{clip-path:url(#SVGID_00000067203265512186910230000004421719562538219654_);fill:url(#SVGID_00000151535153935932052020000013179105813937414277_);}
	
		.st187{clip-path:url(#SVGID_00000043428145536514042230000005291024810687196082_);fill:url(#SVGID_00000119107259092746113350000013790563784550357438_);}
	
		.st188{clip-path:url(#SVGID_00000053545273605740120560000015184751955727989687_);fill:url(#SVGID_00000099635272683259834830000015817115891130114965_);}
	
		.st189{clip-path:url(#SVGID_00000079447729111163923680000016845488516231239045_);fill:url(#SVGID_00000111193463461285092520000001720914227828028304_);}
	
		.st190{clip-path:url(#SVGID_00000030463604725413093300000005806447950564071075_);fill:url(#SVGID_00000025445263303388569680000012624988730203409305_);}
	
		.st191{clip-path:url(#SVGID_00000084502417802386948410000006374572206796773804_);fill:url(#SVGID_00000085214679747469146850000000346606434074651567_);}
	
		.st192{clip-path:url(#SVGID_00000144300830921657439410000014347576106586327972_);fill:url(#SVGID_00000094595267115532563690000004676431888721143741_);}
	
		.st193{clip-path:url(#SVGID_00000075130148616807359680000001820808279441725057_);fill:url(#SVGID_00000167363961625181691190000003209881125904004516_);}
	
		.st194{clip-path:url(#SVGID_00000168809673823733736260000015196071770149977001_);fill:url(#SVGID_00000084506295183431235220000005122248657157879202_);}
	
		.st195{clip-path:url(#SVGID_00000070833999707921274130000012584928585448855996_);fill:url(#SVGID_00000158721238281815942870000014042297872227167375_);}
	
		.st196{clip-path:url(#SVGID_00000132081427861476750830000008604128772189005728_);fill:url(#SVGID_00000113341475011701601290000005284389026552391311_);}
	
		.st197{clip-path:url(#SVGID_00000139985054386106401510000001382079731787281337_);fill:url(#SVGID_00000074408978939734261550000008100425969500942774_);}
	.st198{opacity:0.8;}
	.st199{clip-path:url(#SVGID_00000130629011662217198010000003199771999860206485_);}
	
		.st200{clip-path:url(#SVGID_00000121268729401326043910000006670710215761370787_);fill:url(#SVGID_00000103958337013367690180000004349635402117837964_);}
	.st201{opacity:0.3;}
	.st202{clip-path:url(#SVGID_00000140707405926772932450000009665715683776977033_);}
	
		.st203{clip-path:url(#SVGID_00000170269022683318104880000015883020521300566146_);fill:url(#SVGID_00000061447478051582748600000005842362804890686902_);}
</style>
<g>
	<g>
		<g>
			<polygon class="st0" points="21.2,61.5 26.6,61.5 26.6,62.7 23.2,62.7 23.2,63.8 26.3,63.8 26.3,65 23.2,65 23.2,67 21.2,67 			
				"/>
			<polygon class="st0" points="40.4,61.5 45.8,61.5 45.8,62.7 42.4,62.7 42.4,63.8 45.5,63.8 45.5,65 42.4,65 42.4,67 40.4,67 			
				"/>
			<path class="st0" d="M27.2,61.5h1.9v3.1c0,0.3,0,0.6,0.2,0.8c0.2,0.4,0.7,0.5,1.1,0.5c0.5,0,0.8-0.2,0.9-0.3
				c0.3-0.3,0.3-0.8,0.3-1.1v-3.1h1.9v2.9c0,0.6,0,1.3-0.3,1.8c-0.6,0.9-2,1-2.9,1c-1.2,0-1.9-0.2-2.3-0.4c-0.8-0.4-0.9-1.3-0.9-1.7
				V61.5z"/>
			<path class="st0" d="M35.1,61.5h2v3.8c0,0.4,0,0.9-0.4,1.3c-0.4,0.4-1,0.5-1.5,0.5H34v-1.3h0.4c0.4,0,0.7,0,0.7-0.6V61.5z"/>
			<rect x="37.7" y="61.5" class="st0" width="2" height="5.5"/>
			<rect x="46.4" y="61.5" class="st0" width="2" height="5.5"/>
			<g>
				<g>
					<path class="st0" d="M49.1,61.5h2v4.2h3V67h-4.9V61.5z"/>
					<path class="st0" d="M57.6,67l-1.3-3.8h0V67h-2v-5.5h3.1l0.8,2.6l0.8-2.6h3.1V67h-2v-3.8h0L58.8,67H57.6z"/>
				</g>
			</g>
		</g>
		<g>
			<path class="st0" d="M3,70.1c0,0.1,0,0.1,0.1,0.2c0.1,0.2,0.7,0.3,1,0.4c0.4,0.1,0.7,0.2,1.1,0.2c0.2,0,0.5,0.1,0.7,0.2
				c0.7,0.3,1,0.8,1,1.3c0,0.4-0.1,0.7-0.3,0.9c-0.5,0.7-1.4,1-2.8,1c-0.5,0-1.6,0-2.3-0.6c-0.5-0.4-0.5-0.9-0.6-1.2l1.9-0.1
				c0.1,0.4,0.4,0.6,1.2,0.6c0.2,0,0.5,0,0.7-0.1c0.1-0.1,0.3-0.2,0.3-0.4c0-0.3-0.2-0.4-0.4-0.4c-0.2-0.1-1.5-0.3-1.7-0.4
				c-0.2,0-0.4-0.1-0.6-0.2c-0.2-0.1-1.1-0.4-1.1-1.4c0-0.4,0.2-0.8,0.4-1.1c0.6-0.6,1.7-0.7,2.3-0.7c0.7,0,1.3,0.1,1.7,0.2
				c1,0.3,1.1,1.1,1.2,1.4l-1.9,0.1c0-0.2-0.1-0.5-1-0.5C3.6,69.6,3,69.7,3,70.1"/>
			<path class="st0" d="M7.3,68.7h1.9v3.1c0,0.3,0,0.6,0.2,0.8c0.2,0.4,0.7,0.5,1.1,0.5c0.5,0,0.8-0.2,0.9-0.3
				c0.3-0.3,0.3-0.8,0.3-1.1v-3.1h1.9v2.9c0,0.6,0,1.3-0.3,1.8c-0.6,0.9-2,1-2.9,1c-1.2,0-1.9-0.2-2.3-0.4c-0.8-0.4-0.9-1.3-0.9-1.7
				V68.7z"/>
			<path class="st0" d="M16.4,69.9h1.4c0.2,0,0.3,0,0.5,0.1c0.1,0.1,0.2,0.2,0.2,0.4c0,0.5-0.5,0.5-0.8,0.5h-1.3V69.9z M14.5,74.2
				h1.9v-1.9h1.5c0.9,0,1.7,0,2.2-0.7c0.3-0.3,0.4-0.8,0.4-1.1c0-0.8-0.3-1.2-0.5-1.3c-0.4-0.4-1.1-0.4-1.8-0.4h-3.7V74.2z"/>
			<polygon class="st0" points="26.7,68.7 26.7,69.9 22.9,69.9 22.9,70.8 26.4,70.8 26.4,72 22.9,72 22.9,72.9 26.8,72.9 26.8,74.2 
				21,74.2 21,68.7 			"/>
			<path class="st0" d="M29.4,69.9H31c0.1,0,0.3,0,0.5,0.2c0.1,0.1,0.1,0.2,0.1,0.4c0,0.5-0.5,0.5-0.8,0.5h-1.5V69.9z M27.4,74.2h2
				v-2h1l1.1,2h2.2L32.3,72c0.2-0.1,0.6-0.1,0.9-0.5c0.2-0.3,0.3-0.7,0.3-1c0-0.6-0.2-1.1-0.6-1.4c-0.5-0.3-1-0.3-1.5-0.4h-4V74.2z"
				/>
			<path class="st0" d="M37.8,71.4c0,0.8,0.5,1.7,1.6,1.7c0.4,0,0.8-0.1,1.1-0.4c0.3-0.2,0.3-0.4,0.4-0.6l1.9,0.2
				c-0.1,0.2-0.1,0.4-0.3,0.7c-0.6,1-1.9,1.3-3.1,1.3c-0.5,0-1.1,0-1.6-0.2c-1.1-0.4-2-1.2-2-2.7c0-1.1,0.6-3,3.7-3
				c2.6,0,3.1,1.2,3.2,1.7l-1.8,0.3c-0.1-0.2-0.2-0.4-0.5-0.6c-0.3-0.2-0.7-0.3-0.9-0.3C38.6,69.7,37.8,70.2,37.8,71.4"/>
			<path class="st0" d="M43.1,68.7h1.9v3.1c0,0.3,0,0.6,0.2,0.8c0.2,0.4,0.7,0.5,1.1,0.5c0.5,0,0.8-0.2,0.9-0.3
				c0.3-0.3,0.3-0.8,0.3-1.1v-3.1h1.9v2.9c0,0.6,0,1.3-0.3,1.8c-0.6,0.9-2,1-2.9,1c-1.2,0-1.9-0.2-2.3-0.4c-0.8-0.4-0.9-1.3-0.9-1.7
				V68.7z"/>
			<path class="st0" d="M52.3,69.9h1.4c0.2,0,0.3,0,0.5,0.1c0.1,0.1,0.2,0.2,0.2,0.4c0,0.5-0.5,0.5-0.8,0.5h-1.3V69.9z M50.3,74.2
				h1.9v-1.9h1.5c0.9,0,1.7,0,2.2-0.7c0.3-0.3,0.4-0.8,0.4-1.1c0-0.8-0.3-1.2-0.5-1.3c-0.4-0.4-1.1-0.4-1.8-0.4h-3.7V74.2z"/>
		</g>
		<path class="st0" d="M63.6,74.2h-5.5V73c0.2-0.4,0.4-0.8,1.4-1.4c0.2-0.1,0.3-0.2,1.3-0.7c0.5-0.3,0.8-0.4,0.8-0.7
			c0-0.5-0.6-0.5-0.8-0.5c-0.2,0-0.4,0-0.6,0.1C60,70.1,60,70.4,60,70.6l-1.6,0c0-0.4,0.1-0.8,0.3-1.2c0.3-0.5,0.8-0.8,2.4-0.8
			c1.2,0,1.8,0.2,2.1,0.6c0.2,0.3,0.3,0.6,0.3,1c0,0.8-0.6,1.2-1.2,1.5c-0.3,0.2-0.6,0.3-0.9,0.4c-0.2,0.1-0.8,0.4-1.2,0.7
			c-0.1,0-0.1,0.1-0.1,0.1c0.1,0,0.2,0,0.2,0c0.1,0,0.2,0,0.2,0h3.1V74.2z"/>
		<path class="st0" d="M67.2,69.7c0.5,0,0.8,0.2,1,0.5c0.2,0.3,0.3,0.8,0.3,1.2c0,0.3-0.1,0.9-0.3,1.2c-0.3,0.4-0.7,0.5-0.9,0.5
			c-1,0-1.1-0.9-1.2-1.3c0-0.1,0-0.3,0-0.4c0-0.5,0.1-0.9,0.3-1.2C66.5,69.8,66.9,69.7,67.2,69.7 M67.3,68.5c-1.4,0-2.7,0.4-3.1,2
			c-0.1,0.3-0.1,0.6-0.1,1c0,0.7,0.1,1.7,1.1,2.4c0.7,0.5,1.5,0.5,2,0.5c1.8,0,3.1-0.8,3.1-2.9c0-0.7-0.1-1.6-0.8-2.2
			C69,68.7,68.2,68.5,67.3,68.5"/>
		<path class="st0" d="M76.3,74.2h-5.5V73c0.2-0.4,0.4-0.8,1.4-1.4c0.2-0.1,0.3-0.2,1.3-0.7c0.5-0.3,0.8-0.4,0.8-0.7
			c0-0.5-0.6-0.5-0.8-0.5c-0.2,0-0.4,0-0.6,0.1c-0.3,0.2-0.3,0.5-0.3,0.7l-1.6,0c0-0.4,0.1-0.8,0.3-1.2c0.3-0.5,0.8-0.8,2.4-0.8
			c1.2,0,1.8,0.2,2.1,0.6c0.2,0.3,0.3,0.6,0.3,1c0,0.8-0.6,1.2-1.2,1.5c-0.3,0.2-0.6,0.3-0.9,0.4c-0.2,0.1-0.8,0.4-1.2,0.7
			c-0.1,0-0.1,0.1-0.1,0.1c0.1,0,0.2,0,0.2,0c0.1,0,0.2,0,0.2,0h3.1V74.2z"/>
		<path class="st0" d="M79.1,70.8h0.4c0.5,0,1,0,1.2-0.1c0.2-0.1,0.3-0.2,0.3-0.5c0-0.4-0.4-0.5-0.9-0.5c-0.9,0-1.1,0.3-1.1,0.5
			l-1.8-0.1c0.1-0.5,0.3-1.6,2.9-1.6c0.3,0,0.6,0,0.8,0c0.4,0,1,0.1,1.4,0.4c0.4,0.3,0.6,0.8,0.6,1.1c0,0.1,0,0.3-0.1,0.5
			c-0.1,0.2-0.4,0.5-0.9,0.6c-0.1,0-0.1,0-0.2,0c0.1,0,0.1,0,0.2,0c0.7,0.1,1.1,0.5,1.1,1.2c0,1.7-1.9,1.8-3.1,1.8
			c-0.9,0-1.8,0-2.4-0.5c-0.6-0.4-0.7-1-0.7-1.3l1.7-0.1c0,0.1,0.1,0.3,0.3,0.4c0.3,0.2,0.6,0.2,1,0.2c0.8,0,1.1-0.2,1.1-0.6
			c0-0.5-0.6-0.6-0.9-0.6h-1.1V70.8z"/>
	</g>
	<g>
		<g>
			<g>
				<defs>
					<path id="SVGID_1_" d="M54.5,55.2c0.1-0.2,0.1-0.3,0.2-0.5c0-0.1,0.1-0.2,0.1-0.2c0,0,0.1-0.1,0.1-0.1c0,0,0.1-0.1,0.1-0.1
						c0.1-0.2,0.2-0.3,0.3-0.4c0.1-0.1,0.1-0.1,0.2-0.2c0.1-0.1,0.1-0.1,0.2-0.2c0.2-0.1,0.4-0.3,0.6-0.4c0.2-0.1,0.4-0.2,0.6-0.2
						c-0.1,0.2-0.1,0.5-0.2,0.8c0,0.3-0.1,0.7-0.2,1c0,0.1,0,0.2-0.1,0.2c0,0.1-0.1,0.2-0.1,0.2c-0.1,0.2-0.2,0.3-0.3,0.4
						c-0.2,0.3-0.5,0.4-0.9,0.5c-0.1,0-0.2,0-0.3,0c-0.1,0-0.2,0-0.2,0c-0.1,0-0.2-0.1-0.3-0.2c-0.1-0.1-0.1-0.2-0.1-0.3
						C54.4,55.5,54.4,55.3,54.5,55.2z M55.2,57c0,0.1,0,0.2,0,0.3c0.1,0.1,0.2,0.2,0.3,0.2c0.1,0,0.3,0.1,0.5,0.1c0.2,0,0.4,0,0.6,0
						c0.1,0,0.2,0,0.3-0.1c0.1,0,0.1,0,0.2,0c0.1,0,0.1,0,0.2,0c0.2-0.1,0.4-0.1,0.6-0.2c0.1,0,0.2-0.1,0.3-0.1
						c0.1,0,0.2-0.1,0.2-0.2c0.2-0.2,0.4-0.3,0.6-0.5c0.2-0.2,0.3-0.4,0.4-0.7c-0.3,0.1-0.6,0.1-0.9,0.1c-0.3,0-0.7-0.1-1-0.1
						c-0.1,0-0.2,0-0.3,0c-0.1,0-0.2,0-0.3,0c-0.2,0-0.3,0.1-0.5,0.1c-0.3,0.1-0.6,0.3-0.9,0.7c-0.1,0.1-0.1,0.2-0.2,0.3
						C55.3,56.8,55.3,56.9,55.2,57z M59.3,51.4c0-0.3,0-0.6,0.1-0.8c-0.2,0.1-0.4,0.2-0.6,0.3c-0.1,0.1-0.2,0.1-0.3,0.2
						c0,0-0.1,0.1-0.1,0.1c0,0-0.1,0.1-0.1,0.1c-0.1,0.1-0.2,0.2-0.3,0.4c0,0.1-0.1,0.2-0.1,0.2c0,0.1-0.1,0.2-0.1,0.2
						c-0.1,0.2-0.2,0.3-0.2,0.5c0,0.1-0.1,0.2-0.1,0.3c0,0.1,0,0.2-0.1,0.2c-0.1,0.6,0,1.1,0.9,0.9c0.1,0,0.1,0,0.1-0.1
						c0,0,0.1,0,0.1-0.1c0.1,0,0.2-0.1,0.2-0.2c0.1-0.1,0.3-0.2,0.3-0.4c0-0.1,0.1-0.1,0.1-0.2c0-0.1,0.1-0.2,0.1-0.2
						c0-0.2,0.1-0.3,0.1-0.5c0-0.1,0-0.2,0-0.3c0-0.1,0-0.2,0-0.3C59.3,51.7,59.3,51.5,59.3,51.4z M59.1,55.3c0.1,0,0.2,0,0.3,0
						c0.1,0,0.2,0,0.3-0.1c0.2-0.1,0.4-0.1,0.6-0.2c0.1,0,0.2-0.1,0.3-0.1c0.1-0.1,0.2-0.1,0.3-0.2c0.2-0.1,0.3-0.2,0.5-0.4
						c0,0,0.1-0.1,0.1-0.1c0-0.1,0.1-0.1,0.1-0.2c0.1-0.1,0.2-0.2,0.2-0.3c0.1-0.2,0.3-0.5,0.3-0.7c-0.2,0.1-0.5,0.2-0.9,0.2
						c-0.2,0-0.3,0-0.5,0c-0.1,0-0.2,0-0.3,0c-0.1,0-0.2,0-0.3,0c-0.2,0-0.3,0-0.5,0.1c-0.1,0-0.2,0-0.2,0.1c-0.1,0-0.2,0.1-0.2,0.1
						c-0.2,0.1-0.3,0.2-0.4,0.3c-0.1,0.1-0.1,0.1-0.2,0.2c0,0-0.1,0.1-0.1,0.1c0,0-0.1,0.1-0.1,0.1C58,55.1,58.4,55.4,59.1,55.3z
						 M61.4,48.7c-0.1-0.3-0.1-0.6-0.1-0.8c-0.2,0.1-0.4,0.2-0.5,0.4c-0.1,0.1-0.2,0.2-0.2,0.2c-0.1,0.1-0.1,0.2-0.2,0.3
						c0,0.1-0.1,0.1-0.1,0.2c0,0,0,0.1-0.1,0.1c0,0,0,0.1-0.1,0.1c-0.1,0.2-0.1,0.3-0.2,0.5c0,0.1-0.1,0.2-0.1,0.3
						c0,0.1,0,0.2-0.1,0.3c0,0.2-0.1,0.4-0.1,0.5c0,0.2,0,0.3,0,0.4c0,0.1,0.1,0.2,0.1,0.3c0.1,0.1,0.2,0.1,0.3,0.1
						c0.1,0,0.3,0,0.5-0.1c0.2-0.1,0.3-0.2,0.5-0.3c0.1-0.1,0.2-0.3,0.3-0.4c0.1-0.2,0.1-0.3,0.1-0.5c0-0.2,0-0.3,0-0.5
						c0-0.2,0-0.3,0-0.5C61.5,49,61.5,48.9,61.4,48.7z M61.8,52.6c0.2,0,0.3-0.1,0.5-0.2c0.1,0,0.2-0.1,0.3-0.1
						c0.1-0.1,0.2-0.1,0.3-0.2c0.2-0.1,0.3-0.2,0.5-0.4c0,0,0.1-0.1,0.1-0.1c0,0,0.1-0.1,0.1-0.1c0.1-0.1,0.1-0.1,0.2-0.2
						c0.1-0.1,0.2-0.2,0.2-0.3c0.1-0.1,0.1-0.2,0.2-0.4c0.1-0.2,0.2-0.5,0.2-0.7c-0.2,0.1-0.5,0.2-0.8,0.3c-0.2,0-0.3,0.1-0.5,0.1
						c-0.2,0-0.3,0.1-0.5,0.1c-0.2,0-0.3,0.1-0.5,0.2c-0.2,0.1-0.3,0.1-0.4,0.2c-0.1,0.1-0.3,0.2-0.4,0.4c-0.1,0.1-0.2,0.3-0.3,0.5
						c-0.1,0.2-0.1,0.4-0.1,0.5c0,0.1,0.1,0.2,0.1,0.3c0.1,0.1,0.2,0.1,0.3,0.1C61.5,52.7,61.6,52.7,61.8,52.6z M63.5,47.7
						c0.1-0.3,0.1-0.7,0-1c0-0.1,0-0.2-0.1-0.3c0-0.1,0-0.2-0.1-0.2c0-0.2-0.1-0.3-0.1-0.5c0-0.2-0.1-0.3-0.1-0.4
						c0-0.1-0.1-0.3-0.1-0.4c-0.1,0.1-0.2,0.1-0.3,0.2c-0.1,0.1-0.2,0.2-0.2,0.2c-0.1,0.2-0.3,0.4-0.4,0.6c-0.1,0.1-0.1,0.3-0.2,0.5
						c0,0.1-0.1,0.2-0.1,0.3c0,0,0,0.1,0,0.1c0,0,0,0.1,0,0.1c0,0.2-0.1,0.4-0.1,0.6c0,0.1,0,0.2,0,0.3c0,0,0,0.1,0,0.1
						c0,0,0,0.1,0,0.1c0,0.6,0.3,1.1,1.1,0.6C63.2,48.4,63.4,48,63.5,47.7z M64.4,49.5c0.1,0,0.2-0.1,0.3-0.2
						c0.2-0.1,0.3-0.2,0.5-0.4c0,0,0.1-0.1,0.1-0.1c0,0,0.1-0.1,0.1-0.1c0.1-0.1,0.1-0.2,0.2-0.2c0.1-0.2,0.2-0.3,0.3-0.5
						c0.1-0.2,0.2-0.5,0.3-0.7c0-0.1,0.1-0.3,0.1-0.4c0-0.1,0-0.3,0-0.4c-0.1,0.1-0.2,0.2-0.4,0.2c-0.1,0.1-0.3,0.1-0.4,0.2
						c-0.2,0.1-0.3,0.1-0.5,0.2c-0.1,0-0.2,0.1-0.2,0.1c-0.1,0-0.2,0.1-0.2,0.1c-0.3,0.1-0.6,0.3-0.9,0.5c-0.2,0.2-0.4,0.5-0.5,1
						c-0.2,0.9,0.3,1,0.9,0.8c0,0,0.1,0,0.1-0.1C64.3,49.5,64.3,49.5,64.4,49.5z M65.1,44.5c0-0.2,0-0.3,0-0.5
						c0-0.2-0.1-0.3-0.1-0.5c-0.1-0.2-0.1-0.3-0.2-0.5c-0.1-0.2-0.1-0.3-0.2-0.4c-0.1-0.3-0.3-0.6-0.3-0.8c-0.2,0.2-0.3,0.3-0.4,0.5
						c0,0-0.1,0.1-0.1,0.1c0,0-0.1,0.1-0.1,0.1c-0.1,0.1-0.1,0.2-0.1,0.3c0,0.1,0,0.1-0.1,0.2c0,0.1,0,0.2-0.1,0.3
						c0,0.2-0.1,0.4-0.1,0.5c0,0.1,0,0.2,0,0.3c0,0,0,0.1,0,0.1c0,0,0,0.1,0,0.1c0,0.2,0,0.4,0.1,0.5c0.1,0.3,0.2,0.6,0.4,0.7
						c0.2,0.1,0.4,0,0.8-0.3c0.1-0.1,0.1-0.1,0.2-0.2c0.1-0.1,0.1-0.2,0.2-0.2C65,44.8,65.1,44.7,65.1,44.5z M66,46.3
						c0.1-0.1,0.3-0.2,0.5-0.3c0,0,0.1-0.1,0.1-0.1c0,0,0.1-0.1,0.1-0.1c0.1-0.1,0.1-0.2,0.2-0.2c0.1-0.2,0.3-0.3,0.4-0.5
						c0.1-0.1,0.1-0.2,0.1-0.3c0-0.1,0.1-0.2,0.1-0.3c0-0.1,0.1-0.3,0.1-0.4c0-0.1,0-0.1,0-0.2c0-0.1,0-0.1,0-0.2c0-0.3,0-0.5,0-0.8
						c-0.2,0.2-0.4,0.4-0.7,0.5c-0.1,0.1-0.3,0.2-0.5,0.2c-0.2,0.1-0.3,0.2-0.5,0.3c-0.1,0.1-0.3,0.2-0.4,0.3
						c-0.1,0.1-0.3,0.2-0.4,0.4c-0.1,0.1-0.2,0.3-0.2,0.5c0,0.1-0.1,0.2-0.1,0.3c0,0.1,0,0.2,0,0.3c0,0.4,0.1,0.7,0.3,0.8
						C65.4,46.5,65.7,46.4,66,46.3z M65.9,42.1c0.3-0.3,0.4-0.7,0.4-1c0-0.3-0.1-0.7-0.3-1c0-0.1-0.1-0.2-0.1-0.2
						c0-0.1-0.1-0.1-0.1-0.2c-0.1-0.1-0.2-0.3-0.3-0.4c-0.1-0.1-0.2-0.3-0.2-0.4c0,0,0-0.1,0-0.1c0,0,0,0,0,0l0,0l0,0c0,0,0,0,0,0v0
						l0,0l0,0l0,0c0-0.1-0.1-0.1-0.1-0.2c0,0-0.1,0.1-0.1,0.2l0,0l0,0l0,0v0h0c0,0,0,0,0,0l0,0l0,0l0,0c0,0,0,0,0,0.1
						c-0.1,0.1-0.1,0.2-0.2,0.3c0,0.1-0.1,0.2-0.1,0.3c0,0.1,0,0.1,0,0.2c0,0.1,0,0.1,0,0.2c0,0.1-0.1,0.3-0.1,0.5
						c0,0.2,0,0.4,0,0.5c0,0.2,0,0.4,0.1,0.6c0,0.2,0.1,0.4,0.1,0.5C64.9,42.4,65.3,42.8,65.9,42.1z M67.8,42.3
						c0.1-0.2,0.3-0.3,0.4-0.5c0.1-0.2,0.2-0.4,0.3-0.6c0.1-0.2,0.1-0.4,0.2-0.5c0-0.1,0-0.1,0-0.2c0-0.1,0-0.1,0-0.2
						c0-0.1,0-0.3,0-0.4c0-0.1,0-0.3,0-0.4c0,0,0-0.1,0-0.1l0,0l0,0l0,0c0,0,0-0.1,0,0v0l0,0l0,0l0,0l0,0c0-0.1,0-0.1,0-0.2
						c0,0.1-0.1,0.1-0.1,0.2l0,0l0,0l0,0v0c0,0,0,0.1,0,0l0,0l0,0c0,0,0,0,0,0c0,0-0.1,0.1-0.1,0.1c-0.1,0.1-0.2,0.2-0.4,0.3
						c-0.1,0.1-0.3,0.2-0.4,0.3c-0.1,0.1-0.1,0.1-0.2,0.2c-0.1,0.1-0.1,0.1-0.2,0.2c-0.3,0.2-0.5,0.5-0.7,0.8
						c-0.2,0.3-0.3,0.6-0.2,1.1c0.1,0.9,0.6,0.9,1.1,0.5C67.5,42.6,67.7,42.5,67.8,42.3z M66.4,38.9C66.4,38.9,66.4,38.9,66.4,38.9
						L66.4,38.9L66.4,38.9L66.4,38.9C66.4,38.9,66.4,38.9,66.4,38.9c0.1,0,0.1-0.1,0.1-0.1c0.1-0.1,0.1-0.1,0.2-0.2
						c0.1-0.2,0.2-0.4,0.2-0.5c0,0,0-0.1,0-0.1c0,0,0-0.1,0-0.1c0-0.1,0-0.2,0-0.3c0-0.2-0.1-0.3-0.1-0.5c0-0.1-0.1-0.2-0.1-0.2
						c0-0.1-0.1-0.1-0.1-0.2c-0.2-0.3-0.4-0.5-0.6-0.8c-0.1-0.1-0.2-0.2-0.3-0.3c-0.1-0.1-0.2-0.2-0.2-0.3c-0.1,0.1-0.1,0.2-0.1,0.3
						c0,0.1-0.1,0.2-0.1,0.3C65.1,36,65,36.2,65,36.5c0,0.1,0,0.2,0,0.2c0,0.1,0,0.2,0,0.2c0,0.2,0.1,0.3,0.1,0.5c0,0,0,0.1,0,0.1
						c0,0,0,0,0,0.1l0,0.1c0,0.1,0,0.2,0.1,0.3c0.1,0.2,0.1,0.4,0.2,0.5c0,0.1,0.1,0.1,0.1,0.2c0,0,0,0.1,0.1,0.1l0,0l0,0l0,0
						c0,0,0,0,0,0l0,0l0,0l0,0c0,0,0,0,0,0C65.8,39,65.9,39,66,39C66.1,39.1,66.2,39,66.4,38.9C66.4,39,66.4,39,66.4,38.9L66.4,38.9
						L66.4,38.9z M68.6,38.5c0.1-0.1,0.1-0.2,0.2-0.2l0-0.1c0,0,0,0,0-0.1c0,0,0-0.1,0.1-0.1c0.1-0.2,0.2-0.4,0.2-0.6
						c0-0.1,0.1-0.2,0.1-0.3c0-0.1,0-0.2,0-0.3c0-0.3,0-0.5,0-0.8c0-0.1,0-0.3-0.1-0.4c0-0.1-0.1-0.2-0.1-0.4
						c-0.1,0.1-0.1,0.2-0.2,0.4c-0.1,0.1-0.2,0.2-0.3,0.4c-0.2,0.2-0.5,0.5-0.7,0.7c-0.1,0.1-0.1,0.1-0.2,0.2
						c-0.1,0.1-0.1,0.1-0.2,0.2c-0.1,0.1-0.2,0.3-0.2,0.4c0,0.1-0.1,0.2-0.1,0.2c0,0,0,0.1,0,0.1c0,0,0,0.1,0,0.1c0,0.2,0,0.4,0,0.6
						c0,0.1,0.1,0.2,0.1,0.3c0,0,0,0.1,0.1,0.1c0,0,0,0,0,0l0,0l0,0v0c0,0,0,0,0,0l0,0l0,0c0,0,0,0,0,0c0.1,0.1,0.2,0.2,0.3,0.2
						c0.1,0,0.2,0,0.3-0.1c0,0,0,0,0,0l0,0l0,0h0c0,0,0,0,0,0h0l0,0l0,0c0,0,0.1,0,0.1-0.1c0.1,0,0.1-0.1,0.2-0.2
						C68.4,38.8,68.5,38.7,68.6,38.5z M67,33.7c0-0.1-0.1-0.1-0.1-0.2c-0.1-0.1-0.2-0.3-0.3-0.4c-0.1-0.1-0.2-0.2-0.4-0.4
						c-0.1-0.1-0.3-0.2-0.4-0.3c-0.2-0.2-0.5-0.4-0.6-0.6c-0.1,0.2-0.1,0.4-0.2,0.7c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0,0.3
						c0,0.2,0,0.3,0.1,0.5c0,0,0,0.1,0,0.1c0,0,0,0.1,0,0.1c0,0.1,0.1,0.2,0.1,0.3c0.1,0.2,0.1,0.3,0.2,0.5c0,0.1,0.1,0.2,0.1,0.2
						c0,0.1,0.1,0.1,0.1,0.2c0.4,0.5,0.8,0.7,1.2-0.1c0-0.1,0.1-0.2,0.1-0.3c0-0.1,0-0.2,0-0.3c0-0.2,0-0.3-0.1-0.5
						C67.1,33.8,67,33.8,67,33.7z M69.3,33.6c0-0.1,0-0.1,0-0.2c0-0.2,0-0.4,0-0.6c0-0.1,0-0.3,0-0.4c0-0.1-0.1-0.3-0.1-0.4
						c-0.1-0.3-0.2-0.5-0.3-0.7c-0.1,0.3-0.3,0.5-0.5,0.8c-0.1,0.1-0.2,0.3-0.3,0.4c-0.1,0.1-0.2,0.3-0.3,0.4
						c-0.1,0.1-0.2,0.3-0.3,0.4c0,0.1-0.1,0.2-0.1,0.2c0,0.1-0.1,0.2-0.1,0.2c0,0.2-0.1,0.3,0,0.5c0,0.1,0,0.2,0,0.3
						c0,0.1,0,0.2,0.1,0.3c0.3,0.8,0.8,0.7,1.2,0.1c0.1-0.1,0.1-0.1,0.2-0.2c0-0.1,0.1-0.2,0.1-0.3c0.1-0.2,0.2-0.4,0.2-0.6
						c0-0.1,0.1-0.2,0.1-0.3C69.2,33.7,69.3,33.7,69.3,33.6z M66.1,29.5c-0.3-0.2-0.6-0.4-0.8-0.6c-0.1-0.1-0.3-0.2-0.4-0.2
						c-0.1-0.1-0.2-0.2-0.3-0.3c0,0.1,0,0.2-0.1,0.3c0,0.1,0,0.2,0,0.3c0,0.2,0,0.5,0.1,0.7c0,0.1,0.1,0.3,0.1,0.5
						c0.1,0.2,0.1,0.3,0.2,0.5c0,0.1,0.1,0.2,0.1,0.2c0,0.1,0.1,0.2,0.2,0.2c0.1,0.1,0.2,0.3,0.3,0.4c0.1,0.1,0.2,0.2,0.3,0.3
						c0.1,0.1,0.2,0.1,0.3,0.1c0.1,0,0.2-0.1,0.3-0.2c0,0,0.1-0.1,0.1-0.2c0-0.1,0.1-0.2,0.1-0.3c0.1-0.2,0.1-0.4,0.1-0.6
						c0-0.1,0-0.2,0-0.3c0-0.1-0.1-0.2-0.1-0.2c-0.1-0.2-0.2-0.3-0.3-0.4C66.4,29.8,66.3,29.7,66.1,29.5z M68.8,30.2
						c0-0.2,0-0.4,0-0.6c0-0.2,0-0.4,0-0.6c-0.1-0.3-0.1-0.5-0.2-0.8c-0.1-0.1-0.1-0.2-0.2-0.3c-0.1-0.1-0.1-0.2-0.2-0.3
						c0,0.1-0.1,0.3-0.1,0.4c-0.1,0.1-0.1,0.3-0.2,0.4c-0.2,0.3-0.4,0.6-0.5,0.9c-0.1,0.2-0.1,0.3-0.2,0.5c-0.1,0.2-0.1,0.3-0.1,0.5
						c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0,0.3c0,0.2,0.1,0.4,0.2,0.6c0.1,0.1,0.1,0.2,0.2,0.3c0.1,0.1,0.1,0.1,0.2,0.2
						c0.1,0.1,0.2,0.1,0.3,0.1c0.1,0,0.2-0.1,0.3-0.2c0.1-0.1,0.2-0.2,0.3-0.4c0.1-0.2,0.1-0.3,0.2-0.5c0-0.1,0.1-0.2,0.1-0.3
						C68.8,30.4,68.8,30.3,68.8,30.2z M65.2,26.1C65.1,26.1,65,26,65,26c-0.1,0-0.1-0.1-0.2-0.1c-0.2-0.1-0.3-0.1-0.5-0.2
						c-0.1-0.1-0.3-0.1-0.4-0.2c-0.1-0.1-0.2-0.1-0.3-0.2c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0.1,0.3l0,0.1
						c0,0,0,0.1,0,0.1c0,0.1,0,0.1,0.1,0.2c0.1,0.3,0.3,0.6,0.5,0.9c0.1,0.1,0.2,0.3,0.4,0.4c0.1,0.1,0.1,0.1,0.2,0.2
						c0.1,0.1,0.1,0.1,0.2,0.2c0.5,0.4,1,0.5,1.1-0.4c0-0.1,0-0.2,0-0.3c0-0.1,0-0.2,0-0.3c0-0.2-0.1-0.3-0.2-0.5
						c0-0.1-0.1-0.1-0.1-0.2c-0.1-0.1-0.1-0.1-0.2-0.2C65.5,26.3,65.3,26.2,65.2,26.1z M67.9,26.4c0-0.4-0.1-0.9-0.2-1.2
						c0-0.1,0-0.1-0.1-0.2l0-0.1c0,0,0-0.1,0-0.1c-0.1-0.1-0.1-0.2-0.2-0.3c-0.1-0.1-0.1-0.2-0.2-0.3c-0.1-0.1-0.2-0.2-0.3-0.3
						c0,0.1,0,0.3-0.1,0.4c0,0.1-0.1,0.3-0.1,0.5c-0.1,0.2-0.1,0.3-0.2,0.5c0,0.1-0.1,0.2-0.1,0.2c0,0.1-0.1,0.2-0.1,0.3
						c-0.1,0.2-0.1,0.3-0.1,0.5c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0,0.3c0,0.2,0,0.3,0.1,0.5c0,0.1,0.1,0.2,0.1,0.3
						c0,0.1,0.1,0.2,0.2,0.3c0.5,0.7,1,0.5,1.2-0.2c0-0.1,0.1-0.2,0.1-0.3c0-0.1,0-0.2,0.1-0.3C67.9,26.8,67.9,26.6,67.9,26.4z
						 M65,24.5c0-0.2,0-0.4-0.1-0.6c0-0.1-0.1-0.2-0.1-0.2c0-0.1-0.1-0.1-0.2-0.2c-0.2-0.3-0.5-0.4-0.8-0.6
						c-0.3-0.1-0.6-0.2-0.9-0.3c-0.3-0.1-0.6-0.2-0.8-0.3c0,0.2,0.1,0.5,0.1,0.7c0.1,0.2,0.1,0.4,0.3,0.6c0,0.1,0.1,0.1,0.1,0.2
						c0,0.1,0.1,0.1,0.1,0.2c0.1,0.1,0.2,0.3,0.4,0.4c0.1,0.1,0.1,0.1,0.2,0.2c0.1,0.1,0.1,0.1,0.2,0.2c0.1,0.1,0.3,0.2,0.4,0.3
						c0.1,0,0.1,0.1,0.2,0.1c0.1,0,0.1,0.1,0.2,0.1c0.1,0,0.2,0,0.3,0C64.8,25.1,65,24.9,65,24.5z M66.6,23.4c0-0.1,0-0.2,0-0.3
						c0-0.1,0-0.2,0-0.3c0-0.2-0.1-0.4-0.1-0.6c0-0.1-0.1-0.2-0.1-0.3c0-0.1-0.1-0.2-0.1-0.3c-0.1-0.2-0.3-0.5-0.4-0.7
						c-0.2-0.2-0.4-0.4-0.6-0.5c0,0.3,0,0.6-0.1,0.9c-0.1,0.3-0.2,0.7-0.2,1c-0.1,0.3-0.1,0.7,0,1c0,0.1,0,0.2,0.1,0.2
						c0,0.1,0.1,0.2,0.1,0.2c0.1,0.2,0.2,0.3,0.3,0.5c0.3,0.3,0.6,0.4,0.8,0.3c0.1,0,0.2-0.1,0.2-0.2c0-0.1,0.1-0.1,0.1-0.2
						c0-0.1,0-0.2,0.1-0.2C66.6,23.8,66.6,23.6,66.6,23.4z M63.2,20.7c-0.1-0.2-0.2-0.3-0.3-0.4c-0.3-0.2-0.6-0.4-0.9-0.4
						c-0.2,0-0.3-0.1-0.5-0.1c-0.2,0-0.3,0-0.5-0.1c-0.2,0-0.3,0-0.4-0.1c-0.1,0-0.3-0.1-0.4-0.1c0,0.1,0.1,0.2,0.1,0.3
						c0,0.1,0.1,0.2,0.1,0.3c0,0.1,0.1,0.2,0.2,0.3c0.1,0.1,0.1,0.2,0.2,0.3c0.1,0.1,0.2,0.2,0.3,0.4c0.1,0.1,0.3,0.2,0.4,0.4
						c0.1,0.1,0.3,0.2,0.5,0.3c0.1,0,0.2,0.1,0.2,0.1c0.1,0,0.2,0.1,0.2,0.1c0.6,0.2,1.1,0.2,1-0.7c0-0.1,0-0.2-0.1-0.3
						C63.2,20.9,63.2,20.8,63.2,20.7z M64.8,20.5c0-0.1,0-0.2,0-0.3c0-0.1,0-0.2,0-0.3c0-0.2-0.1-0.4-0.1-0.6
						c-0.1-0.2-0.1-0.4-0.2-0.6c-0.1-0.2-0.2-0.4-0.3-0.5c-0.1-0.1-0.2-0.2-0.3-0.3c-0.1-0.1-0.2-0.2-0.3-0.3
						c-0.1-0.1-0.2-0.2-0.3-0.2c-0.1-0.1-0.2-0.1-0.3-0.2c0,0.1,0.1,0.3,0.1,0.4c0,0.2,0,0.3,0,0.5c0,0.2,0,0.3,0,0.5
						c0,0.2,0,0.3,0,0.5c0,0.4,0,0.7,0.1,1c0.1,0.2,0.1,0.3,0.2,0.5c0.1,0.1,0.1,0.1,0.2,0.2c0.1,0.1,0.1,0.1,0.2,0.2
						C64.4,21.6,64.7,21.2,64.8,20.5z M60.6,17.4c-0.1-0.1-0.3-0.2-0.4-0.2c-0.2,0-0.3-0.1-0.5-0.1c-0.2,0-0.3,0-0.5,0
						c-0.1,0-0.2,0-0.2,0c-0.1,0-0.2,0-0.2,0c-0.3,0-0.6,0-0.8-0.1c0.1,0.4,0.4,0.8,0.7,1.1c0.1,0.1,0.2,0.2,0.4,0.3
						c0.1,0.1,0.3,0.2,0.5,0.3c0.1,0,0.2,0.1,0.3,0.1c0,0,0.1,0,0.1,0.1c0,0,0.1,0,0.1,0.1c0.2,0.1,0.3,0.1,0.5,0.2
						c0.3,0.1,0.6,0.1,0.8-0.1c0.2-0.1,0.2-0.4,0.1-0.8c0-0.1-0.1-0.2-0.1-0.3c0-0.1-0.1-0.2-0.1-0.2C60.9,17.6,60.8,17.5,60.6,17.4
						z M62.4,18.1c0.2-0.1,0.2-0.4,0.2-0.8c0-0.2,0-0.4-0.1-0.6c0,0,0-0.1,0-0.1c0,0,0-0.1,0-0.1c0-0.1-0.1-0.2-0.1-0.3
						c-0.1-0.2-0.2-0.4-0.3-0.6c-0.1-0.2-0.2-0.3-0.4-0.5c-0.4-0.4-0.8-0.7-1.3-0.9c0.1,0.2,0.1,0.6,0.2,0.9c0,0.1,0,0.2,0,0.3
						c0,0.1,0,0.2,0,0.3c0,0.2,0,0.4,0,0.5c0,0.2,0,0.3,0.1,0.5c0,0.2,0.1,0.3,0.2,0.5c0.1,0.2,0.2,0.3,0.3,0.4
						c0.1,0.1,0.1,0.1,0.2,0.2c0.1,0.1,0.2,0.1,0.3,0.2C62,18.2,62.3,18.3,62.4,18.1z M57.1,16.4c0.3,0.1,0.7,0.2,1,0.3
						c0.3,0,0.6,0,0.7-0.2c0.1-0.1,0.1-0.2,0.1-0.3c0-0.1,0-0.1,0-0.2c0-0.1-0.1-0.2-0.1-0.3c-0.1-0.2-0.2-0.4-0.3-0.5
						c-0.1-0.1-0.3-0.2-0.4-0.3c-0.3-0.1-0.6-0.2-1-0.2c-0.3,0-0.7,0.1-1,0.1c-0.3,0-0.6,0.1-0.8,0c0.1,0.1,0.1,0.2,0.2,0.3
						c0,0,0.1,0.1,0.1,0.1c0,0,0.1,0.1,0.1,0.1c0.1,0.2,0.3,0.3,0.5,0.5c0.1,0,0.1,0.1,0.2,0.1c0.1,0,0.1,0.1,0.2,0.1
						C56.7,16.3,56.9,16.3,57.1,16.4z M59.9,15.3c0.1-0.2,0.2-0.4,0.1-0.8c-0.1-0.3-0.2-0.7-0.5-1.1c-0.1-0.2-0.2-0.4-0.4-0.5
						c-0.1-0.1-0.1-0.2-0.2-0.2c-0.1-0.1-0.1-0.1-0.2-0.2c-0.2-0.2-0.4-0.3-0.7-0.4c-0.1,0-0.1-0.1-0.2-0.1c-0.1,0-0.1,0-0.2-0.1
						c-0.1,0-0.2-0.1-0.4-0.1c0.1,0.2,0.2,0.5,0.3,0.9c0.1,0.3,0.1,0.7,0.2,1c0.1,0.3,0.2,0.7,0.4,1c0.1,0.1,0.2,0.3,0.4,0.4
						c0.1,0.1,0.3,0.2,0.5,0.3c0.1,0,0.2,0.1,0.3,0.1c0.1,0,0.2,0,0.2,0C59.8,15.5,59.9,15.4,59.9,15.3z M52.4,13.1
						c0.1,0.2,0.3,0.4,0.4,0.5c0.1,0.1,0.2,0.1,0.3,0.2c0.1,0.1,0.2,0.1,0.3,0.2c0.1,0.1,0.3,0.1,0.5,0.2c0.1,0,0.2,0.1,0.3,0.1
						c0.1,0,0.2,0,0.3,0.1c0.2,0,0.4,0.1,0.5,0.1c0.2,0,0.4,0,0.5,0c0.6,0,1.1-0.3,0.6-1.1c-0.1-0.1-0.1-0.2-0.2-0.2
						c-0.1-0.1-0.1-0.1-0.2-0.2c-0.1-0.1-0.3-0.2-0.5-0.2c-0.1,0-0.2,0-0.2-0.1c-0.1,0-0.2,0-0.2,0c-0.2,0-0.3,0-0.5,0
						c-0.2,0-0.3,0.1-0.5,0.1c-0.2,0-0.3,0.1-0.5,0.1C52.9,13.1,52.6,13.1,52.4,13.1z M57.1,12.1c-0.1-0.2-0.1-0.3-0.2-0.5
						c-0.1-0.2-0.2-0.3-0.4-0.5c-0.1-0.1-0.1-0.2-0.2-0.2c-0.1-0.1-0.1-0.2-0.2-0.2c-0.2-0.1-0.3-0.3-0.5-0.3
						c-0.1-0.1-0.2-0.1-0.4-0.2C55.1,10.1,55,10,54.8,10c-0.3-0.1-0.5-0.1-0.8-0.1c0.2,0.2,0.3,0.5,0.4,0.8c0.1,0.2,0.1,0.3,0.2,0.5
						c0.1,0.2,0.1,0.3,0.2,0.5c0.1,0.2,0.1,0.3,0.2,0.5c0,0.1,0.1,0.1,0.1,0.2c0,0.1,0.1,0.1,0.2,0.2c0.1,0.1,0.2,0.2,0.4,0.3
						c0.1,0,0.2,0.1,0.3,0.1c0.1,0,0.2,0.1,0.3,0.1C57.1,13.3,57.3,12.8,57.1,12.1z"/>
				</defs>
				<clipPath id="SVGID_00000108994952485313194990000016210259609334798735_">
					<use xlink:href="#SVGID_1_"  style="overflow:visible;"/>
				</clipPath>
				<g style="clip-path:url(#SVGID_00000108994952485313194990000016210259609334798735_);">
					<path class="st2" d="M53.8,8.4c2.7,1.2,16.3,10.4,16.3,24.4c0,13.9-5,20.1-13.6,26.2"/>
					<path class="st3" d="M53.8,8.4c2.7,1.2,16.3,10.4,16.3,24.4c0,13.9-5,20.1-13.6,26.2"/>
					<path class="st4" d="M53.8,8.4c2.7,1.2,16.3,10.4,16.3,24.4c0,13.8-5,20.1-13.6,26.2"/>
					<path class="st5" d="M53.8,8.4c2.7,1.2,16.3,10.4,16.3,24.4c0,13.8-5,20.1-13.6,26.2"/>
					<path class="st6" d="M53.7,8.4C56.4,9.7,70,18.8,70,32.8c0,13.8-5,20.1-13.5,26.2"/>
					<path class="st7" d="M53.7,8.5C56.4,9.7,70,18.8,70,32.8c0,13.8-4.9,20-13.5,26.1"/>
					<path class="st8" d="M53.7,8.5C56.4,9.7,70,18.8,70,32.8c0,13.8-4.9,20-13.5,26.1"/>
					<path class="st9" d="M53.7,8.5C56.4,9.7,70,18.8,70,32.8c0,13.8-4.9,20-13.5,26.1"/>
					<path class="st10" d="M53.7,8.5C56.4,9.7,70,18.9,70,32.8c0,13.8-4.9,20-13.5,26.1"/>
					<path class="st11" d="M53.7,8.5c2.7,1.2,16.2,10.3,16.2,24.2c0,13.8-4.9,20-13.5,26.1"/>
					<path class="st12" d="M53.7,8.6c2.7,1.2,16.2,10.3,16.2,24.2c0,13.8-4.9,20-13.5,26.1"/>
					<path class="st13" d="M53.7,8.6c2.7,1.2,16.2,10.3,16.2,24.2c0,13.7-4.9,19.9-13.5,26"/>
					<path class="st14" d="M53.7,8.6c2.7,1.2,16.2,10.3,16.2,24.2c0,13.7-4.9,19.9-13.5,26"/>
					<path class="st15" d="M53.7,8.6c2.7,1.2,16.2,10.3,16.2,24.2c0,13.7-4.9,19.9-13.5,26"/>
					<path class="st16" d="M53.7,8.6c2.6,1.2,16.2,10.3,16.2,24.2c0,13.7-4.9,19.9-13.5,26"/>
					<path class="st17" d="M53.7,8.7c2.6,1.2,16.2,10.3,16.2,24.1c0,13.7-4.9,19.9-13.4,26"/>
					<path class="st18" d="M53.6,8.7C56.3,9.9,69.8,19,69.8,32.8c0,13.7-4.9,19.9-13.4,26"/>
					<path class="st19" d="M53.6,8.7C56.3,9.9,69.8,19,69.8,32.8c0,13.7-4.9,19.9-13.4,25.9"/>
					<path class="st20" d="M53.6,8.7C56.3,9.9,69.8,19,69.8,32.8c0,13.7-4.9,19.8-13.4,25.9"/>
					<path class="st21" d="M53.6,8.7C56.3,9.9,69.7,19,69.7,32.8c0,13.7-4.9,19.8-13.4,25.9"/>
					<path class="st22" d="M53.6,8.8c2.6,1.2,16.1,10.2,16.1,24c0,13.7-4.8,19.8-13.4,25.9"/>
					<path class="st23" d="M53.6,8.8c2.6,1.2,16.1,10.2,16.1,24c0,13.6-4.8,19.8-13.4,25.9"/>
					<path class="st24" d="M53.6,8.8c2.6,1.2,16.1,10.2,16.1,24c0,13.6-4.8,19.8-13.4,25.9"/>
					<path class="st25" d="M53.6,8.8c2.6,1.2,16.1,10.2,16.1,24c0,13.6-4.8,19.8-13.4,25.8"/>
					<path class="st26" d="M53.6,8.8c2.6,1.2,16.1,10.2,16.1,24c0,13.6-4.8,19.7-13.4,25.8"/>
					<path class="st27" d="M53.6,8.9c2.6,1.2,16,10.2,16,23.9c0,13.6-4.8,19.7-13.4,25.8"/>
					<path class="st28" d="M53.6,8.9c2.6,1.2,16,10.2,16,23.9c0,13.6-4.8,19.7-13.3,25.8"/>
					<path class="st29" d="M53.5,8.9c2.6,1.2,16,10.2,16,23.9c0,13.6-4.8,19.7-13.3,25.8"/>
					<path class="st30" d="M53.5,8.9c2.6,1.2,16,10.2,16,23.9c0,13.6-4.8,19.7-13.3,25.7"/>
					<path class="st31" d="M53.5,8.9c2.6,1.2,16,10.2,16,23.9c0,13.6-4.8,19.7-13.3,25.7"/>
					<path class="st32" d="M53.5,8.9c2.6,1.2,16,10.2,16,23.8c0,13.5-4.8,19.6-13.3,25.7"/>
					<path class="st33" d="M53.5,9c2.6,1.2,16,10.2,16,23.8c0,13.5-4.8,19.6-13.3,25.7"/>
					<path class="st34" d="M53.5,9c2.6,1.2,16,10.2,16,23.8c0,13.5-4.8,19.6-13.3,25.7"/>
					<path class="st35" d="M53.5,9c2.6,1.2,15.9,10.2,15.9,23.8c0,13.5-4.8,19.6-13.3,25.7"/>
					<path class="st36" d="M53.5,9c2.6,1.2,15.9,10.1,15.9,23.8c0,13.5-4.8,19.6-13.3,25.6"/>
					<path class="st37" d="M53.5,9c2.6,1.2,15.9,10.1,15.9,23.7c0,13.5-4.7,19.6-13.3,25.6"/>
					<path class="st38" d="M53.5,9.1c2.6,1.2,15.9,10.1,15.9,23.7c0,13.5-4.7,19.6-13.3,25.6"/>
					<path class="st39" d="M53.5,9.1c2.6,1.2,15.9,10.1,15.9,23.7c0,13.5-4.7,19.5-13.2,25.6"/>
					<path class="st40" d="M53.5,9.1c2.6,1.2,15.9,10.1,15.9,23.7c0,13.5-4.7,19.5-13.2,25.6"/>
					<path class="st41" d="M53.4,9.1c2.6,1.2,15.9,10.1,15.9,23.7c0,13.4-4.7,19.5-13.2,25.6"/>
					<path class="st42" d="M53.4,9.1c2.6,1.2,15.9,10.1,15.9,23.6c0,13.4-4.7,19.5-13.2,25.5"/>
					<path class="st43" d="M53.4,9.2c2.6,1.2,15.8,10.1,15.8,23.6c0,13.4-4.7,19.5-13.2,25.5"/>
					<path class="st44" d="M53.4,9.2c2.6,1.2,15.8,10.1,15.8,23.6c0,13.4-4.7,19.5-13.2,25.5"/>
					<path class="st45" d="M53.4,9.2c2.6,1.2,15.8,10.1,15.8,23.6c0,13.4-4.7,19.4-13.2,25.5"/>
					<path class="st46" d="M53.4,9.2c2.6,1.2,15.8,10.1,15.8,23.6c0,13.4-4.7,19.4-13.2,25.5"/>
					<path class="st47" d="M53.4,9.2c2.6,1.2,15.8,10.1,15.8,23.6c0,13.4-4.7,19.4-13.2,25.5"/>
					<path class="st48" d="M53.4,9.3c2.6,1.2,15.8,10.1,15.8,23.5c0,13.4-4.7,19.4-13.2,25.4"/>
					<path class="st49" d="M53.4,9.3c2.6,1.2,15.8,10.1,15.8,23.5c0,13.4-4.7,19.4-13.2,25.4"/>
					<path class="st50" d="M53.4,9.3c2.6,1.2,15.8,10,15.8,23.5c0,13.3-4.7,19.4-13.1,25.4"/>
					<path class="st51" d="M53.4,9.3c2.5,1.2,15.7,10,15.7,23.5c0,13.3-4.7,19.4-13.1,25.4"/>
					<path class="st52" d="M53.4,9.3c2.5,1.2,15.7,10,15.7,23.5c0,13.3-4.6,19.3-13.1,25.4"/>
					<path class="st53" d="M53.3,9.4c2.5,1.2,15.7,10,15.7,23.4c0,13.3-4.6,19.3-13.1,25.3"/>
					<path class="st54" d="M53.3,9.4c2.5,1.2,15.7,10,15.7,23.4c0,13.3-4.6,19.3-13.1,25.3"/>
					<path class="st55" d="M53.3,9.4c2.5,1.2,15.7,10,15.7,23.4c0,13.3-4.6,19.3-13.1,25.3"/>
					<path class="st56" d="M53.3,9.4c2.5,1.2,15.7,10,15.7,23.4c0,13.3-4.6,19.3-13.1,25.3"/>
					<path class="st57" d="M53.3,9.4c2.5,1.2,15.7,10,15.7,23.4c0,13.3-4.6,19.3-13.1,25.3"/>
					<path class="st58" d="M53.3,9.5c2.5,1.2,15.7,10,15.7,23.3c0,13.3-4.6,19.2-13.1,25.3"/>
					<path class="st59" d="M53.3,9.5c2.5,1.1,15.6,10,15.6,23.3C68.9,46,64.3,52,55.9,58"/>
					<path class="st60" d="M53.3,9.5c2.5,1.1,15.6,10,15.6,23.3C68.9,46,64.3,52,55.9,58"/>
					<path class="st61" d="M53.3,9.5c2.5,1.1,15.6,10,15.6,23.3C68.9,46,64.3,52,55.8,58"/>
					<path class="st62" d="M53.3,9.5c2.5,1.1,15.6,10,15.6,23.3c0,13.2-4.6,19.2-13,25.2"/>
					<path class="st63" d="M53.3,9.6c2.5,1.1,15.6,9.9,15.6,23.2c0,13.2-4.6,19.2-13,25.2"/>
					<path class="st64" d="M53.2,9.6c2.5,1.1,15.6,9.9,15.6,23.2c0,13.2-4.6,19.1-13,25.2"/>
					<path class="st65" d="M53.2,9.6c2.5,1.1,15.6,9.9,15.6,23.2c0,13.2-4.6,19.1-13,25.1"/>
					<path class="st66" d="M53.2,9.6c2.5,1.1,15.6,9.9,15.6,23.2c0,13.2-4.6,19.1-13,25.1"/>
					<path class="st67" d="M53.2,9.6c2.5,1.1,15.5,9.9,15.5,23.2c0,13.2-4.5,19.1-13,25.1"/>
					<path class="st68" d="M53.2,9.6c2.5,1.1,15.5,9.9,15.5,23.1c0,13.2-4.5,19.1-13,25.1"/>
					<path class="st69" d="M53.2,9.7c2.5,1.1,15.5,9.9,15.5,23.1c0,13.1-4.5,19.1-13,25.1"/>
					<path class="st70" d="M53.2,9.7c2.5,1.1,15.5,9.9,15.5,23.1c0,13.1-4.5,19.1-13,25.1"/>
					<path class="st71" d="M53.2,9.7c2.5,1.1,15.5,9.9,15.5,23.1c0,13.1-4.5,19-13,25"/>
					<path class="st72" d="M53.2,9.7c2.5,1.1,15.5,9.9,15.5,23.1c0,13.1-4.5,19-13,25"/>
					<path class="st73" d="M53.2,9.7c2.5,1.1,15.5,9.9,15.5,23c0,13.1-4.5,19-12.9,25"/>
					<path class="st74" d="M53.2,9.8c2.5,1.1,15.5,9.9,15.5,23c0,13.1-4.5,19-12.9,25"/>
					<path class="st75" d="M53.2,9.8c2.5,1.1,15.4,9.9,15.4,23c0,13.1-4.5,19-12.9,25"/>
					<path class="st76" d="M53.1,9.8c2.5,1.1,15.4,9.9,15.4,23c0,13.1-4.5,19-12.9,24.9"/>
					<path class="st77" d="M53.1,9.8c2.5,1.1,15.4,9.8,15.4,23c0,13.1-4.5,18.9-12.9,24.9"/>
					<path class="st78" d="M53.1,9.8c2.5,1.1,15.4,9.8,15.4,23c0,13-4.5,18.9-12.9,24.9"/>
					<path class="st79" d="M53.1,9.9c2.5,1.1,15.4,9.8,15.4,22.9c0,13-4.5,18.9-12.9,24.9"/>
					<path class="st80" d="M53.1,9.9c2.5,1.1,15.4,9.8,15.4,22.9c0,13-4.5,18.9-12.9,24.9"/>
					<path class="st81" d="M53.1,9.9c2.5,1.1,15.4,9.8,15.4,22.9c0,13-4.5,18.9-12.9,24.9"/>
					<path class="st82" d="M53.1,9.9c2.5,1.1,15.4,9.8,15.4,22.9c0,13-4.4,18.9-12.9,24.8"/>
					<path class="st83" d="M53.1,9.9c2.5,1.1,15.3,9.8,15.3,22.9c0,13-4.4,18.8-12.9,24.8"/>
					<path class="st84" d="M53.1,10c2.5,1.1,15.3,9.8,15.3,22.8c0,13-4.4,18.8-12.8,24.8"/>
					<path class="st85" d="M53.1,10c2.5,1.1,15.3,9.8,15.3,22.8c0,13-4.4,18.8-12.8,24.8"/>
					<path class="st86" d="M53.1,10c2.4,1.1,15.3,9.8,15.3,22.8c0,13-4.4,18.8-12.8,24.8"/>
					<path class="st87" d="M53,10c2.4,1.1,15.3,9.8,15.3,22.8c0,12.9-4.4,18.8-12.8,24.8"/>
					<path class="st88" d="M53,10c2.4,1.1,15.3,9.8,15.3,22.8c0,12.9-4.4,18.8-12.8,24.7"/>
					<path class="st89" d="M53,10.1c2.4,1.1,15.3,9.8,15.3,22.7c0,12.9-4.4,18.8-12.8,24.7"/>
					<path class="st90" d="M53,10.1c2.4,1.1,15.3,9.8,15.3,22.7c0,12.9-4.4,18.7-12.8,24.7"/>
					<path class="st91" d="M53,10.1c2.4,1.1,15.2,9.7,15.2,22.7c0,12.9-4.4,18.7-12.8,24.7"/>
					<path class="st92" d="M53,10.1c2.4,1.1,15.2,9.7,15.2,22.7c0,12.9-4.4,18.7-12.8,24.7"/>
					<path class="st93" d="M53,10.1c2.4,1.1,15.2,9.7,15.2,22.7c0,12.9-4.4,18.7-12.8,24.7"/>
					<path class="st94" d="M53,10.2c2.4,1.1,15.2,9.7,15.2,22.6c0,12.9-4.4,18.7-12.8,24.6"/>
					<path class="st95" d="M53,10.2c2.4,1.1,15.2,9.7,15.2,22.6c0,12.9-4.4,18.7-12.7,24.6"/>
					<path class="st96" d="M53,10.2c2.4,1.1,15.2,9.7,15.2,22.6c0,12.8-4.4,18.6-12.7,24.6"/>
					<path class="st97" d="M53,10.2c2.4,1.1,15.2,9.7,15.2,22.6c0,12.8-4.4,18.6-12.7,24.6"/>
					<path class="st98" d="M53,10.2c2.4,1.1,15.2,9.7,15.2,22.6c0,12.8-4.3,18.6-12.7,24.6"/>
					<path class="st99" d="M52.9,10.2c2.4,1.1,15.1,9.7,15.1,22.5c0,12.8-4.3,18.6-12.7,24.5"/>
					<path class="st100" d="M52.9,10.3c2.4,1.1,15.1,9.7,15.1,22.5c0,12.8-4.3,18.6-12.7,24.5"/>
					<path class="st101" d="M52.9,10.3C55.3,11.4,68,20,68,32.8c0,12.8-4.3,18.6-12.7,24.5"/>
					<path class="st102" d="M52.9,10.3C55.3,11.4,68,20,68,32.8c0,12.8-4.3,18.5-12.7,24.5"/>
					<path class="st103" d="M52.9,10.3C55.3,11.4,68,20,68,32.8c0,12.8-4.3,18.5-12.7,24.5"/>
					<path class="st104" d="M52.9,10.3C55.3,11.4,68,20,68,32.8c0,12.8-4.3,18.5-12.7,24.5"/>
					<path class="st105" d="M52.9,10.4C55.3,11.5,68,20,68,32.8c0,12.7-4.3,18.5-12.7,24.4"/>
					<path class="st106" d="M52.9,10.4c2.4,1.1,15.1,9.6,15.1,22.4c0,12.7-4.3,18.5-12.6,24.4"/>
					<path class="st107" d="M52.9,10.4c2.4,1.1,15,9.6,15,22.4c0,12.7-4.3,18.5-12.6,24.4"/>
					<path class="st108" d="M52.9,10.4c2.4,1.1,15,9.6,15,22.4c0,12.7-4.3,18.5-12.6,24.4"/>
					<path class="st109" d="M52.9,10.4c2.4,1.1,15,9.6,15,22.4c0,12.7-4.3,18.4-12.6,24.4"/>
					<path class="st110" d="M52.8,10.5c2.4,1.1,15,9.6,15,22.3c0,12.7-4.3,18.4-12.6,24.4"/>
					<path class="st111" d="M52.8,10.5c2.4,1.1,15,9.6,15,22.3c0,12.7-4.3,18.4-12.6,24.3"/>
					<path class="st112" d="M52.8,10.5c2.4,1.1,15,9.6,15,22.3c0,12.7-4.3,18.4-12.6,24.3"/>
					<path class="st113" d="M52.8,10.5c2.4,1.1,15,9.6,15,22.3c0,12.7-4.2,18.4-12.6,24.3"/>
					<path class="st114" d="M52.8,10.5c2.4,1.1,15,9.6,15,22.3c0,12.6-4.2,18.4-12.6,24.3"/>
					<path class="st115" d="M52.8,10.6c2.4,1.1,14.9,9.6,14.9,22.2c0,12.6-4.2,18.3-12.6,24.3"/>
					<path class="st116" d="M52.8,10.6c2.4,1.1,14.9,9.6,14.9,22.2c0,12.6-4.2,18.3-12.6,24.3"/>
					<path class="st117" d="M52.8,10.6c2.4,1.1,14.9,9.6,14.9,22.2c0,12.6-4.2,18.3-12.6,24.2"/>
					<path class="st118" d="M52.8,10.6c2.4,1.1,14.9,9.5,14.9,22.2c0,12.6-4.2,18.3-12.5,24.2"/>
					<path class="st119" d="M52.8,10.6c2.4,1.1,14.9,9.5,14.9,22.2c0,12.6-4.2,18.3-12.5,24.2"/>
					<path class="st120" d="M52.8,10.7c2.3,1.1,14.9,9.5,14.9,22.1c0,12.6-4.2,18.3-12.5,24.2"/>
					<path class="st121" d="M52.8,10.7c2.3,1.1,14.9,9.5,14.9,22.1c0,12.6-4.2,18.3-12.5,24.2"/>
					<path class="st122" d="M52.7,10.7c2.3,1.1,14.9,9.5,14.9,22.1c0,12.6-4.2,18.2-12.5,24.2"/>
					<path class="st123" d="M52.7,10.7c2.3,1.1,14.8,9.5,14.8,22.1c0,12.6-4.2,18.2-12.5,24.1"/>
					<path class="st124" d="M52.7,10.7c2.3,1.1,14.8,9.5,14.8,22.1c0,12.5-4.2,18.2-12.5,24.1"/>
					<path class="st125" d="M52.7,10.8c2.3,1.1,14.8,9.5,14.8,22c0,12.5-4.2,18.2-12.5,24.1"/>
					<path class="st126" d="M52.7,10.8c2.3,1.1,14.8,9.5,14.8,22c0,12.5-4.2,18.2-12.5,24.1"/>
					<path class="st127" d="M52.7,10.8c2.3,1.1,14.8,9.5,14.8,22c0,12.5-4.2,18.2-12.5,24.1"/>
					<path class="st128" d="M52.7,10.8c2.3,1.1,14.8,9.5,14.8,22c0,12.5-4.1,18.1-12.5,24"/>
					<path class="st129" d="M52.7,10.8c2.3,1.1,14.8,9.5,14.8,22c0,12.5-4.1,18.1-12.4,24"/>
					<path class="st130" d="M52.7,10.8c2.3,1.1,14.8,9.5,14.8,21.9c0,12.5-4.1,18.1-12.4,24"/>
					<path class="st131" d="M52.7,10.9c2.3,1.1,14.7,9.5,14.7,21.9c0,12.5-4.1,18.1-12.4,24"/>
					<path class="st132" d="M52.7,10.9c2.3,1.1,14.7,9.4,14.7,21.9c0,12.5-4.1,18.1-12.4,24"/>
					<path class="st133" d="M52.6,10.9C55,12,67.4,20.3,67.4,32.8c0,12.4-4.1,18.1-12.4,24"/>
					<path class="st134" d="M52.6,10.9c2.3,1.1,14.7,9.4,14.7,21.9c0,12.4-4.1,18-12.4,23.9"/>
					<path class="st135" d="M52.6,10.9c2.3,1,14.7,9.4,14.7,21.8c0,12.4-4.1,18-12.4,23.9"/>
					<path class="st136" d="M52.6,11c2.3,1,14.7,9.4,14.7,21.8c0,12.4-4.1,18-12.4,23.9"/>
					<path class="st137" d="M52.6,11c2.3,1,14.7,9.4,14.7,21.8c0,12.4-4.1,18-12.4,23.9"/>
					<path class="st138" d="M52.6,11c2.3,1,14.7,9.4,14.7,21.8c0,12.4-4.1,18-12.4,23.9"/>
					<path class="st139" d="M52.6,11c2.3,1,14.6,9.4,14.6,21.8c0,12.4-4.1,18-12.4,23.9"/>
					<path class="st140" d="M52.6,11c2.3,1,14.6,9.4,14.6,21.8s-4.1,18-12.3,23.8"/>
					<path class="st139" d="M52.6,11.1c2.3,1,14.6,9.4,14.6,21.7c0,12.4-4.1,17.9-12.3,23.8"/>
					<path class="st138" d="M52.6,11.1c2.3,1,14.6,9.4,14.6,21.7c0,12.4-4.1,17.9-12.3,23.8"/>
					<path class="st137" d="M52.6,11.1c2.3,1,14.6,9.4,14.6,21.7c0,12.3-4.1,17.9-12.3,23.8"/>
					<path class="st136" d="M52.6,11.1c2.3,1,14.6,9.4,14.6,21.7c0,12.3-4.1,17.9-12.3,23.8"/>
					<path class="st135" d="M52.5,11.1c2.3,1,14.6,9.3,14.6,21.7c0,12.3-4.1,17.9-12.3,23.8"/>
					<path class="st134" d="M52.5,11.2c2.3,1,14.6,9.3,14.6,21.6c0,12.3-4.1,17.9-12.3,23.7"/>
					<path class="st133" d="M52.5,11.2c2.3,1,14.6,9.3,14.6,21.6c0,12.3-4,17.9-12.3,23.7"/>
					<path class="st132" d="M52.5,11.2c2.3,1,14.5,9.3,14.5,21.6c0,12.3-4,17.9-12.3,23.7"/>
					<path class="st131" d="M52.5,11.2c2.3,1,14.5,9.3,14.5,21.6c0,12.3-4,17.8-12.3,23.7"/>
					<path class="st130" d="M52.5,11.2c2.2,1,14.5,9.3,14.5,21.6c0,12.3-4,17.8-12.3,23.7"/>
					<path class="st129" d="M52.5,11.3c2.2,1,14.5,9.3,14.5,21.5c0,12.3-4,17.8-12.2,23.6"/>
					<path class="st128" d="M52.5,11.3c2.2,1,14.5,9.3,14.5,21.5c0,12.3-4,17.8-12.2,23.6"/>
					<path class="st127" d="M52.5,11.3c2.2,1,14.5,9.3,14.5,21.5c0,12.3-4,17.8-12.2,23.6"/>
					<path class="st126" d="M52.5,11.3c2.2,1,14.5,9.3,14.5,21.5c0,12.3-4,17.8-12.2,23.6"/>
					<path class="st125" d="M52.5,11.3c2.2,1,14.5,9.3,14.5,21.5c0,12.3-4,17.8-12.2,23.6"/>
					<path class="st124" d="M52.4,11.4c2.2,1,14.4,9.3,14.4,21.4c0,12.3-4,17.7-12.2,23.6"/>
					<path class="st123" d="M52.4,11.4c2.2,1,14.4,9.3,14.4,21.4c0,12.3-4,17.7-12.2,23.5"/>
					<path class="st122" d="M52.4,11.4c2.2,1,14.4,9.2,14.4,21.4c0,12.2-4,17.7-12.2,23.5"/>
					<path class="st121" d="M52.4,11.4c2.2,1,14.4,9.2,14.4,21.4c0,12.2-4,17.7-12.2,23.5"/>
					<path class="st120" d="M52.4,11.4c2.2,1,14.4,9.2,14.4,21.4c0,12.2-4,17.7-12.2,23.5"/>
					<path class="st119" d="M52.4,11.4c2.2,1,14.4,9.2,14.4,21.3c0,12.2-4,17.7-12.2,23.5"/>
					<path class="st118" d="M52.4,11.5c2.2,1,14.4,9.2,14.4,21.3c0,12.2-4,17.7-12.1,23.5"/>
					<path class="st117" d="M52.4,11.5c2.2,1,14.4,9.2,14.4,21.3c0,12.2-4,17.7-12.1,23.4"/>
					<path class="st116" d="M52.4,11.5c2.2,1,14.3,9.2,14.3,21.3c0,12.2-4,17.6-12.1,23.4"/>
					<path class="st115" d="M52.4,11.5c2.2,1,14.3,9.2,14.3,21.3c0,12.2-4,17.6-12.1,23.4"/>
					<path class="st114" d="M52.4,11.5c2.2,1,14.3,9.2,14.3,21.2c0,12.2-4,17.6-12.1,23.4"/>
					<path class="st113" d="M52.4,11.6c2.2,1,14.3,9.2,14.3,21.2c0,12.2-4,17.6-12.1,23.4"/>
					<path class="st112" d="M52.3,11.6c2.2,1,14.3,9.2,14.3,21.2c0,12.2-4,17.6-12.1,23.4"/>
					<path class="st111" d="M52.3,11.6c2.2,1,14.3,9.2,14.3,21.2c0,12.2-4,17.6-12.1,23.3"/>
					<path class="st110" d="M52.3,11.6c2.2,1,14.3,9.2,14.3,21.2c0,12.2-4,17.6-12.1,23.3"/>
					<path class="st109" d="M52.3,11.6c2.2,1,14.3,9.2,14.3,21.2c0,12.2-4,17.6-12.1,23.3"/>
					<path class="st108" d="M52.3,11.7c2.2,1,14.2,9.1,14.2,21.1c0,12.2-4,17.5-12.1,23.3"/>
					<path class="st107" d="M52.3,11.7c2.1,1,14.2,9.1,14.2,21.1c0,12.1-4,17.5-12.1,23.3"/>
					<path class="st106" d="M52.3,11.7c2.1,1,14.2,9.1,14.2,21.1c0,12.1-4,17.5-12,23.2"/>
					<path class="st105" d="M52.3,11.7c2.1,1,14.2,9.1,14.2,21.1c0,12.1-4,17.5-12,23.2"/>
					<path class="st104" d="M52.3,11.7c2.1,1,14.2,9.1,14.2,21.1c0,12.1-4,17.5-12,23.2"/>
					<path class="st103" d="M52.3,11.8c2.1,1,14.2,9.1,14.2,21c0,12.1-4,17.5-12,23.2"/>
					<path class="st102" d="M52.3,11.8c2.1,1,14.2,9.1,14.2,21c0,12.1-4,17.5-12,23.2"/>
					<path class="st101" d="M52.2,11.8c2.1,1,14.2,9.1,14.2,21c0,12.1-4,17.5-12,23.2"/>
					<path class="st100" d="M52.2,11.8c2.1,1,14.1,9.1,14.1,21c0,12.1-4,17.4-12,23.1"/>
					<path class="st99" d="M52.2,11.8c2.1,1,14.1,9.1,14.1,21c0,12.1-4,17.4-12,23.1"/>
					<path class="st98" d="M52.2,11.9c2.1,1,14.1,9.1,14.1,20.9c0,12.1-4,17.4-12,23.1"/>
					<path class="st97" d="M52.2,11.9c2.1,1,14.1,9.1,14.1,20.9c0,12.1-4,17.4-12,23.1"/>
					<path class="st96" d="M52.2,11.9c2.1,1,14.1,9.1,14.1,20.9c0,12.1-4,17.4-12,23.1"/>
					<path class="st95" d="M52.2,11.9c2.1,1,14.1,9,14.1,20.9c0,12.1-4,17.4-11.9,23.1"/>
					<path class="st94" d="M52.2,11.9c2.1,1,14.1,9,14.1,20.9c0,12.1-4,17.4-11.9,23"/>
					<path class="st93" d="M52.2,12c2.1,1,14.1,9,14.1,20.8c0,12.1-4,17.4-11.9,23"/>
					<path class="st92" d="M52.2,12c2.1,0.9,14,9,14,20.8c0,12-4,17.3-11.9,23"/>
					<path class="st91" d="M52.2,12c2.1,0.9,14,9,14,20.8c0,12-4,17.3-11.9,23"/>
					<path class="st90" d="M52.2,12c2.1,0.9,14,9,14,20.8c0,12-3.9,17.3-11.9,23"/>
					<path class="st89" d="M52.1,12c2.1,0.9,14,9,14,20.8c0,12-3.9,17.3-11.9,23"/>
					<path class="st88" d="M52.1,12c2.1,0.9,14,9,14,20.7c0,12-3.9,17.3-11.9,22.9"/>
					<path class="st87" d="M52.1,12.1c2.1,0.9,14,9,14,20.7c0,12-3.9,17.3-11.9,22.9"/>
					<path class="st86" d="M52.1,12.1c2.1,0.9,14,9,14,20.7c0,12-3.9,17.3-11.9,22.9"/>
					<path class="st85" d="M52.1,12.1c2.1,0.9,14,9,14,20.7c0,12-3.9,17.3-11.9,22.9"/>
					<path class="st84" d="M52.1,12.1c2,0.9,13.9,9,13.9,20.7c0,12-3.9,17.2-11.8,22.9"/>
					<path class="st83" d="M52.1,12.1c2,0.9,13.9,9,13.9,20.6c0,12-3.9,17.2-11.8,22.8"/>
					<path class="st82" d="M52.1,12.2c2,0.9,13.9,8.9,13.9,20.6c0,12-3.9,17.2-11.8,22.8"/>
					<path class="st81" d="M52.1,12.2c2,0.9,13.9,8.9,13.9,20.6c0,12-3.9,17.2-11.8,22.8"/>
					<path class="st80" d="M52.1,12.2c2,0.9,13.9,8.9,13.9,20.6c0,12-3.9,17.2-11.8,22.8"/>
					<path class="st79" d="M52.1,12.2c2,0.9,13.9,8.9,13.9,20.6c0,12-3.9,17.2-11.8,22.8"/>
					<path class="st78" d="M52,12.2c2,0.9,13.9,8.9,13.9,20.6c0,12-3.9,17.2-11.8,22.8"/>
					<path class="st77" d="M52,12.3c2,0.9,13.9,8.9,13.9,20.5c0,11.9-3.9,17.2-11.8,22.7"/>
					<path class="st76" d="M52,12.3c2,0.9,13.8,8.9,13.8,20.5c0,11.9-3.9,17.1-11.8,22.7"/>
					<path class="st75" d="M52,12.3c2,0.9,13.8,8.9,13.8,20.5c0,11.9-3.9,17.1-11.8,22.7"/>
					<path class="st74" d="M52,12.3c2,0.9,13.8,8.9,13.8,20.5c0,11.9-3.9,17.1-11.8,22.7"/>
					<path class="st73" d="M52,12.3c2,0.9,13.8,8.9,13.8,20.5c0,11.9-3.9,17.1-11.7,22.7"/>
					<path class="st72" d="M52,12.4c2,0.9,13.8,8.9,13.8,20.4c0,11.9-3.9,17.1-11.7,22.7"/>
					<path class="st141" d="M52,12.4c2,0.9,13.8,8.9,13.8,20.4c0,11.9-3.9,17.1-11.7,22.6"/>
					<path class="st70" d="M52,12.4c2,0.9,13.8,8.9,13.8,20.4c0,11.9-3.9,17.1-11.7,22.6"/>
					<path class="st69" d="M52,12.4c2,0.9,13.8,8.8,13.8,20.4c0,11.9-3.9,17.1-11.7,22.6"/>
					<path class="st68" d="M52,12.4c2,0.9,13.7,8.8,13.7,20.4c0,11.9-3.9,17-11.7,22.6"/>
					<path class="st67" d="M52,12.5c2,0.9,13.7,8.8,13.7,20.3c0,11.9-3.9,17-11.7,22.6"/>
					<path class="st66" d="M51.9,12.5c2,0.9,13.7,8.8,13.7,20.3c0,11.9-3.9,17-11.7,22.6"/>
					<path class="st65" d="M51.9,12.5c2,0.9,13.7,8.8,13.7,20.3c0,11.9-3.9,17-11.7,22.5"/>
					<path class="st64" d="M51.9,12.5c2,0.9,13.7,8.8,13.7,20.3c0,11.9-3.9,17-11.7,22.5"/>
					<path class="st63" d="M51.9,12.5c2,0.9,13.7,8.8,13.7,20.3c0,11.9-3.9,17-11.7,22.5"/>
					<path class="st62" d="M51.9,12.6c2,0.9,13.7,8.8,13.7,20.2c0,11.8-3.9,17-11.6,22.5"/>
					<path class="st61" d="M51.9,12.6c1.9,0.9,13.7,8.8,13.7,20.2c0,11.8-3.9,17-11.6,22.5"/>
					<path class="st60" d="M51.9,12.6c1.9,0.9,13.6,8.8,13.6,20.2c0,11.8-3.9,16.9-11.6,22.4"/>
					<path class="st59" d="M51.9,12.6c1.9,0.9,13.6,8.8,13.6,20.2c0,11.8-3.9,16.9-11.6,22.4"/>
					<path class="st58" d="M51.9,12.6c1.9,0.9,13.6,8.8,13.6,20.2c0,11.8-3.9,16.9-11.6,22.4"/>
					<path class="st57" d="M51.9,12.6c1.9,0.9,13.6,8.8,13.6,20.1c0,11.8-3.9,16.9-11.6,22.4"/>
					<path class="st56" d="M51.9,12.7c1.9,0.9,13.6,8.8,13.6,20.1c0,11.8-3.9,16.9-11.6,22.4"/>
					<path class="st55" d="M51.8,12.7c1.9,0.9,13.6,8.7,13.6,20.1c0,11.8-3.9,16.9-11.6,22.4"/>
					<path class="st54" d="M51.8,12.7c1.9,0.9,13.6,8.7,13.6,20.1c0,11.8-3.9,16.9-11.6,22.3"/>
					<path class="st53" d="M51.8,12.7c1.9,0.9,13.6,8.7,13.6,20.1c0,11.8-3.9,16.8-11.6,22.3"/>
					<path class="st52" d="M51.8,12.7c1.9,0.9,13.5,8.7,13.5,20c0,11.8-3.9,16.8-11.6,22.3"/>
					<path class="st51" d="M51.8,12.8c1.9,0.9,13.5,8.7,13.5,20c0,11.8-3.9,16.8-11.6,22.3"/>
					<path class="st50" d="M51.8,12.8c1.9,0.9,13.5,8.7,13.5,20c0,11.8-3.9,16.8-11.5,22.3"/>
					<path class="st49" d="M51.8,12.8c1.9,0.9,13.5,8.7,13.5,20c0,11.8-3.9,16.8-11.5,22.3"/>
					<path class="st48" d="M51.8,12.8c1.9,0.9,13.5,8.7,13.5,20c0,11.8-3.9,16.8-11.5,22.2"/>
					<path class="st47" d="M51.8,12.8c1.9,0.9,13.5,8.7,13.5,20c0,11.7-3.8,16.8-11.5,22.2"/>
					<path class="st46" d="M51.8,12.9c1.9,0.9,13.5,8.7,13.5,19.9c0,11.7-3.8,16.8-11.5,22.2"/>
					<path class="st45" d="M51.8,12.9c1.9,0.9,13.5,8.7,13.5,19.9c0,11.7-3.8,16.7-11.5,22.2"/>
					<path class="st44" d="M51.8,12.9c1.9,0.9,13.4,8.7,13.4,19.9c0,11.7-3.8,16.7-11.5,22.2"/>
					<path class="st43" d="M51.7,12.9c1.9,0.9,13.4,8.7,13.4,19.9c0,11.7-3.8,16.7-11.5,22.2"/>
					<path class="st42" d="M51.7,12.9c1.9,0.8,13.4,8.6,13.4,19.9c0,11.7-3.8,16.7-11.5,22.1"/>
					<path class="st41" d="M51.7,13c1.9,0.8,13.4,8.6,13.4,19.8c0,11.7-3.8,16.7-11.5,22.1"/>
					<path class="st40" d="M51.7,13c1.9,0.8,13.4,8.6,13.4,19.8c0,11.7-3.8,16.7-11.5,22.1"/>
					<path class="st39" d="M51.7,13c1.9,0.8,13.4,8.6,13.4,19.8c0,11.7-3.8,16.7-11.4,22.1"/>
					<path class="st38" d="M51.7,13c1.9,0.8,13.4,8.6,13.4,19.8c0,11.7-3.8,16.7-11.4,22.1"/>
					<path class="st37" d="M51.7,13c1.8,0.8,13.4,8.6,13.4,19.8c0,11.7-3.8,16.6-11.4,22"/>
					<path class="st36" d="M51.7,13.1C53.5,13.9,65,21.7,65,32.8c0,11.7-3.8,16.6-11.4,22"/>
					<path class="st35" d="M51.7,13.1C53.5,13.9,65,21.7,65,32.8c0,11.7-3.8,16.6-11.4,22"/>
					<path class="st34" d="M51.7,13.1C53.5,13.9,65,21.7,65,32.8c0,11.7-3.8,16.6-11.4,22"/>
					<path class="st33" d="M51.7,13.1C53.5,13.9,65,21.7,65,32.8c0,11.7-3.8,16.6-11.4,22"/>
					<path class="st32" d="M51.6,13.1c1.8,0.8,13.3,8.6,13.3,19.7c0,11.6-3.8,16.6-11.4,22"/>
					<path class="st31" d="M51.6,13.2c1.8,0.8,13.3,8.6,13.3,19.6c0,11.6-3.8,16.6-11.4,21.9"/>
					<path class="st30" d="M51.6,13.2c1.8,0.8,13.3,8.6,13.3,19.6c0,11.6-3.8,16.6-11.4,21.9"/>
					<path class="st29" d="M51.6,13.2c1.8,0.8,13.3,8.5,13.3,19.6c0,11.6-3.8,16.5-11.4,21.9"/>
					<path class="st28" d="M51.6,13.2c1.8,0.8,13.2,8.5,13.2,19.6c0,11.6-3.8,16.5-11.3,21.9"/>
					<path class="st27" d="M51.6,13.2c1.8,0.8,13.2,8.5,13.2,19.6c0,11.6-3.8,16.5-11.3,21.9"/>
					<path class="st26" d="M51.6,13.2c1.8,0.8,13.2,8.5,13.2,19.5c0,11.6-3.8,16.5-11.3,21.9"/>
					<path class="st25" d="M51.6,13.3c1.8,0.8,13.2,8.5,13.2,19.5c0,11.6-3.8,16.5-11.3,21.8"/>
					<path class="st24" d="M51.6,13.3c1.8,0.8,13.2,8.5,13.2,19.5c0,11.6-3.8,16.5-11.3,21.8"/>
					<path class="st23" d="M51.6,13.3c1.8,0.8,13.2,8.5,13.2,19.5c0,11.6-3.8,16.5-11.3,21.8"/>
					<path class="st22" d="M51.6,13.3c1.8,0.8,13.2,8.5,13.2,19.5c0,11.6-3.8,16.5-11.3,21.8"/>
					<path class="st21" d="M51.6,13.3c1.8,0.8,13.2,8.5,13.2,19.4c0,11.6-3.8,16.4-11.3,21.8"/>
					<path class="st20" d="M51.5,13.4c1.8,0.8,13.1,8.5,13.1,19.4c0,11.6-3.8,16.4-11.3,21.8"/>
					<path class="st19" d="M51.5,13.4c1.8,0.8,13.1,8.5,13.1,19.4c0,11.6-3.8,16.4-11.3,21.7"/>
					<path class="st18" d="M51.5,13.4c1.8,0.8,13.1,8.5,13.1,19.4c0,11.6-3.8,16.4-11.3,21.7"/>
					<path class="st17" d="M51.5,13.4c1.8,0.8,13.1,8.5,13.1,19.4c0,11.5-3.8,16.4-11.2,21.7"/>
					<path class="st16" d="M51.5,13.4c1.8,0.8,13.1,8.4,13.1,19.4c0,11.5-3.8,16.4-11.2,21.7"/>
					<path class="st15" d="M51.5,13.5c1.8,0.8,13.1,8.4,13.1,19.3c0,11.5-3.8,16.4-11.2,21.7"/>
					<path class="st14" d="M51.5,13.5c1.7,0.8,13.1,8.4,13.1,19.3c0,11.5-3.8,16.4-11.2,21.7"/>
					<path class="st13" d="M51.5,13.5c1.7,0.8,13.1,8.4,13.1,19.3c0,11.5-3.8,16.3-11.2,21.6"/>
					<path class="st12" d="M51.5,13.5c1.7,0.8,13,8.4,13,19.3c0,11.5-3.8,16.3-11.2,21.6"/>
					<path class="st11" d="M51.5,13.5c1.7,0.8,13,8.4,13,19.3c0,11.5-3.8,16.3-11.2,21.6"/>
					<path class="st10" d="M51.5,13.6c1.7,0.8,13,8.4,13,19.2c0,11.5-3.8,16.3-11.2,21.6"/>
					<path class="st9" d="M51.4,13.6c1.7,0.8,13,8.4,13,19.2c0,11.5-3.8,16.3-11.2,21.6"/>
					<path class="st8" d="M51.4,13.6c1.7,0.8,13,8.4,13,19.2c0,11.5-3.8,16.3-11.2,21.5"/>
					<path class="st7" d="M51.4,13.6c1.7,0.8,13,8.4,13,19.2c0,11.5-3.8,16.3-11.2,21.5"/>
					<path class="st6" d="M51.4,13.6c1.7,0.8,13,8.4,13,19.2c0,11.5-3.8,16.3-11.1,21.5"/>
					<path class="st5" d="M51.4,13.7c1.7,0.8,13,8.4,13,19.1c0,11.5-3.7,16.2-11.1,21.5"/>
					<path class="st4" d="M51.4,13.7c1.7,0.8,12.9,8.4,12.9,19.1c0,11.5-3.7,16.2-11.1,21.5"/>
					<path class="st3" d="M51.4,13.7c1.7,0.8,12.9,8.4,12.9,19.1c0,11.5-3.7,16.2-11.1,21.5"/>
					<path class="st2" d="M53.2,54.2c7.4-5.2,11.1-10,11.1-21.4c0-10.7-11.2-18.3-12.9-19.1"/>
				</g>
			</g>
			<g>
				<defs>
					<path id="SVGID_00000145754488922110141470000008438775829136780948_" d="M28.6,55.6c0,0.1,0,0.2-0.1,0.3
						c-0.1,0.1-0.1,0.2-0.3,0.2c-0.1,0-0.1,0-0.2,0c-0.1,0-0.2,0-0.3,0c-0.4-0.1-0.7-0.3-0.9-0.5c-0.1-0.1-0.2-0.3-0.3-0.4
						c0-0.1-0.1-0.2-0.1-0.2c0-0.1,0-0.2-0.1-0.2c-0.1-0.3-0.1-0.7-0.2-1c0-0.3-0.1-0.6-0.2-0.8c0.2,0,0.4,0.1,0.6,0.2
						c0.2,0.1,0.4,0.2,0.6,0.4c0.1,0,0.1,0.1,0.2,0.2c0.1,0.1,0.1,0.1,0.2,0.2c0.1,0.1,0.2,0.3,0.3,0.4c0,0,0.1,0.1,0.1,0.1
						c0,0,0.1,0.1,0.1,0.1c0.1,0.1,0.1,0.2,0.1,0.2c0.1,0.2,0.2,0.3,0.2,0.5C28.5,55.3,28.6,55.5,28.6,55.6z M27.7,56.8
						c0-0.1-0.1-0.2-0.2-0.3c-0.2-0.4-0.5-0.6-0.9-0.7c-0.2-0.1-0.3-0.1-0.5-0.1c-0.1,0-0.2,0-0.3,0c-0.1,0-0.2,0-0.3,0
						c-0.3,0-0.7,0-1,0.1c-0.3,0-0.6,0-0.9-0.1c0.1,0.2,0.3,0.4,0.4,0.7c0.2,0.2,0.4,0.4,0.6,0.5c0.1,0.1,0.1,0.1,0.2,0.2
						c0.1,0,0.2,0.1,0.3,0.1c0.2,0.1,0.4,0.2,0.6,0.2c0.1,0,0.1,0,0.2,0c0.1,0,0.1,0,0.2,0c0.1,0,0.2,0,0.3,0.1c0.2,0,0.4,0,0.6,0
						c0.2,0,0.3,0,0.5-0.1c0.1,0,0.2-0.1,0.3-0.2c0.1-0.1,0.1-0.2,0-0.3C27.7,56.9,27.7,56.8,27.7,56.8z M23.7,51.9
						c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0,0.3c0,0.2,0,0.3,0.1,0.5c0,0.1,0.1,0.2,0.1,0.2c0,0.1,0.1,0.2,0.1,0.2
						c0.1,0.1,0.2,0.3,0.3,0.4c0.1,0.1,0.1,0.1,0.2,0.2c0,0,0.1,0,0.1,0.1c0,0,0.1,0,0.1,0.1c0.8,0.3,1-0.2,0.9-0.9
						c0-0.1,0-0.2-0.1-0.2c0-0.1-0.1-0.2-0.1-0.3c-0.1-0.2-0.1-0.3-0.2-0.5c0-0.1-0.1-0.2-0.1-0.2c0-0.1-0.1-0.2-0.1-0.2
						c-0.1-0.1-0.2-0.3-0.3-0.4c0,0-0.1-0.1-0.1-0.1c0,0-0.1-0.1-0.1-0.1c-0.1-0.1-0.2-0.1-0.3-0.2c-0.2-0.1-0.4-0.2-0.6-0.3
						c0.1,0.2,0.1,0.5,0.1,0.8C23.7,51.5,23.7,51.7,23.7,51.9z M24.6,54.3c0,0-0.1-0.1-0.1-0.1c0,0-0.1-0.1-0.1-0.1
						c-0.1-0.1-0.1-0.2-0.2-0.2c-0.1-0.1-0.3-0.2-0.4-0.3c-0.1,0-0.2-0.1-0.2-0.1c-0.1,0-0.2-0.1-0.2-0.1c-0.2,0-0.3-0.1-0.5-0.1
						c-0.1,0-0.2,0-0.3,0c-0.1,0-0.2,0-0.3,0c-0.2,0-0.3,0-0.5,0c-0.3,0-0.6-0.1-0.9-0.2c0.1,0.2,0.2,0.5,0.3,0.7
						c0.1,0.1,0.1,0.2,0.2,0.3c0,0.1,0.1,0.1,0.1,0.2c0,0,0.1,0.1,0.1,0.1c0.1,0.1,0.3,0.2,0.5,0.4c0.1,0.1,0.2,0.1,0.3,0.2
						c0.1,0.1,0.2,0.1,0.3,0.1c0.2,0.1,0.4,0.2,0.6,0.2c0.1,0,0.2,0.1,0.3,0.1c0.1,0,0.2,0,0.3,0C24.6,55.4,25,55.1,24.6,54.3z
						 M21.5,49.2c0,0.2,0,0.3,0,0.5c0,0.2,0,0.3,0,0.5c0,0.2,0.1,0.3,0.1,0.5c0.1,0.2,0.2,0.3,0.3,0.4c0.1,0.1,0.3,0.2,0.5,0.3
						c0.2,0.1,0.4,0.1,0.5,0.1c0.1,0,0.2,0,0.3-0.1c0.1-0.1,0.1-0.2,0.1-0.3c0-0.1,0-0.3,0-0.4c0-0.2,0-0.3-0.1-0.5
						c0-0.1,0-0.2-0.1-0.3c0-0.1-0.1-0.2-0.1-0.3c-0.1-0.2-0.1-0.3-0.2-0.5c0,0,0-0.1-0.1-0.1c0,0,0-0.1-0.1-0.1
						c0-0.1-0.1-0.1-0.1-0.2c-0.1-0.1-0.1-0.2-0.2-0.3c-0.1-0.1-0.2-0.2-0.2-0.2c-0.2-0.2-0.3-0.3-0.5-0.4c0,0.2,0,0.5-0.1,0.8
						C21.5,48.9,21.5,49,21.5,49.2z M21.6,52.7c0.1,0,0.2,0,0.3-0.1c0.1-0.1,0.1-0.2,0.1-0.3c0-0.1,0-0.3-0.1-0.5
						c-0.1-0.2-0.2-0.4-0.3-0.5c-0.1-0.1-0.2-0.3-0.4-0.4c-0.1-0.1-0.3-0.2-0.4-0.2c-0.2-0.1-0.3-0.1-0.5-0.2
						c-0.2,0-0.3-0.1-0.5-0.1c-0.2,0-0.3-0.1-0.5-0.1c-0.3-0.1-0.6-0.2-0.8-0.3c0,0.3,0.1,0.5,0.2,0.7c0.1,0.1,0.1,0.2,0.2,0.4
						c0.1,0.1,0.1,0.2,0.2,0.3c0.1,0.1,0.1,0.1,0.2,0.2c0,0,0.1,0.1,0.1,0.1c0,0,0.1,0.1,0.1,0.1c0.2,0.1,0.3,0.3,0.5,0.4
						c0.1,0.1,0.2,0.1,0.3,0.2c0.1,0.1,0.2,0.1,0.3,0.1c0.2,0.1,0.4,0.1,0.5,0.2C21.3,52.7,21.5,52.7,21.6,52.7z M20.1,48.6
						c0.7,0.5,1,0.1,1.1-0.6c0,0,0-0.1,0-0.1c0,0,0-0.1,0-0.1c0-0.1,0-0.2,0-0.3c0-0.2,0-0.4-0.1-0.6c0,0,0-0.1,0-0.1
						c0,0,0-0.1,0-0.1c0-0.1,0-0.2-0.1-0.3c-0.1-0.2-0.1-0.3-0.2-0.5c-0.1-0.2-0.2-0.4-0.4-0.6c-0.1-0.1-0.2-0.2-0.2-0.2
						c-0.1-0.1-0.2-0.2-0.3-0.2c0,0.1,0,0.2-0.1,0.4c0,0.1-0.1,0.3-0.1,0.4c0,0.2-0.1,0.3-0.1,0.5c0,0.1,0,0.2-0.1,0.2
						c0,0.1,0,0.2-0.1,0.3c-0.1,0.3-0.1,0.7,0,1C19.6,48,19.8,48.4,20.1,48.6z M18.7,49.6c0,0,0.1,0,0.1,0.1
						c0.7,0.3,1.1,0.1,0.9-0.8c-0.1-0.4-0.3-0.7-0.5-1c-0.2-0.2-0.5-0.4-0.9-0.5c-0.1,0-0.2-0.1-0.2-0.1c-0.1,0-0.2-0.1-0.2-0.1
						c-0.2-0.1-0.3-0.1-0.5-0.2c-0.2-0.1-0.3-0.1-0.4-0.2c-0.1-0.1-0.3-0.1-0.4-0.2c0,0.1,0,0.3,0,0.4c0,0.1,0.1,0.3,0.1,0.4
						c0.1,0.3,0.2,0.5,0.3,0.7c0.1,0.2,0.2,0.3,0.3,0.5c0.1,0.1,0.1,0.2,0.2,0.2c0,0,0.1,0.1,0.1,0.1c0,0,0.1,0.1,0.1,0.1
						c0.2,0.1,0.3,0.3,0.5,0.4c0.1,0.1,0.2,0.1,0.3,0.2C18.6,49.5,18.7,49.5,18.7,49.6z M18,45c0,0.1,0.1,0.2,0.2,0.2
						c0.1,0.1,0.1,0.2,0.2,0.2c0.3,0.3,0.6,0.4,0.8,0.3c0.2-0.1,0.3-0.4,0.4-0.7c0-0.2,0.1-0.3,0.1-0.5c0,0,0-0.1,0-0.1
						c0,0,0-0.1,0-0.1c0-0.1,0-0.2,0-0.3c0-0.2,0-0.4-0.1-0.5c0-0.1,0-0.2-0.1-0.3c0-0.1,0-0.2-0.1-0.2c0-0.1-0.1-0.2-0.1-0.3
						c0-0.1-0.1-0.1-0.1-0.1c0,0-0.1-0.1-0.1-0.1c-0.1-0.2-0.3-0.4-0.4-0.5c0,0.2-0.2,0.5-0.3,0.8c-0.1,0.1-0.1,0.3-0.2,0.4
						c-0.1,0.2-0.1,0.3-0.2,0.5c-0.1,0.2-0.1,0.3-0.1,0.5c0,0.2,0,0.3,0,0.5C17.9,44.7,18,44.8,18,45z M17.7,46.4
						c0.2-0.1,0.3-0.3,0.3-0.8c0-0.1,0-0.2,0-0.3c0-0.1,0-0.2-0.1-0.3c-0.1-0.2-0.1-0.3-0.2-0.5c-0.1-0.1-0.2-0.3-0.4-0.4
						C17.2,44.1,17,44,16.9,44c-0.1-0.1-0.3-0.2-0.5-0.3c-0.2-0.1-0.3-0.2-0.5-0.2c-0.3-0.2-0.5-0.3-0.7-0.5c0,0.3,0,0.5,0,0.8
						c0,0.1,0,0.1,0,0.2c0,0.1,0,0.1,0,0.2c0,0.1,0.1,0.3,0.1,0.4c0,0.1,0.1,0.2,0.1,0.3c0,0.1,0.1,0.2,0.1,0.3
						c0.1,0.2,0.2,0.3,0.4,0.5c0.1,0.1,0.1,0.2,0.2,0.2c0,0,0.1,0.1,0.1,0.1c0,0,0.1,0.1,0.1,0.1c0.2,0.1,0.3,0.2,0.5,0.3
						C17.3,46.4,17.5,46.5,17.7,46.4z M18.3,41.8c0.1-0.2,0.1-0.3,0.1-0.5c0-0.2,0.1-0.4,0.1-0.6c0-0.2,0-0.4,0-0.5
						c0-0.2,0-0.3-0.1-0.5c0-0.1,0-0.1,0-0.2c0-0.1,0-0.1,0-0.2c0-0.1-0.1-0.2-0.1-0.3c0-0.1-0.1-0.2-0.2-0.3c0,0,0,0,0-0.1l0,0l0,0
						l0,0c0,0,0,0.1,0,0h0v0l0,0l0,0l0,0c0-0.1-0.1-0.1-0.1-0.2c0,0.1,0,0.1-0.1,0.2l0,0l0,0l0,0v0c0,0,0-0.1,0,0l0,0l0,0
						c0,0,0,0,0,0c0,0,0,0.1,0,0.1c-0.1,0.1-0.1,0.3-0.2,0.4c-0.1,0.1-0.2,0.3-0.3,0.4c0,0.1-0.1,0.1-0.1,0.2c0,0.1-0.1,0.1-0.1,0.2
						c-0.1,0.3-0.3,0.6-0.3,1c0,0.3,0.1,0.7,0.4,1C17.7,42.8,18.1,42.4,18.3,41.8z M15.6,42.7c0.6,0.4,1.1,0.4,1.1-0.5
						c0-0.4,0-0.8-0.2-1.1c-0.2-0.3-0.4-0.5-0.7-0.8c-0.1-0.1-0.1-0.1-0.2-0.2c-0.1-0.1-0.1-0.1-0.2-0.2c-0.1-0.1-0.3-0.2-0.4-0.3
						c-0.1-0.1-0.3-0.2-0.4-0.3c0,0-0.1-0.1-0.1-0.1c0,0,0,0,0,0l0,0l0,0c0,0,0,0,0,0v0l0,0l0,0l0,0c0-0.1-0.1-0.1-0.1-0.2
						c0,0.1,0,0.1,0,0.2l0,0l0,0l0,0l0,0v0c0,0,0,0,0,0l0,0l0,0l0,0c0,0,0,0.1,0,0.1c0,0.1,0,0.3,0,0.4c0,0.1,0,0.3,0,0.4
						c0,0.1,0,0.1,0,0.2c0,0.1,0,0.1,0,0.2c0,0.2,0.1,0.4,0.2,0.5c0.1,0.2,0.2,0.4,0.3,0.6c0.1,0.2,0.2,0.3,0.4,0.5
						C15.3,42.5,15.4,42.6,15.6,42.7z M16.6,38.9L16.6,38.9C16.6,39,16.6,39,16.6,38.9c0.2,0.1,0.3,0.1,0.4,0.1
						c0.1,0,0.2-0.1,0.3-0.2c0,0,0,0,0,0l0,0l0,0l0,0c0,0,0,0,0,0l0,0l0,0l0,0c0,0,0-0.1,0.1-0.1c0-0.1,0.1-0.1,0.1-0.2
						c0.1-0.1,0.1-0.3,0.2-0.5c0-0.1,0.1-0.2,0.1-0.3l0-0.1c0,0,0,0,0-0.1c0,0,0-0.1,0-0.1c0-0.2,0.1-0.4,0.1-0.5c0-0.1,0-0.2,0-0.2
						c0-0.1,0-0.2,0-0.2c0-0.2-0.1-0.5-0.1-0.7c0-0.1-0.1-0.2-0.1-0.3c0-0.1-0.1-0.2-0.1-0.3c-0.1,0.1-0.1,0.2-0.2,0.3
						c-0.1,0.1-0.2,0.2-0.3,0.3c-0.2,0.2-0.4,0.5-0.6,0.8c0,0.1-0.1,0.1-0.1,0.2c0,0.1-0.1,0.1-0.1,0.2c-0.1,0.2-0.1,0.3-0.1,0.5
						c0,0.1,0,0.2,0,0.3c0,0,0,0.1,0,0.1c0,0,0,0.1,0,0.1c0,0.2,0.1,0.4,0.2,0.5c0.1,0.1,0.1,0.2,0.2,0.2
						C16.5,38.9,16.5,38.9,16.6,38.9C16.6,38.9,16.6,38.9,16.6,38.9L16.6,38.9L16.6,38.9L16.6,38.9C16.6,38.9,16.6,38.9,16.6,38.9
						L16.6,38.9z M14.7,39c0.1,0.1,0.1,0.1,0.2,0.2c0,0,0.1,0,0.1,0.1l0,0l0,0h0c0,0,0,0,0,0h0l0,0l0,0c0,0,0,0,0,0
						c0.1,0.1,0.2,0.1,0.3,0.1c0.1,0,0.2-0.1,0.3-0.2c0,0,0,0,0,0l0,0l0,0c0,0,0,0,0,0v0l0,0l0,0c0,0,0,0,0,0c0,0,0-0.1,0.1-0.1
						c0-0.1,0.1-0.2,0.1-0.3c0-0.2,0.1-0.4,0-0.6c0,0,0-0.1,0-0.1c0,0,0-0.1,0-0.1c0-0.1,0-0.2-0.1-0.2c-0.1-0.2-0.1-0.3-0.2-0.4
						c-0.1-0.1-0.1-0.1-0.2-0.2c-0.1-0.1-0.1-0.1-0.2-0.2c-0.2-0.3-0.5-0.5-0.7-0.7c-0.1-0.1-0.2-0.2-0.3-0.4
						c-0.1-0.1-0.2-0.2-0.2-0.4c0,0.1-0.1,0.2-0.1,0.4c0,0.1-0.1,0.3-0.1,0.4c0,0.3,0,0.5,0,0.8c0,0.1,0,0.2,0,0.3
						c0,0.1,0,0.2,0.1,0.3c0.1,0.2,0.1,0.4,0.2,0.6c0,0,0,0.1,0.1,0.1c0,0,0,0,0,0.1l0,0.1c0.1,0.1,0.1,0.2,0.2,0.2
						C14.5,38.7,14.6,38.8,14.7,39z M15.9,33.9c0,0.2-0.1,0.3-0.1,0.5c0,0.1,0,0.2,0,0.3c0,0.1,0.1,0.2,0.1,0.3
						c0.4,0.8,0.8,0.6,1.2,0.1c0-0.1,0.1-0.1,0.1-0.2c0-0.1,0.1-0.2,0.1-0.2c0.1-0.2,0.2-0.3,0.2-0.5c0-0.1,0.1-0.2,0.1-0.3
						c0,0,0-0.1,0-0.1c0,0,0-0.1,0-0.1c0-0.2,0.1-0.3,0.1-0.5c0-0.1,0-0.2,0-0.3c0-0.1,0-0.2,0-0.3c0-0.2-0.1-0.4-0.2-0.7
						c-0.1,0.2-0.3,0.4-0.6,0.6c-0.1,0.1-0.2,0.2-0.4,0.3c-0.1,0.1-0.2,0.2-0.4,0.4c-0.1,0.1-0.2,0.3-0.3,0.4c0,0.1-0.1,0.1-0.1,0.2
						C16,33.8,15.9,33.8,15.9,33.9z M13.8,33.8c0,0.1,0.1,0.2,0.1,0.3c0.1,0.2,0.1,0.4,0.2,0.6c0,0.1,0.1,0.2,0.1,0.3
						c0,0.1,0.1,0.2,0.2,0.2c0.4,0.6,0.9,0.7,1.2-0.1c0-0.1,0.1-0.2,0.1-0.3c0-0.1,0-0.2,0-0.3c0-0.2,0-0.4,0-0.5
						c0-0.1,0-0.2-0.1-0.2c0-0.1-0.1-0.2-0.1-0.2c-0.1-0.2-0.2-0.3-0.3-0.4c-0.1-0.1-0.2-0.3-0.3-0.4c-0.1-0.1-0.2-0.3-0.3-0.4
						c-0.2-0.3-0.4-0.5-0.5-0.8c-0.1,0.2-0.2,0.5-0.3,0.7c0,0.1-0.1,0.3-0.1,0.4c0,0.1,0,0.3,0,0.4c0,0.2,0,0.4,0,0.6
						c0,0.1,0,0.1,0,0.2C13.7,33.7,13.7,33.7,13.8,33.8z M16.5,29.9c-0.1,0.1-0.2,0.3-0.3,0.4c0,0.1-0.1,0.2-0.1,0.2
						c0,0.1,0,0.2,0,0.3c0,0.2,0,0.4,0.1,0.6c0,0.1,0.1,0.2,0.1,0.3c0,0.1,0.1,0.1,0.1,0.2c0.1,0.1,0.2,0.1,0.3,0.2
						c0.1,0,0.2,0,0.3-0.1c0.1-0.1,0.2-0.2,0.3-0.3c0.1-0.1,0.2-0.3,0.3-0.4c0.1-0.1,0.1-0.2,0.2-0.2c0-0.1,0.1-0.2,0.1-0.2
						c0.1-0.2,0.2-0.3,0.2-0.5c0.1-0.2,0.1-0.3,0.1-0.5c0.1-0.2,0.1-0.5,0.1-0.7c0-0.1,0-0.2,0-0.3c0-0.1,0-0.2-0.1-0.3
						c-0.1,0.1-0.2,0.2-0.3,0.3c-0.1,0.1-0.2,0.2-0.4,0.2c-0.3,0.2-0.6,0.4-0.8,0.6C16.7,29.7,16.6,29.8,16.5,29.9z M14.2,30.5
						c0,0.1,0.1,0.2,0.1,0.3c0.1,0.2,0.1,0.4,0.2,0.5c0.1,0.2,0.2,0.3,0.3,0.4c0.1,0.1,0.2,0.2,0.3,0.2c0.1,0,0.2,0,0.3-0.1
						c0.1,0,0.1-0.1,0.2-0.2c0.1-0.1,0.1-0.2,0.2-0.3c0.1-0.2,0.2-0.4,0.2-0.6c0-0.1,0-0.2,0-0.3c0-0.1,0-0.2,0-0.3
						c0-0.2-0.1-0.3-0.1-0.5c-0.1-0.2-0.1-0.3-0.2-0.5c-0.2-0.3-0.3-0.6-0.5-0.9c-0.1-0.1-0.2-0.3-0.2-0.4c-0.1-0.1-0.1-0.3-0.1-0.4
						c-0.1,0.1-0.2,0.2-0.2,0.3c-0.1,0.1-0.1,0.2-0.2,0.3c-0.1,0.2-0.2,0.5-0.2,0.8c0,0.2,0,0.4,0,0.6c0,0.2,0,0.4,0,0.6
						C14.2,30.3,14.2,30.4,14.2,30.5z M17.4,26.4c-0.1,0.1-0.1,0.1-0.2,0.2c-0.1,0.1-0.1,0.1-0.1,0.2c-0.1,0.1-0.2,0.3-0.2,0.5
						c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0,0.3c0.1,0.9,0.6,0.8,1.1,0.4c0.1,0,0.1-0.1,0.2-0.2c0.1-0.1,0.1-0.1,0.2-0.2
						c0.1-0.1,0.3-0.3,0.4-0.4c0.2-0.3,0.4-0.6,0.5-0.9c0-0.1,0-0.1,0.1-0.2c0,0,0-0.1,0-0.1l0-0.1c0-0.1,0.1-0.2,0.1-0.3
						c0-0.1,0-0.2,0-0.3c0-0.1,0-0.2,0-0.3c-0.1,0.1-0.2,0.1-0.3,0.2c-0.1,0.1-0.3,0.1-0.4,0.2c-0.1,0.1-0.3,0.1-0.5,0.2
						c-0.1,0-0.2,0.1-0.2,0.1c-0.1,0-0.1,0.1-0.2,0.1C17.7,26.2,17.5,26.3,17.4,26.4z M15.1,27c0,0.1,0,0.2,0.1,0.3
						c0,0.1,0.1,0.2,0.1,0.3c0.2,0.7,0.7,0.9,1.2,0.2c0.1-0.1,0.1-0.2,0.2-0.3c0-0.1,0.1-0.2,0.1-0.3c0.1-0.2,0.1-0.3,0.1-0.5
						c0-0.1,0-0.2,0-0.3c0-0.1,0-0.2,0-0.3c0-0.2-0.1-0.3-0.1-0.5c0-0.1-0.1-0.2-0.1-0.3c0-0.1-0.1-0.2-0.1-0.2
						c-0.1-0.2-0.1-0.3-0.2-0.5c-0.1-0.2-0.1-0.3-0.1-0.5c0-0.1-0.1-0.3-0.1-0.4c-0.1,0.1-0.2,0.2-0.3,0.3c-0.1,0.1-0.2,0.2-0.2,0.3
						c-0.1,0.1-0.1,0.2-0.2,0.3c0,0,0,0.1,0,0.1l0,0.1c0,0.1,0,0.1-0.1,0.2c-0.1,0.3-0.2,0.8-0.2,1.2C15,26.6,15.1,26.8,15.1,27z
						 M18.3,25.2c0.1,0,0.2,0,0.3,0c0.1,0,0.1,0,0.2-0.1c0.1,0,0.1-0.1,0.2-0.1c0.1-0.1,0.3-0.2,0.4-0.3c0.1-0.1,0.1-0.1,0.2-0.2
						c0.1-0.1,0.1-0.1,0.2-0.2c0.1-0.1,0.3-0.3,0.4-0.4c0.1-0.1,0.1-0.1,0.1-0.2c0-0.1,0.1-0.1,0.1-0.2c0.1-0.2,0.2-0.4,0.3-0.6
						c0.1-0.2,0.1-0.4,0.1-0.7c-0.2,0.1-0.5,0.2-0.8,0.3c-0.3,0.1-0.6,0.2-0.9,0.3c-0.3,0.1-0.6,0.3-0.8,0.6
						c-0.1,0.1-0.1,0.1-0.2,0.2c0,0.1-0.1,0.2-0.1,0.2c-0.1,0.2-0.1,0.4-0.1,0.6C18,24.9,18.1,25.1,18.3,25.2z M16.5,23.9
						c0,0.1,0,0.2,0.1,0.2c0,0.1,0.1,0.1,0.1,0.2c0.1,0.1,0.1,0.2,0.2,0.2c0.2,0.1,0.4,0,0.8-0.3c0.2-0.2,0.3-0.3,0.3-0.5
						c0-0.1,0.1-0.2,0.1-0.2c0-0.1,0.1-0.2,0.1-0.2c0.1-0.3,0-0.7,0-1c-0.1-0.3-0.2-0.7-0.2-1c-0.1-0.3-0.1-0.6-0.1-0.9
						c-0.2,0.1-0.4,0.3-0.6,0.5c-0.2,0.2-0.3,0.4-0.4,0.7c0,0.1-0.1,0.2-0.1,0.3c0,0.1-0.1,0.2-0.1,0.3c-0.1,0.2-0.1,0.4-0.1,0.6
						c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0,0.3C16.4,23.6,16.4,23.8,16.5,23.9z M19.7,21c0,0.1-0.1,0.2-0.1,0.3c-0.1,0.9,0.4,1,1,0.7
						c0.1,0,0.1-0.1,0.2-0.1c0.1,0,0.2-0.1,0.2-0.1c0.2-0.1,0.3-0.2,0.5-0.3c0.1-0.1,0.3-0.2,0.4-0.4c0.1-0.1,0.2-0.2,0.3-0.4
						c0.1-0.1,0.1-0.2,0.2-0.3c0.1-0.1,0.1-0.2,0.2-0.3c0-0.1,0.1-0.2,0.1-0.3c0-0.1,0.1-0.2,0.1-0.3c-0.1,0-0.2,0.1-0.4,0.1
						c-0.1,0-0.3,0.1-0.4,0.1c-0.2,0-0.3,0-0.5,0.1c-0.2,0-0.3,0.1-0.5,0.1c-0.3,0.1-0.6,0.2-0.9,0.4c-0.1,0.1-0.2,0.2-0.3,0.4
						C19.8,20.8,19.8,20.9,19.7,21z M19.3,21c0.1-0.1,0.2-0.1,0.2-0.2c0.1-0.1,0.1-0.1,0.2-0.2c0.1-0.1,0.2-0.3,0.2-0.5
						c0.1-0.3,0.1-0.7,0.1-1c0-0.2,0-0.4,0-0.5c0-0.2,0-0.3,0-0.5c0-0.2,0-0.3,0-0.5c0-0.2,0-0.3,0.1-0.4c-0.1,0.1-0.2,0.1-0.3,0.2
						c-0.1,0.1-0.2,0.2-0.3,0.2c-0.1,0.1-0.2,0.2-0.3,0.3c-0.1,0.1-0.2,0.2-0.3,0.3c-0.1,0.1-0.2,0.3-0.3,0.5
						c-0.1,0.2-0.2,0.4-0.2,0.6c-0.1,0.2-0.1,0.4-0.1,0.6c0,0.1,0,0.2,0,0.3c0,0.1,0,0.2,0,0.3C18.2,21.2,18.6,21.6,19.3,21z
						 M22,17.8c-0.1,0.1-0.1,0.2-0.1,0.2c0,0.1-0.1,0.2-0.1,0.3c-0.1,0.4-0.1,0.7,0.1,0.8c0.2,0.1,0.4,0.1,0.8,0.1
						c0.2,0,0.3-0.1,0.5-0.2c0,0,0.1,0,0.1-0.1c0,0,0.1,0,0.1-0.1c0.1,0,0.2-0.1,0.3-0.1c0.2-0.1,0.3-0.2,0.5-0.3
						c0.1-0.1,0.3-0.2,0.4-0.3c0.3-0.3,0.6-0.7,0.7-1.1c-0.2,0.1-0.5,0.1-0.8,0.1c-0.1,0-0.2,0-0.2,0c-0.1,0-0.2,0-0.2,0
						c-0.2,0-0.3,0-0.5,0c-0.2,0-0.3,0.1-0.5,0.1c-0.2,0.1-0.3,0.1-0.4,0.2C22.2,17.5,22.1,17.6,22,17.8z M21.4,18
						c0.1-0.1,0.2-0.1,0.3-0.2c0.1-0.1,0.1-0.1,0.2-0.2c0.1-0.1,0.2-0.3,0.3-0.4c0.1-0.2,0.1-0.3,0.2-0.5c0-0.2,0.1-0.3,0.1-0.5
						c0-0.2,0-0.4,0-0.5c0-0.1,0-0.2,0-0.3c0-0.1,0-0.2,0-0.3c0-0.3,0.1-0.6,0.2-0.9c-0.5,0.2-0.9,0.5-1.3,0.9
						c-0.1,0.1-0.2,0.3-0.4,0.5c-0.1,0.2-0.2,0.4-0.3,0.6c0,0.1-0.1,0.2-0.1,0.3c0,0,0,0.1,0,0.1c0,0.1,0,0.1,0,0.1
						c-0.1,0.2-0.1,0.4-0.1,0.6c0,0.3,0,0.6,0.2,0.8C20.7,18.3,21,18.2,21.4,18z M26.4,16.2c0.1,0,0.2-0.1,0.2-0.1
						c0.1,0,0.1-0.1,0.2-0.1c0.2-0.1,0.3-0.3,0.5-0.5c0,0,0.1-0.1,0.1-0.1c0,0,0.1-0.1,0.1-0.1c0.1-0.1,0.1-0.2,0.2-0.3
						c-0.2,0-0.5,0-0.8,0c-0.3,0-0.7-0.1-1-0.1c-0.3,0-0.7,0-1,0.2c-0.2,0.1-0.3,0.2-0.4,0.3c-0.1,0.1-0.2,0.3-0.3,0.5
						c0,0.1-0.1,0.2-0.1,0.3c0,0.1,0,0.2,0,0.2c0,0.1,0,0.2,0.1,0.3c0.2,0.1,0.4,0.2,0.7,0.2c0.3,0,0.7-0.1,1-0.3
						C26.1,16.3,26.3,16.3,26.4,16.2z M23.4,15.5c0.1,0,0.1,0,0.2,0c0.1,0,0.2,0,0.3-0.1c0.2-0.1,0.4-0.2,0.5-0.3
						c0.1-0.1,0.3-0.2,0.4-0.4c0.2-0.3,0.3-0.6,0.4-1c0.1-0.3,0.1-0.7,0.2-1c0.1-0.3,0.1-0.6,0.3-0.9c-0.1,0-0.2,0.1-0.4,0.1
						c-0.1,0-0.1,0-0.2,0.1c-0.1,0-0.1,0.1-0.2,0.1c-0.2,0.1-0.5,0.3-0.7,0.4c-0.1,0.1-0.1,0.1-0.2,0.2c-0.1,0.1-0.1,0.1-0.2,0.2
						c-0.1,0.2-0.3,0.3-0.4,0.5c-0.2,0.4-0.4,0.8-0.5,1.1c-0.1,0.3,0,0.6,0.1,0.8C23.1,15.4,23.2,15.5,23.4,15.5z M29.8,13
						c-0.2,0-0.3-0.1-0.5-0.1c-0.2,0-0.3-0.1-0.5-0.1c-0.2,0-0.3,0-0.5,0c-0.1,0-0.2,0-0.2,0c-0.1,0-0.2,0-0.2,0.1
						c-0.2,0.1-0.3,0.1-0.5,0.2c-0.1,0.1-0.1,0.1-0.2,0.2c-0.1,0.1-0.1,0.2-0.2,0.2c-0.5,0.7,0,1,0.6,1.1c0.2,0,0.3,0,0.5,0
						c0.2,0,0.4,0,0.5-0.1c0.1,0,0.2,0,0.3-0.1c0.1,0,0.2-0.1,0.3-0.1c0.2-0.1,0.3-0.1,0.5-0.2c0.1-0.1,0.2-0.1,0.3-0.2
						c0.1-0.1,0.2-0.1,0.3-0.2c0.2-0.1,0.3-0.3,0.4-0.5C30.3,13.1,30.1,13.1,29.8,13z M26.7,13.1c0.1,0,0.2-0.1,0.3-0.1
						c0.1,0,0.2-0.1,0.3-0.1c0.2-0.1,0.3-0.2,0.4-0.3c0.1-0.1,0.1-0.1,0.2-0.2c0-0.1,0.1-0.1,0.1-0.2c0.1-0.1,0.2-0.3,0.2-0.5
						c0.1-0.2,0.1-0.3,0.2-0.5c0.1-0.2,0.1-0.3,0.2-0.5c0.1-0.3,0.2-0.6,0.4-0.8c-0.3,0-0.5,0.1-0.8,0.1c-0.1,0-0.2,0.1-0.4,0.1
						c-0.1,0.1-0.2,0.1-0.4,0.2c-0.2,0.1-0.3,0.2-0.5,0.3c-0.1,0.1-0.2,0.1-0.2,0.2c-0.1,0.1-0.1,0.2-0.2,0.2
						c-0.1,0.2-0.3,0.3-0.4,0.5c-0.1,0.2-0.2,0.4-0.2,0.5C25.7,12.8,25.8,13.3,26.7,13.1z"/>
				</defs>
				<clipPath id="SVGID_00000142889457059786430830000011068695169383873699_">
					<use xlink:href="#SVGID_00000145754488922110141470000008438775829136780948_"  style="overflow:visible;"/>
				</clipPath>
				<g style="clip-path:url(#SVGID_00000142889457059786430830000011068695169383873699_);">
					<path class="st2" d="M29.2,8.4c-2.7,1.2-16.3,10.4-16.3,24.4c0,13.9,5,20.1,13.6,26.2"/>
					<path class="st3" d="M29.2,8.4c-2.7,1.2-16.3,10.4-16.3,24.4c0,13.9,5,20.1,13.6,26.2"/>
					<path class="st4" d="M29.2,8.4c-2.7,1.2-16.3,10.4-16.3,24.4c0,13.8,5,20.1,13.6,26.2"/>
					<path class="st5" d="M29.2,8.4c-2.7,1.2-16.3,10.4-16.3,24.4c0,13.8,5,20.1,13.6,26.2"/>
					<path class="st6" d="M29.2,8.4c-2.7,1.2-16.3,10.4-16.3,24.3c0,13.8,5,20.1,13.5,26.2"/>
					<path class="st7" d="M29.2,8.5C26.6,9.7,13,18.8,13,32.8c0,13.8,4.9,20,13.5,26.1"/>
					<path class="st8" d="M29.3,8.5C26.6,9.7,13,18.8,13,32.8c0,13.8,4.9,20,13.5,26.1"/>
					<path class="st9" d="M29.3,8.5C26.6,9.7,13,18.8,13,32.8c0,13.8,4.9,20,13.5,26.1"/>
					<path class="st10" d="M29.3,8.5C26.6,9.7,13,18.9,13,32.8c0,13.8,4.9,20,13.5,26.1"/>
					<path class="st11" d="M29.3,8.5C26.6,9.8,13,18.9,13,32.8c0,13.8,4.9,20,13.5,26.1"/>
					<path class="st12" d="M29.3,8.6c-2.7,1.2-16.2,10.3-16.2,24.2c0,13.8,4.9,20,13.5,26.1"/>
					<path class="st13" d="M29.3,8.6c-2.7,1.2-16.2,10.3-16.2,24.2c0,13.7,4.9,19.9,13.5,26"/>
					<path class="st14" d="M29.3,8.6c-2.7,1.2-16.2,10.3-16.2,24.2c0,13.7,4.9,19.9,13.5,26"/>
					<path class="st15" d="M29.3,8.6c-2.7,1.2-16.2,10.3-16.2,24.2c0,13.7,4.9,19.9,13.5,26"/>
					<path class="st16" d="M29.3,8.6c-2.6,1.2-16.2,10.3-16.2,24.2c0,13.7,4.9,19.9,13.5,26"/>
					<path class="st17" d="M29.3,8.7c-2.6,1.2-16.2,10.3-16.2,24.1c0,13.7,4.9,19.9,13.4,26"/>
					<path class="st18" d="M29.3,8.7C26.7,9.9,13.2,19,13.2,32.8c0,13.7,4.9,19.9,13.4,26"/>
					<path class="st19" d="M29.3,8.7C26.7,9.9,13.2,19,13.2,32.8c0,13.7,4.9,19.9,13.4,25.9"/>
					<path class="st20" d="M29.4,8.7C26.7,9.9,13.2,19,13.2,32.8c0,13.7,4.9,19.8,13.4,25.9"/>
					<path class="st21" d="M29.4,8.7C26.7,9.9,13.3,19,13.3,32.8c0,13.7,4.9,19.8,13.4,25.9"/>
					<path class="st22" d="M29.4,8.8C26.7,10,13.3,19,13.3,32.8c0,13.7,4.8,19.8,13.4,25.9"/>
					<path class="st23" d="M29.4,8.8C26.8,10,13.3,19,13.3,32.8c0,13.6,4.8,19.8,13.4,25.9"/>
					<path class="st24" d="M29.4,8.8C26.8,10,13.3,19,13.3,32.8c0,13.6,4.8,19.8,13.4,25.9"/>
					<path class="st25" d="M29.4,8.8C26.8,10,13.3,19,13.3,32.8c0,13.6,4.8,19.8,13.4,25.8"/>
					<path class="st26" d="M29.4,8.8c-2.6,1.2-16.1,10.2-16.1,24c0,13.6,4.8,19.7,13.4,25.8"/>
					<path class="st27" d="M29.4,8.9c-2.6,1.2-16,10.2-16,23.9c0,13.6,4.8,19.7,13.4,25.8"/>
					<path class="st28" d="M29.4,8.9c-2.6,1.2-16,10.2-16,23.9c0,13.6,4.8,19.7,13.3,25.8"/>
					<path class="st29" d="M29.4,8.9c-2.6,1.2-16,10.2-16,23.9c0,13.6,4.8,19.7,13.3,25.8"/>
					<path class="st30" d="M29.4,8.9c-2.6,1.2-16,10.2-16,23.9c0,13.6,4.8,19.7,13.3,25.7"/>
					<path class="st31" d="M29.5,8.9c-2.6,1.2-16,10.2-16,23.9c0,13.6,4.8,19.7,13.3,25.7"/>
					<path class="st32" d="M29.5,8.9c-2.6,1.2-16,10.2-16,23.8c0,13.5,4.8,19.6,13.3,25.7"/>
					<path class="st33" d="M29.5,9c-2.6,1.2-16,10.2-16,23.8c0,13.5,4.8,19.6,13.3,25.7"/>
					<path class="st34" d="M29.5,9c-2.6,1.2-16,10.2-16,23.8c0,13.5,4.8,19.6,13.3,25.7"/>
					<path class="st35" d="M29.5,9c-2.6,1.2-15.9,10.2-15.9,23.8c0,13.5,4.8,19.6,13.3,25.7"/>
					<path class="st36" d="M29.5,9c-2.6,1.2-15.9,10.1-15.9,23.8c0,13.5,4.8,19.6,13.3,25.6"/>
					<path class="st37" d="M29.5,9c-2.6,1.2-15.9,10.1-15.9,23.7c0,13.5,4.7,19.6,13.3,25.6"/>
					<path class="st38" d="M29.5,9.1c-2.6,1.2-15.9,10.1-15.9,23.7c0,13.5,4.7,19.6,13.3,25.6"/>
					<path class="st39" d="M29.5,9.1c-2.6,1.2-15.9,10.1-15.9,23.7c0,13.5,4.7,19.5,13.2,25.6"/>
					<path class="st40" d="M29.5,9.1c-2.6,1.2-15.9,10.1-15.9,23.7c0,13.5,4.7,19.5,13.2,25.6"/>
					<path class="st41" d="M29.5,9.1C27,10.3,13.7,19.2,13.7,32.8c0,13.4,4.7,19.5,13.2,25.6"/>
					<path class="st42" d="M29.5,9.1C27,10.3,13.7,19.2,13.7,32.8c0,13.4,4.7,19.5,13.2,25.5"/>
					<path class="st43" d="M29.6,9.2C27,10.3,13.7,19.3,13.7,32.8c0,13.4,4.7,19.5,13.2,25.5"/>
					<path class="st44" d="M29.6,9.2C27,10.4,13.7,19.3,13.7,32.8c0,13.4,4.7,19.5,13.2,25.5"/>
					<path class="st45" d="M29.6,9.2C27,10.4,13.8,19.3,13.8,32.8c0,13.4,4.7,19.4,13.2,25.5"/>
					<path class="st46" d="M29.6,9.2C27,10.4,13.8,19.3,13.8,32.8c0,13.4,4.7,19.4,13.2,25.5"/>
					<path class="st47" d="M29.6,9.2C27,10.4,13.8,19.3,13.8,32.8c0,13.4,4.7,19.4,13.2,25.5"/>
					<path class="st48" d="M29.6,9.3C27,10.4,13.8,19.3,13.8,32.8c0,13.4,4.7,19.4,13.2,25.4"/>
					<path class="st49" d="M29.6,9.3c-2.6,1.2-15.8,10.1-15.8,23.5c0,13.4,4.7,19.4,13.2,25.4"/>
					<path class="st50" d="M29.6,9.3c-2.6,1.2-15.8,10-15.8,23.5c0,13.3,4.7,19.4,13.1,25.4"/>
					<path class="st51" d="M29.6,9.3c-2.5,1.2-15.7,10-15.7,23.5c0,13.3,4.7,19.4,13.1,25.4"/>
					<path class="st52" d="M29.6,9.3c-2.5,1.2-15.7,10-15.7,23.5c0,13.3,4.6,19.3,13.1,25.4"/>
					<path class="st53" d="M29.6,9.4c-2.5,1.2-15.7,10-15.7,23.4c0,13.3,4.6,19.3,13.1,25.3"/>
					<path class="st54" d="M29.7,9.4c-2.5,1.2-15.7,10-15.7,23.4c0,13.3,4.6,19.3,13.1,25.3"/>
					<path class="st55" d="M29.7,9.4C27.1,10.5,14,19.4,14,32.8c0,13.3,4.6,19.3,13.1,25.3"/>
					<path class="st56" d="M29.7,9.4C27.1,10.6,14,19.4,14,32.8c0,13.3,4.6,19.3,13.1,25.3"/>
					<path class="st57" d="M29.7,9.4C27.1,10.6,14,19.4,14,32.8c0,13.3,4.6,19.3,13.1,25.3"/>
					<path class="st58" d="M29.7,9.5C27.2,10.6,14,19.4,14,32.8c0,13.3,4.6,19.2,13.1,25.3"/>
					<path class="st59" d="M29.7,9.5c-2.5,1.1-15.6,10-15.6,23.3C14.1,46,18.7,52,27.1,58"/>
					<path class="st60" d="M29.7,9.5c-2.5,1.1-15.6,10-15.6,23.3C14.1,46,18.7,52,27.1,58"/>
					<path class="st61" d="M29.7,9.5c-2.5,1.1-15.6,10-15.6,23.3C14.1,46,18.7,52,27.1,58"/>
					<path class="st62" d="M29.7,9.5c-2.5,1.1-15.6,10-15.6,23.3c0,13.2,4.6,19.2,13,25.2"/>
					<path class="st63" d="M29.7,9.6c-2.5,1.1-15.6,9.9-15.6,23.2c0,13.2,4.6,19.2,13,25.2"/>
					<path class="st64" d="M29.7,9.6c-2.5,1.1-15.6,9.9-15.6,23.2c0,13.2,4.6,19.1,13,25.2"/>
					<path class="st65" d="M29.7,9.6c-2.5,1.1-15.6,9.9-15.6,23.2c0,13.2,4.6,19.1,13,25.1"/>
					<path class="st66" d="M29.8,9.6c-2.5,1.1-15.6,9.9-15.6,23.2c0,13.2,4.6,19.1,13,25.1"/>
					<path class="st67" d="M29.8,9.6c-2.5,1.1-15.5,9.9-15.5,23.2c0,13.2,4.5,19.1,13,25.1"/>
					<path class="st68" d="M29.8,9.6c-2.5,1.1-15.5,9.9-15.5,23.1c0,13.2,4.5,19.1,13,25.1"/>
					<path class="st69" d="M29.8,9.7c-2.5,1.1-15.5,9.9-15.5,23.1c0,13.1,4.5,19.1,13,25.1"/>
					<path class="st70" d="M29.8,9.7c-2.5,1.1-15.5,9.9-15.5,23.1c0,13.1,4.5,19.1,13,25.1"/>
					<path class="st71" d="M29.8,9.7c-2.5,1.1-15.5,9.9-15.5,23.1c0,13.1,4.5,19,13,25"/>
					<path class="st72" d="M29.8,9.7c-2.5,1.1-15.5,9.9-15.5,23.1c0,13.1,4.5,19,13,25"/>
					<path class="st73" d="M29.8,9.7c-2.5,1.1-15.5,9.9-15.5,23c0,13.1,4.5,19,12.9,25"/>
					<path class="st74" d="M29.8,9.8c-2.5,1.1-15.5,9.9-15.5,23c0,13.1,4.5,19,12.9,25"/>
					<path class="st75" d="M29.8,9.8c-2.5,1.1-15.4,9.9-15.4,23c0,13.1,4.5,19,12.9,25"/>
					<path class="st76" d="M29.8,9.8c-2.5,1.1-15.4,9.9-15.4,23c0,13.1,4.5,19,12.9,24.9"/>
					<path class="st77" d="M29.9,9.8c-2.5,1.1-15.4,9.8-15.4,23c0,13.1,4.5,18.9,12.9,24.9"/>
					<path class="st78" d="M29.9,9.8c-2.5,1.1-15.4,9.8-15.4,23c0,13,4.5,18.9,12.9,24.9"/>
					<path class="st79" d="M29.9,9.9c-2.5,1.1-15.4,9.8-15.4,22.9c0,13,4.5,18.9,12.9,24.9"/>
					<path class="st80" d="M29.9,9.9c-2.5,1.1-15.4,9.8-15.4,22.9c0,13,4.5,18.9,12.9,24.9"/>
					<path class="st81" d="M29.9,9.9c-2.5,1.1-15.4,9.8-15.4,22.9c0,13,4.5,18.9,12.9,24.9"/>
					<path class="st82" d="M29.9,9.9c-2.5,1.1-15.4,9.8-15.4,22.9c0,13,4.4,18.9,12.9,24.8"/>
					<path class="st83" d="M29.9,9.9c-2.5,1.1-15.3,9.8-15.3,22.9c0,13,4.4,18.8,12.9,24.8"/>
					<path class="st84" d="M29.9,10c-2.5,1.1-15.3,9.8-15.3,22.8c0,13,4.4,18.8,12.8,24.8"/>
					<path class="st85" d="M29.9,10c-2.5,1.1-15.3,9.8-15.3,22.8c0,13,4.4,18.8,12.8,24.8"/>
					<path class="st86" d="M29.9,10c-2.4,1.1-15.3,9.8-15.3,22.8c0,13,4.4,18.8,12.8,24.8"/>
					<path class="st87" d="M29.9,10c-2.4,1.1-15.3,9.8-15.3,22.8c0,12.9,4.4,18.8,12.8,24.8"/>
					<path class="st88" d="M29.9,10c-2.4,1.1-15.3,9.8-15.3,22.8c0,12.9,4.4,18.8,12.8,24.7"/>
					<path class="st89" d="M30,10.1c-2.4,1.1-15.3,9.8-15.3,22.7c0,12.9,4.4,18.8,12.8,24.7"/>
					<path class="st90" d="M30,10.1c-2.4,1.1-15.3,9.8-15.3,22.7c0,12.9,4.4,18.7,12.8,24.7"/>
					<path class="st91" d="M30,10.1c-2.4,1.1-15.2,9.7-15.2,22.7c0,12.9,4.4,18.7,12.8,24.7"/>
					<path class="st92" d="M30,10.1c-2.4,1.1-15.2,9.7-15.2,22.7c0,12.9,4.4,18.7,12.8,24.7"/>
					<path class="st93" d="M30,10.1c-2.4,1.1-15.2,9.7-15.2,22.7c0,12.9,4.4,18.7,12.8,24.7"/>
					<path class="st94" d="M30,10.2c-2.4,1.1-15.2,9.7-15.2,22.6c0,12.9,4.4,18.7,12.8,24.6"/>
					<path class="st95" d="M30,10.2c-2.4,1.1-15.2,9.7-15.2,22.6c0,12.9,4.4,18.7,12.7,24.6"/>
					<path class="st96" d="M30,10.2c-2.4,1.1-15.2,9.7-15.2,22.6c0,12.8,4.4,18.6,12.7,24.6"/>
					<path class="st97" d="M30,10.2c-2.4,1.1-15.2,9.7-15.2,22.6c0,12.8,4.4,18.6,12.7,24.6"/>
					<path class="st98" d="M30,10.2c-2.4,1.1-15.2,9.7-15.2,22.6c0,12.8,4.3,18.6,12.7,24.6"/>
					<path class="st99" d="M30,10.2c-2.4,1.1-15.1,9.7-15.1,22.5c0,12.8,4.3,18.6,12.7,24.5"/>
					<path class="st100" d="M30.1,10.3c-2.4,1.1-15.1,9.7-15.1,22.5c0,12.8,4.3,18.6,12.7,24.5"/>
					<path class="st101" d="M30.1,10.3c-2.4,1.1-15.1,9.7-15.1,22.5c0,12.8,4.3,18.6,12.7,24.5"/>
					<path class="st102" d="M30.1,10.3C27.7,11.4,15,20,15,32.8c0,12.8,4.3,18.5,12.7,24.5"/>
					<path class="st103" d="M30.1,10.3C27.7,11.4,15,20,15,32.8c0,12.8,4.3,18.5,12.7,24.5"/>
					<path class="st104" d="M30.1,10.3C27.7,11.4,15,20,15,32.8c0,12.8,4.3,18.5,12.7,24.5"/>
					<path class="st105" d="M30.1,10.4C27.7,11.5,15,20,15,32.8c0,12.7,4.3,18.5,12.7,24.4"/>
					<path class="st106" d="M30.1,10.4C27.7,11.5,15,20,15,32.8c0,12.7,4.3,18.5,12.6,24.4"/>
					<path class="st107" d="M30.1,10.4c-2.4,1.1-15,9.6-15,22.4c0,12.7,4.3,18.5,12.6,24.4"/>
					<path class="st108" d="M30.1,10.4c-2.4,1.1-15,9.6-15,22.4c0,12.7,4.3,18.5,12.6,24.4"/>
					<path class="st109" d="M30.1,10.4c-2.4,1.1-15,9.6-15,22.4c0,12.7,4.3,18.4,12.6,24.4"/>
					<path class="st110" d="M30.1,10.5c-2.4,1.1-15,9.6-15,22.3c0,12.7,4.3,18.4,12.6,24.4"/>
					<path class="st111" d="M30.1,10.5c-2.4,1.1-15,9.6-15,22.3c0,12.7,4.3,18.4,12.6,24.3"/>
					<path class="st112" d="M30.2,10.5c-2.4,1.1-15,9.6-15,22.3c0,12.7,4.3,18.4,12.6,24.3"/>
					<path class="st113" d="M30.2,10.5c-2.4,1.1-15,9.6-15,22.3c0,12.7,4.2,18.4,12.6,24.3"/>
					<path class="st114" d="M30.2,10.5c-2.4,1.1-15,9.6-15,22.3c0,12.6,4.2,18.4,12.6,24.3"/>
					<path class="st115" d="M30.2,10.6c-2.4,1.1-14.9,9.6-14.9,22.2c0,12.6,4.2,18.3,12.6,24.3"/>
					<path class="st116" d="M30.2,10.6c-2.4,1.1-14.9,9.6-14.9,22.2c0,12.6,4.2,18.3,12.6,24.3"/>
					<path class="st117" d="M30.2,10.6c-2.4,1.1-14.9,9.6-14.9,22.2c0,12.6,4.2,18.3,12.6,24.2"/>
					<path class="st118" d="M30.2,10.6c-2.4,1.1-14.9,9.5-14.9,22.2c0,12.6,4.2,18.3,12.5,24.2"/>
					<path class="st119" d="M30.2,10.6c-2.4,1.1-14.9,9.5-14.9,22.2c0,12.6,4.2,18.3,12.5,24.2"/>
					<path class="st120" d="M30.2,10.7c-2.3,1.1-14.9,9.5-14.9,22.1c0,12.6,4.2,18.3,12.5,24.2"/>
					<path class="st121" d="M30.2,10.7c-2.3,1.1-14.9,9.5-14.9,22.1c0,12.6,4.2,18.3,12.5,24.2"/>
					<path class="st122" d="M30.2,10.7c-2.3,1.1-14.9,9.5-14.9,22.1c0,12.6,4.2,18.2,12.5,24.2"/>
					<path class="st123" d="M30.3,10.7c-2.3,1.1-14.8,9.5-14.8,22.1c0,12.6,4.2,18.2,12.5,24.1"/>
					<path class="st124" d="M30.3,10.7c-2.3,1.1-14.8,9.5-14.8,22.1c0,12.5,4.2,18.2,12.5,24.1"/>
					<path class="st125" d="M30.3,10.8c-2.3,1.1-14.8,9.5-14.8,22c0,12.5,4.2,18.2,12.5,24.1"/>
					<path class="st126" d="M30.3,10.8c-2.3,1.1-14.8,9.5-14.8,22c0,12.5,4.2,18.2,12.5,24.1"/>
					<path class="st127" d="M30.3,10.8c-2.3,1.1-14.8,9.5-14.8,22c0,12.5,4.2,18.2,12.5,24.1"/>
					<path class="st128" d="M30.3,10.8c-2.3,1.1-14.8,9.5-14.8,22c0,12.5,4.1,18.1,12.5,24"/>
					<path class="st129" d="M30.3,10.8c-2.3,1.1-14.8,9.5-14.8,22c0,12.5,4.1,18.1,12.4,24"/>
					<path class="st130" d="M30.3,10.8c-2.3,1.1-14.8,9.5-14.8,21.9c0,12.5,4.1,18.1,12.4,24"/>
					<path class="st131" d="M30.3,10.9c-2.3,1.1-14.7,9.5-14.7,21.9c0,12.5,4.1,18.1,12.4,24"/>
					<path class="st132" d="M30.3,10.9c-2.3,1.1-14.7,9.4-14.7,21.9c0,12.5,4.1,18.1,12.4,24"/>
					<path class="st133" d="M30.3,10.9C28,12,15.6,20.3,15.6,32.8c0,12.4,4.1,18.1,12.4,24"/>
					<path class="st134" d="M30.3,10.9C28,12,15.6,20.4,15.6,32.8c0,12.4,4.1,18,12.4,23.9"/>
					<path class="st135" d="M30.4,10.9c-2.3,1-14.7,9.4-14.7,21.8c0,12.4,4.1,18,12.4,23.9"/>
					<path class="st136" d="M30.4,11c-2.3,1-14.7,9.4-14.7,21.8c0,12.4,4.1,18,12.4,23.9"/>
					<path class="st137" d="M30.4,11c-2.3,1-14.7,9.4-14.7,21.8c0,12.4,4.1,18,12.4,23.9"/>
					<path class="st138" d="M30.4,11c-2.3,1-14.7,9.4-14.7,21.8c0,12.4,4.1,18,12.4,23.9"/>
					<path class="st139" d="M30.4,11c-2.3,1-14.6,9.4-14.6,21.8c0,12.4,4.1,18,12.4,23.9"/>
					<path class="st140" d="M30.4,11c-2.3,1-14.6,9.4-14.6,21.8s4.1,18,12.3,23.8"/>
					<path class="st139" d="M30.4,11.1c-2.3,1-14.6,9.4-14.6,21.7c0,12.4,4.1,17.9,12.3,23.8"/>
					<path class="st138" d="M30.4,11.1c-2.3,1-14.6,9.4-14.6,21.7c0,12.4,4.1,17.9,12.3,23.8"/>
					<path class="st137" d="M30.4,11.1c-2.3,1-14.6,9.4-14.6,21.7c0,12.3,4.1,17.9,12.3,23.8"/>
					<path class="st136" d="M30.4,11.1c-2.3,1-14.6,9.4-14.6,21.7c0,12.3,4.1,17.9,12.3,23.8"/>
					<path class="st135" d="M30.4,11.1c-2.3,1-14.6,9.3-14.6,21.7c0,12.3,4.1,17.9,12.3,23.8"/>
					<path class="st134" d="M30.4,11.2c-2.3,1-14.6,9.3-14.6,21.6c0,12.3,4.1,17.9,12.3,23.7"/>
					<path class="st133" d="M30.5,11.2c-2.3,1-14.6,9.3-14.6,21.6c0,12.3,4,17.9,12.3,23.7"/>
					<path class="st132" d="M30.5,11.2c-2.3,1-14.5,9.3-14.5,21.6c0,12.3,4,17.9,12.3,23.7"/>
					<path class="st131" d="M30.5,11.2c-2.3,1-14.5,9.3-14.5,21.6c0,12.3,4,17.8,12.3,23.7"/>
					<path class="st130" d="M30.5,11.2c-2.2,1-14.5,9.3-14.5,21.6c0,12.3,4,17.8,12.3,23.7"/>
					<path class="st129" d="M30.5,11.3c-2.2,1-14.5,9.3-14.5,21.5c0,12.3,4,17.8,12.2,23.6"/>
					<path class="st128" d="M30.5,11.3c-2.2,1-14.5,9.3-14.5,21.5c0,12.3,4,17.8,12.2,23.6"/>
					<path class="st127" d="M30.5,11.3c-2.2,1-14.5,9.3-14.5,21.5c0,12.3,4,17.8,12.2,23.6"/>
					<path class="st126" d="M30.5,11.3c-2.2,1-14.5,9.3-14.5,21.5c0,12.3,4,17.8,12.2,23.6"/>
					<path class="st125" d="M30.5,11.3c-2.2,1-14.5,9.3-14.5,21.5c0,12.3,4,17.8,12.2,23.6"/>
					<path class="st124" d="M30.5,11.4c-2.2,1-14.4,9.3-14.4,21.4c0,12.3,4,17.7,12.2,23.6"/>
					<path class="st123" d="M30.5,11.4c-2.2,1-14.4,9.3-14.4,21.4c0,12.3,4,17.7,12.2,23.5"/>
					<path class="st122" d="M30.6,11.4c-2.2,1-14.4,9.2-14.4,21.4c0,12.2,4,17.7,12.2,23.5"/>
					<path class="st121" d="M30.6,11.4c-2.2,1-14.4,9.2-14.4,21.4c0,12.2,4,17.7,12.2,23.5"/>
					<path class="st120" d="M30.6,11.4c-2.2,1-14.4,9.2-14.4,21.4c0,12.2,4,17.7,12.2,23.5"/>
					<path class="st119" d="M30.6,11.4c-2.2,1-14.4,9.2-14.4,21.3c0,12.2,4,17.7,12.2,23.5"/>
					<path class="st118" d="M30.6,11.5c-2.2,1-14.4,9.2-14.4,21.3c0,12.2,4,17.7,12.1,23.5"/>
					<path class="st117" d="M30.6,11.5c-2.2,1-14.4,9.2-14.4,21.3c0,12.2,4,17.7,12.1,23.4"/>
					<path class="st116" d="M30.6,11.5c-2.2,1-14.3,9.2-14.3,21.3c0,12.2,4,17.6,12.1,23.4"/>
					<path class="st115" d="M30.6,11.5c-2.2,1-14.3,9.2-14.3,21.3c0,12.2,4,17.6,12.1,23.4"/>
					<path class="st114" d="M30.6,11.5c-2.2,1-14.3,9.2-14.3,21.2c0,12.2,4,17.6,12.1,23.4"/>
					<path class="st113" d="M30.6,11.6c-2.2,1-14.3,9.2-14.3,21.2c0,12.2,4,17.6,12.1,23.4"/>
					<path class="st112" d="M30.6,11.6c-2.2,1-14.3,9.2-14.3,21.2c0,12.2,4,17.6,12.1,23.4"/>
					<path class="st111" d="M30.6,11.6c-2.2,1-14.3,9.2-14.3,21.2c0,12.2,4,17.6,12.1,23.3"/>
					<path class="st110" d="M30.7,11.6c-2.2,1-14.3,9.2-14.3,21.2c0,12.2,4,17.6,12.1,23.3"/>
					<path class="st109" d="M30.7,11.6c-2.2,1-14.3,9.2-14.3,21.2c0,12.2,4,17.6,12.1,23.3"/>
					<path class="st108" d="M30.7,11.7c-2.2,1-14.2,9.1-14.2,21.1c0,12.2,4,17.5,12.1,23.3"/>
					<path class="st107" d="M30.7,11.7c-2.1,1-14.2,9.1-14.2,21.1c0,12.1,4,17.5,12.1,23.3"/>
					<path class="st106" d="M30.7,11.7c-2.1,1-14.2,9.1-14.2,21.1c0,12.1,4,17.5,12,23.2"/>
					<path class="st105" d="M30.7,11.7c-2.1,1-14.2,9.1-14.2,21.1c0,12.1,4,17.5,12,23.2"/>
					<path class="st104" d="M30.7,11.7c-2.1,1-14.2,9.1-14.2,21.1c0,12.1,4,17.5,12,23.2"/>
					<path class="st103" d="M30.7,11.8c-2.1,1-14.2,9.1-14.2,21c0,12.1,4,17.5,12,23.2"/>
					<path class="st102" d="M30.7,11.8c-2.1,1-14.2,9.1-14.2,21c0,12.1,4,17.5,12,23.2"/>
					<path class="st101" d="M30.7,11.8c-2.1,1-14.2,9.1-14.2,21c0,12.1,4,17.5,12,23.2"/>
					<path class="st100" d="M30.7,11.8c-2.1,1-14.1,9.1-14.1,21c0,12.1,4,17.4,12,23.1"/>
					<path class="st99" d="M30.8,11.8c-2.1,1-14.1,9.1-14.1,21c0,12.1,4,17.4,12,23.1"/>
					<path class="st98" d="M30.8,11.9c-2.1,1-14.1,9.1-14.1,20.9c0,12.1,4,17.4,12,23.1"/>
					<path class="st97" d="M30.8,11.9c-2.1,1-14.1,9.1-14.1,20.9c0,12.1,4,17.4,12,23.1"/>
					<path class="st96" d="M30.8,11.9c-2.1,1-14.1,9.1-14.1,20.9c0,12.1,4,17.4,12,23.1"/>
					<path class="st95" d="M30.8,11.9c-2.1,1-14.1,9-14.1,20.9c0,12.1,4,17.4,11.9,23.1"/>
					<path class="st94" d="M30.8,11.9c-2.1,1-14.1,9-14.1,20.9c0,12.1,4,17.4,11.9,23"/>
					<path class="st93" d="M30.8,12c-2.1,1-14.1,9-14.1,20.8c0,12.1,4,17.4,11.9,23"/>
					<path class="st92" d="M30.8,12c-2.1,0.9-14,9-14,20.8c0,12,4,17.3,11.9,23"/>
					<path class="st91" d="M30.8,12c-2.1,0.9-14,9-14,20.8c0,12,4,17.3,11.9,23"/>
					<path class="st90" d="M30.8,12c-2.1,0.9-14,9-14,20.8c0,12,3.9,17.3,11.9,23"/>
					<path class="st89" d="M30.8,12c-2.1,0.9-14,9-14,20.8c0,12,3.9,17.3,11.9,23"/>
					<path class="st88" d="M30.8,12c-2.1,0.9-14,9-14,20.7c0,12,3.9,17.3,11.9,22.9"/>
					<path class="st87" d="M30.9,12.1c-2.1,0.9-14,9-14,20.7c0,12,3.9,17.3,11.9,22.9"/>
					<path class="st86" d="M30.9,12.1c-2.1,0.9-14,9-14,20.7c0,12,3.9,17.3,11.9,22.9"/>
					<path class="st85" d="M30.9,12.1c-2.1,0.9-14,9-14,20.7c0,12,3.9,17.3,11.9,22.9"/>
					<path class="st84" d="M30.9,12.1c-2,0.9-13.9,9-13.9,20.7c0,12,3.9,17.2,11.8,22.9"/>
					<path class="st83" d="M30.9,12.1c-2,0.9-13.9,9-13.9,20.6c0,12,3.9,17.2,11.8,22.8"/>
					<path class="st82" d="M30.9,12.2c-2,0.9-13.9,8.9-13.9,20.6c0,12,3.9,17.2,11.8,22.8"/>
					<path class="st81" d="M30.9,12.2c-2,0.9-13.9,8.9-13.9,20.6c0,12,3.9,17.2,11.8,22.8"/>
					<path class="st80" d="M30.9,12.2c-2,0.9-13.9,8.9-13.9,20.6c0,12,3.9,17.2,11.8,22.8"/>
					<path class="st79" d="M30.9,12.2c-2,0.9-13.9,8.9-13.9,20.6c0,12,3.9,17.2,11.8,22.8"/>
					<path class="st78" d="M30.9,12.2c-2,0.9-13.9,8.9-13.9,20.6c0,12,3.9,17.2,11.8,22.8"/>
					<path class="st77" d="M30.9,12.3c-2,0.9-13.9,8.9-13.9,20.5c0,11.9,3.9,17.2,11.8,22.7"/>
					<path class="st76" d="M31,12.3c-2,0.9-13.8,8.9-13.8,20.5c0,11.9,3.9,17.1,11.8,22.7"/>
					<path class="st75" d="M31,12.3c-2,0.9-13.8,8.9-13.8,20.5c0,11.9,3.9,17.1,11.8,22.7"/>
					<path class="st74" d="M31,12.3c-2,0.9-13.8,8.9-13.8,20.5c0,11.9,3.9,17.1,11.8,22.7"/>
					<path class="st73" d="M31,12.3c-2,0.9-13.8,8.9-13.8,20.5c0,11.9,3.9,17.1,11.7,22.7"/>
					<path class="st72" d="M31,12.4c-2,0.9-13.8,8.9-13.8,20.4c0,11.9,3.9,17.1,11.7,22.7"/>
					<path class="st141" d="M31,12.4c-2,0.9-13.8,8.9-13.8,20.4c0,11.9,3.9,17.1,11.7,22.6"/>
					<path class="st70" d="M31,12.4c-2,0.9-13.8,8.9-13.8,20.4c0,11.9,3.9,17.1,11.7,22.6"/>
					<path class="st69" d="M31,12.4c-2,0.9-13.8,8.8-13.8,20.4c0,11.9,3.9,17.1,11.7,22.6"/>
					<path class="st68" d="M31,12.4c-2,0.9-13.7,8.8-13.7,20.4c0,11.9,3.9,17,11.7,22.6"/>
					<path class="st67" d="M31,12.5c-2,0.9-13.7,8.8-13.7,20.3c0,11.9,3.9,17,11.7,22.6"/>
					<path class="st66" d="M31,12.5c-2,0.9-13.7,8.8-13.7,20.3c0,11.9,3.9,17,11.7,22.6"/>
					<path class="st65" d="M31,12.5c-2,0.9-13.7,8.8-13.7,20.3c0,11.9,3.9,17,11.7,22.5"/>
					<path class="st64" d="M31.1,12.5c-2,0.9-13.7,8.8-13.7,20.3c0,11.9,3.9,17,11.7,22.5"/>
					<path class="st63" d="M31.1,12.5c-2,0.9-13.7,8.8-13.7,20.3c0,11.9,3.9,17,11.7,22.5"/>
					<path class="st62" d="M31.1,12.6c-2,0.9-13.7,8.8-13.7,20.2c0,11.8,3.9,17,11.6,22.5"/>
					<path class="st61" d="M31.1,12.6c-1.9,0.9-13.7,8.8-13.7,20.2c0,11.8,3.9,17,11.6,22.5"/>
					<path class="st60" d="M31.1,12.6c-1.9,0.9-13.6,8.8-13.6,20.2c0,11.8,3.9,16.9,11.6,22.4"/>
					<path class="st59" d="M31.1,12.6c-1.9,0.9-13.6,8.8-13.6,20.2c0,11.8,3.9,16.9,11.6,22.4"/>
					<path class="st58" d="M31.1,12.6c-1.9,0.9-13.6,8.8-13.6,20.2c0,11.8,3.9,16.9,11.6,22.4"/>
					<path class="st57" d="M31.1,12.6c-1.9,0.9-13.6,8.8-13.6,20.1c0,11.8,3.9,16.9,11.6,22.4"/>
					<path class="st56" d="M31.1,12.7c-1.9,0.9-13.6,8.8-13.6,20.1c0,11.8,3.9,16.9,11.6,22.4"/>
					<path class="st55" d="M31.1,12.7c-1.9,0.9-13.6,8.7-13.6,20.1c0,11.8,3.9,16.9,11.6,22.4"/>
					<path class="st54" d="M31.1,12.7c-1.9,0.9-13.6,8.7-13.6,20.1c0,11.8,3.9,16.9,11.6,22.3"/>
					<path class="st53" d="M31.2,12.7c-1.9,0.9-13.6,8.7-13.6,20.1c0,11.8,3.9,16.8,11.6,22.3"/>
					<path class="st52" d="M31.2,12.7c-1.9,0.9-13.5,8.7-13.5,20c0,11.8,3.9,16.8,11.6,22.3"/>
					<path class="st51" d="M31.2,12.8c-1.9,0.9-13.5,8.7-13.5,20c0,11.8,3.9,16.8,11.6,22.3"/>
					<path class="st50" d="M31.2,12.8c-1.9,0.9-13.5,8.7-13.5,20c0,11.8,3.9,16.8,11.5,22.3"/>
					<path class="st49" d="M31.2,12.8c-1.9,0.9-13.5,8.7-13.5,20c0,11.8,3.9,16.8,11.5,22.3"/>
					<path class="st48" d="M31.2,12.8c-1.9,0.9-13.5,8.7-13.5,20c0,11.8,3.9,16.8,11.5,22.2"/>
					<path class="st47" d="M31.2,12.8c-1.9,0.9-13.5,8.7-13.5,20c0,11.7,3.8,16.8,11.5,22.2"/>
					<path class="st46" d="M31.2,12.9c-1.9,0.9-13.5,8.7-13.5,19.9c0,11.7,3.8,16.8,11.5,22.2"/>
					<path class="st45" d="M31.2,12.9c-1.9,0.9-13.5,8.7-13.5,19.9c0,11.7,3.8,16.7,11.5,22.2"/>
					<path class="st44" d="M31.2,12.9c-1.9,0.9-13.4,8.7-13.4,19.9c0,11.7,3.8,16.7,11.5,22.2"/>
					<path class="st43" d="M31.2,12.9c-1.9,0.9-13.4,8.7-13.4,19.9c0,11.7,3.8,16.7,11.5,22.2"/>
					<path class="st42" d="M31.2,12.9c-1.9,0.8-13.4,8.6-13.4,19.9c0,11.7,3.8,16.7,11.5,22.1"/>
					<path class="st41" d="M31.3,13c-1.9,0.8-13.4,8.6-13.4,19.8c0,11.7,3.8,16.7,11.5,22.1"/>
					<path class="st40" d="M31.3,13c-1.9,0.8-13.4,8.6-13.4,19.8c0,11.7,3.8,16.7,11.5,22.1"/>
					<path class="st39" d="M31.3,13c-1.9,0.8-13.4,8.6-13.4,19.8c0,11.7,3.8,16.7,11.4,22.1"/>
					<path class="st38" d="M31.3,13c-1.9,0.8-13.4,8.6-13.4,19.8c0,11.7,3.8,16.7,11.4,22.1"/>
					<path class="st37" d="M31.3,13c-1.8,0.8-13.4,8.6-13.4,19.8c0,11.7,3.8,16.6,11.4,22"/>
					<path class="st36" d="M31.3,13.1C29.5,13.9,18,21.7,18,32.8c0,11.7,3.8,16.6,11.4,22"/>
					<path class="st35" d="M31.3,13.1C29.5,13.9,18,21.7,18,32.8c0,11.7,3.8,16.6,11.4,22"/>
					<path class="st34" d="M31.3,13.1C29.5,13.9,18,21.7,18,32.8c0,11.7,3.8,16.6,11.4,22"/>
					<path class="st33" d="M31.3,13.1C29.5,13.9,18,21.7,18,32.8c0,11.7,3.8,16.6,11.4,22"/>
					<path class="st32" d="M31.3,13.1C29.5,14,18,21.7,18,32.8c0,11.6,3.8,16.6,11.4,22"/>
					<path class="st31" d="M31.3,13.2c-1.8,0.8-13.3,8.6-13.3,19.6c0,11.6,3.8,16.6,11.4,21.9"/>
					<path class="st30" d="M31.4,13.2c-1.8,0.8-13.3,8.6-13.3,19.6c0,11.6,3.8,16.6,11.4,21.9"/>
					<path class="st29" d="M31.4,13.2c-1.8,0.8-13.3,8.5-13.3,19.6c0,11.6,3.8,16.5,11.4,21.9"/>
					<path class="st28" d="M31.4,13.2c-1.8,0.8-13.2,8.5-13.2,19.6c0,11.6,3.8,16.5,11.3,21.9"/>
					<path class="st27" d="M31.4,13.2c-1.8,0.8-13.2,8.5-13.2,19.6c0,11.6,3.8,16.5,11.3,21.9"/>
					<path class="st26" d="M31.4,13.2c-1.8,0.8-13.2,8.5-13.2,19.5c0,11.6,3.8,16.5,11.3,21.9"/>
					<path class="st25" d="M31.4,13.3c-1.8,0.8-13.2,8.5-13.2,19.5c0,11.6,3.8,16.5,11.3,21.8"/>
					<path class="st24" d="M31.4,13.3c-1.8,0.8-13.2,8.5-13.2,19.5c0,11.6,3.8,16.5,11.3,21.8"/>
					<path class="st23" d="M31.4,13.3c-1.8,0.8-13.2,8.5-13.2,19.5c0,11.6,3.8,16.5,11.3,21.8"/>
					<path class="st22" d="M31.4,13.3c-1.8,0.8-13.2,8.5-13.2,19.5c0,11.6,3.8,16.5,11.3,21.8"/>
					<path class="st21" d="M31.4,13.3c-1.8,0.8-13.2,8.5-13.2,19.4c0,11.6,3.8,16.4,11.3,21.8"/>
					<path class="st20" d="M31.4,13.4c-1.8,0.8-13.1,8.5-13.1,19.4c0,11.6,3.8,16.4,11.3,21.8"/>
					<path class="st19" d="M31.4,13.4c-1.8,0.8-13.1,8.5-13.1,19.4c0,11.6,3.8,16.4,11.3,21.7"/>
					<path class="st18" d="M31.5,13.4c-1.8,0.8-13.1,8.5-13.1,19.4c0,11.6,3.8,16.4,11.3,21.7"/>
					<path class="st17" d="M31.5,13.4c-1.8,0.8-13.1,8.5-13.1,19.4c0,11.5,3.8,16.4,11.2,21.7"/>
					<path class="st16" d="M31.5,13.4c-1.8,0.8-13.1,8.4-13.1,19.4c0,11.5,3.8,16.4,11.2,21.7"/>
					<path class="st15" d="M31.5,13.5c-1.8,0.8-13.1,8.4-13.1,19.3c0,11.5,3.8,16.4,11.2,21.7"/>
					<path class="st14" d="M31.5,13.5c-1.7,0.8-13.1,8.4-13.1,19.3c0,11.5,3.8,16.4,11.2,21.7"/>
					<path class="st13" d="M31.5,13.5c-1.7,0.8-13.1,8.4-13.1,19.3c0,11.5,3.8,16.3,11.2,21.6"/>
					<path class="st12" d="M31.5,13.5c-1.7,0.8-13,8.4-13,19.3c0,11.5,3.8,16.3,11.2,21.6"/>
					<path class="st11" d="M31.5,13.5c-1.7,0.8-13,8.4-13,19.3c0,11.5,3.8,16.3,11.2,21.6"/>
					<path class="st10" d="M31.5,13.6c-1.7,0.8-13,8.4-13,19.2c0,11.5,3.8,16.3,11.2,21.6"/>
					<path class="st9" d="M31.5,13.6c-1.7,0.8-13,8.4-13,19.2c0,11.5,3.8,16.3,11.2,21.6"/>
					<path class="st8" d="M31.5,13.6c-1.7,0.8-13,8.4-13,19.2c0,11.5,3.8,16.3,11.2,21.5"/>
					<path class="st7" d="M31.6,13.6c-1.7,0.8-13,8.4-13,19.2c0,11.5,3.8,16.3,11.2,21.5"/>
					<path class="st6" d="M31.6,13.6c-1.7,0.8-13,8.4-13,19.2c0,11.5,3.8,16.3,11.1,21.5"/>
					<path class="st5" d="M31.6,13.7c-1.7,0.8-13,8.4-13,19.1c0,11.5,3.7,16.2,11.1,21.5"/>
					<path class="st4" d="M31.6,13.7c-1.7,0.8-12.9,8.4-12.9,19.1c0,11.5,3.7,16.2,11.1,21.5"/>
					<path class="st3" d="M31.6,13.7c-1.7,0.8-12.9,8.4-12.9,19.1c0,11.5,3.7,16.2,11.1,21.5"/>
					<path class="st2" d="M29.8,54.2c-7.4-5.2-11.1-10-11.1-21.4c0-10.7,11.2-18.3,12.9-19.1"/>
				</g>
			</g>
		</g>
		<g>
			<g>
				<g>
					<g>
						<g>
							<path class="st0" d="M51.1,19.4c-0.1,0-0.2,0-0.3,0c-0.3,0-0.5-0.1-0.6-0.3C50.1,19,50,18.8,50,18.4V17h-0.4v-0.7H50v-0.8
								h0.8v0.8h0.5V17h-0.5v1.2c0,0.2,0,0.3,0.1,0.3c0,0.1,0.1,0.1,0.2,0.1c0.1,0,0.1,0,0.2,0v0.7C51.3,19.4,51.2,19.4,51.1,19.4z"
								/>
							<path class="st0" d="M52.6,19.4h-0.9v-4.5h0.9v1.8h0c0-0.1,0.1-0.3,0.2-0.4c0.1-0.1,0.3-0.2,0.4-0.2c0.2,0,0.3,0,0.4,0.1
								c0.1,0.1,0.2,0.2,0.2,0.3C54,16.7,54,16.8,54,17c0,0.2,0,0.3,0,0.5v1.9h-0.9v-1.9c0-0.1,0-0.2-0.1-0.3c0-0.1-0.1-0.1-0.2-0.1
								c-0.1,0-0.2,0.1-0.3,0.2c-0.1,0.1-0.1,0.2-0.1,0.4V19.4z"/>
						</g>
					</g>
				</g>
			</g>
			<path class="st0" d="M45.4,8.5c0.7,0,1.1,0.5,1.3,1.2c0.3,0.8,0.4,1.9,0.4,2.7c0,0.7-0.1,2.1-0.4,2.9c-0.4,1-0.9,1.1-1.2,1.1
				c-1.3,0-1.5-2.2-1.6-3c0-0.3,0-0.7,0-1c0-1.1,0.1-2.1,0.4-2.8C44.5,8.7,45,8.5,45.4,8.5 M45.6,5.6c-1.9,0-3.7,1-4.2,4.7
				c-0.1,0.7-0.1,1.5-0.1,2.3c0,1.7,0.2,4.1,1.5,5.6c1,1.1,2.1,1.2,2.7,1.2c2.4,0,4.2-2,4.2-6.9c0-1.6-0.1-3.7-1.1-5.3
				C47.8,6.2,46.8,5.7,45.6,5.6 M35.4,11.2H36c0.7,0,1.4,0,1.6-0.2c0.2-0.2,0.4-0.5,0.4-1.1c0-1-0.6-1.2-1.2-1.2
				c-1.2,0-1.5,0.8-1.6,1.2l-2.4-0.2c0.2-1.3,0.5-3.8,3.9-3.8c0.4,0,0.8,0,1.2,0.1c0.6,0.1,1.4,0.2,2,1c0.6,0.7,0.7,1.9,0.7,2.6
				c0,0.3,0,0.8-0.2,1.2c-0.2,0.5-0.5,1.1-1.2,1.5c-0.1,0-0.2,0.1-0.3,0.1c0.1,0,0.2,0.1,0.3,0.1c0.9,0.3,1.5,1.2,1.5,2.9
				c0,3.9-2.6,4.2-4.2,4.2c-1.2,0-2.4-0.1-3.2-1.2c-0.8-1-0.9-2.3-1-3l2.3-0.2c0,0.2,0.2,0.7,0.4,1c0.4,0.4,0.8,0.5,1.4,0.5
				c1.1,0,1.5-0.6,1.5-1.5c0-1.2-0.8-1.3-1.2-1.3h-1.6V11.2z"/>
		</g>
	</g>
	<g>
		<path class="st143" d="M51.3,30.5c-5.1,0.8-3.9,6-7.1,6.7c-5.1,1.2-5.4-7.7-10.6-11c-7.4-4.6-7.8,4.6-11.8,1.2
			C24.9,37.2,32.9,28,37,39.1c-3.1,0-8.6,0.2-8.9,4.8c-0.4,5.4,4.3,6.3,2.3,8.5c8.9,0,1.9-7.4,8.3-7.7c4.4-0.2,4.4,4.1,0.1,4.6
			c2,0.8,9.4,1.1,10.9-3.6c1.2-3.8-2.3-7.3,2.6-9.1c3.2-1.2,5.3,1.6,5.8,2.8C60.5,35.5,56.1,29.8,51.3,30.5"/>
		<g>
			<defs>
				<path id="SVGID_00000011015846849219048250000000442756698859399065_" d="M26.9,26c-1.8,1.4-2.9,3.4-5.1,1.5
					c3.1,9.7,11.1,0.4,15.3,11.6c-3.1-0.1-8.6,0.2-9,4.8c-0.4,5.4,4.3,6.3,2.3,8.5c8.6,0,2.2-7,7.7-7.7c0.2,0,0.3,0,0.5,0
					c4.4-0.2,4.4,4.1,0.1,4.6c2,0.8,9.4,1.1,10.9-3.6c1.2-3.8-2.3-7.3,2.6-9.1c3.2-1.2,5.3,1.7,5.8,2.8c2.4-4-1.9-9.6-6.8-8.9
					c-4.8,0.7-4,5.5-6.7,6.6c-0.1,0.1-0.3,0.1-0.4,0.1c-5.2,1.2-5.3-7.7-10.6-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.4,26.9,26"
					/>
			</defs>
			<clipPath id="SVGID_00000073681457009589390500000000822159928041452940_">
				<use xlink:href="#SVGID_00000011015846849219048250000000442756698859399065_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000155137615832302596480000015925464333150410375_" cx="-317.8858" cy="550.735" r="0.4217" gradientTransform="matrix(63.2979 0 0 -63.2979 20168.1016 34902.5234)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#564700"/>
				<stop  offset="0.1648" style="stop-color:#514200"/>
				<stop  offset="0.4121" style="stop-color:#524200"/>
				<stop  offset="0.489" style="stop-color:#564700"/>
				<stop  offset="0.6703" style="stop-color:#564700"/>
				<stop  offset="0.7454" style="stop-color:#544500"/>
				<stop  offset="0.7527" style="stop-color:#534400"/>
				<stop  offset="0.9121" style="stop-color:#544500"/>
				<stop  offset="1" style="stop-color:#564700"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000073681457009589390500000000822159928041452940_);fill:url(#SVGID_00000155137615832302596480000015925464333150410375_);" width="38.7" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000167383349287398496770000000552506680274162096_" d="M26.9,26c-1.8,1.4-2.9,3.4-5.1,1.5
					c3.1,9.7,11.1,0.4,15.3,11.6c-3.1-0.1-8.7,0.1-9,4.7c-0.4,5.4,4.4,6.3,2.3,8.5c8.6-0.1,2.1-7.1,7.7-7.7c0.2,0,0.3,0,0.5,0
					c4.4-0.2,4.4,4.1,0.1,4.6c2,0.8,9.3,1.1,10.9-3.6c1.2-3.8-2.3-7.3,2.6-9.1c3.2-1.2,5.3,1.7,5.9,2.9c2.4-4-2-9.6-6.8-8.9
					c-4.8,0.7-4,5.5-6.7,6.6c-0.1,0.1-0.3,0.1-0.4,0.1c-5.3,1.2-5.3-7.7-10.6-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.4,26.9,26"
					/>
			</defs>
			<clipPath id="SVGID_00000116219269557988792790000005516286803028500916_">
				<use xlink:href="#SVGID_00000167383349287398496770000000552506680274162096_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000157993899977131595800000001436782362162984112_" cx="-317.8901" cy="550.7334" r="0.4213" gradientTransform="matrix(63.2892 0 0 -63.2892 20165.5898 34897.625)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#5C4D00"/>
				<stop  offset="0.1648" style="stop-color:#534300"/>
				<stop  offset="0.4121" style="stop-color:#544400"/>
				<stop  offset="0.489" style="stop-color:#5C4D00"/>
				<stop  offset="0.6703" style="stop-color:#5C4C00"/>
				<stop  offset="0.735" style="stop-color:#5A4A00"/>
				<stop  offset="0.7527" style="stop-color:#564600"/>
				<stop  offset="0.9121" style="stop-color:#594900"/>
				<stop  offset="1" style="stop-color:#5C4C00"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000116219269557988792790000005516286803028500916_);fill:url(#SVGID_00000157993899977131595800000001436782362162984112_);" width="38.6" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000079487033864316856130000007742688979917320869_" d="M26.9,26c-1.8,1.4-2.9,3.4-5.1,1.5
					c3.1,9.7,11.2,0.4,15.3,11.7c-3.1-0.2-8.7,0.1-9,4.7c-0.4,5.4,4.4,6.3,2.3,8.5c8.6-0.1,2.1-7.1,7.7-7.7c0.2,0,0.3,0,0.5,0
					c4.5-0.2,4.4,4.1,0.1,4.7c2,0.8,9.3,1.1,10.9-3.6c1.3-3.8-2.3-7.3,2.6-9.1c3.2-1.2,5.3,1.7,5.9,2.9c2.4-4-2-9.6-6.8-8.9
					c-4.9,0.7-4,5.5-6.7,6.6c-0.1,0.1-0.3,0.1-0.4,0.1c-5.3,1.2-5.3-7.7-10.6-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.4,26.9,26"
					/>
			</defs>
			<clipPath id="SVGID_00000166653493374699720220000007298984242345580474_">
				<use xlink:href="#SVGID_00000079487033864316856130000007742688979917320869_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000145744790486743431060000011609213408573791632_" cx="-317.8906" cy="550.731" r="0.4217" gradientTransform="matrix(63.281 0 0 -63.281 20163.0176 34892.9609)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#625300"/>
				<stop  offset="0.1648" style="stop-color:#544500"/>
				<stop  offset="0.4121" style="stop-color:#564500"/>
				<stop  offset="0.476" style="stop-color:#5F5005"/>
				<stop  offset="0.489" style="stop-color:#625306"/>
				<stop  offset="0.6703" style="stop-color:#625306"/>
				<stop  offset="0.7282" style="stop-color:#605105"/>
				<stop  offset="0.7507" style="stop-color:#5B4A01"/>
				<stop  offset="0.7527" style="stop-color:#5A4900"/>
				<stop  offset="0.9121" style="stop-color:#5D4D00"/>
				<stop  offset="1" style="stop-color:#625200"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000166653493374699720220000007298984242345580474_);fill:url(#SVGID_00000145744790486743431060000011609213408573791632_);" width="38.6" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000166661667076383326500000010963223074042511022_" d="M26.9,26c-1.8,1.4-2.9,3.4-5.1,1.5
					c3.1,9.7,11.2,0.3,15.3,11.7c-3.1-0.2-8.7,0.1-9,4.7c-0.4,5.4,4.4,6.4,2.3,8.5c8.6-0.1,2-7.1,7.7-7.8c0.2,0,0.3,0,0.5,0
					c4.5-0.2,4.4,4.2,0.1,4.7c2,0.8,9.3,1.1,10.9-3.6c1.3-3.8-2.3-7.4,2.6-9.2c3.3-1.2,5.3,1.7,5.9,2.9c2.4-4-2-9.6-6.8-8.8
					c-4.9,0.7-4,5.5-6.7,6.6c-0.1,0.1-0.3,0.1-0.4,0.1c-5.4,1.2-5.3-7.7-10.6-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26"
					/>
			</defs>
			<clipPath id="SVGID_00000056397186971098506040000009180317907910660543_">
				<use xlink:href="#SVGID_00000166661667076383326500000010963223074042511022_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000119834711866450740600000000399924987063166133_" cx="-317.8868" cy="550.7308" r="0.4214" gradientTransform="matrix(63.2728 0 0 -63.2728 20160.1484 34888.4258)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#695900"/>
				<stop  offset="0.1648" style="stop-color:#564600"/>
				<stop  offset="0.4121" style="stop-color:#584700"/>
				<stop  offset="0.4646" style="stop-color:#625209"/>
				<stop  offset="0.489" style="stop-color:#69590F"/>
				<stop  offset="0.6703" style="stop-color:#68590F"/>
				<stop  offset="0.7234" style="stop-color:#67570D"/>
				<stop  offset="0.744" style="stop-color:#615106"/>
				<stop  offset="0.7527" style="stop-color:#5D4C00"/>
				<stop  offset="0.9121" style="stop-color:#615100"/>
				<stop  offset="1" style="stop-color:#695904"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000056397186971098506040000009180317907910660543_);fill:url(#SVGID_00000119834711866450740600000000399924987063166133_);" width="38.6" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000099659541288312640430000011243079324260122292_" d="M26.9,26c-1.8,1.4-2.9,3.5-5.1,1.5
					c3.1,9.7,11.2,0.3,15.4,11.7c-3.1-0.2-8.7,0-9.1,4.6c-0.4,5.4,4.4,6.4,2.3,8.5c8.7-0.2,1.9-7.2,7.7-7.8c0.2,0,0.3,0,0.5,0
					c4.5-0.1,4.4,4.2,0.1,4.7c2,0.8,9.3,1.1,10.9-3.6c1.3-3.8-2.3-7.4,2.6-9.2c3.3-1.2,5.3,1.8,5.9,2.9c2.3-4-2.1-9.5-6.8-8.8
					c-4.9,0.7-4,5.5-6.7,6.6c-0.1,0.1-0.3,0.1-0.4,0.1c-5.4,1.2-5.3-7.7-10.6-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26"
					/>
			</defs>
			<clipPath id="SVGID_00000042000180157568601520000001737483464658359996_">
				<use xlink:href="#SVGID_00000099659541288312640430000011243079324260122292_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000061439462144296723740000003930401789858021284_" cx="-317.8802" cy="550.7404" r="0.4218" gradientTransform="matrix(63.2647 0 0 -63.2647 20157.1582 34884.5742)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#6F6006"/>
				<stop  offset="0.1648" style="stop-color:#584700"/>
				<stop  offset="0.4121" style="stop-color:#5A4800"/>
				<stop  offset="0.454" style="stop-color:#64530B"/>
				<stop  offset="0.489" style="stop-color:#6F6017"/>
				<stop  offset="0.6703" style="stop-color:#6E5F17"/>
				<stop  offset="0.7187" style="stop-color:#6D5E15"/>
				<stop  offset="0.7375" style="stop-color:#69590E"/>
				<stop  offset="0.7511" style="stop-color:#615002"/>
				<stop  offset="0.7527" style="stop-color:#604F00"/>
				<stop  offset="0.9121" style="stop-color:#665500"/>
				<stop  offset="1" style="stop-color:#6F5E0C"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000042000180157568601520000001737483464658359996_);fill:url(#SVGID_00000061439462144296723740000003930401789858021284_);" width="38.6" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000105410469317631828130000006637668427288834973_" d="M26.9,26c-1.8,1.4-2.9,3.5-5.1,1.5
					c3.1,9.7,11.3,0.2,15.4,11.8c-3.1-0.3-8.8,0-9.1,4.6c-0.4,5.4,4.5,6.4,2.3,8.5c8.7-0.2,1.9-7.2,7.7-7.8c0.2,0,0.3,0,0.5,0
					c4.5-0.1,4.4,4.2,0.1,4.7c2,0.8,9.3,1,10.9-3.6c1.3-3.8-2.3-7.4,2.6-9.2c3.3-1.2,5.3,1.8,5.9,2.9c2.3-4-2.1-9.5-6.8-8.8
					c-4.9,0.7-4,5.5-6.8,6.5c-0.1,0-0.3,0.1-0.4,0.1c-5.5,1.2-5.3-7.7-10.5-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26"/>
			</defs>
			<clipPath id="SVGID_00000104706797455076760410000015495284510614009266_">
				<use xlink:href="#SVGID_00000105410469317631828130000006637668427288834973_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000017479018590644389050000004794073703591014023_" cx="-317.8893" cy="550.7299" r="0.4214" gradientTransform="matrix(63.2566 0 0 -63.2566 20155.1523 34879.4727)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#76660D"/>
				<stop  offset="0.1586" style="stop-color:#5A4A01"/>
				<stop  offset="0.1648" style="stop-color:#594900"/>
				<stop  offset="0.4121" style="stop-color:#5C4900"/>
				<stop  offset="0.4465" style="stop-color:#65530B"/>
				<stop  offset="0.489" style="stop-color:#76661F"/>
				<stop  offset="0.6703" style="stop-color:#75651F"/>
				<stop  offset="0.7157" style="stop-color:#74641D"/>
				<stop  offset="0.7333" style="stop-color:#705F16"/>
				<stop  offset="0.746" style="stop-color:#69570A"/>
				<stop  offset="0.7527" style="stop-color:#635100"/>
				<stop  offset="0.9121" style="stop-color:#6A5900"/>
				<stop  offset="1" style="stop-color:#766513"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000104706797455076760410000015495284510614009266_);fill:url(#SVGID_00000017479018590644389050000004794073703591014023_);" width="38.6" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000137094944539147031040000005816810144203500698_" d="M26.9,26c-1.8,1.4-2.9,3.5-5.1,1.5
					c3.1,9.7,11.3,0.2,15.4,11.8c-3.1-0.3-8.8-0.1-9.1,4.6c-0.4,5.4,4.5,6.4,2.3,8.5c8.7-0.2,1.8-7.2,7.7-7.8c0.2,0,0.3,0,0.5,0
					c4.5-0.1,4.4,4.2,0.1,4.7c2,0.8,9.2,1,10.8-3.6c1.3-3.8-2.3-7.4,2.6-9.2c3.3-1.2,5.3,1.8,5.9,3c2.3-4-2.1-9.5-6.8-8.8
					c-4.9,0.8-4,5.5-6.8,6.5c-0.1,0-0.3,0.1-0.4,0.1c-5.5,1.2-5.3-7.7-10.5-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26"/>
			</defs>
			<clipPath id="SVGID_00000066514136475876861520000000231131373373818303_">
				<use xlink:href="#SVGID_00000137094944539147031040000005816810144203500698_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000000194921766088135150000016489445009753774237_" cx="-317.8781" cy="550.739" r="0.4215" gradientTransform="matrix(63.2483 0 0 -63.2483 20151.7949 34875.4258)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#7C6D13"/>
				<stop  offset="0.1337" style="stop-color:#615104"/>
				<stop  offset="0.1648" style="stop-color:#5A4A00"/>
				<stop  offset="0.4121" style="stop-color:#5E4B00"/>
				<stop  offset="0.4414" style="stop-color:#67550B"/>
				<stop  offset="0.489" style="stop-color:#7C6D26"/>
				<stop  offset="0.6703" style="stop-color:#7B6C26"/>
				<stop  offset="0.7137" style="stop-color:#7A6B24"/>
				<stop  offset="0.7306" style="stop-color:#76661D"/>
				<stop  offset="0.7428" style="stop-color:#705F11"/>
				<stop  offset="0.7527" style="stop-color:#675400"/>
				<stop  offset="0.9121" style="stop-color:#6F5D00"/>
				<stop  offset="1" style="stop-color:#7C6B19"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000066514136475876861520000000231131373373818303_);fill:url(#SVGID_00000000194921766088135150000016489445009753774237_);" width="38.6" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000002385646195663712670000005050339116302011810_" d="M26.9,26c-1.8,1.4-2.9,3.5-5.1,1.5
					c3.1,9.7,11.3,0.2,15.4,11.8c-3.1-0.3-8.8-0.1-9.1,4.5c-0.4,5.4,4.5,6.4,2.3,8.5c8.7-0.3,1.8-7.3,7.7-7.8c0.2,0,0.3,0,0.5,0
					c4.5-0.1,4.4,4.3,0.1,4.8c2,0.8,9.2,1,10.8-3.6c1.3-3.8-2.4-7.4,2.6-9.2c3.3-1.2,5.3,1.8,5.9,3c2.3-4-2.1-9.5-6.8-8.8
					c-4.9,0.8-4,5.5-6.8,6.5c-0.1,0-0.3,0.1-0.4,0.1c-5.6,1.2-5.3-7.7-10.5-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26"/>
			</defs>
			<clipPath id="SVGID_00000084527331105470063540000017626015972144095144_">
				<use xlink:href="#SVGID_00000002385646195663712670000005050339116302011810_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000146462449222108851480000015066361776401219980_" cx="-317.8856" cy="550.7297" r="0.4215" gradientTransform="matrix(63.2404 0 0 -63.2404 20149.752 34870.5195)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#817318"/>
				<stop  offset="0.1185" style="stop-color:#675707"/>
				<stop  offset="0.1648" style="stop-color:#5C4B00"/>
				<stop  offset="0.4121" style="stop-color:#604C00"/>
				<stop  offset="0.4373" style="stop-color:#68550B"/>
				<stop  offset="0.4817" style="stop-color:#7D6E28"/>
				<stop  offset="0.489" style="stop-color:#81732E"/>
				<stop  offset="0.6703" style="stop-color:#80722D"/>
				<stop  offset="0.7122" style="stop-color:#7F712B"/>
				<stop  offset="0.7284" style="stop-color:#7C6C24"/>
				<stop  offset="0.7402" style="stop-color:#766518"/>
				<stop  offset="0.7498" style="stop-color:#6D5A07"/>
				<stop  offset="0.7527" style="stop-color:#6A5600"/>
				<stop  offset="0.9121" style="stop-color:#736000"/>
				<stop  offset="1" style="stop-color:#81711F"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000084527331105470063540000017626015972144095144_);fill:url(#SVGID_00000146462449222108851480000015066361776401219980_);" width="38.5" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000152229860915408526370000018383999744762333108_" d="M26.9,26c-1.8,1.4-2.8,3.5-5.1,1.5
					c3.1,9.7,11.4,0.1,15.5,11.9c-3.2-0.4-8.8-0.1-9.2,4.5c-0.4,5.4,4.5,6.4,2.3,8.5c8.7-0.3,1.7-7.3,7.7-7.9c0.2,0,0.3,0,0.5,0
					c4.5-0.1,4.4,4.3,0.1,4.8c2,0.8,9.2,1,10.8-3.6c1.3-3.8-2.4-7.4,2.6-9.3c3.4-1.2,5.3,1.8,5.9,3c2.3-4-2.2-9.5-6.8-8.8
					c-4.9,0.8-4,5.5-6.8,6.5c-0.1,0-0.3,0.1-0.4,0.1c-5.6,1.2-5.2-7.7-10.5-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26"/>
			</defs>
			<clipPath id="SVGID_00000061433056534074911840000010694849487310975151_">
				<use xlink:href="#SVGID_00000152229860915408526370000018383999744762333108_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000080888594202593563250000016613869724404760761_" cx="-317.8807" cy="550.7324" r="0.4216" gradientTransform="matrix(63.2324 0 0 -63.2324 20146.9043 34866.2773)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#887A1D"/>
				<stop  offset="0.1043" style="stop-color:#6E5E0B"/>
				<stop  offset="0.1648" style="stop-color:#5D4C00"/>
				<stop  offset="0.4121" style="stop-color:#614D00"/>
				<stop  offset="0.4347" style="stop-color:#69560B"/>
				<stop  offset="0.4744" style="stop-color:#7F6F28"/>
				<stop  offset="0.489" style="stop-color:#887A35"/>
				<stop  offset="0.6703" style="stop-color:#867835"/>
				<stop  offset="0.7107" style="stop-color:#857733"/>
				<stop  offset="0.7264" style="stop-color:#82732C"/>
				<stop  offset="0.7378" style="stop-color:#7C6C20"/>
				<stop  offset="0.7471" style="stop-color:#74620F"/>
				<stop  offset="0.7527" style="stop-color:#6D5900"/>
				<stop  offset="0.9121" style="stop-color:#776400"/>
				<stop  offset="1" style="stop-color:#887825"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000061433056534074911840000010694849487310975151_);fill:url(#SVGID_00000080888594202593563250000016613869724404760761_);" width="38.5" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000140705386754224655490000009335606701776757409_" d="M26.9,26c-1.8,1.4-2.8,3.5-5.1,1.5
					c3.1,9.7,11.4,0.1,15.5,11.9c-3.2-0.4-8.9-0.2-9.2,4.4c-0.4,5.4,4.5,6.4,2.3,8.5c8.7-0.4,1.6-7.4,7.7-7.9c0.2,0,0.3,0,0.5,0
					c4.5-0.1,4.4,4.3,0.1,4.8c2,0.8,9.2,1,10.8-3.6c1.3-3.8-2.4-7.5,2.6-9.3c3.4-1.2,5.3,1.9,5.9,3c2.3-4-2.2-9.5-6.8-8.8
					c-4.9,0.8-4,5.6-6.8,6.5c-0.1,0-0.3,0.1-0.4,0.1c-5.7,1.2-5.2-7.8-10.5-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26"/>
			</defs>
			<clipPath id="SVGID_00000046306227705783906870000017810573283331802273_">
				<use xlink:href="#SVGID_00000140705386754224655490000009335606701776757409_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000102507136832491115440000007472758730154778287_" cx="-317.8836" cy="550.7279" r="0.4216" gradientTransform="matrix(63.2245 0 0 -63.2245 20144.5527 34861.6719)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#8F8023"/>
				<stop  offset="9.499209e-02" style="stop-color:#756410"/>
				<stop  offset="0.1648" style="stop-color:#5F4D00"/>
				<stop  offset="0.4121" style="stop-color:#634E00"/>
				<stop  offset="0.4326" style="stop-color:#6B570B"/>
				<stop  offset="0.4687" style="stop-color:#807028"/>
				<stop  offset="0.489" style="stop-color:#8E803C"/>
				<stop  offset="0.6703" style="stop-color:#8D7F3C"/>
				<stop  offset="0.7097" style="stop-color:#8C7E3A"/>
				<stop  offset="0.7249" style="stop-color:#897A33"/>
				<stop  offset="0.736" style="stop-color:#837227"/>
				<stop  offset="0.745" style="stop-color:#7B6816"/>
				<stop  offset="0.7527" style="stop-color:#705B00"/>
				<stop  offset="0.893" style="stop-color:#7A6600"/>
				<stop  offset="0.9121" style="stop-color:#7C6800"/>
				<stop  offset="1" style="stop-color:#8E7E2B"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000046306227705783906870000017810573283331802273_);fill:url(#SVGID_00000102507136832491115440000007472758730154778287_);" width="38.5" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000175317714225970576710000003052379425050622887_" d="M26.9,26c-1.8,1.4-2.8,3.5-5.1,1.5
					c3.1,9.7,11.4,0.1,15.5,12c-3.2-0.4-8.9-0.2-9.2,4.4c-0.4,5.4,4.6,6.4,2.3,8.5c8.7-0.4,1.6-7.4,7.7-7.9c0.2,0,0.4,0,0.5,0
					c4.6-0.1,4.4,4.3,0.1,4.8c2,0.8,9.2,1,10.8-3.6c1.3-3.8-2.4-7.5,2.6-9.3c3.4-1.2,5.3,1.9,5.9,3c2.3-4-2.2-9.4-6.8-8.7
					c-4.9,0.8-4,5.6-6.8,6.5c-0.1,0-0.3,0.1-0.4,0.1c-5.7,1.2-5.2-7.8-10.5-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26"/>
			</defs>
			<clipPath id="SVGID_00000111170643251598939200000008712171777106263743_">
				<use xlink:href="#SVGID_00000175317714225970576710000003052379425050622887_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000176002755839301729460000016192589422488763799_" cx="-317.8867" cy="550.7263" r="0.4215" gradientTransform="matrix(63.2167 0 0 -63.2167 20142.2871 34857.25)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#968628"/>
				<stop  offset="8.726253e-02" style="stop-color:#7B6A14"/>
				<stop  offset="0.1648" style="stop-color:#604E00"/>
				<stop  offset="0.4121" style="stop-color:#655000"/>
				<stop  offset="0.4307" style="stop-color:#6D590B"/>
				<stop  offset="0.4634" style="stop-color:#827128"/>
				<stop  offset="0.489" style="stop-color:#958744"/>
				<stop  offset="0.6703" style="stop-color:#948544"/>
				<stop  offset="0.7086" style="stop-color:#938442"/>
				<stop  offset="0.7235" style="stop-color:#90803B"/>
				<stop  offset="0.7343" style="stop-color:#8A792F"/>
				<stop  offset="0.7431" style="stop-color:#816F1E"/>
				<stop  offset="0.7507" style="stop-color:#776208"/>
				<stop  offset="0.7527" style="stop-color:#735E00"/>
				<stop  offset="0.893" style="stop-color:#7E6900"/>
				<stop  offset="0.9121" style="stop-color:#806B00"/>
				<stop  offset="1" style="stop-color:#968431"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000111170643251598939200000008712171777106263743_);fill:url(#SVGID_00000176002755839301729460000016192589422488763799_);" width="38.5" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000101081791645160278140000002842722069876491908_" d="M26.9,26c-1.8,1.4-2.8,3.6-5.1,1.5
					c3.1,9.7,11.4,0,15.6,12c-3.2-0.5-8.9-0.2-9.3,4.4c-0.4,5.4,4.6,6.4,2.3,8.5c8.7-0.4,1.5-7.4,7.7-7.9c0.2,0,0.4,0,0.5,0
					c4.6,0,4.4,4.4,0.1,4.8c2,0.8,9.2,1,10.8-3.6c1.3-3.7-2.4-7.5,2.6-9.3c3.4-1.3,5.3,1.9,5.9,3.1c2.2-4-2.2-9.4-6.8-8.7
					c-4.9,0.8-4,5.6-6.9,6.5c-0.1,0-0.3,0.1-0.4,0.1c-5.8,1.2-5.2-7.8-10.4-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26"/>
			</defs>
			<clipPath id="SVGID_00000066508713403624237080000004220810621899009163_">
				<use xlink:href="#SVGID_00000101081791645160278140000002842722069876491908_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000089535459872775955030000008723678220510642876_" cx="-317.8769" cy="550.7363" r="0.4215" gradientTransform="matrix(63.2088 0 0 -63.2088 20139.1602 34853.5273)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#9D8D2D"/>
				<stop  offset="7.986256e-02" style="stop-color:#827119"/>
				<stop  offset="0.1648" style="stop-color:#614F00"/>
				<stop  offset="0.4121" style="stop-color:#675100"/>
				<stop  offset="0.4292" style="stop-color:#6F5A0B"/>
				<stop  offset="0.4591" style="stop-color:#837128"/>
				<stop  offset="0.489" style="stop-color:#9C8E4C"/>
				<stop  offset="0.6703" style="stop-color:#9A8C4C"/>
				<stop  offset="0.7077" style="stop-color:#998B4A"/>
				<stop  offset="0.7222" style="stop-color:#968743"/>
				<stop  offset="0.7328" style="stop-color:#908037"/>
				<stop  offset="0.7414" style="stop-color:#887626"/>
				<stop  offset="0.7488" style="stop-color:#7D6910"/>
				<stop  offset="0.7527" style="stop-color:#766000"/>
				<stop  offset="0.8781" style="stop-color:#806B00"/>
				<stop  offset="0.9121" style="stop-color:#846F00"/>
				<stop  offset="1" style="stop-color:#9C8A36"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000066508713403624237080000004220810621899009163_);fill:url(#SVGID_00000089535459872775955030000008723678220510642876_);" width="38.5" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000055709181114864593760000010344943176481570210_" d="M26.9,26.1c-1.8,1.4-2.8,3.6-5.1,1.5
					c3.1,9.7,11.5,0,15.6,12c-3.2-0.5-9-0.3-9.3,4.3c-0.4,5.4,4.6,6.4,2.3,8.5c8.7-0.5,1.4-7.5,7.7-7.9c0.2,0,0.4,0,0.5,0
					c4.6,0,4.4,4.4,0.1,4.8c2,0.8,9.1,1,10.8-3.6c1.3-3.7-2.4-7.5,2.6-9.3c3.5-1.3,5.4,1.9,5.9,3.1c2.2-4-2.3-9.4-6.8-8.7
					c-4.9,0.8-4,5.6-6.9,6.5c-0.1,0-0.3,0.1-0.4,0.1c-5.8,1.2-5.2-7.8-10.4-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26.1"
					/>
			</defs>
			<clipPath id="SVGID_00000105403760271284256690000011069086663761020852_">
				<use xlink:href="#SVGID_00000055709181114864593760000010344943176481570210_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000055687930863408658620000014389876148708863667_" cx="-317.8762" cy="550.7354" r="0.4216" gradientTransform="matrix(63.2011 0 0 -63.2011 20136.6621 34849.2266)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#A49433"/>
				<stop  offset="7.443804e-02" style="stop-color:#89781E"/>
				<stop  offset="0.1648" style="stop-color:#635100"/>
				<stop  offset="0.4121" style="stop-color:#695200"/>
				<stop  offset="0.4279" style="stop-color:#715B0B"/>
				<stop  offset="0.4557" style="stop-color:#857229"/>
				<stop  offset="0.489" style="stop-color:#A39554"/>
				<stop  offset="0.6703" style="stop-color:#A19253"/>
				<stop  offset="0.7071" style="stop-color:#A09151"/>
				<stop  offset="0.7214" style="stop-color:#9D8D4A"/>
				<stop  offset="0.7318" style="stop-color:#97863E"/>
				<stop  offset="0.7402" style="stop-color:#8E7C2D"/>
				<stop  offset="0.7475" style="stop-color:#846F17"/>
				<stop  offset="0.7527" style="stop-color:#796201"/>
				<stop  offset="0.8665" style="stop-color:#836D00"/>
				<stop  offset="0.9121" style="stop-color:#887300"/>
				<stop  offset="1" style="stop-color:#A4913C"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000105403760271284256690000011069086663761020852_);fill:url(#SVGID_00000055687930863408658620000014389876148708863667_);" width="38.5" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000126298641217644551640000016452122005058383776_" d="M26.9,26.1c-1.8,1.4-2.8,3.6-5.1,1.5
					c3.1,9.7,11.5-0.1,15.6,12.1c-3.2-0.5-9-0.3-9.3,4.3c-0.4,5.4,4.6,6.4,2.3,8.5c8.7-0.5,1.4-7.5,7.7-8c0.2,0,0.4,0,0.5,0
					c4.6,0,4.4,4.4,0.1,4.9c2,0.8,9.1,1,10.8-3.6c1.4-3.7-2.4-7.5,2.6-9.4c3.5-1.3,5.4,1.9,5.9,3.1c2.2-4-2.3-9.4-6.8-8.7
					c-4.9,0.8-4,5.6-6.9,6.5c-0.1,0-0.3,0.1-0.4,0.1c-5.9,1.2-5.2-7.8-10.4-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26.1"
					/>
			</defs>
			<clipPath id="SVGID_00000183222269129233888130000008683172141533496501_">
				<use xlink:href="#SVGID_00000126298641217644551640000016452122005058383776_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000145744904244995104330000016107939539654237114_" cx="-317.8812" cy="550.7262" r="0.4213" gradientTransform="matrix(63.1932 0 0 -63.1932 20134.4336 34844.2969)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#AB9B38"/>
				<stop  offset="6.888436e-02" style="stop-color:#907F23"/>
				<stop  offset="0.1648" style="stop-color:#645201"/>
				<stop  offset="0.4121" style="stop-color:#6B5300"/>
				<stop  offset="0.4267" style="stop-color:#725C0B"/>
				<stop  offset="0.4524" style="stop-color:#877329"/>
				<stop  offset="0.486" style="stop-color:#A79958"/>
				<stop  offset="0.489" style="stop-color:#AA9D5D"/>
				<stop  offset="0.6703" style="stop-color:#A79A5C"/>
				<stop  offset="0.7065" style="stop-color:#A6995A"/>
				<stop  offset="0.7205" style="stop-color:#A39553"/>
				<stop  offset="0.7307" style="stop-color:#9D8D47"/>
				<stop  offset="0.739" style="stop-color:#958336"/>
				<stop  offset="0.7461" style="stop-color:#8A7520"/>
				<stop  offset="0.7524" style="stop-color:#7D6505"/>
				<stop  offset="0.7527" style="stop-color:#7C6403"/>
				<stop  offset="0.857" style="stop-color:#856F01"/>
				<stop  offset="0.9121" style="stop-color:#8C7700"/>
				<stop  offset="1" style="stop-color:#AA9842"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000183222269129233888130000008683172141533496501_);fill:url(#SVGID_00000145744904244995104330000016107939539654237114_);" width="38.5" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000016795021120651926930000004400786855107228580_" d="M26.9,26.1c-1.8,1.4-2.8,3.6-5.1,1.5
					c3.1,9.7,11.5-0.1,15.6,12.1c-3.2-0.6-9-0.3-9.3,4.3c-0.4,5.4,4.7,6.4,2.3,8.5c8.8-0.5,1.3-7.5,7.7-8c0.2,0,0.4,0,0.5,0
					c4.6,0,4.4,4.4,0.1,4.9c2,0.8,9.1,1,10.8-3.6c1.4-3.7-2.4-7.5,2.7-9.4c3.5-1.3,5.4,2,5.9,3.1c2.2-4-2.3-9.4-6.8-8.7
					c-5,0.8-3.9,5.6-6.9,6.5c-0.1,0-0.3,0.1-0.4,0.1c-5.9,1.2-5.2-7.8-10.4-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.7,25.5,26.9,26.1"
					/>
			</defs>
			<clipPath id="SVGID_00000020366434195587597420000004728803122332892843_">
				<use xlink:href="#SVGID_00000016795021120651926930000004400786855107228580_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000029016218725383030730000013618096353770924421_" cx="-317.8795" cy="550.7274" r="0.4213" gradientTransform="matrix(63.1854 0 0 -63.1854 20131.8379 34840.0742)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#B2A23D"/>
				<stop  offset="6.413458e-02" style="stop-color:#978629"/>
				<stop  offset="0.1648" style="stop-color:#655303"/>
				<stop  offset="0.4121" style="stop-color:#6D5400"/>
				<stop  offset="0.4258" style="stop-color:#745D0B"/>
				<stop  offset="0.4499" style="stop-color:#887429"/>
				<stop  offset="0.4814" style="stop-color:#A89A58"/>
				<stop  offset="0.489" style="stop-color:#B1A465"/>
				<stop  offset="0.6703" style="stop-color:#AEA165"/>
				<stop  offset="0.7058" style="stop-color:#ADA063"/>
				<stop  offset="0.7196" style="stop-color:#AA9C5C"/>
				<stop  offset="0.7296" style="stop-color:#A49450"/>
				<stop  offset="0.7378" style="stop-color:#9B8A3F"/>
				<stop  offset="0.7448" style="stop-color:#917D28"/>
				<stop  offset="0.751" style="stop-color:#836C0D"/>
				<stop  offset="0.7527" style="stop-color:#7F6704"/>
				<stop  offset="0.8529" style="stop-color:#897202"/>
				<stop  offset="0.9121" style="stop-color:#917B00"/>
				<stop  offset="1" style="stop-color:#B29F48"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000020366434195587597420000004728803122332892843_);fill:url(#SVGID_00000029016218725383030730000013618096353770924421_);" width="38.4" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000047754323746854574740000009589194656070020504_" d="M26.9,26.1c-1.8,1.4-2.8,3.6-5.1,1.5
					c3.1,9.7,11.6-0.1,15.7,12.1c-3.2-0.6-9-0.4-9.4,4.2c-0.4,5.4,4.7,6.4,2.3,8.5c8.8-0.6,1.2-7.6,7.8-8c0.2,0,0.4,0,0.5,0
					c4.6,0,4.4,4.5,0.1,4.9c2,0.8,9.1,0.9,10.8-3.6c1.4-3.7-2.4-7.6,2.7-9.4c3.5-1.3,5.4,2,5.9,3.1c2.2-4-2.3-9.4-6.8-8.7
					c-5,0.8-3.9,5.6-6.9,6.4c-0.1,0-0.3,0.1-0.4,0.1c-6,1.2-5.2-7.8-10.4-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.6,25.5,26.9,26.1"/>
			</defs>
			<clipPath id="SVGID_00000001628156655873607620000007056821544564762275_">
				<use xlink:href="#SVGID_00000047754323746854574740000009589194656070020504_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000106851035306074898410000010108277595099449791_" cx="-317.874" cy="550.7333" r="0.4212" gradientTransform="matrix(63.1777 0 0 -63.1777 20129.0605 34836.2109)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#B9AA43"/>
				<stop  offset="5.938442e-02" style="stop-color:#9E8E2F"/>
				<stop  offset="0.1648" style="stop-color:#675404"/>
				<stop  offset="0.4121" style="stop-color:#6F5600"/>
				<stop  offset="0.4249" style="stop-color:#765F0B"/>
				<stop  offset="0.4474" style="stop-color:#8A7629"/>
				<stop  offset="0.4769" style="stop-color:#AA9B58"/>
				<stop  offset="0.489" style="stop-color:#B8AC6E"/>
				<stop  offset="0.6703" style="stop-color:#B5A86D"/>
				<stop  offset="0.7053" style="stop-color:#B4A76B"/>
				<stop  offset="0.7189" style="stop-color:#B0A364"/>
				<stop  offset="0.7288" style="stop-color:#AA9B58"/>
				<stop  offset="0.7369" style="stop-color:#A29147"/>
				<stop  offset="0.7439" style="stop-color:#968330"/>
				<stop  offset="0.75" style="stop-color:#897215"/>
				<stop  offset="0.7527" style="stop-color:#816906"/>
				<stop  offset="0.8491" style="stop-color:#8C7403"/>
				<stop  offset="0.9121" style="stop-color:#957E00"/>
				<stop  offset="1" style="stop-color:#B9A64F"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000001628156655873607620000007056821544564762275_);fill:url(#SVGID_00000106851035306074898410000010108277595099449791_);" width="38.4" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000165228752616082434670000015572656108544373183_" d="M26.9,26.1c-1.8,1.4-2.8,3.6-5.1,1.5
					c3.1,9.7,11.6-0.2,15.7,12.2c-3.2-0.7-9.1-0.4-9.4,4.2c-0.4,5.4,4.7,6.4,2.3,8.5c8.8-0.6,1.2-7.6,7.8-8c0.2,0,0.4,0,0.5,0
					c4.6,0,4.4,4.5,0.1,4.9c2,0.8,9.1,0.9,10.7-3.6c1.4-3.7-2.5-7.6,2.7-9.4c3.6-1.3,5.4,2,5.9,3.2c2.2-4-2.4-9.3-6.8-8.6
					c-5,0.8-3.9,5.6-6.9,6.4c-0.1,0-0.3,0.1-0.4,0.1c-6,1.2-5.1-7.8-10.4-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.6,25.5,26.9,26.1"/>
			</defs>
			<clipPath id="SVGID_00000094587667612799085950000006790983554840490686_">
				<use xlink:href="#SVGID_00000165228752616082434670000015572656108544373183_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000042704780337482472970000007671143464548715931_" cx="-317.8763" cy="550.7315" r="0.4217" gradientTransform="matrix(63.1701 0 0 -63.1701 20126.793 34831.9062)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#C0B047"/>
				<stop  offset="5.642876e-02" style="stop-color:#A59433"/>
				<stop  offset="0.1648" style="stop-color:#685505"/>
				<stop  offset="0.4121" style="stop-color:#715700"/>
				<stop  offset="0.4241" style="stop-color:#78600B"/>
				<stop  offset="0.4453" style="stop-color:#8C7629"/>
				<stop  offset="0.473" style="stop-color:#AB9B58"/>
				<stop  offset="0.489" style="stop-color:#BFB377"/>
				<stop  offset="0.6703" style="stop-color:#BBAF76"/>
				<stop  offset="0.7048" style="stop-color:#BAAE74"/>
				<stop  offset="0.7182" style="stop-color:#B7AA6D"/>
				<stop  offset="0.7279" style="stop-color:#B1A261"/>
				<stop  offset="0.7358" style="stop-color:#A89850"/>
				<stop  offset="0.7427" style="stop-color:#9D8A39"/>
				<stop  offset="0.7487" style="stop-color:#8F791E"/>
				<stop  offset="0.7527" style="stop-color:#846B07"/>
				<stop  offset="0.8457" style="stop-color:#8F7603"/>
				<stop  offset="0.9121" style="stop-color:#998100"/>
				<stop  offset="1" style="stop-color:#BFAD55"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000094587667612799085950000006790983554840490686_);fill:url(#SVGID_00000042704780337482472970000007671143464548715931_);" width="38.4" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000074426044834301509090000005426959359098950804_" d="M26.9,26.1c-1.8,1.4-2.7,3.6-5.1,1.4
					c3.1,9.7,11.6-0.2,15.7,12.2c-3.3-0.7-9.1-0.5-9.4,4.2c-0.4,5.4,4.7,6.4,2.3,8.5c8.8-0.6,1.1-7.7,7.8-8c0.2,0,0.4,0,0.5,0
					c4.6,0.1,4.4,4.5,0.1,4.9c2,0.8,9,0.9,10.7-3.6c1.4-3.7-2.5-7.6,2.7-9.4c3.6-1.3,5.4,2,5.9,3.2c2.1-4-2.4-9.3-6.8-8.6
					c-5,0.8-3.9,5.6-6.9,6.4c-0.1,0-0.3,0.1-0.4,0.1c-6.1,1.2-5.1-7.8-10.3-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.6,25.5,26.9,26.1"
					/>
			</defs>
			<clipPath id="SVGID_00000010299533953585521780000015143902180263293111_">
				<use xlink:href="#SVGID_00000074426044834301509090000005426959359098950804_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000024708259984805672850000016411029868301818756_" cx="-317.8814" cy="550.7249" r="0.4219" gradientTransform="matrix(63.1628 0 0 -63.1628 20124.7754 34827.4727)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#C7B84D"/>
				<stop  offset="5.229685e-02" style="stop-color:#AC9C39"/>
				<stop  offset="0.1648" style="stop-color:#695506"/>
				<stop  offset="0.4121" style="stop-color:#725700"/>
				<stop  offset="0.4235" style="stop-color:#79600B"/>
				<stop  offset="0.4435" style="stop-color:#8D7729"/>
				<stop  offset="0.4697" style="stop-color:#AC9C58"/>
				<stop  offset="0.489" style="stop-color:#C6BB80"/>
				<stop  offset="0.6703" style="stop-color:#C3B77F"/>
				<stop  offset="0.7043" style="stop-color:#C2B67D"/>
				<stop  offset="0.7175" style="stop-color:#BEB176"/>
				<stop  offset="0.7271" style="stop-color:#B8AA6A"/>
				<stop  offset="0.735" style="stop-color:#B09F59"/>
				<stop  offset="0.7417" style="stop-color:#A49142"/>
				<stop  offset="0.7477" style="stop-color:#968027"/>
				<stop  offset="0.7527" style="stop-color:#876D09"/>
				<stop  offset="0.8425" style="stop-color:#927805"/>
				<stop  offset="0.9121" style="stop-color:#9E8400"/>
				<stop  offset="1" style="stop-color:#C6B45C"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000010299533953585521780000015143902180263293111_);fill:url(#SVGID_00000024708259984805672850000016411029868301818756_);" width="38.4" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000106853184977648254200000002414565960035965057_" d="M26.9,26.1c-1.8,1.4-2.7,3.6-5.1,1.4
					c3.1,9.7,11.7-0.3,15.7,12.2c-3.3-0.7-9.1-0.5-9.4,4.1c-0.4,5.4,4.8,6.4,2.3,8.5c8.8-0.7,1-7.7,7.8-8.1c0.2,0,0.4,0,0.5,0
					c4.7,0.1,4.4,4.5,0.1,5c2,0.8,9,0.9,10.7-3.6c1.4-3.7-2.5-7.6,2.7-9.5c3.6-1.3,5.4,2,5.9,3.2c2.1-4-2.4-9.3-6.8-8.6
					c-5,0.8-3.9,5.6-7,6.4c-0.1,0-0.3,0.1-0.4,0.1c-6.1,1.1-5.1-7.8-10.3-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.6,25.5,26.9,26.1"/>
			</defs>
			<clipPath id="SVGID_00000152243674072765656460000003779620733098461594_">
				<use xlink:href="#SVGID_00000106853184977648254200000002414565960035965057_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000089536692093943044660000005296758437814028209_" cx="-317.8734" cy="550.7319" r="0.4214" gradientTransform="matrix(63.1551 0 0 -63.1551 20121.8086 34823.6719)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#CEC053"/>
				<stop  offset="4.916528e-02" style="stop-color:#B4A43F"/>
				<stop  offset="0.1548" style="stop-color:#715D0D"/>
				<stop  offset="0.1648" style="stop-color:#6A5608"/>
				<stop  offset="0.4121" style="stop-color:#745801"/>
				<stop  offset="0.4229" style="stop-color:#7B610C"/>
				<stop  offset="0.442" style="stop-color:#8F782A"/>
				<stop  offset="0.467" style="stop-color:#AE9D59"/>
				<stop  offset="0.489" style="stop-color:#CDC389"/>
				<stop  offset="0.6703" style="stop-color:#C9BE88"/>
				<stop  offset="0.7038" style="stop-color:#C8BD86"/>
				<stop  offset="0.7169" style="stop-color:#C5B87F"/>
				<stop  offset="0.7263" style="stop-color:#BFB173"/>
				<stop  offset="0.7341" style="stop-color:#B6A662"/>
				<stop  offset="0.7407" style="stop-color:#AB984B"/>
				<stop  offset="0.7467" style="stop-color:#9D8730"/>
				<stop  offset="0.752" style="stop-color:#8D730F"/>
				<stop  offset="0.7527" style="stop-color:#8A700A"/>
				<stop  offset="0.8396" style="stop-color:#957B05"/>
				<stop  offset="0.9121" style="stop-color:#A28800"/>
				<stop  offset="1" style="stop-color:#CDBB63"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000152243674072765656460000003779620733098461594_);fill:url(#SVGID_00000089536692093943044660000005296758437814028209_);" width="38.4" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000077307514889523291620000003244490359896742576_" d="M26.9,26.1c-1.8,1.4-2.7,3.7-5.1,1.4
					c3.1,9.7,11.7-0.3,15.8,12.3c-3.3-0.8-9.1-0.5-9.5,4.1c-0.4,5.4,4.8,6.4,2.3,8.5c8.8-0.7,1-7.8,7.8-8.1c0.2,0,0.4,0,0.5,0
					c4.7,0.1,4.4,4.6,0,5c2,0.8,9,0.9,10.7-3.6c1.4-3.7-2.5-7.6,2.7-9.5c3.6-1.3,5.4,2.1,5.9,3.2c2.1-4-2.4-9.3-6.8-8.6
					c-5,0.8-3.9,5.6-7,6.4c-0.1,0-0.3,0.1-0.4,0.1c-6.2,1.1-5.1-7.8-10.3-11c-1.5-1-2.8-1.3-3.8-1.3C28.6,25,27.6,25.5,26.9,26.1"/>
			</defs>
			<clipPath id="SVGID_00000155122596704753400800000014127603806959624637_">
				<use xlink:href="#SVGID_00000077307514889523291620000003244490359896742576_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000106830002662462455340000006977417367498362294_" cx="-317.8809" cy="550.7198" r="0.4214" gradientTransform="matrix(63.1476 0 0 -63.1476 20119.8945 34818.8047)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#D6C859"/>
				<stop  offset="4.640178e-02" style="stop-color:#BCAC45"/>
				<stop  offset="0.1461" style="stop-color:#786513"/>
				<stop  offset="0.1648" style="stop-color:#6B5709"/>
				<stop  offset="0.4121" style="stop-color:#765902"/>
				<stop  offset="0.4224" style="stop-color:#7D620D"/>
				<stop  offset="0.4406" style="stop-color:#91792B"/>
				<stop  offset="0.4645" style="stop-color:#B09F5B"/>
				<stop  offset="0.489" style="stop-color:#D5CB93"/>
				<stop  offset="0.6703" style="stop-color:#D0C691"/>
				<stop  offset="0.7035" style="stop-color:#CFC58F"/>
				<stop  offset="0.7163" style="stop-color:#CCC088"/>
				<stop  offset="0.7257" style="stop-color:#C5B97C"/>
				<stop  offset="0.7333" style="stop-color:#BDAE6B"/>
				<stop  offset="0.7399" style="stop-color:#B19F54"/>
				<stop  offset="0.7458" style="stop-color:#A38D39"/>
				<stop  offset="0.751" style="stop-color:#937918"/>
				<stop  offset="0.7527" style="stop-color:#8D710C"/>
				<stop  offset="0.8343" style="stop-color:#987C07"/>
				<stop  offset="0.9121" style="stop-color:#A68B00"/>
				<stop  offset="1" style="stop-color:#D5C36A"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000155122596704753400800000014127603806959624637_);fill:url(#SVGID_00000106830002662462455340000006977417367498362294_);" width="38.4" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000135679566661130497200000011182717044270796439_" d="M26.8,26.1c-1.8,1.4-2.7,3.7-5.1,1.4
					c3.1,9.7,11.7-0.3,15.8,12.3c-3.3-0.8-9.2-0.6-9.5,4c-0.4,5.4,4.8,6.4,2.3,8.5c8.8-0.7,0.9-7.8,7.8-8.1c0.2,0,0.4,0,0.5,0
					c4.7,0.1,4.4,4.6,0,5c2,0.8,9,0.9,10.7-3.6c1.4-3.7-2.5-7.6,2.7-9.5c3.7-1.3,5.4,2.1,5.9,3.2c2.1-4-2.5-9.3-6.8-8.6
					c-5,0.8-3.9,5.6-7,6.4c-0.1,0-0.3,0.1-0.4,0.1c-6.2,1.1-5.1-7.8-10.3-11c-1.5-1-2.8-1.3-3.8-1.3C28.5,25,27.6,25.5,26.8,26.1"/>
			</defs>
			<clipPath id="SVGID_00000136407600766508862060000017459966725235355561_">
				<use xlink:href="#SVGID_00000135679566661130497200000011182717044270796439_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000160149322940576411530000007433321372952865931_" cx="-317.8686" cy="550.7324" r="0.4213" gradientTransform="matrix(63.1401 0 0 -63.1401 20116.752 34815.4375)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#DECF5F"/>
				<stop  offset="4.427637e-02" style="stop-color:#C3B34B"/>
				<stop  offset="0.1394" style="stop-color:#7F6C19"/>
				<stop  offset="0.1648" style="stop-color:#6C580B"/>
				<stop  offset="0.3965" style="stop-color:#775A04"/>
				<stop  offset="0.4121" style="stop-color:#785A03"/>
				<stop  offset="0.4219" style="stop-color:#7F630E"/>
				<stop  offset="0.4393" style="stop-color:#927A2C"/>
				<stop  offset="0.4621" style="stop-color:#B2A05C"/>
				<stop  offset="0.489" style="stop-color:#DCD39D"/>
				<stop  offset="0.6703" style="stop-color:#D7CE9B"/>
				<stop  offset="0.703" style="stop-color:#D6CD99"/>
				<stop  offset="0.7157" style="stop-color:#D3C892"/>
				<stop  offset="0.7249" style="stop-color:#CCC186"/>
				<stop  offset="0.7324" style="stop-color:#C4B675"/>
				<stop  offset="0.7389" style="stop-color:#B9A85E"/>
				<stop  offset="0.7447" style="stop-color:#AB9643"/>
				<stop  offset="0.7499" style="stop-color:#9B8222"/>
				<stop  offset="0.7527" style="stop-color:#90740D"/>
				<stop  offset="0.832" style="stop-color:#9B7F08"/>
				<stop  offset="0.9121" style="stop-color:#AA8F00"/>
				<stop  offset="1" style="stop-color:#DCCA71"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000136407600766508862060000017459966725235355561_);fill:url(#SVGID_00000160149322940576411530000007433321372952865931_);" width="38.3" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000013893729340552544080000007003713453336565180_" d="M26.8,26.1c-1.8,1.4-2.7,3.7-5.1,1.4
					c3.1,9.7,11.7-0.4,15.8,12.4c-3.3-0.8-9.2-0.6-9.5,4c-0.4,5.4,4.8,6.4,2.3,8.5c8.8-0.8,0.8-7.8,7.8-8.1c0.2,0,0.4,0,0.5,0
					c4.7,0.1,4.4,4.6,0,5c2,0.8,9,0.9,10.7-3.6c1.4-3.7-2.5-7.6,2.7-9.5c3.7-1.3,5.4,2.1,6,3.3c2.1-4-2.5-9.3-6.8-8.6
					c-5,0.8-3.9,5.6-7,6.4c-0.1,0-0.3,0.1-0.4,0.1c-6.3,1.1-5.1-7.8-10.3-11c-1.5-1-2.8-1.3-3.8-1.3C28.5,25,27.6,25.5,26.8,26.1"/>
			</defs>
			<clipPath id="SVGID_00000084501524341859221160000008130063238458755258_">
				<use xlink:href="#SVGID_00000013893729340552544080000007003713453336565180_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000172428147486323210790000017551033185395047837_" cx="-317.8744" cy="550.7254" r="0.4214" gradientTransform="matrix(63.1328 0 0 -63.1328 20114.7891 34811.0039)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#E6D765"/>
				<stop  offset="4.174364e-02" style="stop-color:#CBBB51"/>
				<stop  offset="0.1314" style="stop-color:#887420"/>
				<stop  offset="0.1648" style="stop-color:#6D580C"/>
				<stop  offset="0.3824" style="stop-color:#785B05"/>
				<stop  offset="0.4121" style="stop-color:#7A5B04"/>
				<stop  offset="0.4215" style="stop-color:#81640F"/>
				<stop  offset="0.4381" style="stop-color:#947B2D"/>
				<stop  offset="0.4599" style="stop-color:#B4A15D"/>
				<stop  offset="0.4859" style="stop-color:#DFD59F"/>
				<stop  offset="0.489" style="stop-color:#E4DCA7"/>
				<stop  offset="0.6703" style="stop-color:#DFD5A5"/>
				<stop  offset="0.7026" style="stop-color:#DED4A3"/>
				<stop  offset="0.7151" style="stop-color:#DACF9C"/>
				<stop  offset="0.7243" style="stop-color:#D4C890"/>
				<stop  offset="0.7317" style="stop-color:#CBBD7F"/>
				<stop  offset="0.7381" style="stop-color:#C0AE68"/>
				<stop  offset="0.7439" style="stop-color:#B29C4D"/>
				<stop  offset="0.749" style="stop-color:#A1882C"/>
				<stop  offset="0.7527" style="stop-color:#92750F"/>
				<stop  offset="0.8278" style="stop-color:#9D8009"/>
				<stop  offset="0.9121" style="stop-color:#AE9200"/>
				<stop  offset="1" style="stop-color:#E4D278"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000084501524341859221160000008130063238458755258_);fill:url(#SVGID_00000172428147486323210790000017551033185395047837_);" width="38.3" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000066478456599862179890000013093216925416978078_" d="M26.8,26.1c-1.8,1.4-2.7,3.7-5.1,1.4
					c3.1,9.7,11.8-0.4,15.8,12.4c-3.3-0.9-9.2-0.6-9.5,4c-0.4,5.4,4.9,6.4,2.3,8.5c8.8-0.8,0.8-7.9,7.8-8.1c0.2,0,0.4,0,0.5,0
					c4.7,0.1,4.4,4.6,0,5c2,0.8,8.9,0.9,10.7-3.6c1.4-3.7-2.5-7.7,2.7-9.5c3.7-1.3,5.4,2.1,6,3.3c2.1-4-2.5-9.2-6.8-8.6
					c-5,0.8-3.9,5.6-7,6.4c-0.1,0-0.3,0.1-0.4,0.1c-6.3,1.1-5.1-7.8-10.3-11c-1.5-1-2.8-1.3-3.8-1.3C28.5,25,27.6,25.5,26.8,26.1"/>
			</defs>
			<clipPath id="SVGID_00000119810151166762279780000008066910086805007524_">
				<use xlink:href="#SVGID_00000066478456599862179890000013093216925416978078_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000071556999604345423350000008804228066190677911_" cx="-317.8774" cy="550.7226" r="0.4216" gradientTransform="matrix(63.1255 0 0 -63.1255 20112.6523 34806.7539)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#EDE06C"/>
				<stop  offset="3.951054e-02" style="stop-color:#D3C459"/>
				<stop  offset="0.1243" style="stop-color:#907D27"/>
				<stop  offset="0.1648" style="stop-color:#6E590E"/>
				<stop  offset="0.3824" style="stop-color:#795C06"/>
				<stop  offset="0.4121" style="stop-color:#7B5C05"/>
				<stop  offset="0.4211" style="stop-color:#826510"/>
				<stop  offset="0.437" style="stop-color:#967C2E"/>
				<stop  offset="0.458" style="stop-color:#B5A25E"/>
				<stop  offset="0.4829" style="stop-color:#E1D6A0"/>
				<stop  offset="0.489" style="stop-color:#ECE4B1"/>
				<stop  offset="0.6703" style="stop-color:#E6DEAF"/>
				<stop  offset="0.7022" style="stop-color:#E5DDAD"/>
				<stop  offset="0.7146" style="stop-color:#E1D8A6"/>
				<stop  offset="0.7236" style="stop-color:#DBD19A"/>
				<stop  offset="0.7309" style="stop-color:#D3C589"/>
				<stop  offset="0.7373" style="stop-color:#C7B772"/>
				<stop  offset="0.7429" style="stop-color:#B9A557"/>
				<stop  offset="0.748" style="stop-color:#A99136"/>
				<stop  offset="0.7526" style="stop-color:#967912"/>
				<stop  offset="0.7527" style="stop-color:#957810"/>
				<stop  offset="0.8258" style="stop-color:#A0830A"/>
				<stop  offset="0.9121" style="stop-color:#B29600"/>
				<stop  offset="1" style="stop-color:#EBD980"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000119810151166762279780000008066910086805007524_);fill:url(#SVGID_00000071556999604345423350000008804228066190677911_);" width="38.3" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000058547548956567611970000003967433959458581692_" d="M26.8,26.1c-1.8,1.4-2.7,3.7-5,1.4
					c3.1,9.7,11.8-0.5,15.9,12.4c-3.3-0.9-9.2-0.7-9.6,3.9c-0.4,5.4,4.9,6.4,2.3,8.5c8.9-0.9,0.7-7.9,7.8-8.2c0.2,0,0.4,0,0.5,0
					c4.7,0.1,4.4,4.6,0,5c2,0.8,8.9,0.9,10.7-3.6c1.4-3.7-2.5-7.7,2.7-9.6c3.7-1.3,5.4,2.1,6,3.3c2.1-4-2.6-9.2-6.8-8.5
					c-5,0.8-3.9,5.6-7,6.4c-0.1,0-0.3,0.1-0.4,0.1c-6.4,1.1-5.1-7.8-10.2-11c-1.5-1-2.8-1.3-3.8-1.3C28.5,25,27.6,25.5,26.8,26.1"/>
			</defs>
			<clipPath id="SVGID_00000139280398300559047930000002465079971179105958_">
				<use xlink:href="#SVGID_00000058547548956567611970000003967433959458581692_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000178186810832767551440000014152314348080026546_" cx="-317.868" cy="550.7305" r="0.4214" gradientTransform="matrix(63.1182 0 0 -63.1182 20109.7266 34803.2578)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#F5E872"/>
				<stop  offset="3.773879e-02" style="stop-color:#DBCC5F"/>
				<stop  offset="0.1187" style="stop-color:#98852D"/>
				<stop  offset="0.1648" style="stop-color:#6F5A0F"/>
				<stop  offset="0.3702" style="stop-color:#7A5C08"/>
				<stop  offset="0.4121" style="stop-color:#7D5D06"/>
				<stop  offset="0.4207" style="stop-color:#846611"/>
				<stop  offset="0.4359" style="stop-color:#977D2F"/>
				<stop  offset="0.4558" style="stop-color:#B7A35F"/>
				<stop  offset="0.4795" style="stop-color:#E2D7A1"/>
				<stop  offset="0.489" style="stop-color:#F4EDBD"/>
				<stop  offset="0.6703" style="stop-color:#EDE6BA"/>
				<stop  offset="0.7018" style="stop-color:#ECE5B8"/>
				<stop  offset="0.714" style="stop-color:#E8E0B1"/>
				<stop  offset="0.7229" style="stop-color:#E2D8A5"/>
				<stop  offset="0.7302" style="stop-color:#DACD94"/>
				<stop  offset="0.7364" style="stop-color:#CEBF7D"/>
				<stop  offset="0.742" style="stop-color:#C0AD62"/>
				<stop  offset="0.7471" style="stop-color:#AF9841"/>
				<stop  offset="0.7516" style="stop-color:#9C801B"/>
				<stop  offset="0.7527" style="stop-color:#977911"/>
				<stop  offset="0.8206" style="stop-color:#A1840B"/>
				<stop  offset="0.9121" style="stop-color:#B69A00"/>
				<stop  offset="1" style="stop-color:#F3E287"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000139280398300559047930000002465079971179105958_);fill:url(#SVGID_00000178186810832767551440000014152314348080026546_);" width="38.3" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000169542053806744880910000015227357621608498308_" d="M26.8,26.1c-1.8,1.4-2.7,3.7-5,1.4
					C24.9,37.2,33.6,27,37.7,40c-3.3-0.9-9.3-0.7-9.6,3.9c-0.4,5.4,4.9,6.5,2.3,8.5c8.9-0.9,0.6-8,7.8-8.2c0.2,0,0.4,0,0.5,0
					c4.7,0.2,4.4,4.7,0,5.1c2,0.8,8.9,0.9,10.7-3.6c1.4-3.7-2.5-7.7,2.7-9.6c3.8-1.4,5.4,2.2,6,3.3c2-4-2.6-9.2-6.8-8.5
					c-5,0.8-3.9,5.6-7.1,6.4c-0.2,0-0.3,0.1-0.4,0.1c-6.5,1.1-5-7.8-10.2-11c-1.5-1-2.8-1.3-3.8-1.3C28.5,25,27.6,25.5,26.8,26.1"/>
			</defs>
			<clipPath id="SVGID_00000123412016666476358690000001070869823931399304_">
				<use xlink:href="#SVGID_00000169542053806744880910000015227357621608498308_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000063603849081785324860000004958515062346187666_" cx="-317.8805" cy="550.7181" r="0.421" gradientTransform="matrix(63.1111 0 0 -63.1111 20108.252 34798.5664)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FDF079"/>
				<stop  offset="3.613938e-02" style="stop-color:#E3D465"/>
				<stop  offset="0.1137" style="stop-color:#9F8D33"/>
				<stop  offset="0.1648" style="stop-color:#705B10"/>
				<stop  offset="0.3594" style="stop-color:#7B5D09"/>
				<stop  offset="0.4121" style="stop-color:#7F5E07"/>
				<stop  offset="0.4204" style="stop-color:#866712"/>
				<stop  offset="0.435" style="stop-color:#997E30"/>
				<stop  offset="0.4542" style="stop-color:#B8A460"/>
				<stop  offset="0.4771" style="stop-color:#E3D8A2"/>
				<stop  offset="0.489" style="stop-color:#FBF5C7"/>
				<stop  offset="0.6703" style="stop-color:#F4EEC4"/>
				<stop  offset="0.7014" style="stop-color:#F3EDC2"/>
				<stop  offset="0.7135" style="stop-color:#EFE8BB"/>
				<stop  offset="0.7223" style="stop-color:#E9E0AF"/>
				<stop  offset="0.7295" style="stop-color:#E1D59E"/>
				<stop  offset="0.7357" style="stop-color:#D5C787"/>
				<stop  offset="0.7412" style="stop-color:#C7B56C"/>
				<stop  offset="0.7462" style="stop-color:#B7A04B"/>
				<stop  offset="0.7507" style="stop-color:#A48825"/>
				<stop  offset="0.7527" style="stop-color:#9A7B12"/>
				<stop  offset="0.819" style="stop-color:#A4860C"/>
				<stop  offset="0.9121" style="stop-color:#BA9D00"/>
				<stop  offset="1" style="stop-color:#FBE98E"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000123412016666476358690000001070869823931399304_);fill:url(#SVGID_00000063603849081785324860000004958515062346187666_);" width="38.3" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000084525406864245762970000002241171293724403849_" d="M26.8,26.1c-1.8,1.4-2.7,3.7-5,1.4
					C24.9,37.2,33.7,27,37.7,40c-3.3-1-9.3-0.7-9.6,3.9c-0.4,5.4,4.9,6.5,2.3,8.5c8.9-0.9,0.6-8,7.8-8.2c5.3-0.2,5,4.7,0.6,5.1
					c2,0.8,8.9,0.8,10.7-3.6c1.5-3.7-2.5-7.7,2.7-9.6c3.8-1.4,5.4,2.2,6,3.4c2-4-2.6-9.2-6.8-8.5c-5.1,0.8-3.9,5.6-7.1,6.3
					c-7,1.6-5.4-7.7-10.6-11c-1.5-1-2.8-1.3-3.8-1.3C28.5,25,27.6,25.5,26.8,26.1"/>
			</defs>
			<clipPath id="SVGID_00000148649909045937742010000012758860241660575104_">
				<use xlink:href="#SVGID_00000084525406864245762970000002241171293724403849_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000177467160334202142140000008039939677443436939_" cx="-317.869" cy="550.7267" r="0.4216" gradientTransform="matrix(63.1037 0 0 -63.1037 20105.1523 34795.0625)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF57F"/>
				<stop  offset="3.508200e-02" style="stop-color:#E5D96B"/>
				<stop  offset="0.1103" style="stop-color:#A49239"/>
				<stop  offset="0.1648" style="stop-color:#715B12"/>
				<stop  offset="0.3594" style="stop-color:#7C5E0B"/>
				<stop  offset="0.4121" style="stop-color:#805F08"/>
				<stop  offset="0.4201" style="stop-color:#876813"/>
				<stop  offset="0.4343" style="stop-color:#9A7F31"/>
				<stop  offset="0.4529" style="stop-color:#B8A461"/>
				<stop  offset="0.475" style="stop-color:#E2D8A3"/>
				<stop  offset="0.489" style="stop-color:#FFFBD0"/>
				<stop  offset="0.6703" style="stop-color:#FAF5CE"/>
				<stop  offset="0.7011" style="stop-color:#F9F4CC"/>
				<stop  offset="0.7131" style="stop-color:#F6EFC5"/>
				<stop  offset="0.7218" style="stop-color:#F0E8B9"/>
				<stop  offset="0.7289" style="stop-color:#E7DCA8"/>
				<stop  offset="0.735" style="stop-color:#DCCE91"/>
				<stop  offset="0.7405" style="stop-color:#CEBC76"/>
				<stop  offset="0.7454" style="stop-color:#BEA755"/>
				<stop  offset="0.7499" style="stop-color:#AB8F2F"/>
				<stop  offset="0.7527" style="stop-color:#9D7D13"/>
				<stop  offset="0.8175" style="stop-color:#A7880D"/>
				<stop  offset="0.9121" style="stop-color:#BEA000"/>
				<stop  offset="1" style="stop-color:#FFF095"/>
			</radialGradient>
			
				<rect x="21.8" y="25" style="clip-path:url(#SVGID_00000148649909045937742010000012758860241660575104_);fill:url(#SVGID_00000177467160334202142140000008039939677443436939_);" width="38.3" height="27.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000169549785001019396850000006512702530177093545_" d="M29.6,25.1c-1.1,0-2,0.5-2.7,1c-1.8,1.3-2.7,3.7-5,1.5
					c3,9.5,11.8-0.6,15.9,12.5c-3.3-0.9-9.3-0.7-9.6,3.8c0,0.1,0,0.2,0,0.3c-0.2,5.1,4.8,6.2,2.3,8.2c8.6-0.9,0.5-8,7.7-8.2
					c0.2,0,0.4,0,0.6,0c4.7,0.2,4.3,4.7,0.1,5.1c2,0.7,8.7,0.8,10.4-3.6c1.4-3.5-2.2-7.3,2.1-9.3c0.2-0.1,0.4-0.2,0.6-0.2
					c3.7-1.3,5.4,2.1,6,3.3c1.9-3.9-2.6-9-6.8-8.4c-5,0.8-3.9,5.6-7,6.3c-0.6,0.1-1.1,0.2-1.6,0.2c-5.2-0.1-4.3-8-9-11.1
					c-1.6-1-2.8-1.4-3.9-1.4C29.7,25.1,29.6,25.1,29.6,25.1"/>
			</defs>
			<clipPath id="SVGID_00000179614738330876402290000000867309388984521142_">
				<use xlink:href="#SVGID_00000169549785001019396850000006512702530177093545_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000073723650328024905710000000139195336210122643_" cx="-317.9392" cy="550.8206" r="0.4214" gradientTransform="matrix(64.4433 0 0 -64.4433 20535.5527 35539.1797)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF57F"/>
				<stop  offset="3.515533e-02" style="stop-color:#E5D96A"/>
				<stop  offset="0.1107" style="stop-color:#A49234"/>
				<stop  offset="0.1319" style="stop-color:#917D24"/>
				<stop  offset="0.1648" style="stop-color:#766013"/>
				<stop  offset="0.3022" style="stop-color:#7C600E"/>
				<stop  offset="0.3892" style="stop-color:#7E610C"/>
				<stop  offset="0.4121" style="stop-color:#826209"/>
				<stop  offset="0.4206" style="stop-color:#896B14"/>
				<stop  offset="0.4356" style="stop-color:#9D8232"/>
				<stop  offset="0.4554" style="stop-color:#BCA862"/>
				<stop  offset="0.478" style="stop-color:#E6DAA1"/>
				<stop  offset="0.4784" style="stop-color:#E8DCA4"/>
				<stop  offset="0.4821" style="stop-color:#F5ECBA"/>
				<stop  offset="0.4857" style="stop-color:#FCF6C7"/>
				<stop  offset="0.489" style="stop-color:#FFF9CB"/>
				<stop  offset="0.6703" style="stop-color:#FAF5CC"/>
				<stop  offset="0.6979" style="stop-color:#F9F4CA"/>
				<stop  offset="0.7101" style="stop-color:#F5EFC3"/>
				<stop  offset="0.7194" style="stop-color:#EFE7B6"/>
				<stop  offset="0.7271" style="stop-color:#E6DCA4"/>
				<stop  offset="0.7338" style="stop-color:#DBCD8D"/>
				<stop  offset="0.7399" style="stop-color:#CCBA70"/>
				<stop  offset="0.7455" style="stop-color:#BBA44D"/>
				<stop  offset="0.7506" style="stop-color:#A88C27"/>
				<stop  offset="0.7527" style="stop-color:#9F8014"/>
				<stop  offset="0.8297" style="stop-color:#AA8B0D"/>
				<stop  offset="0.8679" style="stop-color:#B19309"/>
				<stop  offset="0.9121" style="stop-color:#BFA200"/>
				<stop  offset="0.9835" style="stop-color:#F5E17B"/>
				<stop  offset="0.99" style="stop-color:#F7E480"/>
				<stop  offset="0.9979" style="stop-color:#FDED8F"/>
				<stop  offset="1" style="stop-color:#FFF094"/>
			</radialGradient>
			
				<rect x="21.9" y="25.1" style="clip-path:url(#SVGID_00000179614738330876402290000000867309388984521142_);fill:url(#SVGID_00000073723650328024905710000000139195336210122643_);" width="38" height="27.3"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000163063038512680203360000015861605876639299775_" d="M29.7,25.1c-1.1,0-2,0.5-2.7,1c-1.8,1.3-2.7,3.7-5,1.5
					c3,9.3,11.7-0.6,15.8,12.4c-3.3-0.9-9.3-0.7-9.6,3.8c0,0.1,0,0.2,0,0.3c-0.2,5.1,4.7,6.1,2.3,8.2c8.4-1,0.5-8,7.7-8.2
					c0.2,0,0.4,0,0.6,0c4.6,0.2,4.3,4.7,0.3,5.1c2,0.7,8.5,0.7,10.2-3.7c1.4-3.5-2.2-7.3,2.1-9.3c0.2-0.1,0.4-0.2,0.6-0.2
					c3.7-1.3,5.4,2,6,3.2c1.8-3.8-2.6-8.9-6.7-8.2c-4.9,0.8-3.8,5.6-7,6.3c-0.6,0.1-1.1,0.2-1.6,0.2c-5.2,0-4.3-7.9-9-11
					C32,25.5,30.8,25.1,29.7,25.1C29.7,25.1,29.7,25.1,29.7,25.1"/>
			</defs>
			<clipPath id="SVGID_00000172418829703181175710000003496873132927892370_">
				<use xlink:href="#SVGID_00000163063038512680203360000015861605876639299775_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000052077606385028478000000003890744804378502586_" cx="-317.9978" cy="550.9061" r="0.4214" gradientTransform="matrix(65.6842 0 0 -65.6842 20933.998 36228.6055)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF580"/>
				<stop  offset="3.596561e-02" style="stop-color:#E6D96B"/>
				<stop  offset="0.1132" style="stop-color:#A59236"/>
				<stop  offset="0.1319" style="stop-color:#958028"/>
				<stop  offset="0.1648" style="stop-color:#7B6516"/>
				<stop  offset="0.3022" style="stop-color:#7E6210"/>
				<stop  offset="0.3892" style="stop-color:#80630F"/>
				<stop  offset="0.4121" style="stop-color:#84650C"/>
				<stop  offset="0.421" style="stop-color:#8B6E17"/>
				<stop  offset="0.4368" style="stop-color:#9F8535"/>
				<stop  offset="0.4574" style="stop-color:#BFAC64"/>
				<stop  offset="0.478" style="stop-color:#E4D79B"/>
				<stop  offset="0.4786" style="stop-color:#E6DA9F"/>
				<stop  offset="0.4822" style="stop-color:#F3EAB5"/>
				<stop  offset="0.4858" style="stop-color:#FAF4C2"/>
				<stop  offset="0.489" style="stop-color:#FDF7C6"/>
				<stop  offset="0.6703" style="stop-color:#FAF5CA"/>
				<stop  offset="0.6951" style="stop-color:#F9F4C8"/>
				<stop  offset="0.7075" style="stop-color:#F5EFC0"/>
				<stop  offset="0.7173" style="stop-color:#EFE6B3"/>
				<stop  offset="0.7256" style="stop-color:#E5DAA0"/>
				<stop  offset="0.733" style="stop-color:#D9CA88"/>
				<stop  offset="0.7398" style="stop-color:#CAB769"/>
				<stop  offset="0.7461" style="stop-color:#B9A046"/>
				<stop  offset="0.7518" style="stop-color:#A5861D"/>
				<stop  offset="0.7527" style="stop-color:#A18116"/>
				<stop  offset="0.8297" style="stop-color:#AB8C0D"/>
				<stop  offset="0.8679" style="stop-color:#B29409"/>
				<stop  offset="0.9121" style="stop-color:#C0A300"/>
				<stop  offset="0.9835" style="stop-color:#F5E27A"/>
				<stop  offset="0.9902" style="stop-color:#F7E57F"/>
				<stop  offset="0.9983" style="stop-color:#FDEE8E"/>
				<stop  offset="1" style="stop-color:#FFF092"/>
			</radialGradient>
			
				<rect x="21.9" y="25.1" style="clip-path:url(#SVGID_00000172418829703181175710000003496873132927892370_);fill:url(#SVGID_00000052077606385028478000000003890744804378502586_);" width="37.8" height="27.2"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000134221057600439047100000010770032888462356385_" d="M27,26.2c-1.8,1.3-2.7,3.7-5,1.6
					c2.9,9.1,11.7-0.7,15.8,12.4c-3.3-0.9-9.2-0.7-9.6,3.7c0,0.1,0,0.2,0,0.3c-0.2,5.1,4.6,6.1,2.3,8.1c8.2-1,0.4-8,7.6-8.1
					c0.2,0,0.4,0,0.6,0c4.6,0.2,4.3,4.7,0.4,5.1c2,0.7,8.4,0.6,10-3.7c1.4-3.5-2.2-7.2,2.1-9.3c0.2-0.1,0.4-0.2,0.6-0.3
					c3.6-1.3,5.4,1.9,6.1,3.1c1.7-3.7-2.6-8.7-6.7-8c-4.9,0.8-3.8,5.5-6.9,6.3c-0.6,0.1-1.1,0.2-1.6,0.2c-5.2,0-4.4-7.8-9-10.9
					c-1.5-1-2.8-1.4-3.9-1.4c0,0,0,0-0.1,0C28.6,25.2,27.8,25.6,27,26.2"/>
			</defs>
			<clipPath id="SVGID_00000016776020752035379310000000894209787225516190_">
				<use xlink:href="#SVGID_00000134221057600439047100000010770032888462356385_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000062875822335507847250000010232394793102513580_" cx="-318.0472" cy="550.9846" r="0.421" gradientTransform="matrix(66.83 0 0 -66.83 21301.7148 36865.375)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF580"/>
				<stop  offset="3.712143e-02" style="stop-color:#E6D96B"/>
				<stop  offset="0.1169" style="stop-color:#A69236"/>
				<stop  offset="0.1319" style="stop-color:#99842B"/>
				<stop  offset="0.1648" style="stop-color:#7F6919"/>
				<stop  offset="0.3022" style="stop-color:#806512"/>
				<stop  offset="0.3865" style="stop-color:#826611"/>
				<stop  offset="0.4121" style="stop-color:#87680E"/>
				<stop  offset="0.4213" style="stop-color:#8E7119"/>
				<stop  offset="0.4376" style="stop-color:#A28837"/>
				<stop  offset="0.4589" style="stop-color:#C2AE66"/>
				<stop  offset="0.478" style="stop-color:#E2D597"/>
				<stop  offset="0.4782" style="stop-color:#E3D698"/>
				<stop  offset="0.482" style="stop-color:#F0E6AE"/>
				<stop  offset="0.4856" style="stop-color:#F7F0BB"/>
				<stop  offset="0.489" style="stop-color:#FAF3BF"/>
				<stop  offset="0.6703" style="stop-color:#FBF5C8"/>
				<stop  offset="0.6926" style="stop-color:#FAF3C6"/>
				<stop  offset="0.7052" style="stop-color:#F6EEBD"/>
				<stop  offset="0.7154" style="stop-color:#EFE5B0"/>
				<stop  offset="0.7243" style="stop-color:#E5D99C"/>
				<stop  offset="0.7324" style="stop-color:#D8C882"/>
				<stop  offset="0.7399" style="stop-color:#C9B463"/>
				<stop  offset="0.7467" style="stop-color:#B69D3E"/>
				<stop  offset="0.7527" style="stop-color:#A38417"/>
				<stop  offset="0.8297" style="stop-color:#AC8E0D"/>
				<stop  offset="0.8679" style="stop-color:#B39609"/>
				<stop  offset="0.9121" style="stop-color:#C1A501"/>
				<stop  offset="0.9835" style="stop-color:#F6E37B"/>
				<stop  offset="0.9906" style="stop-color:#F8E680"/>
				<stop  offset="0.9991" style="stop-color:#FEEF8F"/>
				<stop  offset="1" style="stop-color:#FFF091"/>
			</radialGradient>
			
				<rect x="22" y="25.2" style="clip-path:url(#SVGID_00000016776020752035379310000000894209787225516190_);fill:url(#SVGID_00000062875822335507847250000010232394793102513580_);" width="37.6" height="27.1"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000009556217500891808420000004314153217624919699_" d="M27.1,26.2c-1.8,1.3-2.7,3.7-5,1.6
					c2.9,8.9,11.6-0.7,15.7,12.3c-3.2-0.8-9.2-0.7-9.6,3.6c0,0.1,0,0.2,0,0.3c-0.2,5,4.5,6.1,2.4,8.1c7.9-1,0.4-8,7.5-8.1
					c0.2,0,0.4,0,0.6,0c4.6,0.2,4.3,4.6,0.5,5.1c1.9,0.6,8.2,0.5,9.8-3.7c1.4-3.5-2.2-7.2,2.1-9.3c0.2-0.1,0.4-0.2,0.6-0.3
					c3.5-1.3,5.4,1.8,6.1,3c1.5-3.7-2.7-8.5-6.7-7.9c-4.8,0.8-3.7,5.5-6.9,6.4c-0.6,0.1-1.1,0.2-1.6,0.2c-5.2,0-4.4-7.7-9-10.9
					c-1.5-1-2.8-1.5-3.9-1.5c0,0-0.1,0-0.1,0C28.6,25.3,27.8,25.7,27.1,26.2"/>
			</defs>
			<clipPath id="SVGID_00000087393409997257508760000008986097549972506529_">
				<use xlink:href="#SVGID_00000009556217500891808420000004314153217624919699_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000084530208540074042810000013999505777657251510_" cx="-318.0935" cy="551.0535" r="0.4215" gradientTransform="matrix(67.8836 0 0 -67.8836 21640.002 37450.918)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF580"/>
				<stop  offset="3.866435e-02" style="stop-color:#E6D96B"/>
				<stop  offset="0.1218" style="stop-color:#A59235"/>
				<stop  offset="0.1319" style="stop-color:#9D892E"/>
				<stop  offset="0.1648" style="stop-color:#836E1C"/>
				<stop  offset="0.3022" style="stop-color:#826814"/>
				<stop  offset="0.3865" style="stop-color:#846913"/>
				<stop  offset="0.4121" style="stop-color:#896A10"/>
				<stop  offset="0.4217" style="stop-color:#90731B"/>
				<stop  offset="0.4385" style="stop-color:#A48B39"/>
				<stop  offset="0.4605" style="stop-color:#C4B168"/>
				<stop  offset="0.478" style="stop-color:#E1D393"/>
				<stop  offset="0.4818" style="stop-color:#EDE3A8"/>
				<stop  offset="0.4855" style="stop-color:#F5EDB5"/>
				<stop  offset="0.489" style="stop-color:#F8F0B9"/>
				<stop  offset="0.6699" style="stop-color:#FBF5C5"/>
				<stop  offset="0.6703" style="stop-color:#FBF5C5"/>
				<stop  offset="0.6905" style="stop-color:#FAF3C2"/>
				<stop  offset="0.7033" style="stop-color:#F5EEBA"/>
				<stop  offset="0.714" style="stop-color:#EEE4AB"/>
				<stop  offset="0.7235" style="stop-color:#E4D796"/>
				<stop  offset="0.7323" style="stop-color:#D7C67B"/>
				<stop  offset="0.7405" style="stop-color:#C6B05A"/>
				<stop  offset="0.7481" style="stop-color:#B39734"/>
				<stop  offset="0.7527" style="stop-color:#A68619"/>
				<stop  offset="0.8297" style="stop-color:#AD8F0F"/>
				<stop  offset="0.8668" style="stop-color:#B4970C"/>
				<stop  offset="0.9121" style="stop-color:#C3A705"/>
				<stop  offset="0.9835" style="stop-color:#F6E47A"/>
				<stop  offset="0.9906" style="stop-color:#F8E77F"/>
				<stop  offset="0.9991" style="stop-color:#FEF08E"/>
				<stop  offset="1" style="stop-color:#FFF190"/>
			</radialGradient>
			
				<rect x="22.1" y="25.3" style="clip-path:url(#SVGID_00000087393409997257508760000008986097549972506529_);fill:url(#SVGID_00000084530208540074042810000013999505777657251510_);" width="37.3" height="27"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000104692166215538511150000005202413964160453504_" d="M27.2,26.2c-1.8,1.3-2.7,3.7-5,1.7
					c2.8,8.6,11.6-0.8,15.7,12.3c-3.2-0.8-9.2-0.7-9.6,3.6c0,0.1,0,0.2,0,0.3c-0.2,5,4.4,6.1,2.4,8.1c7.7-1,0.3-8,7.5-8.1
					c0.2,0,0.4,0,0.6,0c4.5,0.2,4.4,4.6,0.7,5.2c1.9,0.6,8,0.4,9.6-3.7c1.3-3.5-2.2-7.2,2.1-9.3c0.2-0.1,0.4-0.2,0.6-0.3
					c3.4-1.3,5.4,1.6,6.2,2.9c1.4-3.6-2.7-8.4-6.7-7.7c-4.8,0.8-3.7,5.5-6.9,6.4c-0.6,0.1-1.1,0.2-1.6,0.2c-5.1,0.1-4.5-7.6-9-10.8
					c-1.5-1-2.7-1.5-3.8-1.5c-0.1,0-0.1,0-0.2,0C28.6,25.3,27.9,25.7,27.2,26.2"/>
			</defs>
			<clipPath id="SVGID_00000088107112415832952610000003180575665920981945_">
				<use xlink:href="#SVGID_00000104692166215538511150000005202413964160453504_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000035492692706511906400000014826222732312854691_" cx="-318.1468" cy="551.1052" r="0.4216" gradientTransform="matrix(68.8492 0 0 -68.8492 21950.8633 37986.875)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF580"/>
				<stop  offset="4.001195e-02" style="stop-color:#E6D96B"/>
				<stop  offset="0.126" style="stop-color:#A79235"/>
				<stop  offset="0.1319" style="stop-color:#A28D31"/>
				<stop  offset="0.1648" style="stop-color:#87721F"/>
				<stop  offset="0.3022" style="stop-color:#856B15"/>
				<stop  offset="0.3865" style="stop-color:#876C14"/>
				<stop  offset="0.4121" style="stop-color:#8C6D12"/>
				<stop  offset="0.4221" style="stop-color:#93761D"/>
				<stop  offset="0.4397" style="stop-color:#A78D3B"/>
				<stop  offset="0.4627" style="stop-color:#C6B46A"/>
				<stop  offset="0.478" style="stop-color:#DED08E"/>
				<stop  offset="0.4817" style="stop-color:#EADFA2"/>
				<stop  offset="0.4855" style="stop-color:#F2E9AF"/>
				<stop  offset="0.489" style="stop-color:#F5ECB3"/>
				<stop  offset="0.6328" style="stop-color:#F9F3BF"/>
				<stop  offset="0.6703" style="stop-color:#FBF5C3"/>
				<stop  offset="0.6887" style="stop-color:#FAF3C0"/>
				<stop  offset="0.7015" style="stop-color:#F5EDB7"/>
				<stop  offset="0.7126" style="stop-color:#EEE3A8"/>
				<stop  offset="0.7227" style="stop-color:#E3D592"/>
				<stop  offset="0.7323" style="stop-color:#D5C375"/>
				<stop  offset="0.7413" style="stop-color:#C4AD53"/>
				<stop  offset="0.7498" style="stop-color:#B0932A"/>
				<stop  offset="0.7527" style="stop-color:#A8891A"/>
				<stop  offset="0.8297" style="stop-color:#AE9010"/>
				<stop  offset="0.8679" style="stop-color:#B5980D"/>
				<stop  offset="0.9121" style="stop-color:#C3A707"/>
				<stop  offset="0.9835" style="stop-color:#F8E579"/>
				<stop  offset="0.9906" style="stop-color:#FAE87E"/>
				<stop  offset="0.9991" style="stop-color:#FEEF8D"/>
				<stop  offset="1" style="stop-color:#FFF08F"/>
			</radialGradient>
			
				<rect x="22.2" y="25.3" style="clip-path:url(#SVGID_00000088107112415832952610000003180575665920981945_);fill:url(#SVGID_00000035492692706511906400000014826222732312854691_);" width="37.1" height="26.8"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000013913376360433001280000004287798163097862557_" d="M27.2,26.2c-1.8,1.2-2.8,3.6-5,1.7
					c2.8,8.4,11.5-0.8,15.7,12.3c-3.2-0.8-9.2-0.7-9.6,3.5c0,0.1,0,0.2,0,0.3c-0.2,5,4.4,6,2.4,8.1c7.5-1.1,0.3-8,7.4-8.1
					c0.2,0,0.4,0,0.6,0c4.5,0.2,4.4,4.6,0.8,5.2c1.9,0.6,7.8,0.3,9.4-3.7c1.3-3.5-2.2-7.1,2.1-9.3c0.2-0.1,0.4-0.2,0.6-0.3
					c3.4-1.3,5.4,1.5,6.2,2.8c1.3-3.5-2.7-8.2-6.7-7.6c-4.7,0.7-3.6,5.5-6.8,6.4c-0.5,0.2-1.1,0.2-1.6,0.2c-5.1,0.1-4.6-7.5-9-10.7
					c-1.4-1-2.7-1.5-3.8-1.6c-0.1,0-0.2,0-0.3,0C28.7,25.4,27.9,25.8,27.2,26.2"/>
			</defs>
			<clipPath id="SVGID_00000046309264867066435120000006484352474402728355_">
				<use xlink:href="#SVGID_00000013913376360433001280000004287798163097862557_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000096020890454963292020000008114666118500726206_" cx="-318.1807" cy="551.1657" r="0.4213" gradientTransform="matrix(69.7304 0 0 -69.7304 22233.6367 38477.0273)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF581"/>
				<stop  offset="4.183748e-02" style="stop-color:#E6D96C"/>
				<stop  offset="0.1319" style="stop-color:#A59235"/>
				<stop  offset="0.1648" style="stop-color:#8C7823"/>
				<stop  offset="0.3022" style="stop-color:#876D18"/>
				<stop  offset="0.3892" style="stop-color:#896E17"/>
				<stop  offset="0.4121" style="stop-color:#8D6F15"/>
				<stop  offset="0.4225" style="stop-color:#957820"/>
				<stop  offset="0.441" style="stop-color:#A9903E"/>
				<stop  offset="0.465" style="stop-color:#C9B76D"/>
				<stop  offset="0.478" style="stop-color:#DDCF8A"/>
				<stop  offset="0.4813" style="stop-color:#E7DC9B"/>
				<stop  offset="0.4853" style="stop-color:#EFE6A8"/>
				<stop  offset="0.489" style="stop-color:#F2E9AC"/>
				<stop  offset="0.6048" style="stop-color:#F8F0B8"/>
				<stop  offset="0.6703" style="stop-color:#FCF6C1"/>
				<stop  offset="0.6871" style="stop-color:#FAF4BE"/>
				<stop  offset="0.7" style="stop-color:#F6EEB4"/>
				<stop  offset="0.7116" style="stop-color:#EDE3A4"/>
				<stop  offset="0.7224" style="stop-color:#E2D48D"/>
				<stop  offset="0.7327" style="stop-color:#D3C16F"/>
				<stop  offset="0.7427" style="stop-color:#C1A94A"/>
				<stop  offset="0.7521" style="stop-color:#AC8E1F"/>
				<stop  offset="0.7527" style="stop-color:#AA8C1C"/>
				<stop  offset="0.8204" style="stop-color:#AE9111"/>
				<stop  offset="0.8297" style="stop-color:#AF920F"/>
				<stop  offset="0.8679" style="stop-color:#B69A0E"/>
				<stop  offset="0.9121" style="stop-color:#C5A90C"/>
				<stop  offset="0.9835" style="stop-color:#F8E679"/>
				<stop  offset="0.991" style="stop-color:#FAE97E"/>
				<stop  offset="1" style="stop-color:#FFF18D"/>
				<stop  offset="1" style="stop-color:#FFF18D"/>
			</radialGradient>
			
				<rect x="22.2" y="25.4" style="clip-path:url(#SVGID_00000046309264867066435120000006484352474402728355_);fill:url(#SVGID_00000096020890454963292020000008114666118500726206_);" width="36.8" height="26.7"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000147943173997884057850000004515341856662121345_" d="M27.3,26.2c-1.8,1.2-2.8,3.6-5,1.8
					c2.7,8.2,11.4-0.8,15.6,12.2c-3.2-0.7-9.2-0.7-9.6,3.5c0,0.1,0,0.2,0,0.3c-0.2,4.9,4.3,6,2.4,8c7.2-1.1,0.3-8,7.4-8.1
					c0.2,0,0.4,0,0.6,0c4.5,0.2,4.4,4.6,1,5.2c1.9,0.5,7.7,0.2,9.2-3.8c1.3-3.5-2.2-7.1,2.1-9.3c0.2-0.1,0.4-0.2,0.6-0.3
					c3.3-1.3,5.4,1.4,6.2,2.7c1.2-3.4-2.7-8-6.7-7.4c-4.7,0.7-3.6,5.4-6.8,6.4c-0.5,0.2-1,0.2-1.5,0.2c-5.1,0.2-4.6-7.4-9-10.6
					c-1.4-1-2.6-1.5-3.7-1.6c-0.1,0-0.2,0-0.4,0C28.7,25.4,28,25.8,27.3,26.2"/>
			</defs>
			<clipPath id="SVGID_00000166660407960897340850000003778562378070592390_">
				<use xlink:href="#SVGID_00000147943173997884057850000004515341856662121345_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000072268872486944397650000007819977759980520089_" cx="-318.2133" cy="551.2158" r="0.4216" gradientTransform="matrix(70.5297 0 0 -70.5297 22490.3203 38921.4336)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF581"/>
				<stop  offset="4.341850e-02" style="stop-color:#E6D96C"/>
				<stop  offset="0.1319" style="stop-color:#AA9638"/>
				<stop  offset="0.1648" style="stop-color:#917C26"/>
				<stop  offset="0.2935" style="stop-color:#8B711B"/>
				<stop  offset="0.3022" style="stop-color:#8A701A"/>
				<stop  offset="0.3892" style="stop-color:#8C7119"/>
				<stop  offset="0.4121" style="stop-color:#907216"/>
				<stop  offset="0.423" style="stop-color:#977B21"/>
				<stop  offset="0.4422" style="stop-color:#AB933F"/>
				<stop  offset="0.4672" style="stop-color:#CCBA6E"/>
				<stop  offset="0.478" style="stop-color:#DBCC85"/>
				<stop  offset="0.4812" style="stop-color:#E5D995"/>
				<stop  offset="0.4853" style="stop-color:#EDE3A2"/>
				<stop  offset="0.489" style="stop-color:#F0E7A6"/>
				<stop  offset="0.5931" style="stop-color:#F6EEB2"/>
				<stop  offset="0.6703" style="stop-color:#FCF6BE"/>
				<stop  offset="0.6858" style="stop-color:#FAF4BB"/>
				<stop  offset="0.6988" style="stop-color:#F5EDB0"/>
				<stop  offset="0.711" style="stop-color:#ECE29F"/>
				<stop  offset="0.7226" style="stop-color:#E0D286"/>
				<stop  offset="0.7338" style="stop-color:#D0BE66"/>
				<stop  offset="0.7447" style="stop-color:#BDA540"/>
				<stop  offset="0.7527" style="stop-color:#AC8F1E"/>
				<stop  offset="0.8204" style="stop-color:#AF9213"/>
				<stop  offset="0.8297" style="stop-color:#AF9311"/>
				<stop  offset="0.8668" style="stop-color:#B69B11"/>
				<stop  offset="0.9121" style="stop-color:#C6AB11"/>
				<stop  offset="0.9835" style="stop-color:#F9E779"/>
				<stop  offset="0.9914" style="stop-color:#FBEA7E"/>
				<stop  offset="1" style="stop-color:#FFF18B"/>
			</radialGradient>
			
				<rect x="22.3" y="25.4" style="clip-path:url(#SVGID_00000166660407960897340850000003778562378070592390_);fill:url(#SVGID_00000072268872486944397650000007819977759980520089_);" width="36.6" height="26.6"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000183933900566853673920000008684373760899871414_" d="M27.4,26.3c-1.8,1.2-2.8,3.6-5,1.8
					c2.7,8,11.4-0.9,15.6,12.2c-3.1-0.7-9.1-0.7-9.6,3.4c0,0.1,0,0.2,0,0.3c-0.2,4.9,4.2,6,2.4,8c7-1.1,0.2-8,7.3-8
					c0.2,0,0.4,0,0.6,0c4.5,0.2,4.4,4.6,1.1,5.2c1.9,0.5,7.5,0.2,9-3.8c1.3-3.5-2.2-7,2.1-9.2c0.2-0.1,0.4-0.2,0.6-0.3
					c3.2-1.2,5.4,1.3,6.3,2.6c1.1-3.4-2.7-7.9-6.7-7.3c-4.6,0.7-3.5,5.4-6.7,6.4c-0.5,0.2-1,0.2-1.5,0.3c-5.1,0.2-4.7-7.3-9-10.6
					c-1.4-1-2.6-1.5-3.7-1.7c-0.1,0-0.3,0-0.4,0C28.7,25.5,28,25.8,27.4,26.3"/>
			</defs>
			<clipPath id="SVGID_00000026864544123663752470000011001720916994727083_">
				<use xlink:href="#SVGID_00000183933900566853673920000008684373760899871414_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000059268350557447949030000013597520545951508609_" cx="-318.244" cy="551.2569" r="0.4218" gradientTransform="matrix(71.2517 0 0 -71.2517 22722.3359 39322.6641)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF582"/>
				<stop  offset="4.514301e-02" style="stop-color:#E6D96C"/>
				<stop  offset="0.1319" style="stop-color:#AE9A3B"/>
				<stop  offset="0.1648" style="stop-color:#958029"/>
				<stop  offset="0.2789" style="stop-color:#8E761E"/>
				<stop  offset="0.3022" style="stop-color:#8C731B"/>
				<stop  offset="0.3892" style="stop-color:#8E741A"/>
				<stop  offset="0.4121" style="stop-color:#927519"/>
				<stop  offset="0.4236" style="stop-color:#9A7E24"/>
				<stop  offset="0.444" style="stop-color:#AE9742"/>
				<stop  offset="0.4706" style="stop-color:#CFBE71"/>
				<stop  offset="0.478" style="stop-color:#D9CA80"/>
				<stop  offset="0.4812" style="stop-color:#E2D690"/>
				<stop  offset="0.4853" style="stop-color:#EAE09D"/>
				<stop  offset="0.489" style="stop-color:#EDE3A1"/>
				<stop  offset="0.5838" style="stop-color:#F4EBAD"/>
				<stop  offset="0.6703" style="stop-color:#FDF6BC"/>
				<stop  offset="0.6846" style="stop-color:#FBF4B8"/>
				<stop  offset="0.6978" style="stop-color:#F5ECAD"/>
				<stop  offset="0.7105" style="stop-color:#ECE09A"/>
				<stop  offset="0.723" style="stop-color:#DFD080"/>
				<stop  offset="0.7353" style="stop-color:#CDBA5E"/>
				<stop  offset="0.7473" style="stop-color:#B9A035"/>
				<stop  offset="0.7527" style="stop-color:#AE9220"/>
				<stop  offset="0.8166" style="stop-color:#B09415"/>
				<stop  offset="0.8297" style="stop-color:#B19412"/>
				<stop  offset="0.8668" style="stop-color:#B89C13"/>
				<stop  offset="0.9121" style="stop-color:#C6AC15"/>
				<stop  offset="0.9835" style="stop-color:#F9E778"/>
				<stop  offset="0.9914" style="stop-color:#FBEA7D"/>
				<stop  offset="1" style="stop-color:#FFF18A"/>
			</radialGradient>
			
				<rect x="22.4" y="25.5" style="clip-path:url(#SVGID_00000026864544123663752470000011001720916994727083_);fill:url(#SVGID_00000059268350557447949030000013597520545951508609_);" width="36.4" height="26.5"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000067948596426632483720000009671644149386132135_" d="M27.4,26.3c-1.8,1.2-2.8,3.6-5,1.8
					c2.7,7.8,11.3-0.9,15.5,12.2c-3.1-0.6-9.1-0.7-9.6,3.4c0,0.1,0,0.2,0,0.3c-0.2,4.9,4.1,6,2.4,8c6.7-1.1,0.2-8,7.2-8
					c0.2,0,0.4,0,0.6,0c4.4,0.2,4.4,4.6,1.2,5.2c1.9,0.4,7.3,0.1,8.8-3.8c1.3-3.5-2.2-7,2.1-9.2c0.2-0.1,0.4-0.2,0.6-0.3
					c3.2-1.2,5.4,1.2,6.3,2.5c1-3.3-2.7-7.7-6.6-7.1c-4.6,0.7-3.5,5.4-6.7,6.4c-0.5,0.2-1,0.2-1.5,0.3c-5.1,0.2-4.7-7.2-9-10.5
					c-1.3-1-2.5-1.6-3.7-1.7c-0.2,0-0.3,0-0.5,0C28.8,25.6,28.1,25.9,27.4,26.3"/>
			</defs>
			<clipPath id="SVGID_00000137826669104043492890000014002528672687729038_">
				<use xlink:href="#SVGID_00000067948596426632483720000009671644149386132135_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000070830600471921507240000009676447048662072755_" cx="-318.2677" cy="551.2988" r="0.4212" gradientTransform="matrix(71.902 0 0 -71.902 22931.0059 39684.4414)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF582"/>
				<stop  offset="4.750754e-02" style="stop-color:#E6D96C"/>
				<stop  offset="0.1319" style="stop-color:#B39F3F"/>
				<stop  offset="0.1648" style="stop-color:#9A852D"/>
				<stop  offset="0.2676" style="stop-color:#927B22"/>
				<stop  offset="0.3022" style="stop-color:#8F761D"/>
				<stop  offset="0.3892" style="stop-color:#91761C"/>
				<stop  offset="0.4121" style="stop-color:#95771B"/>
				<stop  offset="0.4242" style="stop-color:#9D8026"/>
				<stop  offset="0.4455" style="stop-color:#B19944"/>
				<stop  offset="0.4734" style="stop-color:#D1C173"/>
				<stop  offset="0.478" style="stop-color:#D7C87C"/>
				<stop  offset="0.4809" style="stop-color:#E0D28A"/>
				<stop  offset="0.4851" style="stop-color:#E8DD97"/>
				<stop  offset="0.489" style="stop-color:#EBE09B"/>
				<stop  offset="0.5763" style="stop-color:#F2E8A7"/>
				<stop  offset="0.6703" style="stop-color:#FDF5B9"/>
				<stop  offset="0.6837" style="stop-color:#FBF2B5"/>
				<stop  offset="0.6972" style="stop-color:#F5EBA9"/>
				<stop  offset="0.7108" style="stop-color:#EBDE95"/>
				<stop  offset="0.7243" style="stop-color:#DCCB78"/>
				<stop  offset="0.7379" style="stop-color:#CAB454"/>
				<stop  offset="0.7513" style="stop-color:#B49728"/>
				<stop  offset="0.7527" style="stop-color:#B19423"/>
				<stop  offset="0.8052" style="stop-color:#B19518"/>
				<stop  offset="0.8297" style="stop-color:#B19511"/>
				<stop  offset="0.8657" style="stop-color:#B89D13"/>
				<stop  offset="0.9121" style="stop-color:#C8AE17"/>
				<stop  offset="0.9835" style="stop-color:#FAE878"/>
				<stop  offset="0.992" style="stop-color:#FCEB7D"/>
				<stop  offset="1" style="stop-color:#FFF188"/>
			</radialGradient>
			
				<rect x="22.5" y="25.6" style="clip-path:url(#SVGID_00000137826669104043492890000014002528672687729038_);fill:url(#SVGID_00000070830600471921507240000009676447048662072755_);" width="36.1" height="26.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000105391923296559169530000016742352745910891959_" d="M27.5,26.3c-1.8,1.2-2.8,3.6-4.9,1.9
					c2.6,7.6,11.3-1,15.5,12.1c-3.1-0.6-9.1-0.7-9.5,3.3c0,0.1,0,0.2,0,0.3c-0.3,4.9,4,5.9,2.5,7.9c6.5-1.1,0.1-8,7.2-8
					c0.2,0,0.4,0,0.6,0c4.4,0.2,4.5,4.6,1.4,5.2c1.9,0.4,7.2,0,8.6-3.8c1.3-3.5-2.2-6.9,2-9.2c0.2-0.1,0.4-0.2,0.6-0.3
					c3.1-1.2,5.4,1.1,6.3,2.4c0.8-3.2-2.7-7.5-6.6-6.9c-4.5,0.7-3.4,5.4-6.6,6.4c-0.5,0.2-1,0.2-1.5,0.3c-5.1,0.3-4.8-7.1-9-10.4
					c-1.3-1-2.5-1.6-3.6-1.8c-0.2,0-0.4,0-0.6,0C28.8,25.6,28.1,25.9,27.5,26.3"/>
			</defs>
			<clipPath id="SVGID_00000163044196150603512130000013969037077936247460_">
				<use xlink:href="#SVGID_00000105391923296559169530000016742352745910891959_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000181806361847630658180000018149825491276539836_" cx="-318.2928" cy="551.3319" r="0.4218" gradientTransform="matrix(72.4834 0 0 -72.4834 23117.9062 40007.6367)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF583"/>
				<stop  offset="5.015760e-02" style="stop-color:#E6D96D"/>
				<stop  offset="0.1319" style="stop-color:#B7A443"/>
				<stop  offset="0.1648" style="stop-color:#9F8A31"/>
				<stop  offset="0.2586" style="stop-color:#977F27"/>
				<stop  offset="0.3022" style="stop-color:#927820"/>
				<stop  offset="0.3924" style="stop-color:#94791F"/>
				<stop  offset="0.4121" style="stop-color:#977A1D"/>
				<stop  offset="0.4249" style="stop-color:#9F8328"/>
				<stop  offset="0.4475" style="stop-color:#B39C46"/>
				<stop  offset="0.4771" style="stop-color:#D4C475"/>
				<stop  offset="0.478" style="stop-color:#D5C577"/>
				<stop  offset="0.4806" style="stop-color:#DDCE83"/>
				<stop  offset="0.485" style="stop-color:#E5D890"/>
				<stop  offset="0.489" style="stop-color:#E8DC94"/>
				<stop  offset="0.5662" style="stop-color:#F0E5A0"/>
				<stop  offset="0.6703" style="stop-color:#FFF6B7"/>
				<stop  offset="0.683" style="stop-color:#FDF3B3"/>
				<stop  offset="0.6968" style="stop-color:#F6EBA5"/>
				<stop  offset="0.7111" style="stop-color:#EADD90"/>
				<stop  offset="0.7259" style="stop-color:#DBC971"/>
				<stop  offset="0.7407" style="stop-color:#C6B04A"/>
				<stop  offset="0.7527" style="stop-color:#B39825"/>
				<stop  offset="0.8052" style="stop-color:#B2971A"/>
				<stop  offset="0.8297" style="stop-color:#B29713"/>
				<stop  offset="0.8657" style="stop-color:#B99F15"/>
				<stop  offset="0.9121" style="stop-color:#C9B01A"/>
				<stop  offset="0.9835" style="stop-color:#FBE977"/>
				<stop  offset="0.992" style="stop-color:#FCEC7C"/>
				<stop  offset="1" style="stop-color:#FFF287"/>
			</radialGradient>
			
				<rect x="22.5" y="25.6" style="clip-path:url(#SVGID_00000163044196150603512130000013969037077936247460_);fill:url(#SVGID_00000181806361847630658180000018149825491276539836_);" width="35.9" height="26.3"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000150064308374703434940000009392181357200144261_" d="M27.6,26.3c-1.8,1.1-2.8,3.6-4.9,1.9
					c2.6,7.3,11.2-1,15.4,12.1c-3-0.6-9.1-0.7-9.5,3.3c0,0.1,0,0.2,0,0.3c-0.3,4.8,3.9,5.9,2.5,7.9c6.3-1.2,0.1-7.9,7.1-8
					c0.2,0,0.4,0,0.6,0c4.4,0.2,4.5,4.6,1.5,5.2c1.8,0.4,7-0.1,8.4-3.8c1.3-3.5-2.2-6.9,2-9.2c0.2-0.1,0.4-0.2,0.6-0.3
					c3-1.2,5.4,1,6.4,2.3c0.7-3.1-2.8-7.4-6.6-6.8c-4.5,0.7-3.4,5.3-6.6,6.4c-0.5,0.2-1,0.3-1.5,0.3c-5.1,0.3-4.9-7-9-10.4
					c-1.3-1-2.4-1.6-3.6-1.8c-0.2,0-0.4-0.1-0.6-0.1C28.8,25.7,28.2,25.9,27.6,26.3"/>
			</defs>
			<clipPath id="SVGID_00000131367146769487660100000003067796271515414179_">
				<use xlink:href="#SVGID_00000150064308374703434940000009392181357200144261_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000165235761349746947650000001444045970879878071_" cx="-318.3055" cy="551.3714" r="0.4216" gradientTransform="matrix(73.0014 0 0 -73.0014 23283.7656 40296.4219)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF583"/>
				<stop  offset="5.313212e-02" style="stop-color:#E6D96D"/>
				<stop  offset="0.1319" style="stop-color:#BBA947"/>
				<stop  offset="0.1648" style="stop-color:#A48F34"/>
				<stop  offset="0.2512" style="stop-color:#9B842A"/>
				<stop  offset="0.3022" style="stop-color:#947B21"/>
				<stop  offset="0.3924" style="stop-color:#967C20"/>
				<stop  offset="0.4121" style="stop-color:#997D1F"/>
				<stop  offset="0.4256" style="stop-color:#A1862A"/>
				<stop  offset="0.4495" style="stop-color:#B59F48"/>
				<stop  offset="0.478" style="stop-color:#D3C373"/>
				<stop  offset="0.4803" style="stop-color:#D9CB7D"/>
				<stop  offset="0.4848" style="stop-color:#E2D58A"/>
				<stop  offset="0.489" style="stop-color:#E5D98E"/>
				<stop  offset="0.5613" style="stop-color:#EDE29A"/>
				<stop  offset="0.6703" style="stop-color:#FFF6B4"/>
				<stop  offset="0.6824" style="stop-color:#FCF3AF"/>
				<stop  offset="0.6967" style="stop-color:#F5E9A1"/>
				<stop  offset="0.7121" style="stop-color:#E8DA89"/>
				<stop  offset="0.7284" style="stop-color:#D7C467"/>
				<stop  offset="0.7451" style="stop-color:#C1A83D"/>
				<stop  offset="0.7527" style="stop-color:#B59A27"/>
				<stop  offset="0.8031" style="stop-color:#B4991C"/>
				<stop  offset="0.8297" style="stop-color:#B39814"/>
				<stop  offset="0.8657" style="stop-color:#BAA017"/>
				<stop  offset="0.9121" style="stop-color:#CAB11D"/>
				<stop  offset="0.9835" style="stop-color:#FCEA76"/>
				<stop  offset="0.9923" style="stop-color:#FDED7B"/>
				<stop  offset="1" style="stop-color:#FFF285"/>
			</radialGradient>
			
				<rect x="22.6" y="25.7" style="clip-path:url(#SVGID_00000131367146769487660100000003067796271515414179_);fill:url(#SVGID_00000165235761349746947650000001444045970879878071_);" width="35.6" height="26.2"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000170248786346960176760000015169092410317879690_" d="M27.6,26.3c-1.8,1.1-2.9,3.5-4.9,2
					c2.5,7.1,11.1-1.1,15.4,12c-3-0.5-9.1-0.7-9.5,3.2c0,0.1,0,0.2,0,0.3c-0.3,4.8,3.8,5.9,2.5,7.9c6-1.2,0-7.9,7.1-8
					c0.2,0,0.4,0,0.6,0c4.3,0.2,4.5,4.5,1.6,5.3c1.8,0.3,6.8-0.2,8.2-3.9c1.3-3.5-2.2-6.8,2-9.2c0.2-0.1,0.4-0.2,0.6-0.3
					c3-1.2,5.4,0.9,6.4,2.2c0.6-3.1-2.8-7.2-6.6-6.6c-4.4,0.6-3.3,5.3-6.6,6.4c-0.5,0.2-1,0.3-1.5,0.3c-5.1,0.3-4.9-6.9-9-10.3
					c-1.2-1-2.4-1.6-3.5-1.9c-0.2,0-0.5-0.1-0.7-0.1C28.9,25.7,28.2,26,27.6,26.3"/>
			</defs>
			<clipPath id="SVGID_00000005250614483227850460000004749919428076826786_">
				<use xlink:href="#SVGID_00000170248786346960176760000015169092410317879690_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000160886739578261869750000017064723529945793716_" cx="-318.3259" cy="551.3971" r="0.4213" gradientTransform="matrix(73.461 0 0 -73.461 23431.6191 40551.9727)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF584"/>
				<stop  offset="5.650536e-02" style="stop-color:#E6D96E"/>
				<stop  offset="0.1319" style="stop-color:#C0AE4B"/>
				<stop  offset="0.1648" style="stop-color:#A99538"/>
				<stop  offset="0.2422" style="stop-color:#A08A2E"/>
				<stop  offset="0.3022" style="stop-color:#967E23"/>
				<stop  offset="0.3892" style="stop-color:#987F22"/>
				<stop  offset="0.4121" style="stop-color:#9C8021"/>
				<stop  offset="0.4264" style="stop-color:#A4892C"/>
				<stop  offset="0.4517" style="stop-color:#B8A24A"/>
				<stop  offset="0.478" style="stop-color:#D1C26F"/>
				<stop  offset="0.4799" style="stop-color:#D6C877"/>
				<stop  offset="0.4847" style="stop-color:#DFD284"/>
				<stop  offset="0.489" style="stop-color:#E2D688"/>
				<stop  offset="0.5571" style="stop-color:#EADF94"/>
				<stop  offset="0.6703" style="stop-color:#FFF6B1"/>
				<stop  offset="0.682" style="stop-color:#FCF2AB"/>
				<stop  offset="0.697" style="stop-color:#F4E89B"/>
				<stop  offset="0.7138" style="stop-color:#E6D781"/>
				<stop  offset="0.732" style="stop-color:#D2BF5C"/>
				<stop  offset="0.7509" style="stop-color:#BAA02E"/>
				<stop  offset="0.7527" style="stop-color:#B79D29"/>
				<stop  offset="0.7993" style="stop-color:#B59B1E"/>
				<stop  offset="0.8297" style="stop-color:#B49914"/>
				<stop  offset="0.8647" style="stop-color:#BBA118"/>
				<stop  offset="0.9121" style="stop-color:#CBB320"/>
				<stop  offset="0.9835" style="stop-color:#FCEB77"/>
				<stop  offset="0.9927" style="stop-color:#FDEE7C"/>
				<stop  offset="1" style="stop-color:#FFF285"/>
			</radialGradient>
			
				<rect x="22.7" y="25.7" style="clip-path:url(#SVGID_00000005250614483227850460000004749919428076826786_);fill:url(#SVGID_00000160886739578261869750000017064723529945793716_);" width="35.4" height="26.1"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000021117113644158882110000000112137728833285291_" d="M27.7,26.4c-1.8,1.1-2.9,3.5-4.9,2
					c2.5,6.9,11.1-1.1,15.3,12c-3-0.5-9-0.7-9.5,3.2c0,0.1,0,0.2,0,0.3c-0.3,4.8,3.7,5.9,2.5,7.9c5.8-1.2,0-7.9,7-8
					c0.2,0,0.4,0,0.6,0c4.3,0.2,4.5,4.5,1.8,5.3c1.8,0.3,6.6-0.3,8-3.9c1.3-3.4-2.2-6.8,2-9.2c0.2-0.1,0.4-0.2,0.6-0.3
					c2.9-1.2,5.4,0.8,6.5,2.1c0.5-3-2.8-7-6.6-6.5c-4.4,0.6-3.3,5.3-6.5,6.4c-0.5,0.2-1,0.3-1.5,0.3c-5.1,0.4-5-6.8-9-10.2
					c-1.2-1-2.3-1.7-3.5-1.9c-0.3-0.1-0.5-0.1-0.7-0.1C28.9,25.8,28.3,26,27.7,26.4"/>
			</defs>
			<clipPath id="SVGID_00000039094565673862262910000017764874411431875983_">
				<use xlink:href="#SVGID_00000021117113644158882110000000112137728833285291_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000143574894617311101620000004807379911558225811_" cx="-318.3433" cy="551.4202" r="0.4213" gradientTransform="matrix(73.8671 0 0 -73.8671 23562.2305 40777.9102)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF584"/>
				<stop  offset="5.955044e-02" style="stop-color:#E6D96E"/>
				<stop  offset="0.1319" style="stop-color:#C4B24F"/>
				<stop  offset="0.1648" style="stop-color:#AE9A3C"/>
				<stop  offset="0.2352" style="stop-color:#A58F32"/>
				<stop  offset="0.3022" style="stop-color:#998025"/>
				<stop  offset="0.3966" style="stop-color:#9B8024"/>
				<stop  offset="0.4121" style="stop-color:#9D8123"/>
				<stop  offset="0.4275" style="stop-color:#A58B2E"/>
				<stop  offset="0.4546" style="stop-color:#BAA44B"/>
				<stop  offset="0.478" style="stop-color:#CFBF6A"/>
				<stop  offset="0.4797" style="stop-color:#D4C471"/>
				<stop  offset="0.4846" style="stop-color:#DDCF7E"/>
				<stop  offset="0.489" style="stop-color:#E0D382"/>
				<stop  offset="0.5522" style="stop-color:#E8DC8E"/>
				<stop  offset="0.6662" style="stop-color:#FEF5AE"/>
				<stop  offset="0.6703" style="stop-color:#FFF6AF"/>
				<stop  offset="0.6818" style="stop-color:#FCF2A9"/>
				<stop  offset="0.6977" style="stop-color:#F2E697"/>
				<stop  offset="0.7162" style="stop-color:#E3D379"/>
				<stop  offset="0.7365" style="stop-color:#CEB951"/>
				<stop  offset="0.7527" style="stop-color:#BAA02B"/>
				<stop  offset="0.7976" style="stop-color:#B79D20"/>
				<stop  offset="0.8297" style="stop-color:#B59A15"/>
				<stop  offset="0.8638" style="stop-color:#BCA219"/>
				<stop  offset="0.9121" style="stop-color:#CDB522"/>
				<stop  offset="0.9835" style="stop-color:#FCEB76"/>
				<stop  offset="0.9931" style="stop-color:#FDEE7B"/>
				<stop  offset="1" style="stop-color:#FFF283"/>
			</radialGradient>
			
				<rect x="22.8" y="25.8" style="clip-path:url(#SVGID_00000039094565673862262910000017764874411431875983_);fill:url(#SVGID_00000143574894617311101620000004807379911558225811_);" width="35.2" height="25.9"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000145766761257171230170000003377371196350780292_" d="M27.8,26.4c-1.8,1.1-2.9,3.5-4.9,2.1
					c2.4,6.7,11-1.2,15.3,12c-3-0.5-9-0.7-9.5,3.1c0,0.1,0,0.2,0,0.3c-0.3,4.7,3.6,5.8,2.5,7.8c5.6-1.2-0.1-7.9,6.9-7.9
					c0.2,0,0.4,0,0.6,0c4.3,0.2,4.5,4.5,1.9,5.3c1.8,0.3,6.5-0.3,7.8-3.9c1.3-3.4-2.2-6.7,2-9.2c0.2-0.1,0.4-0.2,0.6-0.3
					c2.8-1.1,5.4,0.7,6.5,2c0.4-2.9-2.8-6.9-6.6-6.3c-4.3,0.6-3.2,5.2-6.5,6.4c-0.5,0.2-1,0.3-1.5,0.3c-5.1,0.4-5-6.7-9-10.1
					c-1.2-1-2.3-1.7-3.4-2c-0.3-0.1-0.5-0.1-0.8-0.1C28.9,25.8,28.3,26.1,27.8,26.4"/>
			</defs>
			<clipPath id="SVGID_00000142884825936674688290000018273998570767088308_">
				<use xlink:href="#SVGID_00000145766761257171230170000003377371196350780292_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000090294028544646701280000000222299923283485109_" cx="-318.3539" cy="551.4443" r="0.4221" gradientTransform="matrix(74.2273 0 0 -74.2273 23677.75 40978.6094)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF586"/>
				<stop  offset="6.389005e-02" style="stop-color:#E7D96F"/>
				<stop  offset="0.1319" style="stop-color:#C9B753"/>
				<stop  offset="0.1648" style="stop-color:#B39F40"/>
				<stop  offset="0.2313" style="stop-color:#AA9436"/>
				<stop  offset="0.3022" style="stop-color:#9C8327"/>
				<stop  offset="0.3966" style="stop-color:#9E8326"/>
				<stop  offset="0.4121" style="stop-color:#A08425"/>
				<stop  offset="0.4286" style="stop-color:#A88E30"/>
				<stop  offset="0.4576" style="stop-color:#BDA84D"/>
				<stop  offset="0.478" style="stop-color:#CEBD66"/>
				<stop  offset="0.4795" style="stop-color:#D2C26C"/>
				<stop  offset="0.4845" style="stop-color:#DBCC79"/>
				<stop  offset="0.489" style="stop-color:#DED07D"/>
				<stop  offset="0.5501" style="stop-color:#E6DA89"/>
				<stop  offset="0.6602" style="stop-color:#FDF3A9"/>
				<stop  offset="0.6703" style="stop-color:#FFF6AC"/>
				<stop  offset="0.6819" style="stop-color:#FBF1A5"/>
				<stop  offset="0.6992" style="stop-color:#F0E490"/>
				<stop  offset="0.7201" style="stop-color:#DFCE6F"/>
				<stop  offset="0.7435" style="stop-color:#C7B041"/>
				<stop  offset="0.7527" style="stop-color:#BCA32D"/>
				<stop  offset="0.7947" style="stop-color:#B99F22"/>
				<stop  offset="0.8297" style="stop-color:#B69B15"/>
				<stop  offset="0.8647" style="stop-color:#BDA319"/>
				<stop  offset="0.9121" style="stop-color:#CDB524"/>
				<stop  offset="0.9835" style="stop-color:#FDEC75"/>
				<stop  offset="0.9935" style="stop-color:#FEEF7A"/>
				<stop  offset="1" style="stop-color:#FFF381"/>
			</radialGradient>
			
				<rect x="22.9" y="25.8" style="clip-path:url(#SVGID_00000142884825936674688290000018273998570767088308_);fill:url(#SVGID_00000090294028544646701280000000222299923283485109_);" width="34.9" height="25.8"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000127753611906259634270000002169011666954379427_" d="M27.8,26.4c-1.8,1.1-2.9,3.5-4.9,2.1
					c2.4,6.5,11-1.2,15.3,11.9c-2.9-0.4-9-0.7-9.5,3.1c0,0.1,0,0.2,0,0.3c-0.3,4.7,3.5,5.8,2.5,7.8c5.3-1.2-0.1-7.9,6.9-7.9
					c0.2,0,0.4,0,0.5,0c4.3,0.2,4.5,4.5,2.1,5.3c1.8,0.2,6.3-0.4,7.6-3.9c1.3-3.4-2.2-6.7,2-9.1c0.2-0.1,0.4-0.2,0.6-0.3
					c2.8-1.1,5.4,0.6,6.5,1.9c0.2-2.8-2.8-6.7-6.6-6.2c-4.3,0.6-3.2,5.2-6.4,6.4c-0.5,0.2-1,0.3-1.5,0.3c-5,0.4-5.1-6.5-9-10.1
					c-1.1-1-2.3-1.7-3.4-2c-0.3-0.1-0.6-0.1-0.9-0.1C29,25.9,28.4,26.1,27.8,26.4"/>
			</defs>
			<clipPath id="SVGID_00000000943746262093922440000000290951562271424431_">
				<use xlink:href="#SVGID_00000127753611906259634270000002169011666954379427_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000093165019693615446180000011247878441790839734_" cx="-318.3715" cy="551.4616" r="0.4212" gradientTransform="matrix(74.5453 0 0 -74.5453 23780.3809 41155.5273)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF586"/>
				<stop  offset="6.868859e-02" style="stop-color:#E7D96F"/>
				<stop  offset="0.1319" style="stop-color:#CEBC57"/>
				<stop  offset="0.1648" style="stop-color:#B8A544"/>
				<stop  offset="0.2247" style="stop-color:#AF9A3B"/>
				<stop  offset="0.3022" style="stop-color:#9F8529"/>
				<stop  offset="0.4022" style="stop-color:#A18628"/>
				<stop  offset="0.4121" style="stop-color:#A28727"/>
				<stop  offset="0.4302" style="stop-color:#AA9132"/>
				<stop  offset="0.4619" style="stop-color:#BFAB4F"/>
				<stop  offset="0.478" style="stop-color:#CCBB61"/>
				<stop  offset="0.4793" style="stop-color:#CFBF66"/>
				<stop  offset="0.4844" style="stop-color:#D8C973"/>
				<stop  offset="0.489" style="stop-color:#DBCD77"/>
				<stop  offset="0.5462" style="stop-color:#E3D783"/>
				<stop  offset="0.6494" style="stop-color:#FAF0A3"/>
				<stop  offset="0.6703" style="stop-color:#FFF6AA"/>
				<stop  offset="0.6822" style="stop-color:#FAF0A1"/>
				<stop  offset="0.7014" style="stop-color:#EEE18A"/>
				<stop  offset="0.7255" style="stop-color:#DAC863"/>
				<stop  offset="0.7527" style="stop-color:#BFA62F"/>
				<stop  offset="0.7934" style="stop-color:#BBA224"/>
				<stop  offset="0.8297" style="stop-color:#B69D16"/>
				<stop  offset="0.8647" style="stop-color:#BDA51B"/>
				<stop  offset="0.9121" style="stop-color:#CEB727"/>
				<stop  offset="0.9835" style="stop-color:#FDED75"/>
				<stop  offset="0.9941" style="stop-color:#FEF07A"/>
				<stop  offset="1" style="stop-color:#FFF380"/>
			</radialGradient>
			
				<rect x="22.9" y="25.9" style="clip-path:url(#SVGID_00000000943746262093922440000000290951562271424431_);fill:url(#SVGID_00000093165019693615446180000011247878441790839734_);" width="34.7" height="25.7"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000150089430100737479730000016378675906453669813_" d="M27.9,26.4c-1.8,1-2.9,3.5-4.9,2.2
					c2.3,6.3,10.9-1.2,15.2,11.9c-2.9-0.4-9-0.7-9.5,3c0,0.1,0,0.2,0,0.3c-0.3,4.7,3.4,5.8,2.6,7.8c5.1-1.3-0.2-7.9,6.8-7.9
					c0.2,0,0.4,0,0.5,0c4.2,0.3,4.6,4.5,2.2,5.3c1.8,0.2,6.1-0.5,7.4-4c1.2-3.4-2.2-6.7,2-9.1c0.2-0.1,0.4-0.2,0.6-0.3
					c2.7-1.1,5.4,0.5,6.6,1.8c0.1-2.8-2.8-6.5-6.6-6c-4.2,0.6-3.1,5.2-6.4,6.4c-0.5,0.2-1,0.3-1.5,0.3c-5,0.5-5.2-6.4-9-10
					c-1.1-1-2.2-1.7-3.4-2c-0.3-0.1-0.6-0.1-0.9-0.1C29,25.9,28.4,26.1,27.9,26.4"/>
			</defs>
			<clipPath id="SVGID_00000168076481019466514770000003289075020636140721_">
				<use xlink:href="#SVGID_00000150089430100737479730000016378675906453669813_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000004514858811159346880000009834456515463846577_" cx="-318.3791" cy="551.4822" r="0.4216" gradientTransform="matrix(74.8307 0 0 -74.8307 23871.8555 41314.7344)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF587"/>
				<stop  offset="7.599368e-02" style="stop-color:#E7D970"/>
				<stop  offset="0.1319" style="stop-color:#D3C25C"/>
				<stop  offset="0.1648" style="stop-color:#BEAB49"/>
				<stop  offset="0.2207" style="stop-color:#B5A03F"/>
				<stop  offset="0.3022" style="stop-color:#A2882A"/>
				<stop  offset="0.4022" style="stop-color:#A48929"/>
				<stop  offset="0.4121" style="stop-color:#A58928"/>
				<stop  offset="0.4318" style="stop-color:#AD9333"/>
				<stop  offset="0.4663" style="stop-color:#C2AE50"/>
				<stop  offset="0.478" style="stop-color:#CAB95C"/>
				<stop  offset="0.4791" style="stop-color:#CCBC60"/>
				<stop  offset="0.4842" style="stop-color:#D5C66D"/>
				<stop  offset="0.489" style="stop-color:#D8CA71"/>
				<stop  offset="0.5437" style="stop-color:#E1D47D"/>
				<stop  offset="0.6423" style="stop-color:#F8EE9D"/>
				<stop  offset="0.6703" style="stop-color:#FFF7A7"/>
				<stop  offset="0.6831" style="stop-color:#F9F09C"/>
				<stop  offset="0.7052" style="stop-color:#EBDD80"/>
				<stop  offset="0.7339" style="stop-color:#D3BF53"/>
				<stop  offset="0.7527" style="stop-color:#C1A931"/>
				<stop  offset="0.7934" style="stop-color:#BDA426"/>
				<stop  offset="0.8297" style="stop-color:#B79E18"/>
				<stop  offset="0.8638" style="stop-color:#BEA61D"/>
				<stop  offset="0.9121" style="stop-color:#CFB92A"/>
				<stop  offset="0.9835" style="stop-color:#FFEE74"/>
				<stop  offset="0.9941" style="stop-color:#FFF079"/>
				<stop  offset="1" style="stop-color:#FFF37F"/>
			</radialGradient>
			
				<rect x="23" y="25.9" style="clip-path:url(#SVGID_00000168076481019466514770000003289075020636140721_);fill:url(#SVGID_00000004514858811159346880000009834456515463846577_);" width="34.4" height="25.6"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000087381463697187154580000008212329766355398304_" d="M28,26.5c-1.8,1-2.9,3.5-4.9,2.2
					c2.3,6,10.8-1.3,15.2,11.9c-2.9-0.4-9-0.7-9.5,2.9c0,0.1,0,0.2,0,0.3c-0.3,4.7,3.3,5.8,2.6,7.7c4.9-1.3-0.2-7.9,6.8-7.9
					c0.2,0,0.4,0,0.5,0c4.2,0.3,4.6,4.5,2.3,5.3c1.8,0.2,5.9-0.6,7.1-4c1.2-3.4-2.3-6.6,2-9.1c0.2-0.1,0.4-0.2,0.6-0.3
					c2.6-1.1,5.4,0.3,6.6,1.7c0-2.7-2.8-6.4-6.5-5.9c-4.1,0.6-3.1,5.2-6.3,6.4c-0.5,0.2-1,0.3-1.5,0.3c-5,0.5-5.2-6.3-9-9.9
					c-1.1-1-2.2-1.7-3.3-2.1c-0.3-0.1-0.7-0.2-1-0.2C29,26,28.5,26.2,28,26.5"/>
			</defs>
			<clipPath id="SVGID_00000016757400316256386150000004732352044308657809_">
				<use xlink:href="#SVGID_00000087381463697187154580000008212329766355398304_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000179642631138525062180000001516793495454562194_" cx="-318.3934" cy="551.4964" r="0.4216" gradientTransform="matrix(75.0897 0 0 -75.0897 23955.4473 41458.9375)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF587"/>
				<stop  offset="8.346549e-02" style="stop-color:#E7D970"/>
				<stop  offset="0.1319" style="stop-color:#D7C761"/>
				<stop  offset="0.1648" style="stop-color:#C3B14D"/>
				<stop  offset="0.2172" style="stop-color:#BAA644"/>
				<stop  offset="0.3022" style="stop-color:#A48B2D"/>
				<stop  offset="0.4022" style="stop-color:#A68C2C"/>
				<stop  offset="0.4121" style="stop-color:#A78C2B"/>
				<stop  offset="0.4341" style="stop-color:#AF9736"/>
				<stop  offset="0.4728" style="stop-color:#C6B353"/>
				<stop  offset="0.478" style="stop-color:#C9B758"/>
				<stop  offset="0.4785" style="stop-color:#CAB85A"/>
				<stop  offset="0.484" style="stop-color:#D2C367"/>
				<stop  offset="0.489" style="stop-color:#D5C66B"/>
				<stop  offset="0.5414" style="stop-color:#DED077"/>
				<stop  offset="0.6358" style="stop-color:#F5EB97"/>
				<stop  offset="0.6703" style="stop-color:#FFF6A4"/>
				<stop  offset="0.6848" style="stop-color:#F8ED97"/>
				<stop  offset="0.7117" style="stop-color:#E5D674"/>
				<stop  offset="0.7479" style="stop-color:#C7B03B"/>
				<stop  offset="0.7527" style="stop-color:#C3AB33"/>
				<stop  offset="0.791" style="stop-color:#BEA628"/>
				<stop  offset="0.8297" style="stop-color:#B89F18"/>
				<stop  offset="0.8638" style="stop-color:#BFA71D"/>
				<stop  offset="0.9121" style="stop-color:#D0BA2B"/>
				<stop  offset="0.9835" style="stop-color:#FFEF73"/>
				<stop  offset="0.9941" style="stop-color:#FFF178"/>
				<stop  offset="1" style="stop-color:#FFF37E"/>
			</radialGradient>
			
				<rect x="23.1" y="26" style="clip-path:url(#SVGID_00000016757400316256386150000004732352044308657809_);fill:url(#SVGID_00000179642631138525062180000001516793495454562194_);" width="34.2" height="25.5"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000161615667431224798050000006972623541738017209_" d="M28,26.5c-1.8,1-2.9,3.4-4.9,2.3
					c2.2,5.8,10.8-1.3,15.1,11.8c-2.9-0.3-8.9-0.7-9.5,2.9c0,0.1,0,0.2,0,0.3c-0.3,4.6,3.3,5.7,2.6,7.7c4.6-1.3-0.2-7.9,6.7-7.9
					c0.2,0,0.4,0,0.5,0c4.2,0.3,4.6,4.5,2.5,5.3c1.7,0.1,5.8-0.7,6.9-4c1.2-3.4-2.3-6.6,2-9.1c0.2-0.1,0.4-0.2,0.6-0.3
					c2.6-1.1,5.4,0.2,6.7,1.6c-0.1-2.6-2.8-6.2-6.5-5.7c-4.1,0.6-3.1,5.1-6.3,6.4c-0.5,0.2-0.9,0.3-1.5,0.3c-5,0.5-5.3-6.2-9-9.8
					c-1.1-1-2.1-1.8-3.3-2.1c-0.4-0.1-0.7-0.2-1-0.2C29.1,26,28.5,26.2,28,26.5"/>
			</defs>
			<clipPath id="SVGID_00000000189578630409497370000007415334099150901673_">
				<use xlink:href="#SVGID_00000161615667431224798050000006972623541738017209_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000030487041605049445450000015122393182719951508_" cx="-318.403" cy="551.5122" r="0.4216" gradientTransform="matrix(75.327 0 0 -75.327 24031.7676 41591.2656)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF588"/>
				<stop  offset="9.265658e-02" style="stop-color:#E7D970"/>
				<stop  offset="0.1319" style="stop-color:#DCCC65"/>
				<stop  offset="0.1648" style="stop-color:#C8B652"/>
				<stop  offset="0.2151" style="stop-color:#BFAB48"/>
				<stop  offset="0.3022" style="stop-color:#A78E2F"/>
				<stop  offset="0.4107" style="stop-color:#A98F2D"/>
				<stop  offset="0.4121" style="stop-color:#A98F2D"/>
				<stop  offset="0.4372" style="stop-color:#B19A38"/>
				<stop  offset="0.478" style="stop-color:#C6B553"/>
				<stop  offset="0.4782" style="stop-color:#C6B554"/>
				<stop  offset="0.4838" style="stop-color:#D0C061"/>
				<stop  offset="0.489" style="stop-color:#D3C465"/>
				<stop  offset="0.5386" style="stop-color:#DCCE71"/>
				<stop  offset="0.6281" style="stop-color:#F3E891"/>
				<stop  offset="0.6703" style="stop-color:#FFF6A2"/>
				<stop  offset="0.6881" style="stop-color:#F5EA90"/>
				<stop  offset="0.7235" style="stop-color:#DDCB62"/>
				<stop  offset="0.7527" style="stop-color:#C6AF37"/>
				<stop  offset="0.788" style="stop-color:#C1A92C"/>
				<stop  offset="0.8297" style="stop-color:#B9A019"/>
				<stop  offset="0.863" style="stop-color:#C0A81E"/>
				<stop  offset="0.9121" style="stop-color:#D2BC2D"/>
				<stop  offset="0.9835" style="stop-color:#FFF073"/>
				<stop  offset="0.9954" style="stop-color:#FFF278"/>
				<stop  offset="1" style="stop-color:#FFF37C"/>
			</radialGradient>
			
				<rect x="23.2" y="26" style="clip-path:url(#SVGID_00000000189578630409497370000007415334099150901673_);fill:url(#SVGID_00000030487041605049445450000015122393182719951508_);" width="34.1" height="25.4"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000075862117624819814500000011147948388874759610_" d="M28.1,26.5c-1.8,1-3,3.4-4.9,2.3
					c2.2,5.6,10.7-1.4,15.1,11.8c-2.8-0.3-8.9-0.7-9.5,2.8c0,0.1,0,0.2,0,0.3c-0.3,4.6,3.2,5.7,2.6,7.7c4.4-1.3-0.3-7.9,6.7-7.8
					c0.2,0,0.4,0,0.5,0c4.1,0.3,4.6,4.5,2.6,5.4c1.7,0.1,5.6-0.8,6.7-4c1.2-3.4-2.3-6.5,2-9.1c0.2-0.1,0.4-0.2,0.6-0.3
					c2.5-1.1,5.4,0.1,6.7,1.5c-0.2-2.5-2.9-6-6.5-5.5c-4,0.5-3,5.1-6.3,6.4C44,38,43.5,38.2,43,38.2c-5,0.6-5.4-6.1-9-9.8
					c-1-1-2.1-1.8-3.2-2.2c-0.4-0.1-0.7-0.2-1.1-0.2C29.1,26.1,28.6,26.2,28.1,26.5"/>
			</defs>
			<clipPath id="SVGID_00000012445383578204227430000009295919128863690428_">
				<use xlink:href="#SVGID_00000075862117624819814500000011147948388874759610_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000141448165397680898680000017254912984326889653_" cx="-318.4126" cy="551.5278" r="0.4215" gradientTransform="matrix(75.5409 0 0 -75.5409 24100.7227 41710.6445)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF588"/>
				<stop  offset="0.107" style="stop-color:#E6D970"/>
				<stop  offset="0.1319" style="stop-color:#E0D26A"/>
				<stop  offset="0.1648" style="stop-color:#CEBC57"/>
				<stop  offset="0.2124" style="stop-color:#C4B14D"/>
				<stop  offset="0.2959" style="stop-color:#AB9332"/>
				<stop  offset="0.3022" style="stop-color:#A99130"/>
				<stop  offset="0.4022" style="stop-color:#AB922F"/>
				<stop  offset="0.4121" style="stop-color:#AC922F"/>
				<stop  offset="0.4401" style="stop-color:#B49D3A"/>
				<stop  offset="0.478" style="stop-color:#C5B34F"/>
				<stop  offset="0.4837" style="stop-color:#CDBD5C"/>
				<stop  offset="0.489" style="stop-color:#D0C060"/>
				<stop  offset="0.5374" style="stop-color:#D9CA6C"/>
				<stop  offset="0.6245" style="stop-color:#F1E58C"/>
				<stop  offset="0.6703" style="stop-color:#FFF69F"/>
				<stop  offset="0.6952" style="stop-color:#F0E484"/>
				<stop  offset="0.7486" style="stop-color:#CBB63F"/>
				<stop  offset="0.7527" style="stop-color:#C8B239"/>
				<stop  offset="0.7871" style="stop-color:#C3AC2E"/>
				<stop  offset="0.8297" style="stop-color:#BAA21A"/>
				<stop  offset="0.863" style="stop-color:#C1AA20"/>
				<stop  offset="0.9121" style="stop-color:#D3BE30"/>
				<stop  offset="0.9835" style="stop-color:#FFF072"/>
				<stop  offset="0.9954" style="stop-color:#FFF277"/>
				<stop  offset="1" style="stop-color:#FFF37B"/>
			</radialGradient>
			
				<rect x="23.2" y="26.1" style="clip-path:url(#SVGID_00000012445383578204227430000009295919128863690428_);fill:url(#SVGID_00000141448165397680898680000017254912984326889653_);" width="33.9" height="25.3"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000021817083653270243500000001833956254763990710_" d="M28.2,26.5c-1.8,1-3,3.4-4.9,2.4
					c2.1,5.4,10.7-1.4,15,11.7c-2.8-0.3-8.9-0.7-9.5,2.8c0,0.1,0,0.2,0,0.3c-0.4,4.6,3.1,5.7,2.6,7.6c4.2-1.3-0.3-7.9,6.6-7.8
					c0.2,0,0.4,0,0.5,0c4.1,0.3,4.6,4.4,2.8,5.4c1.7,0.1,5.4-0.9,6.5-4c1.2-3.4-2.3-6.5,2-9.1c0.2-0.1,0.4-0.2,0.5-0.3
					c2.4-1,5.4,0,6.7,1.4c-0.3-2.4-2.9-5.9-6.5-5.4c-4,0.5-3,5.1-6.2,6.4c-0.4,0.2-0.9,0.3-1.4,0.4c-5,0.6-5.4-6-9-9.7
					c-1-1-2-1.8-3.2-2.2c-0.4-0.1-0.8-0.2-1.1-0.2C29.1,26.1,28.6,26.3,28.2,26.5"/>
			</defs>
			<clipPath id="SVGID_00000005247615477802128730000011250996508354684580_">
				<use xlink:href="#SVGID_00000021817083653270243500000001833956254763990710_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000158024817193557529990000013663437006870221732_" cx="-318.4141" cy="551.5467" r="0.4219" gradientTransform="matrix(75.7349 0 0 -75.7349 24162.6816 41819.3477)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF589"/>
				<stop  offset="0.123" style="stop-color:#E8D970"/>
				<stop  offset="0.1319" style="stop-color:#E6D76E"/>
				<stop  offset="0.1648" style="stop-color:#D3C25C"/>
				<stop  offset="0.2099" style="stop-color:#CAB752"/>
				<stop  offset="0.2892" style="stop-color:#B29937"/>
				<stop  offset="0.3022" style="stop-color:#AD9432"/>
				<stop  offset="0.4121" style="stop-color:#AE9531"/>
				<stop  offset="0.444" style="stop-color:#B6A03B"/>
				<stop  offset="0.478" style="stop-color:#C3B14B"/>
				<stop  offset="0.4833" style="stop-color:#CBBA56"/>
				<stop  offset="0.489" style="stop-color:#CEBE5A"/>
				<stop  offset="0.5356" style="stop-color:#D7C866"/>
				<stop  offset="0.6196" style="stop-color:#EEE386"/>
				<stop  offset="0.6703" style="stop-color:#FFF69C"/>
				<stop  offset="0.7181" style="stop-color:#E2D165"/>
				<stop  offset="0.7527" style="stop-color:#CBB53B"/>
				<stop  offset="0.7855" style="stop-color:#C5AF30"/>
				<stop  offset="0.8297" style="stop-color:#BAA31A"/>
				<stop  offset="0.863" style="stop-color:#C1AB21"/>
				<stop  offset="0.9121" style="stop-color:#D4BF32"/>
				<stop  offset="0.9835" style="stop-color:#FFF171"/>
				<stop  offset="0.9962" style="stop-color:#FFF376"/>
				<stop  offset="1" style="stop-color:#FFF479"/>
			</radialGradient>
			
				<rect x="23.3" y="26.1" style="clip-path:url(#SVGID_00000005247615477802128730000011250996508354684580_);fill:url(#SVGID_00000158024817193557529990000013663437006870221732_);" width="33.8" height="25.2"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000141423022213763876640000015686084749188777104_" d="M28.2,26.5c-1.8,1-3,3.4-4.8,2.4
					c2.1,5.2,10.6-1.5,15,11.7c-2.8-0.2-8.9-0.7-9.5,2.7c0,0.1,0,0.2,0,0.3c-0.4,4.5,3,5.7,2.6,7.6c3.9-1.4-0.4-7.9,6.5-7.8
					c0.2,0,0.3,0,0.5,0c4.1,0.3,4.7,4.4,2.9,5.4c1.7,0,5.2-0.9,6.3-4.1c1.2-3.4-2.3-6.4,2-9.1c0.2-0.1,0.4-0.2,0.5-0.3
					c2.4-1,5.4-0.1,6.8,1.3c-0.5-2.4-2.9-5.7-6.5-5.2c-3.9,0.5-2.9,5-6.2,6.4c-0.4,0.2-0.9,0.3-1.4,0.4c-5,0.6-5.5-5.9-9-9.6
					c-1-1-2-1.8-3.2-2.3c-0.4-0.2-0.8-0.2-1.2-0.2C29.2,26.2,28.7,26.3,28.2,26.5"/>
			</defs>
			<clipPath id="SVGID_00000009559420259813084290000001661689146957046182_">
				<use xlink:href="#SVGID_00000141423022213763876640000015686084749188777104_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000088125299001661372390000015060515646578415503_" cx="-318.4294" cy="551.5513" r="0.4217" gradientTransform="matrix(75.9171 0 0 -75.9171 24221.877 41920.4883)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF589"/>
				<stop  offset="0.1319" style="stop-color:#EADC73"/>
				<stop  offset="0.1648" style="stop-color:#D8C861"/>
				<stop  offset="0.2078" style="stop-color:#CFBD57"/>
				<stop  offset="0.2832" style="stop-color:#B69F3D"/>
				<stop  offset="0.3022" style="stop-color:#AF9735"/>
				<stop  offset="0.4107" style="stop-color:#B09733"/>
				<stop  offset="0.4121" style="stop-color:#B09733"/>
				<stop  offset="0.448" style="stop-color:#B8A23C"/>
				<stop  offset="0.478" style="stop-color:#C1AF47"/>
				<stop  offset="0.4828" style="stop-color:#C8B750"/>
				<stop  offset="0.489" style="stop-color:#CCBB54"/>
				<stop  offset="0.534" style="stop-color:#D5C560"/>
				<stop  offset="0.6151" style="stop-color:#ECE080"/>
				<stop  offset="0.6703" style="stop-color:#FFF699"/>
				<stop  offset="0.7527" style="stop-color:#CDB83D"/>
				<stop  offset="0.7855" style="stop-color:#C7B132"/>
				<stop  offset="0.8297" style="stop-color:#BBA41C"/>
				<stop  offset="0.8622" style="stop-color:#C2AC22"/>
				<stop  offset="0.9116" style="stop-color:#D6C133"/>
				<stop  offset="0.9121" style="stop-color:#D6C133"/>
				<stop  offset="0.9835" style="stop-color:#FFF272"/>
				<stop  offset="1" style="stop-color:#FFF476"/>
			</radialGradient>
			
				<rect x="23.4" y="26.2" style="clip-path:url(#SVGID_00000009559420259813084290000001661689146957046182_);fill:url(#SVGID_00000088125299001661372390000015060515646578415503_);" width="33.7" height="25.1"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000016032825989782614360000009142421824784788108_" d="M28.3,26.6c-1.8,0.9-3,3.4-4.8,2.5
					c2,5,10.5-1.5,14.9,11.7c-2.7-0.2-8.9-0.6-9.5,2.7c0,0.1,0,0.2,0,0.3c-0.4,4.5,2.9,5.6,2.7,7.6c3.7-1.4-0.4-7.9,6.5-7.8
					c0.2,0,0.3,0,0.5,0c4.1,0.3,4.7,4.4,3,5.4c1.7,0,5-1,6.1-4.1c1.2-3.4-2.3-6.4,2-9c0.2-0.1,0.3-0.2,0.5-0.3
					c2.3-1,5.4-0.2,6.8,1.2c-0.6-2.3-2.9-5.5-6.5-5.1c-3.9,0.5-2.9,5-6.1,6.4c-0.4,0.2-0.9,0.3-1.4,0.4c-5,0.7-5.6-5.8-9-9.6
					c-0.9-1-1.9-1.8-3.1-2.3c-0.4-0.2-0.8-0.3-1.2-0.3C29.2,26.2,28.7,26.3,28.3,26.6"/>
			</defs>
			<clipPath id="SVGID_00000109026355681693479410000003396698577731630243_">
				<use xlink:href="#SVGID_00000016032825989782614360000009142421824784788108_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000153689379313257780760000013298644610441174163_" cx="-318.4345" cy="551.5664" r="0.4219" gradientTransform="matrix(76.0952 0 0 -76.0952 24279.0781 42020.1055)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF68A"/>
				<stop  offset="0.1319" style="stop-color:#F0E278"/>
				<stop  offset="0.1648" style="stop-color:#DECE65"/>
				<stop  offset="0.2058" style="stop-color:#D4C35B"/>
				<stop  offset="0.2778" style="stop-color:#BBA640"/>
				<stop  offset="0.3022" style="stop-color:#B19A36"/>
				<stop  offset="0.4121" style="stop-color:#B29A35"/>
				<stop  offset="0.4552" style="stop-color:#BAA53D"/>
				<stop  offset="0.478" style="stop-color:#BFAD42"/>
				<stop  offset="0.4826" style="stop-color:#C5B44A"/>
				<stop  offset="0.489" style="stop-color:#C9B84E"/>
				<stop  offset="0.532" style="stop-color:#D2C25A"/>
				<stop  offset="0.6095" style="stop-color:#E9DD7A"/>
				<stop  offset="0.6703" style="stop-color:#FFF697"/>
				<stop  offset="0.7013" style="stop-color:#ECDF75"/>
				<stop  offset="0.7527" style="stop-color:#CFBB3F"/>
				<stop  offset="0.7847" style="stop-color:#C9B434"/>
				<stop  offset="0.8297" style="stop-color:#BCA61D"/>
				<stop  offset="0.8622" style="stop-color:#C3AE24"/>
				<stop  offset="0.9116" style="stop-color:#D7C336"/>
				<stop  offset="0.9121" style="stop-color:#D7C336"/>
				<stop  offset="0.9835" style="stop-color:#FFF371"/>
				<stop  offset="1" style="stop-color:#FFF475"/>
			</radialGradient>
			
				<rect x="23.5" y="26.2" style="clip-path:url(#SVGID_00000109026355681693479410000003396698577731630243_);fill:url(#SVGID_00000153689379313257780760000013298644610441174163_);" width="33.6" height="25"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000004507934921320311260000003781434972639700641_" d="M28.4,26.6c-1.8,0.9-3,3.4-4.8,2.5
					c2,4.7,10.5-1.6,14.9,11.6c-2.7-0.1-8.9-0.6-9.4,2.6c0,0.1,0,0.2,0,0.3c-0.4,4.5,2.8,5.6,2.7,7.6c3.4-1.4-0.5-7.9,6.4-7.8
					c0.2,0,0.3,0,0.5,0c4,0.3,4.7,4.4,3.2,5.4c1.7-0.1,4.9-1.1,5.9-4.1c1.2-3.4-2.3-6.3,2-9c0.2-0.1,0.3-0.2,0.5-0.3
					c2.2-1,5.4-0.3,6.8,1.1c-0.7-2.2-2.9-5.4-6.5-4.9c-3.8,0.5-2.8,5-6.1,6.4c-0.4,0.2-0.9,0.3-1.4,0.4c-5,0.7-5.6-5.6-9-9.5
					c-0.9-1-1.9-1.9-3.1-2.4c-0.5-0.2-0.9-0.3-1.3-0.3C29.2,26.2,28.8,26.4,28.4,26.6"/>
			</defs>
			<clipPath id="SVGID_00000148650869503162712140000017042020016686679231_">
				<use xlink:href="#SVGID_00000004507934921320311260000003781434972639700641_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000021805525331892630070000009373361902524008370_" cx="-318.4415" cy="551.579" r="0.4216" gradientTransform="matrix(76.2726 0 0 -76.2726 24336.1484 42119.1836)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF68A"/>
				<stop  offset="0.1319" style="stop-color:#F4E77D"/>
				<stop  offset="0.1648" style="stop-color:#E4D46A"/>
				<stop  offset="0.2041" style="stop-color:#DAC960"/>
				<stop  offset="0.273" style="stop-color:#C1AC45"/>
				<stop  offset="0.3022" style="stop-color:#B49D38"/>
				<stop  offset="0.4121" style="stop-color:#B59E37"/>
				<stop  offset="0.4701" style="stop-color:#BCA93D"/>
				<stop  offset="0.478" style="stop-color:#BDAB3E"/>
				<stop  offset="0.4823" style="stop-color:#C3B145"/>
				<stop  offset="0.489" style="stop-color:#C7B549"/>
				<stop  offset="0.5307" style="stop-color:#D0BF55"/>
				<stop  offset="0.6057" style="stop-color:#E7DA75"/>
				<stop  offset="0.6703" style="stop-color:#FFF695"/>
				<stop  offset="0.723" style="stop-color:#E1D05D"/>
				<stop  offset="0.7527" style="stop-color:#D2BE42"/>
				<stop  offset="0.7827" style="stop-color:#CCB737"/>
				<stop  offset="0.8297" style="stop-color:#BDA71D"/>
				<stop  offset="0.8622" style="stop-color:#C4AF24"/>
				<stop  offset="0.9116" style="stop-color:#D7C438"/>
				<stop  offset="0.9121" style="stop-color:#D7C438"/>
				<stop  offset="0.9835" style="stop-color:#FFF370"/>
				<stop  offset="1" style="stop-color:#FFF473"/>
			</radialGradient>
			
				<rect x="23.5" y="26.2" style="clip-path:url(#SVGID_00000148650869503162712140000017042020016686679231_);fill:url(#SVGID_00000021805525331892630070000009373361902524008370_);" width="33.5" height="24.9"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000160875316527321090950000017385046752487216789_" d="M28.4,26.6c-1.8,0.9-3,3.3-4.8,2.6
					c1.9,4.5,10.4-1.6,14.8,11.6c-2.7-0.1-8.8-0.6-9.4,2.6c0,0.1,0,0.2,0,0.3c-0.4,4.5,2.7,5.6,2.7,7.5c3.2-1.4-0.5-7.9,6.4-7.7
					c0.2,0,0.3,0,0.5,0c4,0.3,4.7,4.4,3.3,5.4c1.7-0.1,4.7-1.2,5.7-4.1c1.2-3.4-2.3-6.3,2-9c0.2-0.1,0.3-0.2,0.5-0.3
					c2.2-1,5.4-0.4,6.9,1c-0.8-2.1-2.9-5.2-6.4-4.8c-3.8,0.5-2.8,4.9-6,6.4c-0.4,0.2-0.9,0.3-1.4,0.4c-5,0.7-5.7-5.5-9-9.4
					c-0.9-1-1.8-1.9-3-2.4c-0.5-0.2-0.9-0.3-1.3-0.3C29.3,26.3,28.8,26.4,28.4,26.6"/>
			</defs>
			<clipPath id="SVGID_00000086685068211076535740000009687377355803697047_">
				<use xlink:href="#SVGID_00000160875316527321090950000017385046752487216789_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000168816251385442309150000013229495728432301212_" cx="-318.4448" cy="551.5972" r="0.4212" gradientTransform="matrix(76.459 0 0 -76.459 24395.8105 42223.6484)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF68B"/>
				<stop  offset="0.1319" style="stop-color:#FAED82"/>
				<stop  offset="0.1648" style="stop-color:#E9DA70"/>
				<stop  offset="0.2025" style="stop-color:#DFCF66"/>
				<stop  offset="0.2686" style="stop-color:#C6B24B"/>
				<stop  offset="0.3022" style="stop-color:#B7A03B"/>
				<stop  offset="0.4121" style="stop-color:#B8A03A"/>
				<stop  offset="0.478" style="stop-color:#BBA939"/>
				<stop  offset="0.4823" style="stop-color:#C0AE40"/>
				<stop  offset="0.489" style="stop-color:#C4B244"/>
				<stop  offset="0.5298" style="stop-color:#CDBC50"/>
				<stop  offset="0.6033" style="stop-color:#E5D870"/>
				<stop  offset="0.6703" style="stop-color:#FFF692"/>
				<stop  offset="0.6834" style="stop-color:#F7ED84"/>
				<stop  offset="0.7296" style="stop-color:#DFCE56"/>
				<stop  offset="0.7527" style="stop-color:#D5C244"/>
				<stop  offset="0.7827" style="stop-color:#CEBB39"/>
				<stop  offset="0.8297" style="stop-color:#BDA91F"/>
				<stop  offset="0.8622" style="stop-color:#C4B126"/>
				<stop  offset="0.9116" style="stop-color:#D9C63B"/>
				<stop  offset="0.9121" style="stop-color:#D9C63B"/>
				<stop  offset="0.9835" style="stop-color:#FFF470"/>
				<stop  offset="1" style="stop-color:#FFF472"/>
			</radialGradient>
			
				<rect x="23.6" y="26.3" style="clip-path:url(#SVGID_00000086685068211076535740000009687377355803697047_);fill:url(#SVGID_00000168816251385442309150000013229495728432301212_);" width="33.3" height="24.8"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000037663771741862280070000016625222312222712478_" d="M28.5,26.6c-1.8,0.9-3.1,3.3-4.8,2.6
					c1.9,4.3,10.4-1.6,14.8,11.6c-2.7-0.1-8.8-0.6-9.4,2.5c0,0.1,0,0.2,0,0.3c-0.4,4.4,2.6,5.6,2.7,7.5c3-1.5-0.6-7.9,6.3-7.7
					c0.2,0,0.3,0,0.5,0c4,0.3,4.7,4.4,3.5,5.5c1.6-0.1,4.5-1.3,5.5-4.1c1.1-3.4-2.3-6.3,2-9c0.2-0.1,0.3-0.2,0.5-0.3
					c2.1-0.9,5.4-0.5,6.9,0.9c-0.9-2.1-2.9-5-6.4-4.6c-3.7,0.5-2.7,4.9-6,6.4c-0.4,0.2-0.9,0.3-1.4,0.4c-5,0.8-5.8-5.4-9-9.3
					c-0.8-1-1.8-1.9-3-2.5c-0.5-0.2-0.9-0.3-1.4-0.3C29.3,26.3,28.9,26.4,28.5,26.6"/>
			</defs>
			<clipPath id="SVGID_00000154422249134869203580000009208574438714624923_">
				<use xlink:href="#SVGID_00000037663771741862280070000016625222312222712478_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000086651074385127177070000010813093171575170221_" cx="-318.4575" cy="551.6052" r="0.4216" gradientTransform="matrix(76.6604 0 0 -76.6604 24461.0195 42335.5977)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF68B"/>
				<stop  offset="0.1319" style="stop-color:#FFF287"/>
				<stop  offset="0.1648" style="stop-color:#EFE176"/>
				<stop  offset="0.2006" style="stop-color:#E6D66C"/>
				<stop  offset="0.2633" style="stop-color:#CCB951"/>
				<stop  offset="0.3022" style="stop-color:#BAA33D"/>
				<stop  offset="0.4107" style="stop-color:#B9A33B"/>
				<stop  offset="0.4121" style="stop-color:#B9A33B"/>
				<stop  offset="0.478" style="stop-color:#BAA734"/>
				<stop  offset="0.4819" style="stop-color:#BEAC3A"/>
				<stop  offset="0.489" style="stop-color:#C2B03E"/>
				<stop  offset="0.5286" style="stop-color:#CBBA4A"/>
				<stop  offset="0.5999" style="stop-color:#E3D66A"/>
				<stop  offset="0.6703" style="stop-color:#FFF68F"/>
				<stop  offset="0.6942" style="stop-color:#F1E576"/>
				<stop  offset="0.7322" style="stop-color:#DECE53"/>
				<stop  offset="0.7527" style="stop-color:#D7C546"/>
				<stop  offset="0.7809" style="stop-color:#D0BE3B"/>
				<stop  offset="0.8297" style="stop-color:#BFAA1E"/>
				<stop  offset="0.8614" style="stop-color:#C6B126"/>
				<stop  offset="0.9097" style="stop-color:#D9C63B"/>
				<stop  offset="0.9121" style="stop-color:#DAC73C"/>
				<stop  offset="0.9835" style="stop-color:#FFF46F"/>
				<stop  offset="1" style="stop-color:#FFF470"/>
			</radialGradient>
			
				<rect x="23.7" y="26.3" style="clip-path:url(#SVGID_00000154422249134869203580000009208574438714624923_);fill:url(#SVGID_00000086651074385127177070000010813093171575170221_);" width="33.2" height="24.7"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000027569431254193147740000007368326582151350960_" d="M26.9,27.9c-1,0.9-2,1.8-3.1,1.4
					c1.8,4.1,10.3-1.7,14.8,11.5c-2.7,0-9.2-0.7-9.5,2.7c-0.4,4.4,2.5,5.5,2.7,7.5c2.8-1.5-0.8-8.2,6.7-7.7c3.9,0.3,4.8,4.4,3.6,5.5
					c1.6-0.2,4.3-1.4,5.3-4.2c1.1-3.3-2.3-6.2,1.9-9c1.9-1.3,5.7-1.1,7.5,0.5c-1.1-2-3-4.9-6.4-4.5c-4.1,0.5-2.4,6-7.4,6.8
					c-6.2,1-6-8.9-11.9-11.8c-0.5-0.2-1-0.4-1.4-0.4C28.6,26.4,27.7,27.1,26.9,27.9"/>
			</defs>
			<clipPath id="SVGID_00000109727715367995965270000012014076680703900329_">
				<use xlink:href="#SVGID_00000027569431254193147740000007368326582151350960_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000064319151665558811070000006595236509522577545_" cx="-318.458" cy="551.6271" r="0.4212" gradientTransform="matrix(76.8844 0 0 -76.8844 24532.457 42461.0859)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFF68C"/>
				<stop  offset="0.1319" style="stop-color:#FFF68C"/>
				<stop  offset="0.3022" style="stop-color:#BCA63F"/>
				<stop  offset="0.4165" style="stop-color:#BCA63D"/>
				<stop  offset="0.4576" style="stop-color:#BAA536"/>
				<stop  offset="0.478" style="stop-color:#B8A52F"/>
				<stop  offset="0.5059" style="stop-color:#CABA47"/>
				<stop  offset="0.5492" style="stop-color:#E1D465"/>
				<stop  offset="0.5917" style="stop-color:#F2E77B"/>
				<stop  offset="0.6326" style="stop-color:#FCF288"/>
				<stop  offset="0.6703" style="stop-color:#FFF68C"/>
				<stop  offset="0.6985" style="stop-color:#F1E574"/>
				<stop  offset="0.7576" style="stop-color:#D6C647"/>
				<stop  offset="0.8033" style="stop-color:#C5B22A"/>
				<stop  offset="0.8297" style="stop-color:#BFAB20"/>
				<stop  offset="0.8597" style="stop-color:#C5B228"/>
				<stop  offset="0.9056" style="stop-color:#D6C63D"/>
				<stop  offset="0.9613" style="stop-color:#F2E55F"/>
				<stop  offset="0.9835" style="stop-color:#FFF46F"/>
				<stop  offset="1" style="stop-color:#FFF46F"/>
			</radialGradient>
			
				<rect x="23.8" y="26.4" style="clip-path:url(#SVGID_00000109727715367995965270000012014076680703900329_);fill:url(#SVGID_00000064319151665558811070000006595236509522577545_);" width="33.1" height="24.7"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000116924161279847828910000007797976476058824107_" d="M41.1,21.3c-2.6,0.3-4.5,2.8-4.2,5.4
					c0.3,2.6,2.8,4.5,5.4,4.2c2.6-0.3,4.5-2.8,4.2-5.4c-0.3-2.4-2.4-4.2-4.8-4.2C41.5,21.3,41.3,21.3,41.1,21.3"/>
			</defs>
			<clipPath id="SVGID_00000054955003221372177140000016298740290123741883_">
				<use xlink:href="#SVGID_00000116924161279847828910000007797976476058824107_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000012447065672398493030000009705126019173166994_" cx="-311.3975" cy="536.3903" r="0.4216" gradientTransform="matrix(17.0282 -2.2417 -2.2417 -17.0282 6545.3066 8459.4502)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FFFDED"/>
				<stop  offset="6.324048e-02" style="stop-color:#FFFDE9"/>
				<stop  offset="0.1242" style="stop-color:#FFFBDE"/>
				<stop  offset="0.1374" style="stop-color:#FFFBDA"/>
				<stop  offset="0.1804" style="stop-color:#F8F3C3"/>
				<stop  offset="0.3414" style="stop-color:#E0D570"/>
				<stop  offset="0.4735" style="stop-color:#CEBF34"/>
				<stop  offset="0.57" style="stop-color:#C3B10E"/>
				<stop  offset="0.6209" style="stop-color:#BFAC00"/>
				<stop  offset="1" style="stop-color:#91741D"/>
			</radialGradient>
			
				<polygon style="clip-path:url(#SVGID_00000054955003221372177140000016298740290123741883_);fill:url(#SVGID_00000012447065672398493030000009705126019173166994_);" points="
				35.3,21.4 46.7,19.9 48.2,31 36.8,32.5 			"/>
		</g>
		<g>
			<defs>
				<path id="SVGID_00000027590190264377238820000000894823145258190991_" d="M51.6,43c-2.6,0.3-4.5,2.8-4.2,5.4
					c0.3,2.6,2.8,4.5,5.4,4.2c2.6-0.3,4.5-2.8,4.2-5.4c-0.3-2.4-2.4-4.2-4.8-4.2C52,43,51.8,43,51.6,43"/>
			</defs>
			<clipPath id="SVGID_00000137133442660153260700000015954186868175320448_">
				<use xlink:href="#SVGID_00000027590190264377238820000000894823145258190991_"  style="overflow:visible;"/>
			</clipPath>
			
				<radialGradient id="SVGID_00000093867428913454559370000006670900962013106865_" cx="-311.9853" cy="538.2108" r="0.4215" gradientTransform="matrix(17.028 -2.2417 -2.2417 -17.028 6569.8096 8510.7363)" gradientUnits="userSpaceOnUse">
				<stop  offset="0" style="stop-color:#FDFDFD"/>
				<stop  offset="0.1374" style="stop-color:#FCFCFC"/>
				<stop  offset="0.3424" style="stop-color:#CACBCC"/>
				<stop  offset="0.513" style="stop-color:#A6A9AB"/>
				<stop  offset="0.6044" style="stop-color:#999C9E"/>
				<stop  offset="1" style="stop-color:#565657"/>
			</radialGradient>
			
				<polygon style="clip-path:url(#SVGID_00000137133442660153260700000015954186868175320448_);fill:url(#SVGID_00000093867428913454559370000006670900962013106865_);" points="
				45.8,43.1 57.2,41.6 58.6,52.8 47.3,54.3 			"/>
		</g>
		<g>
			<g>
				<defs>
					<rect id="SVGID_00000176001811203455597100000008703809949562776246_" x="36.9" y="21.3" width="9.7" height="9.7"/>
				</defs>
				<clipPath id="SVGID_00000027580172013093326870000003447562251953084579_">
					<use xlink:href="#SVGID_00000176001811203455597100000008703809949562776246_"  style="overflow:visible;"/>
				</clipPath>
			</g>
		</g>
		<g class="st198">
			<g>
				<defs>
					<rect id="SVGID_00000069387597079918568570000014615638107915691149_" x="36.9" y="21.3" width="9.7" height="9.7"/>
				</defs>
				<clipPath id="SVGID_00000090289475050882483320000012761120061843924370_">
					<use xlink:href="#SVGID_00000069387597079918568570000014615638107915691149_"  style="overflow:visible;"/>
				</clipPath>
				<g style="clip-path:url(#SVGID_00000090289475050882483320000012761120061843924370_);">
					<defs>
						<path id="SVGID_00000085211372834451279230000006604683231969911692_" d="M46.5,25.5c0.3,2.6-1.5,5.1-4.2,5.4
							c-2.6,0.3-5.1-1.5-5.4-4.2c-0.3-2.6,1.5-5.1,4.2-5.4C43.8,20.9,46.2,22.8,46.5,25.5"/>
					</defs>
					<clipPath id="SVGID_00000050646155001314470580000010867758695859020466_">
						<use xlink:href="#SVGID_00000085211372834451279230000006604683231969911692_"  style="overflow:visible;"/>
					</clipPath>
					
						<radialGradient id="SVGID_00000125580225447398429400000016996450983913905296_" cx="-311.4232" cy="536.5296" r="0.4216" gradientTransform="matrix(17.1261 -2.2546 -2.2546 -17.1261 6583.6177 8510.2207)" gradientUnits="userSpaceOnUse">
						<stop  offset="0" style="stop-color:#FFFDED"/>
						<stop  offset="0.1142" style="stop-color:#FFFBDE"/>
						<stop  offset="0.1374" style="stop-color:#FFFBDA"/>
						<stop  offset="0.2022" style="stop-color:#FEFAD6"/>
						<stop  offset="0.2664" style="stop-color:#FCF6CA"/>
						<stop  offset="0.3303" style="stop-color:#F7F0B6"/>
						<stop  offset="0.3942" style="stop-color:#F2E79A"/>
						<stop  offset="0.458" style="stop-color:#EADC76"/>
						<stop  offset="0.5219" style="stop-color:#E1CF4A"/>
						<stop  offset="0.5845" style="stop-color:#D6BF17"/>
						<stop  offset="0.6099" style="stop-color:#D1B800"/>
						<stop  offset="0.7409" style="stop-color:#BBA700"/>
						<stop  offset="0.8462" style="stop-color:#A79600"/>
						<stop  offset="1" style="stop-color:#918300"/>
					</radialGradient>
					
						<polygon style="clip-path:url(#SVGID_00000050646155001314470580000010867758695859020466_);fill:url(#SVGID_00000125580225447398429400000016996450983913905296_);" points="
						35.3,21.1 46.7,19.6 48.2,31 36.8,32.5 					"/>
				</g>
			</g>
		</g>
		<g class="st201">
			<g>
				<defs>
					<rect id="SVGID_00000163777014373191386190000013858964307178367641_" x="47.4" y="43" width="9.7" height="9.7"/>
				</defs>
				<clipPath id="SVGID_00000148654892521808141620000004921752939570355640_">
					<use xlink:href="#SVGID_00000163777014373191386190000013858964307178367641_"  style="overflow:visible;"/>
				</clipPath>
				<g style="clip-path:url(#SVGID_00000148654892521808141620000004921752939570355640_);">
					<defs>
						<path id="SVGID_00000138564853670792419210000000928445047743620480_" d="M57,47.2c0.3,2.6-1.5,5.1-4.2,5.4
							c-2.6,0.3-5.1-1.5-5.4-4.2c-0.3-2.6,1.5-5.1,4.2-5.4C54.2,42.7,56.7,44.5,57,47.2"/>
					</defs>
					<clipPath id="SVGID_00000095338567461219354030000016828673255750110083_">
						<use xlink:href="#SVGID_00000138564853670792419210000000928445047743620480_"  style="overflow:visible;"/>
					</clipPath>
					
						<radialGradient id="SVGID_00000086650044257091240570000015236056141293319058_" cx="-311.9663" cy="538.252" r="0.4215" gradientTransform="matrix(17.0274 -2.2416 -2.2416 -17.0274 6569.3369 8511.1924)" gradientUnits="userSpaceOnUse">
						<stop  offset="0" style="stop-color:#FFFFFF"/>
						<stop  offset="0.1538" style="stop-color:#EEEEEF"/>
						<stop  offset="0.2554" style="stop-color:#E0E1E2"/>
						<stop  offset="0.4458" style="stop-color:#BCBEC0"/>
						<stop  offset="0.6099" style="stop-color:#989C9E"/>
						<stop  offset="0.7939" style="stop-color:#7D8285"/>
						<stop  offset="0.8462" style="stop-color:#757A7D"/>
						<stop  offset="1" style="stop-color:#525557"/>
					</radialGradient>
					
						<polygon style="clip-path:url(#SVGID_00000095338567461219354030000016828673255750110083_);fill:url(#SVGID_00000086650044257091240570000015236056141293319058_);" points="
						45.8,42.8 57.2,41.3 58.7,52.8 47.3,54.3 					"/>
				</g>
			</g>
		</g>
	</g>
</g>
</svg>
