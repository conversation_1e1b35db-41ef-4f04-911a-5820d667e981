# Generated by Django 3.1.7 on 2021-03-29 19:54

from django.db import migrations
import modelcluster.fields


class Migration(migrations.Migration):
    dependencies = [
        ('home', '0009_auto_20210329_1936'),
    ]

    operations = [
        migrations.AlterField(
            model_name='newsarticle',
            name='categories',
            field=modelcluster.fields.ParentalManyToManyField(
                blank=True, related_name='news_articles', to='home.NewsCategory'
            ),
        ),
        migrations.AlterField(
            model_name='newsarticle',
            name='clubs',
            field=modelcluster.fields.ParentalManyToManyField(blank=True, related_name='news_articles', to='home.Club'),
        ),
    ]
