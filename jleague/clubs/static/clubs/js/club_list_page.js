(function () {
  const navContainer = (function () {
    const containerEl = document.querySelector('#nav-container');
    const navEls = containerEl && containerEl.querySelectorAll('a');

    function init() {
      navEls.forEach((el) => {
        el.addEventListener('click', (event) => {
          const slug = el.getAttribute('data-slug');

          navEls.forEach((el1) => {
            if (el1.getAttribute('data-slug') === slug) el1.classList.add('active');
            else el1.classList.remove('active');
          });
        });
      });
    }

    return { init };
  })();

  window.addEventListener('load', () => {
    navContainer.init();
  });
})();
