<?xml version="1.0" encoding="utf-8"?>
<svg viewBox="0 0 400 128" xmlns="http://www.w3.org/2000/svg">
  <g transform="matrix(0.3184399902820587, 0, 0, 0.3184399902820587, -8.899558067321777, -150.6319122314453)">
    <path class="cls-3" d="M241.13,754.95s-4.36-5.76-6.69-10.67c-1.41,3.74-2.65,6.7-2.65,6.7l4.9,7.24,11.29,5.68-6.85-8.95Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <polygon class="cls-3" points="404.06 686.36 399.36 677.33 399.98 670.11 391.63 661.37 390.54 676.94 396.15 685.2 392.87 697.02 404.06 686.36" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <path class="cls-3" d="M419.47,704.67l-5.6,2.8-4.99-8.06s-1.14,7.91-1.41,10.74l3.91,7.9,10.05.84.71,6.62s3.43-5.91,4.81-14.62c-2.8-3.73-7.47-6.22-7.47-6.22" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <polygon class="cls-3" points="390.2 701.55 385.22 706.85 385.07 716.19 393.01 709.34 390.2 701.55" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <polygon class="cls-3" points="392.15 613.44 396.45 611.03 398.16 614.47 394.01 615.33 392.15 613.44" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <path class="cls-3" d="M394.57,587.8l.77-3.48-5.25-1.17s-.82,3.74-1.87,7c2.57-.82,6.28-2.32,6.28-2.32h.06s0-.03,0-.03h.03s-.03,0-.03,0Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <path class="cls-3" d="M397.91,597.63l1.19-3.02-4.46-4.22,1.46,12.18-5.67,7.44s2.8-.23,6.19-.7c1.75-1.17,4.89-5.94,4.89-5.94l-3.61-5.74Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <polygon class="cls-3" points="387.74 616.75 392.97 618.01 392.13 626.29 387.74 616.75" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <polygon class="cls-3" points="388.51 632.24 390.95 631.73 390.42 634.31 388.08 634.76 388.51 632.24" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <path class="cls-3" d="M367.16,773.79l-10.89,16.58c.71-.24,1.41-.51,2.11-.76l3.87-1.48c7.79-3.14,14.95-6.99,21.11-11.48,14.35-10.47,25.34-23.87,34.61-42.18.34-.66.69-1.32,1.04-1.98l.21-.65-16.07,17.24-3.52-3.95s5.82-8.72,7.07-12.77c-2.18-2.18-5.91-3.74-5.91-3.74l-11.52,8.81-6.41-3.24-6.66,13.11s-12.46,6.23-17.44,8.41l-6.94,19.07,13.48-3.1,1.87,2.09Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <path class="cls-3" d="M521.8,486c-14.92,0-26.59.84-41.24.84s-31.34-.84-47.35-.84c-5.68,0-7.78,1.86-7.82,5.37-.05,3.89,3.26,5.08,7.32,5.08,3.24,0,12.03-1.05,15.56,4.01,4.61,6.59-1.86,31.42-7,53.3-1.73,7.62-5.29,21.74-9.8,39.2,12.6,20.79,19.85,45.18,19.85,71.26,0,2.84-.12,5.66-.28,8.46,20.69-71.07,40.01-137.4,40.01-137.4,5.1-17.99,10.18-29.59,15.91-34.45,4.5-3.41,12.45-4.15,16.28-4.38,4.05-.24,6.63-1.89,6.67-5.23.05-3.55-2.42-5.23-8.1-5.23" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <path class="cls-3" d="M430.86,728.35c-3.51-.89-6.39,3.3-8.86,8.17-8.91,17.6-20.1,32.2-35.98,43.8-6.53,4.77-14.98,9.39-24.94,13.14-1.53.56-3.07,1.12-4.63,1.63,0,0,0,0,0,.01-12.4,4.12-26.88,6.82-42.84,6.82-34.62,0-66.23-12.8-90.42-33.9-1.43-1.25-2.84-2.53-4.22-3.84-.09-.08-.18-.17-.27-.27-1.3-1.23-2.58-2.49-3.83-3.78-.21-.21-.41-.43-.61-.65-1.16-1.2-2.3-2.42-3.41-3.67-.28-.31-.54-.63-.82-.95-1.07-1.22-2.12-2.44-3.15-3.69-5.81-7.12-10.91-14.85-15.22-23.06-1.14-2.18-2.22-4.39-3.24-6.63-.16-.35-.32-.69-.47-1.04-.91-2.01-1.76-4.05-2.56-6.11-.11-.3-.23-.59-.35-.89-.45-1.17-.88-2.34-1.3-3.52-.05-.12-.08-.25-.12-.36-.77-2.21-1.48-4.45-2.14-6.7-.08-.3-.17-.6-.25-.89l2.73,2.4s6.46,13.7,7.48,15.86c3.01,6.41,7.77,13.72,11.74,19.45,9.91,14.24,22.67,26.26,37.31,35.77l-10.93-14.1-6.54-13.55,3.74-6.15-6.08-10.9-8.25-11.13,2.53-18.28,4.64-9.44c.56-10.94,1.24-17.44,3.12-25.22,0,0-8.8,2.42-18.14,6.85-4.67-17.98-4.2-29.19-4.2-29.19l4.9-16.81s1.87,22.65,7.93,31.06c5.14-2.18,13.86-5.29,13.86-5.29l9.34-24.91-8.41,2.18-5.6-9.65,4.05-13.7s2.95,11.68,4.28,12.38c2.25-1.17,5.84-1.39,5.84-1.39l2.34,5.21s5.37-.78,10.58-1.24c2.49,5.29,6.85,11.52,6.85,11.52l7.47-.62,10.03,27.49c5.2,0,9.57,0,12.11.01v-77.83h92.61l-5.61-6.81-1.09-9.18-15.26-11.83,2.18-6.54s5.88,5.06,10.55,9.11c4.67,1.06,12.49,3.04,12.49,3.04l-11.83-10.58-.93-5.61-9.65-1.55-10.28-2.18c-2.55-.36-8.93-1.43-10.58-1.56-8.41-3.74-18.69-5.91-18.69-5.91l-21.48.3-14.48,3.59s-4.18-.27-5.12-.89c-7.01,1.24-17.28,5.23-17.28,5.23l-12.47,1.3c19.27-10.27,41.26-16.11,64.62-16.11s43.97,5.19,59.67,14.53c16.3,9.7,26.38,25.52,29.76,44.42.74,4.14,2.05,8.33,5.18,8.33,2.71,0,4.86-1.9,4.75-6.98-.14-6.26-.58-32.55-.8-46.31-.32-15.01.46-18.46.29-21.79-.16-3.27-1.43-5.87-7.54-6.85-12.61-2.02-26.91-6.87-41.27-10.54-19.03-4.86-36.76-6.9-50.05-6.9-66.11,0-118.05,17.14-152.31,49.4-.05.03-.08.06-.13.1-.02.03-.04.05-.06.07-1.6,1.52-3.18,3.05-4.7,4.63-1.56,1.24-3.01,2.01-4.11,1.87-3.01-.39-4.22-2.32-5.23-5.94-3.74-16.76-6.41-27.07-6.52-32.51-.12-5.63.62-9.18,4.01-12.25,3.96-3.59,12.54-3.37,16.1-3.37,4.45,0,8.13-1.19,8.24-5.08.1-3.51-2.11-5.36-8.35-5.36-25.81,0-47.14.84-63.21.84s-37.17-.84-60.31-.84c-6.24,0-9,1.67-9.11,5.22-.1,3.34,2.66,4.99,7.09,5.23,4.2.23,12.88.97,17.67,4.38,6.09,4.86,10.32,14.07,15.89,33.78,5.36,19,67.66,242.75,85.38,300.13,6.54,21.18,11.35,26.59,18.81,26.59s13.19-11.43,23.72-33.25c.85-1.76,2.09-4.44,3.64-7.86,1.47-2.93,2.59-4.76,4.23-5.41,1.72-.68,3.19-.04,6.22,1.06,28.73,11.93,63.32,18.12,103.03,18.12,13.48,0,28.43-1.52,43.25-4.94.1,0,.16.03.27,0,5.26-1.05,7.78-2.12,9.35-1.52.71.27,1.29,1,1.88,2.14.82,2.27,1.59,4.36,2.29,6.21,7.45,19.93,12.33,26.11,17.65,26.11s9.66-6.59,13.31-19.41c1.14-4.01,10.31-35.55,22.38-77.01,4.49-13.65,7.25-22.18,8.78-27.37,1.79-6.1,1.07-9.09-1.9-9.86M220.43,579.96l-1.17,5.76-8.1,3.43c2.49-7.16,8.1-12.45,8.1-12.45l6.22-2.49-5.06,5.76ZM240.43,580.12s-3.27,5.76-3.74,10.9c-4.67,1.17-5.29,1.55-5.29,1.55,0,0-2.34-7.63-2.18-9.65,2.95-5.92,9.42-12.38,9.42-12.38l7.39-1.64-6.54,7.98.94,3.23ZM236.9,549.91c3.14-1.06,5.95-1.55,5.95-1.55l-4.2,6.77s-11.91,4.2-15.53,5.6l-.34.06c4.46-3.92,9.18-7.56,14.13-10.89M196.13,592.55c4.5-3.9,8.81-7.45,8.81-7.45l-1.38-3.52c2.07-2.76,4.27-5.41,6.53-8.01l.76,7.18s-7.16,9.96-9.65,13.7c-.63,3.12-.32,3.43-.32,6.85,5.3-3.43,12.95-5.15,12.95-5.15l-5.74,10.88-5.89,2.97-4.42-4.29-9.7,1.98c2.36-5.23,5.07-10.27,8.04-15.13" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <path class="cls-3" d="M290.45,587.25v77.52c-.1.1-.19.19-.28.29h-57.51s0,15.89,0,15.89c0,40.82,33.1,73.92,73.92,73.92s73.93-33.1,73.93-73.92v-93.7h-90.05Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <path class="cls-1" d="M338.78,690.28c-12.58,0-22.77,10.19-22.77,22.76s10.19,22.77,22.77,22.77,22.77-10.19,22.77-22.77-10.19-22.76-22.77-22.76" style="fill: rgb(236, 28, 36); stroke-width: 0px;"/>
    <path class="cls-2" d="M306.58,658.07c-12.58,0-22.77,10.19-22.77,22.77s10.19,22.76,22.77,22.76,22.77-10.19,22.77-22.76-10.19-22.77-22.77-22.77" style="fill: rgb(255, 255, 255); stroke-width: 0px;"/>
  </g>
  <g transform="matrix(0.3184399902820587, 0, 0, 0.3184399902820587, -8.899558067321777, -150.6319122314453)">
    <path class="cls-3" d="M719.52,832.54h5.67v2.77h.08c1.42-2.31,3.78-3.36,6.46-3.36,6.81,0,9.87,5.5,9.87,11.67,0,5.8-3.19,11.21-9.53,11.21-2.6,0-5.08-1.14-6.51-3.28h-.08v10.33h-5.97v-29.36ZM735.65,843.46c0-3.44-1.39-7.01-5.21-7.01s-5.16,3.49-5.16,7.01,1.34,6.89,5.21,6.89,5.16-3.36,5.16-6.89Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <path class="cls-3" d="M755.88,831.95c6.85,0,11.26,4.53,11.26,11.46s-4.41,11.42-11.26,11.42-11.22-4.54-11.22-11.42,4.41-11.46,11.22-11.46ZM755.88,850.35c4.07,0,5.29-3.49,5.29-6.93s-1.22-6.97-5.29-6.97-5.25,3.49-5.25,6.97,1.22,6.93,5.25,6.93Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <path class="cls-3" d="M795.29,854.25h-6.13l-3.87-14.57h-.08l-3.69,14.57h-6.18l-6.88-21.71h6.3l3.99,14.74h.08l3.61-14.74h5.8l3.7,14.7h.08l3.99-14.7h6.13l-6.85,21.71Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <path class="cls-3" d="M809.24,844.85c.17,3.78,2.01,5.5,5.33,5.5,2.4,0,4.33-1.47,4.71-2.81h5.25c-1.68,5.12-5.25,7.31-10.16,7.31-6.85,0-11.09-4.71-11.09-11.42s4.49-11.46,11.09-11.46c7.39,0,10.96,6.21,10.54,12.89h-15.66ZM818.95,841.07c-.55-3.02-1.85-4.62-4.75-4.62-3.78,0-4.87,2.94-4.95,4.62h9.7Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <path class="cls-3" d="M828.63,832.54h5.67v4.03h.08c1.09-2.73,4.03-4.62,6.92-4.62.42,0,.93.08,1.3.21v5.54c-.54-.13-1.42-.21-2.14-.21-4.37,0-5.88,3.15-5.88,6.97v9.79h-5.97v-21.71Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <path class="cls-3" d="M850.2,844.85c.17,3.78,2.01,5.5,5.33,5.5,2.4,0,4.33-1.47,4.71-2.81h5.25c-1.68,5.12-5.25,7.31-10.16,7.31-6.85,0-11.09-4.71-11.09-11.42s4.49-11.46,11.09-11.46c7.39,0,10.96,6.21,10.54,12.89h-15.67ZM859.9,841.07c-.55-3.02-1.85-4.62-4.75-4.62-3.78,0-4.87,2.94-4.95,4.62h9.7Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <path class="cls-3" d="M885.09,851.48h-.08c-1.39,2.35-3.82,3.36-6.59,3.36-6.55,0-9.75-5.63-9.75-11.64s3.24-11.25,9.62-11.25c2.56,0,5.08,1.09,6.43,3.23h.08v-10.92h5.97v29.99h-5.67v-2.77ZM879.88,836.45c-3.91,0-5.25,3.36-5.25,6.93s1.55,6.97,5.25,6.97c3.94,0,5.12-3.45,5.12-7.01s-1.26-6.89-5.12-6.89Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <path class="cls-3" d="M904.79,824.27h5.97v10.92h.08c1.47-2.22,4.16-3.23,6.85-3.23,4.33,0,8.99,3.48,8.99,11.42s-4.66,11.47-8.99,11.47c-3.19,0-5.84-.97-7.14-3.36h-.08v2.77h-5.67v-29.99ZM915.62,836.45c-3.53,0-5.08,3.32-5.08,6.97s1.55,6.93,5.08,6.93,5.08-3.32,5.08-6.93-1.55-6.97-5.08-6.97Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <path class="cls-3" d="M940.78,856.98c-1.3,3.48-3.36,4.91-7.48,4.91-1.22,0-2.44-.08-3.65-.21v-4.91c1.13.08,2.31.25,3.49.21,2.05-.21,2.73-2.35,2.05-4.07l-7.64-20.37h6.39l4.91,14.87h.08l4.75-14.87h6.17l-9.07,24.44Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <path class="cls-3" d="M976.91,851.48h-.08c-1.39,2.35-3.82,3.36-6.59,3.36-6.55,0-9.74-5.63-9.74-11.64s3.23-11.25,9.61-11.25c2.56,0,5.08,1.09,6.43,3.23h.08v-10.92h5.96v29.99h-5.67v-2.77ZM971.7,836.45c-3.91,0-5.25,3.36-5.25,6.93s1.55,6.97,5.25,6.97c3.94,0,5.12-3.45,5.12-7.01s-1.26-6.89-5.12-6.89Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <path class="cls-3" d="M997.77,831.95c6.85,0,11.26,4.53,11.26,11.46s-4.41,11.42-11.26,11.42-11.22-4.54-11.22-11.42,4.41-11.46,11.22-11.46ZM997.77,850.35c4.07,0,5.29-3.49,5.29-6.93s-1.22-6.97-5.29-6.97-5.25,3.49-5.25,6.97,1.22,6.93,5.25,6.93Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <path class="cls-3" d="M1027.61,840.18c-.38-2.44-1.94-3.74-4.41-3.74-3.82,0-5.08,3.86-5.08,7.06s1.22,6.85,4.95,6.85c2.77,0,4.37-1.76,4.75-4.41h5.75c-.75,5.75-4.74,8.91-10.45,8.91-6.55,0-10.97-4.62-10.97-11.13s4.04-11.76,11.09-11.76c5.12,0,9.83,2.69,10.21,8.23h-5.84Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <path class="cls-3" d="M1047.56,831.95c6.85,0,11.26,4.53,11.26,11.46s-4.41,11.42-11.26,11.42-11.22-4.54-11.22-11.42,4.41-11.46,11.22-11.46ZM1047.56,850.35c4.07,0,5.29-3.49,5.29-6.93s-1.22-6.97-5.29-6.97-5.25,3.49-5.25,6.97,1.22,6.93,5.25,6.93Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <path class="cls-3" d="M1062.93,832.54h5.63v2.94h.08c1.55-2.22,3.82-3.53,6.64-3.53s5.16,1.01,6.34,3.61c1.26-1.89,3.52-3.61,6.55-3.61,4.62,0,7.94,2.14,7.94,7.77v14.53h-5.97v-12.31c0-2.9-.25-5.29-3.65-5.29s-3.99,2.77-3.99,5.5v12.1h-5.97v-12.18c0-2.52.17-5.42-3.61-5.42-1.17,0-4.03.76-4.03,5v12.6h-5.97v-21.71Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    <path class="cls-3" d="M1111.4,831.95c6.85,0,11.26,4.53,11.26,11.46s-4.41,11.42-11.26,11.42-11.22-4.54-11.22-11.42,4.41-11.46,11.22-11.46ZM1111.4,850.35c4.07,0,5.29-3.49,5.29-6.93s-1.22-6.97-5.29-6.97-5.25,3.49-5.25,6.97,1.22,6.93,5.25,6.93Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
  </g>
  <g transform="matrix(0.3184399902820587, 0, 0, 0.3184399902820587, -8.899558067321777, -150.6319122314453)">
    <g>
      <path class="cls-3" d="M960.78,525.2l-5.3,6.3c8.33,9.94,25.11,4.36,25.11-10.16v-24.93h5.83v-7.52h-19.88v7.52h5.84v24.67c.09,7.22-7.39,9.33-11.61,4.12Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <rect class="cls-3" x="985.91" y="526.06" width="7.85" height="7.59" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <polygon class="cls-3" points="1058.08 513.36 1058.08 526.06 1074.78 526.06 1074.78 518.37 1082.52 518.37 1082.52 533.65 1044.51 533.65 1044.51 526.06 1050.32 526.06 1050.32 496.41 1044.51 496.41 1044.51 488.89 1082.52 488.89 1082.52 502.3 1074.78 502.3 1074.78 496.41 1058.08 496.41 1058.08 506.1 1070.91 506.1 1070.91 513.36 1058.08 513.36" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-3" d="M1174.66,501.77c-3.23-3.51-6.74-5.96-11.85-5.96-8.89,0-15.89,6.28-15.89,15.4s6.79,15.51,15.71,15.51c6.71,0,11.76-3.8,13.85-9.58h-14.23v-7.82h23.32c.02.67.03,1.08.03,1.43,0,14.76-10.91,24.08-23.13,24.08-14.18,0-23.42-10.73-23.42-24.08s10.68-23.19,23.42-23.19c4.75,0,8.71,1.23,12.2,3.74v-2.4h7.9v15.87h-7.9v-2.98Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <polygon class="cls-3" points="1126.07 488.89 1126.07 496.41 1121.06 496.41 1133.02 526.06 1137.86 526.06 1137.86 533.65 1119.36 533.65 1119.36 526.06 1124.45 526.06 1113.72 498.07 1103.05 526.06 1107.98 526.06 1107.98 533.65 1089.58 533.65 1089.58 526.06 1094.45 526.06 1106.4 496.41 1101.38 496.41 1101.38 488.89 1126.07 488.89" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-3" d="M1214.8,534.99c-9.13,0-18.29-6.97-18.29-19.15v-19.43h-6v-7.52h19.51v7.52h-5.94v19.03c0,6.35,4.55,11.47,10.72,11.47s10.73-5.11,10.73-11.47v-19.03h-5.94v-7.52h19.46v7.52h-5.95v19.43c0,12.4-9.15,19.15-18.29,19.15Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <polygon class="cls-3" points="1259.62 513.36 1259.62 526.06 1276.29 526.06 1276.29 518.37 1284.07 518.37 1284.07 533.65 1246.04 533.65 1246.04 526.06 1251.87 526.06 1251.87 496.41 1246.04 496.41 1246.04 488.89 1284.07 488.89 1284.07 502.3 1276.29 502.3 1276.29 496.41 1259.62 496.41 1259.62 506.1 1272.41 506.1 1272.41 513.36 1259.62 513.36" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <polygon class="cls-3" points="1021.14 488.89 1001.44 488.89 1001.44 496.41 1007.28 496.41 1007.28 526.06 1001.44 526.06 1001.44 533.65 1037.48 533.65 1037.48 518.37 1029.73 518.37 1029.73 526.06 1015.05 526.06 1015.05 496.41 1021.14 496.41 1021.14 488.89" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    </g>
    <g>
      <path class="cls-3" d="M573.32,489.55l5.68,21.73c.81,3.13,1.11,4.62,1.51,6.87.38-2.25.87-4.49,1.56-7.05l5.86-21.54h11.12l3.87,43.4h-8.55l-1.64-23.17c-.18-2.74-.32-4.93-.32-7.24-.49,2.49-.86,4.05-1.61,6.68l-6.87,23.73h-7.43l-6.19-22.79c-.57-2.13-1.3-5.19-1.74-7.68,0,2.56-.14,5.12-.32,7.8l-1.49,22.67h-8.68l4.05-43.4h11.18Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-3" d="M636.9,489.55l-1.13,7.12h-14.86v10.06h12.43v7.1h-12.3v11.63h16.42v7.49h-25.3v-43.4h24.74Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-3" d="M654.77,489.55v43.4h-9.13v-43.4h9.13Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-3" d="M673.93,489.55v34.97c0,5.62-.19,6.75-.63,8.49-1.24,5.31-6.17,8.68-10.11,10.18l-4.05-4.49c2.24-1.37,4.31-3.49,5.05-5.26.69-1.54.88-3.3.88-7.97v-35.91h8.87Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-3" d="M693.48,489.55v43.4h-9.12v-43.4h9.12Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-3" d="M716.95,489.55l5.57,11.87c1.12,2.37,1.68,3.93,2.18,5.54.5-1.24,1.37-3.36,2.31-5.42l5.62-11.99h10.29l-13.87,25.54v17.86h-9.11v-17.86l-13.5-25.54h10.49Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-3" d="M752.54,515.4c.94-2.88,1.51-4.93,2.31-8.05.82-3.26,2.06-8.38,2.06-8.38,0,0,1.32,5.69,1.75,7.68.93,4.19,2.39,8.75,2.39,8.75h-8.51ZM752.5,489.42l-14.25,43.53h9.11l3.07-10.31h12.73l3.07,10.31h9.36l-13.67-43.53h-9.42Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-3" d="M805.38,498.85c-3.95-2.37-6.87-3.31-10.12-3.31-3.56,0-6.06,2.06-6.06,5,0,2.25,1.2,3.37,4.8,4.37l5.24,1.44c7.13,1.94,11,6.49,11,12.92,0,8.8-7.12,14.61-17.93,14.61-4.92,0-10.42-1.44-14.8-3.93l3.26-6.81c4.04,2.13,7.55,3.49,11.93,3.49,4.98,0,7.6-2.05,7.6-5.93,0-2.81-1.8-4.55-5.68-5.62l-4.8-1.3c-4.31-1.19-6.99-2.88-8.56-5.49-1.19-1.94-1.81-4.2-1.81-6.63,0-7.94,6.38-13.43,15.55-13.43,5.3,0,10.67,1.62,14.48,4.38l-4.11,6.24Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-3" d="M826.8,489.55v28.53c0,2.68.24,4.25.75,5.19,1.01,1.93,3.07,3,5.93,3,3.8,0,6.24-1.88,6.63-5.07.12-.93.12-1.56.12-3.87v-27.78h8.88v29.22c0,4.12-.07,4.93-.7,6.81-1.12,3.44-5.37,8.18-14.74,8.18-6.62,0-11.48-2-13.92-5.8-1.44-2.27-1.87-4.01-1.87-8.19v-30.22h8.93Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-3" d="M868.19,496.42h4c3.45,0,5.43,1.25,6.87,3.62,1.76,2.87,2.19,7.11,2.19,12.16,0,9.19-2.62,13.62-8.36,13.62h-4.7v-29.4ZM859.34,489.55v43.4h10.54c2.56,0,4.62-.12,6.26-.24,4.04-.32,7.92-2.69,10.54-6.25,2.94-3.94,4.38-9.12,4.38-15.05,0-7.81-1.94-13.37-5.93-17.31-2.62-2.62-5.81-3.99-9.48-4.31-1.76-.12-5.08-.24-7.56-.24h-8.74Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-3" d="M906.67,515.4c.93-2.88,1.49-4.93,2.31-8.05.81-3.26,2.06-8.38,2.06-8.38,0,0,1.31,5.69,1.74,7.68.94,4.19,2.38,8.75,2.38,8.75h-8.5ZM906.61,489.42l-14.25,43.53h9.13l3.05-10.31h12.74l3.07,10.31h9.36l-13.68-43.53h-9.43Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    </g>
  </g>
  <g transform="matrix(0.3184399902820587, 0, 0, 0.3184399902820587, -8.899558067321777, -150.6319122314453)">
    <g>
      <polygon class="cls-3" points="1047.68 600.53 1047.68 623.43 991.65 623.43 991.65 612.74 1000.16 612.74 1000.16 566.42 991.65 566.42 991.65 555.74 1020.98 555.74 1020.98 566.42 1012.48 566.42 1012.48 612.74 1036.12 612.74 1036.12 600.53 1047.68 600.53" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-3" d="M1101.13,610.21c-3.54,1.77-8.6,2.65-15.17,2.65h-8.86v-47.06l9.52-.12h.33c5.68,0,10.04.7,13.07,2.11,7.9,3.54,11.85,10.7,11.85,21.48s-3.58,17.38-10.74,20.93M1111.1,560.7c-5.68-3.84-12.99-5.76-21.92-5.76h-32.89v10.86h8.31v47.06h-8.31v10.85h29.68c10.11,0,17.68-1.33,22.7-3.98,10.7-5.61,16.05-15.76,16.05-30.45,0-12.85-4.54-22.37-13.62-28.57" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-3" d="M951.19,584.45h-16.22v-18.07h15.78c8.98,0,13.48,2.96,13.48,8.87s-4.35,9.2-13.03,9.2M975.84,612.93v-8.65c0-6.36-1.93-10.67-5.81-12.93-2.05-1.17-4.13-1.76-6.24-1.76,8.77-1.75,13.15-6.97,13.15-15.66,0-12.2-8.69-18.3-26.08-18.3h-36.48v10.74h8.11v46.56h-8.11v10.74h29.36v-10.74h-8.77v-18.08h16.22c5.4,0,8.95,1.38,10.63,4.16,1.02,1.68,1.53,5.04,1.53,10.07v14.58h19.39v-10.74h-6.91Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <polygon class="cls-3" points="832.68 555.75 832.68 566.44 825.15 566.44 808.14 623.47 794.29 623.47 780.78 580.18 767.58 623.47 753.84 623.47 736.18 566.44 728.65 566.44 728.65 555.75 756.79 555.75 756.79 566.44 749.7 566.44 761.37 606.46 773.14 566.44 766.6 566.44 766.6 555.75 796.48 555.75 796.48 566.44 789.82 566.44 801.82 606.46 813.27 566.44 806.29 566.44 806.29 555.75 832.68 555.75" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-1" d="M907.27,589.8c0,20.72-16.8,37.51-37.51,37.51s-37.51-16.79-37.51-37.51,16.79-37.51,37.51-37.51,37.51,16.79,37.51,37.51" style="fill: rgb(236, 28, 36); stroke-width: 0px;"/>
      <path class="cls-3" d="M788.81,651.1l8.83,24.85h-17.44l8.61-24.85ZM818.78,697.53l-17.32-46.43h7.63v-10.68h-41.31v10.68h8.82l-16.78,46.43h-8.28v10.57h27.36v-10.57h-6.21l3.92-11.44h24.63l4.04,11.44h-6.98v10.57h27.03v-10.57h-6.54Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-3" d="M654.89,682.16l9.8,4.69c-5.52,14.83-16.48,22.24-32.91,22.24-10.9,0-19.69-3.42-26.38-10.25-6.32-6.46-9.48-14.68-9.48-24.63s3.16-18.1,9.48-24.63c6.61-6.76,15.29-10.13,26.05-10.13,8.65,0,16.13,2.43,22.46,7.3v-6.32h10.46v24.96h-10.57c-1.67-4.87-4.4-8.7-8.18-11.5-3.78-2.8-8.29-4.2-13.51-4.2-7.05,0-12.76,2.37-17.11,7.08-4.36,4.73-6.54,10.54-6.54,17.44s2.25,12.63,6.75,17.38c4.5,4.76,10.14,7.14,16.9,7.14,5.38,0,10.08-1.5,14.11-4.47,4.03-2.97,6.92-7.01,8.67-12.1" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <polygon class="cls-3" points="1023.92 686.85 1023.92 708.1 964.52 708.1 964.52 697.42 973.02 697.42 973.02 651.1 964.52 651.1 964.52 640.42 1023.05 640.42 1023.05 659.93 1011.61 659.93 1011.61 651.1 985.35 651.1 985.35 667.78 1006.38 667.78 1006.38 677.91 985.35 677.91 985.35 697.42 1012.48 697.42 1012.48 686.85 1023.92 686.85" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-3" d="M1179.61,673.01c0,8.79-2.14,16.39-6.43,22.78-5.96,8.93-15.29,13.4-28.01,13.4-12.06,0-21.47-3.92-28.23-11.77-5.67-6.53-8.5-14.17-8.5-22.88,0-10.75,3.49-19.37,10.46-25.83,6.61-6.11,15-9.15,25.18-9.15,9.52,0,16.86,2.39,22.02,7.19v-6.21h10.57v23.32h-10.57c0-.21-.04-.54-.11-.99-.58-2.83-2.47-5.51-5.66-8.05-4.21-3.27-9.41-4.95-15.59-5.02h-.76c-5.96,0-11.19,2.11-15.69,6.32-4.8,4.66-7.2,10.57-7.2,17.77,0,7.56,2.44,13.69,7.3,18.42,4.58,4.51,10.25,6.76,17.01,6.76,6.39,0,11.77-2.03,16.12-6.11,3.78-3.41,5.81-7,6.11-10.79h-20.93v-10.24h32.81c.07.36.11.73.11,1.09" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <polygon class="cls-3" points="743.73 697.42 743.73 708.1 715.17 708.1 715.17 697.42 722.91 697.42 722.91 678.45 694.03 678.45 694.03 697.42 701.66 697.42 701.66 708.1 673.1 708.1 673.1 697.42 681.6 697.42 681.6 651.1 673.1 651.1 673.1 640.42 701.66 640.42 701.66 651.1 694.03 651.1 694.03 668.32 722.91 668.32 722.91 651.1 715.17 651.1 715.17 640.42 743.73 640.42 743.73 651.1 735.22 651.1 735.22 697.42 743.73 697.42" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <polygon class="cls-3" points="955.47 685.21 955.47 708.1 899.45 708.1 899.45 697.42 907.95 697.42 907.95 651.1 899.45 651.1 899.45 640.42 928.77 640.42 928.77 651.1 920.27 651.1 920.27 697.42 943.92 697.42 943.92 685.21 955.47 685.21" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <polygon class="cls-3" points="890.54 685.21 890.54 708.1 834.52 708.1 834.52 697.42 843.02 697.42 843.02 651.1 834.52 651.1 834.52 640.42 863.84 640.42 863.84 651.1 855.34 651.1 855.34 697.42 878.99 697.42 878.99 685.21 890.54 685.21" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <polygon class="cls-3" points="1246.24 686.85 1246.24 708.1 1186.85 708.1 1186.85 697.42 1195.35 697.42 1195.35 651.1 1186.85 651.1 1186.85 640.42 1245.37 640.42 1245.37 659.93 1233.93 659.93 1233.93 651.1 1207.67 651.1 1207.67 667.78 1228.7 667.78 1228.7 677.91 1207.67 677.91 1207.67 697.42 1234.8 697.42 1234.8 686.85 1246.24 686.85" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <polygon class="cls-3" points="1105.26 640.42 1105.26 651.1 1097.2 651.1 1097.2 708.1 1083.25 708.1 1053.38 661.45 1053.38 697.42 1061.45 697.42 1061.45 708.1 1033.33 708.1 1033.33 697.42 1041.61 697.42 1041.61 651.1 1033.33 651.1 1033.33 640.42 1054.47 640.42 1085.43 688.59 1085.43 651.1 1077.15 651.1 1077.15 640.42 1105.26 640.42" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    </g>
    <g>
      <path class="cls-3" d="M840.07,795.94h-55.53l-.79-13.07c15.16-9.46,25.08-16.11,29.74-19.94,7.13-5.86,10.7-11.23,10.7-16.1,0-8.03-4.58-12.05-13.74-12.05-3.6,0-6.8,1.28-9.57,3.83-3.46,3.23-5.33,7.81-5.63,13.74l-12.73-2.7c.9-7.81,4.03-14.06,9.4-18.75s12.03-7.04,19.99-7.04c7.43,0,13.55,2.07,18.36,6.19,4.8,4.13,7.21,9.54,7.21,16.22,0,5.71-1.69,10.77-5.07,15.2-2.7,3.6-7.09,7.47-13.18,11.6-6.31,3.98-12.58,7.92-18.81,11.83h27.37v-9.69h12.28v20.73Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-3" d="M913.96,761.14c0,11.49-2.82,20.48-8.44,26.97-5.63,6.5-12.99,9.75-22.08,9.75s-16.37-3.19-21.85-9.58c-5.78-6.6-8.68-15.65-8.68-27.14s2.86-20.01,8.56-26.7c5.71-6.68,13.03-10.02,21.96-10.02s16.35,3.34,22.02,10.02c5.67,6.68,8.5,15.58,8.5,26.7ZM901.24,761.14c0-7.74-1.58-14.06-4.75-18.98-3.17-4.92-7.53-7.38-13.1-7.38s-9.83,2.46-13,7.38c-3.16,4.92-4.74,11.24-4.74,18.98s1.56,14.32,4.69,19.09c3.12,4.77,7.48,7.15,13.05,7.15s9.94-2.38,13.1-7.15c3.17-4.77,4.75-11.13,4.75-19.09Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-3" d="M983.57,795.94h-55.53l-.79-13.07c15.16-9.46,25.08-16.11,29.74-19.94,7.13-5.86,10.7-11.23,10.7-16.1,0-8.03-4.58-12.05-13.74-12.05-3.6,0-6.8,1.28-9.57,3.83-3.46,3.23-5.33,7.81-5.63,13.74l-12.73-2.7c.9-7.81,4.03-14.06,9.4-18.75,5.37-4.69,12.03-7.04,19.99-7.04,7.43,0,13.55,2.07,18.36,6.19,4.8,4.13,7.21,9.54,7.21,16.22,0,5.71-1.69,10.77-5.07,15.2-2.7,3.6-7.09,7.47-13.18,11.6-6.31,3.98-12.58,7.92-18.81,11.83h27.37v-9.69h12.28v20.73Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
      <path class="cls-3" d="M1059.65,777.58h-12.5v7.32h9.46v11.04h-31.65v-11.04h9.46v-7.32h-35.36l-3.27-11.49,34.36-40.1h17.01v40.55h12.5v11.04ZM1034.43,766.54v-28.41l-24.64,28.41h24.64Z" style="fill: rgb(35, 31, 32); stroke-width: 0px;"/>
    </g>
  </g>
</svg>