.sidebar-cta {
  height: 0px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 20px;
  overflow: hidden;
  display: flex;
  visibility: hidden;
  opacity: 0;
  transition: all 300ms ease-in-out;

  &.sidebar-cta--show {
    visibility: visible;
    opacity: 1;
    height: 200px;
    margin: 8px;
  }

  .sidebar-cta__body {
    width: 100%;
    height: calc(100% - 40px);
    overflow: hidden;
    flex-shrink: 0;

    img {
      object-fit: cover;
      width: 100%;
      height: 100%;
      display: block;
      object-position: center top;
    }
  }

  .sidebar-cta__upper{
    &:hover {
      opacity: 1;
    }
  }

  .sidebar-cta__wrapper{
    width: 100%;
  }

  .sidebar-cta__footer {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 100%;
    padding: 10px 24px;
    border: 0;
    height: 40px;
    color: var(--color-white);
    background-color: var(--color-black);
    transition: all 0.3s linear;
    cursor: pointer;

    &:hover {
      background-color: var(--color-red);
      opacity: 1;
    }

    .sidebar-cta__text {
      font-size: 16px;
      font-weight: 700;
      font-style: normal;
      letter-spacing: 1.667px;
      font-family: var(--font-barlow-condensed);
      text-align: center;
      text-transform: uppercase;
      line-height: 20px;
      color: var(--color-white);
      opacity: 1;
    }
  }
}
