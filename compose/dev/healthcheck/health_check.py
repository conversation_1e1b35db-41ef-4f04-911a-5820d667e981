import os
import requests
from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError

# TODO: find the way to get these from environment
BOT_TOKEN = '********************************************************'
CHANNEL = 'C020P87KA31'
HOSTNAME = 'jleague.co'


def ping_check():
    response = os.system(f'ping -c 1 {HOSTNAME}')

    if response != 0:
        msg = f'Health Check Alert: Ping domain name failed.'
        try:
            client = WebClient(token=BOT_TOKEN)
            client.chat_postMessage(
                channel=CHANNEL,
                text=msg,
                unfurl_links=False,
            )
        except SlackApiError as error:
            print(f'Error: {error}')


def health_check():
    r = requests.get(f'https://www.{HOSTNAME}/health/')

    if r.status_code != 200:
        msg = f'Health Check Alert: Request health status failed.'
        try:
            client = WebClient(token=BOT_TOKEN)
            client.chat_postMessage(
                channel=CHANNEL,
                text=msg,
                unfurl_links=False,
            )
        except SlackApiError as error:
            print(f'Error: {error}')


if __name__ == '__main__':
    ping_check()
    health_check()
