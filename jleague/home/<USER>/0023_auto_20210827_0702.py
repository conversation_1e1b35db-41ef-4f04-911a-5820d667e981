# Generated by Django 3.1.13 on 2021-08-27 11:02

from django.db import migrations, models
import django.db.models.deletion
import wagtail.fields


class Migration(migrations.Migration):
    dependencies = [
        ('wagtailcore', '0060_fix_workflow_unique_constraint'),
        ('wagtailimages', '0023_add_choose_permissions'),
        ('home', '0022_teaminfo_phone_number'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='teaminfo',
            name='mascotes',
        ),
        migrations.CreateModel(
            name='MascotItem',
            fields=[
                (
                    'page_ptr',
                    models.OneToOneField(
                        auto_created=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        parent_link=True,
                        primary_key=True,
                        serialize=False,
                        to='wagtailcore.page',
                    ),
                ),
                ('date', models.DateTimeField(blank=True, null=True, verbose_name='Date')),
                ('description', wagtail.fields.RichTextField(blank=True, null=True)),
                (
                    'image',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='+',
                        to='wagtailimages.image',
                    ),
                ),
            ],
            options={
                'abstract': False,
            },
            bases=('wagtailcore.page',),
        ),
    ]
