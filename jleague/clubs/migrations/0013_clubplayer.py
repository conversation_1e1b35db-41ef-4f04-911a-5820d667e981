# Generated by Django 3.1.13 on 2022-06-01 03:51

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):
    dependencies = [
        ('clubs', '0012_auto_20220531_1719'),
        ('players', '0006_auto_20220531_1719'),
    ]

    operations = [
        migrations.CreateModel(
            name='ClubPlayer',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField()),
                ('position', models.CharField(max_length=10, null=True)),
                ('jersey_no', models.IntegerField(null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                (
                    'club',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, related_name='club_players', to='clubs.club'
                    ),
                ),
                (
                    'player',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, related_name='player_club', to='players.player'
                    ),
                ),
            ],
        ),
    ]
