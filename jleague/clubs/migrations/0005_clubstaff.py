# Generated by Django 3.1.13 on 2022-04-25 05:13

from django.db import migrations, models
import django.db.models.deletion
import modelcluster.fields


class Migration(migrations.Migration):
    dependencies = [
        ('clubs', '0004_auto_20220419_1625'),
    ]

    operations = [
        migrations.CreateModel(
            name='ClubStaff',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sort_order', models.IntegerField(blank=True, editable=False, null=True)),
                ('staff_id', models.IntegerField(unique=True)),
                ('name', models.CharField(max_length=255)),
                ('name_short', models.CharField(max_length=255)),
                (
                    'position',
                    models.CharField(
                        choices=[
                            ('Coach', 'Coach'),
                            ('Goalkeeping Coach', 'Goalkeeping Coach'),
                            ('Head Coach', 'Head Coach'),
                            ('Player', 'Player'),
                        ],
                        max_length=255,
                    ),
                ),
                ('nationality_name', models.Char<PERSON>ield(max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                (
                    'club_extra_info',
                    modelcluster.fields.ParentalKey(
                        default=None,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='club_staffs',
                        to='clubs.clubextrainfo',
                    ),
                ),
            ],
            options={
                'ordering': ['sort_order'],
                'abstract': False,
            },
        ),
    ]
