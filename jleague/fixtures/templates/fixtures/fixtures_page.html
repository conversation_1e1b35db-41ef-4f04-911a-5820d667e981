{% extends 'base.html' %}

{% load pipeline static %}

{% block extra_styles %}
  {% stylesheet 'fixtures-page' %}
{% endblock %}

{% block extra_scripts %}
  <script type="text/javascript">
    const isFixturesStage = {% if is_fixtures_stage %}true{% else %}false{% endif %};
    let selectedStage = {% if selected_stage %}'{{ selected_stage }}'{% else %}null{% endif %};
    let selectedCompetition  = '{{ selected_competition }}';
    let selectedMonth = '{{ selected_month }}';
    let selectedYear = {{ selected_year }};
    let selectedTeam = '{{ selected_team }}';
  </script>

  {% javascript 'fixtures-page' %}
{% endblock extra_scripts %}

{% block active_body_class %}template-fixtures{% endblock %}

{% block content %}
  <main class="content-container fixtures">
    <div class="fixtures__header">
      {% include 'fixtures/components/fixtures_header.html' %}
    </div>

    <div class="fixtures__body">
      <section class="fixture-results-list"></section>
    </div>

    {% include 'components/tickets-modal.html' %}
  </main>
{% endblock content %}
