@import 'setup/variable';

.tickets-modal {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 350ms ease-in-out;
  z-index: 10000;
  visibility: visible;
  opacity: 1;
  pointer-events: auto;

  .tickets-modal__backdrop {
    background-color: transparentize($color: $color-black, $amount: 0.15);
    content: '';
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;

    &:hover {
      cursor: pointer;
    }
  }

  .tickets-modal__close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: transparent;
    border: none;
    font-size: 24px;
    color: $color-black;
    opacity: 0.75;

    &:hover {
      cursor: pointer;
      opacity: 1;
    }
  }

  .tickets-modal__content {
    position: relative;
    z-index: 2;

    border-radius: 24px;
    background: $color-white;
    background-blend-mode: darken;
    box-shadow: 0px 0px 8px 0px transparentize($color: $color-white, $amount: 0.7) inset;
    backdrop-filter: blur(64px);
    display: flex;
    width: 320px;
    padding: 24px;
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  &.tickets-modal--closed {
    pointer-events: none;
    visibility: hidden;
    z-index: 0;
    opacity: 0;
  }

  .tickets-list {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 16px;

    .tickets-list__header {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 16px;

      .ticket-icon {
        height: 24px;
        width: 24px;
      }

      .ticket-title {
        color: $color-black;
        text-align: center;
        font-size: 20px;
        font-family: $font-overpass;
        font-weight: 900;
        line-height: 100%;
      }
    }

    .tickets-list__body {
      display: flex;
      flex-direction: column;
      gap: 8px;
      width: 100%;
    }
  }
}

body.lang-th {
  .tickets-list .tickets-list__header .ticket-title {
    @include font-thai();
  }
}
