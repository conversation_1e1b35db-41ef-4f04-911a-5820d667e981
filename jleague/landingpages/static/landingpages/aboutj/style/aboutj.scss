@import 'setup/variable';

.jwc {
  .section{
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 48px;
    margin: 0 auto;
    position: relative;
    padding: 160px 40px;
    background-color: var(--color-white);
    .section__header{
      margin: 0;
      padding: 0;
      border: 0;
      font-size: 100%;
      font: inherit;
      vertical-align: baseline;
    }
    .section-title{
      text-align: center;
      font-family: var
      (--font-overpass);
      font-style: normal;
      font-weight: 900;
      line-height: 1;
      letter-spacing: 2px;
      text-transform: uppercase;
      word-break: break-word;
      white-space: normal;
      margin-bottom: 48px;
    }
    .section__body{
      max-width: 1440px;
      width: 100%;
    }
    }

  .header-teams-list,
  .nav-container {
    z-index: 10;
  }

  .social-networks-area {
    margin-top: 0;
  }

  .jwc__header {
    background-color: var(--color-black);
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 100%;
    padding: 0;

    .jwc-bg {
      position: relative;
      z-index: 1;
      width: 100%;
      height: auto;
      overflow: hidden;
      aspect-ratio: 16/9;

      img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    }
  }

  .section {
    .section__header,
    .section__footer {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .section__footer {
      margin-top: 64px;
    }

    .section__body {
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      align-items: center;

      p+p {
        margin-top: 24px;
      }
    }

    .section-title {
      text-align: center;
      font-family: var(--font-overpass);
      font-size: 72px;
      font-style: normal;
      font-weight: 900;
      line-height: 75px;
      letter-spacing: 2px;
      text-transform: uppercase;
      position: relative;
      padding-bottom: 28px;
      color: var(--color-black);

      &::after {
        content: '';
        width: 112px;
        height: 12px;
        background-color: var(--color-red);
        border-radius: 12px 2px;
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
      }
    }

    .text-body {
      font-family: var(--font-overpass);
      font-size: 27px;
      font-weight: 400;
      line-height: 32px;
      letter-spacing: 0px;
      text-align: justify !important;
      line-height: 1.777;

      strong {
        font-weight: 700;
      }
    }
  }

  @media screen and (min-width: 1441px) {
    .section {
      .section__content {
        max-width: 1200px;
      }
    }
  }

  @media screen and (max-width: 768px) {
    .section {
      padding: 100px 40px;

      .section-title {
        font-size: 40px;
        line-height: 40px;

        &::after {
          width: 80px;
        }
      }

      .text-body {
        font-size: 18px;
        line-height: 21.6px;
      }
    }
  }

  @media screen and (max-width: 512px) {
    .jwc {
      .section {
        padding: 96px 24px;
        .section-title {
          font-size: 20px;
          line-height: 10px;
        }

      }
    }

    @media screen and (max-width: 480px) {
      .section-title {
        font-size: 36px;
        line-height: 36px;

        &::after {
          width: 60px;
        }
      }
    }
  }

  @import 'about-section';
  @import 'fixtures-section';
  @import 'fixtures_results';
  @import 'floating-buttons';
  @import 'news-section';
  @import 'child-page';
  @import 'sticky-bar';
}
