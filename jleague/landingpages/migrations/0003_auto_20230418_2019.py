# Generated by Django 4.1.4 on 2023-04-18 11:19

from django.db import migrations


def create_j30_timelines(apps, schema_editor):
    J30Timeline = apps.get_model("landingpages", "J30Timeline")
    J30TimelineEvent = apps.get_model("landingpages", "J30TimelineEvent")
    J30TimelineSlideImage = apps.get_model("landingpages", "J30TimelineSlideImage")

    tls = [
        {
            "title": "1993 - 2002",
            "description": "J30_TIMELINE_1993_2002_P1,J30_TIMELINE_1993_2002_P2,J30_TIMELINE_1993_2002_P3,J30_TIMELINE_1993_2002_P4,J30_TIMELINE_1993_2002_P5,J30_TIMELINE_1993_2002_P6",
            "start": 1993,
            "end": 2002,
            "item_start_index": 1,
            "items": [
                (1993, {"v": 36}),
                (1993, {"v": 7}),
                (1994, {"v": 12}),
                (1998, {"p": 17905}),
                (1999, {"v": 37}),
                (1999, {"v": 38}),
                (2002, {"p": 17906}),
            ],
            "slide_imgs": [
                (17915, "[1993]JL93001_003209"),
                (17916, "[1993]JL93002_0054183073"),
                (17917, "[1994]JL94325_3174A183073"),
                (17918, "[1994]JL94325_3185173073"),
                (17919, "[1995]JL95142_1428093073"),
                (17920, "[1995]JL95170_1672053073"),
                (17921, "[1996]JL96370_3014043073"),
                (17922, "[1997]JL97039_0333013073"),
                (17923, "[1997]photo_18783073"),
                (17924, "[1997]photo_30333073"),
                (17925, "[1998]photo_916943070"),
                (17926, "[1998]photo_921273070"),
                (17927, "[1998]photo_925043070"),
                (17928, "[1998]photo_928543070"),
                (17929, "[1999]JL99025_0168133070"),
                (17930, "[1999]JL99025_0169083070"),
                (17931, "[2000]JL00366_AW4606"),
                (17932, "[2001]JL01303_2245093070"),
                (17933, "[2001]L01303_2245023070"),
                (17934, "[2002]photo_376943070"),
                (17935, "[2002]photo_422043070"),
                (17936, "[2002]photo_997013070"),
                (17937, "[2002]photo_998243070"),
            ],
        },
        {
            "title": "2003 - 2012",
            "description": "J30_TIMELINE_2003_2012_P1,J30_TIMELINE_2003_2012_P2,J30_TIMELINE_2003_2012_P3,J30_TIMELINE_2003_2012_P4,J30_TIMELINE_2003_2012_P5,J30_TIMELINE_2003_2012_P6",
            "start": 2003,
            "end": 2012,
            "item_start_index": 8,
            "items": [
                (2004, {"v": 39}),
                (2005, {"v": 40}),
                (2007, {"p": 17907}),
                (2008, {"p": 17908}),
                (2009, {"v": 45}),
                (2011, {"p": 17909}),
                (2012, {"p": 17910}),
            ],
            "slide_imgs": [
                (17938, "[2003]JL98061_0507113073"),
                (17939, "[2004]JL03367_3711203070"),
                (17940, "[2005]JL05628_d163504"),
                (17941, "[2006]JL06170_0933104"),
                (17942, "[2006]JL06414_1083104"),
                (17943, "[2007]JL07064_1643504"),
                (17944, "[2007]JL07193_1783504"),
                (17945, "[2009]JL08501_2023504"),
                (17946, "[2010]JL10528_5434256"),
                (17947, "[2011]JL12315_2784087"),
            ],
        },
        {
            "title": "2013 - 2022",
            "description": "J30_TIMELINE_2013_2022_P1,J30_TIMELINE_2013_2022_P2,J30_TIMELINE_2013_2022_P3,J30_TIMELINE_2013_2022_P4,J30_TIMELINE_2013_2022_P5,J30_TIMELINE_2013_2022_P6",
            "start": 2013,
            "end": 2022,
            "item_start_index": 15,
            "items": [
                (2016, {"p": 17912}),
                (2017, {"p": 17911}),
                (2017, {"v": 41}),
                (2018, {"v": 23}),
                (2018, {"p": 17913}),
                (2019, {"v": 42}),
                (2020, {"v": 43}),
                (2021, {"v": 31}),
                (2022, {"p": 17914}),
            ],
            "slide_imgs": [
                (17948, "[2013]JL13258_2965184"),
                (17949, "[2013]JL13269_1655184"),
                (17950, "[2014]JLG3_14199_d481024"),
                (17951, "[2015]JL15285_1425184"),
                (17952, "[2016]JLG3_16246_0695184"),
                (17953, "[2017]JL17572_1203936"),
                (17954, "[2017]JL17572_1653936"),
                (17955, "[2021]JL20171_1044167"),
                (17956, "[2022]JL21096_d045184"),
                (17957, "[2022]VI21026_1113648"),
            ],
        },
        {
            "title": "2023 AND BEYOND",
            "description": "J30_TIMELINE_2023_BEYOND_P1,J30_TIMELINE_2023_BEYOND_P2,J30_TIMELINE_2023_BEYOND_P3,J30_TIMELINE_2023_BEYOND_P4,J30_TIMELINE_2023_BEYOND_P5",
            "start": 2023,
            "end": 2023,
            "item_start_index": 24,
            "items": [(2023, {"v": 44})],
            "slide_imgs": [
                (17959, "[2023]JL00366_AW3432"),
                (17960, "[2023]JLG3_14199_d351024"),
                (17962, "[2023]1464911141 (1)"),
                (17963, "[2023]JL23013_d04"),
                (17964, "[2023]JL23089_001"),
                (17965, "[2023]JL23143_d01"),
                (17966, "[2023]JLG23087_d37 (1)"),
                (17967, "[2023]JLG23100_006"),
            ],
        },
    ]

    for tl in tls:
        timeline = J30Timeline.objects.create(
            title=tl["title"],
            description=tl["description"],
            start_period=tl["start"],
            end_period=tl["end"],
            is_active=True,
        )

        for i, item in enumerate(tl["items"], start=tl["item_start_index"]):
            J30TimelineEvent.objects.create(
                timeline=timeline,
                title=f"J30_TIMELINE_ITEM_{i}_TITLE",
                description=f"J30_TIMELINE_ITEM_{i}_DESCRIPTION",
                period=item[0],
                image_id=item[1]["p"] if item[1].get("p") else None,
                video_id=item[1]["v"] if item[1].get("v") else None,
                is_active=True,
            )

        for i, item in enumerate(tl["slide_imgs"]):
            J30TimelineSlideImage.objects.create(
                timeline=timeline,
                title=item[1],
                description=f'J30 timeline slide image {i} of period {tl["title"]}',
                image_id=item[0],
                is_active=True,
            )


def create_j30_greatest_moments(apps, schema_editor):
    J30GreatestMoment = apps.get_model("landingpages", "J30GreatestMoment")
    Document = apps.get_model("wagtaildocs", "Document")

    documents = Document.objects.all().order_by("created_at")
    video_nominees = [
        "1",
        "2",
        "3",
        "5",
        "6",
        "8",
        "10",
        "11",
        "12",
        "14",
        "15",
        "16",
        "17",
        "18",
        "19",
        "20",
        "21",
        "24",
        "25",
        "26",
    ]

    for doc in documents:
        file_path = str(doc.file)
        if "documents/No-" not in file_path:
            continue

        video_no = file_path.replace("documents/No-", "").replace(".mp4", "")
        if video_no not in video_nominees:
            continue

        J30GreatestMoment.objects.create(
            title=f"J30_GREATEST_MOMENT_VIDEO_{video_no}_TITLE",
            description=doc.title[12:],
            event_date=doc.title[1:11],
            video=doc,
            is_active=True,
        )


class Migration(migrations.Migration):
    dependencies = [
        ("landingpages", "0002_j30greatestmoment"),
    ]

    operations = [
        migrations.RunPython(create_j30_timelines),
        migrations.RunPython(create_j30_greatest_moments),
    ]
