from enum import IntEnum, Enum


class GameKindEnum(Enum):
    AFC_CHAMPIONS_LEAGUE = "acl"
    AFC_CHAMPIONS_LEAGUE_ELITE = "acle"
    AFC_CHAMPIONS_LEAGUE_TWO = "acl2"
    ASIA_CHALLENGE = "asia-challenge"
    EMPERORS_CUP = "emperor"
    FIFA_CLUB_WORLD_CUP = "cwc"
    FUJIFILM_SUPER_CUP = "ffsc"
    J1_ENTRY_PLAY_OFF = "j1-entry-play-off"
    J1_PROMOTION_PLAY_OFF = "j1-promotion-play-offs"
    J1_LEAGUE = "j1"
    J2_LEAGUE = "j2"
    J3_LEAGUE = "j3"
    LEVAIN_CUP = "levain"
    WORLD_CHALLENGE = "jwc"
    FRIENDLY_MATCH = "friendly-match"
    J3_JFL_Replace = "j3-jfl-replace"
    J2_PROMOTION_PLAY_OFF = "j2-promotion-play-off"

class LevainCupStageEnum(IntEnum):
    """Levain Cup tournament stages"""
    FIRST_ROUND = 1
    SECOND_ROUND = 2
    THIRD_ROUND = 3
    PLAYOFF_ROUND_FIRST_LEG = 4
    PLAYOFF_ROUND_SECOND_LEG = 5
    PRIME_ROUND_QUARTER_FIRST_LEG = 6
    PRIME_ROUND_QUARTER_SECOND_LEG = 7
    PRIME_ROUND_SEMI_FIRST_LEG = 8
    PRIME_ROUND_SEMI_SECOND_LEG = 9
    PRIME_ROUND_FINAL = 10


class LevainCupStageName(Enum):
    """Display names for Levain Cup stages"""
    FIRST_ROUND = "1st Round"
    SECOND_ROUND = "2nd Round"
    THIRD_ROUND = "3rd Round"
    PLAYOFF_ROUND_FIRST_LEG = "Play-off Round 1st leg"
    PLAYOFF_ROUND_SECOND_LEG = "Play-off Round 2nd leg"
    PRIME_ROUND_QUARTER_FIRST_LEG = "Prime Round Quarter-finals 1st leg"
    PRIME_ROUND_QUARTER_SECOND_LEG = "Prime Round Quarter-finals 2nd leg"
    PRIME_ROUND_SEMI_FIRST_LEG = "Prime Round Semi-finals 1st leg"
    PRIME_ROUND_SEMI_SECOND_LEG = "Prime Round Semi-finals 2nd leg"
    PRIME_ROUND_FINAL = "Prime Round Final"


class GameKindShortNameEnum(Enum):
    AFC_CHAMPIONS_LEAGUE = "AFC Champions League"
    ASIA_CHALLENGE = "Asia Challenge"
    AFC_CHAMPIONS_LEAGUE_ELITE = "AFC Champions League Elite"
    AFC_CHAMPIONS_LEAGUE_TWO = "AFC Champions League Two"
    EMPERORS_CUP = "Emperors Cup"
    FIFA_CLUB_WORLD_CUP = "FIFA Club World Cup"
    FUJIFILM_SUPER_CUP = "Fujifilm Super Cup"
    J1_ENTRY_PLAY_OFF = "J1 Entry Play-Off"
    J1_PROMOTION_PLAY_OFF = "J1 Promotion Play-Off"
    J1_LEAGUE = "J1 League"
    J2_LEAGUE = "J2 League"
    J3_LEAGUE = "J3 League"
    LEVAIN_CUP = "Levain Cup"
    WORLD_CHALLENGE = "JWC powered by docomo"
    FRIENDLY_MATCH = "Club Friendly Match"
    J3_JFL_Replace = "J3 JFL Replace"
    J2_PROMOTION_PLAY_OFF = "J2 Promotion Play-Off"


class GameSituationCSSClassEnum(Enum):
    AFTER_EXTRA_TIME = "aet"
    FULL_TIME = "full-time"
    HALF_TIME = "half-time"
    LIVE = "live"
    PENALTY = "pen"
    POSTPONED = "postponed"
    UPCOMING = "upcoming"


class GameSituationNameEnum(Enum):
    AFTER_EXTRA_TIME = "AET"
    FULL_TIME = "Full-Time"
    HALF_TIME = "Half-Time"
    LIVE = "Live"
    PENALTY = "PEN"
    POSTPONED = "Postponed"
    UPCOMING = "Upcoming"
    KICK_OFF = "Kick-Off"
    AFTER_THE_MATCH = "After Match"


class GameSituationShortNameEnum(Enum):
    AFTER_EXTRA_TIME = "AET"
    FULL_TIME = "FT"
    HALF_TIME = "HT"
    LIVE = "Live"
    PENALTY = "PEN"
    POSTPONED = "Postponed"
    UPCOMING = "Upcoming"
    KICK_OFF = "K.O."
    AFTER_THE_MATCH = "AM"


class GameSituationEnum(IntEnum):
    """source: (JL_reference)ID_information.xlsx"""

    BEFORE = 1
    HALFWAY = 2
    FINISHED = 3
    CONFISCATION = 4
    SUNSET_SUSPENSION = 5  # deprecated
    HALFWAY_SUSPENSION = 6
    BEFORE_SUSPENSION = 7
    VICTORY_GOAL_END = 8
    PK_GAME_END = 9
    SUSPENSION = 99  # deprecated


class GameStateEnum(IntEnum):
    """source: (JL_reference)ID_information.xlsx"""

    KICK_OFF = 0  # doesn't have this in DS doc
    FIRST_HALF = 1
    SECOND_HALF = 2
    OVERTIME_FIRST_HALF = 3
    OVERTIME_SECOND_HALF = 4
    SECOND_OVERTIME_FIRST_HALF = 5
    SECOND_OVERTIME_SECOND_HALF = 6
    PK = 7
    AFTER_THE_MATCH = 99


class SeasonEnum(IntEnum):
    """source: (JL_reference)ID_information.xlsx"""

    UNDEFINED = 0
    SEASON_TOTAL = 1
    FIRST_STAGE = 2
    SECOND_STAGE = 3
    GROUP_STAGE = 4
    KNOCKOUT_STAGE = 5
    RANKING_DECISION = 6
    SECOND_ROUND = 7
    FINAL_ROUND = 8
    FIRST_ROUND = 9
    POST_SEASON = 10
    ASIA_FINAL_ROUND = 11
    THIRD_ROUND = 12
    PLAYOFF_STAGE = 13
    FOURTH_ROUND = 14
    FIRST_ROUND_2 = 15
    PLAYOFF_ROUND = 16
    PRIME_ROUND = 17


class SeasonTitleEnum(Enum):
    """source: (JL_reference)ID_information.xlsx"""

    UNDEFINED = "Undefined"
    SEASON_TOTAL = "Season Total"
    FIRST_STAGE = "First Stage"
    SECOND_STAGE = "Second Stage"
    GROUP_STAGE = "Group Stage"
    KNOCKOUT_STAGE = "Knockout Stage"
    RANKING_DECISION = "Ranking Decision"
    SECOND_ROUND = "Second Round"
    FINAL_ROUND = "Final Round"
    FIRST_ROUND = "First Round"  # id=9
    POST_SEASON = "Post Season"
    ASIA_FINAL_ROUND = "AsiaFinalRound"
    THIRD_ROUND = "Third Round"
    PLAYOFF_STAGE = "Play-Off Stage"
    FOURTH_ROUND = "Fourth Round"
    FIRST_ROUND_2 = "First Round"  # id=15
    PLAYOFF_ROUND = "Play-Off Round"
    PRIME_ROUND = "Prime Round"


class GameStageKey(Enum):
    FIRST_ROUND = "first-round"
    SECOND_ROUND = "second-round"
    THIRD_ROUND = "third-round"
    FOURTH_ROUND = "fourth-round"
    GROUP_STAGE = "group"
    ROUND_16 = "round-16"
    PLAYOFF = "playoff"
    QUARTER_FINAL = "quarter-finals"
    SEMI_FINAL = "semi-finals"
    FINAL = "final"


class GameStageName(Enum):
    FIRST_ROUND = "First Round"
    SECOND_ROUND = "Second Round"
    THIRD_ROUND = "Third Round"
    FOURTH_ROUND = "Fourth Round"
    GROUP_STAGE = "Group Stage"
    ROUND_16 = "Round of 16"
    PLAYOFF = "Play-Off Stage"
    QUARTER_FINAL = "Quarter-Finals"
    SEMI_FINAL = "Semi-Finals"
    FINAL = "Final"


class GameStageShortName(Enum):
    FIRST_ROUND = "R1"
    SECOND_ROUND = "R2"
    THIRD_ROUND = "R3"
    FOURTH_ROUND = "R4"
    GROUP_STAGE = "MW"
    ROUND_16 = "R16"
    PLAYOFF = "PO"
    QUARTER_FINAL = "QF"
    SEMI_FINAL = "SF"
    FINAL = "F"


class GameStageNameTranslationKey(Enum):
    FIRST_ROUND = "MATCH_STAGE_FIRST_ROUND"
    SECOND_ROUND = "MATCH_STAGE_SECOND_ROUND"
    THIRD_ROUND = "MATCH_STAGE_THIRD_ROUND"
    FOURTH_ROUND = "MATCH_STAGE_FOURTH_ROUND"
    GROUP_STAGE = "MATCH_STAGE_GROUP_STAGE_MATCHWEEK"
    ROUND_16 = "MATCH_STAGE_ROUND_OF_16"
    PLAYOFF = "MATCH_STAGE_PLAYOFF"
    QUARTER_FINAL = "MATCH_STAGE_QUARTERFINAL"
    SEMI_FINAL = "MATCH_STAGE_SEMIFINAL"
    FINAL = "MATCH_STAGE_FINAL"


class GameStageShortNameTranslationKey(Enum):
    FIRST_ROUND = "MATCH_STAGE_FIRST_ROUND_SHORT"
    SECOND_ROUND = "MATCH_STAGE_SECOND_ROUND_SHORT"
    THIRD_ROUND = "MATCH_STAGE_THIRD_ROUND_SHORT"
    FOURTH_ROUND = "MATCH_STAGE_FOURTH_ROUND_SHORT"
    GROUP_STAGE = "MATCH_STAGE_GROUP_STAGE_MATCHWEEK_SHORT"
    ROUND_16 = "MATCH_STAGE_ROUND_OF_16_SHORT"
    PLAYOFF = "MATCH_STAGE_PLAYOFF_SHORT"
    QUARTER_FINAL = "MATCH_STAGE_QUARTERFINAL_SHORT"
    SEMI_FINAL = "MATCH_STAGE_SEMIFINAL_SHORT"
    FINAL = "MATCH_STAGE_FINAL_SHORT"


class TeamMemberTypeEnum(Enum):
    COACH = "Coach"
    GOALKEEPING_COACH = "Goalkeeping Coach"
    HEAD_COACH = "Head Coach"
    MANAGER = "Manager"
    PLAYER = "Player"


class PlayerPositionEnum(Enum):
    GOALKEEPERS = "GK"
    DEFENDERS = "DF"
    MIDFIELDERS = "MF"
    FORWARDS = "FW"


class PlayerPositionNameEnum(Enum):
    GOALKEEPERS = "Goalkeepers"
    DEFENDERS = "Defenders"
    MIDFIELDERS = "Midfielders"
    FORWARDS = "Forwards"


class CompetitionEnum(IntEnum):
    """source: (JL_reference)ID_information.xlsx"""

    # J-League Tournaments
    J1_LEAGUE = 2
    LEVAIN_CUP = 4
    J2_LEAGUE = 6
    EMPERORS_CUP = 10
    FUJIFILM_SUPER_CUP = 21
    FIFA_CLUB_WORLD_CUP = 37
    AFC_CHAMPIONS_LEAGUE = 46
    AFC_CHAMPIONS_LEAGUE_ELITE = 245  # AFC Champions League Elite
    AFC_CHAMPIONS_LEAGUE_TWO = 246  # AFC Champions League 2
    J_YOUTH_CUP = 57
    J1_PROMOTION_PLAY_OFF = 60
    J3_LEAGUE = 68
    JLEAGUE_INTERNATIONAL_YOUTH_CUP = 157  # J.LEAGUE INTERNATIONAL YOUTH CUP
    ASIA_CHALLENGE = 158  # J.LEAGUE Asia Challenge
    WORLD_CHALLENGE = 213  # J.LEAGUE WORLD CHALLENGE
    J1_ENTRY_PLAYOFF = 224  # J1 J2 Play-Offs
    J3_JFL_Replace = 238
    J2_PROMOTION_PLAY_OFF = 242

    # National Team Tournaments
    FRIENDLY_MATCH = 13
    ASIAN_CUP = 15
    KIRIN_CUP = 16
    FIFA_WORLD_CUP = 18
    OLYMPIC_GAMES = 23
    EAFF_E1_Football_Championship = 24
    U23_INTERNATIONAL_FRIENDLY_MATCH = 31
    OLYMPIC_WOMEN = 32
    FIFA_U20_WORLD_CUP = 36
    FIFA_U17_WORLD_CUP = 45
    COPA_AMERICA = 58
    FIFA_WOMENS_WORLD_CUP = 59
    WOMENS_INTERNATIONAL_FRIENDLY_MATCH = 63
    AFC_U23_CHAMPIONSHIP = 72
    EAFF_WOMENS_E1_FOOTBALL_CHAMPIONSHIP = 77
    FIFA_WORLD_CUP_ASIAN_QUALIFIERS = 225
    WOMENS_ASIAN_CUP = 226
